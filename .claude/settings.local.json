{"permissions": {"allow": ["Bash(grep:*)", "<PERSON><PERSON>(go run:*)", "Bash(go build:*)", "<PERSON><PERSON>(chmod:*)", "Bash(./scripts/fix_imports.sh:*)", "Bash(./scripts/remove_unused_time.sh:*)", "<PERSON><PERSON>(sed:*)", "Bash(TZ=Asia/Shanghai date)", "<PERSON><PERSON>(curl:*)", "Bash(rm:*)", "<PERSON><PERSON>(tail:*)", "Bash(find:*)", "Bash(go mod:*)", "<PERSON><PERSON>(./balance-demo)", "Bash(grep -n -A 20 \"type.*BalanceRepository.*interface\" /Users/<USER>/Desktop/go-kuaidi-*********/internal/repository/balance_repository.go)", "Bash(cp:*)", "<PERSON><PERSON>(mv:*)", "<PERSON>sh(./simple-balance-demo)", "Bash(./stress-test)", "<PERSON><PERSON>(pkill:*)", "Bash(rg:*)", "Bash(ls:*)", "<PERSON><PERSON>(timeout:*)", "Bash(./go-kuaidi:*)", "Ba<PERSON>(go vet:*)", "<PERSON><PERSON>(python3:*)", "Bash(pip3 install:*)", "Bash(psql:*)", "<PERSON><PERSON>(go test:*)", "Bash(git add:*)", "Bash(git commit:*)"], "deny": []}}