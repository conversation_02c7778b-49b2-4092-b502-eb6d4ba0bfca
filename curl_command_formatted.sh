#!/bin/bash

# 易达快递计费回调接口调用
# 接口地址: http://127.0.0.1:8081/api/v1/callbacks/yida/billing
# 方法: POST
# Content-Type: application/json

# 请求URL
URL="http://127.0.0.1:8081/api/v1/callbacks/yida/billing"

# 请求头
HEADERS=(
    "Content-Type: application/json"
)

# 请求体数据
REQUEST_BODY='{
    "context": "{\"calcFeeWeight\":1,\"feeBlockList\":\"[{\\\"type\\\":0,\\\"name\\\":\\\"运费\\\",\\\"fee\\\":\\\"10.00\\\"}]\",\"freightBase\":\"weight\",\"realWeight\":0.1}",
    "orderNo": "YT250712154807065538",
    "thirdNo": "gk1752306486",
    "pushType": 2,
    "contextObj": {
        "realWeight": 0.1,
        "freightBase": "weight",
        "feeBlockList": [
            {
                "fee": 10,
                "name": "运费",
                "type": 0
            }
        ],
        "calcFeeWeight": 1
    },
    "deliveryId": "YT2529977659013",
    "deliveryType": "YTO"
}'

# 执行curl命令
echo "正在调用易达快递计费回调接口..."
echo "URL: $URL"
echo "请求数据:"
echo "$REQUEST_BODY" | jq '.' 2>/dev/null || echo "$REQUEST_BODY"
echo ""

curl -X POST "$URL" \
    -H "${HEADERS[0]}" \
    -d "$REQUEST_BODY" \
    -w "\nHTTP状态码: %{http_code}\n响应时间: %{time_total}s\n" \
    -s

echo ""
echo "调用完成" 