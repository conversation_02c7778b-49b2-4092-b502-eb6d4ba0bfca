#!/bin/bash

# =====================================================
# Go-Kuaidi 简化查价测试脚本
# 快速验证查价功能和JD/DBL过滤效果
# =====================================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 配置
BASE_URL="http://localhost:8081"
LOGIN_USERNAME="admin"
LOGIN_PASSWORD="1104030777+.aA..@"

# 简化测试配置
TEST_ROUTES=(
    "北京市,北京市,上海市,上海市"
    "广东省,广州市,浙江省,杭州市"
    "江苏省,南京市,山东省,济南市"
)
TEST_WEIGHTS=(1.0 2.5 5.0)

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_test() {
    echo -e "${PURPLE}[TEST]${NC} $1"
}

log_perf() {
    echo -e "${CYAN}[PERF]${NC} $1"
}

# 分割线
print_separator() {
    echo -e "${CYAN}=================================================${NC}"
}

print_section() {
    echo -e "${CYAN}=============== $1 ===============${NC}"
}

# 获取JWT Token
get_jwt_token() {
    log_info "正在获取JWT Token..."
    
    TOKEN_RESPONSE=$(curl -s -X POST "${BASE_URL}/api/v1/auth/login" \
        -H "Content-Type: application/json" \
        -d "{
            \"username\": \"${LOGIN_USERNAME}\",
            \"password\": \"${LOGIN_PASSWORD}\"
        }")

    TOKEN=$(echo $TOKEN_RESPONSE | jq -r '.access_token')

    if [ "$TOKEN" = "null" ] || [ -z "$TOKEN" ]; then
        log_error "获取Token失败: $TOKEN_RESPONSE"
        exit 1
    fi

    log_success "Token获取成功"
}

# 系统健康检查
check_system_health() {
    log_info "🔍 系统健康检查..."
    
    # 检查服务是否运行
    health_response=$(curl -s "${BASE_URL}/health")
    if [ $? -eq 0 ]; then
        log_success "✅ 服务运行正常"
    else
        log_error "❌ 服务无法访问"
        exit 1
    fi
}

# 获取毫秒时间戳（兼容macOS）
get_timestamp_ms() {
    if command -v gdate &> /dev/null; then
        # 如果安装了GNU date
        gdate +%s%3N
    else
        # macOS默认date，使用秒级时间戳
        echo $(($(date +%s) * 1000))
    fi
}

# 简化查价测试
simple_price_test() {
    local route=$1
    local weight=$2

    IFS=',' read -r from_province from_city to_province to_city <<< "$route"

    log_info "📊 测试路线: ${from_province}${from_city} -> ${to_province}${to_city}, 重量: ${weight}kg"

    local start_time=$(get_timestamp_ms)

    local response=$(curl -s -X POST "${BASE_URL}/api/gateway/execute" \
        -H "Content-Type: application/json" \
        -d "{
            \"apiMethod\": \"QUERY_PRICE\",
            \"clientType\": \"web\",
            \"accessToken\": \"$TOKEN\",
            \"businessParams\": {
                \"from_province\": \"${from_province}\",
                \"from_city\": \"${from_city}\",
                \"to_province\": \"${to_province}\",
                \"to_city\": \"${to_city}\",
                \"weight\": ${weight},
                \"goods_name\": \"测试物品\"
            }
        }")

    local end_time=$(get_timestamp_ms)
    local response_time=$((end_time - start_time))

    local success=$(echo "$response" | jq -r '.success')
    if [ "$success" = "true" ]; then
        log_success "✅ 查价成功 - 响应时间: ${response_time}ms"

        # 检查返回的快递公司
        local companies=$(echo "$response" | jq -r '.data[].express_company' | sort | uniq)
        local price_count=$(echo "$response" | jq -r '.data | length')

        log_info "返回价格选项数量: ${price_count}"
        log_info "快递公司列表:"
        echo "$companies" | while read -r company; do
            if [ "$company" = "JD" ] || [ "$company" = "DBL" ]; then
                log_error "❌ 发现不应该出现的实时查价快递公司: $company"
            else
                log_info "  ✅ $company"
            fi
        done

        # 显示价格范围
        local min_price=$(echo "$response" | jq -r '.data | min_by(.price) | .price')
        local max_price=$(echo "$response" | jq -r '.data | max_by(.price) | .price')
        log_info "价格范围: ${min_price} - ${max_price} 元"

    else
        log_error "❌ 查价失败"
        echo "$response" | jq '.'
    fi

    echo ""
}



# 主函数
main() {
    print_separator
    log_info "🚀 Go-Kuaidi 简化查价测试"
    print_separator

    # 检查依赖
    for cmd in curl jq; do
        if ! command -v $cmd &> /dev/null; then
            log_error "$cmd 命令未找到，请安装 $cmd"
            exit 1
        fi
    done

    # 获取认证Token
    get_jwt_token

    # 系统健康检查
    check_system_health

    print_section "查价功能测试"

    # 测试不同路线和重量组合
    for route in "${TEST_ROUTES[@]}"; do
        for weight in "${TEST_WEIGHTS[@]}"; do
            simple_price_test "$route" "$weight"
            sleep 1  # 短暂间隔
        done
    done

    print_separator
    log_success "✅ 查价测试完成！"
    log_info "💡 请检查是否有实时查价快递公司出现在结果中"
    print_separator
}

# 执行主函数
main
