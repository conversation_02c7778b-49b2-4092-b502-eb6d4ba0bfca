# 超时保护配置
timeout_protection:
  # 是否启用超时保护
  enabled: true
  
  # 定时任务配置
  scheduled_task:
    # 是否自动启动定时任务
    auto_start: true
    # 检查间隔（分钟）
    check_interval: 30
    
  # 供应商超时配置
  provider_timeouts:
    # 快递鸟：30分钟超时（因为不推送取消状态）
    kuaidiniao:
      timeout_minutes: 30
      max_retries: 10
      retry_interval_seconds: 3
      
    # 快递100：2小时超时
    kuaidi100:
      timeout_minutes: 120
      max_retries: 5
      retry_interval_seconds: 5
      
    # 易达：2小时超时
    yida:
      timeout_minutes: 120
      max_retries: 5
      retry_interval_seconds: 5
      
    # 云通：2小时超时
    yuntong:
      timeout_minutes: 120
      max_retries: 5
      retry_interval_seconds: 5
      
    # 菜鸟：1小时超时
    cainiao:
      timeout_minutes: 60
      max_retries: 8
      retry_interval_seconds: 4

  # 健康检查阈值
  health_check:
    # 取消中订单阈值
    cancelling_orders:
      warning_threshold: 20
      error_threshold: 50
      
    # 超时订单阈值
    timeout_orders:
      warning_threshold: 10
      error_threshold: 30
      
    # 供应商特定阈值
    provider_thresholds:
      kuaidiniao:
        warning: 5
        error: 15
      kuaidi100:
        warning: 10
        error: 25
      yida:
        warning: 10
        error: 25
      yuntong:
        warning: 10
        error: 25
      cainiao:
        warning: 8
        error: 20

  # 告警配置
  alerts:
    # 是否启用告警
    enabled: true
    # 告警渠道
    channels:
      - type: "log"
        level: "error"
      - type: "webhook"
        url: "http://localhost:8081/api/v1/alerts/timeout"
        
  # 自动修复配置
  auto_fix:
    # 是否启用自动修复
    enabled: true
    # 批量处理大小
    batch_size: 50
    # 处理间隔（毫秒）
    process_interval_ms: 100
