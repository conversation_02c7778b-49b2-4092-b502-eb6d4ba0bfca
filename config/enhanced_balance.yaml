# 高并发余额服务配置
enhanced_balance:
  # 服务模式
  # sync: 同步模式 - 最高一致性，最低并发
  # async_eventual: 异步最终一致性 - 最高性能  
  # async_strong: 异步强一致性 - 平衡性能和一致性
  # sharded_eventual: 分片最终一致性 - 超高并发
  # sharded_strong: 分片强一致性 - 高并发+强一致性
  # hybrid: 混合模式 - 自适应选择最优策略
  mode: "sharded_eventual"
  
  # 功能开关
  enable_async_processing: true    # 启用异步处理
  enable_sharding: true           # 启用分片
  enable_distributed_lock: true   # 启用分布式锁
  enable_monitoring: true         # 启用监控
  
  # 性能参数
  max_concurrency: 1000          # 最大并发数
  queue_size: 20000              # 队列大小
  worker_count: 50               # 工作协程数
  batch_size: 200                # 批处理大小
  flush_interval: "20ms"         # 刷新间隔
  
  # 重试参数
  max_retries: 10                # 最大重试次数
  base_retry_delay: "20ms"       # 基础重试延迟
  max_retry_delay: "1s"          # 最大重试延迟
  enable_exponential_backoff: true # 启用指数退避
  
  # 分片参数
  shard_count: 16                # 分片数量
  consistency_mode: "eventual"   # eventual: 最终一致性, strong: 强一致性
  
  # 降级参数
  enable_graceful_degradation: true # 启用优雅降级
  fallback_to_sync: true           # 降级到同步模式
  emergency_mode_threshold: 0.20   # 紧急模式阈值(20%错误率)

# 监控配置
monitoring:
  # 告警阈值
  alert_thresholds:
    error_rate: 0.05              # 错误率阈值 5%
    conflict_rate: 0.10           # 冲突率阈值 10%
    response_time: "1s"           # 响应时间阈值 1秒
    queue_size: 1000              # 队列大小阈值 1000
    consecutive_failures: 10       # 连续失败次数 10
  
  # 健康检查
  health_check_interval: "30s"   # 健康检查间隔
  metrics_window: "1m"           # 指标时间窗口
  
  # 熔断器
  circuit_breaker:
    failure_threshold: 10         # 失败阈值
    success_threshold: 5          # 成功阈值
    timeout: "60s"               # 超时时间

# Redis配置
redis:
  addr: "localhost:6379"
  password: ""
  db: 0
  pool_size: 100
  min_idle_conns: 10
  max_retries: 3
  retry_delay: "100ms"
  
  # 分布式锁配置
  lock:
    default_ttl: "10s"           # 默认锁超时时间
    renewal_interval: "3s"       # 续期间隔
    max_lock_time: "5m"          # 最大锁定时间

# 数据库分片配置
database_shards:
  # 分片0 - 主数据库
  shard_0:
    driver: "postgres"
    dsn: "postgres://user:password@localhost/kuaidi_balance_0?sslmode=disable"
    max_open_conns: 100
    max_idle_conns: 10
    conn_max_lifetime: "1h"
  
  # 分片1-15 (可根据需要配置更多分片)
  shard_1:
    driver: "postgres" 
    dsn: "postgres://user:password@localhost/kuaidi_balance_1?sslmode=disable"
    max_open_conns: 100
    max_idle_conns: 10
    conn_max_lifetime: "1h"
    
  # 可以继续添加更多分片配置
  # shard_2:
  #   driver: "postgres"
  #   dsn: "postgres://user:password@localhost/kuaidi_balance_2?sslmode=disable"
  #   max_open_conns: 100
  #   max_idle_conns: 10
  #   conn_max_lifetime: "1h"

# 日志配置
logging:
  level: "info"                  # debug, info, warn, error
  format: "json"                 # json, console
  output: "stdout"               # stdout, stderr, file
  file_path: "/var/log/kuaidi/balance.log"
  max_size: 100                  # MB
  max_backups: 10
  max_age: 30                    # days
  compress: true

# 环境配置
environment: "production"        # development, staging, production
debug: false