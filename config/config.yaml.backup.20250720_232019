# Go-Kuaidi 配置文件
# 支持环境变量覆盖，格式：GOKUAIDI_SECTION_KEY=value
# 例如：GOKUAIDI_PERFORMANCE_TIMEOUT_PROVIDER_QUERY_MS=2000

# 服务器配置
server:
  port: 8081
  read_timeout: 30
  write_timeout: 30
  idle_timeout: 120
  max_header_bytes: 1048576

# 数据库配置 - 云端部署（性能优化）
database:
  connection_string: "*************************************************/go_kuaidi?sslmode=disable&timezone=Asia/Shanghai"
  max_open_conns: 200         # 🚀 优化：提升到200个连接支持高并发
  max_idle_conns: 100         # 🚀 优化：提升到100个空闲连接
  conn_max_lifetime: 1800     # 30分钟连接生命周期
  conn_max_idle_time: 300     # 5分钟空闲超时
  timezone: "Asia/Shanghai"   # 🕐 数据库连接时区：北京时间（统一）

# Redis配置 - 云端部署（性能优化）
redis:
  connection_string: "redis://default:a63006320@8.138.252.193:6379"
  db: 0
  pool_size: 100              # 🚀 优化：提升到100个连接池
  min_idle_conns: 20          # 🚀 优化：提升到20个最小空闲连接
  max_conn_age: 1800          # 30分钟连接生命周期
  pool_timeout: 10            # 减少连接超时
  idle_timeout: 300           # 保持空闲超时

# 认证配置 - 本地部署
auth:
  token_expiry_seconds: 2592000  # 30天 = 30 * 24 * 60 * 60 = 2,592,000秒
  issuer: "go-kuaidi-production"
  audience: "go-kuaidi-api-production"
  private_key_path: "keys/private.pem"
  public_key_path: "keys/public.pem"
  jwt_secret: "production-jwt-ultra-secure-key-2025-go-kuaidi-enterprise-grade-authentication"

# 🕐 时区配置 - 统一使用北京时间
timezone:
  default: "Asia/Shanghai"           # 默认时区：北京时间
  database_storage: "Asia/Shanghai"  # 数据库存储时区：北京时间（统一）
  api_response: "Asia/Shanghai"      # API响应时区：北京时间
  log_format: "Asia/Shanghai"        # 日志时区：北京时间
  enable_auto_conversion: true       # 启用自动时区转换

# 🔒 安全配置 - 企业级强制安全策略
security:
  signature:
    enabled: true  # 🔒 强制启用签名验证，不允许环境变量覆盖
    timestamp_validity_seconds: 1800  # 🚀 优化：30分钟时间戳有效期，适应高并发和网络延迟
    nonce_validity_seconds: 1800      # 🚀 优化：30分钟nonce有效期
    max_request_body_size: 1048576   # 1MB最大请求体
    disable_in_development: false    # 🔒 所有环境都强制启用签名验证
    disable_nonce_validation: true   # 🚀 禁用nonce验证（保留签名验证但跳过nonce检查）
    timezone: "Asia/Shanghai"         # 🕐 签名验证使用北京时间
    skip_paths:
      - "/health"      # 健康检查
      - "/oauth/token" # OAuth token获取（使用client_secret验证）

  # 🚀 企业级nonce管理配置
  nonce:
    validity_duration: "30m"         # 🚀 优化：30分钟nonce有效期
    max_nonce_length: 64             # 最大nonce长度
    min_nonce_length: 16             # 最小nonce长度
    redis_key_prefix: "nonce:v2:"    # Redis键前缀
    redis_timeout: "3s"              # Redis操作超时
    batch_size: 100                  # 批量操作大小
    cleanup_interval: "10m"          # 清理间隔
    enable_strict_mode: true         # 启用严格模式
    max_nonce_per_client: 1000       # 每个客户端最大nonce数
    enable_metrics: true             # 启用指标收集
    metrics_interval: "1m"           # 指标收集间隔
    timezone: "Asia/Shanghai"         # 🕐 nonce时间戳使用北京时间

  # 速率限制配置
  rate_limit:
    enabled: false  # 🔧 暂时关闭API限速功能
    requests_per_minute: 60
    burst_size: 10

  # 审计日志配置
  audit:
    enabled: true
    queue_size: 1000
    batch_size: 100
    batch_interval_seconds: 30
    max_request_body_size: 4096
    max_response_body_size: 4096
    sampling_rate: 100
    retention_days: 90
    sensitive_fields:
      - "password"
      - "secret"
      - "token"
      - "key"
      - "authorization"
      - "client_secret"
      - "private_key"
      - "api_key"
      - "access_token"
      - "refresh_token"
      - "session_id"
      - "cookie"
      - "x-signature"
      - "x-api-key"
      - "bearer"
      - "basic"
    skip_paths:
      - "/health"
      - "/metrics"
      - "/favicon.ico"

  # HTTP安全头配置
  headers:
    enabled: true
    force_https: false
    hsts_max_age_seconds: 31536000  # 1年
    content_security_policy: "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'"

# 供应商配置已迁移到数据库
# 所有供应商密钥配置现在通过ProviderConfigService从数据库获取
# 配置表：system_configs (config_group: provider_kuaidi100, provider_yida, provider_yuntong)
providers:
  # 快递100配置已迁移到数据库 (config_group: provider_kuaidi100)

  # 易达配置已迁移到数据库 (config_group: provider_yida)

  # 云通配置已迁移到数据库 (config_group: provider_yuntong)

# 性能配置
performance:
  # 查价接口配置
  price_query:
    # 快速响应策略
    fast_response:
      enabled: true
      max_wait_ms: 1500
      first_stage_timeout_ms: 800
      provider_timeout_ms: 600
      min_results: 1

    # 降级策略
    fallback:
      enabled: true
      timeout_ms: 2000
      preferred_providers:
        - yida
        - kuaidi100
        - yuntong

    # 默认值配置
    defaults:
      district: "市辖区"
      sender_name: "寄件人"
      sender_mobile: "13800000000"
      receiver_name: "收件人"
      receiver_mobile: "13900000000"
      goods_name: "普通货物"
      quantity: 1

  # 缓存配置 - 优化为更短的TTL以减少内存使用
  cache:
    price_ttl_minutes: 5      # 减少到5分钟
    fast_query_ttl_minutes: 3  # 减少到3分钟
    fallback_ttl_minutes: 2      # 减少到2分钟
    warmup_ttl_minutes: 10         # 减少到10分钟

  # 超时配置
  timeout:
    provider_query_ms: 1000
    total_query_ms: 3000
    http_client_seconds: 10
    database_seconds: 5
    redis_seconds: 3

  # 重试配置
  retry:
    max_attempts: 3
    interval_ms: 100
    backoff_multiplier: 2.0
    max_interval_ms: 1000

  # 并发配置
  concurrency:
    worker_pool_size: 40        # 🚀 优化：提升到40个Worker
    task_queue_size: 2000       # 🚀 优化：提升到2000个任务队列
    max_concurrent_queries: 100 # 🚀 优化：提升到100个并发查询

# 日志配置
logging:
  level: "info"                    # 🚀 改为info级别，减少调试日志
  format: "json"
  output: "both"                    # 同时输出到文件和控制台
  file_path: "logs/go-kuaidi.log"
  max_size_mb: 50                  # 🚀 减少单个文件大小到50MB
  max_backups: 5                   # 🚀 减少备份数量到5个
  max_age_days: 7                  # 🚀 减少保留天数到7天
  compress: true

  # 分级日志配置
  error_file_path: "logs/error.log"
  access_file_path: "logs/access.log"
  audit_file_path: "logs/audit.log"

  # 日志采样配置（生产环境启用采样）
  sampling:
    enabled: true                   # 🚀 启用采样，减少日志量
    initial: 50                     # 🚀 前50条正常记录
    thereafter: 200                 # 🚀 之后每200条采样1条

# 监控配置
monitoring:
  enabled: true
  metrics_path: "/metrics"
  health_path: "/health"
  pprof_enabled: false

# 环境配置
environment: "production"

# 地区配置
region:
  data_path: "config/getAreaCascaderVo.json"
  auto_load: true


# 回调配置
callback:
  base_url: "http://47.123.6.144:8082"
  default_base_url: "http://47.123.6.144:8082"
  endpoints:
    kuaidi100: "/webhook/kuaidi100"
    yida: "/webhook/yida"
    yuntong: "/webhook/yuntong"
    cainiao: "/webhook/cainiao"
    kuaidiniao: "/webhook/kuaidiniao"
  fallback:
    enabled: true
    base_url: "http://47.123.6.144:8082"
  gateway:
    timeout: "30s"
    max_retries: 3
    retry_interval: "5s"
    rate_limit: 1000
    enable_security: true
    enable_audit_log: true
  queue:
    type: "redis"
    url: "redis://default:a63006320@1Panel-redis-dryE:6379"
    max_workers: 10
    buffer_size: 1000
    timeout: "30s"
    retry_delay: "5s"
    max_retries: 3
  forward:
    default_timeout: "30s"
    default_retry_count: 3
    max_retry_count: 5
    retry_interval: "5s"
    max_retry_interval: "5m"
    enable_retry_backoff: true
    max_concurrency: 50

# 工单配置
workorder:
  # 快递100工单配置
  kuaidi100:
    api_key: "uLoMDJtA1221"
    secret: "f47268d51c00416ebd216733666bd53d"
    customer: "52DB1E76E26136D4B3E9D70F24A65491"
    base_url: "https://api.kuaidi100.com"
    timeout: 30

  # 易达工单配置
  yida:
    username: "ysh1998"
    private_key: "favujb8xeeq36yc5"
    base_url: "https://www.yida178.cn/prod-api/thirdApi/execute"
    timeout: 30

  # 云通工单配置
  yuntong:
    business_id: "25443"
    api_key: "ZGQwZTgzNTJhZDg5YTE4Mjc3NTU5N2Q0NmVlYWMxMjM"
    base_url: "https://open.yuntongzy.com/express/api/OrderService"
    timeout: 30

  file_upload:
    type: "local"
    upload_dir: "./uploads"
    base_url: "http://localhost:8080"
    oss:
      endpoint: ""
      access_key_id: ""
      access_key_secret: ""
      bucket_name: ""

# 缓存预热配置
warmup:
  # 启动时预热配置
  startup:
    enabled: false  # 禁用缓存预热以提升响应速度
    strategy: "priority"
    delay_seconds: 30

  # 预热策略配置
  strategy:
    mode: "priority"
    batch_size: 10
    batch_interval_ms: 100
    max_duration_minutes: 60
    max_retries: 3

  # 路线配置
  routes:
    priority_cities:
      - "北京市"
      - "上海市"
      - "广州市"
      - "深圳市"
      - "杭州市"
      - "南京市"
      - "成都市"
      - "武汉市"
    popular_routes:
      - "北京市->上海市"
      - "深圳市->北京市"
      - "广州市->上海市"
      - "杭州市->北京市"

  # 调度配置
  schedule:
    enabled: false
    cron_expression: "0 2 * * *"
    window_hours: 2

# 用户管理配置
user_management:
  default_page_size: 20
  max_page_size: 100
  max_batch_size: 100
  password_min_length: 8
  password_max_length: 72
  require_uppercase: true
  require_lowercase: true
  require_numbers: true
  require_special: true
  username_min_length: 3
  username_max_length: 50
  online_threshold_minutes: 15
  enable_audit_log: true
  cache_enabled: false
  cache_ttl: "0s"

# 余额管理配置
balance_management:
  cache_enabled: false
  cache_ttl: "0s"
  real_time_data: true
  enable_audit_log: true
  max_transaction_amount: 100000.00
  currency: "CNY"

# 功能开关
features:
  # 注意：签名验证已移至security.signature配置，此处不再控制
  enable_rate_limiting: true
  enable_audit_logging: true
  enable_performance_monitoring: true
  enable_cache_warmup: true
  cache_warmup:
    auto_start: true
    strategy: "smart"
    batch_size: 10
    concurrent_workers: 5
    delay_between_batches_ms: 100
