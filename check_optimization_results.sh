#!/bin/bash

# ===================================================================
# 快递系统优化效果检查脚本
# 用于验证配置优化和索引创建的效果
# ===================================================================

echo "🔍 快递系统优化效果检查"
echo "=========================================="

# 1. 检查配置文件优化
echo "📋 1. 检查配置文件优化..."

echo "数据库连接池配置:"
grep -A 5 "database:" config/config.yaml | grep -E "(max_open_conns|max_idle_conns)"

echo ""
echo "Redis连接池配置:"
grep -A 5 "redis:" config/config.yaml | grep -E "(pool_size|min_idle_conns)"

echo ""
echo "并发处理配置:"
grep -A 5 "concurrency:" config/config.yaml | grep -E "(worker_pool_size|task_queue_size|max_concurrent_queries)"

# 2. 检查数据库索引
echo ""
echo "🗄️  2. 检查数据库索引状态..."

# 创建临时SQL脚本
cat > /tmp/check_indexes.sql << 'EOF'
-- 检查关键索引是否存在
SELECT 
    indexname,
    tablename,
    CASE 
        WHEN indexname LIKE '%user_created%' THEN '✅ 用户查询优化'
        WHEN indexname LIKE '%tracking_provider%' THEN '✅ 回调处理优化'  
        WHEN indexname LIKE '%customer_provider%' THEN '✅ 客户订单优化'
        WHEN indexname LIKE '%platform_id%' THEN '✅ 平台订单优化'
        WHEN indexname LIKE '%admin_stats%' THEN '✅ 管理员统计优化'
        ELSE '其他索引'
    END as optimization_type,
    pg_size_pretty(pg_relation_size(indexname::regclass)) as index_size
FROM pg_indexes 
WHERE schemaname = 'public' 
  AND tablename = 'order_records'
  AND (
    indexname LIKE 'idx_orders_user_created%' OR
    indexname LIKE 'idx_orders_tracking_provider%' OR  
    indexname LIKE 'idx_orders_customer_provider%' OR
    indexname LIKE 'idx_orders_platform_id%' OR
    indexname LIKE 'idx_orders_admin_stats%'
  )
ORDER BY indexname;

-- 显示表统计信息
SELECT 
    'order_records' as table_name,
    COUNT(*) as total_records,
    COUNT(*) FILTER (WHERE created_at >= CURRENT_DATE) as today_records,
    COUNT(*) FILTER (WHERE created_at >= CURRENT_DATE - INTERVAL '7 days') as week_records,
    pg_size_pretty(pg_total_relation_size('order_records')) as total_size,
    pg_size_pretty(pg_relation_size('order_records')) as table_size
FROM order_records;
EOF

if command -v psql >/dev/null 2>&1; then
    echo "连接数据库检查索引..."
    psql "*************************************************/go_kuaidi" -f /tmp/check_indexes.sql 2>/dev/null || {
        echo "❌ 无法连接数据库进行索引检查"
        echo "请手动执行: psql < performance_optimization_indexes.sql"
    }
    rm -f /tmp/check_indexes.sql
else
    echo "⚠️  未安装psql，无法检查数据库索引"
    echo "请安装PostgreSQL客户端后重新检查"
fi

# 3. 检查服务状态
echo ""
echo "🚀 3. 检查服务状态..."

if pgrep -f "go-kuaidi" > /dev/null; then
    PID=$(pgrep -f "go-kuaidi")
    echo "✅ 服务运行中 (PID: $PID)"
    
    # 检查端口监听
    if lsof -Pi :8081 -sTCP:LISTEN >/dev/null 2>&1; then
        echo "✅ 端口8081正常监听"
    else
        echo "❌ 端口8081未监听"
    fi
    
    # 检查健康状态
    if command -v curl >/dev/null 2>&1; then
        if curl -s -f http://localhost:8081/health >/dev/null 2>&1; then
            echo "✅ 健康检查通过"
        else
            echo "⚠️  健康检查失败"
        fi
    fi
    
    # 显示资源使用情况
    echo ""
    echo "资源使用情况:"
    ps -p $PID -o pid,pcpu,pmem,vsz,rss,comm 2>/dev/null || echo "无法获取进程信息"
    
else
    echo "❌ 服务未运行"
    echo "请执行: ./restart_optimized_service.sh"
fi

# 4. 性能测试
echo ""
echo "⚡ 4. 快速性能测试..."

if command -v curl >/dev/null 2>&1 && pgrep -f "go-kuaidi" > /dev/null; then
    echo "测试API响应时间..."
    
    # 测试健康检查端点响应时间
    start_time=$(date +%s%N)
    curl -s -f http://localhost:8081/health >/dev/null 2>&1
    end_time=$(date +%s%N)
    response_time=$(( (end_time - start_time) / 1000000 ))
    
    echo "健康检查响应时间: ${response_time}ms"
    
    if [ $response_time -lt 100 ]; then
        echo "✅ 响应时间优秀 (<100ms)"
    elif [ $response_time -lt 500 ]; then
        echo "✅ 响应时间良好 (<500ms)"
    else
        echo "⚠️  响应时间较慢 (>500ms)"
    fi
else
    echo "⚠️  无法进行性能测试（服务未运行或curl未安装）"
fi

# 5. 生成优化报告
echo ""
echo "📊 5. 优化效果总结..."
echo "=========================================="
echo "🎯 配置优化完成情况:"

# 检查数据库连接池
DB_CONNS=$(grep "max_open_conns:" config/config.yaml | grep -o '[0-9]\+')
if [ "$DB_CONNS" = "200" ]; then
    echo "✅ 数据库连接池: 80 → 200 (提升2.5倍)"
else
    echo "❌ 数据库连接池: 未优化"
fi

# 检查Redis连接池  
REDIS_POOL=$(grep "pool_size:" config/config.yaml | grep -o '[0-9]\+')
if [ "$REDIS_POOL" = "100" ]; then
    echo "✅ Redis连接池: 50 → 100 (提升2倍)"
else
    echo "❌ Redis连接池: 未优化"
fi

# 检查Worker Pool
WORKER_POOL=$(grep "worker_pool_size:" config/config.yaml | grep -o '[0-9]\+')
if [ "$WORKER_POOL" = "40" ]; then
    echo "✅ Worker Pool: 20 → 40 (提升2倍)"
else
    echo "❌ Worker Pool: 未优化"
fi

# 检查任务队列
TASK_QUEUE=$(grep "task_queue_size:" config/config.yaml | grep -o '[0-9]\+')
if [ "$TASK_QUEUE" = "2000" ]; then
    echo "✅ 任务队列: 100 → 2000 (提升20倍)"
else
    echo "❌ 任务队列: 未优化"
fi

echo ""
echo "🎯 预期性能提升:"
echo "• 并发处理能力: +150%"
echo "• 数据库连接容量: +150%"  
echo "• Redis性能: +100%"
echo "• 任务处理能力: +100%"
echo "• 队列容量: +1900%"

echo ""
echo "📋 下一步行动:"
echo "1. 如果索引未创建，执行: psql < performance_optimization_indexes.sql"
echo "2. 进行压力测试验证性能提升"
echo "3. 监控系统资源使用情况"
echo "4. 根据实际负载调整配置参数"

echo ""
echo "=========================================="
echo "优化检查完成！"
echo "=========================================="