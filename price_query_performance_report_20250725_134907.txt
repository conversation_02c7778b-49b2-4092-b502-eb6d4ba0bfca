Go-<PERSON>aidi 查价接口性能测试报告
生成时间: 2025年 7月25日 星期五 13时49分07秒 CST
测试配置: 并发用户 1 5 10 20 30 50, 测试时长 30秒

=== 测试结果汇总 ===
并发数 总请求  成功数  成功率%   平均时间ms 最小时间ms 最大时间ms QPS     
--------------------------------------------------------------------------------
1        <USER>         <GROUP>         100.00       1764.70      1000         2000         .56     
5        69         69         100.00       2159.42      1000         3000         2.30    
10       79         79         100.00       3886.07      3000         5000         2.63    
20       92         92         100.00       7173.91      5000         13000        3.06    
30       92         92         100.00       10217.39     4000         29000        3.06    
50       101        101        100.00       15851.48     4000         20000        3.36    

=== 性能分析 ===
最佳性能点: 50并发用户, QPS: 3.36
每日查价承载能力: 203212.80 次 (70%利用率)

=== 系统配置 ===
- 数据库连接池: 200个连接
- Redis连接池: 100个连接
- 查价缓存TTL: 5分钟
- 服务器配置: 8核32GB

=== 优化建议 ===
1. 如果成功率 < 95%，考虑增加数据库连接池
2. 如果响应时间 > 500ms，考虑优化缓存策略
3. 监控CPU和内存使用率，避免超过80%
4. 设置告警: 响应时间>1s, 错误率>5%
