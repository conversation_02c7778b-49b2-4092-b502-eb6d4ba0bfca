#!/usr/bin/env python3
import urllib.parse
import json

# 快递鸟回调的URL编码数据
encoded_data = 'RequestData=%7B%22PushTime%22%3A%222025-07-20+13%3A12%3A22%22%2C%22EBusinessID%22%3A%221778716%22%2C%22Data%22%3A%5B%7B%22ShipperCode%22%3A%22STO%22%2C%22CreateTime%22%3A%222025-07-20+13%3A12%3A28%22%2C%22OrderCode%22%3A%22gk1752988341%22%2C%22EBusinessID%22%3A%221778716%22%2C%22PickerInfo%22%3A%5B%7B%22StationName%22%3A%22%22%2C%22StationAddress%22%3A%22%22%2C%22StationTel%22%3A%22%22%2C%22StationCode%22%3A%22%22%2C%22PickupCode%22%3A%22066707%22%7D%5D%2C%22Cost%22%3A0.00%2C%22Success%22%3Atrue%2C%22CallRequestType%22%3A%221801%22%2C%22Weight%22%3A1.00%2C%22Reason%22%3A%22%E8%AE%A2%E5%8D%95%E5%B7%B2%E5%88%86%E9%85%8D%E5%BF%AB%E9%80%92%E5%91%98%22%2C%22LogisticCode%22%3A%22773367552456137%22%2C%22State%22%3A%22102%22%2C%22FetchTime%22%3A%22%22%2C%22KDNOrderCode%22%3A%22KDN2507201310000374%22%2C%22OperateType%22%3A2%7D%5D%2C%22Count%22%3A1%7D&DataSign=ZWJiNTMxZDkzODA5OWYzMzcyZGQ0ZDdhZDM4NTQzNDI%3D&RequestType=103'

try:
    # 解析URL编码数据
    parsed_data = urllib.parse.parse_qs(encoded_data)
    request_data = parsed_data['RequestData'][0]
    
    # URL解码
    decoded_json = urllib.parse.unquote(request_data)
    print('解码后的JSON数据:')
    print(decoded_json)
    
    # 解析JSON
    callback_data = json.loads(decoded_json)
    print('\n解析后的结构化数据:')
    print(json.dumps(callback_data, indent=2, ensure_ascii=False))
    
    # 分析关键信息
    data_item = callback_data['Data'][0]
    print(f'\n关键信息分析:')
    print(f'订单号: {data_item["OrderCode"]}')
    print(f'运单号: {data_item["LogisticCode"]}')
    print(f'状态码: {data_item["State"]}')
    print(f'状态描述: {data_item["Reason"]}')
    print(f'取件码: {data_item["PickerInfo"][0]["PickupCode"]}')
    print(f'回调类型: {data_item["CallRequestType"]}')
    print(f'操作类型: {data_item["OperateType"]}')
    
except Exception as e:
    print(f"解析错误: {e}")
