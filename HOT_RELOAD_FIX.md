# 热重载修复说明

## 问题描述

当在管理后台开启/关闭某个快递公司时，热重载不生效，需要重启服务才能看到变更。

## 根本原因

1. **内存缓存失效逻辑错误**：`InvalidateCacheByPattern` 使用了错误的匹配模式
   - 缓存键格式：`fromProvince:toProvince:provider:expressCode:weight`
   - 旧匹配模式：`provider:expressCode:` (前缀匹配)
   - 问题：前缀永远匹配不上，导致内存中的旧价格一直命中

2. **供应商缓存并发污染**：刷新缓存后，并发请求可能重新写入旧数据

3. **配置检查间隔太长**：5秒和3秒的检查间隔导致热重载响应慢

## 修复方案

### 1. 修复内存缓存失效逻辑

**文件**: `internal/repository/weight_tier_cache_repository.go`

```go
// 旧代码
pattern := fmt.Sprintf("%s:%s:", provider, expressCode)
if strings.HasPrefix(keyStr, pattern) { ... }

// 新代码  
pattern := fmt.Sprintf(":%s:%s:", provider, expressCode)
if strings.Contains(keyStr, pattern) { ... }
```

**改进**:
- 使用 `Contains` 替代 `HasPrefix`
- 匹配 `:provider:expressCode:` 模式，确保精确匹配
- 添加删除计数日志，便于调试

### 2. 防止供应商缓存并发污染

**文件**: `internal/express/cache_service.go`

```go
// 关键改进
func (s *ExpressMappingCacheService) RefreshProviderCache(ctx context.Context, providerCode string) error {
    // 使用更强的锁避免并发污染
    s.providerCachesMutex.Lock()
    defer s.providerCachesMutex.Unlock()
    
    // 直接从数据库加载最新数据（在锁保护下）
    companies, err := s.loadSupportedCompaniesFromDBDirect(ctx, providerCode)
    
    // 立即更新缓存，防止并发重复查询
    s.providerCompaniesCache[cacheKey] = companies
    s.providerCacheExpiry[cacheKey] = time.Now().Add(s.providerCacheExpiration)
}
```

**改进**:
- 在锁保护下直接从数据库重新加载
- 避免调用可能使用缓存的方法
- 立即更新缓存，防止并发污染

### 3. 提高配置检查频率

**文件**: `internal/adapter/dynamic_provider_manager.go`
```go
// 从5秒改为2秒
ticker := time.NewTicker(2 * time.Second)
```

**文件**: `internal/adapter/config_watcher.go`
```go
// 从3秒改为1秒
ticker := time.NewTicker(1 * time.Second)
```

### 4. 完善供应商列表

确保所有供应商都被监控：
```go
knownProviders := []string{"kuaidi100", "yida", "yuntong", "cainiao", "kuaidiniao"}
```

## 测试验证

使用提供的测试脚本 `test_hot_reload.sh`：

```bash
./test_hot_reload.sh
```

### 预期结果

1. **日志确认**：
   - `InvalidateCacheByPattern` 显示 `deleted_count > 0`
   - `RefreshProviderCache` 显示正确的映射列表变更
   - 不再看到被禁用公司的"内存缓存命中"日志

2. **功能确认**：
   - 禁用快递公司后，3秒内查价结果不再包含该公司
   - 启用快递公司后，3秒内查价结果重新包含该公司

## 性能影响

- **内存缓存失效**：从O(n)前缀遍历改为O(n)包含检查，性能相当
- **配置检查**：从5秒改为2秒，CPU使用略微增加但影响微乎其微
- **并发控制**：增加锁保护，但避免了数据不一致问题

## 兼容性

修复完全向后兼容，不影响现有功能。

## 注意事项

1. **全局禁用**：如果要全局禁用某个快递公司，需要在所有供应商下都设置 `is_supported=false`
2. **缓存预热**：服务重启后第一次查询可能稍慢，这是正常的缓存预热过程
3. **监控建议**：建议监控日志中的缓存命中率和失效操作，确保热重载正常工作

## 总结

通过修复缓存失效逻辑、防止并发污染、提高检查频率，实现了真正的热重载：
- **响应时间**：从需要重启改为3秒内生效
- **数据一致性**：确保配置变更立即反映到所有缓存层
- **操作体验**：管理员修改配置后立即看到效果 