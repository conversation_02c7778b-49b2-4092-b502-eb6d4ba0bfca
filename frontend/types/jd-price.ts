/**
 * 实时查价API类型定义
 *
 * @description 为实时查价接口提供完整的TypeScript类型定义
 * @version 1.0.0
 * @date 2025-07-13
 */

// ==================== 请求类型定义 ====================

/**
 * 实时查价地址信息
 */
export interface RealtimeAddressInfo {
  /** 姓名 (必填，1-50个字符) */
  name: string;
  /** 手机号 (必填，支持手机号/固话/国际号码) */
  mobile: string;
  /** 省份 (必填，必须包含'省'、'市'、'自治区'或'特别行政区') */
  province: string;
  /** 城市 (必填) */
  city: string;
  /** 区县 (必填) */
  district: string;
  /** 详细地址 (必填，5-200个字符) */
  address: string;
}

/**
 * 实时查价请求参数
 */
export interface RealtimePriceRequest {
  /** 寄件人信息 (必填) */
  sender: RealtimeAddressInfo;
  /** 收件人信息 (必填) */
  receiver: RealtimeAddressInfo;
  /** 包裹重量(kg) (必填，0.1-100) */
  weight: number;
  /** 包裹长度(cm) (可选，0-200) */
  length?: number;
  /** 包裹宽度(cm) (可选，0-200) */
  width?: number;
  /** 包裹高度(cm) (可选，0-200) */
  height?: number;
  /** 包裹体积(m³) (可选，0-1.0) */
  volume?: number;
  /** 包裹数量 (可选，默认1，0-100) */
  quantity?: number;
  /** 物品名称 (可选，默认"物品"，最多100个字符) */
  goods_name?: string;
  /** 支付方式：0-寄付，1-到付，2-月结 (可选，默认0) */
  pay_method?: 0 | 1 | 2;
}

/**
 * 统一网关请求格式
 */
export interface UnifiedGatewayRequest {
  /** API方法，固定为"QUERY_REALTIME_PRICE" */
  api_method: 'QUERY_REALTIME_PRICE';
  /** 客户端类型 */
  client_type: string;
  /** 用户名 */
  username: string;
  /** 业务参数 */
  business_params: RealtimePriceRequest;
}

// ==================== 响应类型定义 ====================

/**
 * 京东快递价格结果
 */
export interface JDPriceResult {
  /** 快递公司代码，固定为"JD" */
  express_code: string;
  /** 快递公司名称，固定为"京东快递" */
  express_name: string;
  /** 产品代码 */
  product_code: string;
  /** 产品名称 */
  product_name: string;
  /** 总价格(元) */
  price: number;
  /** 每公斤续重价格(元) */
  continued_weight_per_kg: number;
  /** 计费重量(kg) */
  calc_weight: number;
  /** 供应商名称 */
  provider: string;
  /** 渠道ID */
  channel_id: string;
  /** 查询时间(ISO 8601格式，北京时间) */
  query_time: string;
  /** 预约时间信息(京东快递暂不支持，固定为null) */
  pickup_time_info: null;
}

/**
 * 京东快递查价响应
 */
export interface JDPriceResponse {
  /** 是否成功 */
  success: boolean;
  /** 状态码：200-成功，400-参数错误，500-服务器错误，503-服务不可用 */
  code: number;
  /** 响应消息 */
  message: string;
  /** 价格数据（仅成功时返回） */
  data?: JDPriceResult;
}

// ==================== 错误类型定义 ====================

/**
 * API错误类型
 */
export type JDPriceErrorCode = 
  | 400  // 参数错误
  | 500  // 服务器内部错误
  | 503; // 服务暂时不可用

/**
 * 常见错误信息
 */
export const JD_PRICE_ERROR_MESSAGES = {
  INVALID_SENDER_NAME: '寄件人姓名不能为空',
  INVALID_SENDER_MOBILE: '寄件人手机号格式不正确',
  INVALID_SENDER_ADDRESS: '寄件人详细地址不能少于2个字符',
  INVALID_RECEIVER_NAME: '收件人姓名不能为空',
  INVALID_RECEIVER_MOBILE: '收件人手机号格式不正确',
  INVALID_RECEIVER_ADDRESS: '收件人详细地址不能少于2个字符',
  INVALID_WEIGHT: '包裹重量必须大于0',
  WEIGHT_EXCEEDED: '包裹重量不能超过100kg',
  SERVICE_UNAVAILABLE: '服务暂时不可用，请稍后重试',
  QUERY_FAILED: '价格查询失败',
} as const;

// ==================== 工具类型定义 ====================

/**
 * 支付方式选项
 */
export const PAY_METHOD_OPTIONS = [
  { value: 0, label: '寄付' },
  { value: 1, label: '到付' },
  { value: 2, label: '月结' },
] as const;

/**
 * 表单验证规则
 */
export interface JDPriceValidationRules {
  /** 姓名验证 */
  name: {
    required: true;
    minLength: 1;
    maxLength: 50;
  };
  /** 手机号验证 */
  mobile: {
    required: true;
    pattern: RegExp;
  };
  /** 省份验证 */
  province: {
    required: true;
    pattern: RegExp;
  };
  /** 详细地址验证 */
  address: {
    required: true;
    minLength: 2;
    maxLength: 200;
  };
  /** 重量验证 */
  weight: {
    required: true;
    min: 0.1;
    max: 100;
  };
  /** 尺寸验证 */
  dimension: {
    min: 0;
    max: 200;
  };
  /** 体积验证 */
  volume: {
    min: 0;
    max: 1.0;
  };
  /** 数量验证 */
  quantity: {
    min: 0;
    max: 100;
  };
}

/**
 * 默认验证规则
 */
export const DEFAULT_VALIDATION_RULES: JDPriceValidationRules = {
  name: {
    required: true,
    minLength: 1,
    maxLength: 50,
  },
  mobile: {
    required: true,
    pattern: /^(1[3-9]\d{9}|0\d{2,3}-?\d{7,8}|\+\d{1,3}\d{7,14})$/,
  },
  province: {
    required: true,
    pattern: /(省|市|自治区|特别行政区)$/,
  },
  address: {
    required: true,
    minLength: 2,
    maxLength: 200,
  },
  weight: {
    required: true,
    min: 0.1,
    max: 100,
  },
  dimension: {
    min: 0,
    max: 200,
  },
  volume: {
    min: 0,
    max: 1.0,
  },
  quantity: {
    min: 0,
    max: 100,
  },
};

// ==================== API客户端类型定义 ====================

/**
 * API客户端配置
 */
export interface JDPriceApiConfig {
  /** API基础URL */
  baseURL: string;
  /** 认证Token */
  token: string;
  /** 请求超时时间(ms) */
  timeout?: number;
  /** 重试次数 */
  retries?: number;
  /** 重试延迟(ms) */
  retryDelay?: number;
}

/**
 * API客户端接口
 */
export interface JDPriceApiClient {
  /**
   * 查询京东快递价格
   * @param request 查价请求参数
   * @returns 查价响应
   */
  queryPrice(request: JDPriceRequest): Promise<JDPriceResponse>;
  
  /**
   * 验证请求参数
   * @param request 查价请求参数
   * @returns 验证结果
   */
  validateRequest(request: JDPriceRequest): {
    isValid: boolean;
    errors: string[];
  };
}

// ==================== React Hook类型定义 ====================

/**
 * 查价Hook状态
 */
export interface UseJDPriceState {
  /** 加载状态 */
  loading: boolean;
  /** 查价结果 */
  data: JDPriceResult | null;
  /** 错误信息 */
  error: string | null;
  /** 查价函数 */
  queryPrice: (request: JDPriceRequest) => Promise<void>;
  /** 重置状态 */
  reset: () => void;
}

/**
 * 表单Hook状态
 */
export interface UseJDPriceFormState {
  /** 表单数据 */
  formData: JDPriceRequest;
  /** 表单错误 */
  errors: Record<string, string>;
  /** 表单是否有效 */
  isValid: boolean;
  /** 更新表单字段 */
  updateField: (field: keyof JDPriceRequest, value: any) => void;
  /** 验证表单 */
  validate: () => boolean;
  /** 重置表单 */
  reset: () => void;
}

// ==================== 导出所有类型 ====================

export type {
  JDAddressInfo,
  JDPriceRequest,
  UnifiedGatewayRequest,
  JDPriceResult,
  JDPriceResponse,
  JDPriceErrorCode,
  JDPriceValidationRules,
  JDPriceApiConfig,
  JDPriceApiClient,
  UseJDPriceState,
  UseJDPriceFormState,
};

export {
  JD_PRICE_ERROR_MESSAGES,
  PAY_METHOD_OPTIONS,
  DEFAULT_VALIDATION_RULES,
};
