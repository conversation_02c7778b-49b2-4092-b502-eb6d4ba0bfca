<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快递订单管理 - 重新设计版</title>
    <link href="https://cdn.jsdelivr.net/npm/element-plus@2.4.4/dist/index.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@element-plus/icons-vue@2.1.0/dist/index.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        /* 页面头部 */
        .page-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 24px 32px;
            margin-bottom: 24px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 16px;
        }

        .header-left h1 {
            font-size: 28px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .header-left p {
            color: #7f8c8d;
            font-size: 14px;
        }

        .header-actions {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.9);
            color: #667eea;
            border: 1px solid rgba(102, 126, 234, 0.3);
        }

        .btn-secondary:hover {
            background: rgba(102, 126, 234, 0.1);
            transform: translateY(-1px);
        }

        /* 统计卡片 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 24px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-4px);
        }

        .stat-icon {
            width: 48px;
            height: 48px;
            margin: 0 auto 16px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
        }

        .stat-icon.orders { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .stat-icon.success { background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%); }
        .stat-icon.pending { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        .stat-icon.failed { background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%); }

        .stat-number {
            font-size: 32px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .stat-label {
            color: #7f8c8d;
            font-size: 14px;
            font-weight: 500;
        }

        /* 搜索和筛选区域 */
        .search-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .search-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .form-group label {
            font-size: 14px;
            font-weight: 500;
            color: #2c3e50;
        }

        .form-input {
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: white;
        }

        .form-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-select {
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            background: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .form-select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .search-actions {
            display: flex;
            gap: 12px;
            justify-content: flex-end;
            flex-wrap: wrap;
        }

        /* 视图切换 */
        .view-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 16px;
        }

        .view-toggle {
            display: flex;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 8px;
            padding: 4px;
            gap: 4px;
        }

        .view-btn {
            padding: 8px 16px;
            border: none;
            background: transparent;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            color: #7f8c8d;
        }

        .view-btn.active {
            background: #667eea;
            color: white;
        }

        .bulk-actions {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        /* 订单卡片视图 */
        .orders-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .order-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .order-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 16px;
        }

        .order-info h3 {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 4px;
        }

        .order-info p {
            font-size: 14px;
            color: #7f8c8d;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-success { background: #d4edda; color: #155724; }
        .status-warning { background: #fff3cd; color: #856404; }
        .status-danger { background: #f8d7da; color: #721c24; }
        .status-info { background: #d1ecf1; color: #0c5460; }
        .status-primary { background: #cce7ff; color: #004085; }

        .card-content {
            margin-bottom: 16px;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .info-label {
            color: #7f8c8d;
            font-weight: 500;
        }

        .info-value {
            color: #2c3e50;
            font-weight: 600;
        }

        .card-actions {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 12px;
            border-radius: 6px;
        }

        /* 表格视图 */
        .orders-table {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 16px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }

        .table th {
            background: rgba(102, 126, 234, 0.1);
            font-weight: 600;
            color: #2c3e50;
            font-size: 14px;
        }

        .table td {
            font-size: 14px;
            color: #495057;
        }

        .table tbody tr:hover {
            background: rgba(102, 126, 234, 0.05);
        }

        /* 分页 */
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 8px;
            margin-top: 24px;
            flex-wrap: wrap;
        }

        .pagination-btn {
            padding: 8px 12px;
            border: 1px solid #e9ecef;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .pagination-btn:hover {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .pagination-btn.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .pagination-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 12px;
            }

            .header-content {
                flex-direction: column;
                align-items: stretch;
            }

            .header-actions {
                justify-content: center;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .search-grid {
                grid-template-columns: 1fr;
            }

            .orders-grid {
                grid-template-columns: 1fr;
            }

            .view-controls {
                flex-direction: column;
                align-items: stretch;
            }

            .orders-table {
                overflow-x: auto;
            }

            .table {
                min-width: 800px;
            }
        }

        /* 加载动画 */
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 40px;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 空状态 */
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #7f8c8d;
        }

        .empty-state .icon {
            font-size: 64px;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        .empty-state h3 {
            font-size: 18px;
            margin-bottom: 8px;
            color: #2c3e50;
        }

        .empty-state p {
            font-size: 14px;
            margin-bottom: 24px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="page-header">
            <div class="header-content">
                <div class="header-left">
                    <h1>
                        📦 快递订单管理
                    </h1>
                    <p>管理和跟踪您的所有快递订单</p>
                </div>
                <div class="header-actions">
                    <button class="btn btn-secondary" onclick="refreshOrders()">
                        🔄 刷新
                    </button>
                    <button class="btn btn-secondary" onclick="exportOrders()">
                        📊 导出
                    </button>
                    <button class="btn btn-primary" onclick="createOrder()">
                        ➕ 创建订单
                    </button>
                </div>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon orders">📦</div>
                <div class="stat-number" id="totalOrders">0</div>
                <div class="stat-label">总订单数</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon success">✅</div>
                <div class="stat-number" id="successOrders">0</div>
                <div class="stat-label">成功订单</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon pending">⏳</div>
                <div class="stat-number" id="pendingOrders">0</div>
                <div class="stat-label">处理中</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon failed">❌</div>
                <div class="stat-number" id="failedOrders">0</div>
                <div class="stat-label">失败订单</div>
            </div>
        </div>

        <!-- 搜索和筛选区域 -->
        <div class="search-section">
            <div class="search-grid">
                <div class="form-group">
                    <label for="searchKeyword">搜索关键词</label>
                    <input type="text" id="searchKeyword" class="form-input" placeholder="订单号、运单号、收件人姓名...">
                </div>
                <div class="form-group">
                    <label for="expressCompany">快递公司</label>
                    <select id="expressCompany" class="form-select">
                        <option value="">全部快递公司</option>
                        <option value="shentong">申通快递</option>
                        <option value="yuantong">圆通快递</option>
                        <option value="zhongtong">中通快递</option>
                        <option value="yunda">韵达快递</option>
                        <option value="jtexpress">极兔快递</option>
                        <option value="jd">京东快递</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="orderStatus">订单状态</label>
                    <select id="orderStatus" class="form-select">
                        <option value="">全部状态</option>
                        <option value="submitted">已提交</option>
                        <option value="assigned">已分配</option>
                        <option value="awaiting_pickup">等待揽收</option>
                        <option value="picked_up">已揽收</option>
                        <option value="in_transit">运输中</option>
                        <option value="out_for_delivery">派送中</option>
                        <option value="delivered">已签收</option>
                        <option value="cancelled">已取消</option>
                        <option value="failed">下单失败</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="dateRange">创建时间</label>
                    <input type="date" id="startDate" class="form-input" style="margin-bottom: 8px;">
                    <input type="date" id="endDate" class="form-input">
                </div>
            </div>
            <div class="search-actions">
                <button class="btn btn-secondary" onclick="resetSearch()">重置</button>
                <button class="btn btn-primary" onclick="searchOrders()">🔍 搜索</button>
            </div>
        </div>

        <!-- 视图控制和批量操作 -->
        <div class="view-controls">
            <div class="view-toggle">
                <button class="view-btn active" onclick="switchView('card')">📋 卡片视图</button>
                <button class="view-btn" onclick="switchView('table')">📊 表格视图</button>
            </div>
            <div class="bulk-actions">
                <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                <label for="selectAll">全选</label>
                <button class="btn btn-secondary btn-small" onclick="bulkCancel()" disabled id="bulkCancelBtn">批量取消</button>
                <button class="btn btn-secondary btn-small" onclick="bulkExport()" disabled id="bulkExportBtn">批量导出</button>
            </div>
        </div>

        <!-- 订单列表 - 卡片视图 -->
        <div id="cardView" class="orders-grid">
            <!-- 订单卡片将通过JavaScript动态生成 -->
        </div>

        <!-- 订单列表 - 表格视图 -->
        <div id="tableView" class="orders-table" style="display: none;">
            <table class="table">
                <thead>
                    <tr>
                        <th><input type="checkbox" onchange="toggleSelectAll()"></th>
                        <th>订单号</th>
                        <th>运单号</th>
                        <th>快递公司</th>
                        <th>状态</th>
                        <th>寄件人</th>
                        <th>收件人</th>
                        <th>重量</th>
                        <th>价格</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="tableBody">
                    <!-- 表格行将通过JavaScript动态生成 -->
                </tbody>
            </table>
        </div>

        <!-- 加载状态 -->
        <div id="loadingState" class="loading" style="display: none;">
            <div class="spinner"></div>
        </div>

        <!-- 空状态 -->
        <div id="emptyState" class="empty-state" style="display: none;">
            <div class="icon">📦</div>
            <h3>暂无订单数据</h3>
            <p>您还没有创建任何订单，点击上方"创建订单"按钮开始使用</p>
            <button class="btn btn-primary" onclick="createOrder()">创建第一个订单</button>
        </div>

        <!-- 分页 -->
        <div class="pagination" id="pagination">
            <!-- 分页按钮将通过JavaScript动态生成 -->
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // 模拟订单数据
        const mockOrders = [
            {
                id: 1,
                platform_order_no: "PF202501180001",
                customer_order_no: "CU001",
                tracking_no: "SF1234567890",
                express_name: "顺丰快递",
                status: "delivered",
                status_desc: "已签收",
                sender_name: "张三",
                receiver_name: "李四",
                weight: 1.5,
                price: 15.80,
                actual_fee: 15.80,
                created_at: "2025-01-18 10:30:00",
                can_cancel: false,
                can_retry: false,
                can_delete: false
            },
            {
                id: 2,
                platform_order_no: "PF202501180002",
                customer_order_no: "CU002",
                tracking_no: "YT9876543210",
                express_name: "圆通快递",
                status: "in_transit",
                status_desc: "运输中",
                sender_name: "王五",
                receiver_name: "赵六",
                weight: 2.0,
                price: 12.50,
                actual_fee: 12.50,
                created_at: "2025-01-18 09:15:00",
                can_cancel: true,
                can_retry: false,
                can_delete: false
            },
            {
                id: 3,
                platform_order_no: "PF202501180003",
                customer_order_no: "CU003",
                tracking_no: "",
                express_name: "申通快递",
                status: "failed",
                status_desc: "下单失败",
                sender_name: "钱七",
                receiver_name: "孙八",
                weight: 0.8,
                price: 8.00,
                actual_fee: 0.00,
                created_at: "2025-01-18 08:45:00",
                can_cancel: false,
                can_retry: true,
                can_delete: true,
                failure_reason: "地址信息不完整"
            },
            {
                id: 4,
                platform_order_no: "PF202501180004",
                customer_order_no: "CU004",
                tracking_no: "ZT5555666677",
                express_name: "中通快递",
                status: "awaiting_pickup",
                status_desc: "等待揽收",
                sender_name: "周九",
                receiver_name: "吴十",
                weight: 3.2,
                price: 18.50,
                actual_fee: 18.50,
                created_at: "2025-01-18 07:20:00",
                can_cancel: true,
                can_retry: false,
                can_delete: false
            },
            {
                id: 5,
                platform_order_no: "PF202501180005",
                customer_order_no: "CU005",
                tracking_no: "YD8888999900",
                express_name: "韵达快递",
                status: "picked_up",
                status_desc: "已揽收",
                sender_name: "郑十一",
                receiver_name: "王十二",
                weight: 1.8,
                price: 14.20,
                actual_fee: 16.80,
                created_at: "2025-01-17 16:45:00",
                can_cancel: false,
                can_retry: false,
                can_delete: false
            }
        ];

        // 当前视图模式
        let currentView = 'card';
        let selectedOrders = new Set();
        let currentPage = 1;
        let pageSize = 10;
        let filteredOrders = [...mockOrders];

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            updateStatistics();
            renderOrders();
            updatePagination();
            updateBulkActions();
        });

        // 更新统计数据
        function updateStatistics() {
            const total = mockOrders.length;
            const success = mockOrders.filter(o => ['delivered', 'billed'].includes(o.status)).length;
            const pending = mockOrders.filter(o => ['submitted', 'assigned', 'awaiting_pickup', 'picked_up', 'in_transit', 'out_for_delivery'].includes(o.status)).length;
            const failed = mockOrders.filter(o => o.status === 'failed').length;

            document.getElementById('totalOrders').textContent = total;
            document.getElementById('successOrders').textContent = success;
            document.getElementById('pendingOrders').textContent = pending;
            document.getElementById('failedOrders').textContent = failed;
        }

        // 切换视图
        function switchView(view) {
            currentView = view;

            // 更新按钮状态
            document.querySelectorAll('.view-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            // 切换视图显示
            if (view === 'card') {
                document.getElementById('cardView').style.display = 'grid';
                document.getElementById('tableView').style.display = 'none';
            } else {
                document.getElementById('cardView').style.display = 'none';
                document.getElementById('tableView').style.display = 'block';
            }

            renderOrders();
        }

        // 渲染订单列表
        function renderOrders() {
            const startIndex = (currentPage - 1) * pageSize;
            const endIndex = startIndex + pageSize;
            const pageOrders = filteredOrders.slice(startIndex, endIndex);

            if (currentView === 'card') {
                renderCardView(pageOrders);
            } else {
                renderTableView(pageOrders);
            }

            // 显示空状态
            if (filteredOrders.length === 0) {
                document.getElementById('emptyState').style.display = 'block';
                document.getElementById('cardView').style.display = 'none';
                document.getElementById('tableView').style.display = 'none';
            } else {
                document.getElementById('emptyState').style.display = 'none';
            }
        }

        // 渲染卡片视图
        function renderCardView(orders) {
            const cardView = document.getElementById('cardView');
            cardView.innerHTML = '';

            orders.forEach(order => {
                const card = document.createElement('div');
                card.className = 'order-card';
                card.innerHTML = `
                    <div class="card-header">
                        <div class="order-info">
                            <h3>${order.platform_order_no}</h3>
                            <p>客户订单号: ${order.customer_order_no}</p>
                        </div>
                        <span class="status-badge ${getStatusClass(order.status)}">${order.status_desc}</span>
                    </div>
                    <div class="card-content">
                        <div class="info-row">
                            <span class="info-label">运单号:</span>
                            <span class="info-value">${order.tracking_no || '暂无'}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">快递公司:</span>
                            <span class="info-value">${order.express_name}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">寄件人:</span>
                            <span class="info-value">${order.sender_name}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">收件人:</span>
                            <span class="info-value">${order.receiver_name}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">重量:</span>
                            <span class="info-value">${order.weight}kg</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">价格:</span>
                            <span class="info-value">¥${order.price.toFixed(2)}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">创建时间:</span>
                            <span class="info-value">${formatDateTime(order.created_at)}</span>
                        </div>
                    </div>
                    <div class="card-actions">
                        <input type="checkbox" ${selectedOrders.has(order.id) ? 'checked' : ''}
                               onchange="toggleOrderSelection(${order.id})">
                        <button class="btn btn-secondary btn-small" onclick="viewOrderDetail(${order.id})">查看详情</button>
                        <button class="btn btn-secondary btn-small" onclick="trackOrder(${order.id})">物流跟踪</button>
                        ${order.can_cancel ? `<button class="btn btn-secondary btn-small" onclick="cancelOrder(${order.id})">取消订单</button>` : ''}
                        ${order.can_retry ? `<button class="btn btn-secondary btn-small" onclick="retryOrder(${order.id})">重试下单</button>` : ''}
                        ${order.can_delete ? `<button class="btn btn-secondary btn-small" onclick="deleteOrder(${order.id})">删除</button>` : ''}
                    </div>
                `;
                cardView.appendChild(card);
            });
        }

        // 渲染表格视图
        function renderTableView(orders) {
            const tableBody = document.getElementById('tableBody');
            tableBody.innerHTML = '';

            orders.forEach(order => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td><input type="checkbox" ${selectedOrders.has(order.id) ? 'checked' : ''}
                               onchange="toggleOrderSelection(${order.id})"></td>
                    <td>${order.platform_order_no}</td>
                    <td>${order.tracking_no || '暂无'}</td>
                    <td>${order.express_name}</td>
                    <td><span class="status-badge ${getStatusClass(order.status)}">${order.status_desc}</span></td>
                    <td>${order.sender_name}</td>
                    <td>${order.receiver_name}</td>
                    <td>${order.weight}kg</td>
                    <td>¥${order.price.toFixed(2)}</td>
                    <td>${formatDateTime(order.created_at)}</td>
                    <td>
                        <button class="btn btn-secondary btn-small" onclick="viewOrderDetail(${order.id})">详情</button>
                        <button class="btn btn-secondary btn-small" onclick="trackOrder(${order.id})">跟踪</button>
                        ${order.can_cancel ? `<button class="btn btn-secondary btn-small" onclick="cancelOrder(${order.id})">取消</button>` : ''}
                        ${order.can_retry ? `<button class="btn btn-secondary btn-small" onclick="retryOrder(${order.id})">重试</button>` : ''}
                        ${order.can_delete ? `<button class="btn btn-secondary btn-small" onclick="deleteOrder(${order.id})">删除</button>` : ''}
                    </td>
                `;
                tableBody.appendChild(row);
            });
        }

        // 获取状态样式类
        function getStatusClass(status) {
            const statusMap = {
                'delivered': 'status-success',
                'billed': 'status-success',
                'in_transit': 'status-info',
                'out_for_delivery': 'status-info',
                'picked_up': 'status-primary',
                'awaiting_pickup': 'status-warning',
                'assigned': 'status-warning',
                'submitted': 'status-info',
                'cancelled': 'status-danger',
                'failed': 'status-danger'
            };
            return statusMap[status] || 'status-info';
        }

        // 格式化日期时间
        function formatDateTime(dateString) {
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN', {
                timeZone: 'Asia/Shanghai',
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        // 切换订单选择
        function toggleOrderSelection(orderId) {
            if (selectedOrders.has(orderId)) {
                selectedOrders.delete(orderId);
            } else {
                selectedOrders.add(orderId);
            }
            updateBulkActions();
        }

        // 全选/取消全选
        function toggleSelectAll() {
            const checkboxes = document.querySelectorAll('input[type="checkbox"]');
            const selectAllChecked = document.getElementById('selectAll').checked;

            if (selectAllChecked) {
                filteredOrders.forEach(order => selectedOrders.add(order.id));
            } else {
                selectedOrders.clear();
            }

            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAllChecked;
            });

            updateBulkActions();
        }

        // 更新批量操作按钮状态
        function updateBulkActions() {
            const hasSelection = selectedOrders.size > 0;
            document.getElementById('bulkCancelBtn').disabled = !hasSelection;
            document.getElementById('bulkExportBtn').disabled = !hasSelection;
        }

        // 更新分页
        function updatePagination() {
            const totalPages = Math.ceil(filteredOrders.length / pageSize);
            const pagination = document.getElementById('pagination');
            pagination.innerHTML = '';

            // 上一页按钮
            const prevBtn = document.createElement('button');
            prevBtn.className = 'pagination-btn';
            prevBtn.textContent = '上一页';
            prevBtn.disabled = currentPage === 1;
            prevBtn.onclick = () => {
                if (currentPage > 1) {
                    currentPage--;
                    renderOrders();
                    updatePagination();
                }
            };
            pagination.appendChild(prevBtn);

            // 页码按钮
            for (let i = 1; i <= totalPages; i++) {
                if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
                    const pageBtn = document.createElement('button');
                    pageBtn.className = `pagination-btn ${i === currentPage ? 'active' : ''}`;
                    pageBtn.textContent = i;
                    pageBtn.onclick = () => {
                        currentPage = i;
                        renderOrders();
                        updatePagination();
                    };
                    pagination.appendChild(pageBtn);
                } else if (i === currentPage - 3 || i === currentPage + 3) {
                    const ellipsis = document.createElement('span');
                    ellipsis.textContent = '...';
                    ellipsis.style.padding = '8px';
                    pagination.appendChild(ellipsis);
                }
            }

            // 下一页按钮
            const nextBtn = document.createElement('button');
            nextBtn.className = 'pagination-btn';
            nextBtn.textContent = '下一页';
            nextBtn.disabled = currentPage === totalPages;
            nextBtn.onclick = () => {
                if (currentPage < totalPages) {
                    currentPage++;
                    renderOrders();
                    updatePagination();
                }
            };
            pagination.appendChild(nextBtn);
        }

        // 搜索订单
        function searchOrders() {
            const keyword = document.getElementById('searchKeyword').value.toLowerCase();
            const expressCompany = document.getElementById('expressCompany').value;
            const orderStatus = document.getElementById('orderStatus').value;
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;

            filteredOrders = mockOrders.filter(order => {
                // 关键词搜索
                if (keyword && !order.platform_order_no.toLowerCase().includes(keyword) &&
                    !order.customer_order_no.toLowerCase().includes(keyword) &&
                    !order.tracking_no.toLowerCase().includes(keyword) &&
                    !order.receiver_name.toLowerCase().includes(keyword)) {
                    return false;
                }

                // 快递公司筛选
                if (expressCompany && !order.express_name.includes(expressCompany)) {
                    return false;
                }

                // 订单状态筛选
                if (orderStatus && order.status !== orderStatus) {
                    return false;
                }

                // 日期范围筛选
                if (startDate || endDate) {
                    const orderDate = new Date(order.created_at).toISOString().split('T')[0];
                    if (startDate && orderDate < startDate) return false;
                    if (endDate && orderDate > endDate) return false;
                }

                return true;
            });

            currentPage = 1;
            renderOrders();
            updatePagination();
        }

        // 重置搜索
        function resetSearch() {
            document.getElementById('searchKeyword').value = '';
            document.getElementById('expressCompany').value = '';
            document.getElementById('orderStatus').value = '';
            document.getElementById('startDate').value = '';
            document.getElementById('endDate').value = '';

            filteredOrders = [...mockOrders];
            currentPage = 1;
            renderOrders();
            updatePagination();
        }

        // 订单操作函数
        function viewOrderDetail(orderId) {
            const order = mockOrders.find(o => o.id === orderId);
            alert(`查看订单详情: ${order.platform_order_no}\n状态: ${order.status_desc}\n快递公司: ${order.express_name}`);
        }

        function trackOrder(orderId) {
            const order = mockOrders.find(o => o.id === orderId);
            if (order.tracking_no) {
                alert(`物流跟踪: ${order.tracking_no}\n当前状态: ${order.status_desc}`);
            } else {
                alert('该订单暂无运单号，无法跟踪物流信息');
            }
        }

        function cancelOrder(orderId) {
            const order = mockOrders.find(o => o.id === orderId);
            if (confirm(`确定要取消订单 ${order.platform_order_no} 吗？`)) {
                alert('订单取消功能演示 - 实际项目中这里会调用API');
            }
        }

        function retryOrder(orderId) {
            const order = mockOrders.find(o => o.id === orderId);
            if (confirm(`确定要重试订单 ${order.platform_order_no} 吗？`)) {
                alert('订单重试功能演示 - 实际项目中这里会调用API');
            }
        }

        function deleteOrder(orderId) {
            const order = mockOrders.find(o => o.id === orderId);
            if (confirm(`确定要删除失败订单 ${order.platform_order_no} 吗？删除后将无法恢复。`)) {
                alert('删除订单功能演示 - 实际项目中这里会调用API');
            }
        }

        // 页面操作函数
        function refreshOrders() {
            document.getElementById('loadingState').style.display = 'block';
            document.getElementById('cardView').style.display = 'none';
            document.getElementById('tableView').style.display = 'none';

            setTimeout(() => {
                document.getElementById('loadingState').style.display = 'none';
                renderOrders();
                alert('订单列表已刷新');
            }, 1000);
        }

        function exportOrders() {
            alert('导出功能演示 - 实际项目中这里会生成Excel文件');
        }

        function createOrder() {
            alert('创建订单功能演示 - 实际项目中这里会打开创建订单对话框');
        }

        function bulkCancel() {
            if (selectedOrders.size === 0) return;
            if (confirm(`确定要批量取消选中的 ${selectedOrders.size} 个订单吗？`)) {
                alert('批量取消功能演示 - 实际项目中这里会调用API');
            }
        }

        function bulkExport() {
            if (selectedOrders.size === 0) return;
            alert(`批量导出功能演示 - 将导出选中的 ${selectedOrders.size} 个订单`);
        }
    </script>
</body>
</html>
