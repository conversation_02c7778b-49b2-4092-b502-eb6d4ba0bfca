<template>
  <div class="universal-pickup-time-selector">
    <!-- 通用预约时间选择器（快递鸟等供应商） -->
    <div v-if="pickupTimeInfo && pickupTimeInfo.pickup_required" class="pickup-time-section">
      <div class="section-header">
        <el-icon class="header-icon"><Clock /></el-icon>
        <span class="section-title">预约取件时间</span>
        <el-tag type="success" size="small">{{ getProviderName() }}</el-tag>
      </div>
      
      <div class="pickup-time-notice">
        <el-alert
          title="预约说明"
          type="info"
          :closable="false"
          show-icon
        >
          <template #default>
            <div class="notice-content">
              <p>• 请从以下可用时间段中选择一个预约时间</p>
              <p v-if="pickupTimeInfo.min_advance_hours > 0">
                • 最少需要提前 {{ pickupTimeInfo.min_advance_hours }} 小时预约
              </p>
              <p v-if="pickupTimeInfo.supports_pickup_code">
                • 支持取件码功能
              </p>
            </div>
          </template>
        </el-alert>
      </div>

      <!-- 可用时间段选择 -->
      <div class="time-slots-container">
        <div class="time-slots-header">
          <span>可预约时间段：</span>
        </div>
        
        <div class="time-slots-grid">
          <div
            v-for="(slot, index) in availableSlots"
            :key="`${slot.slot_id}-${index}`"
            class="time-slot-item"
            :class="{
              'selected': isSlotSelected(slot),
              'disabled': slot.available === false
            }"
            @click="selectTimeSlot(slot)"
          >
            <div class="slot-name">{{ slot.slot_name }}</div>
            <div class="slot-time">{{ formatSlotTime(slot) }}</div>
            <div v-if="slot.description" class="slot-description">{{ slot.description }}</div>
            <div v-if="slot.available === false" class="slot-unavailable">已约满</div>
          </div>
        </div>
        
        <div v-if="availableSlots.length === 0" class="no-slots">
          <el-empty description="暂无可用预约时间段" />
        </div>
      </div>

      <!-- 选中的时间显示 -->
      <div v-if="selectedSlot" class="selected-time-display">
        <el-card class="selected-time-card">
          <div class="selected-time-content">
            <el-icon class="check-icon"><Check /></el-icon>
            <div class="selected-time-info">
              <div class="selected-date">{{ selectedSlot.slot_name }}</div>
              <div class="selected-time">{{ formatSlotTime(selectedSlot) }}</div>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 不支持预约的提示 -->
    <div v-else-if="!pickupTimeInfo" class="no-pickup-info">
      <el-alert
        title="此快递公司不支持预约取件"
        type="info"
        :closable="false"
        show-icon
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Clock, Check } from '@element-plus/icons-vue'
import type { PickupTimeInfo, PickupTimeSlot } from '@/api/model/kuaidiModel'

// Props
interface Props {
  pickupTimeInfo?: PickupTimeInfo
  modelValue?: {
    startTime: string
    endTime: string
  }
}

const props = withDefaults(defineProps<Props>(), {
  pickupTimeInfo: undefined,
  modelValue: undefined
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: { startTime: string; endTime: string } | undefined]
}>()

// 响应式数据
const selectedSlot = ref<PickupTimeSlot | null>(null)

// 计算属性
const availableSlots = computed(() => {
  return props.pickupTimeInfo?.available_slots || []
})

// 判断时间段是否被选中
const isSlotSelected = (slot: PickupTimeSlot) => {
  return selectedSlot.value?.slot_id === slot.slot_id
}

// 格式化时间段显示
const formatSlotTime = (slot: PickupTimeSlot) => {
  if (!slot.start_time || !slot.end_time) return ''
  
  // 提取时间部分（去掉日期）
  const startTime = slot.start_time.split(' ')[1] || slot.start_time
  const endTime = slot.end_time.split(' ')[1] || slot.end_time
  
  return `${startTime} - ${endTime}`
}

// 获取供应商名称
const getProviderName = () => {
  // 可以根据需要自定义供应商名称显示
  return '快递鸟'
}

// 选择时间段
const selectTimeSlot = (slot: PickupTimeSlot) => {
  if (slot.available === false) return

  selectedSlot.value = slot

  // 🔥 处理不同的时间格式
  const startDateTime = convertToISO8601(slot.start_time, props.pickupTimeInfo?.time_format)
  const endDateTime = convertToISO8601(slot.end_time, props.pickupTimeInfo?.time_format)

  emit('update:modelValue', {
    startTime: startDateTime,
    endTime: endDateTime
  })
}

// 辅助方法：将不同格式的时间转换为ISO 8601格式
const convertToISO8601 = (timeStr: string, timeFormat?: string) => {
  if (!timeStr) return ''
  
  // 快递鸟格式：YYYY-MM-DD HH:mm:ss 或 2025-07-25 09:00:00
  // 菜鸟格式：2006-01-02 15:04:05 或 2025-07-16 09:00:00
  
  // 统一处理：将空格替换为T，添加时区信息
  if (timeStr.includes(' ')) {
    return timeStr.replace(' ', 'T') + '+08:00'
  }
  
  // 如果已经是ISO格式，直接返回
  if (timeStr.includes('T')) {
    return timeStr.includes('+') ? timeStr : timeStr + '+08:00'
  }
  
  return timeStr
}

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  if (!newValue) {
    selectedSlot.value = null
    return
  }

  // 根据外部值找到对应的时间段
  // 将ISO 8601格式转换回后端格式进行匹配
  const startTime = newValue.startTime.replace('T', ' ').replace('+08:00', '')
  const endTime = newValue.endTime.replace('T', ' ').replace('+08:00', '')

  const matchingSlot = availableSlots.value.find(slot =>
    slot.start_time === startTime &&
    slot.end_time === endTime
  )

  selectedSlot.value = matchingSlot || null
}, { immediate: true })
</script>

<style scoped>
.universal-pickup-time-selector {
  margin-top: 16px;
}

.pickup-time-section {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  background-color: #fafafa;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  gap: 8px;
}

.header-icon {
  color: #409eff;
  font-size: 18px;
}

.section-title {
  font-weight: 600;
  font-size: 16px;
  color: #303133;
}

.pickup-time-notice {
  margin-bottom: 16px;
}

.notice-content p {
  margin: 4px 0;
  font-size: 14px;
}

.time-slots-container {
  margin-bottom: 16px;
}

.time-slots-header {
  margin-bottom: 12px;
  font-weight: 500;
  color: #606266;
}

.time-slots-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
}

.time-slot-item {
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #fff;
}

.time-slot-item:hover:not(.disabled) {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.time-slot-item.selected {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.time-slot-item.disabled {
  background-color: #f5f7fa;
  border-color: #dcdfe6;
  cursor: not-allowed;
  opacity: 0.6;
}

.slot-name {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.slot-time {
  color: #606266;
  font-size: 14px;
  margin-bottom: 4px;
}

.slot-description {
  color: #909399;
  font-size: 12px;
}

.slot-unavailable {
  color: #f56c6c;
  font-size: 12px;
  font-weight: 500;
}

.no-slots {
  text-align: center;
  padding: 20px;
}

.selected-time-display {
  margin-top: 16px;
}

.selected-time-card {
  border: 2px solid #67c23a;
  background-color: #f0f9ff;
}

.selected-time-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.check-icon {
  color: #67c23a;
  font-size: 20px;
}

.selected-time-info {
  flex: 1;
}

.selected-date {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.selected-time {
  color: #606266;
  font-size: 14px;
}

.no-pickup-info {
  margin-top: 16px;
}
</style>
