<template>
  <div class="cainiao-pickup-time-selector">
    <!-- 菜鸟预约时间选择器 -->
    <div v-if="pickupTimeInfo && pickupTimeInfo.pickup_required" class="pickup-time-section">
      <div class="section-header">
        <el-icon class="header-icon"><Clock /></el-icon>
        <span class="section-title">预约取件时间</span>
        <el-tag type="warning" size="small">菜鸟裹裹专用</el-tag>
      </div>
      
      <div class="pickup-time-notice">
        <el-alert
          title="重要提示"
          type="info"
          :closable="false"
          show-icon
        >
          <template #default>
            <div class="notice-content">
              <p>• 菜鸟裹裹必须按查价结果预约时间，不可自定义</p>
              <p>• 请从以下可用时间段中选择一个预约时间</p>
              <p v-if="pickupTimeInfo.min_advance_hours > 0">
                • 最少需要提前 {{ pickupTimeInfo.min_advance_hours }} 小时预约
              </p>
            </div>
          </template>
        </el-alert>
      </div>

      <!-- 可用时间段选择 -->
      <div class="time-slots-container">
        <div class="time-slots-header">
          <span>可预约时间段：</span>
        </div>
        
        <div class="time-slots-grid">
          <div
            v-for="(slot, index) in availableSlots"
            :key="`${slot.slot_id}-${index}`"
            class="time-slot-item"
            :class="{
              'selected': isSlotSelected(slot),
              'disabled': slot.available === false
            }"
            @click="selectTimeSlot(slot)"
          >
            <div class="slot-name">{{ slot.slot_name }}</div>
            <div class="slot-time">{{ formatSlotTime(slot) }}</div>
            <div v-if="slot.description" class="slot-description">{{ slot.description }}</div>
            <div v-if="slot.available === false" class="slot-unavailable">已约满</div>
          </div>
        </div>
        
        <div v-if="availableSlots.length === 0" class="no-slots">
          <el-empty description="暂无可用预约时间段" />
        </div>
      </div>

      <!-- 选中的时间显示 -->
      <div v-if="selectedSlot" class="selected-time-display">
        <el-card class="selected-time-card">
          <div class="selected-time-content">
            <el-icon class="check-icon"><Check /></el-icon>
            <div class="selected-time-info">
              <div class="selected-date">{{ selectedSlot.slot_name }}</div>
              <div class="selected-time">{{ formatSlotTime(selectedSlot) }}</div>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 非菜鸟供应商的提示 -->
    <div v-else-if="!pickupTimeInfo" class="no-pickup-info">
      <el-alert
        title="此快递公司不支持预约取件"
        type="info"
        :closable="false"
        show-icon
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Clock, Check } from '@element-plus/icons-vue'
import type { PickupTimeInfo, PickupTimeSlot } from '@/api/model/kuaidiModel'

// Props
interface Props {
  pickupTimeInfo?: PickupTimeInfo
  modelValue?: {
    startTime: string
    endTime: string
  }
}

const props = withDefaults(defineProps<Props>(), {
  pickupTimeInfo: undefined,
  modelValue: undefined
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: { startTime: string; endTime: string } | undefined]
}>()

// 响应式数据
const selectedSlot = ref<PickupTimeSlot | null>(null)

// 计算属性
const availableSlots = computed(() => {
  if (!props.pickupTimeInfo?.available_slots) return []
  // 如果没有available字段，默认为可用
  return props.pickupTimeInfo.available_slots.filter(slot => slot.available !== false)
})

// 方法
const formatSlotTime = (slot: PickupTimeSlot) => {
  // 从完整时间字符串中提取时间部分
  const startTime = slot.start_time.split(' ')[1]?.substring(0, 5) || ''
  const endTime = slot.end_time.split(' ')[1]?.substring(0, 5) || ''
  return `${startTime} - ${endTime}`
}

const isSlotSelected = (slot: PickupTimeSlot) => {
  if (!selectedSlot.value) return false
  return selectedSlot.value.slot_id === slot.slot_id
}

const selectTimeSlot = (slot: PickupTimeSlot) => {
  if (slot.available === false) return

  selectedSlot.value = slot

  // 直接使用后端返回的完整时间格式，转换为ISO 8601格式
  const startDateTime = convertToISO8601(slot.start_time)
  const endDateTime = convertToISO8601(slot.end_time)

  emit('update:modelValue', {
    startTime: startDateTime,
    endTime: endDateTime
  })
}

// 辅助方法：将后端时间格式转换为ISO 8601格式
const convertToISO8601 = (timeStr: string) => {
  // 后端格式：2025-07-16 09:00:00
  // 转换为：2025-07-16T09:00:00+08:00
  return timeStr.replace(' ', 'T') + '+08:00'
}

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  if (!newValue) {
    selectedSlot.value = null
    return
  }

  // 根据外部值找到对应的时间段
  // 将ISO 8601格式转换回后端格式进行匹配
  const startTime = newValue.startTime.replace('T', ' ').replace('+08:00', '')
  const endTime = newValue.endTime.replace('T', ' ').replace('+08:00', '')

  const matchingSlot = availableSlots.value.find(slot =>
    slot.start_time === startTime &&
    slot.end_time === endTime
  )

  selectedSlot.value = matchingSlot || null
}, { immediate: true })
</script>

<style scoped>
.cainiao-pickup-time-selector {
  margin-top: 16px;
}

.pickup-time-section {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  background-color: #fafafa;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
}

.header-icon {
  color: #409eff;
}

.section-title {
  font-weight: 600;
  color: #303133;
}

.pickup-time-notice {
  margin-bottom: 16px;
}

.notice-content p {
  margin: 4px 0;
  font-size: 14px;
}

.time-slots-container {
  margin-top: 16px;
}

.time-slots-header {
  margin-bottom: 12px;
  font-weight: 500;
  color: #606266;
}

.time-slots-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
}

.time-slot-item {
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.3s;
  background-color: white;
}

.time-slot-item:hover:not(.disabled) {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.time-slot-item.selected {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.time-slot-item.disabled {
  background-color: #f5f7fa;
  border-color: #dcdfe6;
  cursor: not-allowed;
  opacity: 0.6;
}

.slot-name {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.slot-time {
  color: #606266;
  font-size: 14px;
}

.slot-description {
  color: #909399;
  font-size: 12px;
  margin-top: 4px;
}

.slot-unavailable {
  color: #f56c6c;
  font-size: 12px;
  margin-top: 4px;
}

.no-slots {
  text-align: center;
  padding: 40px 0;
}

.selected-time-display {
  margin-top: 16px;
}

.selected-time-card {
  border: 1px solid #67c23a;
  background-color: #f0f9ff;
}

.selected-time-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.check-icon {
  color: #67c23a;
  font-size: 20px;
}

.selected-time-info {
  flex: 1;
}

.selected-date {
  font-weight: 600;
  color: #303133;
}

.selected-time {
  color: #606266;
  font-size: 14px;
}

.no-pickup-info {
  margin-top: 16px;
}
</style>
