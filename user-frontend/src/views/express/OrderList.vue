<template>
  <div class="express-order-list">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>快递订单管理</span>
          <el-button type="primary" @click="showCreateDialog = true">
            <el-icon><Plus /></el-icon>
            创建订单
          </el-button>
        </div>
      </template>

      <!-- 搜索筛选区域 -->
      <div class="search-area">
        <el-form :model="searchForm" inline class="search-form">
          <el-form-item label="平台订单号">
            <el-input
              v-model="searchForm.order_no"
              placeholder="请输入平台订单号"
              clearable
              class="search-input"
            />
          </el-form-item>
          <el-form-item label="客户订单号">
            <el-input
              v-model="searchForm.customer_order_no"
              placeholder="请输入客户订单号"
              clearable
              class="search-input"
            />
          </el-form-item>
          <el-form-item label="运单号">
            <el-input
              v-model="searchForm.tracking_no"
              placeholder="点击进行批量运单号查询"
              clearable
              class="search-input tracking-no-clickable"
              readonly
              @click="showBatchTrackingDialog = true"
            >
              <template #suffix>
                <el-icon class="tracking-input-icon"><Search /></el-icon>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="订单状态">
            <el-select
              v-model="searchForm.status"
              placeholder="请选择状态"
              clearable
              :loading="configLoading"
              class="search-select"
            >
              <el-option
                v-for="status in orderStatusOptions"
                :key="status.value"
                :label="status.label"
                :value="status.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="快递公司">
            <el-select
              v-model="searchForm.express_type"
              placeholder="请选择快递公司"
              clearable
              :loading="configLoading"
              class="search-select"
            >
              <el-option
                v-for="company in expressCompanyOptions"
                :key="company.value"
                :label="company.label"
                :value="company.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="供应商">
            <el-select
              v-model="searchForm.provider"
              placeholder="请选择供应商"
              clearable
              class="search-select"
            >
              <el-option label="快递鸟" value="kuaidiniao" />
              <el-option label="快递100" value="kuaidi100" />
              <el-option label="易达" value="yida" />
              <el-option label="云通" value="yuntong" />
              <el-option label="菜鸟" value="cainiao" />
              <el-option label="京东" value="jd" />
              <el-option label="德邦" value="dbl" />
            </el-select>
          </el-form-item>
          <el-form-item label="重量异常">
            <el-select
              v-model="searchForm.weight_anomaly"
              placeholder="请选择重量异常类型"
              clearable
              class="search-select"
            >
              <el-option label="超重订单" value="overweight" />
              <el-option label="超轻订单" value="underweight" />
            </el-select>
          </el-form-item>
        </el-form>

        <!-- 时间筛选区域 -->
        <div class="time-filter-section">
          <el-form :model="searchForm" inline class="time-filter-form">
            <el-form-item label="创建时间">
              <div class="time-filter-container">
                <!-- 日期范围选择器 -->
                <el-date-picker
                  v-model="dateRange"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                  style="width: 380px; margin-right: 10px"
                  @change="handleDateRangeChange"
                />

                <!-- 快捷时间选项 -->
                <div class="quick-time-buttons">
                  <el-button
                    size="small"
                    :type="isQuickTimeActive('today') ? 'primary' : ''"
                    @click="setQuickTime('today')"
                  >
                    今天
                  </el-button>
                  <el-button
                    size="small"
                    :type="isQuickTimeActive('yesterday') ? 'primary' : ''"
                    @click="setQuickTime('yesterday')"
                  >
                    昨天
                  </el-button>
                  <el-button
                    size="small"
                    :type="isQuickTimeActive('last7days') ? 'primary' : ''"
                    @click="setQuickTime('last7days')"
                  >
                    最近7天
                  </el-button>
                  <el-button
                    size="small"
                    :type="isQuickTimeActive('last30days') ? 'primary' : ''"
                    @click="setQuickTime('last30days')"
                  >
                    最近30天
                  </el-button>
                  <el-button
                    size="small"
                    @click="clearTimeFilter"
                  >
                    清除
                  </el-button>
                </div>
              </div>
            </el-form-item>
          </el-form>
        </div>



        <!-- 搜索按钮区域 -->
        <div class="search-buttons-section">
          <el-button
            type="primary"
            @click="handleUnifiedSearch"
            :loading="loading"
            class="unified-search-btn"
          >
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset" class="reset-btn">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </div>
      </div>

      <!-- 🔥 新增：批量查询结果统计 -->
      <div v-if="batchQueryStats" class="batch-stats-section">
        <el-alert
          :title="`批量查询结果统计`"
          type="info"
          :closable="false"
          class="batch-stats-alert"
        >
          <template #default>
            <div class="batch-stats-content">
              <div class="stats-item">
                <span class="stats-label">查询运单号：</span>
                <span class="stats-value">{{ batchQueryStats.total_queried }} 个</span>
              </div>
              <div class="stats-item">
                <span class="stats-label">找到订单：</span>
                <span class="stats-value success">{{ batchQueryStats.found_count }} 个</span>
              </div>
              <div class="stats-item">
                <span class="stats-label">未找到：</span>
                <span class="stats-value error">{{ (batchQueryStats.not_found || []).length }} 个</span>
              </div>
              <div class="stats-item">
                <span class="stats-label">查询耗时：</span>
                <span class="stats-value">{{ batchQueryStats.query_time }}</span>
              </div>
            </div>

            <!-- 未找到的运单号列表 -->
            <div v-if="(batchQueryStats.not_found || []).length > 0" class="not-found-section">
              <div class="not-found-title">未找到的运单号：</div>
              <div class="not-found-list">
                <el-tag
                  v-for="trackingNo in (batchQueryStats.not_found || [])"
                  :key="trackingNo"
                  type="danger"
                  size="small"
                  class="not-found-tag"
                >
                  {{ trackingNo }}
                </el-tag>
              </div>
            </div>
          </template>
        </el-alert>
      </div>

      <!-- 订单列表 -->
      <div class="table-container">
        <el-table
          :data="orderList"
          v-loading="loading"
          stripe
          class="order-table"
          :scroll-x="true"
          style="width: 100%"
        >
          <!-- 🔥 优化：固定左侧关键列 -->
          <el-table-column prop="created_at" label="创建时间" width="140" fixed="left">
            <template #default="{ row }">
              <div class="time-info">
                <div class="full-time">{{ formatDate(row.created_at) }}</div>
                <div class="relative-time">{{ getRelativeTime(row.created_at) }}</div>
              </div>
            </template>
          </el-table-column>

          <!-- 🔥 优化：合并订单号列，减少宽度 -->
          <el-table-column label="订单信息" width="140" show-overflow-tooltip>
            <template #default="{ row }">
              <div class="order-info">
                <div class="platform-order" :title="row.platform_order_no || row.order_no">
                  {{ row.platform_order_no || row.order_no }}
                </div>
                <div class="customer-order" :title="row.customer_order_no">
                  客户: {{ row.customer_order_no }}
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="tracking_no" label="运单号" width="140" show-overflow-tooltip />

          <!-- 🔥 优化：合并快递公司和供应商 -->
          <el-table-column label="快递/供应商" width="120">
            <template #default="{ row }">
              <div class="express-provider-info">
                <div class="express-name">{{ getExpressName(row) }}</div>
                <el-tag :type="getProviderTagType(row.provider)" size="small" class="provider-tag">
                  {{ getProviderName(row) }}
                </el-tag>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="status_desc" label="状态" width="90">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)" size="small">{{ getStatusDesc(row.status) }}</el-tag>
            </template>
          </el-table-column>
          <!-- 🔥 优化：合并价格信息 -->
          <el-table-column label="价格信息" width="100">
            <template #default="{ row }">
              <div class="price-info">
                <div class="price-row">
                  <span class="label">预收:</span>
                  <span class="price-value">¥{{ formatPrice(row.price || 0) }}</span>
                </div>
                <div class="price-row">
                  <span class="label">实收:</span>
                  <span class="price-value actual">¥{{ formatPrice(row.actual_fee || 0) }}</span>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="pickup_time" label="预约时间" width="150">
            <template #default="{ row }">
              <div v-if="hasPickupTime(row)" class="pickup-time-info">
                <div class="pickup-time-range">
                  <el-icon><Clock /></el-icon>
                  {{ formatPickupTime(row) }}
                </div>
              </div>
              <span v-else class="no-pickup-time">-</span>
            </template>
          </el-table-column>
          <!-- 🔥 优化：简化重量体积信息 -->
          <el-table-column label="重量信息" width="120">
            <template #default="{ row }">
              <div class="weight-info">
                <div class="weight-row">
                  <span class="weight-value">{{ formatWeight(row.weight) }}kg</span>
                  <!-- 重量异常标记 -->
                  <el-tag
                    v-if="getWeightAnomalyType(row)"
                    :type="getWeightAnomalyTagType(row)"
                    size="small"
                    class="weight-anomaly-tag"
                  >
                    {{ getWeightAnomalyText(row) }}
                  </el-tag>
                </div>
                <div v-if="row.actual_weight > 0" class="actual-weight">
                  实际: {{ formatWeight(row.actual_weight) }}kg
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="courier_info" label="揽件员" width="130">
            <template #default="{ row }">
              <div v-if="row.courier_name || row.courier_phone || row.pickup_code" class="courier-info">
                <div v-if="row.courier_name" class="courier-name">{{ row.courier_name }}</div>
                <div v-if="row.courier_phone" class="courier-phone">{{ row.courier_phone }}</div>
                <div v-if="row.pickup_code" class="pickup-code">
                  <el-tag type="success" size="small">{{ row.pickup_code }}</el-tag>
                </div>
              </div>
              <span v-else class="no-courier">-</span>
            </template>
          </el-table-column>
          <!-- 🔥 优化：固定右侧操作列 -->
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="{ row }">
              <div class="action-buttons">
                <el-button type="primary" size="small" @click="viewOrder(row)">详情</el-button>
                <!-- 🔥 修复：失败订单不显示取消按钮，只显示删除按钮 -->
                <el-button
                  v-if="canCancelOrder(row)"
                  type="danger"
                  size="small"
                  @click="cancelOrder(row)"
                >
                  取消
                </el-button>
                <!-- 🔥 新增：失败订单删除按钮 -->
                <el-button
                  v-if="canDeleteFailedOrder(row)"
                  type="danger"
                  size="small"
                  @click="deleteFailedOrder(row)"
                >
                  删除
                </el-button>
                <!-- 🔥 新增：申请售后按钮 -->
                <el-button
                  v-if="canApplyAfterSales(row)"
                  type="warning"
                  size="small"
                  @click="applyAfterSales(row)"
                >
                  申请售后
                </el-button>
                <!-- 🔥 新增：重试下单按钮 -->
                <el-button
                  v-if="canRetryOrder(row)"
                  type="primary"
                  size="small"
                  @click="retryOrder(row)"
                >
                  重试下单
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 🔥 新增：空状态提示 -->
      <div v-if="!loading && orderList.length === 0" class="empty-state">
        <div class="empty-icon">
          <el-icon size="64" color="#c0c4cc">
            <Box />
          </el-icon>
        </div>
        <div class="empty-text">
          <h3>暂无订单数据</h3>
          <p v-if="hasActiveFilters">当前筛选条件下没有找到订单，请尝试调整筛选条件</p>
          <p v-else>您还没有创建任何订单，点击上方"创建订单"按钮开始下单</p>
        </div>
        <div class="empty-actions">
          <el-button v-if="hasActiveFilters" @click="handleReset">
            <el-icon><Refresh /></el-icon>
            清除筛选
          </el-button>
          <el-button v-else type="primary" @click="showCreateDialog = true">
            <el-icon><Plus /></el-icon>
            创建订单
          </el-button>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.page_size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 创建订单对话框 -->
    <CreateOrderDialog v-model:visible="showCreateDialog" @success="handleCreateSuccess" />

    <!-- 订单详情对话框 -->
    <OrderDetailDialog v-model:visible="showDetailDialog" :order="selectedOrder" />

    <!-- 🔥 新增：售后申请对话框 -->
    <CreateWorkOrderDialog
      v-model:visible="showAfterSalesDialog"
      :prefilled-order-data="selectedOrderForAfterSales || undefined"
      @success="handleAfterSalesSuccess"
    />

    <!-- 🔥 新增：批量运单号查询对话框 -->
    <el-dialog
      v-model="showBatchTrackingDialog"
      title="批量运单号查询"
      width="600px"
      :close-on-click-modal="false"
      class="batch-tracking-dialog"
    >
      <div class="batch-tracking-content">
        <div class="batch-input-section">
          <el-form :model="batchQueryForm" label-width="0">
            <el-form-item>
              <div class="batch-input-container">
                <el-input
                  v-model="batchQueryForm.tracking_nos_text"
                  type="textarea"
                  :rows="6"
                  placeholder="请输入运单号，支持多种格式：
1. 每行一个运单号
2. 逗号分隔：SF123,YT456,ZTO789
3. 空格分隔：SF123 YT456 ZTO789
最多支持50个运单号"
                  class="batch-textarea"
                  :maxlength="2000"
                  show-word-limit
                />
                <div class="batch-input-tips">
                  <span class="tip-text">支持多种格式输入，自动去重和格式化</span>
                  <span v-if="parsedTrackingNos.length > 0" class="parsed-count">
                    已识别 {{ parsedTrackingNos.length }} 个运单号
                  </span>
                </div>
              </div>
            </el-form-item>
          </el-form>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showBatchTrackingDialog = false">取消</el-button>
          <el-button @click="clearBatchInput">清空</el-button>
          <el-button
            type="primary"
            @click="handleBatchTrackingQuery"
            :loading="loading"
            :disabled="parsedTrackingNos.length === 0"
          >
            <el-icon><Search /></el-icon>
            批量查询 ({{ parsedTrackingNos.length }})
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted, computed } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { Plus, Search, Refresh, Box, Clock } from '@element-plus/icons-vue'
  import { ExpressService, ConfigService } from '@/api'
  import type { OrderListItem, OrderListRequest, ExpressCompany, OrderStatus, BatchQueryStats } from '@/api'
  import { ErrorHandler } from '@/utils/errorHandler'

  import CreateOrderDialog from './components/CreateOrderDialog.vue'
  import OrderDetailDialog from './components/OrderDetailDialog.vue'
  // 🔥 新增：导入工单创建组件
  import CreateWorkOrderDialog from '../workorder/components/CreateWorkOrderDialog.vue'

  // 响应式数据
  const loading = ref(false)
  const configLoading = ref(false)
  const showCreateDialog = ref(false)
  const showDetailDialog = ref(false)
  const showAfterSalesDialog = ref(false) // 🔥 新增：售后申请对话框状态
  const showBatchTrackingDialog = ref(false) // 🔥 新增：批量运单号查询对话框状态
  const orderList = ref<OrderListItem[]>([])
  const selectedOrder = ref<OrderListItem | null>(null)
  const selectedOrderForAfterSales = ref<OrderListItem | null>(null) // 🔥 新增：售后申请选中的订单

  // 🔥 新增：批量查询相关数据
  const batchQueryStats = ref<BatchQueryStats | null>(null) // 批量查询统计信息
  const batchQueryForm = reactive({
    tracking_nos_text: '' // 批量运单号输入文本
  })

  // 配置数据
  const expressCompanies = ref<ExpressCompany[]>([])
  const orderStatuses = ref<OrderStatus[]>([])

  // 计算属性
  const expressCompanyOptions = computed(() =>
    expressCompanies.value.map((company) => ({
      label: company.name,
      value: company.code
    }))
  )

  const orderStatusOptions = computed(() =>
    orderStatuses.value.map((status) => ({
      label: status.name,
      value: status.code
    }))
  )

  // 🔥 新增：判断是否有激活的筛选条件
  const hasActiveFilters = computed(() => {
    return !!(
      searchForm.status ||
      searchForm.express_type ||
      searchForm.provider ||
      searchForm.customer_order_no ||
      searchForm.order_no ||
      searchForm.tracking_no ||
      searchForm.weight_anomaly ||
      searchForm.start_time ||
      searchForm.end_time
    )
  })

  // 🔥 新增：解析批量运单号的计算属性
  const parsedTrackingNos = computed(() => {
    if (!batchQueryForm.tracking_nos_text.trim()) {
      return []
    }

    // 支持多种分隔符：换行、逗号、分号、空格、制表符
    const separators = /[\n,;，；\s\t]+/
    const trackingNos = batchQueryForm.tracking_nos_text
      .split(separators)
      .map(no => no.trim())
      .filter(no => no.length > 0)

    // 去重
    const uniqueTrackingNos = [...new Set(trackingNos)]

    // 验证运单号格式（长度在8-30位之间）
    const validTrackingNos = uniqueTrackingNos.filter(no =>
      no.length >= 8 && no.length <= 30
    )

    return validTrackingNos.slice(0, 50) // 最多50个
  })

  // 搜索表单
  const searchForm = reactive<OrderListRequest>({
    page: 1,
    page_size: 20,
    status: undefined,
    express_type: undefined,
    provider: undefined,
    customer_order_no: undefined,
    order_no: undefined,
    tracking_no: undefined,
    // 🔥 新增：批量查询字段
    tracking_nos: undefined,
    batch_mode: undefined,
    weight_anomaly: undefined,
    start_time: undefined,
    end_time: undefined,
    sort_by: 'created_at',  // 🔥 默认按创建时间排序
    sort_order: 'desc'      // 🔥 默认倒序（最新在前）
  })

  // 🔥 新增：时间筛选相关数据
  const dateRange = ref<[string, string] | null>(null)
  const currentQuickTime = ref<string>('')

  // 分页信息
  const pagination = reactive({
    page: 1,
    page_size: 20,
    total: 0
  })

  // 加载系统配置
  const loadSystemConfig = async () => {
    configLoading.value = true
    try {
      const [companies, statuses] = await Promise.all([
        ConfigService.getExpressCompanies(),
        ConfigService.getOrderStatuses()
      ])

      expressCompanies.value = companies
      orderStatuses.value = statuses
    } catch (error) {
      ErrorHandler.handleApiError(error, false)
      // 使用默认配置作为降级方案
      console.warn('使用默认配置')
    } finally {
      configLoading.value = false
    }
  }

  // 获取订单列表
  const getOrderList = async () => {
    loading.value = true
    try {
      const params = {
        ...searchForm,
        page: pagination.page,
        page_size: pagination.page_size
      }

      const response = await ExpressService.getOrderList(params)

      if (response.success && response.data) {
        orderList.value = response.data.items || []
        pagination.total = response.data.total || 0

        // 🔥 新增：处理批量查询统计信息
        batchQueryStats.value = response.data.batch_stats || null
      } else {
        throw new Error(response.message || '获取订单列表失败')
      }
    } catch (error) {
      ErrorHandler.handleApiError(error)
      // 清空列表数据
      orderList.value = []
      pagination.total = 0
    } finally {
      loading.value = false
    }
  }

  // 🔥 修改：统一搜索方法（现在只执行普通搜索）
  const handleUnifiedSearch = () => {
    handleSearch()
  }

  // 搜索
  const handleSearch = () => {
    pagination.page = 1
    saveFiltersToStorage() // 保存筛选条件
    getOrderList()
  }

  // 重置
  const handleReset = () => {
    // 重置搜索表单到初始状态
    Object.assign(searchForm, {
      page: 1,
      page_size: 20,
      order_no: undefined,
      tracking_no: undefined,
      // 🔥 新增：重置批量查询字段
      tracking_nos: undefined,
      batch_mode: undefined,
      status: undefined,
      express_type: undefined,
      provider: undefined,
      customer_order_no: undefined,
      weight_anomaly: undefined,
      start_time: undefined,
      end_time: undefined,
      sort_by: 'created_at',
      sort_order: 'desc'
    })

    // 🔥 新增：重置时间筛选
    dateRange.value = null
    currentQuickTime.value = ''

    // 🔥 新增：重置批量查询
    batchQueryForm.tracking_nos_text = ''
    batchQueryStats.value = null

    // 重置分页到第一页
    pagination.page = 1
    pagination.page_size = 20

    // 清除存储的筛选条件
    clearStoredFilters()

    // 重新获取订单列表
    getOrderList()

    // 提示用户重置成功
    ElMessage.success('搜索条件已重置')
  }

  // 🔥 新增：批量查询方法
  const handleBatchQuery = async () => {
    if (parsedTrackingNos.value.length === 0) {
      ElMessage.warning('请输入有效的运单号')
      return
    }

    if (parsedTrackingNos.value.length > 50) {
      ElMessage.warning('批量查询最多支持50个运单号')
      return
    }

    try {
      // 清空其他搜索条件，只保留批量运单号查询
      Object.assign(searchForm, {
        page: 1,
        page_size: 20,
        order_no: undefined,
        tracking_no: undefined,
        status: undefined,
        express_type: undefined,
        provider: undefined,
        customer_order_no: undefined,
        weight_anomaly: undefined,
        start_time: undefined,
        end_time: undefined,
        sort_by: 'created_at',
        sort_order: 'desc',
        // 设置批量查询参数
        tracking_nos: parsedTrackingNos.value,
        batch_mode: true
      })

      // 重置分页
      pagination.page = 1

      // 执行查询
      await getOrderList()

      // 提示查询结果
      if (batchQueryStats.value) {
        const { total_queried, found_count, not_found } = batchQueryStats.value
        const notFoundCount = (not_found || []).length
        if (found_count > 0) {
          ElMessage.success(`批量查询完成：找到 ${found_count} 个订单，未找到 ${notFoundCount} 个`)
        } else {
          ElMessage.warning(`批量查询完成：未找到任何订单`)
        }
      }
    } catch (error) {
      console.error('批量查询失败:', error)
      ErrorHandler.handleApiError(error)
    }
  }

  // 🔥 新增：从对话框执行批量查询
  const handleBatchTrackingQuery = async () => {
    await handleBatchQuery()
    // 查询成功后关闭对话框
    showBatchTrackingDialog.value = false
  }

  // 🔥 新增：清空批量输入
  const clearBatchInput = () => {
    batchQueryForm.tracking_nos_text = ''
  }

  // 🔥 新增：时间筛选相关方法

  // 处理日期范围变化
  const handleDateRangeChange = (value: [string, string] | null) => {
    if (value && value.length === 2) {
      searchForm.start_time = value[0]
      searchForm.end_time = value[1]
      currentQuickTime.value = '' // 清除快捷时间选择状态
    } else {
      searchForm.start_time = undefined
      searchForm.end_time = undefined
    }
  }

  // 设置快捷时间
  const setQuickTime = (type: string) => {
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())

    let startTime: Date
    let endTime: Date = new Date(today.getTime() + 24 * 60 * 60 * 1000 - 1) // 今天23:59:59

    switch (type) {
      case 'today':
        startTime = new Date(today)
        endTime = new Date(today.getTime() + 24 * 60 * 60 * 1000 - 1)
        break
      case 'yesterday':
        startTime = new Date(today.getTime() - 24 * 60 * 60 * 1000)
        endTime = new Date(today.getTime() - 1)
        break
      case 'last7days':
        startTime = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
        endTime = new Date(today.getTime() + 24 * 60 * 60 * 1000 - 1)
        break
      case 'last30days':
        startTime = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)
        endTime = new Date(today.getTime() + 24 * 60 * 60 * 1000 - 1)
        break
      default:
        return
    }

    // 格式化时间字符串
    const formatDateTime = (date: Date) => {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    }

    const startTimeStr = formatDateTime(startTime)
    const endTimeStr = formatDateTime(endTime)

    // 更新日期范围选择器
    dateRange.value = [startTimeStr, endTimeStr]

    // 更新搜索表单
    searchForm.start_time = startTimeStr
    searchForm.end_time = endTimeStr

    // 记录当前快捷时间类型
    currentQuickTime.value = type
  }

  // 判断快捷时间是否激活
  const isQuickTimeActive = (type: string) => {
    return currentQuickTime.value === type
  }

  // 清除时间筛选
  const clearTimeFilter = () => {
    dateRange.value = null
    searchForm.start_time = undefined
    searchForm.end_time = undefined
    currentQuickTime.value = ''
  }

  // 🔥 新增：筛选条件持久化
  const STORAGE_KEY = 'express_order_list_filters'

  // 保存筛选条件到localStorage
  const saveFiltersToStorage = () => {
    const filters = {
      status: searchForm.status,
      express_type: searchForm.express_type,
      provider: searchForm.provider,
      customer_order_no: searchForm.customer_order_no,
      order_no: searchForm.order_no,
      tracking_no: searchForm.tracking_no,
      weight_anomaly: searchForm.weight_anomaly,
      start_time: searchForm.start_time,
      end_time: searchForm.end_time,
      dateRange: dateRange.value,
      currentQuickTime: currentQuickTime.value,
      sort_by: searchForm.sort_by,
      sort_order: searchForm.sort_order
    }
    localStorage.setItem(STORAGE_KEY, JSON.stringify(filters))
  }

  // 从localStorage恢复筛选条件
  const loadFiltersFromStorage = () => {
    try {
      const saved = localStorage.getItem(STORAGE_KEY)
      if (saved) {
        const filters = JSON.parse(saved)

        // 恢复搜索表单
        if (filters.status) searchForm.status = filters.status
        if (filters.express_type) searchForm.express_type = filters.express_type
        if (filters.provider) searchForm.provider = filters.provider
        if (filters.customer_order_no) searchForm.customer_order_no = filters.customer_order_no
        if (filters.order_no) searchForm.order_no = filters.order_no
        if (filters.tracking_no) searchForm.tracking_no = filters.tracking_no
        if (filters.weight_anomaly) searchForm.weight_anomaly = filters.weight_anomaly
        if (filters.start_time) searchForm.start_time = filters.start_time
        if (filters.end_time) searchForm.end_time = filters.end_time
        if (filters.sort_by) searchForm.sort_by = filters.sort_by
        if (filters.sort_order) searchForm.sort_order = filters.sort_order

        // 恢复时间筛选状态
        if (filters.dateRange) dateRange.value = filters.dateRange
        if (filters.currentQuickTime) currentQuickTime.value = filters.currentQuickTime
      }
    } catch (error) {
      console.warn('恢复筛选条件失败:', error)
    }
  }

  // 清除存储的筛选条件
  const clearStoredFilters = () => {
    localStorage.removeItem(STORAGE_KEY)
  }

  // 分页处理
  const handleSizeChange = (size: number) => {
    pagination.page_size = size
    pagination.page = 1
    getOrderList()
  }

  const handleCurrentChange = (page: number) => {
    pagination.page = page
    getOrderList()
  }

  // 查看订单详情
  const viewOrder = (order: OrderListItem) => {
    selectedOrder.value = order
    showDetailDialog.value = true
  }

  // 取消订单
  const cancelOrder = async (order: OrderListItem) => {
    try {
      await ElMessageBox.confirm(
        `确定要取消订单 ${order.order_no} 吗？取消后将无法恢复。`,
        '确认取消订单',
        {
          confirmButtonText: '确定取消',
          cancelButtonText: '暂不取消',
          type: 'warning',
          dangerouslyUseHTMLString: false
        }
      )

      const response = await ExpressService.cancelOrder({
        order_no: order.order_no,
        reason: '用户主动取消'
      })

      if (response.success) {
        // 根据响应消息判断是否为两阶段取消流程
        if (response.message && response.message.includes('等待供应商确认')) {
          ElMessage.success('取消请求已发起，等待供应商确认')
        } else {
          ElMessage.success(response.message || '订单取消成功')
        }
        await getOrderList() // 刷新列表
      } else {
        throw new Error(response.message || '取消订单失败')
      }
    } catch (error) {
      if (error !== 'cancel') {
        ErrorHandler.handleApiError(error)
      }
    }
  }

  // 创建订单成功回调
  const handleCreateSuccess = () => {
    // 🔥 强制刷新订单列表，确保新订单显示在最前面
    pagination.page = 1  // 跳转到第一页
    searchForm.sort_by = 'created_at'
    searchForm.sort_order = 'desc'
    getOrderList()
    ElMessage.success('订单创建成功，已为您刷新列表')
  }

  // 🔥 新增：申请售后
  const applyAfterSales = (order: OrderListItem) => {
    selectedOrderForAfterSales.value = order
    showAfterSalesDialog.value = true
  }

  // 🔥 新增：售后申请成功回调
  const handleAfterSalesSuccess = () => {
    showAfterSalesDialog.value = false
    selectedOrderForAfterSales.value = null
    ElMessage.success('售后申请已提交，我们将尽快为您处理')
    // 可选：刷新订单列表以显示最新状态
    getOrderList()
  }

  // 🔥 新增：判断订单是否可以申请售后
  const canApplyAfterSales = (order: OrderListItem) => {
    // 仅当订单处于"已取消 / 作废"相关状态时禁止申请售后，其余状态均可申请
    const forbiddenStatuses = ['cancelled', 'canceled', 'cancelling', 'voided']
    return !forbiddenStatuses.includes(order.status)
  }

  // 🔥 新增：判断订单是否可以重试
  const canRetryOrder = (order: OrderListItem) => {
    // 只有失败状态的订单可以重试，且必须标记为可重试
    return order.status === 'failed' && order.can_retry === true
  }

  // 🔥 新增：重试下单
  const retryOrder = async (order: OrderListItem) => {
    try {
      await ElMessageBox.confirm(
        `确定要重试订单 ${order.customer_order_no} 吗？`,
        '重试下单',
        {
          confirmButtonText: '确定重试',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      loading.value = true
      
      // 这里可以调用重试API，或者提取失败订单的参数重新下单
      // 暂时显示提示信息
      ElMessage.info('重试功能正在开发中，敬请期待')
      
      // TODO: 实现重试逻辑
      // const response = await ExpressService.retryOrder(order.id)
      // if (response.success) {
      //   ElMessage.success('订单重试成功')
      //   await getOrderList()
      // } else {
      //   throw new Error(response.message || '重试失败')
      // }
      
    } catch (error) {
      if (error !== 'cancel') {
        ErrorHandler.handleApiError(error)
      }
    } finally {
      loading.value = false
    }
  }

  // 🔥 新增：判断失败订单是否可以删除
  const canDeleteFailedOrder = (order: OrderListItem) => {
    // 只有失败状态的订单可以删除
    return order.status === 'failed'
  }

  // 🔥 新增：删除失败订单
  const deleteFailedOrder = async (order: OrderListItem) => {
    try {
      await ElMessageBox.confirm(
        `确定要删除失败订单 ${order.customer_order_no} 吗？删除后将无法恢复。`,
        '确认删除失败订单',
        {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          type: 'warning',
          dangerouslyUseHTMLString: false
        }
      )

      loading.value = true

      const response = await ExpressService.deleteFailedOrder({
        order_no: order.order_no
      })

      if (response.success) {
        ElMessage.success('失败订单删除成功')
        await getOrderList() // 刷新列表
      } else {
        throw new Error(response.message || '删除失败订单失败')
      }
    } catch (error) {
      if (error !== 'cancel') {
        ErrorHandler.handleApiError(error)
      }
    } finally {
      loading.value = false
    }
  }

  // 获取状态标签类型
  const getStatusType = (status: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
    const statusInfo = orderStatuses.value.find((s) => s.code === status)

    // 如果配置中找到了状态，使用配置的颜色
    if (statusInfo?.color) {
      return statusInfo.color as 'primary' | 'success' | 'warning' | 'info' | 'danger'
    }

    // 本地状态颜色映射作为备选
    const localStatusColors: Record<string, 'primary' | 'success' | 'warning' | 'info' | 'danger'> =
      {
        created: 'info',
        submitted: 'primary',
        assigned: 'info',
        awaiting_pickup: 'warning',
        picked_up: 'primary',
        in_transit: 'info',
        out_for_delivery: 'info',
        delivered: 'success',
        failed_delivery: 'danger',
        returned: 'warning',
        cancelling: 'warning', // 取消中 - 橙色
        cancelled: 'danger', // 英式拼写 - 红色
        canceled: 'danger', // 美式拼写 - 红色
        processing: 'primary',
        billed: 'success',
        voided: 'danger',
        exception: 'danger',
        failed: 'danger' // 下单失败 - 红色
      }

    return localStatusColors[status] || 'info'
  }

  // 获取供应商名称
  const getProviderName = (order: any) => {
    if (!order) return '-'

    // 如果有provider_name，直接返回
    if (order.provider_name) {
      return order.provider_name
    }

    // 供应商代码映射
    const providerMap: Record<string, string> = {
      kuaidiniao: '快递鸟',
      kuaidi100: '快递100',
      yida: '易达',
      yuntong: '云通',
      cainiao: '菜鸟',
      jd: '京东',
      dbl: '德邦'
    }

    return providerMap[order.provider] || order.provider || '-'
  }

  // 获取供应商标签类型
  const getProviderTagType = (provider: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
    const providerColors: Record<string, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
      kuaidiniao: 'primary',  // 快递鸟 - 蓝色
      kuaidi100: 'success',   // 快递100 - 绿色
      yida: 'warning',        // 易达 - 橙色
      yuntong: 'info',        // 云通 - 灰色
      cainiao: 'danger',      // 菜鸟 - 红色
      jd: 'primary',          // 京东 - 蓝色
      dbl: 'warning'          // 德邦 - 橙色
    }

    return providerColors[provider] || 'info'
  }

  // 获取快递公司名称
  const getExpressName = (order: any) => {
    if (!order) return '-'

    // 如果有express_name且不是英文代码，直接返回
    if (order.express_name && !isExpressCode(order.express_name)) {
      return order.express_name
    }

    // 快递公司代码映射（包含所有供应商的代码）
    const expressMap: Record<string, string> = {
      // === 快递100供应商代码 ===
      shentong: '申通快递',
      yuantong: '圆通快递',
      zhongtong: '中通快递',
      yunda: '韵达快递',
      jtexpress: '极兔快递',
      jd: '京东快递',
      // 德邦物流已移除支持
      ems: 'EMS',
      sf: '顺丰快递',

      // === 云通供应商代码 ===
      ST: '申通快递', // 云通申通代码
      YT: '圆通快递', // 云通圆通代码
      ZT: '中通快递', // 云通中通代码
      YD: '韵达快递', // 云通韵达代码
      JT: '极兔快递', // 云通极兔代码
      JD: '京东物流', // 云通京东代码

      // === 易达供应商代码 ===
      'STO-INT': '申通快递', // 易达申通代码
      YUND: '韵达快递', // 易达韵达代码

      // === 菜鸟供应商代码 ===
      YUNDA: '韵达快递', // 菜鸟韵达代码
      LE04284890: '京东物流', // 菜鸟京东代码
      DBKD: '德邦快递', // 菜鸟德邦代码
      HTKY: '极兔快递', // 菜鸟极兔代码（使用百世快递代码）
      NORMAL: '菜鸟裹裹', // 菜鸟标准快递代码

      // === 统一系统代码 ===
      SF: '顺丰速运',
      STO: '申通快递',
      CAINIAO: '菜鸟裹裹', // 🔧 修改：统一使用CAINIAO代码
      // 德邦物流已移除支持
      EMS: '中国邮政'
    }

    const expressCode = order.express_type || order.express_name
    return expressMap[expressCode] || expressCode || '-'
  }

  // 判断是否为快递公司代码
  const isExpressCode = (name: string) => {
    const codes = [
      // 快递100代码
      'shentong',
      'yuantong',
      'zhongtong',
      'yunda',
      'jtexpress',
      'jd',
      // 德邦物流已移除支持
      'ems',
      'sf',
      // 云通代码
      'ST',
      'YT',
      'ZT',
      'YD',
      'JT',
      'JD',
      // 易达代码
      'STO-INT',
      'YTO',
      'ZTO',
      'YUND',
      // 统一系统代码
      'SF',
      'STO',
      'CN', // 🔧 新增：中国标准快递代码
      // 德邦物流已移除支持
      'EMS',
      'HTKY',
      'NORMAL' // 🔧 新增：菜鸟标准快递代码
    ]
    return codes.includes(name) || codes.includes(name.toLowerCase())
  }

  // 获取状态描述
  const getStatusDesc = (status: string) => {
    const statusMap: Record<string, string> = {
      // === 下单阶段 ===
      submitted: '已提交',
      submit_failed: '提交失败',
      print_failed: '面单生成失败',

      // === 分配阶段 ===
      assigned: '已分配',
      awaiting_pickup: '等待揽收',

      // === 揽收阶段 ===
      picked_up: '已揽收',
      pickup_failed: '揽收失败',

      // === 运输阶段 ===
      in_transit: '运输中',
      out_for_delivery: '派送中',

      // === 签收阶段 ===
      delivered: '已签收',
      delivered_abnormal: '异常签收',

      // === 计费阶段 ===
      billed: '已计费',

      // === 异常状态 ===
      exception: '异常',
      returned: '已退回',
      forwarded: '已转寄',

      // === 取消状态 ===
      cancelling: '取消中',
      cancelled: '已取消',
      voided: '已作废',

      // === 失败状态 ===
      failed: '下单失败',

      // === 特殊状态 ===
      weight_updated: '重量更新',
      revived: '订单复活',

      // === 兼容状态 ===
      failed_delivery: '派送失败',
      canceled: '已取消', // 美式拼写
      processing: '处理中',
      created: '已创建'
    }
    return statusMap[status] || status
  }

  // 格式化价格
  const formatPrice = (price: number | string) => {
    const numPrice = typeof price === 'string' ? parseFloat(price) : price
    return numPrice.toFixed(2)
  }

  // 格式化重量
  const formatWeight = (weight: number | string | null | undefined) => {
    if (weight === null || weight === undefined) return '0.0'
    const numWeight = typeof weight === 'string' ? parseFloat(weight) : weight
    return isNaN(numWeight) ? '0.0' : numWeight.toFixed(1)
  }

  // 🔥 修复：智能体积格式化（自动识别单位）
  const formatVolume = (volume: number | string | null | undefined) => {
    if (volume === null || volume === undefined) return '0cm³'
    const numVolume = typeof volume === 'string' ? parseFloat(volume) : volume
    if (isNaN(numVolume) || numVolume === 0) return '0cm³'

    // 🔥 智能单位识别：
    // - 如果数值 < 1，认为是m³，需要转换为cm³
    // - 如果数值 >= 1000，认为是cm³，直接显示
    let volumeCm3: number
    if (numVolume < 1) {
      // 小于1的数值，认为是m³，转换为cm³
      volumeCm3 = numVolume * 1000000
    } else {
      // 大于等于1000的数值，认为已经是cm³
      volumeCm3 = numVolume
    }

    // 格式化显示：去掉不必要的小数位
    if (volumeCm3 >= 1000) {
      return `${Math.round(volumeCm3)}cm³`
    } else {
      return `${volumeCm3.toFixed(2)}cm³`
    }
  }

  // 🔥 修复：明确指定北京时区进行时间格式化
  const formatDate = (dateString: string) => {
    if (!dateString) return '-'
    try {
      return new Date(dateString).toLocaleString('zh-CN', {
        timeZone: 'Asia/Shanghai', // 明确指定北京时区
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    } catch {
      return dateString
    }
  }

  // 🔥 新增：检查是否有预约时间
  const hasPickupTime = (row: OrderListItem) => {
    return row.pickup_start_time && row.pickup_start_time.trim() !== '' &&
           row.pickup_end_time && row.pickup_end_time.trim() !== ''
  }

  // 🔥 新增：格式化预约时间
  const formatPickupTime = (row: OrderListItem) => {
    if (!hasPickupTime(row)) return '-'
    
    try {
      const startTime = new Date(row.pickup_start_time!)
      const endTime = new Date(row.pickup_end_time!)
      
      // 格式化为 MM-DD HH:mm - HH:mm
      const startFormatted = startTime.toLocaleString('zh-CN', {
        timeZone: 'Asia/Shanghai',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
      
      const endFormatted = endTime.toLocaleString('zh-CN', {
        timeZone: 'Asia/Shanghai',
        hour: '2-digit',
        minute: '2-digit'
      })
      
      // 如果是同一天，只显示日期一次
      const isSameDay = startTime.toDateString() === endTime.toDateString()
      if (isSameDay) {
        return `${startFormatted} - ${endFormatted}`
      } else {
        const endDateFormatted = endTime.toLocaleString('zh-CN', {
          timeZone: 'Asia/Shanghai',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        })
        return `${startFormatted} - ${endDateFormatted}`
      }
    } catch {
      return `${row.pickup_start_time} - ${row.pickup_end_time}`
    }
  }

  // 获取相对时间
  const getRelativeTime = (dateString: string) => {
    if (!dateString) return ''
    try {
      const date = new Date(dateString)
      const now = new Date()
      const diffMs = now.getTime() - date.getTime()
      const diffMinutes = Math.floor(diffMs / (1000 * 60))
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

      if (diffMinutes < 1) {
        return '刚刚'
      } else if (diffMinutes < 60) {
        return `${diffMinutes}分钟前`
      } else if (diffHours < 24) {
        return `${diffHours}小时前`
      } else if (diffDays < 7) {
        return `${diffDays}天前`
      } else {
        return formatDate(dateString).split(' ')[0] // 只显示日期
      }
    } catch {
      return ''
    }
  }

  // 判断订单是否可以取消
  const canCancelOrder = (order: OrderListItem) => {
    // 🔥 修复：失败订单不能取消（因为从未成功下单）
    if (order.status === 'failed') {
      return false
    }

    // 不能取消的状态：已实际取件之后的状态和最终状态
    const nonCancelableStatuses = [
      'picked_up', // 已揽收 - 实际取件后不能取消
      'in_transit', // 运输中
      'out_for_delivery', // 派送中
      'delivered', // 已签收
      'delivered_abnormal', // 异常签收
      'billed', // 已计费
      'cancelling', // 取消中
      'cancelled', // 已取消
      'canceled', // 已取消（兼容旧状态）
      'voided', // 已作废
      'returned', // 已退回
      'forwarded', // 已转寄
      'failed' // 🔥 新增：失败订单不能取消
    ]

    // 只要不在不可取消状态列表中，就可以取消
    return !nonCancelableStatuses.includes(order.status)
  }

  // 获取重量异常类型
  const getWeightAnomalyType = (order: OrderListItem) => {
    const orderWeight = order.weight || 0
    const actualWeight = order.actual_weight || 0
    const actualFee = order.actual_fee || 0
    const price = order.price || 0

    // 只有当实际重量和实际费用都有有效值时才进行判断
    // 避免刚下单时显示异常（此时actual_weight=0, actual_fee=0）
    if (actualWeight <= 0 || actualFee <= 0) {
      return null
    }

    // 超重：实际重量 > 下单重量 且 实收费用 > 预收费用
    if (actualWeight > orderWeight && actualFee > price) {
      return 'overweight'
    }

    // 超轻：实际重量 < 下单重量 且 实收费用 < 预收费用
    if (actualWeight < orderWeight && actualFee < price) {
      return 'underweight'
    }

    return null
  }

  // 获取重量异常标签类型
  const getWeightAnomalyTagType = (order: OrderListItem) => {
    const anomalyType = getWeightAnomalyType(order)
    if (anomalyType === 'overweight') {
      return 'danger' // 超重用红色
    }
    if (anomalyType === 'underweight') {
      return 'warning' // 超轻用橙色
    }
    return 'info'
  }

  // 获取重量异常文本
  const getWeightAnomalyText = (order: OrderListItem) => {
    const anomalyType = getWeightAnomalyType(order)
    if (anomalyType === 'overweight') {
      return '超重'
    }
    if (anomalyType === 'underweight') {
      return '超轻'
    }
    return ''
  }

  // 初始化
  onMounted(async () => {
    // 恢复筛选条件
    loadFiltersFromStorage()

    await loadSystemConfig()
    await getOrderList()
  })
</script>

<style scoped>
  .express-order-list {
    padding: 20px;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .search-area {
    margin-bottom: 20px;
    padding: 20px;
    background-color: #f5f7fa;
    border-radius: 4px;
  }

  .pagination-wrapper {
    margin-top: 20px;
    text-align: right;
  }

  /* 空状态样式 */
  .empty-state {
    padding: 60px 20px;
    text-align: center;
  }

  .empty-text {
    margin: 16px 0 8px;
    font-size: 16px;
    font-weight: 500;
    color: #909399;
  }

  .empty-description {
    margin: 0 0 24px;
    font-size: 14px;
    color: #c0c4cc;
    line-height: 1.5;
  }

  /* 重量体积信息样式 */
  .weight-volume-info {
    line-height: 1.4;
    font-size: 12px;
  }

  .info-row {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-bottom: 3px;
    gap: 6px;
  }

  .info-row:last-child {
    margin-bottom: 0;
  }

  .info-row .label {
    color: #909399;
    font-weight: 500;
    min-width: 30px;
    font-size: 11px;
    flex-shrink: 0;
  }

  .weight-value {
    color: #409eff;
    font-weight: 500;
    font-size: 12px;
    min-width: 45px;
    text-align: right;
  }

  .volume-value {
    color: #67c23a;
    font-weight: 500;
    font-size: 11px;
    min-width: 55px;
    text-align: right;
  }

  .fee-value {
    color: #e6a23c;
    font-weight: 600;
    font-size: 12px;
    min-width: 50px;
    text-align: right;
  }

  /* 重量异常标记样式 */
  .weight-anomaly-tag {
    margin-left: 6px;
    font-size: 10px;
    padding: 1px 4px;
    height: 16px;
    line-height: 14px;
    border-radius: 2px;
  }

  /* 揽件员信息样式 */
  .courier-info {
    line-height: 1.4;
  }

  .courier-name {
    font-weight: 500;
    color: #303133;
    font-size: 13px;
  }

  .courier-phone {
    color: #606266;
    font-size: 12px;
    margin-top: 2px;
  }

  .no-courier {
    color: #c0c4cc;
    font-style: italic;
  }

  /* 🔥 新增：操作按钮样式 */
  .action-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;

    .el-button {
      margin: 0;
      padding: 4px 8px;
      font-size: 12px;
      min-width: auto;
    }

    .el-button--small {
      height: 24px;
      line-height: 1;
    }
  }

  /* 时间信息样式 */
  .time-info {
    line-height: 1.4;
  }

  .full-time {
    font-size: 12px;
    color: #303133;
    font-weight: 500;
  }

  .relative-time {
    font-size: 11px;
    color: #909399;
    margin-top: 2px;
  }

  /* 🔥 新增：预约时间样式 */
  .pickup-time-info {
    line-height: 1.4;
  }

  .pickup-time-range {
    font-size: 12px;
    color: #67c23a;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .pickup-time-range .el-icon {
    font-size: 14px;
  }

  .no-pickup-time {
    color: #c0c4cc;
    font-size: 12px;
  }

  /* 🔥 新增：固定列样式优化 */
  :deep(.el-table__fixed-left) {
    box-shadow: 2px 0 6px rgba(0, 0, 0, 0.1);
  }

  :deep(.el-table__fixed-right) {
    box-shadow: -2px 0 6px rgba(0, 0, 0, 0.1);
  }

  /* 🔥 优化：表格整体样式 */
  :deep(.el-table) {
    font-size: 13px;
  }

  :deep(.el-table th) {
    background-color: #fafafa;
    font-weight: 600;
    color: #303133;
  }

  :deep(.el-table td) {
    padding: 8px 0;
  }

  /* 🔥 新增：搜索筛选区域样式优化 */
  .search-area {
    background: #f8f9fa;
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 16px;
    border: 1px solid #e4e7ed;
  }

  .search-form {
    margin: 0;
  }

  .search-form :deep(.el-form-item) {
    margin-bottom: 12px;
    margin-right: 16px;
  }

  .search-form :deep(.el-form-item__label) {
    font-weight: 500;
    color: #303133;
    font-size: 14px;
    min-width: 80px;
  }

  /* 统一输入框样式 */
  .search-input {
    width: 180px;
  }

  .search-input :deep(.el-input__wrapper) {
    border-radius: 6px;
  }

  /* 统一选择框样式 */
  .search-select {
    width: 160px;
  }

  .search-select :deep(.el-select__wrapper) {
    border-radius: 6px;
  }

  /* 搜索按钮样式 */
  .search-buttons {
    margin-left: 8px;
  }

  .search-btn {
    padding: 8px 20px;
    border-radius: 6px;
    font-weight: 500;
  }

  .reset-btn {
    padding: 8px 16px;
    border-radius: 6px;
    margin-left: 8px;
  }

  /* 🔥 新增：时间筛选区域样式 */
  .time-filter-section {
    background: #f0f9ff;
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 16px;
    border: 1px solid #bfdbfe;
  }

  .time-filter-form {
    margin: 0;
  }

  .time-filter-form :deep(.el-form-item) {
    margin-bottom: 0;
  }

  .time-filter-form :deep(.el-form-item__label) {
    font-weight: 500;
    color: #1e40af;
    font-size: 14px;
    min-width: 80px;
  }

  .time-filter-container {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 8px;
  }

  .quick-time-buttons {
    display: flex;
    gap: 6px;
    flex-wrap: wrap;
  }

  .quick-time-buttons .el-button {
    padding: 6px 12px;
    font-size: 12px;
    border-radius: 4px;
    min-height: 28px;
  }

  .quick-time-buttons .el-button--primary {
    background-color: #3b82f6;
    border-color: #3b82f6;
  }

  /* 🔥 优化：搜索按钮区域样式 */
  .search-buttons-section {
    display: flex;
    justify-content: center;
    gap: 16px;
    padding: 20px 0;
    border-top: 1px solid #e4e7ed;
    margin-top: 20px;
  }

  .unified-search-btn {
    padding: 12px 32px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 15px;
    min-width: 140px;
    height: 44px;
    background: linear-gradient(135deg, #409eff 0%, #3b82f6 100%);
    border: none;
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(64, 158, 255, 0.4);
      background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    }

    &:active {
      transform: translateY(0);
    }
  }

  .search-buttons-section .reset-btn {
    padding: 12px 24px;
    border-radius: 8px;
    min-width: 100px;
    height: 44px;
    font-weight: 500;
    border: 2px solid #e4e7ed;
    color: #606266;
    background-color: #ffffff;
    transition: all 0.3s ease;

    &:hover {
      border-color: #c0c4cc;
      color: #409eff;
      background-color: #f8f9fa;
    }
  }

  /* 🔥 新增：响应式设计 */
  @media (max-width: 1200px) {
    .search-input {
      width: 160px;
    }

    .search-select {
      width: 140px;
    }
  }

  @media (max-width: 768px) {
    .search-area {
      padding: 12px;
    }

    .search-form :deep(.el-form-item) {
      margin-right: 12px;
      margin-bottom: 8px;
    }

    .search-input {
      width: 140px;
    }

    .search-select {
      width: 120px;
    }

    .search-form :deep(.el-form-item__label) {
      min-width: 70px;
      font-size: 13px;
    }

    .search-btn,
    .reset-btn {
      padding: 6px 12px;
      font-size: 13px;
    }

    /* 时间筛选器移动端适配 */
    .time-filter-section {
      padding: 12px;
    }

    .time-filter-container {
      flex-direction: column;
      align-items: flex-start;
    }

    .time-filter-container .el-date-picker {
      width: 100% !important;
      margin-right: 0 !important;
      margin-bottom: 8px;
    }

    .quick-time-buttons {
      width: 100%;
      justify-content: flex-start;
    }

    .search-buttons-section {
      flex-direction: column;
      align-items: center;
      padding: 12px 0;
    }

    .search-buttons-section .el-button {
      width: 200px;
    }

    /* 空状态移动端适配 */
    .empty-state {
      padding: 60px 20px;
    }

    .empty-text h3 {
      font-size: 16px;
    }

    .empty-text p {
      font-size: 13px;
    }

    .empty-actions {
      flex-direction: column;
      align-items: center;
    }

    .empty-actions .el-button {
      width: 200px;
    }
  }

  /* 🔥 新增：取件码样式 */
  .pickup-code {
    margin-top: 4px;
  }

  .pickup-code .el-tag {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    font-size: 12px;
    letter-spacing: 1px;
  }

  /* 🔥 新增：空状态样式 */
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 80px 20px;
    text-align: center;
    background: #fafafa;
    border-radius: 8px;
    margin: 20px 0;
  }

  .empty-icon {
    margin-bottom: 20px;
  }

  .empty-text h3 {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 500;
    color: #303133;
  }

  .empty-text p {
    margin: 0 0 20px 0;
    font-size: 14px;
    color: #606266;
    line-height: 1.5;
  }

  .empty-actions {
    display: flex;
    gap: 12px;
  }

  .empty-actions .el-button {
    padding: 10px 20px;
    border-radius: 6px;
  }

  /* 响应式处理 */
  @media (max-width: 768px) {
    .action-buttons {
      flex-direction: column;
      gap: 2px;

      .el-button {
        width: 100%;
        justify-content: center;
      }
    }
  }



  /* 🔥 新增：批量查询统计样式 */
  .batch-stats-section {
    margin-bottom: 16px;

    .batch-stats-alert {
      border-radius: 8px;
    }

    .batch-stats-content {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
      margin-bottom: 12px;

      .stats-item {
        display: flex;
        align-items: center;
        gap: 4px;

        .stats-label {
          font-weight: 500;
          color: #606266;
        }

        .stats-value {
          font-weight: 600;

          &.success {
            color: #67c23a;
          }

          &.error {
            color: #f56c6c;
          }
        }
      }
    }

    .not-found-section {
      margin-top: 12px;
      padding-top: 12px;
      border-top: 1px solid #e4e7ed;

      .not-found-title {
        font-size: 13px;
        font-weight: 500;
        color: #606266;
        margin-bottom: 8px;
      }

      .not-found-list {
        display: flex;
        flex-wrap: wrap;
        gap: 6px;
        max-height: 200px;
        overflow-y: auto;

        .not-found-tag {
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-size: 12px;
        }
      }
    }
  }

  /* 🔥 新增：批量搜索按钮样式 */
  .batch-search-btn {
    background: linear-gradient(135deg, #67c23a, #85ce61);
    border: none;

    &:hover {
      background: linear-gradient(135deg, #85ce61, #67c23a);
    }
  }

  /* 🔥 新增：运单号输入框样式 */
  .tracking-no-clickable {
    cursor: pointer;

    :deep(.el-input__wrapper) {
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        border-color: #409eff;
        box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
      }
    }

    :deep(.el-input__inner) {
      cursor: pointer;
      color: #606266;

      &::placeholder {
        color: #409eff;
        font-weight: 500;
      }
    }

    .tracking-input-icon {
      color: #409eff;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        color: #3b82f6;
        transform: scale(1.1);
      }
    }
  }

  /* 🔥 新增：批量运单号查询对话框样式 */
  :deep(.batch-tracking-dialog) {
    .el-dialog__header {
      background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
      border-bottom: 1px solid #e3e6f0;
      border-radius: 8px 8px 0 0;
    }

    .el-dialog__title {
      font-weight: 600;
      color: #303133;
    }

    .el-dialog__body {
      padding: 24px;
    }
  }

  .batch-tracking-content {
    .batch-input-section {
      .batch-input-container {
        width: 100%;

        .batch-textarea {
          width: 100%;
          font-family: 'SF Mono', 'Monaco', 'Menlo', 'Consolas', 'Ubuntu Mono', monospace;
          font-size: 14px;
          line-height: 1.6;
          border-radius: 8px;
          border: 2px solid #e1e5e9;
          transition: all 0.3s ease;
          background-color: #ffffff;

          &:focus {
            border-color: #409eff;
            box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.1);
          }

          &:hover {
            border-color: #c0c4cc;
          }

          :deep(.el-textarea__inner) {
            resize: vertical;
            min-height: 120px;
          }
        }

        .batch-input-tips {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: 8px;
          font-size: 12px;
          color: #666;

          .tip-text {
            color: #909399;
          }

          .parsed-count {
            color: #67c23a;
            font-weight: 500;
            background: rgba(103, 194, 58, 0.1);
            padding: 2px 8px;
            border-radius: 12px;
          }
        }
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 16px 0 0 0;
    border-top: 1px solid #e4e7ed;
    margin-top: 16px;

    .el-button {
      padding: 8px 16px;
      border-radius: 6px;
      font-weight: 500;
    }
  }

  /* 🔥 新增：表格布局优化样式 */
  .table-container {
    width: 100%;
    overflow-x: auto;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .order-table {
      min-width: 1200px; /* 设置最小宽度确保表格完整显示 */

      /* 优化表头样式 */
      :deep(.el-table__header-wrapper) {
        .el-table__header {
          th {
            background-color: #f8f9fa;
            color: #333;
            font-weight: 600;
            border-bottom: 2px solid #e9ecef;
          }
        }
      }

      /* 优化表格行样式 */
      :deep(.el-table__body-wrapper) {
        .el-table__row {
          &:hover {
            background-color: #f5f7fa;
          }
        }
      }

      /* 订单信息列样式 */
      .order-info {
        .platform-order {
          font-weight: 600;
          color: #333;
          font-size: 13px;
          margin-bottom: 2px;
        }
        .customer-order {
          font-size: 12px;
          color: #666;
        }
      }

      /* 快递供应商信息样式 */
      .express-provider-info {
        .express-name {
          font-weight: 500;
          margin-bottom: 4px;
          font-size: 13px;
        }
        .provider-tag {
          font-size: 11px;
        }
      }

      /* 价格信息样式 */
      .price-info {
        .price-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 2px;
          font-size: 12px;

          .label {
            color: #666;
            font-size: 11px;
          }
          .price-value {
            font-weight: 600;
            color: #333;

            &.actual {
              color: #67c23a;
            }
          }
        }
      }

      /* 重量信息样式 */
      .weight-info {
        .weight-row {
          display: flex;
          align-items: center;
          gap: 4px;
          margin-bottom: 2px;
        }
        .weight-value {
          font-weight: 600;
          color: #333;
          font-size: 13px;
        }
        .actual-weight {
          font-size: 11px;
          color: #666;
        }
        .weight-anomaly-tag {
          font-size: 10px;
        }
      }

      /* 揽件员信息样式 */
      .courier-info {
        .courier-name {
          font-weight: 500;
          color: #333;
          margin-bottom: 2px;
          font-size: 13px;
        }
        .courier-phone {
          font-size: 12px;
          color: #666;
          margin-bottom: 2px;
        }
        .pickup-code {
          .el-tag {
            font-size: 10px;
          }
        }
      }

      /* 时间信息样式 */
      .time-info {
        .full-time {
          font-size: 12px;
          color: #333;
          margin-bottom: 2px;
        }
        .relative-time {
          font-size: 11px;
          color: #666;
        }
      }

      /* 预约时间样式 */
      .pickup-time-info {
        .pickup-time-range {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 12px;
          color: #333;
        }
      }

      /* 操作按钮样式 */
      .action-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;

        .el-button {
          padding: 4px 8px;
          font-size: 12px;
          border-radius: 4px;
        }
      }
    }
  }

  /* 响应式设计 */
  @media (max-width: 1400px) {
    .table-container {
      .order-table {
        min-width: 1000px;
      }
    }
  }

  @media (max-width: 1200px) {
    .table-container {
      .order-table {
        min-width: 900px;

        /* 在小屏幕上隐藏部分非关键列 */
        :deep(.el-table__column--hidden) {
          display: none;
        }
      }
    }
  }
</style>
