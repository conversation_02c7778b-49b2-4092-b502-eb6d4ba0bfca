# 订单列表供应商标识显示修改说明

## 📋 修改概述

为订单列表页面添加供应商标识显示功能，包括：
1. 在表格中新增供应商列
2. 在搜索筛选区域添加供应商筛选选项
3. 添加供应商名称映射和颜色标签

## 🔧 具体修改内容

### 1. 表格列添加

**位置**: 快递公司列和状态列之间
**文件**: `user-frontend/src/views/express/OrderList.vue` (第101-117行)

```vue
<el-table-column prop="provider" label="供应商" width="100">
  <template #default="{ row }">
    <el-tag :type="getProviderTagType(row.provider)" size="small">
      {{ getProviderName(row) }}
    </el-tag>
  </template>
</el-table-column>
```

### 2. 搜索筛选添加

**位置**: 快递公司筛选和重量异常筛选之间
**文件**: `user-frontend/src/views/express/OrderList.vue` (第73-85行)

```vue
<el-form-item label="供应商">
  <el-select
    v-model="searchForm.provider"
    placeholder="请选择供应商"
    clearable
    class="search-select"
  >
    <el-option label="快递鸟" value="kuaidiniao" />
    <el-option label="快递100" value="kuaidi100" />
    <el-option label="易达" value="yida" />
    <el-option label="云通" value="yuntong" />
    <el-option label="菜鸟" value="cainiao" />
    <el-option label="京东" value="jd" />
    <el-option label="德邦" value="dbl" />
  </el-select>
</el-form-item>
```

### 3. 方法函数添加

**位置**: `getExpressName` 方法之前
**文件**: `user-frontend/src/views/express/OrderList.vue` (第595-636行)

```typescript
// 获取供应商名称
const getProviderName = (order: any) => {
  if (!order) return '-'

  // 如果有provider_name，直接返回
  if (order.provider_name) {
    return order.provider_name
  }

  // 供应商代码映射
  const providerMap: Record<string, string> = {
    kuaidiniao: '快递鸟',
    kuaidi100: '快递100',
    yida: '易达',
    yuntong: '云通',
    cainiao: '菜鸟',
    jd: '京东',
    dbl: '德邦'
  }

  return providerMap[order.provider] || order.provider || '-'
}

// 获取供应商标签类型
const getProviderTagType = (provider: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const providerColors: Record<string, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    kuaidiniao: 'primary',  // 快递鸟 - 蓝色
    kuaidi100: 'success',   // 快递100 - 绿色
    yida: 'warning',        // 易达 - 橙色
    yuntong: 'info',        // 云通 - 灰色
    cainiao: 'danger',      // 菜鸟 - 红色
    jd: 'primary',          // 京东 - 蓝色
    dbl: 'warning'          // 德邦 - 橙色
  }

  return providerColors[provider] || 'info'
}
```

## 📊 API数据字段

根据后端API响应，订单列表项包含以下供应商相关字段：

```typescript
interface OrderListItem {
  // ... 其他字段
  provider: string        // 供应商代码 (如: kuaidiniao, kuaidi100, yida, etc.)
  provider_name: string   // 供应商名称 (如: 快递鸟, 快递100, 易达, etc.)
  // ... 其他字段
}
```

## 🎨 视觉效果

### 供应商标签颜色方案

| 供应商 | 代码 | 颜色 | Element Plus类型 |
|--------|------|------|------------------|
| 快递鸟 | kuaidiniao | 蓝色 | primary |
| 快递100 | kuaidi100 | 绿色 | success |
| 易达 | yida | 橙色 | warning |
| 云通 | yuntong | 灰色 | info |
| 菜鸟 | cainiao | 红色 | danger |
| 京东 | jd | 蓝色 | primary |
| 德邦 | dbl | 橙色 | warning |

### 表格布局

```
| 平台订单号 | 客户订单号 | 运单号 | 快递公司 | 供应商 | 状态 | ... |
|-----------|-----------|--------|----------|--------|------|-----|
| GK2025... | gk1753... | KDN... | 极兔快递 | 快递鸟 | 已提交 | ... |
```

## ✅ 功能特性

1. **智能显示**: 优先显示 `provider_name`，如果没有则使用代码映射
2. **颜色区分**: 不同供应商使用不同颜色的标签，便于快速识别
3. **筛选功能**: 支持按供应商筛选订单
4. **响应式设计**: 标签大小适中，不影响表格布局
5. **容错处理**: 未知供应商显示原始代码，使用默认颜色

## 🔄 兼容性

- ✅ 向后兼容：不影响现有功能
- ✅ 数据容错：处理缺失或未知供应商数据
- ✅ 响应式：适配不同屏幕尺寸
- ✅ 国际化：支持中文显示

## 🚀 使用方法

1. **查看供应商**: 在订单列表中直接查看每个订单的供应商标识
2. **筛选订单**: 使用搜索区域的供应商下拉框筛选特定供应商的订单
3. **颜色识别**: 通过不同颜色快速区分不同供应商的订单

---
*修改完成时间: 2025-07-24*  
*修改人员: Augment Agent*
