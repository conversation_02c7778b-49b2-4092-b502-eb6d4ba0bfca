import api from '@/utils/http'
import { useUserStore } from '@/store/modules/user'
import { SecureStorage } from '@/utils/security'
import type {
  BaseResponse,
  SimpleOrderRequest,
  SimpleOrderResponse,
  SimplePriceRequest,
  SimplePriceResponse,
  CancelOrderRequest,
  CancelOrderResponse
} from './model/kuaidiModel'

// 🚀 实时查价接口类型定义
export interface RealtimeAddressInfo {
  /** 姓名 (必填，最多50个字符) */
  name: string;
  /** 手机号 (必填，11位数字) */
  mobile: string;
  /** 省份 (必填，最多20个字符) */
  province: string;
  /** 城市 (必填，最多20个字符) */
  city: string;
  /** 区县 (必填，最多20个字符) */
  district: string;
  /** 详细地址 (必填，最多200个字符) */
  address: string;
}

export interface RealtimePriceRequest {
  /** 寄件人信息 (必填) */
  sender: RealtimeAddressInfo;
  /** 收件人信息 (必填) */
  receiver: RealtimeAddressInfo;
  /** 包裹重量(kg) (必填，0.1-100) */
  weight: number;
  /** 包裹长度(cm) (可选，0-200) */
  length?: number;
  /** 包裹宽度(cm) (可选，0-200) */
  width?: number;
  /** 包裹高度(cm) (可选，0-200) */
  height?: number;
  /** 包裹体积(m³) (可选，0-1.0) */
  volume?: number;
  /** 包裹数量 (可选，默认1，0-100) */
  quantity?: number;
  /** 物品名称 (可选，默认"物品"，最多100个字符) */
  goods_name?: string;
  /** 支付方式：0-寄付，1-到付，2-月结 (可选，默认0) */
  pay_method?: 0 | 1 | 2;
}

export interface RealtimePriceItem {
  /** 快递公司代码 */
  express_code: string;
  /** 快递公司名称 */
  express_name: string;
  /** 价格 */
  price: number;
  /** 续重价格(每公斤) */
  continued_weight_per_kg: number;
  /** 产品代码 */
  product_code: string;
  /** 产品名称 */
  product_name: string;
  /** 渠道ID */
  channel_id: string;
  /** 预计送达天数 */
  estimated_days: number;
  /** 供应商 */
  provider: string;
  /** 查询来源 */
  source: string;
  /** 响应时间(毫秒) */
  response_time: number;
  /** 🚀 订单代码（后端生成的正确代码，必填） */
  order_code: string;
  /** 计费重量 */
  calc_weight: number;
  /** 价格有效期 */
  expires_at: string;
  /** 🔥 新增：预约时间信息（菜鸟裹裹专用） */
  pickup_time_info?: import('@/api/model/kuaidiModel').PickupTimeInfo;
}

export interface RealtimePriceResponse {
  /** 是否成功 */
  success: boolean;
  /** 状态码 */
  code: number;
  /** 消息 */
  message: string;
  /** 价格列表 */
  data: RealtimePriceItem[];
  /** 查询时间 */
  query_time: string;
  /** 总响应时间(毫秒) */
  total_response_time: number;
}

/**
 * 统一网关请求接口
 */
interface UnifiedGatewayRequest {
  clientType: string         // 客户端类型：api | web
  username?: string         // 用户名或客户端ID（web客户端可选）
  timestamp?: string        // 时间戳（毫秒，web客户端可选）
  sign?: string            // 签名（web客户端可选）
  accessToken?: string     // JWT Token（web客户端必需）
  apiMethod: string        // API方法：QUERY_PRICE | CREATE_ORDER
  businessParams: any     // 业务参数
}

/**
 * 统一网关响应接口
 */
interface UnifiedGatewayResponse<T = any> {
  code: number
  msg: string
  data: T
  success: boolean
}

/**
 * 统一网关API服务
 * 使用统一网关接口进行查价和下单操作
 */
export class UnifiedGatewayService {
  /**
   * 获取当前用户的访问令牌
   * @returns JWT访问令牌
   */
  private static getAccessToken(): string {
    // 优先从安全存储获取token
    const storedToken = SecureStorage.getToken()
    if (storedToken) {
      return storedToken.startsWith('Bearer ') ? storedToken.substring(7) : storedToken
    }

    // 从store获取token
    const { token } = useUserStore().info
    if (token) {
      return token.startsWith('Bearer ') ? token.substring(7) : token
    }

    throw new Error('未找到访问令牌，请重新登录')
  }

  /**
   * 统一网关查价
   * @param params 查价参数
   * @returns 价格列表
   */
  static async queryPrice(params: SimplePriceRequest): Promise<BaseResponse<SimplePriceResponse>> {
    // 获取访问令牌
    const accessToken = this.getAccessToken()

    // 构建统一网关请求
    const gatewayRequest: UnifiedGatewayRequest = {
      clientType: 'web',
      accessToken: accessToken,
      apiMethod: 'QUERY_PRICE',
      businessParams: params
    }

    const response = await api.post<UnifiedGatewayResponse<SimplePriceResponse>>({
      url: '/api/gateway/execute',
      params: gatewayRequest
    })

    // 转换为统一的BaseResponse格式
    return {
      success: response.success,
      code: response.code,
      message: response.msg,
      data: response.data
    }
  }

  /**
   * 统一网关下单
   * @param params 下单参数
   * @returns 下单响应
   */
  static async createOrder(params: SimpleOrderRequest): Promise<BaseResponse<SimpleOrderResponse>> {
    // 获取访问令牌
    const accessToken = this.getAccessToken()

    // 构建统一网关请求
    const gatewayRequest: UnifiedGatewayRequest = {
      clientType: 'web',
      accessToken: accessToken,
      apiMethod: 'CREATE_ORDER',
      businessParams: params
    }

    const response = await api.post<UnifiedGatewayResponse<SimpleOrderResponse>>({
      url: '/api/gateway/execute',
      params: gatewayRequest
    })

    // 转换为统一的BaseResponse格式
    return {
      success: response.success,
      code: response.code,
      message: response.msg,
      data: response.data
    }
  }

  /**
   * 统一网关取消订单
   * @param params 取消订单参数
   * @returns 取消订单响应
   */
  static async cancelOrder(params: CancelOrderRequest): Promise<BaseResponse<CancelOrderResponse>> {
    // 获取访问令牌
    const accessToken = this.getAccessToken()

    // 构建统一网关请求
    const gatewayRequest: UnifiedGatewayRequest = {
      clientType: 'web',
      accessToken: accessToken,
      apiMethod: 'CANCEL_ORDER',
      businessParams: params
    }

    const response = await api.post<UnifiedGatewayResponse<CancelOrderResponse>>({
      url: '/api/gateway/execute',
      params: gatewayRequest
    })

    // 转换为统一的BaseResponse格式
    return {
      success: response.success,
      code: response.code,
      message: response.msg,
      data: response.data
    }
  }

  /**
   * 🚀 实时查价接口
   * @param params 实时查价参数
   * @returns 实时价格列表
   */
  static async queryRealtimePrice(params: RealtimePriceRequest): Promise<BaseResponse<RealtimePriceResponse>> {
    // 获取访问令牌
    const accessToken = this.getAccessToken()

    // 构建统一网关请求
    const gatewayRequest: UnifiedGatewayRequest = {
      clientType: 'web',
      accessToken: accessToken,
      apiMethod: 'QUERY_REALTIME_PRICE',
      businessParams: params
    }

    const response = await api.post<UnifiedGatewayResponse<RealtimePriceResponse>>({
      url: '/api/gateway/execute',
      params: gatewayRequest
    })

    // 转换为统一的BaseResponse格式
    return {
      success: response.success,
      code: response.code,
      message: response.msg,
      data: response.data
    }
  }
}