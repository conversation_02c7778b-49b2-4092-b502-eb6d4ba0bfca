// 快递API相关数据模型

// ============ 通用响应模型 ============
export interface BaseResponse<T = any> {
  success: boolean
  code: number
  message: string
  data?: T
}

// ============ 分页模型 ============
export interface PaginationRequest {
  page?: number
  limit?: number
}

export interface PaginationResponse {
  total: number
  page: number
  limit: number
}

// ============ OAuth认证模型 ============
export interface TokenRequest {
  grant_type: 'client_credentials'
  client_id: string
  client_secret: string
  scope?: string
}

export interface TokenResponse {
  access_token: string
  token_type: 'bearer'
  expires_in: number
  scope: string
}

export interface RevokeTokenRequest {
  token: string
  token_type_hint?: 'access_token' | 'refresh_token'
  client_id: string
  client_secret: string
}

// ============ 用户管理模型 ============

export interface UserProfile {
  id: string
  username: string
  email: string
  client_id: string
  callback_url: string
  created_at: string
}

export interface UpdateCallbackUrlRequest {
  callback_url: string
}

// ============ 用户注册模型 ============
export interface UserRegisterRequest {
  username: string
  email: string
  password: string
  client_id?: string // 可选，允许前端自定义客户端ID
}

export interface UserRegisterResponse {
  user_id: string
  client_id: string
  created_at: string
}

// ============ 快递订单模型 ============
export interface SimpleOrderRequest {
  user_id?: string
  customer_order_no?: string // 🔥 新增：客户自定义订单号
  order_code: string
  sender_name: string
  sender_mobile: string // 🔥 修改：统一联系方式字段，支持手机号和固定电话
  sender_province: string
  sender_city: string
  sender_district: string
  sender_address: string
  receiver_name: string
  receiver_mobile: string // 🔥 修改：统一联系方式字段，支持手机号和固定电话
  receiver_province: string
  receiver_city: string
  receiver_district: string
  receiver_address: string
  weight: number
  volume?: number
  length?: number // 长度(cm)
  width?: number // 宽度(cm)
  height?: number // 高度(cm)
  quantity?: number
  goods_name?: string
  pay_method?: 0 | 1 | 2 // 0-寄付，1-到付，2-月结
  remark?: string
  insure_value?: number
  expected_price?: number

  // 🚀 新增：预约时间相关字段
  pickup_start_time?: string // 预约开始时间（ISO 8601格式）
  pickup_end_time?: string   // 预约结束时间（ISO 8601格式）
}

export interface SimpleOrderResponse {
  customer_order_no: string // 🔥 新增：客户订单号
  order_no: string
  tracking_no: string
  express_code: string
  express_name: string
  price: number
  delivery_time: string
}

export interface CancelOrderRequest {
  order_no?: string          // 🔥 智能订单号：支持平台订单号、客户订单号、供应商订单号 (可选)
  tracking_no?: string       // 运单号 (可选)
  platform_order_no?: string // 🔥 新增：明确指定平台订单号 (可选)
  reason: string             // 取消原因 (必填)
}

export interface CancelOrderResponse {
  order_no: string
  is_cancelled: boolean
}

export interface QueryOrderRequest {
  order_no?: string
  tracking_no?: string
  provider?: string
}

export interface SenderInfo {
  name: string
  mobile: string
  tel?: string // 🔥 新增：固定电话
  province: string
  city: string
  district: string
  address: string
}

export interface ReceiverInfo {
  name: string
  mobile: string
  tel?: string // 🔥 新增：固定电话
  province: string
  city: string
  district: string
  address: string
}

export interface TrackInfo {
  time: string
  context: string // 后端返回的字段名
  description?: string // 兼容旧版本，可选
  location: string
  status?: string // 后端返回的状态
  status_code?: string // 后端返回的状态码
  area_code?: string // 后端返回的区域编码
  area_name?: string // 后端返回的区域名称
}

export interface QueryOrderResponse {
  customer_order_no: string // 🔥 新增：客户订单号
  order_no: string
  tracking_no: string
  express_type: string
  status: string
  status_desc: string
  weight: number
  price: number
  actual_fee: number
  insurance_fee: number
  overweight_fee: number
  underweight_fee: number
  weight_adjustment_reason: string
  billing_status: string
  order_volume: number
  actual_weight: number
  actual_volume: number
  charged_weight: number
  sender_info: SenderInfo
  receiver_info: ReceiverInfo
  created_at: string
  updated_at: string
  tracks: TrackInfo[]
  
  // 🔥 新增：失败订单相关字段
  failure_reason?: string // 失败原因类型
  failure_message?: string // 失败详细信息
  failure_stage?: string // 失败阶段
  failure_source?: string // 失败来源：provider-供应商, system-系统
  failure_time?: string // 失败时间
  can_retry?: boolean // 是否可重试
}

export interface QueryTrackRequest {
  tracking_no: string
  express_type?: string
  customer_name?: string
}

export interface QueryTrackResponse {
  tracking_no: string
  express_type: string
  status: string
  status_desc: string
  tracks: TrackInfo[]
}

// ============ 订单列表模型 ============
export interface OrderListRequest {
  page?: number
  page_size?: number
  status?: string
  express_type?: string
  provider?: string
  customer_order_no?: string
  order_no?: string
  tracking_no?: string

  // 🔥 新增：批量运单号查询支持
  tracking_nos?: string[]  // 批量运单号列表
  batch_mode?: boolean     // 批量查询模式标识

  weight_anomaly?: 'overweight' | 'underweight'
  start_time?: string
  end_time?: string
  sort_by?: 'created_at' | 'updated_at' | 'price'
  sort_order?: 'asc' | 'desc'
}

export interface OrderListItem {
  id: number
  customer_order_no: string
  order_no: string
  tracking_no: string
  express_type: string
  express_name: string
  provider: string
  provider_name: string
  status: string
  status_desc: string
  weight: number
  price: number
  actual_fee: number
  insurance_fee: number
  overweight_fee: number
  underweight_fee: number
  weight_adjustment_reason: string
  billing_status: string
  sender_info: string
  receiver_info: string

  // 重量体积信息
  order_volume: number
  actual_weight: number
  actual_volume: number
  charged_weight: number

  // 揽件员信息
  courier_name?: string
  courier_phone?: string
  courier_code?: string  // 🔥 新增：快递员工号
  station_name?: string  // 🔥 新增：网点名称
  pickup_code?: string   // 🔥 新增：取件码

  // 时间信息
  pickup_start_time?: string // 预约取件开始时间
  pickup_end_time?: string   // 预约取件结束时间
  created_at: string
  updated_at: string

  // 失败订单相关字段
  failure_reason?: string
  failure_message?: string
  failure_stage?: string
  failure_source?: string
  failure_time?: string
  can_retry?: boolean
}

// 🔥 新增：批量查询统计信息
export interface BatchQueryStats {
  total_queried: number    // 查询的运单号总数
  found_count: number      // 找到的订单数量
  not_found: string[]      // 未找到的运单号列表
  query_time: string       // 查询耗时（毫秒）
}

export interface OrderListResponse {
  items: OrderListItem[]
  total: number
  page: number
  page_size: number
  total_pages: number
  has_next: boolean
  has_prev: boolean

  // 🔥 新增：批量查询统计信息
  batch_stats?: BatchQueryStats  // 批量查询统计，仅在批量查询时返回
}

// ============ 订单统计模型 ============
export interface StatusSummary {
  status: string
  count: number
  percentage: number
}

export interface DailySummary {
  date: string
  order_count: number
  total_amount: number
}

export interface OrderStatisticsResponse {
  status_counts?: Record<string, number>
  total_orders: number
  total_amount: number
  status_summary: StatusSummary[]
  daily_summary: DailySummary[]
}

// ============ 价格查询模型 ============
export interface SimplePriceRequest {
  from_province: string
  from_city: string
  from_district?: string
  to_province: string
  to_city: string
  to_district?: string
  weight: number
  volume?: number
  length?: number // 🔥 新增：长度(cm)
  width?: number // 🔥 新增：宽度(cm)
  height?: number // 🔥 新增：高度(cm)
  quantity?: number
  goods_name?: string
  pay_method?: number
  express_code?: string // 🚀 新增：快递公司代码 (可选，不填则查询所有快递公司)
}

export interface PriceItem {
  express_code: string
  express_name: string
  product_code: string
  product_name: string
  price: number
  continued_weight_per_kg: number
  calc_weight: number
  order_code: string
  expires_at: string
  // 🔥 新增：预约时间信息（菜鸟裹裹专用）
  pickup_time_info?: PickupTimeInfo
}

// 🔥 新增：预约时间信息接口
export interface PickupTimeInfo {
  pickup_required: boolean // 是否需要预约取件
  supports_pickup_code: boolean // 是否支持取件码
  min_advance_hours: number // 最少提前预约小时数
  time_format: string // 时间格式
  available_slots: PickupTimeSlot[] // 可用时间段
}

// 🔥 新增：预约时间段接口
export interface PickupTimeSlot {
  slot_id: string // 时间段ID
  slot_name: string // 时间段名称（如：明天 09:00-23:59）
  start_time: string // 开始时间 (YYYY-MM-DD HH:mm:ss)
  end_time: string // 结束时间 (YYYY-MM-DD HH:mm:ss)
  available?: boolean // 是否可用（可选，默认true）
  description?: string // 描述信息
}

export type SimplePriceResponse = PriceItem[]

// ============ 余额管理模型 ============
export interface BalanceResponse {
  success?: boolean
  data?: any
  user_id: string
  balance: string
  available_balance: string
  currency: string
  status: string
  updated_at: string
}

export interface DepositRequest {
  amount: string
  payment_method: string
  transaction_id: string
}

export interface PaymentRequest {
  amount: string
  order_no: string
  description: string
}

export interface RefundRequest {
  amount: string
  original_transaction_id?: string
  order_no?: string
  reason: string
}

export interface TransactionResponse {
  id: string
  user_id: string
  type: string // 🔥 修正：后端返回的字段名是 type，不是 transaction_type
  amount: string
  currency: string
  balance_before: string
  balance_after: string

  // 订单关联信息（新增）
  order_no: string
  platform_order_no: string // 平台订单号
  customer_order_no: string
  tracking_no: string

  // 交易分类信息（新增）
  transaction_category: string
  transaction_sub_type: string

  // 描述信息（增强）
  reference_id: string
  description: string
  detail_description: string
  user_friendly_desc: string

  // 业务上下文（新增）
  metadata?: Record<string, any>
  business_context?: Record<string, any>
  related_transaction_id?: string

  // 操作信息
  operator_id: string
  status: string // 🔥 修正：后端返回小写状态值
  created_at: string
}

export interface TransactionHistoryRequest {
  limit?: number
  offset?: number
  type?: string // 🔥 更新：支持新的细分交易类型
  status?: string // 🔥 更新：支持小写状态值
  start_time?: string
  end_time?: string
  customer_order_no?: string // 🔥 新增：客户订单号筛选
  order_no?: string // 🔥 新增：平台订单号筛选
  tracking_no?: string // 🔥 新增：运单号筛选
}

export interface TransactionHistoryItem {
  id: string
  user_id: string
  type: string // 🔥 修正：后端返回的字段名是 type
  amount: string
  currency: string
  balance_before: string
  balance_after: string

  // 订单关联信息（新增）
  order_no: string
  platform_order_no: string // 平台订单号
  customer_order_no: string
  tracking_no: string

  // 交易分类信息（新增）
  transaction_category: string
  transaction_sub_type: string

  // 描述信息（增强）
  reference_id: string
  description: string
  detail_description: string
  user_friendly_desc: string

  // 业务上下文（新增）
  metadata?: Record<string, any>
  business_context?: Record<string, any>
  related_transaction_id?: string

  // 操作信息
  operator_id: string
  status: string // 🔥 更新：支持小写状态值
  created_at: string
}

export interface TransactionHistoryResponse {
  success?: boolean
  data?: any
  items: TransactionHistoryItem[]
  total: number
  page: number
  page_size: number
}

// ============ 缓存管理模型 ============
export interface WarmupRequest {
  strategy: 'full' | 'priority' | 'smart'
}

export interface WarmupStatusResponse {
  is_warming: boolean
  status: 'IDLE' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED' | 'STOPPED'
  start_time: string | null
  end_time: string | null
  duration_ms: number
  total_routes: number
  completed_routes: number
  success_routes: number
  failed_routes: number
  progress_percent: number
  success_rate: number
  error_message: string | null
}

export interface WarmupHistoryItem {
  id: string
  strategy: string
  status: string
  start_time: string
  end_time: string
  duration_ms: number
  total_routes: number
  completed_routes: number
  success_routes: number
  failed_routes: number
  error_message?: string
}

export interface WarmupHistoryResponse {
  history: WarmupHistoryItem[]
  total: number
}

export interface WarmupConfigResponse {
  default_strategy: string
  allowed_strategies: string[]
  concurrent_requests: number
  timeout_per_request_ms: number
  priority_regions: string[]
  smart_threshold_hours: number
}

export interface QuickWarmupRequest {
  quick_type: 'default_priority' | 'hot_routes' | 'specific_regions'
  regions?: string[]
}

// ============ 健康检查模型 ============
export interface HealthCheckResponse {
  status: 'UP' | 'DOWN'
  timestamp: string
  services: {
    database: 'UP' | 'DOWN'
    redis: 'UP' | 'DOWN'
  }
}

// ============ 回调管理模型 ============
export interface CallbackRecord {
  id: string
  event_type: string
  order_no: string
  customer_order_no: string
  tracking_no: string
  callback_url: string
  request_data: any
  response_data?: any
  http_status: number
  status: 'success' | 'failed' | 'pending'
  retry_count: number
  error_message: string
  request_at?: string
  response_at?: string
  created_at: string
}

// 管理员视图的回调记录（包含供应商信息）
export interface AdminCallbackRecord {
  id: string
  provider: string
  callback_type: string
  order_no: string
  customer_order_no: string
  tracking_no: string
  user_id: string
  raw_data: any
  standardized_data: any
  event_type: string
  internal_status: 'success' | 'failed' | 'pending'
  external_status: 'success' | 'failed' | 'pending'
  internal_error?: string
  external_error?: string
  retry_count: number
  received_at: string
  internal_processed_at?: string
  external_processed_at?: string
  created_at: string
  updated_at: string
}

export interface CallbackConfig {
  callback_url: string
  callback_secret?: string
  enabled: boolean
  retry_count: number
  timeout_seconds: number
  subscribed_events: string[]
  created_at: string
  updated_at: string
}

// API密钥信息
export interface ApiKeyInfo {
  client_id: string
  client_secret?: string // 只在重置时返回
  created_at: string
  last_used_at?: string
}

// 重置客户端密钥响应
export interface ResetClientSecretResponse {
  client_secret: string
  new_client_secret?: string // 兼容不同的响应格式
}

export interface CallbackStatistics {
  total_callbacks: number
  success_rate: number
  provider_stats: Record<string, { total: number; success: number }>
  event_type_stats: Record<string, number>
  last_24h_stats: {
    total: number
    success: number
    failed: number
  }
}

export interface CallbackListRequest {
  page?: number
  page_size?: number
  event_type?: string
  status?: string
  tracking_no?: string
  start_time?: string
  end_time?: string
}

export interface CallbackListResponse {
  records: CallbackRecord[]
  total: number
  page: number
  page_size: number
}

export interface UpdateCallbackConfigRequest {
  callback_url: string
  callback_secret?: string
  enabled: boolean
  retry_count?: number
  timeout_seconds?: number
  subscribed_events?: string[]
}

// ============ 订单状态历史模型 ============
export interface StatusHistoryItem {
  id: number
  order_no: string
  customer_order_no: string
  from_status: string
  from_status_desc: string
  to_status: string
  to_status_desc: string
  provider: string
  provider_name: string
  change_source: string
  change_source_desc: string
  operator_name?: string
  change_reason?: string
  created_at: string
  duration: string
}

export interface GetStatusHistoryRequest {
  order_no?: string
  customer_order_no?: string
  page?: number
  limit?: number
}

export interface GetStatusHistoryResponse {
  items: StatusHistoryItem[]
  total: number
  page: number
  limit: number
}
