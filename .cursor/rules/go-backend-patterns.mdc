---
globs: *.go
---

# Go Backend Patterns & Architecture

## Code Organization
- **Clean Architecture**: Follows hexagonal/clean architecture principles
- **Layer Separation**: Clear separation between handlers, services, repositories, and models
- **Dependency Injection**: Services are injected through constructors and interfaces

## Key Patterns

### Handler Pattern
Handlers in [api/handler/](mdc:api/handler/) follow this structure:
- Input validation and parameter binding
- Service layer calls for business logic
- Response formatting and error handling
- Middleware integration for auth, logging, etc.

### Service Pattern
Services in [internal/service/](mdc:internal/service/) implement:
- Business logic and rules
- Transaction management
- Cross-cutting concerns (logging, metrics)
- Repository orchestration

### Repository Pattern
Repositories in [internal/repository/](mdc:internal/repository/) handle:
- Database operations
- Data access abstraction
- Query optimization
- Connection management

### Adapter Pattern
Provider adapters in [internal/adapter/](mdc:internal/adapter/) implement:
- External API integration
- Protocol translation
- Error handling and retry logic
- Response standardization

## Error Handling
- Custom error types in [internal/errors/](mdc:internal/errors/)
- Structured error responses
- Comprehensive logging with context
- Graceful degradation for external service failures

## Configuration Management
- YAML-based configuration in [config/](mdc:config/)
- Environment-specific overrides
- Runtime configuration reloading
- Validation and defaults

## Testing Patterns
- Unit tests for individual components
- Integration tests for service interactions
- Mock interfaces for external dependencies
- Test data fixtures and helpers

## Security Considerations
- Input validation at all entry points
- SQL injection prevention
- Authentication and authorization middleware
- Rate limiting and throttling
- Audit logging for sensitive operations
