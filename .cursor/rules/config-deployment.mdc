---
globs: *.yaml,*.json,*.sh,*.conf,Dockerfile
---

# Configuration & Deployment Patterns

## Configuration Management

### Main Configuration Files
- **System Config**: [config/config.yaml](mdc:config/config.yaml) - Main system configuration
- **Enhanced Balance**: [config/enhanced_balance.yaml](mdc:enhanced_balance.yaml) - Balance management settings
- **Callback Service Config**: [callback-receiver-service/config.json](mdc:callback-receiver-service/config.json) - Callback service configuration

### Configuration Structure
```yaml
# Example configuration pattern
server:
  port: 8080
  host: "0.0.0.0"
  
database:
  host: "localhost"
  port: 5432
  name: "kuaidi_db"
  
providers:
  jd:
    enabled: true
    api_key: "${JD_API_KEY}"
    base_url: "https://api.jd.com"
  
  cainiao:
    enabled: true
    app_key: "${CAINIAO_APP_KEY}"
    secret: "${CAINIAO_SECRET}"
```

### Environment Variables
- **Secrets Management**: Sensitive data via environment variables
- **Environment-specific**: Different configs for dev/staging/prod
- **Docker Integration**: Environment variables in containers
- **Default Values**: Fallback values for optional settings

## Deployment Architecture

### Build Scripts
- **Production Build**: [callback-receiver-service/build-production.sh](mdc:callback-receiver-service/build-production.sh)
- **Deployment Scripts**: Various deployment scripts in [scripts/](mdc:scripts/)
- **Migration Scripts**: Database migration scripts

### Container Configuration
- **Docker Setup**: Container configuration for services
- **Nginx Config**: [docker/nginx/](mdc:docker/nginx/) - Reverse proxy configuration
- **Multi-stage Builds**: Optimized Docker builds

### Service Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   User Frontend │    │  Admin Frontend │    │  External APIs  │
│   (Vue.js/TS)   │    │   (Vue.js/TS)   │    │   (Providers)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │  Nginx Gateway  │
                    │   (Load Balancer)│
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │  Main Go Server │
                    │   (API Layer)   │
                    └─────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Callback Service│    │    Database     │    │  External APIs  │
│   (Go Service)  │    │   (PostgreSQL)  │    │   (Providers)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Database Management

### Migration System
- **Database Migrations**: [migrations/](mdc:migrations/) - Database schema changes
- **SQL Scripts**: [sql/](mdc:sql/) - Database initialization scripts
- **Migration Tools**: [cmd/migrate/](mdc:cmd/migrate/) - Migration management tools

### Backup & Recovery
- **Backup Scripts**: Automated database backups
- **Recovery Procedures**: Database recovery processes
- **Data Integrity**: Validation and consistency checks

## Monitoring & Logging

### Logging Configuration
- **Structured Logging**: JSON-formatted logs
- **Log Levels**: Configurable log levels per environment
- **Log Rotation**: Automated log file management
- **Centralized Logging**: Log aggregation setup

### Health Checks
- **Service Health**: Health check endpoints
- **Database Health**: Database connection monitoring
- **Provider Health**: External provider status checks
- **Metrics Collection**: Performance metrics gathering

## Security Configuration

### SSL/TLS Configuration
- **Certificate Management**: SSL certificate handling
- **HTTPS Enforcement**: Force HTTPS in production
- **Security Headers**: Proper security headers configuration

### Access Control
- **Authentication**: JWT token configuration
- **Authorization**: Role-based access control
- **Rate Limiting**: API rate limiting configuration
- **CORS Settings**: Cross-origin resource sharing setup

## Development Environment

### Local Development
- **Development Config**: Local development settings
- **Test Configuration**: Test environment setup
- **Mock Services**: Mock external services for testing
- **Development Tools**: Local development utilities

### CI/CD Pipeline
- **Build Pipeline**: Automated build processes
- **Testing Pipeline**: Automated testing execution
- **Deployment Pipeline**: Automated deployment processes
- **Quality Gates**: Code quality checks

## Performance Optimization

### Caching Configuration
- **Redis Configuration**: Caching layer setup
- **Cache Policies**: Cache expiration and invalidation
- **Cache Warming**: Preload frequently accessed data

### Load Balancing
- **Nginx Configuration**: Load balancer setup
- **Health Checks**: Service health monitoring
- **Failover**: Automatic failover configuration
- **Scaling**: Auto-scaling configuration

## Troubleshooting

### Common Issues
- **Configuration Validation**: Validate configuration files
- **Environment Setup**: Proper environment variable setup
- **Service Dependencies**: Ensure all services are running
- **Network Connectivity**: Check network configurations

### Debugging Tools
- **Log Analysis**: Log analysis and debugging
- **Performance Monitoring**: Performance issue identification
- **Error Tracking**: Error monitoring and alerting
- **Service Tracing**: Request tracing across services
