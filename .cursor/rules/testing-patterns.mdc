---
globs: *_test.go,*.test.ts,*.spec.ts,*.test.js,*.spec.js
---

# Testing Patterns & Practices

## Go Testing Architecture

### Test Organization
- **Unit Tests**: Individual component testing with `*_test.go` files
- **Integration Tests**: Service interaction testing in [test/integration/](mdc:test/integration/)
- **Error Handling Tests**: Comprehensive error scenario testing in [test/error_handling/](mdc:test/error_handling/)

### Testing Patterns
```go
// Example test structure
func TestOrderService_CreateOrder(t *testing.T) {
    // Setup
    mockRepo := &MockOrderRepository{}
    service := NewOrderService(mockRepo)
    
    // Test cases
    tests := []struct {
        name    string
        input   OrderRequest
        want    OrderResponse
        wantErr bool
    }{
        {
            name: "valid_order",
            input: OrderRequest{/* ... */},
            want: OrderResponse{/* ... */},
            wantErr: false,
        },
        // More test cases...
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            // Execute and Assert
        })
    }
}
```

### Mock Interfaces
- **Repository Mocks**: Mock database interactions
- **External Service Mocks**: Mock provider API calls
- **HTTP Client Mocks**: Mock HTTP requests/responses
- **Interface-based Testing**: Use interfaces for testability

## Frontend Testing

### Vue.js Testing
- **Component Tests**: Test individual Vue components
- **Unit Tests**: Test utility functions and composables
- **Integration Tests**: Test component interactions
- **E2E Tests**: Full user journey testing

### Testing Tools
- **Vitest**: Modern testing framework for Vue.js
- **Vue Test Utils**: Vue.js component testing utilities
- **Cypress**: End-to-end testing framework
- **Testing Library**: DOM testing utilities

## Provider Integration Testing

### Adapter Testing
Each provider adapter should have comprehensive tests:
- **API Integration**: Test actual API calls (with mocking)
- **Response Parsing**: Test response transformation
- **Error Handling**: Test error scenarios and recovery
- **Rate Limiting**: Test rate limiting behavior

### Callback Testing
- **Callback Processing**: Test callback standardization
- **Validation**: Test callback validation logic
- **Status Mapping**: Test status code mapping
- **Authentication**: Test callback authentication

## Database Testing

### Repository Testing
- **CRUD Operations**: Test all database operations
- **Query Performance**: Test query optimization
- **Transaction Handling**: Test transaction boundaries
- **Data Integrity**: Test constraint validation

### Migration Testing
- **Schema Changes**: Test migration scripts
- **Data Migration**: Test data transformation
- **Rollback**: Test migration rollback procedures
- **Performance**: Test migration performance

## API Testing

### Handler Testing
- **Request Validation**: Test input validation
- **Response Formatting**: Test response structure
- **Error Handling**: Test error responses
- **Authentication**: Test auth middleware

### Integration Testing
- **Full Request Flow**: Test complete request lifecycle
- **Service Integration**: Test service layer interactions
- **Database Integration**: Test database operations
- **External API Integration**: Test provider API calls

## Performance Testing

### Load Testing
- **Concurrent Requests**: Test concurrent user scenarios
- **Provider Failover**: Test provider switching under load
- **Database Performance**: Test database under load
- **Memory Usage**: Test memory consumption

### Stress Testing
- **Breaking Point**: Find system limits
- **Recovery**: Test system recovery
- **Resource Exhaustion**: Test resource limits
- **Graceful Degradation**: Test system behavior under stress

## Security Testing

### Authentication Testing
- **JWT Validation**: Test token validation
- **Authorization**: Test role-based access
- **Session Management**: Test session handling
- **Rate Limiting**: Test API rate limits

### Input Validation Testing
- **SQL Injection**: Test SQL injection prevention
- **XSS Protection**: Test cross-site scripting prevention
- **Input Sanitization**: Test input cleaning
- **Parameter Validation**: Test parameter constraints

## Test Data Management

### Test Fixtures
- **Static Data**: Predefined test data
- **Dynamic Generation**: Generate test data on-demand
- **Database Seeding**: Populate test databases
- **Cleanup**: Clean up test data after tests

### Mock Data
- **Provider Responses**: Mock external API responses
- **Database Records**: Mock database records
- **User Sessions**: Mock authenticated sessions
- **Error Scenarios**: Mock error conditions

## Continuous Integration Testing

### Automated Testing
- **Test Pipeline**: Automated test execution
- **Code Coverage**: Track test coverage metrics
- **Quality Gates**: Enforce testing requirements
- **Parallel Execution**: Run tests in parallel

### Test Reporting
- **Test Results**: Generate test reports
- **Coverage Reports**: Track code coverage
- **Performance Metrics**: Monitor test performance
- **Failure Analysis**: Analyze test failures

## Best Practices

### Test Writing
- **Clear Test Names**: Descriptive test names
- **Arrange-Act-Assert**: Clear test structure
- **Single Responsibility**: One assertion per test
- **Independent Tests**: Tests should not depend on each other

### Test Maintenance
- **Regular Updates**: Keep tests updated with code changes
- **Refactoring**: Refactor tests as needed
- **Test Coverage**: Maintain adequate test coverage
- **Documentation**: Document complex test scenarios

### Performance
- **Fast Tests**: Keep tests fast and efficient
- **Parallel Execution**: Run tests in parallel
- **Test Isolation**: Isolate test environments
- **Resource Management**: Manage test resources efficiently
