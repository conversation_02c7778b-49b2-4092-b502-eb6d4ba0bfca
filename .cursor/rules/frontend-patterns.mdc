---
globs: *.vue,*.ts,*.js
---

# Frontend Vue.js/TypeScript Patterns

## Application Structure
The project has two main frontend applications:
- **Admin Frontend**: [admin-frontend/](mdc:admin-frontend/) - Administrative dashboard
- **User Frontend**: [user-frontend/](mdc:user-frontend/) - Customer-facing interface

## Key Directories
- **Components**: Reusable Vue components in [src/components/](mdc:src/components/)
- **Views**: Page-level components in [src/views/](mdc:src/views/)
- **API**: HTTP client and API definitions in [src/api/](mdc:src/api/)
- **Store**: Pinia state management in [src/store/](mdc:src/store/)
- **Types**: TypeScript type definitions in [src/types/](mdc:src/types/)
- **Utils**: Helper functions and utilities in [src/utils/](mdc:src/utils/)

## Vue.js Patterns

### Component Structure
- **Composition API**: Use `<script setup>` for component logic
- **TypeScript**: All components use TypeScript for type safety
- **Props/Emits**: Define clear interfaces for component communication
- **Composables**: Reusable logic in [src/composables/](mdc:src/composables/)

### State Management
- **Pinia**: Modern Vue state management
- **Module-based**: Separate stores for different features
- **Reactive**: Use reactive refs and computed properties
- **Persistence**: Store important state in localStorage/sessionStorage

### Routing
- **Vue Router**: File-based routing structure
- **Guards**: Navigation guards for authentication
- **Lazy Loading**: Route-level code splitting
- **Meta Fields**: Route metadata for permissions and breadcrumbs

## TypeScript Patterns

### Type Definitions
- **API Models**: Define types for API responses in [src/types/](mdc:src/types/)
- **Component Props**: Strict typing for component interfaces
- **Event Handling**: Type-safe event handlers
- **Generic Types**: Reusable generic types for common patterns

### API Integration
- **HTTP Client**: Axios-based client with interceptors
- **Request/Response Types**: Typed API contracts
- **Error Handling**: Consistent error handling patterns
- **Loading States**: Reactive loading indicators

## Styling Patterns

### CSS/SCSS
- **Scoped Styles**: Component-scoped styling
- **SCSS Variables**: Consistent design tokens
- **Responsive Design**: Mobile-first approach
- **Component Library**: Reusable UI components

### UI Framework
- **Element Plus**: Vue 3 UI library integration
- **Icons**: Consistent icon usage
- **Theming**: Dark/light theme support
- **Accessibility**: ARIA labels and keyboard navigation

## Development Patterns

### Code Quality
- **ESLint**: Consistent code formatting
- **Prettier**: Automatic code formatting
- **TypeScript**: Strict type checking
- **Vue DevTools**: Development debugging

### Build & Deployment
- **Vite**: Fast build tooling
- **Environment Variables**: Environment-specific configuration
- **Production Optimization**: Bundle splitting and optimization
- **Static Assets**: Proper asset handling and optimization
