---
description: API and service patterns for express delivery system
---

# API & Service Patterns

## Express Delivery Provider Integration

### Unified Gateway Pattern
The system uses a unified gateway approach to handle multiple express delivery providers:
- **Unified Gateway Handler**: [api/handler/unified_gateway_handler.go](mdc:api/handler/unified_gateway_handler.go)
- **Provider Adapters**: Individual adapters for each provider in [internal/adapter/](mdc:internal/adapter/)
- **Standardized Response**: Common response format across all providers

### Supported Providers
- **JD Express**: [internal/adapter/jd.go](mdc:internal/adapter/jd.go)
- **Cainiao**: [internal/adapter/cainiao.go](mdc:internal/adapter/cainiao.go)
- **Yida**: [internal/adapter/yida.go](mdc:internal/adapter/yida.go)
- **Yuntong**: [internal/adapter/yuntong.go](mdc:internal/adapter/yuntong.go)
- **Kuaidi100**: [internal/adapter/kuaidi100.go](mdc:internal/adapter/kuaidi100.go)

### Provider Factory Pattern
- **Dynamic Provider Selection**: Runtime provider switching based on configuration
- **Provider Status Checking**: Health monitoring for providers
- **Fallback Mechanisms**: Automatic fallback to backup providers

## Callback System

### Callback Handling Architecture
- **Callback Router**: [api/router/callback_router.go](mdc:api/router/callback_router.go)
- **Callback Service**: [internal/service/callback/](mdc:internal/service/callback/)
- **Separate Callback Service**: [callback-receiver-service/](mdc:callback-receiver-service/)

### Callback Processing
- **Standardization**: Convert provider-specific callbacks to standard format
- **Validation**: Validate callback authenticity and content
- **Status Mapping**: Map provider statuses to internal status codes
- **Notification**: Send updates to customers and internal systems

## Order Management

### Order Lifecycle
1. **Order Creation**: Validate order details and pricing
2. **Provider Selection**: Choose optimal provider based on rules
3. **Order Submission**: Submit to selected provider
4. **Status Tracking**: Process status updates via callbacks
5. **Completion**: Handle delivery completion and billing

### Price Validation
- **Multi-Provider Pricing**: Query multiple providers for best rates
- **Price Validation Service**: [internal/service/order_price_validation_service.go](mdc:internal/service/order_price_validation_service.go)
- **Real-time Pricing**: Dynamic pricing based on current provider rates

## API Design Patterns

### RESTful API Structure
- **Resource-based URLs**: Clear resource hierarchy
- **HTTP Methods**: Proper use of GET, POST, PUT, DELETE
- **Status Codes**: Appropriate HTTP status codes
- **Error Responses**: Consistent error format

### Request/Response Patterns
- **Request Validation**: Input validation at API boundary
- **Response Formatting**: Standardized response structure
- **Pagination**: Consistent pagination for list endpoints
- **Filtering**: Common filtering and sorting patterns

### Authentication & Authorization
- **JWT Tokens**: Stateless authentication
- **Role-based Access**: Different access levels for admin/user
- **API Keys**: External system integration authentication
- **Rate Limiting**: Prevent API abuse

## Service Communication

### Internal Service Communication
- **Service Layer**: Business logic encapsulation
- **Repository Pattern**: Data access abstraction
- **Event-driven Updates**: Asynchronous processing where appropriate
- **Transaction Management**: Proper transaction boundaries

### External Integration
- **HTTP Client**: Robust HTTP client with retry logic
- **Circuit Breaker**: Prevent cascading failures
- **Timeout Handling**: Proper timeout configuration
- **Error Recovery**: Graceful error handling and recovery

## Data Consistency

### Database Patterns
- **Transaction Boundaries**: Proper transaction management
- **Data Validation**: Multi-layer validation
- **Audit Logging**: Track all significant changes
- **Soft Deletes**: Maintain data history

### Cache Management
- **Cache Invalidation**: Proper cache invalidation strategies
- **Cache Warming**: Preload frequently accessed data
- **Cache Consistency**: Maintain consistency between cache and database
