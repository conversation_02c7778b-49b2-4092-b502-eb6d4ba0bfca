---
alwaysApply: true
---

# Go-Kuaidi Express Delivery Management System

## System Architecture
This is a microservices-based express delivery management system with the following main components:

- **Main Go Backend**: Root directory with core logistics services
- **Admin Frontend**: Vue.js/TypeScript dashboard at [admin-frontend/](mdc:admin-frontend/)
- **User Frontend**: Vue.js/TypeScript client interface at [user-frontend/](mdc:user-frontend/)
- **Callback Receiver**: Separate service at [callback-receiver-service/](mdc:callback-receiver-service/)

## Key Entry Points
- **Main Server**: [cmd/main.go](mdc:cmd/main.go) - Primary application entry point
- **API Handlers**: [api/handler/](mdc:api/handler/) - REST API endpoints
- **Internal Services**: [internal/service/](mdc:internal/service/) - Business logic layer
- **Configuration**: [config/config.yaml](mdc:config/config.yaml) - Main system configuration

## Core Functionality
- **Order Management**: Create, track, and manage express delivery orders
- **Price Validation**: Multi-provider price comparison and validation
- **Callback Handling**: Process delivery status updates from providers
- **Provider Integration**: Support for multiple express delivery providers (JD, Cainiao, etc.)
- **Admin Dashboard**: Administrative interface for system management
- **User Interface**: Customer-facing order tracking and management

## Provider System
The system supports multiple express delivery providers through a plugin-like architecture:
- **Adapters**: [internal/adapter/](mdc:internal/adapter/) - Provider-specific implementations
- **Unified Gateway**: Standardized interface for all providers
- **Dynamic Provider Management**: Runtime provider configuration and switching
