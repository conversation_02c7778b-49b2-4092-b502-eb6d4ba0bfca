#!/bin/bash

echo "🎉 缓存明细筛选功能演示"
echo "======================="

echo ""
echo "✨ 功能特性:"
echo "   1. 📊 状态筛选：全部记录 / 成功缓存 / 失败记录"
echo "   2. 🔍 关键词搜索：按路线名称搜索"
echo "   3. 📈 实时统计：显示各类记录的数量"
echo "   4. 🎨 可视化标识：成功/失败状态用不同颜色区分"
echo "   5. 💡 失败原因详情：鼠标悬停查看完整错误信息"

echo ""
echo "🔧 技术实现:"
echo "   - 前端：Vue 3 + TypeScript + Element Plus"
echo "   - 后端：Go + Gin + PostgreSQL"
echo "   - 数据源：weight_tier_query_logs 表"
echo "   - 实时筛选：客户端计算属性"

echo ""
echo "📊 数据验证:"

# 验证API数据
echo "   正在验证API数据..."
TOTAL_RECORDS=$(curl -s "http://localhost:8081/api/v1/weight-cache/details?provider=kuaidi100&express_code=STO&page=1&page_size=100" | python3 -c "
import json
import sys
try:
    data = json.load(sys.stdin)
    if data.get('success'):
        records = data.get('data', {}).get('records', [])
        total = len(records)
        success = len([r for r in records if (r.get('failed_queries', 0) == 0)])
        failed = len([r for r in records if (r.get('failed_queries', 0) > 0)])
        print(f'{total},{success},{failed}')
    else:
        print('0,0,0')
except:
    print('0,0,0')
")

IFS=',' read -r TOTAL SUCCESS FAILED <<< "$TOTAL_RECORDS"

echo "   ✅ 数据统计验证完成:"
echo "      - 总记录数: $TOTAL"
echo "      - 成功缓存: $SUCCESS"
echo "      - 失败记录: $FAILED"

echo ""
echo "🎯 使用指南:"
echo ""
echo "1️⃣  访问系统:"
echo "   🌐 打开浏览器访问: http://localhost:3008/weight-cache"
echo "   🔐 登录账号: admin / **********+.aA..@"
echo ""
echo "2️⃣  打开缓存明细:"
echo "   📋 找到快递100供应商的申通快递(STO)"
echo "   🖱️  点击'缓存明细'按钮"
echo ""
echo "3️⃣  使用筛选功能:"
echo "   🔽 状态筛选下拉框:"
echo "      - 全部记录：显示所有 $TOTAL 条记录"
echo "      - ✅ 成功缓存：只显示 $SUCCESS 条成功记录"
echo "      - ❌ 失败记录：只显示 $FAILED 条失败记录"
echo ""
echo "   🔍 搜索框："
echo "      - 输入省份名称（如：湖南、广东）"
echo "      - 支持与状态筛选组合使用"
echo ""
echo "4️⃣  查看详细信息:"
echo "   📊 统计信息栏：实时显示筛选结果统计"
echo "   🏷️  失败统计列：显示失败次数和总查询次数"
echo "   🚨 失败原因列：显示具体错误信息"
echo "   💡 鼠标悬停：查看完整的失败原因详情"
echo ""
echo "5️⃣  测试建议:"
echo "   🧪 筛选测试："
echo "      - 先选择'全部记录'，观察总数"
echo "      - 切换到'成功缓存'，验证筛选效果"
echo "      - 切换到'失败记录'，查看失败详情"
echo ""
echo "   🔍 搜索测试："
echo "      - 搜索'湖南'，查看相关路线"
echo "      - 结合'失败记录'筛选，查找问题路线"
echo ""
echo "✅ 功能演示准备完成！"
echo ""
echo "💡 提示：如果看不到失败记录，可以尝试搜索'湖南->陕西'路线，"
echo "   该路线有已知的失败记录用于测试筛选功能。"
