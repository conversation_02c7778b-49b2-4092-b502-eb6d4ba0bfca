package main

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"github.com/go-redis/redis/v8"
	_ "github.com/lib/pq"
)

// CallbackNotification 回调通知消息
type CallbackNotification struct {
	ID       string `json:"id"`
	Provider string `json:"provider"`
}

// CallbackRawData 原始回调数据
type CallbackRawData struct {
	ID         string            `json:"id"`
	Provider   string            `json:"provider"`
	RawBody    string            `json:"raw_body"`
	Headers    map[string]string `json:"headers"`
	ClientIP   string            `json:"client_ip"`
	ReceivedAt time.Time         `json:"received_at"`
	Processed  bool              `json:"processed"`
	CreatedAt  time.Time         `json:"created_at"`
}

// CallbackConsumer 回调消费者
type CallbackConsumer struct {
	receiverDB      *sql.DB // 回调接收服务的数据库
	mainSystemDB    *sql.DB // 主系统数据库
	redis           *redis.Client
	queueName       string
	callbackService CallbackServiceInterface // 主系统原有的回调服务
}

// CallbackResponse 回调响应
type CallbackResponse struct {
	Success   bool   `json:"success"`
	Message   string `json:"message"`
	RequestID string `json:"request_id,omitempty"`
}

// CallbackServiceInterface 主系统回调服务接口
type CallbackServiceInterface interface {
	ProcessCallback(ctx context.Context, provider string, rawData []byte, headers map[string]string) (*CallbackResponse, error)
}

func NewCallbackConsumer(
	receiverDB *sql.DB,
	mainSystemDB *sql.DB,
	redis *redis.Client,
	queueName string,
	callbackService CallbackServiceInterface,
) *CallbackConsumer {
	return &CallbackConsumer{
		receiverDB:      receiverDB,
		mainSystemDB:    mainSystemDB,
		redis:           redis,
		queueName:       queueName,
		callbackService: callbackService,
	}
}

// Start 启动消费者
func (c *CallbackConsumer) Start(ctx context.Context) error {
	log.Println("启动回调消费者...")

	for {
		select {
		case <-ctx.Done():
			log.Println("停止回调消费者")
			return ctx.Err()
		default:
			// 从Redis队列获取通知
			result, err := c.redis.BRPop(ctx, 5*time.Second, c.queueName).Result()
			if err != nil {
				if err == redis.Nil {
					// 队列为空，继续等待
					continue
				}
				log.Printf("从Redis获取消息失败: %v", err)
				time.Sleep(1 * time.Second)
				continue
			}

			if len(result) < 2 {
				continue
			}

			// 解析通知消息
			var notification CallbackNotification
			if err := json.Unmarshal([]byte(result[1]), &notification); err != nil {
				log.Printf("解析通知消息失败: %v", err)
				continue
			}

			// 处理回调
			if err := c.processCallback(ctx, &notification); err != nil {
				log.Printf("处理回调失败: %v", err)
			}
		}
	}
}

// processCallback 处理回调
func (c *CallbackConsumer) processCallback(ctx context.Context, notification *CallbackNotification) error {
	log.Printf("处理回调: ID=%s, Provider=%s", notification.ID, notification.Provider)

	// 1. 从回调接收服务数据库获取原始数据
	rawData, err := c.getRawCallbackData(ctx, notification.ID)
	if err != nil {
		return err
	}

	// 2. 调用主系统原有的回调处理逻辑
	response, err := c.callbackService.ProcessCallback(
		ctx,
		rawData.Provider,
		[]byte(rawData.RawBody),
		rawData.Headers,
	)

	if err != nil {
		log.Printf("主系统处理回调失败: %v", err)
		return err
	}

	if response != nil && !response.Success {
		log.Printf("主系统回调处理失败: %s", response.Message)
		return fmt.Errorf("主系统回调处理失败: %s", response.Message)
	}

	// 3. 标记为已处理
	err = c.markAsProcessed(ctx, notification.ID)
	if err != nil {
		log.Printf("标记已处理失败: %v", err)
		// 不返回错误，因为业务逻辑已经处理成功
	}

	log.Printf("回调处理完成: ID=%s", notification.ID)
	return nil
}

// getRawCallbackData 从回调接收服务数据库获取原始数据
func (c *CallbackConsumer) getRawCallbackData(ctx context.Context, id string) (*CallbackRawData, error) {
	query := `
		SELECT id, provider, raw_body, headers, client_ip, 
		       received_at, processed, created_at
		FROM callback_raw_data 
		WHERE id = $1
	`

	row := c.receiverDB.QueryRowContext(ctx, query, id)

	var data CallbackRawData
	var headersJSON []byte

	err := row.Scan(
		&data.ID,
		&data.Provider,
		&data.RawBody,
		&headersJSON,
		&data.ClientIP,
		&data.ReceivedAt,
		&data.Processed,
		&data.CreatedAt,
	)

	if err != nil {
		return nil, err
	}

	// 反序列化headers
	err = json.Unmarshal(headersJSON, &data.Headers)
	if err != nil {
		return nil, err
	}

	return &data, nil
}

// markAsProcessed 标记为已处理
func (c *CallbackConsumer) markAsProcessed(ctx context.Context, id string) error {
	query := `UPDATE callback_raw_data SET processed = true WHERE id = $1`
	_, err := c.receiverDB.ExecContext(ctx, query, id)
	return err
}

// 示例：如何在主系统中集成
func main() {
	// 连接回调接收服务数据库
	receiverDB, err := sql.Open("postgres", "postgresql://postgres:<EMAIL>:5432/callback_receiver?sslmode=disable")
	if err != nil {
		log.Fatal(err)
	}
	defer receiverDB.Close()

	// 连接主系统数据库
	mainSystemDB, err := sql.Open("postgres", "postgresql://postgres:<EMAIL>:5432/go_kuaidi?sslmode=disable")
	if err != nil {
		log.Fatal(err)
	}
	defer mainSystemDB.Close()

	// 连接Redis
	rdb := redis.NewClient(&redis.Options{
		Addr: "localhost:6379",
	})
	defer rdb.Close()

	// 创建主系统的回调服务实例
	// var callbackService CallbackServiceInterface = yourExistingCallbackService

	// 创建消费者
	// consumer := NewCallbackConsumer(receiverDB, mainSystemDB, rdb, "callback_notifications", callbackService)

	// 启动消费者
	// ctx := context.Background()
	// consumer.Start(ctx)
}
