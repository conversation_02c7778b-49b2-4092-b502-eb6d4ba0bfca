# 回调接收微服务 Makefile

.PHONY: help build test run clean docker-build docker-run deps lint

# 默认目标
help:
	@echo "可用命令:"
	@echo "  deps         - 下载依赖"
	@echo "  build        - 构建应用"
	@echo "  test         - 运行测试"
	@echo "  run          - 运行应用"
	@echo "  clean        - 清理构建文件"
	@echo "  docker-build - 构建Docker镜像"
	@echo "  docker-run   - 运行Docker容器"
	@echo "  lint         - 代码检查"

# 下载依赖
deps:
	go mod download
	go mod tidy

# 构建应用
build:
	CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o bin/callback-receiver ./cmd/main.go

# 运行测试
test:
	go test -v ./...

# 运行应用
run:
	go run ./cmd/main.go

# 清理构建文件
clean:
	rm -rf bin/
	go clean

# 构建Docker镜像
docker-build:
	docker build -t callback-receiver:latest .

# 运行Docker容器
docker-run:
	docker-compose up -d

# 代码检查
lint:
	golangci-lint run

# 格式化代码
fmt:
	go fmt ./...

# 生成模拟对象
mock:
	mockgen -source=internal/repository/interfaces.go -destination=internal/repository/mocks/mock_repository.go
	mockgen -source=internal/service/interfaces.go -destination=internal/service/mocks/mock_service.go
