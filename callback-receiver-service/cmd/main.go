package main

import (
	"context"
	"database/sql"
	"net/http"
	"os"
	"os/signal"
	"strings"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	_ "github.com/lib/pq"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"

	"callback-receiver/internal/adapter"
	"callback-receiver/internal/config"
	"callback-receiver/internal/handler"
	"callback-receiver/internal/repository"
	"callback-receiver/internal/service"
)

func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		panic("加载配置失败: " + err.Error())
	}

	// 初始化日志
	logger, err := initLogger(cfg)
	if err != nil {
		panic("初始化日志失败: " + err.Error())
	}
	defer logger.Sync()

	logger.Info("🚀 启动回调接收服务",
		zap.String("service", cfg.Service.Name),
		zap.String("version", cfg.Service.Version),
		zap.String("environment", cfg.Service.Environment),
		zap.String("port", cfg.Server.Port))

	// 连接数据库
	db, err := sql.Open("postgres", cfg.Database.URL)
	if err != nil {
		logger.Fatal("连接数据库失败", zap.Error(err))
	}
	defer db.Close()

	// 应用数据库连接池配置
	db.SetMaxOpenConns(cfg.Database.MaxOpenConns)
	db.SetMaxIdleConns(cfg.Database.MaxIdleConns)
	db.SetConnMaxLifetime(time.Duration(cfg.Database.ConnMaxLifetime) * time.Second)
	db.SetConnMaxIdleTime(time.Duration(cfg.Database.ConnMaxIdleTime) * time.Second)

	// 测试数据库连接
	if err := db.Ping(); err != nil {
		logger.Fatal("数据库连接测试失败", zap.Error(err))
	}
	logger.Info("✅ 数据库连接成功")

	// 连接Redis
	var rdb *redis.Client
	if strings.HasPrefix(cfg.Redis.URL, "redis://") {
		// 解析Redis URL
		opts, err := redis.ParseURL(cfg.Redis.URL)
		if err != nil {
			logger.Fatal("解析Redis URL失败", zap.Error(err))
		}

		// 应用Redis配置
		opts.PoolSize = cfg.Redis.PoolSize
		opts.MinIdleConns = cfg.Redis.MinIdleConns
		opts.MaxConnAge = time.Duration(cfg.Redis.MaxConnAge) * time.Second
		opts.PoolTimeout = time.Duration(cfg.Redis.PoolTimeout) * time.Second
		opts.IdleTimeout = time.Duration(cfg.Redis.IdleTimeout) * time.Second
		opts.DialTimeout = cfg.Redis.DialTimeout
		opts.ReadTimeout = cfg.Redis.ReadTimeout
		opts.WriteTimeout = cfg.Redis.WriteTimeout

		rdb = redis.NewClient(opts)
	} else {
		// 简单地址格式
		rdb = redis.NewClient(&redis.Options{
			Addr:         cfg.Redis.URL,
			PoolSize:     cfg.Redis.PoolSize,
			MinIdleConns: cfg.Redis.MinIdleConns,
			MaxConnAge:   time.Duration(cfg.Redis.MaxConnAge) * time.Second,
			PoolTimeout:  time.Duration(cfg.Redis.PoolTimeout) * time.Second,
			IdleTimeout:  time.Duration(cfg.Redis.IdleTimeout) * time.Second,
			DialTimeout:  cfg.Redis.DialTimeout,
			ReadTimeout:  cfg.Redis.ReadTimeout,
			WriteTimeout: cfg.Redis.WriteTimeout,
		})
	}
	defer rdb.Close()

	// 测试Redis连接
	if err := rdb.Ping(context.Background()).Err(); err != nil {
		logger.Fatal("Redis连接测试失败", zap.Error(err))
	}
	logger.Info("✅ Redis连接成功", zap.String("queue", cfg.Redis.QueueName))

	// 创建Redis适配器
	redisAdapter := adapter.NewRedisAdapter(rdb)

	// 创建仓储层
	repo := repository.NewCallbackRepository(db)

	// 创建服务层
	notifier := service.NewRedisNotifier(redisAdapter, cfg.Redis.QueueName)
	callbackService := service.NewCallbackService(repo, notifier, logger)

	// 创建处理器
	callbackHandler := handler.NewCallbackHandler(callbackService, logger)

	// 设置路由
	r := setupRouter(callbackHandler, cfg)

	// 创建HTTP服务器
	srv := &http.Server{
		Addr:         ":" + cfg.Server.Port,
		Handler:      r,
		ReadTimeout:  cfg.Server.ReadTimeout,
		WriteTimeout: cfg.Server.WriteTimeout,
		IdleTimeout:  cfg.Server.IdleTimeout,
	}

	// 启动服务器
	go func() {
		logger.Info("✅ 回调接收服务已启动",
			zap.String("address", ":"+cfg.Server.Port),
			zap.Duration("read_timeout", cfg.Server.ReadTimeout),
			zap.Duration("write_timeout", cfg.Server.WriteTimeout))

		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Fatal("服务器启动失败", zap.Error(err))
		}
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("🛑 收到停止信号，正在优雅关闭服务器...")

	// 优雅关闭
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := srv.Shutdown(ctx); err != nil {
		logger.Error("服务器关闭失败", zap.Error(err))
	} else {
		logger.Info("✅ 服务器已安全关闭")
	}
}

func initLogger(cfg *config.Config) (*zap.Logger, error) {
	level := zapcore.InfoLevel
	switch cfg.Logging.Level {
	case "debug":
		level = zapcore.DebugLevel
	case "warn":
		level = zapcore.WarnLevel
	case "error":
		level = zapcore.ErrorLevel
	}

	config := zap.Config{
		Level:       zap.NewAtomicLevelAt(level),
		Development: cfg.Development.Debug,
		Encoding:    cfg.Logging.Format,
		EncoderConfig: zapcore.EncoderConfig{
			TimeKey:        "timestamp",
			LevelKey:       "level",
			NameKey:        "logger",
			CallerKey:      "caller",
			MessageKey:     "msg",
			StacktraceKey:  "stacktrace",
			LineEnding:     zapcore.DefaultLineEnding,
			EncodeLevel:    zapcore.LowercaseLevelEncoder,
			EncodeTime:     zapcore.ISO8601TimeEncoder,
			EncodeDuration: zapcore.SecondsDurationEncoder,
			EncodeCaller:   zapcore.ShortCallerEncoder,
		},
		OutputPaths:      []string{"stdout"},
		ErrorOutputPaths: []string{"stderr"},
	}

	return config.Build()
}

func setupRouter(handler *handler.CallbackHandler, cfg *config.Config) *gin.Engine {
	// 设置运行模式
	if cfg.Development.Debug {
		gin.SetMode(gin.DebugMode)
	} else {
		gin.SetMode(gin.ReleaseMode)
	}

	r := gin.New()
	r.Use(gin.Recovery())

	// 添加请求日志中间件
	if cfg.Logging.Level == "debug" {
		r.Use(gin.Logger())
	}

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":    "ok",
			"service":   cfg.Service.Name,
			"version":   cfg.Service.Version,
			"timestamp": time.Now().Unix(),
		})
	})

	// 回调接收端点 - 使用配置中的提供商配置
	webhook := r.Group("/webhook")
	{
		if cfg.Providers.Yuntong.Enabled {
			webhook.POST("/yuntong", handler.ReceiveYuntong)
		}
		if cfg.Providers.Yida.Enabled {
			webhook.POST("/yida", handler.ReceiveYida)
		}
		if cfg.Providers.Kuaidi100.Enabled {
			webhook.POST("/kuaidi100", handler.ReceiveKuaidi100)
		}
		if cfg.Providers.Cainiao.Enabled {
			webhook.POST("/cainiao", handler.ReceiveCainiao)
		}
		if cfg.Providers.Kuaidiniao.Enabled {
			webhook.POST("/kuaidiniao", handler.ReceiveKuaidiniao)
		}
	}

	return r
}
