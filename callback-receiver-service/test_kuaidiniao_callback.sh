#!/bin/bash

# 快递鸟回调测试脚本
# 用于测试回调接收服务是否正确处理快递鸟回调

echo "🧪 测试快递鸟回调接收服务"

# 回调接收服务地址
CALLBACK_URL="http://localhost:8082/webhook/kuaidiniao"

# 测试数据 - 快递鸟状态推送回调
CALLBACK_DATA='{
  "PushType": 1,
  "EBusinessID": "test_business_id",
  "OrderCode": "TEST_ORDER_20250720001",
  "LogisticCode": "KDN2025072000001",
  "ShipperCode": "YTO",
  "State": "1",
  "StateEx": "1",
  "Location": "广东省深圳市",
  "PushTime": "2025-07-20 10:30:00",
  "Traces": [
    {
      "AcceptTime": "2025-07-20 10:30:00",
      "AcceptStation": "已揽收",
      "Location": "广东省深圳市"
    }
  ]
}'

echo "📤 发送快递鸟回调数据到: $CALLBACK_URL"
echo "📦 回调数据:"
echo "$CALLBACK_DATA" | jq .

echo ""
echo "🚀 发送请求..."

# 发送回调请求
RESPONSE=$(curl -s -w "\n%{http_code}" \
  -X POST \
  -H "Content-Type: application/json" \
  -H "User-Agent: KuaidiNiao-Callback/1.0" \
  -d "$CALLBACK_DATA" \
  "$CALLBACK_URL")

# 分离响应体和状态码
HTTP_CODE=$(echo "$RESPONSE" | tail -n1)
RESPONSE_BODY=$(echo "$RESPONSE" | head -n -1)

echo "📥 响应状态码: $HTTP_CODE"
echo "📥 响应内容:"
echo "$RESPONSE_BODY" | jq . 2>/dev/null || echo "$RESPONSE_BODY"

# 检查响应
if [ "$HTTP_CODE" = "200" ]; then
    echo "✅ 回调请求成功"
    
    # 检查响应格式是否符合快递鸟期望
    RESULT=$(echo "$RESPONSE_BODY" | jq -r '.Result // empty' 2>/dev/null)
    RESULT_CODE=$(echo "$RESPONSE_BODY" | jq -r '.ResultCode // empty' 2>/dev/null)
    REASON=$(echo "$RESPONSE_BODY" | jq -r '.Reason // empty' 2>/dev/null)
    
    if [ "$RESULT" = "true" ] && [ "$RESULT_CODE" = "100" ] && [ "$REASON" = "成功" ]; then
        echo "✅ 响应格式正确，符合快递鸟期望"
    else
        echo "⚠️  响应格式可能不符合快递鸟期望"
        echo "   期望: {\"Result\": true, \"ResultCode\": \"100\", \"Reason\": \"成功\"}"
        echo "   实际: $RESPONSE_BODY"
    fi
else
    echo "❌ 回调请求失败，状态码: $HTTP_CODE"
    echo "   响应: $RESPONSE_BODY"
fi

echo ""
echo "🔍 检查日志..."
echo "请查看回调接收服务的日志，确认回调数据已正确处理并发送到Redis队列"

echo ""
echo "📋 测试完成"
echo "如果测试成功，回调数据应该："
echo "1. 被保存到 callback_raw_data 表"
echo "2. 发送通知到 Redis 队列 'callback_notifications'"
echo "3. 主服务的消费者应该能够处理该通知"
