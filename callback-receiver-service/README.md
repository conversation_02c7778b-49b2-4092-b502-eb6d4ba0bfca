# 回调服务集群

一个高性能、高可用的回调服务集群，包含回调接收服务和回调消费者，专门用于接收供应商的回调通知并确保数据不丢失。

## 🏗️ 架构设计

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   供应商回调     │───▶│  回调接收微服务   │───▶│   PostgreSQL    │
│  (HTTP请求)     │    │  (端口8080)      │    │  (原始数据)      │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌──────────────┐
                       │   Redis       │
                       │  (消息队列)   │
                       └──────────────┘
                              │
                              ▼
                       ┌──────────────┐    ┌─────────────────┐
                       │  回调消费者   │───▶│   主系统         │
                       │  (独立进程)   │    │  (端口8081)      │
                       └──────────────┘    └─────────────────┘
```

## ✨ 功能特性

### 🔥 核心特性
- ✅ **永不丢失回调** - 立即持久化，三重保障机制
- ✅ **高性能响应** - 毫秒级回调响应时间
- ✅ **高可用架构** - 服务完全解耦，故障隔离
- ✅ **智能重试** - 指数退避，自动恢复失败消息
- ✅ **实时监控** - 详细日志，健康检查

### 📦 服务组件
- 📥 **回调接收服务**: 接收供应商回调，存储到数据库
- 🔄 **回调消费者**: 消费队列消息，转发到主系统
- 🛡️ **容错机制**: 重试、重新入队、定时恢复

## 🚀 快速开始

### 环境要求
- Go 1.19+
- PostgreSQL 12+
- Redis 6+

### 一键启动

```bash
# 克隆项目
git clone <repository-url>
cd callback-receiver-service

# 启动所有服务
./start.sh start
```

### 管理命令

```bash
# 查看帮助
./start.sh help

# 启动服务集群
./start.sh start

# 查看服务状态
./start.sh status

# 查看日志
./start.sh logs              # 所有日志
./start.sh logs receiver     # 接收服务日志
./start.sh logs consumer     # 消费者日志

# 健康检查
./start.sh health

# 停止服务
./start.sh stop

# 重启服务
./start.sh restart

# 构建服务
./start.sh build
```

## � 服务状态示例

```bash
$ ./start.sh status
=========================================
回调服务集群状态
=========================================
📥 回调接收服务:
   状态: 运行中 (PID: 12345)
   地址: http://localhost:8080
🔄 回调消费者:
   状态: 运行中 (PID: 12346)
   队列: callback_notifications
=========================================
```
