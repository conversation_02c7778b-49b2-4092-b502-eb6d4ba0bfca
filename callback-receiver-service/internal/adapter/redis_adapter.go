package adapter

import (
	"context"

	"github.com/go-redis/redis/v8"
)

// RedisAdapter Redis适配器 (遵循适配器模式)
type RedisAdapter struct {
	client *redis.Client
}

func NewRedisAdapter(client *redis.Client) *RedisAdapter {
	return &RedisAdapter{client: client}
}

// LPush 实现RedisClient接口
func (r *RedisAdapter) LPush(ctx context.Context, key string, values ...interface{}) error {
	return r.client.LPush(ctx, key, values...).Err()
}
