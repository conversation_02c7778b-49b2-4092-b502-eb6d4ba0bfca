package config

import (
	"encoding/json"
	"fmt"
	"os"
	"time"
)

type Config struct {
	Service     ServiceConfig     `json:"service"`
	Server      ServerConfig      `json:"server"`
	Database    DatabaseConfig    `json:"database"`
	Redis       RedisConfig       `json:"redis"`
	MainSystem  MainSystemConfig  `json:"main_system"`
	Consumer    ConsumerConfig    `json:"consumer"`
	Providers   ProvidersConfig   `json:"providers"`
	WorkOrder   WorkOrderConfig   `json:"workorder_callback"`
	Security    SecurityConfig    `json:"security"`
	Monitoring  MonitoringConfig  `json:"monitoring"`
	Logging     LoggingConfig     `json:"logging"`
	Performance PerformanceConfig `json:"performance"`
	Recovery    RecoveryConfig    `json:"recovery"`
	Development DevelopmentConfig `json:"development"`
}

type ServiceConfig struct {
	Name        string `json:"name"`
	Version     string `json:"version"`
	Environment string `json:"environment"`
}

type ServerConfig struct {
	Port         string        `json:"port"`
	Timeout      time.Duration `json:"timeout"`
	ReadTimeout  time.Duration `json:"read_timeout"`
	WriteTimeout time.Duration `json:"write_timeout"`
	IdleTimeout  time.Duration `json:"idle_timeout"`
}

type DatabaseConfig struct {
	URL             string `json:"url"`
	MaxOpenConns    int    `json:"max_open_conns"`
	MaxIdleConns    int    `json:"max_idle_conns"`
	ConnMaxLifetime int    `json:"conn_max_lifetime"`
	ConnMaxIdleTime int    `json:"conn_max_idle_time"`
}

type RedisConfig struct {
	URL              string        `json:"url"`
	QueueName        string        `json:"queue_name"`
	DelayedQueueName string        `json:"delayed_queue_name"`
	DB               int           `json:"db"`
	PoolSize         int           `json:"pool_size"`
	MinIdleConns     int           `json:"min_idle_conns"`
	MaxConnAge       int           `json:"max_conn_age"`
	PoolTimeout      int           `json:"pool_timeout"`
	IdleTimeout      int           `json:"idle_timeout"`
	DialTimeout      time.Duration `json:"dial_timeout"`
	ReadTimeout      time.Duration `json:"read_timeout"`
	WriteTimeout     time.Duration `json:"write_timeout"`
}

type MainSystemConfig struct {
	URL                 string        `json:"url"`
	Timeout             time.Duration `json:"timeout"`
	RetryAttempts       int           `json:"retry_attempts"`
	RetryDelay          time.Duration `json:"retry_delay"`
	MaxIdleConns        int           `json:"max_idle_conns"`
	MaxIdleConnsPerHost int           `json:"max_idle_conns_per_host"`
	IdleConnTimeout     time.Duration `json:"idle_conn_timeout"`
}

type ConsumerConfig struct {
	Port                string                    `json:"port"`
	WorkerCount         int                       `json:"worker_count"`
	BatchSize           int                       `json:"batch_size"`
	PollInterval        time.Duration             `json:"poll_interval"`
	VisibilityTimeout   time.Duration             `json:"visibility_timeout"`
	MaxRetries          int                       `json:"max_retries"`
	RetryDelay          time.Duration             `json:"retry_delay"`
	WorkerTimeout       time.Duration             `json:"worker_timeout"`
	EnableManagementAPI bool                      `json:"enable_management_api"`
	ManagementEndpoints ManagementEndpointsConfig `json:"management_endpoints"`
}

type ManagementEndpointsConfig struct {
	Health  string `json:"health"`
	Status  string `json:"status"`
	Metrics string `json:"metrics"`
	Queue   string `json:"queue"`
}

type ProvidersConfig struct {
	Kuaidi100  ProviderConfig `json:"kuaidi100"`
	Yida       ProviderConfig `json:"yida"`
	Yuntong    ProviderConfig `json:"yuntong"`
	Cainiao    ProviderConfig `json:"cainiao"`
	Kuaidiniao ProviderConfig `json:"kuaidiniao"`
}

type ProviderConfig struct {
	Enabled          bool                   `json:"enabled"`
	Endpoint         string                 `json:"endpoint"`
	APIEndpoint      string                 `json:"api_endpoint"`
	StatusField      string                 `json:"status_field"`
	ContentField     string                 `json:"content_field"`
	CommitterField   string                 `json:"committer_field"`
	AttachmentField  string                 `json:"attachment_field,omitempty"`
	WorkOrderIDField string                 `json:"work_order_id_field"`
	ResponseFormat   map[string]interface{} `json:"response_format"`
}

type WorkOrderConfig struct {
	Enabled              bool              `json:"enabled"`
	IdentificationFields []string          `json:"identification_fields"`
	ProviderEndpoints    map[string]string `json:"provider_endpoints"`
}

type SecurityConfig struct {
	APIKeyValidation    APIKeyValidationConfig    `json:"api_key_validation"`
	SignatureValidation SignatureValidationConfig `json:"signature_validation"`
	IPWhitelist         IPWhitelistConfig         `json:"ip_whitelist"`
}

type APIKeyValidationConfig struct {
	Enabled        bool     `json:"enabled"`
	HeaderName     string   `json:"header_name"`
	RequiredScopes []string `json:"required_scopes"`
}

type SignatureValidationConfig struct {
	Enabled      bool   `json:"enabled"`
	Algorithm    string `json:"algorithm"`
	HeaderName   string `json:"header_name"`
	SecretKeyEnv string `json:"secret_key_env"`
}

type IPWhitelistConfig struct {
	Enabled    bool     `json:"enabled"`
	AllowedIPs []string `json:"allowed_ips"`
}

type MonitoringConfig struct {
	Enabled             bool                 `json:"enabled"`
	MetricsPort         int                  `json:"metrics_port"`
	HealthCheckPort     int                  `json:"health_check_port"`
	HealthCheckInterval time.Duration        `json:"health_check_interval"`
	HealthCheckTimeout  time.Duration        `json:"health_check_timeout"`
	Endpoints           []MonitoringEndpoint `json:"endpoints"`
}

type MonitoringEndpoint struct {
	Name   string `json:"name"`
	Type   string `json:"type"`
	Config string `json:"config,omitempty"`
	URL    string `json:"url,omitempty"`
}

type LoggingConfig struct {
	Level           string            `json:"level"`
	Format          string            `json:"format"`
	Output          string            `json:"output"`
	FilePath        string            `json:"file_path"`
	MaxSize         int               `json:"max_size"`
	MaxBackups      int               `json:"max_backups"`
	MaxAge          int               `json:"max_age"`
	Compress        bool              `json:"compress"`
	Fields          map[string]string `json:"fields"`
	SensitiveFields []string          `json:"sensitive_fields"`
}

type PerformanceConfig struct {
	Concurrency ConcurrencyConfig `json:"concurrency"`
	Cache       CacheConfig       `json:"cache"`
}

type ConcurrencyConfig struct {
	MaxWorkers      int           `json:"max_workers"`
	QueueBufferSize int           `json:"queue_buffer_size"`
	WorkerTimeout   time.Duration `json:"worker_timeout"`
}

type CacheConfig struct {
	Enabled bool          `json:"enabled"`
	TTL     time.Duration `json:"ttl"`
	MaxSize int           `json:"max_size"`
}

type RecoveryConfig struct {
	FailedMessageRecovery FailedMessageRecoveryConfig `json:"failed_message_recovery"`
	DeadLetterQueue       DeadLetterQueueConfig       `json:"dead_letter_queue"`
}

type FailedMessageRecoveryConfig struct {
	Enabled       bool          `json:"enabled"`
	CheckInterval time.Duration `json:"check_interval"`
	MaxAge        time.Duration `json:"max_age"`
	BatchSize     int           `json:"batch_size"`
}

type DeadLetterQueueConfig struct {
	Enabled    bool          `json:"enabled"`
	QueueName  string        `json:"queue_name"`
	MaxRetries int           `json:"max_retries"`
	RetryDelay time.Duration `json:"retry_delay"`
}

type DevelopmentConfig struct {
	Debug          bool          `json:"debug"`
	Profiling      bool          `json:"profiling"`
	MockMainSystem bool          `json:"mock_main_system"`
	Testing        TestingConfig `json:"testing"`
}

type TestingConfig struct {
	Enabled       bool     `json:"enabled"`
	MockProviders []string `json:"mock_providers"`
	TestDataPath  string   `json:"test_data_path"`
}

// Load 加载配置
func Load() (*Config, error) {
	// 优先从配置文件加载
	if configFile := getEnv("CONFIG_FILE", "config.json"); configFile != "" {
		if _, err := os.Stat(configFile); err == nil {
			return loadFromFile(configFile)
		}
	}

	// 如果配置文件不存在，使用环境变量创建默认配置
	config := &Config{
		Service: ServiceConfig{
			Name:        getEnv("SERVICE_NAME", "callback-receiver-service"),
			Version:     getEnv("SERVICE_VERSION", "1.0.0"),
			Environment: getEnv("SERVICE_ENV", "production"),
		},
		Server: ServerConfig{
			Port:         getEnv("PORT", "8082"),
			Timeout:      5 * time.Second,
			ReadTimeout:  30 * time.Second,
			WriteTimeout: 30 * time.Second,
			IdleTimeout:  60 * time.Second,
		},
		Database: DatabaseConfig{
			URL:             getEnv("DATABASE_URL", "*************************************************/callback_receiver?sslmode=disable"),
			MaxOpenConns:    25,
			MaxIdleConns:    10,
			ConnMaxLifetime: 300,
			ConnMaxIdleTime: 60,
		},
		Redis: RedisConfig{
			URL:              getEnv("REDIS_URL", "redis://default:a63006320@8.138.252.193:6379"),
			QueueName:        getEnv("REDIS_QUEUE_NAME", "callback_notifications"),
			DelayedQueueName: getEnv("REDIS_DELAYED_QUEUE_NAME", "callback_notifications_delayed"),
			DB:               0,
			PoolSize:         10,
			MinIdleConns:     2,
			MaxConnAge:       300,
			PoolTimeout:      30,
			IdleTimeout:      300,
			DialTimeout:      5 * time.Second,
			ReadTimeout:      3 * time.Second,
			WriteTimeout:     3 * time.Second,
		},
		MainSystem: MainSystemConfig{
			URL:                 getEnv("MAIN_SYSTEM_URL", "https://my.py258.com"),
			Timeout:             30 * time.Second,
			RetryAttempts:       3,
			RetryDelay:          1 * time.Second,
			MaxIdleConns:        100,
			MaxIdleConnsPerHost: 10,
			IdleConnTimeout:     90 * time.Second,
		},
		Consumer: ConsumerConfig{
			Port:                getEnv("CONSUMER_PORT", "8083"),
			WorkerCount:         5,
			BatchSize:           10,
			PollInterval:        1 * time.Second,
			VisibilityTimeout:   30 * time.Second,
			MaxRetries:          3,
			RetryDelay:          1 * time.Second,
			WorkerTimeout:       60 * time.Second,
			EnableManagementAPI: true,
			ManagementEndpoints: ManagementEndpointsConfig{
				Health:  "/health",
				Status:  "/status",
				Metrics: "/metrics",
				Queue:   "/queue/status",
			},
		},
		Logging: LoggingConfig{
			Level:      getEnv("LOG_LEVEL", "info"),
			Format:     getEnv("LOG_FORMAT", "json"),
			Output:     getEnv("LOG_OUTPUT", "both"),
			FilePath:   getEnv("LOG_FILE_PATH", "./logs"),
			MaxSize:    100,
			MaxBackups: 5,
			MaxAge:     30,
			Compress:   true,
		},
	}

	// 配置验证
	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("配置验证失败: %w", err)
	}

	return config, nil
}

// loadFromFile 从JSON文件加载配置
func loadFromFile(filename string) (*Config, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %w", err)
	}

	var config Config
	if err := json.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %w", err)
	}

	// 配置验证
	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("配置验证失败: %w", err)
	}

	fmt.Printf("✅ 已从配置文件加载配置: %s\n", filename)
	return &config, nil
}

// Validate 验证配置
func (c *Config) Validate() error {
	if c.Server.Port == "" {
		return fmt.Errorf("服务器端口不能为空")
	}
	if c.Database.URL == "" {
		return fmt.Errorf("数据库URL不能为空")
	}
	if c.Redis.URL == "" {
		return fmt.Errorf("Redis URL不能为空")
	}
	if c.Redis.QueueName == "" {
		return fmt.Errorf("Redis队列名不能为空")
	}
	if c.MainSystem.URL == "" {
		return fmt.Errorf("主系统URL不能为空")
	}
	return nil
}

// GetMainSystemURL 获取主系统URL (兼容旧版本)
func (c *Config) GetMainSystemURL() string {
	return c.MainSystem.URL
}

// GetRedisQueueName 获取Redis队列名 (兼容旧版本)
func (c *Config) GetRedisQueueName() string {
	return c.Redis.QueueName
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
