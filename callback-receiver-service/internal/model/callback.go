package model

import "time"

// CallbackRawData 原始回调数据
type CallbackRawData struct {
	ID         string            `json:"id" db:"id"`
	Provider   string            `json:"provider" db:"provider"`
	RawBody    string            `json:"raw_body" db:"raw_body"`
	Headers    map[string]string `json:"headers" db:"headers"`
	ClientIP   string            `json:"client_ip" db:"client_ip"`
	Hash       string            `json:"hash" db:"hash"`
	ReceivedAt time.Time         `json:"received_at" db:"received_at"`
	Processed  bool              `json:"processed" db:"processed"`
	CreatedAt  time.Time         `json:"created_at" db:"created_at"`
}

// CallbackNotification 回调通知消息
type CallbackNotification struct {
	ID       string `json:"id"`
	Provider string `json:"provider"`
}

// CallbackResponse 回调响应
type CallbackResponse struct {
	Success   bool        `json:"success"`
	Message   string      `json:"message"`
	RequestID string      `json:"request_id,omitempty"`
	Data      interface{} `json:"data,omitempty"`
}
