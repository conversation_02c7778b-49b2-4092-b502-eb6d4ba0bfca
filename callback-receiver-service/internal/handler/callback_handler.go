package handler

import (
	"io"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"callback-receiver/internal/model"
	"callback-receiver/internal/service"
)

type CallbackHandler struct {
	service service.CallbackServiceInterface
	logger  *zap.Logger
}

func NewCallbackHandler(service service.CallbackServiceInterface, logger *zap.Logger) *CallbackHandler {
	return &CallbackHandler{
		service: service,
		logger:  logger,
	}
}

// ReceiveYuntong 接收云通回调
func (h *CallbackHandler) ReceiveYuntong(c *gin.Context) {
	h.receiveCallback(c, "yuntong")
}

// ReceiveYida 接收易达回调
func (h *CallbackHandler) ReceiveYida(c *gin.Context) {
	h.receiveCallback(c, "yida")
}

// ReceiveKuaidi100 接收快递100回调
func (h *CallbackHandler) ReceiveKuaidi100(c *gin.Context) {
	h.receiveCallback(c, "kuaidi100")
}

// ReceiveCainiao 接收菜鸟回调
func (h *CallbackHandler) ReceiveCainiao(c *gin.Context) {
	h.receiveCallback(c, "cainiao")
}

// ReceiveKuaidiniao 接收快递鸟回调
func (h *CallbackHandler) ReceiveKuaidiniao(c *gin.Context) {
	h.receiveCallback(c, "kuaidiniao")
}

// receiveCallback 通用回调接收处理
func (h *CallbackHandler) receiveCallback(c *gin.Context, provider string) {
	// 输入验证
	if provider == "" {
		h.logger.Error("供应商参数为空")
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "供应商参数无效",
		})
		return
	}

	// 读取请求体
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		h.logger.Error("读取请求体失败",
			zap.String("provider", provider),
			zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "读取请求体失败",
		})
		return
	}

	// 验证请求体不为空
	if len(body) == 0 {
		h.logger.Error("请求体为空",
			zap.String("provider", provider))
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求体不能为空",
		})
		return
	}

	// 获取请求头
	headers := make(map[string]string)
	for key, values := range c.Request.Header {
		if len(values) > 0 {
			headers[key] = values[0]
		}
	}

	// 创建回调数据
	callbackData := &model.CallbackRawData{
		Provider: provider,
		RawBody:  string(body),
		Headers:  headers,
		ClientIP: c.ClientIP(),
	}

	// 🔥 优化：记录详细的接收日志，包含关键业务信息
	h.logger.Info("📥 接收到供应商回调",
		zap.String("provider", provider),
		zap.String("client_ip", c.ClientIP()),
		zap.Int("body_size", len(body)),
		zap.String("user_agent", c.GetHeader("User-Agent")),
		zap.String("content_type", c.GetHeader("Content-Type")),
		zap.String("raw_body", string(body))) // 🔥 记录原始回调数据

	// 处理回调数据
	err = h.service.ReceiveCallback(c.Request.Context(), callbackData)
	if err != nil {
		h.logger.Error("❌ 回调处理失败",
			zap.String("provider", provider),
			zap.String("client_ip", c.ClientIP()),
			zap.String("raw_body", string(body)),
			zap.Error(err))

		// 即使处理失败，也返回成功，避免供应商重复发送
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "处理失败，但已记录",
		})
		return
	}

	// 返回成功响应
	h.logger.Info("✅ 回调处理成功",
		zap.String("provider", provider),
		zap.String("client_ip", c.ClientIP()))

	// 根据供应商返回期望的格式
	switch provider {
	case "yuntong":
		// 云通期望的响应格式
		c.JSON(http.StatusOK, gin.H{
			"EBusinessID": "",
			"UpdateTime":  "",
			"Success":     true,
			"Reason":      "",
		})
	case "yida":
		// 易达期望的响应格式
		c.JSON(http.StatusOK, gin.H{
			"msg":     "接收成功",
			"code":    "200",
			"success": true,
		})
	case "kuaidi100":
		// 快递100期望的响应格式
		c.JSON(http.StatusOK, gin.H{
			"result":     true,
			"returnCode": "200",
			"message":    "成功",
		})
	case "cainiao":
		// 菜鸟期望的响应格式
		c.JSON(http.StatusOK, gin.H{
			"result": gin.H{
				"statusMsg":  "成功",
				"data":       true,
				"success":    true,
				"statusCode": "200",
			},
		})
	case "kuaidiniao":
		// 🔥 修复：快递鸟官方文档要求的响应格式
		c.JSON(http.StatusOK, gin.H{
			"EBusinessID": "1778716",                                // 用户ID（必需）
			"UpdateTime":  time.Now().Format("2006-01-02 15:04:05"), // 更新时间（必需）
			"Success":     true,                                     // 成功与否（必需）
			"Reason":      "成功",                                     // 描述（必需）
		})
	default:
		// 通用响应格式
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "ok",
		})
	}
}
