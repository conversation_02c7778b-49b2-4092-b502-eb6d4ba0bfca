package service

import (
	"context"
	"errors"
	"testing"

	"callback-receiver/internal/model"

	"go.uber.org/zap"
)

// MockRepository 模拟仓储 (遵循依赖倒置原则)
type MockRepository struct {
	saveFunc            func(ctx context.Context, data *model.CallbackRawData) (string, error)
	markAsProcessedFunc func(ctx context.Context, id string) error
}

func (m *MockRepository) Save(ctx context.Context, data *model.CallbackRawData) (string, error) {
	if m.saveFunc != nil {
		return m.saveFunc(ctx, data)
	}
	return "test-id", nil
}

func (m *MockRepository) MarkAsProcessed(ctx context.Context, id string) error {
	if m.markAsProcessedFunc != nil {
		return m.markAsProcessedFunc(ctx, id)
	}
	return nil
}

func (m *MockRepository) GetByID(ctx context.Context, id string) (*model.CallbackRawData, error) {
	return nil, nil
}

func (m *MockRepository) GetUnprocessed(ctx context.Context, limit int) ([]*model.CallbackRawData, error) {
	return nil, nil
}

// MockNotifier 模拟通知器
type MockNotifier struct {
	notifyFunc func(ctx context.Context, notification *model.CallbackNotification) error
}

func (m *MockNotifier) Notify(ctx context.Context, notification *model.CallbackNotification) error {
	if m.notifyFunc != nil {
		return m.notifyFunc(ctx, notification)
	}
	return nil
}

// 创建测试用的日志器
func createTestLogger() *zap.Logger {
	logger, _ := zap.NewDevelopment()
	return logger
}

func TestCallbackService_ReceiveCallback_Success(t *testing.T) {
	// 准备测试数据
	mockRepo := &MockRepository{}
	mockNotifier := &MockNotifier{}
	logger := createTestLogger()

	service := &CallbackService{
		repo:     mockRepo,
		notifier: mockNotifier,
		logger:   logger,
	}

	data := &model.CallbackRawData{
		Provider: "yuntong",
		RawBody:  `{"test": "data"}`,
		Headers:  map[string]string{"Content-Type": "application/json"},
		ClientIP: "127.0.0.1",
	}

	// 执行测试
	err := service.ReceiveCallback(context.Background(), data)

	// 验证结果
	if err != nil {
		t.Errorf("期望成功，但得到错误: %v", err)
	}
}

func TestCallbackService_ReceiveCallback_ValidationError(t *testing.T) {
	service := &CallbackService{}

	tests := []struct {
		name string
		data *model.CallbackRawData
	}{
		{"空数据", nil},
		{"空供应商", &model.CallbackRawData{Provider: "", RawBody: "test"}},
		{"空内容", &model.CallbackRawData{Provider: "yuntong", RawBody: ""}},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := service.ReceiveCallback(context.Background(), tt.data)
			if err == nil {
				t.Errorf("期望验证错误，但没有得到错误")
			}
		})
	}
}

func TestCallbackService_ReceiveCallback_RepositoryError(t *testing.T) {
	mockRepo := &MockRepository{
		saveFunc: func(ctx context.Context, data *model.CallbackRawData) (string, error) {
			return "", errors.New("数据库错误")
		},
	}
	mockNotifier := &MockNotifier{}
	logger := createTestLogger()

	service := &CallbackService{
		repo:     mockRepo,
		notifier: mockNotifier,
		logger:   logger,
	}

	data := &model.CallbackRawData{
		Provider: "yuntong",
		RawBody:  `{"test": "data"}`,
		Headers:  map[string]string{},
		ClientIP: "127.0.0.1",
	}

	err := service.ReceiveCallback(context.Background(), data)
	if err == nil {
		t.Errorf("期望数据库错误，但没有得到错误")
	}
}
