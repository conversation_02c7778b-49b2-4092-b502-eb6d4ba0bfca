package service

import (
	"context"

	"callback-receiver/internal/model"
)

// CallbackServiceInterface 回调服务接口 (遵循依赖倒置原则)
type CallbackServiceInterface interface {
	// ReceiveCallback 接收并处理回调数据
	ReceiveCallback(ctx context.Context, data *model.CallbackRawData) error
}

// NotificationService 通知服务接口 (遵循依赖倒置原则)
type NotificationService interface {
	Notify(ctx context.Context, notification *model.CallbackNotification) error
}
