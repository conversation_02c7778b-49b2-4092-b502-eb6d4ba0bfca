package service

import (
	"context"
	"encoding/json"
	"fmt"
	"net/url"
	"strings"

	"go.uber.org/zap"

	"callback-receiver/internal/model"
	"callback-receiver/internal/repository"
)

type CallbackService struct {
	repo     repository.CallbackRepositoryInterface
	notifier NotificationService
	logger   *zap.Logger
}

func NewCallbackService(repo repository.CallbackRepositoryInterface, notifier NotificationService, logger *zap.Logger) CallbackServiceInterface {
	return &CallbackService{
		repo:     repo,
		notifier: notifier,
		logger:   logger,
	}
}

// ReceiveCallback 接收并处理回调数据
func (s *CallbackService) ReceiveCallback(ctx context.Context, data *model.CallbackRawData) error {
	// 输入验证 (遵循防御性编程原则)
	if data == nil {
		return fmt.Errorf("回调数据不能为空")
	}
	if data.Provider == "" {
		return fmt.Errorf("供应商不能为空")
	}
	if data.RawBody == "" {
		return fmt.Errorf("回调内容不能为空")
	}

	// 🔥 优化：解析回调数据获取关键业务信息
	orderInfo := s.extractOrderInfo(data)

	// 1. 存储原始数据到数据库
	id, err := s.repo.Save(ctx, data)
	if err != nil {
		s.logger.Error("❌ 保存回调数据失败",
			zap.String("provider", data.Provider),
			zap.String("client_ip", data.ClientIP),
			zap.String("order_info", orderInfo),
			zap.Error(err))
		return fmt.Errorf("保存回调数据失败: %w", err)
	}

	s.logger.Info("💾 回调数据已保存到数据库",
		zap.String("callback_id", id),
		zap.String("provider", data.Provider),
		zap.String("client_ip", data.ClientIP),
		zap.String("order_info", orderInfo),
		zap.Time("received_at", data.ReceivedAt))

	// 2. 发送通知到Redis队列
	notification := &model.CallbackNotification{
		ID:       id,
		Provider: data.Provider,
	}

	err = s.notifier.Notify(ctx, notification)
	if err != nil {
		s.logger.Error("❌ 发送Redis通知失败",
			zap.String("callback_id", id),
			zap.String("provider", data.Provider),
			zap.String("order_info", orderInfo),
			zap.Error(err))
		// 不返回错误，因为数据已经保存了，通知失败不影响核心功能
	} else {
		s.logger.Info("📤 Redis通知已发送",
			zap.String("callback_id", id),
			zap.String("provider", data.Provider),
			zap.String("order_info", orderInfo))
	}

	return nil
}

// extractOrderInfo 从回调数据中提取关键订单信息用于日志记录
func (s *CallbackService) extractOrderInfo(data *model.CallbackRawData) string {
	if data == nil || data.RawBody == "" {
		return "无数据"
	}

	// 根据不同供应商使用不同的解析方式
	switch data.Provider {
	case "kuaidi100":
		return s.extractKuaidi100InfoFromRaw(data.RawBody)
	case "yida":
		return s.extractYidaInfoFromRaw(data.RawBody)
	case "yuntong":
		return s.extractYuntongInfoFromRaw(data.RawBody)
	case "kuaidiniao":
		return s.extractKuaidiniaoInfoFromRaw(data.RawBody)
	default:
		return fmt.Sprintf("未知供应商: %s", data.RawBody[:min(200, len(data.RawBody))])
	}
}

// extractKuaidi100InfoFromRaw 从原始数据提取快递100回调信息
func (s *CallbackService) extractKuaidi100InfoFromRaw(rawBody string) string {
	// 尝试解析JSON获取订单信息
	var jsonData map[string]interface{}
	if err := json.Unmarshal([]byte(rawBody), &jsonData); err != nil {
		return fmt.Sprintf("解析失败: %s", rawBody[:min(100, len(rawBody))])
	}
	return s.extractKuaidi100Info(jsonData)
}

// extractYidaInfoFromRaw 从原始数据提取易达回调信息
func (s *CallbackService) extractYidaInfoFromRaw(rawBody string) string {
	// 尝试解析JSON获取订单信息
	var jsonData map[string]interface{}
	if err := json.Unmarshal([]byte(rawBody), &jsonData); err != nil {
		return fmt.Sprintf("解析失败: %s", rawBody[:min(100, len(rawBody))])
	}
	return s.extractYidaInfo(jsonData)
}

// extractYuntongInfoFromRaw 从原始数据提取云通回调信息
func (s *CallbackService) extractYuntongInfoFromRaw(rawBody string) string {
	// 尝试解析JSON获取订单信息
	var jsonData map[string]interface{}
	if err := json.Unmarshal([]byte(rawBody), &jsonData); err != nil {
		return fmt.Sprintf("解析失败: %s", rawBody[:min(100, len(rawBody))])
	}
	return s.extractYuntongInfo(jsonData)
}

// extractKuaidiniaoInfoFromRaw 从原始数据提取快递鸟回调信息
func (s *CallbackService) extractKuaidiniaoInfoFromRaw(rawBody string) string {
	// 🔥 修复：快递鸟回调数据是URL编码的表单数据，需要先解析表单
	if strings.Contains(rawBody, "RequestData=") {
		// URL编码的表单数据格式（快递鸟官方格式）
		return s.parseKuaidiniaoFormData(rawBody)
	} else {
		// JSON格式（测试数据）
		var jsonData map[string]interface{}
		if err := json.Unmarshal([]byte(rawBody), &jsonData); err != nil {
			return fmt.Sprintf("解析失败: %s", rawBody[:min(100, len(rawBody))])
		}
		return s.extractKuaidiniaoInfo(jsonData)
	}
}

// parseKuaidiniaoFormData 解析快递鸟URL编码的表单数据
func (s *CallbackService) parseKuaidiniaoFormData(rawBody string) string {
	// 解析URL编码的表单数据
	values, err := url.ParseQuery(rawBody)
	if err != nil {
		return fmt.Sprintf("表单解析失败: %s", rawBody[:min(100, len(rawBody))])
	}

	// 提取RequestData字段
	requestData := values.Get("RequestData")
	if requestData == "" {
		return "快递鸟回调(缺少RequestData)"
	}

	// 解析RequestData中的JSON数据
	var jsonData map[string]interface{}
	if err := json.Unmarshal([]byte(requestData), &jsonData); err != nil {
		return fmt.Sprintf("RequestData解析失败: %s", requestData[:min(100, len(requestData))])
	}

	// 提取关键信息
	var info []string

	// 添加请求类型
	if requestType := values.Get("RequestType"); requestType != "" {
		pushTypeText := s.getKuaidiniaoRequestTypeText(requestType)
		info = append(info, fmt.Sprintf("类型:%s(%s)", pushTypeText, requestType))
	}

	// 从Data数组中提取订单信息
	if dataArray, ok := jsonData["Data"].([]interface{}); ok && len(dataArray) > 0 {
		if orderData, ok := dataArray[0].(map[string]interface{}); ok {
			if orderNo, ok := orderData["OrderCode"].(string); ok {
				info = append(info, fmt.Sprintf("订单号:%s", orderNo))
			}
			if trackingNo, ok := orderData["LogisticCode"].(string); ok {
				info = append(info, fmt.Sprintf("运单号:%s", trackingNo))
			}
			if state, ok := orderData["State"].(string); ok {
				statusText := s.getKuaidiniaoStatusText(state)
				info = append(info, fmt.Sprintf("状态:%s(%s)", statusText, state))
			}
		}
	}

	if len(info) == 0 {
		return "快递鸟回调(无订单信息)"
	}
	return fmt.Sprintf("快递鸟: %s", strings.Join(info, ", "))
}

// extractKuaidi100Info 提取快递100回调信息
func (s *CallbackService) extractKuaidi100Info(data map[string]interface{}) string {
	var info []string

	if param, ok := data["param"].(string); ok {
		var paramData map[string]interface{}
		if err := json.Unmarshal([]byte(param), &paramData); err == nil {
			if orderNo, ok := paramData["orderId"].(string); ok {
				info = append(info, fmt.Sprintf("订单号:%s", orderNo))
			}
			if trackingNo, ok := paramData["kuaidinum"].(string); ok {
				info = append(info, fmt.Sprintf("运单号:%s", trackingNo))
			}
			if status, ok := paramData["status"].(float64); ok {
				statusText := s.getKuaidi100StatusText(int(status))
				info = append(info, fmt.Sprintf("状态:%s(%d)", statusText, int(status)))
			}
		}
	}

	if len(info) == 0 {
		return "快递100回调(无订单信息)"
	}
	return fmt.Sprintf("快递100: %s", strings.Join(info, ", "))
}

// extractYidaInfo 提取易达回调信息
func (s *CallbackService) extractYidaInfo(data map[string]interface{}) string {
	var info []string

	if orderNo, ok := data["orderNo"].(string); ok {
		info = append(info, fmt.Sprintf("订单号:%s", orderNo))
	}
	if trackingNo, ok := data["deliveryId"].(string); ok {
		info = append(info, fmt.Sprintf("运单号:%s", trackingNo))
	}
	if status, ok := data["status"].(float64); ok {
		statusText := s.getYidaStatusText(int(status))
		info = append(info, fmt.Sprintf("状态:%s(%d)", statusText, int(status)))
	}

	if len(info) == 0 {
		return "易达回调(无订单信息)"
	}
	return fmt.Sprintf("易达: %s", strings.Join(info, ", "))
}

// extractYuntongInfo 提取云通回调信息
func (s *CallbackService) extractYuntongInfo(data map[string]interface{}) string {
	var info []string

	if orderNo, ok := data["OrderCode"].(string); ok {
		info = append(info, fmt.Sprintf("订单号:%s", orderNo))
	}
	if trackingNo, ok := data["LogisticCode"].(string); ok {
		info = append(info, fmt.Sprintf("运单号:%s", trackingNo))
	}
	if state, ok := data["State"].(float64); ok {
		statusText := s.getYuntongStatusText(int(state))
		info = append(info, fmt.Sprintf("状态:%s(%d)", statusText, int(state)))
	}

	if len(info) == 0 {
		return "云通回调(无订单信息)"
	}
	return fmt.Sprintf("云通: %s", strings.Join(info, ", "))
}

// extractKuaidiniaoInfo 提取快递鸟回调信息
func (s *CallbackService) extractKuaidiniaoInfo(data map[string]interface{}) string {
	var info []string

	if orderNo, ok := data["OrderCode"].(string); ok {
		info = append(info, fmt.Sprintf("订单号:%s", orderNo))
	}
	if trackingNo, ok := data["LogisticCode"].(string); ok {
		info = append(info, fmt.Sprintf("运单号:%s", trackingNo))
	}
	if state, ok := data["State"].(string); ok {
		statusText := s.getKuaidiniaoStatusText(state)
		info = append(info, fmt.Sprintf("状态:%s(%s)", statusText, state))
	}
	if pushType, ok := data["PushType"].(float64); ok {
		pushTypeText := s.getKuaidiniaoPushTypeText(int(pushType))
		info = append(info, fmt.Sprintf("推送类型:%s(%d)", pushTypeText, int(pushType)))
	}

	if len(info) == 0 {
		return "快递鸟回调(无订单信息)"
	}
	return fmt.Sprintf("快递鸟: %s", strings.Join(info, ", "))
}

// getKuaidi100StatusText 获取快递100状态文本
func (s *CallbackService) getKuaidi100StatusText(status int) string {
	statusMap := map[int]string{
		0: "暂无轨迹",
		1: "已揽收",
		2: "在途中",
		3: "已签收",
		4: "问题件",
		5: "疑难件",
		6: "退件签收",
	}
	if text, ok := statusMap[status]; ok {
		return text
	}
	return "未知状态"
}

// getYidaStatusText 获取易达状态文本
func (s *CallbackService) getYidaStatusText(status int) string {
	statusMap := map[int]string{
		1: "已分配",
		2: "已揽收",
		3: "运输中",
		4: "派送中",
		5: "已签收",
		6: "异常",
		7: "拒收",
	}
	if text, ok := statusMap[status]; ok {
		return text
	}
	return "未知状态"
}

// getYuntongStatusText 获取云通状态文本
func (s *CallbackService) getYuntongStatusText(state int) string {
	statusMap := map[int]string{
		0: "暂无轨迹",
		1: "已揽收",
		2: "在途中",
		3: "已签收",
		4: "问题件",
	}
	if text, ok := statusMap[state]; ok {
		return text
	}
	return "未知状态"
}

// getKuaidiniaoStatusText 获取快递鸟状态文本
func (s *CallbackService) getKuaidiniaoStatusText(state string) string {
	statusMap := map[string]string{
		"0": "暂无物流信息",
		"1": "已揽收",
		"2": "在途中",
		"3": "已签收",
		"4": "异常",
	}
	if text, ok := statusMap[state]; ok {
		return text
	}
	return "未知状态"
}

// getKuaidiniaoPushTypeText 获取快递鸟推送类型文本
func (s *CallbackService) getKuaidiniaoPushTypeText(pushType int) string {
	pushTypeMap := map[int]string{
		1: "状态推送",
		2: "计费推送",
		3: "揽收推送",
		4: "异常推送",
		5: "订单变更推送",
	}
	if text, ok := pushTypeMap[pushType]; ok {
		return text
	}
	return "未知推送类型"
}

// getKuaidiniaoRequestTypeText 获取快递鸟请求类型文本
func (s *CallbackService) getKuaidiniaoRequestTypeText(requestType string) string {
	requestTypeMap := map[string]string{
		"103":  "物流轨迹推送",
		"104":  "计费推送",
		"1801": "下单结果推送",
		"1802": "取消订单推送",
		"1803": "订单状态推送",
	}
	if text, ok := requestTypeMap[requestType]; ok {
		return text
	}
	return "未知请求类型"
}

// min 辅助函数
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// RedisNotifier Redis通知器实现
type RedisNotifier struct {
	client    RedisClient
	queueName string
}

type RedisClient interface {
	LPush(ctx context.Context, key string, values ...interface{}) error
}

func NewRedisNotifier(client RedisClient, queueName string) NotificationService {
	return &RedisNotifier{
		client:    client,
		queueName: queueName,
	}
}

// Notify 发送通知到Redis队列
func (n *RedisNotifier) Notify(ctx context.Context, notification *model.CallbackNotification) error {
	data, err := json.Marshal(notification)
	if err != nil {
		return fmt.Errorf("序列化通知消息失败: %w", err)
	}

	if err := n.client.LPush(ctx, n.queueName, string(data)); err != nil {
		return fmt.Errorf("发送通知到队列失败: %w", err)
	}

	return nil
}
