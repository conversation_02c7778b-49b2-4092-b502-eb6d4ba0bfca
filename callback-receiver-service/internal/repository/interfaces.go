package repository

import (
	"context"

	"callback-receiver/internal/model"
)

// CallbackRepositoryInterface 回调数据仓储接口 (遵循依赖倒置原则)
type CallbackRepositoryInterface interface {
	// Save 保存原始回调数据
	Save(ctx context.Context, data *model.CallbackRawData) (string, error)

	// MarkAsProcessed 标记为已处理
	MarkAsProcessed(ctx context.Context, id string) error

	// GetByID 根据ID获取回调数据
	GetByID(ctx context.Context, id string) (*model.CallbackRawData, error)

	// GetUnprocessed 获取未处理的回调数据
	GetUnprocessed(ctx context.Context, limit int) ([]*model.CallbackRawData, error)
}
