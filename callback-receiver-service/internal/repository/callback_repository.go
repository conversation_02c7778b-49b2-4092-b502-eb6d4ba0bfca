package repository

import (
	"context"
	"database/sql"
	"encoding/json"
	"time"

	"github.com/google/uuid"

	"callback-receiver/internal/model"
)

type CallbackRepository struct {
	db *sql.DB
}

func NewCallbackRepository(db *sql.DB) CallbackRepositoryInterface {
	return &CallbackRepository{db: db}
}

// Save 保存原始回调数据
func (r *CallbackRepository) Save(ctx context.Context, data *model.CallbackRawData) (string, error) {
	id := uuid.New().String()
	now := time.Now()

	// 序列化headers
	headersJSON, err := json.Marshal(data.Headers)
	if err != nil {
		return "", err
	}

	// 生成简单的hash (使用UUID作为hash)
	hash := id

	query := `
		INSERT INTO callback_raw_data (
			id, provider, raw_body, headers, client_ip, hash,
			received_at, processed, created_at
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
	`

	_, err = r.db.ExecContext(ctx, query,
		id,
		data.Provider,
		data.RawBody,
		headersJSON,
		data.ClientIP,
		hash,
		now,
		false,
		now,
	)

	if err != nil {
		return "", err
	}

	return id, nil
}

// MarkAsProcessed 标记为已处理
func (r *CallbackRepository) MarkAsProcessed(ctx context.Context, id string) error {
	query := `UPDATE callback_raw_data SET processed = true WHERE id = $1`
	_, err := r.db.ExecContext(ctx, query, id)
	return err
}

// GetByID 根据ID获取回调数据
func (r *CallbackRepository) GetByID(ctx context.Context, id string) (*model.CallbackRawData, error) {
	query := `
		SELECT id, provider, raw_body, headers, client_ip, 
		       received_at, processed, created_at
		FROM callback_raw_data 
		WHERE id = $1
	`

	row := r.db.QueryRowContext(ctx, query, id)

	var data model.CallbackRawData
	var headersJSON []byte

	err := row.Scan(
		&data.ID,
		&data.Provider,
		&data.RawBody,
		&headersJSON,
		&data.ClientIP,
		&data.ReceivedAt,
		&data.Processed,
		&data.CreatedAt,
	)

	if err != nil {
		return nil, err
	}

	// 反序列化headers
	err = json.Unmarshal(headersJSON, &data.Headers)
	if err != nil {
		return nil, err
	}

	return &data, nil
}

// GetUnprocessed 获取未处理的回调数据
func (r *CallbackRepository) GetUnprocessed(ctx context.Context, limit int) ([]*model.CallbackRawData, error) {
	query := `
		SELECT id, provider, raw_body, headers, client_ip, 
		       received_at, processed, created_at
		FROM callback_raw_data 
		WHERE processed = false
		ORDER BY received_at ASC
		LIMIT $1
	`

	rows, err := r.db.QueryContext(ctx, query, limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var results []*model.CallbackRawData

	for rows.Next() {
		var data model.CallbackRawData
		var headersJSON []byte

		err := rows.Scan(
			&data.ID,
			&data.Provider,
			&data.RawBody,
			&headersJSON,
			&data.ClientIP,
			&data.ReceivedAt,
			&data.Processed,
			&data.CreatedAt,
		)

		if err != nil {
			return nil, err
		}

		// 反序列化headers
		err = json.Unmarshal(headersJSON, &data.Headers)
		if err != nil {
			return nil, err
		}

		results = append(results, &data)
	}

	return results, nil
}
