{"service": {"name": "callback-receiver-service", "version": "1.0.0", "environment": "production"}, "server": {"port": "8082", "timeout": **********, "read_timeout": **********0, "write_timeout": **********0, "idle_timeout": 60000000000}, "database": {"url": "*************************************************/callback_receiver?sslmode=disable", "max_open_conns": 25, "max_idle_conns": 10, "conn_max_lifetime": 300, "conn_max_idle_time": 60}, "redis": {"url": "redis://default:a63006320@*************:6379", "queue_name": "callback_notifications", "delayed_queue_name": "callback_notifications_delayed", "db": 0, "pool_size": 10, "min_idle_conns": 2, "max_conn_age": 300, "pool_timeout": 30, "idle_timeout": 300, "dial_timeout": **********, "read_timeout": **********, "write_timeout": **********}, "main_system": {"url": "http://localhost:8081", "timeout": **********0, "retry_attempts": 3, "retry_delay": **********, "max_idle_conns": 100, "max_idle_conns_per_host": 10, "idle_conn_timeout": 90000000000}, "consumer": {"worker_count": 5, "batch_size": 10, "poll_interval": **********, "visibility_timeout": **********0, "max_retries": 3, "retry_delay": **********, "worker_timeout": 60000000000}, "providers": {"kuaidi100": {"enabled": true, "endpoint": "/webhook/kuaidi100", "api_endpoint": "/api/v1/callbacks/kuaidi100", "status_field": "status", "content_field": "content", "committer_field": "committer", "attachment_field": "attach", "work_order_id_field": "workorderId", "response_format": {"result": true, "returnCode": "200", "message": "成功"}}, "yida": {"enabled": true, "endpoint": "/webhook/yida", "api_endpoint": "/api/v1/callbacks/yida", "status_field": "status", "content_field": "reply", "committer_field": "operator", "work_order_id_field": "workOrderId", "response_format": {"msg": "接收成功", "code": "200", "success": true}}, "yuntong": {"enabled": true, "endpoint": "/webhook/yuntong", "api_endpoint": "/api/v1/callbacks/yuntong", "status_field": "Status", "content_field": "Content", "committer_field": "Operator", "work_order_id_field": "WorkOrderId", "response_format": {"EBusinessID": "", "UpdateTime": "", "Success": true, "Reason": ""}}, "cainiao": {"enabled": true, "endpoint": "/webhook/cainiao", "api_endpoint": "/api/v1/callbacks/cainiao", "status_field": "status", "content_field": "content", "committer_field": "operator", "work_order_id_field": "workOrderId", "response_format": {"success": true, "message": "ok", "code": "200"}}, "kuaidiniao": {"enabled": true, "endpoint": "/webhook/kuaidiniao", "api_endpoint": "/api/v1/callbacks/kuaidiniao", "status_field": "State", "content_field": "Content", "committer_field": "Operator", "work_order_id_field": "OrderCode", "response_format": {"Result": true, "ResultCode": "100", "Reason": "成功"}}}, "workorder_callback": {"enabled": true, "identification_fields": ["workorderId", "workOrderId", "WorkOrderId", "work_order_id", "workorder_id", "complaintId", "ticketId", "ComplaintNumber"], "provider_endpoints": {"kuaidi100": "/api/v1/callbacks/workorders/kuaidi100", "yida": "/api/v1/callbacks/workorders/yida", "yuntong": "/api/v1/callbacks/workorders/yuntong", "cainiao": "/api/v1/callbacks/workorders/cainiao", "kuaidiniao": "/api/v1/callbacks/workorders/kuaidiniao", "default": "/api/v1/callbacks/workorders/{provider}"}}, "security": {"api_key_validation": {"enabled": false, "header_name": "X-API-Key", "required_scopes": ["callback:write"]}, "signature_validation": {"enabled": false, "algorithm": "HMAC-SHA256", "header_name": "X-Signature", "secret_key_env": "CALLBACK_SECRET_KEY"}, "ip_whitelist": {"enabled": false, "allowed_ips": []}}, "monitoring": {"enabled": true, "metrics_port": 9090, "health_check_port": 8080, "health_check_interval": **********0, "health_check_timeout": **********0, "endpoints": [{"name": "database", "type": "postgres", "config": "database"}, {"name": "redis", "type": "redis", "config": "redis"}, {"name": "main_system", "type": "http", "url": "http://localhost:8081/health"}]}, "logging": {"level": "info", "format": "json", "output": "both", "file_path": "./logs", "max_size": 100, "max_backups": 5, "max_age": 30, "compress": true, "fields": {"service": "callback-receiver", "component": "main"}, "sensitive_fields": ["password", "token", "secret", "key"]}, "performance": {"concurrency": {"max_workers": 10, "queue_buffer_size": 1000, "worker_timeout": 60000000000}, "cache": {"enabled": true, "ttl": **********00, "max_size": 1000}}, "recovery": {"failed_message_recovery": {"enabled": true, "check_interval": 60000000000, "max_age": **********00, "batch_size": 10}, "dead_letter_queue": {"enabled": true, "queue_name": "callback_dlq", "max_retries": 5, "retry_delay": **********0}}, "development": {"debug": false, "profiling": false, "mock_main_system": false, "testing": {"enabled": false, "mock_providers": ["test_provider"], "test_data_path": "./testdata"}}}