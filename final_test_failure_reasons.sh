#!/bin/bash

echo "🎉 最终测试：缓存失败原因功能"
echo "=================================="

echo ""
echo "📊 测试有失败记录的缓存路线:"
echo "路线: 湖南省->陕西省, kuaidi100, STO, 2kg"

# 测试有失败记录的缓存路线
curl -s "http://localhost:8081/api/v1/weight-cache/details?provider=kuaidi100&express_code=STO&from_province=湖南省&to_province=陕西省&weight_kg=2&page=1&page_size=5" | python3 -c "
import json
import sys
try:
    data = json.load(sys.stdin)
    if data.get('success'):
        routes = data.get('data', {}).get('routes', [])
        print(f'✅ 找到 {len(routes)} 条缓存记录')
        for route in routes:
            print(f'📍 路线: {route.get(\"route\", \"N/A\")}')
            print(f'⚖️  重量: {route.get(\"weight_kg\", \"N/A\")}kg')
            print(f'💰 价格: ¥{route.get(\"price\", \"N/A\")}')
            print(f'❌ 失败次数: {route.get(\"failed_queries\", 0)}')
            print(f'📊 总查询次数: {route.get(\"total_queries\", 0)}')
            
            failure_reasons = route.get('failure_reasons', [])
            if failure_reasons:
                print('🚨 失败原因:')
                for i, reason in enumerate(failure_reasons, 1):
                    print(f'   {i}. {reason.get(\"error_message\", \"N/A\")}')
                    print(f'      出现次数: {reason.get(\"count\", 0)}')
                    print(f'      最后出现: {reason.get(\"last_occurred\", \"N/A\")}')
                    print(f'      来源: {reason.get(\"source\", \"N/A\")}')
                    print()
            else:
                print('✅ 无失败原因记录')
            print('---')
    else:
        print(f'❌ API调用失败: {data.get(\"message\", \"未知错误\")}')
except Exception as e:
    print(f'❌ 解析JSON失败: {e}')
"

echo ""
echo "📊 对比测试：无失败记录的缓存路线:"
echo "路线: 上海市->北京市, yuntong, JT, 5kg"

# 测试无失败记录的缓存路线
curl -s "http://localhost:8081/api/v1/weight-cache/details?provider=yuntong&express_code=JT&from_province=上海市&to_province=北京市&weight_kg=5&page=1&page_size=5" | python3 -c "
import json
import sys
try:
    data = json.load(sys.stdin)
    if data.get('success'):
        routes = data.get('data', {}).get('routes', [])
        print(f'✅ 找到 {len(routes)} 条缓存记录')
        for route in routes:
            print(f'📍 路线: {route.get(\"route\", \"N/A\")}')
            print(f'⚖️  重量: {route.get(\"weight_kg\", \"N/A\")}kg')
            print(f'💰 价格: ¥{route.get(\"price\", \"N/A\")}')
            print(f'❌ 失败次数: {route.get(\"failed_queries\", 0)}')
            print(f'📊 总查询次数: {route.get(\"total_queries\", 0)}')
            
            failure_reasons = route.get('failure_reasons', [])
            if failure_reasons:
                print('🚨 失败原因:')
                for i, reason in enumerate(failure_reasons, 1):
                    print(f'   {i}. {reason.get(\"error_message\", \"N/A\")}')
                    print(f'      出现次数: {reason.get(\"count\", 0)}')
                    print(f'      最后出现: {reason.get(\"last_occurred\", \"N/A\")}')
                    print(f'      来源: {reason.get(\"source\", \"N/A\")}')
                    print()
            else:
                print('✅ 无失败原因记录')
            print('---')
    else:
        print(f'❌ API调用失败: {data.get(\"message\", \"未知错误\")}')
except Exception as e:
    print(f'❌ 解析JSON失败: {e}')
"

echo ""
echo "🎯 功能测试完成！"
