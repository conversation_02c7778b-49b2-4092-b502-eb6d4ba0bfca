#!/bin/bash

# 订单费用差异检查工具部署脚本
# 版本: 1.0.0
# 作者: Go Kuaidi Team
# 日期: 2025-01-29

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
PROJECT_NAME="go-kuaidi"
TOOL_NAME="billing-checker"
BUILD_DIR="./build"
DEPLOY_DIR="/opt/kuaidi/tools"
LOG_DIR="/var/log/kuaidi"
CONFIG_DIR="/etc/kuaidi"
SERVICE_NAME="billing-checker"

# 函数：打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 函数：检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        print_error "命令 $1 未找到，请先安装"
        exit 1
    fi
}

# 函数：检查Go环境
check_go_environment() {
    print_info "检查Go环境..."
    check_command "go"
    
    GO_VERSION=$(go version | awk '{print $3}' | sed 's/go//')
    print_info "Go版本: $GO_VERSION"
    
    # 检查Go版本是否满足要求（1.23.0+）
    if [[ $(echo "$GO_VERSION 1.23.0" | tr " " "\n" | sort -V | head -n1) != "1.23.0" ]]; then
        print_warning "建议使用Go 1.23.0或更高版本"
    fi
}

# 函数：创建必要的目录
create_directories() {
    print_info "创建部署目录..."
    
    sudo mkdir -p "$DEPLOY_DIR"
    sudo mkdir -p "$LOG_DIR"
    sudo mkdir -p "$CONFIG_DIR"
    sudo mkdir -p "$BUILD_DIR"
    
    # 设置目录权限
    sudo chown -R $USER:$USER "$BUILD_DIR"
    sudo chmod 755 "$DEPLOY_DIR"
    sudo chmod 755 "$LOG_DIR"
    sudo chmod 755 "$CONFIG_DIR"
    
    print_success "目录创建完成"
}

# 函数：构建应用程序
build_application() {
    print_info "构建应用程序..."
    
    # 清理之前的构建
    rm -rf "$BUILD_DIR"
    mkdir -p "$BUILD_DIR"
    
    # 设置构建环境变量
    export CGO_ENABLED=0
    export GOOS=linux
    export GOARCH=amd64
    
    # 构建应用程序
    go build -a -installsuffix cgo -ldflags="-w -s" -o "$BUILD_DIR/$TOOL_NAME" "./cmd/$TOOL_NAME"
    
    if [ $? -eq 0 ]; then
        print_success "应用程序构建成功"
    else
        print_error "应用程序构建失败"
        exit 1
    fi
    
    # 显示构建信息
    ls -lh "$BUILD_DIR/$TOOL_NAME"
}

# 函数：运行测试
run_tests() {
    print_info "运行单元测试..."
    
    go test ./internal/service -v -cover
    
    if [ $? -eq 0 ]; then
        print_success "所有测试通过"
    else
        print_error "测试失败"
        exit 1
    fi
}

# 函数：部署应用程序
deploy_application() {
    print_info "部署应用程序..."
    
    # 停止现有服务（如果存在）
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        print_info "停止现有服务..."
        sudo systemctl stop "$SERVICE_NAME"
    fi
    
    # 复制二进制文件
    sudo cp "$BUILD_DIR/$TOOL_NAME" "$DEPLOY_DIR/"
    sudo chmod +x "$DEPLOY_DIR/$TOOL_NAME"
    
    # 创建符号链接到系统PATH
    sudo ln -sf "$DEPLOY_DIR/$TOOL_NAME" "/usr/local/bin/$TOOL_NAME"
    
    print_success "应用程序部署完成"
}

# 函数：创建配置文件
create_config() {
    print_info "创建配置文件..."
    
    CONFIG_FILE="$CONFIG_DIR/$TOOL_NAME.yaml"
    
    if [ ! -f "$CONFIG_FILE" ]; then
        sudo tee "$CONFIG_FILE" > /dev/null <<EOF
# 订单费用差异检查工具配置文件
database:
  connection_string: "*************************************************/go_kuaidi"
  max_open_conns: 25
  max_idle_conns: 5
  conn_max_lifetime: 300

billing_checker:
  default_batch_size: 100
  default_workers: 5
  default_timeout: "30m"
  threshold_amount: 0.01

logging:
  level: "info"
  format: "json"
  output: "$LOG_DIR/$TOOL_NAME.log"

monitoring:
  enabled: true
  prometheus_port: 9090
EOF
        print_success "配置文件创建完成: $CONFIG_FILE"
    else
        print_warning "配置文件已存在: $CONFIG_FILE"
    fi
}

# 函数：创建systemd服务文件
create_systemd_service() {
    print_info "创建systemd服务文件..."
    
    SERVICE_FILE="/etc/systemd/system/$SERVICE_NAME.service"
    
    sudo tee "$SERVICE_FILE" > /dev/null <<EOF
[Unit]
Description=订单费用差异检查工具
Documentation=https://github.com/your-org/go-kuaidi
After=network.target postgresql.service

[Service]
Type=oneshot
User=kuaidi
Group=kuaidi
WorkingDirectory=$DEPLOY_DIR
ExecStart=$DEPLOY_DIR/$TOOL_NAME -config=$CONFIG_DIR/$TOOL_NAME.yaml -days=1
StandardOutput=journal
StandardError=journal
SyslogIdentifier=$SERVICE_NAME

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=$LOG_DIR

[Install]
WantedBy=multi-user.target
EOF
    
    # 重新加载systemd配置
    sudo systemctl daemon-reload
    
    print_success "systemd服务文件创建完成"
}

# 函数：创建定时任务
create_cron_job() {
    print_info "创建定时任务..."
    
    CRON_FILE="/etc/cron.d/$SERVICE_NAME"
    
    sudo tee "$CRON_FILE" > /dev/null <<EOF
# 订单费用差异检查定时任务
# 每天凌晨2点执行检查
0 2 * * * kuaidi $DEPLOY_DIR/$TOOL_NAME -config=$CONFIG_DIR/$TOOL_NAME.yaml -days=1 -output=$LOG_DIR/daily-check.log

# 每周日凌晨3点执行完整检查
0 3 * * 0 kuaidi $DEPLOY_DIR/$TOOL_NAME -config=$CONFIG_DIR/$TOOL_NAME.yaml -days=7 -output=$LOG_DIR/weekly-check.log
EOF
    
    print_success "定时任务创建完成"
}

# 函数：创建用户和组
create_user() {
    print_info "创建系统用户..."
    
    if ! id "kuaidi" &>/dev/null; then
        sudo useradd -r -s /bin/false -d /nonexistent kuaidi
        print_success "用户kuaidi创建完成"
    else
        print_warning "用户kuaidi已存在"
    fi
    
    # 设置目录权限
    sudo chown -R kuaidi:kuaidi "$LOG_DIR"
}

# 函数：验证部署
verify_deployment() {
    print_info "验证部署..."
    
    # 检查二进制文件
    if [ -x "$DEPLOY_DIR/$TOOL_NAME" ]; then
        print_success "二进制文件部署成功"
    else
        print_error "二进制文件部署失败"
        exit 1
    fi
    
    # 检查配置文件
    if [ -f "$CONFIG_DIR/$TOOL_NAME.yaml" ]; then
        print_success "配置文件存在"
    else
        print_error "配置文件不存在"
        exit 1
    fi
    
    # 测试运行
    print_info "测试运行..."
    "$DEPLOY_DIR/$TOOL_NAME" -help > /dev/null 2>&1
    if [ $? -eq 0 ]; then
        print_success "应用程序可以正常运行"
    else
        print_error "应用程序运行测试失败"
        exit 1
    fi
}

# 函数：显示部署信息
show_deployment_info() {
    print_success "部署完成！"
    echo
    echo "部署信息:"
    echo "  应用程序: $DEPLOY_DIR/$TOOL_NAME"
    echo "  配置文件: $CONFIG_DIR/$TOOL_NAME.yaml"
    echo "  日志目录: $LOG_DIR"
    echo "  系统服务: $SERVICE_NAME"
    echo
    echo "使用方法:"
    echo "  手动运行: $TOOL_NAME -help"
    echo "  启动服务: sudo systemctl start $SERVICE_NAME"
    echo "  查看日志: sudo journalctl -u $SERVICE_NAME -f"
    echo "  查看定时任务: sudo crontab -l -u kuaidi"
    echo
    echo "监控地址:"
    echo "  Prometheus: http://localhost:9090"
    echo "  Grafana: http://localhost:3000 (admin/admin123)"
}

# 主函数
main() {
    print_info "开始部署订单费用差异检查工具..."
    
    # 检查是否为root用户
    if [ "$EUID" -eq 0 ]; then
        print_error "请不要使用root用户运行此脚本"
        exit 1
    fi
    
    # 执行部署步骤
    check_go_environment
    create_directories
    run_tests
    build_application
    create_user
    deploy_application
    create_config
    create_systemd_service
    create_cron_job
    verify_deployment
    show_deployment_info
    
    print_success "部署完成！"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
