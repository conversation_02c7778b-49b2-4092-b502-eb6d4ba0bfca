# Go-Kuaidi 日志轮转配置
# 适用于 logrotate 工具

/Users/<USER>/Desktop/go-kuaidi-*********/logs/*.log {
    # 每日轮转
    daily
    
    # 保留30天的日志
    rotate 30
    
    # 压缩旧日志
    compress
    
    # 延迟压缩（下次轮转时压缩）
    delaycompress
    
    # 如果日志文件不存在，不报错
    missingok
    
    # 不轮转空文件
    notifempty
    
    # 创建新文件的权限
    create 644 root root
    
    # 轮转后执行的脚本
    postrotate
        # 发送USR1信号给应用程序重新打开日志文件
        # 如果应用程序支持的话
        /bin/kill -USR1 $(cat /var/run/go-kuaidi.pid 2>/dev/null) 2>/dev/null || true
    endscript
}

# 错误日志特殊处理
/Users/<USER>/Desktop/go-kuaidi-*********/logs/error.log {
    daily
    rotate 90
    compress
    delaycompress
    missingok
    notifempty
    create 644 root root
    
    # 错误日志轮转后发送通知
    postrotate
        echo "错误日志已轮转: $(date)" >> /Users/<USER>/Desktop/go-kuaidi-*********/logs/rotate.log
    endscript
}

# 访问日志配置
/Users/<USER>/Desktop/go-kuaidi-*********/logs/access.log {
    daily
    rotate 7
    compress
    delaycompress
    missingok
    notifempty
    create 644 root root
}

# 审计日志配置（保留更长时间）
/Users/<USER>/Desktop/go-kuaidi-*********/logs/audit.log {
    weekly
    rotate 52
    compress
    delaycompress
    missingok
    notifempty
    create 644 root root
}
