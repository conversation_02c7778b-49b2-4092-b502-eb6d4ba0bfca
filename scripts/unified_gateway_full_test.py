#!/usr/bin/env python3
"""Go-Kuaidi 统一网关全功能自动化测试脚本（取消订单最后执行）

使用方法：
    python unified_gateway_full_test.py \
        --base http://127.0.0.1:8081 \
        --client_id <CLIENT_ID> \
        --client_secret <CLIENT_SECRET> \
        --user_id <USER_ID> \
        --username fulltester \
        --password Password123!

依赖：requests
"""
from __future__ import annotations

import argparse
import base64
import hashlib
import hmac
import json
import sys
import time
import urllib.parse
from typing import Any, Dict, List

import requests

SEPS = (", ", ": ")


def json_dumps(data: Any) -> str:
    return json.dumps(data, ensure_ascii=False, separators=SEPS)


def sign_request(client_id: str, client_secret: str, body_no_sign: str, ts: str) -> str:
    params = {
        "client_id": client_id,
        "nonce": ts,
        "path": urllib.parse.quote("/api/gateway/execute", safe=""),
        "timestamp": ts,
    }
    param_str = "&".join(f"{k}={params[k]}" for k in sorted(params))
    string_to_sign = f"{param_str}&body=" + base64.b64encode(body_no_sign.encode()).decode()
    return base64.b64encode(
        hmac.new(client_secret.encode(), string_to_sign.encode(), hashlib.sha256).digest()
    ).decode()


def build_payload(client_id: str, client_secret: str, api_method: str, biz: Dict[str, Any]) -> str:
    ts = str(int(time.time()))
    body = {
        "clientType": "api",
        "username": client_id,
        "timestamp": ts,
        "apiMethod": api_method,
        "businessParams": biz,
    }
    body_str = json_dumps(body)
    body["sign"] = sign_request(client_id, client_secret, body_str, ts)
    return json_dumps(body)


class GatewayTester:
    def __init__(self, base: str, client_id: str, client_secret: str, session: requests.Session):
        self.base = base.rstrip("/")
        self.client_id = client_id
        self.client_secret = client_secret
        self.session = session

    def call(self, api_method: str, biz: Dict[str, Any]):
        # 避免同一秒重复 timestamp→nonce，确保请求唯一
        time.sleep(1)
        payload = build_payload(self.client_id, self.client_secret, api_method, biz)
        r = self.session.post(
            self.base + "/api/gateway/execute",
            data=payload.encode(),
            headers={"Content-Type": "application/json; charset=utf-8"},
            timeout=30,
        )
        try:
            j = r.json()
        except json.JSONDecodeError:
            print("响应非 JSON:", r.text)
            r.raise_for_status()
        if not j.get("success", False):
            raise RuntimeError(f"{api_method} 失败: {j}")
        return j["data"]


# ---------- CLI workflow ----------

def main():
    parser = argparse.ArgumentParser(description="Go-Kuaidi 统一网关全链路测试")
    parser.add_argument("--base", required=True)
    parser.add_argument("--client_id", required=True)
    parser.add_argument("--client_secret", required=True)
    parser.add_argument("--user_id", required=True)
    parser.add_argument("--username", help="后台测试账号用户名，可选")
    parser.add_argument("--password", help="后台测试账号密码，可选")
    args = parser.parse_args()

    sess = requests.Session()
    # 若提供了用户名密码，可登录做额外调试；否则直接跳过
    if args.username and args.password:
        login_resp = sess.post(
            f"{args.base}/api/v1/auth/login",
            json={"username": args.username, "password": args.password},
            timeout=15,
        ).json()
        if login_resp.get("success", False):
            access = login_resp.get("access_token") or login_resp.get("data", {}).get("access_token")
            if access:
                sess.headers.update({"Authorization": "Bearer " + access})
                print("(可选) 已登录后台，token 截断:", access[:20], "…")
        else:
            print("(可选) 后台登录失败，继续执行网关测试 …")

    tester = GatewayTester(args.base, args.client_id, args.client_secret, sess)

    print("1) 查询价格 ...")
    price_data = tester.call(
        "QUERY_PRICE",
        {
            "from_province": "广东省",
            "from_city": "广州市",
            "to_province": "湖北省",
            "to_city": "武汉市",
            "weight": 1.2,
        },
    )["data"]
    order_code = price_data[0]["order_code"]
    sel_price = float(price_data[0]["price"])
    print("   获取报价", sel_price, "元, order_code=", order_code[:60], "…")

    time.sleep(1)

    print("2) 创建订单 ...")
    order_data = tester.call(
        "CREATE_ORDER",
        {
            "user_id": args.user_id,
            "order_code": order_code,
            "sender_name": "张三",
            "sender_mobile": "13800001111",
            "sender_province": "广东省",
            "sender_city": "广州市",
            "sender_district": "天河区",
            "sender_address": "体育西路123号",
            "receiver_name": "李四",
            "receiver_mobile": "13900002222",
            "receiver_province": "湖北省",
            "receiver_city": "武汉市",
            "receiver_district": "武昌区",
            "receiver_address": "中南路66号",
            "weight": 1.2,
            "quantity": 1,
            "goods_name": "服装",
            "expected_price": sel_price,
        },
    )
    order_no = order_data["order_no"]
    waybill_no = order_data["waybill_no"]
    print("   创建成功: order_no", order_no, "waybill_no", waybill_no)

    # 等待订单数据落库
    time.sleep(2)

    print("3) 查询订单详情 ...")
    tester.call("QUERY_ORDER", {"order_no": order_no})
    print("   订单查询成功")

    print("4) 查询物流轨迹 ...")
    try:
        tester.call("QUERY_TRACK", {"tracking_no": waybill_no, "order_no": order_no})
        print("   轨迹查询成功（可能暂无数据但接口 200）")
    except RuntimeError as e:
        print("   轨迹查询暂未返回成功:", e)

    print("5) 取消订单 (最后一步)...")
    tester.call(
        "CANCEL_ORDER",
        {"order_no": order_no, "tracking_no": waybill_no, "reason": "自动化测试取消"},
    )
    print("   取消订单请求已发起 -> 流程结束 ✔")


if __name__ == "__main__":
    main() 