#!/bin/bash

echo "移除未使用的time导入..."

# 需要移除time导入的文件列表
files=(
    "internal/repository/balance_repository.go"
    "internal/repository/order_status_history_repository.go"
    "internal/repository/system_config_repository.go"  
    "internal/repository/workorder_repository.go"
)

for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        echo "处理文件: $file"
        # 移除time导入行
        sed -i '' '/^[[:space:]]*"time"[[:space:]]*$/d' "$file"
    fi
done

echo "移除完成！"