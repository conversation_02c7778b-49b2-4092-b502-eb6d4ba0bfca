#!/bin/bash

# 测试重复请求检测
set -e

# 配置变量
API_URL="http://localhost:8081/api/gateway/execute"
CLIENT_ID="f2Mpl9Lg8JVYPSWFNkPWOzqt"
CLIENT_SECRET="n5bNOPSWsRcd1ntOWPo13fxtpgJ6evbB"

# 使用固定的时间戳来测试重复请求检测
FIXED_TIMESTAMP="1751967756000"

echo "🔍 测试重复请求检测"
echo "使用固定时间戳: $FIXED_TIMESTAMP"
echo "=========================================="

# 测试函数
test_duplicate_request() {
    local request_num=$1
    
    echo ""
    echo "📋 第${request_num}次请求"
    echo "----------------------------------------"
    
    # 构建业务参数
    local business_params='{"from_province":"北京","from_city":"北京市","to_province":"上海","to_city":"上海市","weight":1}'
    
    # 构建完整请求体
    local request_body=$(cat <<EOF
{
    "apiMethod": "QUERY_PRICE",
    "businessParams": $business_params,
    "timestamp": "$FIXED_TIMESTAMP",
    "username": "$CLIENT_ID"
}
EOF
)
    
    echo "请求体: $request_body"
    
    # Base64编码请求体
    local body_base64=$(echo -n "$request_body" | base64)
    
    # 构建待签名字符串
    local sign_string="client_id=${CLIENT_ID}&nonce=${FIXED_TIMESTAMP}&timestamp=${FIXED_TIMESTAMP}&body=${body_base64}"
    
    echo "待签名字符串: $sign_string"
    
    # 生成签名
    local signature=$(echo -n "$sign_string" | openssl dgst -sha256 -hmac "$CLIENT_SECRET" -binary | base64)
    
    echo "生成的签名: $signature"
    
    # 构建最终请求数据
    local final_request=$(cat <<EOF
{
    "apiMethod": "QUERY_PRICE",
    "businessParams": $business_params,
    "sign": "$signature",
    "timestamp": "$FIXED_TIMESTAMP",
    "username": "$CLIENT_ID"
}
EOF
)
    
    echo "发送请求到 $API_URL"
    
    # 发送请求并显示响应
    local response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "$final_request" \
        "$API_URL")
    
    echo "服务器响应:"
    echo "$response" | jq '.'
    
    # 检查是否是重复请求错误
    if echo "$response" | jq -e '.error == "Duplicate request detected"' > /dev/null; then
        echo "✅ 重复请求检测正常工作！"
        return 0
    elif echo "$response" | jq -e '.success == true' > /dev/null; then
        echo "⚠️  请求成功，可能重复检测未生效"
        return 1
    else
        echo "❌ 其他错误"
        return 2
    fi
}

# 执行测试
echo "第一次请求（应该成功）:"
test_duplicate_request 1
first_result=$?

echo ""
echo "等待1秒..."
sleep 1

echo "第二次请求（应该被拒绝）:"
test_duplicate_request 2
second_result=$?

echo ""
echo "=========================================="
echo "📊 测试结果总结:"

if [ $first_result -eq 1 ] && [ $second_result -eq 0 ]; then
    echo "✅ 重复请求检测功能正常工作！"
    echo "   - 第一次请求成功"
    echo "   - 第二次请求被正确拒绝"
elif [ $first_result -eq 1 ] && [ $second_result -eq 1 ]; then
    echo "❌ 重复请求检测可能未生效！"
    echo "   - 两次请求都成功了"
else
    echo "⚠️  测试结果不确定，请检查日志"
fi
