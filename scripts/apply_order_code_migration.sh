#!/bin/bash

# 执行order_code字段迁移脚本
# 用于为order_records表添加order_code字段，设置长度为1024字符

set -e

echo "🚀 开始执行order_code字段迁移..."

# 数据库连接信息
DB_HOST="*************"
DB_PORT="5432"
DB_NAME="go_kuaidi"
DB_USER="postgres"
DB_PASS="gjx6ngf4"

# 迁移文件路径
MIGRATION_FILE="sql/migrations/20250106_add_order_code_field.sql"

# 检查迁移文件是否存在
if [ ! -f "$MIGRATION_FILE" ]; then
    echo "❌ 迁移文件不存在: $MIGRATION_FILE"
    exit 1
fi

echo "📋 迁移文件: $MIGRATION_FILE"

# 执行迁移
echo "🔧 执行数据库迁移..."
PGPASSWORD="$DB_PASS" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -f "$MIGRATION_FILE"

if [ $? -eq 0 ]; then
    echo "✅ order_code字段迁移成功完成！"
    echo ""
    echo "📊 迁移详情："
    echo "   - 字段名: order_code"
    echo "   - 数据类型: VARCHAR(1024)"
    echo "   - 用途: 存储查价接口返回的order_code，用于下单时验证价格一致性"
    echo "   - 索引: 已创建 idx_order_records_order_code 索引"
    echo ""
    echo "🔍 验证迁移结果..."
    
    # 验证字段是否添加成功
    PGPASSWORD="$DB_PASS" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
    SELECT column_name, data_type, character_maximum_length, is_nullable
    FROM information_schema.columns 
    WHERE table_name = 'order_records' AND column_name = 'order_code';
    "
    
    echo ""
    echo "🎉 迁移验证完成！"
else
    echo "❌ 迁移执行失败"
    exit 1
fi 