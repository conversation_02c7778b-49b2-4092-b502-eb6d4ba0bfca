#!/bin/bash

# 设置日志清理定时任务
# 每天凌晨2点执行日志清理

CRON_JOB="0 2 * * * cd $(pwd) && ./scripts/cleanup_logs.sh >> logs/cron_cleanup.log 2>&1"

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

log_info "设置日志清理定时任务..."

# 检查是否已存在相同的定时任务
if crontab -l 2>/dev/null | grep -q "cleanup_logs.sh"; then
    log_warn "发现已存在的日志清理定时任务，将更新为最新配置"
    # 删除旧的定时任务
    crontab -l 2>/dev/null | grep -v "cleanup_logs.sh" | crontab -
fi

# 添加新的定时任务
(crontab -l 2>/dev/null; echo "$CRON_JOB") | crontab -

if [ $? -eq 0 ]; then
    log_info "定时任务设置成功"
    log_info "当前定时任务列表:"
    crontab -l
else
    log_error "定时任务设置失败"
    exit 1
fi

log_info "日志清理定时任务配置完成"
log_info "任务将在每天凌晨2点执行" 