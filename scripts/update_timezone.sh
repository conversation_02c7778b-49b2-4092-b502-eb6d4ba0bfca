#!/bin/bash

# 脚本用于批量更新时区相关代码

echo "开始更新时区相关代码..."

# 查找所有包含 time.Now() 的 Go 文件
find . -name "*.go" -not -path "./vendor/*" -not -path "./.git/*" | while read file; do
    if grep -q "time\.Now()" "$file"; then
        echo "处理文件: $file"
        
        # 检查是否已经导入了 util 包
        if ! grep -q "go-kuaidi/internal/util" "$file"; then
            # 添加 util 包导入
            sed -i.bak '/^import (/,/^)/{
                /^)/{
                    i\
	"go-kuaidi/internal/util"
                }
            }' "$file"
            
            # 如果没有多行导入，则添加单行导入
            if ! grep -q "^import (" "$file"; then
                sed -i.bak '/^import "/{
                    a\
import "go-kuaidi/internal/util"
                }' "$file"
            fi
        fi
        
        # 替换 time.Now() 为 util.NowBeijing()
        sed -i.bak 's/time\.Now()/util.NowBeijing()/g' "$file"
        
        # 清理备份文件
        rm -f "$file.bak"
        
        echo "已更新: $file"
    fi
done

echo "时区代码更新完成！"