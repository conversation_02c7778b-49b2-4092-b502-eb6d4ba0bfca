#!/bin/bash

# 订单费用差异检查工具使用示例脚本
# 版本: 1.0.0
# 作者: Go Kuaidi Team
# 日期: 2025-01-29

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 工具路径
TOOL_PATH="./cmd/billing-checker/main.go"
BUILD_PATH="./billing-checker"

# 函数：打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 函数：构建工具
build_tool() {
    print_info "构建费用差异检查工具..."
    
    if [ ! -f "$TOOL_PATH" ]; then
        print_error "工具源码文件不存在: $TOOL_PATH"
        exit 1
    fi
    
    go build -o "$BUILD_PATH" "$TOOL_PATH"
    
    if [ $? -eq 0 ]; then
        print_success "工具构建成功: $BUILD_PATH"
    else
        print_error "工具构建失败"
        exit 1
    fi
}

# 函数：显示帮助信息
show_help() {
    print_info "显示工具帮助信息..."
    echo "----------------------------------------"
    "$BUILD_PATH" -help
    echo "----------------------------------------"
}

# 函数：示例1 - 基本干运行检查
example_basic_dry_run() {
    print_info "示例1: 基本干运行检查（最近3天）"
    echo "命令: $BUILD_PATH -dry-run -days=3 -verbose"
    echo "----------------------------------------"
    
    "$BUILD_PATH" -dry-run -days=3 -verbose
    
    print_success "示例1执行完成"
    echo
}

# 函数：示例2 - 生产环境检查和修复
example_production_check() {
    print_info "示例2: 生产环境检查和修复（最近1天）"
    echo "命令: $BUILD_PATH -days=1 -output=./reports/daily-check.txt"
    echo "----------------------------------------"
    
    # 创建报告目录
    mkdir -p ./reports
    
    "$BUILD_PATH" -days=1 -output=./reports/daily-check.txt
    
    if [ -f "./reports/daily-check.txt" ]; then
        print_success "报告已生成: ./reports/daily-check.txt"
        echo "报告内容预览:"
        head -20 "./reports/daily-check.txt"
    fi
    
    print_success "示例2执行完成"
    echo
}

# 函数：示例3 - 高并发批量处理
example_high_concurrency() {
    print_info "示例3: 高并发批量处理（最近7天）"
    echo "命令: $BUILD_PATH -days=7 -workers=10 -batch-size=200 -timeout=1h -verbose"
    echo "----------------------------------------"
    
    "$BUILD_PATH" -days=7 -workers=10 -batch-size=200 -timeout=1h -verbose
    
    print_success "示例3执行完成"
    echo
}

# 函数：示例4 - JSON格式报告
example_json_report() {
    print_info "示例4: 生成JSON格式报告"
    echo "命令: $BUILD_PATH -dry-run -days=3 -report-format=json -output=./reports/billing-report.json"
    echo "----------------------------------------"
    
    mkdir -p ./reports
    
    # 注意：JSON格式可能尚未实现，这里会显示错误信息
    "$BUILD_PATH" -dry-run -days=3 -report-format=json -output=./reports/billing-report.json || true
    
    print_warning "JSON格式报告功能可能尚未实现"
    echo
}

# 函数：示例5 - 小批量测试
example_small_batch_test() {
    print_info "示例5: 小批量测试（调试模式）"
    echo "命令: $BUILD_PATH -dry-run -days=1 -batch-size=10 -workers=1 -verbose"
    echo "----------------------------------------"
    
    "$BUILD_PATH" -dry-run -days=1 -batch-size=10 -workers=1 -verbose
    
    print_success "示例5执行完成"
    echo
}

# 函数：示例6 - 模拟定时任务
example_cron_simulation() {
    print_info "示例6: 模拟定时任务执行"
    echo "模拟每日定时任务: $BUILD_PATH -days=1 -output=./reports/cron-daily.log"
    echo "----------------------------------------"
    
    mkdir -p ./reports
    
    # 添加时间戳到日志
    echo "=== 定时任务执行开始 $(date) ===" >> ./reports/cron-daily.log
    "$BUILD_PATH" -days=1 -output=./reports/cron-daily.log
    echo "=== 定时任务执行结束 $(date) ===" >> ./reports/cron-daily.log
    
    print_success "定时任务模拟完成，日志文件: ./reports/cron-daily.log"
    echo
}

# 函数：清理生成的文件
cleanup() {
    print_info "清理生成的文件..."
    
    if [ -f "$BUILD_PATH" ]; then
        rm "$BUILD_PATH"
        print_success "删除构建文件: $BUILD_PATH"
    fi
    
    if [ -d "./reports" ]; then
        rm -rf "./reports"
        print_success "删除报告目录: ./reports"
    fi
}

# 函数：显示性能统计
show_performance_stats() {
    print_info "性能统计建议..."
    echo "----------------------------------------"
    echo "批处理大小建议:"
    echo "  - 小型系统（<10万订单）: batch-size=50-100"
    echo "  - 中型系统（10-100万订单）: batch-size=100-200"
    echo "  - 大型系统（>100万订单）: batch-size=200-500"
    echo
    echo "并发工作协程建议:"
    echo "  - 2核CPU: workers=2-4"
    echo "  - 4核CPU: workers=4-8"
    echo "  - 8核CPU: workers=8-16"
    echo "  - 16核CPU: workers=16-32"
    echo
    echo "超时时间建议:"
    echo "  - 日常检查（1天数据）: timeout=10m"
    echo "  - 周度检查（7天数据）: timeout=30m"
    echo "  - 月度检查（30天数据）: timeout=2h"
    echo "----------------------------------------"
}

# 主函数
main() {
    print_info "订单费用差异检查工具使用示例"
    echo "========================================"
    
    # 检查Go环境
    if ! command -v go &> /dev/null; then
        print_error "Go环境未安装，请先安装Go 1.23.0+"
        exit 1
    fi
    
    # 构建工具
    build_tool
    
    # 显示帮助信息
    show_help
    
    # 运行示例
    print_info "开始运行使用示例..."
    echo
    
    # 基础示例
    example_basic_dry_run
    
    # 生产环境示例
    example_production_check
    
    # 高并发示例
    example_high_concurrency
    
    # JSON报告示例
    example_json_report
    
    # 小批量测试示例
    example_small_batch_test
    
    # 定时任务模拟
    example_cron_simulation
    
    # 显示性能统计建议
    show_performance_stats
    
    # 询问是否清理文件
    echo
    read -p "是否清理生成的文件？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        cleanup
    else
        print_info "保留生成的文件，可手动清理"
        echo "构建文件: $BUILD_PATH"
        echo "报告目录: ./reports/"
    fi
    
    print_success "所有示例执行完成！"
    echo
    echo "下一步建议:"
    echo "1. 根据实际需求调整参数"
    echo "2. 设置定时任务（crontab）"
    echo "3. 配置监控和告警"
    echo "4. 定期检查日志和报告"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
