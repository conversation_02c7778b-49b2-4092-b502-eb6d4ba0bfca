#!/bin/bash

# 统一网关请求调试脚本
# 帮助用户检查请求格式是否正确

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

# 配置变量
API_URL="http://localhost:8081/api/gateway/execute"

# 显示帮助信息
show_help() {
    echo "统一网关请求调试工具"
    echo ""
    echo "用法:"
    echo "  $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  -t, --test     运行测试请求"
    echo "  -f, --format   显示正确的请求格式"
    echo "  -d, --debug    调试用户请求"
    echo ""
    echo "示例:"
    echo "  $0 --test                    # 运行测试请求"
    echo "  $0 --format                  # 显示正确格式"
    echo "  $0 --debug                   # 调试模式"
}

# 显示正确的请求格式
show_correct_format() {
    log_info "统一网关正确的请求格式："
    echo ""
    echo "🔹 URL: POST $API_URL"
    echo "🔹 Content-Type: application/json"
    echo ""
    echo "🔹 请求体格式（JSON）："
    cat <<'EOF'
{
  "clientId": "your-client-id",        // 必需：客户端ID
  "timestamp": "1751963220750",        // 必需：毫秒级时间戳（推荐）
  "apiMethod": "QUERY_PRICE",          // 必需：API方法
  "businessParams": {                  // 必需：业务参数
    "from": "北京",
    "to": "上海",
    "weight": 1
  },
  "sign": "your-signature"             // 必需：签名
}
EOF
    echo ""
    echo "🔹 支持的字段名变体："
    echo "  clientId / client_id / clientID"
    echo "  timestamp / timeStamp"
    echo "  sign / signature"
    echo ""
    echo "🔹 时间戳格式："
    echo "  毫秒级（推荐）: $(generate_millisec_timestamp)"
    echo "  秒级（兼容）:   $(date +%s)"
}

# 生成毫秒级时间戳
generate_millisec_timestamp() {
    # 使用更可靠的方法生成毫秒级时间戳
    if command -v gdate >/dev/null 2>&1; then
        # macOS with GNU coreutils
        gdate +%s%3N
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS 原生方法
        echo $(($(date +%s) * 1000))
    else
        # Linux
        date +%s%3N
    fi
}

# 生成测试请求
generate_test_request() {
    local client_id=${1:-"test-client"}
    local timestamp=${2:-$(generate_millisec_timestamp)}
    
    cat <<EOF
{
  "clientId": "$client_id",
  "timestamp": "$timestamp",
  "apiMethod": "QUERY_PRICE",
  "businessParams": {
    "from": "北京",
    "to": "上海",
    "weight": 1
  },
  "sign": "test_signature_$timestamp"
}
EOF
}

# 测试请求
test_request() {
    log_info "生成测试请求..."
    
    local test_request=$(generate_test_request)
    echo "测试请求内容:"
    echo "$test_request"
    echo ""
    
    log_info "发送测试请求到 $API_URL"
    
    local response=$(curl -s -X POST "$API_URL" \
        -H "Content-Type: application/json" \
        -d "$test_request")
    
    echo "服务器响应:"
    echo "$response" | jq . 2>/dev/null || echo "$response"
    echo ""
    
    # 分析响应
    if echo "$response" | grep -q '"success":true'; then
        log_info "✅ 请求格式正确，API调用成功"
    elif echo "$response" | grep -q "MISSING_SIGNATURE_PARAMS"; then
        log_error "❌ 缺少必要的签名参数"
        echo ""
        echo "可能的原因："
        echo "1. 请求体中缺少 clientId 字段"
        echo "2. 请求体中缺少 timestamp 字段"
        echo "3. 请求体中缺少 sign 字段"
        echo "4. 字段名拼写错误"
        echo ""
        echo "请检查您的请求格式是否符合要求。"
    elif echo "$response" | grep -q "Nonce already used\|TIMESTAMP_ALREADY_USED"; then
        log_warn "⚠️ 时间戳重复，请使用新的时间戳"
    elif echo "$response" | grep -q "TIMESTAMP_EXPIRED"; then
        log_warn "⚠️ 时间戳已过期"
    elif echo "$response" | grep -q "INVALID_TIMESTAMP_FORMAT"; then
        log_error "❌ 时间戳格式无效"
    else
        log_warn "⚠️ 其他错误，请查看响应详情"
    fi
}

# 调试模式
debug_mode() {
    log_info "进入调试模式..."
    echo ""
    echo "请提供您的请求信息进行调试："
    echo ""
    
    # 获取用户输入
    read -p "请输入您的 clientId: " user_client_id
    read -p "请输入您的 timestamp: " user_timestamp
    read -p "请输入您的 sign: " user_sign
    
    echo ""
    log_info "分析您的请求参数..."
    
    # 验证参数
    local has_error=false
    
    if [ -z "$user_client_id" ]; then
        log_error "❌ clientId 为空"
        has_error=true
    else
        log_info "✅ clientId: $user_client_id"
    fi
    
    if [ -z "$user_timestamp" ]; then
        log_error "❌ timestamp 为空"
        has_error=true
    else
        # 检查时间戳格式
        if [[ "$user_timestamp" =~ ^[0-9]+$ ]]; then
            local timestamp_length=${#user_timestamp}
            if [ $timestamp_length -eq 10 ]; then
                log_warn "⚠️ timestamp 是秒级 ($user_timestamp)，建议使用毫秒级"
            elif [ $timestamp_length -eq 13 ]; then
                log_info "✅ timestamp 是毫秒级 ($user_timestamp)"
            else
                log_warn "⚠️ timestamp 长度异常 ($timestamp_length 位): $user_timestamp"
            fi
            
            # 检查时间戳是否合理
            local current_time=$(date +%s)
            local user_time_sec
            if [ $timestamp_length -eq 13 ]; then
                user_time_sec=$((user_timestamp / 1000))
            else
                user_time_sec=$user_timestamp
            fi
            
            local time_diff=$((current_time - user_time_sec))
            if [ $time_diff -gt 300 ]; then
                log_warn "⚠️ timestamp 可能已过期（超过5分钟）"
            elif [ $time_diff -lt -300 ]; then
                log_warn "⚠️ timestamp 来自未来，请检查系统时间"
            else
                log_info "✅ timestamp 时间范围正常"
            fi
        else
            log_error "❌ timestamp 格式无效，必须是数字: $user_timestamp"
            has_error=true
        fi
    fi
    
    if [ -z "$user_sign" ]; then
        log_error "❌ sign 为空"
        has_error=true
    else
        log_info "✅ sign: ${user_sign:0:20}..."
    fi
    
    echo ""
    
    if [ "$has_error" = true ]; then
        log_error "发现参数错误，请修正后重试"
        echo ""
        show_correct_format
    else
        log_info "参数格式看起来正确，生成测试请求..."
        echo ""
        
        local debug_request=$(cat <<EOF
{
  "clientId": "$user_client_id",
  "timestamp": "$user_timestamp",
  "apiMethod": "QUERY_PRICE",
  "businessParams": {
    "from": "北京",
    "to": "上海",
    "weight": 1
  },
  "sign": "$user_sign"
}
EOF
)
        
        echo "生成的请求:"
        echo "$debug_request"
        echo ""
        
        read -p "是否发送此请求进行测试？(y/N): " confirm
        if [[ $confirm =~ ^[Yy]$ ]]; then
            log_info "发送测试请求..."
            local response=$(curl -s -X POST "$API_URL" \
                -H "Content-Type: application/json" \
                -d "$debug_request")
            
            echo ""
            echo "服务器响应:"
            echo "$response" | jq . 2>/dev/null || echo "$response"
        fi
    fi
}

# 检查应用程序状态
check_application() {
    if curl -s "http://localhost:8081/health" > /dev/null 2>&1; then
        log_info "✅ 应用程序正在运行"
        return 0
    else
        log_error "❌ 应用程序未运行或无法访问"
        echo "请先启动应用程序：./build/go-kuaidi --config=config/config.yaml"
        return 1
    fi
}

# 主函数
main() {
    case "${1:-}" in
        -h|--help)
            show_help
            ;;
        -f|--format)
            show_correct_format
            ;;
        -t|--test)
            if check_application; then
                test_request
            fi
            ;;
        -d|--debug)
            if check_application; then
                debug_mode
            fi
            ;;
        "")
            log_info "🔍 统一网关请求调试工具"
            echo ""
            if check_application; then
                echo "选择操作："
                echo "1. 显示正确的请求格式"
                echo "2. 运行测试请求"
                echo "3. 调试您的请求"
                echo ""
                read -p "请选择 (1-3): " choice
                
                case $choice in
                    1) show_correct_format ;;
                    2) test_request ;;
                    3) debug_mode ;;
                    *) echo "无效选择" ;;
                esac
            fi
            ;;
        *)
            echo "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
