-- wrk价格查询压力测试脚本
-- 模拟真实的价格查询请求

-- 请求模板
local price_requests = {
    {
        method = "POST",
        path = "/api/v1/express/price",
        headers = {
            ["Content-Type"] = "application/json",
            ["Authorization"] = "Bearer test_token_123"
        },
        body = [[{
            "sender_province": "北京市",
            "sender_city": "北京市",
            "sender_district": "朝阳区",
            "receiver_province": "上海市",
            "receiver_city": "上海市", 
            "receiver_district": "浦东新区",
            "weight": 1.0,
            "volume": 0.001
        }]]
    },
    {
        method = "POST",
        path = "/api/v1/express/price",
        headers = {
            ["Content-Type"] = "application/json",
            ["Authorization"] = "Bearer test_token_123"
        },
        body = [[{
            "sender_province": "广东省",
            "sender_city": "深圳市",
            "sender_district": "南山区",
            "receiver_province": "浙江省",
            "receiver_city": "杭州市",
            "receiver_district": "西湖区",
            "weight": 2.5,
            "volume": 0.002
        }]]
    },
    {
        method = "POST",
        path = "/api/v1/express/price",
        headers = {
            ["Content-Type"] = "application/json",
            ["Authorization"] = "Bearer test_token_123"
        },
        body = [[{
            "sender_province": "江苏省",
            "sender_city": "南京市",
            "sender_district": "鼓楼区",
            "receiver_province": "山东省",
            "receiver_city": "青岛市",
            "receiver_district": "市南区",
            "weight": 0.5,
            "volume": 0.0005
        }]]
    }
}

-- 当前请求索引
local request_index = 1

-- 初始化函数
function init(args)
    -- 设置随机种子
    math.randomseed(os.time())
    
    -- 打印测试信息
    print("价格查询压力测试开始")
    print("请求模板数量: " .. #price_requests)
end

-- 构建请求
function request()
    -- 循环使用请求模板
    local req = price_requests[request_index]
    request_index = request_index + 1
    if request_index > #price_requests then
        request_index = 1
    end
    
    -- 构建wrk请求
    wrk.method = req.method
    wrk.path = req.path
    wrk.body = req.body
    
    -- 设置请求头
    wrk.headers = {}
    for key, value in pairs(req.headers) do
        wrk.headers[key] = value
    end
    
    return wrk.format()
end

-- 响应处理
function response(status, headers, body)
    -- 统计响应状态
    if status ~= 200 then
        print("Error response: " .. status .. " - " .. body)
    end
end

-- 完成回调
function done(summary, latency, requests)
    print("\n=== 价格查询压力测试结果 ===")
    print("总请求数: " .. summary.requests)
    print("总耗时: " .. summary.duration / 1000000 .. "s")
    print("平均QPS: " .. summary.requests / (summary.duration / 1000000))
    print("错误数: " .. summary.errors.connect + summary.errors.read + summary.errors.write + summary.errors.status + summary.errors.timeout)
    
    print("\n延迟统计:")
    print("最小延迟: " .. latency.min / 1000 .. "ms")
    print("最大延迟: " .. latency.max / 1000 .. "ms")
    print("平均延迟: " .. latency.mean / 1000 .. "ms")
    print("50%延迟: " .. latency:percentile(50) / 1000 .. "ms")
    print("90%延迟: " .. latency:percentile(90) / 1000 .. "ms")
    print("95%延迟: " .. latency:percentile(95) / 1000 .. "ms")
    print("99%延迟: " .. latency:percentile(99) / 1000 .. "ms")
end
