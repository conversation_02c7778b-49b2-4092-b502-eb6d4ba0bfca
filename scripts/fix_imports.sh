#!/bin/bash

echo "修复import语句格式错误..."

# 查找所有包含错误import格式的文件
grep -r "\"go.uber.org/zap\"[[:space:]]*\"github.com/your-org/go-kuaidi/internal/util\"" . --include="*.go" | cut -d: -f1 | sort -u | while read file; do
    echo "修复文件: $file"
    # 修复import语句，在两个包之间添加换行
    sed -i '' 's/"go.uber.org\/zap"[[:space:]]*"github.com\/your-org\/go-kuaidi\/internal\/util"/"go.uber.org\/zap"\
	"github.com\/your-org\/go-kuaidi\/internal\/util"/' "$file"
done

# 查找其他可能的错误格式
grep -r "\")[[:space:]]*\"github.com/your-org/go-kuaidi/internal/util\"" . --include="*.go" | cut -d: -f1 | sort -u | while read file; do
    echo "检查文件: $file"
    # 修复其他import错误
    sed -i '' 's/\("[^"]*"\)[[:space:]]*\("github.com\/your-org\/go-kuaidi\/internal\/util"\)/\1\
	\2/' "$file"
done

echo "import格式修复完成！"