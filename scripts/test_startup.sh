#!/bin/bash

echo "🔧 测试程序启动（Prometheus修复验证）"
echo "========================================"

# 设置超时时间
TIMEOUT=10

echo "1. 启动程序..."
cd /Users/<USER>/Desktop/go-kuaidi-7.4.00.21

# 启动程序并在后台运行
timeout $TIMEOUT ./bin/go-kuaidi &
PID=$!

echo "   程序PID: $PID"
echo "   等待 $TIMEOUT 秒..."

# 等待指定时间
sleep $TIMEOUT

# 检查进程是否还在运行
if kill -0 $PID 2>/dev/null; then
    echo "2. ✅ 程序启动成功，运行正常"
    echo "3. 停止程序..."
    kill $PID
    wait $PID 2>/dev/null
    echo "   程序已停止"
else
    echo "2. ❌ 程序启动失败或异常退出"
    echo "   检查错误日志..."
fi

echo ""
echo "========================================"
echo "🎉 启动测试完成"
echo "如果看到'程序启动成功'，说明Prometheus重复注册问题已修复"
echo "🚀 修复效果验证完成！"
