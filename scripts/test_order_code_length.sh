#!/bin/bash

# 测试order_code字段长度限制
# 验证order_code字段是否能正确存储最大长度的值

set -e

echo "🧪 开始测试order_code字段长度限制..."

# 数据库连接信息
DB_HOST="*************"
DB_PORT="5432"
DB_NAME="go_kuaidi"
DB_USER="postgres"
DB_PASS="gjx6ngf4"

# 生成测试用的order_code（模拟增强版格式）
generate_test_order_code() {
    # 模拟EnhancedOrderCode的JSON结构
    local json_data='{
        "standard_code": "ZTO",
        "original_code": "ZTO",
        "provider": "kuaidi100",
        "channel_id": "kuaidi100_ZTO_STANDARD",
        "product_code": "STANDARD",
        "original_request": {
            "from_province": "广东省",
            "from_city": "深圳市",
            "from_district": "南山区",
            "from_address": "科技园南区深圳湾科技生态园10栋A座",
            "to_province": "北京市",
            "to_city": "北京市",
            "to_district": "朝阳区",
            "to_address": "建国门外大街1号国贸大厦",
            "weight": 5.5,
            "length": 30,
            "width": 20,
            "height": 15,
            "volume": 9000,
            "goods_name": "电子产品",
            "goods_type": "fragile",
            "insurance_amount": 1000,
            "cod_amount": 0,
            "special_requirements": "轻拿轻放，避免挤压",
            "customer_notes": "请尽快配送，谢谢！",
            "package_count": 1,
            "is_return": false,
            "is_pickup": false,
            "pickup_time": null,
            "delivery_time": "2025-01-15T14:00:00+08:00",
            "contact_person": "张三",
            "contact_phone": "13800138000",
            "contact_email": "<EMAIL>",
            "company_name": "测试公司",
            "tax_number": "91110000123456789X",
            "invoice_title": "测试公司",
            "invoice_content": "电子产品",
            "invoice_amount": 1000,
            "payment_method": "prepaid",
            "currency": "CNY",
            "exchange_rate": 1.0,
            "customs_declaration": {
                "declared_value": 1000,
                "declared_currency": "CNY",
                "hs_code": "8517120000",
                "origin_country": "CN",
                "destination_country": "CN"
            },
            "additional_services": [
                "insurance",
                "signature_required",
                "sms_notification"
            ],
            "route_optimization": {
                "preferred_route": "fastest",
                "avoid_tolls": false,
                "avoid_highways": false
            },
            "environmental_options": {
                "eco_friendly_packaging": true,
                "carbon_offset": false
            },
            "tracking_preferences": {
                "sms_updates": true,
                "email_updates": true,
                "push_notifications": false
            },
            "delivery_instructions": {
                "access_notes": "请按门铃",
                "security_code": "1234",
                "building_access": "需要门禁卡",
                "floor_number": "15",
                "room_number": "1501"
            },
            "return_instructions": {
                "return_address": "广东省深圳市南山区科技园",
                "return_contact": "李四",
                "return_phone": "13900139000"
            },
            "custom_fields": {
                "order_source": "web",
                "campaign_id": "WINTER2025",
                "customer_segment": "premium",
                "loyalty_points": 100,
                "referral_code": "FRIEND2025"
            },
            "metadata": {
                "session_id": "sess_123456789",
                "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)",
                "ip_address": "*************",
                "device_type": "desktop",
                "browser": "Chrome",
                "os": "macOS",
                "screen_resolution": "1920x1080",
                "timezone": "Asia/Shanghai",
                "language": "zh-CN",
                "currency_preference": "CNY",
                "shipping_preference": "standard",
                "payment_preference": "credit_card",
                "notification_preference": "email",
                "privacy_settings": {
                    "data_sharing": false,
                    "marketing_emails": true,
                    "third_party_tracking": false
                },
                "accessibility_settings": {
                    "high_contrast": false,
                    "large_text": false,
                    "screen_reader": false
                },
                "performance_metrics": {
                    "page_load_time": 1200,
                    "api_response_time": 800,
                    "user_interaction_time": 5000
                }
            }
        },
        "cached_data": {
            "cache_key": "ZTO_GD_BJ_5.5kg_STANDARD",
            "cache_hit_count": 15,
            "last_cache_hit": "2025-01-06T10:30:00+08:00",
            "cache_ttl_minutes": 30,
            "cache_source": "redis",
            "cache_version": "v2.1.0"
        },
        "validation_results": {
            "address_validation": "valid",
            "weight_validation": "valid",
            "price_validation": "valid",
            "route_validation": "valid",
            "restriction_check": "passed",
            "compliance_check": "passed"
        },
        "performance_metrics": {
            "query_time_ms": 150,
            "cache_hit": true,
            "provider_response_time_ms": 120,
            "total_processing_time_ms": 270
        }
    }'
    
    # 计算JSON数据长度
    local json_length=$(echo "$json_data" | wc -c)
    echo "JSON数据长度: $json_length 字符"
    
    # Base64编码
    local base64_data=$(echo "$json_data" | base64)
    local base64_length=$(echo "$base64_data" | wc -c)
    echo "Base64编码后长度: $base64_length 字符"
    
    # 构建完整的order_code
    local order_code="ENHANCED_ORDER_CODE_$base64_data"
    local total_length=$(echo "$order_code" | wc -c)
    echo "完整order_code长度: $total_length 字符"
    
    echo "$order_code"
}

# 生成测试数据
echo "📝 生成测试order_code..."
test_order_code=$(generate_test_order_code)

# 获取长度
order_code_length=$(echo "$test_order_code" | wc -c)
echo "测试order_code长度: $order_code_length 字符"

# 检查长度是否超过限制
if [ $order_code_length -gt 2048 ]; then
    echo "❌ 警告：order_code长度 ($order_code_length) 超过2048字符限制"
    echo "建议：考虑压缩数据或使用更短的字段"
else
    echo "✅ order_code长度 ($order_code_length) 在2048字符限制内"
fi

# 测试数据库插入
echo ""
echo "🗄️ 测试数据库插入..."

# 创建测试表（如果不存在）
PGPASSWORD="$DB_PASS" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
CREATE TABLE IF NOT EXISTS order_code_test (
    id SERIAL PRIMARY KEY,
    order_code VARCHAR(2048),
    test_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
"

# 插入测试数据
echo "插入测试数据..."
PGPASSWORD="$DB_PASS" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
INSERT INTO order_code_test (order_code) VALUES ('$test_order_code');
"

if [ $? -eq 0 ]; then
    echo "✅ 数据库插入测试成功"
    
    # 验证数据
    echo "验证插入的数据..."
    PGPASSWORD="$DB_PASS" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
    SELECT 
        id,
        LENGTH(order_code) as order_code_length,
        test_time
    FROM order_code_test 
    ORDER BY id DESC 
    LIMIT 1;
    "
    
    # 清理测试数据
    echo "清理测试数据..."
    PGPASSWORD="$DB_PASS" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
    DELETE FROM order_code_test WHERE order_code = '$test_order_code';
    "
    
    echo "✅ 测试完成！order_code字段支持最大2048字符。"
else
    echo "❌ 数据库插入测试失败"
    exit 1
fi 