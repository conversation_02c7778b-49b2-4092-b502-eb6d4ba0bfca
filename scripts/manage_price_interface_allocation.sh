#!/bin/bash

# 查价接口分配管理脚本
# 用于管理快递公司的查价接口类型分配

set -e

# 数据库连接配置
DB_HOST="*************"
DB_PORT="5432"
DB_NAME="go_kuaidi"
DB_USER="postgres"
DB_PASSWORD="gjx6ngf4"
DB_URL="postgresql://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}?sslmode=disable"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "查价接口分配管理脚本"
    echo ""
    echo "用法: $0 [命令] [参数]"
    echo ""
    echo "命令:"
    echo "  list                     - 列出所有快递公司的接口分配情况"
    echo "  stats                    - 显示接口分配统计信息"
    echo "  set <公司代码> <接口类型>  - 设置快递公司的接口类型"
    echo "  get <公司代码>            - 获取快递公司的接口类型"
    echo "  migrate                  - 运行接口分配迁移脚本"
    echo "  validate                 - 验证所有快递公司都有接口分配"
    echo "  help                     - 显示此帮助信息"
    echo ""
    echo "接口类型:"
    echo "  QUERY_PRICE             - 标准查价接口"
    echo "  QUERY_REALTIME_PRICE    - 实时查价接口"
    echo ""
    echo "示例:"
    echo "  $0 list"
    echo "  $0 set JD QUERY_REALTIME_PRICE"
    echo "  $0 get YTO"
    echo "  $0 stats"
}

# 执行SQL查询
execute_sql() {
    local sql="$1"
    psql "$DB_URL" -c "$sql"
}

# 列出所有接口分配
list_allocations() {
    print_info "查询所有快递公司的接口分配情况..."
    
    execute_sql "
    SELECT 
        c.code as \"公司代码\",
        c.name as \"公司名称\",
        COALESCE(
            (SELECT config_value FROM express_company_configs 
             WHERE company_id = c.id AND config_key = 'price_query_interface' AND is_active = true),
            (SELECT 
                CASE 
                    WHEN config_value = 'dedicated' THEN 'QUERY_REALTIME_PRICE'
                    WHEN config_value = 'unified' THEN 'QUERY_PRICE'
                    ELSE config_value
                END
             FROM express_company_configs 
             WHERE company_id = c.id AND config_key = 'interface_type' AND is_active = true),
            '未配置'
        ) as \"接口类型\",
        COALESCE(
            (SELECT 'price_query_interface' FROM express_company_configs 
             WHERE company_id = c.id AND config_key = 'price_query_interface' AND is_active = true),
            (SELECT 'interface_type' FROM express_company_configs 
             WHERE company_id = c.id AND config_key = 'interface_type' AND is_active = true),
            '无配置源'
        ) as \"配置源\",
        c.is_active as \"是否启用\"
    FROM express_companies c
    WHERE c.is_active = true
    ORDER BY c.code;
    "
}

# 显示统计信息
show_stats() {
    print_info "查询接口分配统计信息..."
    
    execute_sql "
    WITH allocation_stats AS (
        SELECT 
            c.code,
            c.name,
            COALESCE(
                (SELECT config_value FROM express_company_configs 
                 WHERE company_id = c.id AND config_key = 'price_query_interface' AND is_active = true),
                (SELECT 
                    CASE 
                        WHEN config_value = 'dedicated' THEN 'QUERY_REALTIME_PRICE'
                        WHEN config_value = 'unified' THEN 'QUERY_PRICE'
                        ELSE config_value
                    END
                 FROM express_company_configs 
                 WHERE company_id = c.id AND config_key = 'interface_type' AND is_active = true)
            ) as interface_type
        FROM express_companies c
        WHERE c.is_active = true
    )
    SELECT 
        '总快递公司数' as \"统计项\",
        COUNT(*)::text as \"数量\"
    FROM allocation_stats
    UNION ALL
    SELECT 
        '标准查价接口',
        COUNT(*)::text
    FROM allocation_stats 
    WHERE interface_type = 'QUERY_PRICE'
    UNION ALL
    SELECT 
        '实时查价接口',
        COUNT(*)::text
    FROM allocation_stats 
    WHERE interface_type = 'QUERY_REALTIME_PRICE'
    UNION ALL
    SELECT 
        '未配置接口类型',
        COUNT(*)::text
    FROM allocation_stats 
    WHERE interface_type IS NULL;
    "
}

# 设置接口类型
set_interface() {
    local company_code="$1"
    local interface_type="$2"
    
    if [[ -z "$company_code" || -z "$interface_type" ]]; then
        print_error "请提供公司代码和接口类型"
        echo "用法: $0 set <公司代码> <接口类型>"
        return 1
    fi
    
    if [[ "$interface_type" != "QUERY_PRICE" && "$interface_type" != "QUERY_REALTIME_PRICE" ]]; then
        print_error "无效的接口类型: $interface_type"
        echo "支持的接口类型: QUERY_PRICE, QUERY_REALTIME_PRICE"
        return 1
    fi
    
    print_info "设置 $company_code 的接口类型为 $interface_type..."
    
    execute_sql "
    DO \$\$
    DECLARE
        company_id_var UUID;
        config_id_var UUID;
    BEGIN
        -- 查询快递公司ID
        SELECT id INTO company_id_var 
        FROM express_companies 
        WHERE code = '$company_code' AND is_active = true;
        
        IF company_id_var IS NULL THEN
            RAISE EXCEPTION '快递公司不存在或已禁用: $company_code';
        END IF;
        
        -- 检查是否已存在配置
        SELECT id INTO config_id_var
        FROM express_company_configs 
        WHERE company_id = company_id_var AND config_key = 'price_query_interface';
        
        IF config_id_var IS NULL THEN
            -- 创建新配置
            INSERT INTO express_company_configs (
                id, company_id, config_key, config_value, config_type,
                description, is_active, created_at, updated_at, created_by, updated_by
            ) VALUES (
                gen_random_uuid(), company_id_var, 'price_query_interface', '$interface_type', 'string',
                '查价接口类型配置', true, NOW(), NOW(), 'admin_script', 'admin_script'
            );
            RAISE NOTICE '已创建新的接口配置';
        ELSE
            -- 更新现有配置
            UPDATE express_company_configs 
            SET config_value = '$interface_type', updated_at = NOW(), updated_by = 'admin_script'
            WHERE id = config_id_var;
            RAISE NOTICE '已更新现有接口配置';
        END IF;
    END
    \$\$;
    "
    
    print_success "接口类型设置成功"
}

# 获取接口类型
get_interface() {
    local company_code="$1"
    
    if [[ -z "$company_code" ]]; then
        print_error "请提供公司代码"
        echo "用法: $0 get <公司代码>"
        return 1
    fi
    
    print_info "查询 $company_code 的接口类型..."
    
    execute_sql "
    SELECT 
        c.code as \"公司代码\",
        c.name as \"公司名称\",
        COALESCE(
            (SELECT config_value FROM express_company_configs 
             WHERE company_id = c.id AND config_key = 'price_query_interface' AND is_active = true),
            (SELECT 
                CASE 
                    WHEN config_value = 'dedicated' THEN 'QUERY_REALTIME_PRICE'
                    WHEN config_value = 'unified' THEN 'QUERY_PRICE'
                    ELSE config_value
                END
             FROM express_company_configs 
             WHERE company_id = c.id AND config_key = 'interface_type' AND is_active = true),
            '未配置'
        ) as \"接口类型\"
    FROM express_companies c
    WHERE c.code = '$company_code' AND c.is_active = true;
    "
}

# 运行迁移
run_migration() {
    print_info "运行接口分配迁移脚本..."
    
    local migration_file="migrations/015_setup_price_interface_allocation.sql"
    
    if [[ ! -f "$migration_file" ]]; then
        print_error "迁移文件不存在: $migration_file"
        return 1
    fi
    
    psql "$DB_URL" -f "$migration_file"
    
    print_success "迁移脚本执行完成"
}

# 验证所有快递公司都有接口分配
validate_allocations() {
    print_info "验证所有快递公司的接口分配..."
    
    execute_sql "
    SELECT 
        c.code as \"未配置的公司代码\",
        c.name as \"公司名称\"
    FROM express_companies c
    WHERE c.is_active = true
      AND NOT EXISTS (
          SELECT 1 FROM express_company_configs cfg
          WHERE cfg.company_id = c.id 
            AND cfg.config_key = 'price_query_interface'
            AND cfg.is_active = true
      )
      AND NOT EXISTS (
          SELECT 1 FROM express_company_configs cfg
          WHERE cfg.company_id = c.id 
            AND cfg.config_key = 'interface_type'
            AND cfg.is_active = true
      );
    "
}

# 主函数
main() {
    case "${1:-help}" in
        "list")
            list_allocations
            ;;
        "stats")
            show_stats
            ;;
        "set")
            set_interface "$2" "$3"
            ;;
        "get")
            get_interface "$2"
            ;;
        "migrate")
            run_migration
            ;;
        "validate")
            validate_allocations
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 执行主函数
main "$@"
