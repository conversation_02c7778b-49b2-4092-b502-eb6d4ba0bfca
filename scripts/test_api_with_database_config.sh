#!/bin/bash

# API测试脚本 - 验证数据库配置是否正常工作
# 测试流程：登录 -> 获取token -> 调用价格查询API

set -e

BASE_URL="http://localhost:8081"
ADMIN_USERNAME="admin"
ADMIN_PASSWORD="password"

echo "🚀 开始API测试 - 验证数据库配置"
echo "=================================="

# 1. 用户登录获取访问令牌
echo ""
echo "📝 步骤1: 用户登录获取访问令牌"
echo "用户名: $ADMIN_USERNAME"

LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d "{
    \"username\": \"$ADMIN_USERNAME\",
    \"password\": \"$ADMIN_PASSWORD\"
  }")

echo "登录响应: $LOGIN_RESPONSE"

# 提取访问令牌
ACCESS_TOKEN=$(echo "$LOGIN_RESPONSE" | jq -r '.access_token // empty')

if [ -z "$ACCESS_TOKEN" ] || [ "$ACCESS_TOKEN" = "null" ]; then
    echo "❌ 登录失败，无法获取访问令牌"
    echo "响应: $LOGIN_RESPONSE"
    exit 1
fi

echo "✅ 登录成功，获取到访问令牌"
echo "Token: ${ACCESS_TOKEN:0:20}..."

# 2. 调用价格查询API（使用JWT认证）
echo ""
echo "📝 步骤2: 调用价格查询API"
echo "路径: /api/v1/express/price"

PRICE_RESPONSE=$(curl -s -X POST "$BASE_URL/api/v1/express/price" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -d '{
    "from_province": "广东省",
    "from_city": "深圳市", 
    "from_district": "南山区",
    "to_province": "北京市",
    "to_city": "北京市",
    "to_district": "朝阳区",
    "weight": 1.0,
    "express_company": "SF"
  }')

echo "价格查询响应:"
echo "$PRICE_RESPONSE" | jq '.' 2>/dev/null || echo "$PRICE_RESPONSE"

# 3. 检查响应是否成功
if echo "$PRICE_RESPONSE" | jq -e '.success == true' >/dev/null 2>&1; then
    echo ""
    echo "✅ 价格查询成功！"
    echo "🎯 数据库配置验证通过 - 供应商配置正在从数据库获取"
    
    # 提取价格信息
    PRICES=$(echo "$PRICE_RESPONSE" | jq -r '.data.prices[]? | "\(.provider): ¥\(.price)"' 2>/dev/null || echo "无价格数据")
    if [ "$PRICES" != "无价格数据" ]; then
        echo ""
        echo "💰 获取到的价格信息:"
        echo "$PRICES"
    fi
    
elif echo "$PRICE_RESPONSE" | jq -e '.code' >/dev/null 2>&1; then
    ERROR_CODE=$(echo "$PRICE_RESPONSE" | jq -r '.code')
    ERROR_MSG=$(echo "$PRICE_RESPONSE" | jq -r '.message // .error // "未知错误"')
    echo ""
    echo "⚠️  API调用返回错误:"
    echo "错误代码: $ERROR_CODE"
    echo "错误信息: $ERROR_MSG"
    
    if [ "$ERROR_CODE" = "401" ]; then
        echo "🔒 认证失败 - 可能是token过期或权限不足"
    elif [ "$ERROR_CODE" = "500" ]; then
        echo "🔧 服务器内部错误 - 可能是配置问题"
    fi
else
    echo ""
    echo "❌ API调用失败或返回格式异常"
    echo "原始响应: $PRICE_RESPONSE"
fi

# 4. 测试健康检查（无需认证）
echo ""
echo "📝 步骤3: 测试健康检查"
HEALTH_RESPONSE=$(curl -s -X GET "$BASE_URL/health")
echo "健康检查响应: $HEALTH_RESPONSE"

if echo "$HEALTH_RESPONSE" | jq -e '.status == "ok"' >/dev/null 2>&1; then
    echo "✅ 服务健康状态正常"
else
    echo "⚠️  服务健康状态异常"
fi

echo ""
echo "🎯 测试总结:"
echo "============"
echo "1. 用户登录: $([ -n "$ACCESS_TOKEN" ] && echo "✅ 成功" || echo "❌ 失败")"
echo "2. 价格查询: $(echo "$PRICE_RESPONSE" | jq -e '.success == true' >/dev/null 2>&1 && echo "✅ 成功" || echo "⚠️  有问题")"
echo "3. 健康检查: $(echo "$HEALTH_RESPONSE" | jq -e '.status == "ok"' >/dev/null 2>&1 && echo "✅ 成功" || echo "⚠️  异常")"
echo ""
echo "🔍 配置验证结论:"
if echo "$PRICE_RESPONSE" | jq -e '.success == true' >/dev/null 2>&1; then
    echo "✅ 数据库配置工作正常 - 供应商配置已成功从数据库获取"
    echo "✅ YAML配置删除后系统仍能正常工作"
    echo "✅ 企业级配置管理迁移成功！"
else
    echo "⚠️  需要进一步检查配置或权限问题"
fi
