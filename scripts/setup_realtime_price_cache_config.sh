#!/bin/bash

# 实时查价缓存配置设置脚本
# 用于添加实时查价禁用缓存的配置项

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 数据库连接配置
DB_URL="*************************************************/go_kuaidi"

# 检查psql命令是否可用
check_psql() {
    if ! command -v psql >/dev/null 2>&1; then
        log_error "psql 命令未找到，请安装 PostgreSQL 客户端"
        exit 1
    fi
}

# 检查数据库连接
check_database_connection() {
    log_info "检查数据库连接..."
    
    if psql "${DB_URL}" -c "SELECT 1;" >/dev/null 2>&1; then
        log_success "数据库连接正常"
    else
        log_error "数据库连接失败，请检查连接配置"
        exit 1
    fi
}

# 执行配置添加
add_realtime_cache_config() {
    log_info "添加实时查价缓存配置..."

    SQL_FILE="./scripts/add_realtime_price_cache_config.sql"

    if [ ! -f "${SQL_FILE}" ]; then
        log_error "SQL文件不存在: ${SQL_FILE}"
        exit 1
    fi

    if psql "${DB_URL}" -f "${SQL_FILE}"; then
        log_success "实时查价缓存配置添加成功"
    else
        log_error "配置添加失败"
        exit 1
    fi
}

# 验证配置
verify_config() {
    log_info "验证配置..."
    
    result=$(psql "${DB_URL}" -t -c "
        SELECT config_value 
        FROM system_configs 
        WHERE config_group = 'price_cache'
          AND config_key = 'realtime_price_disable_cache';
    " 2>/dev/null | tr -d ' ')

    if [ "$result" = "true" ]; then
        log_success "配置验证成功：实时查价缓存已禁用"
        log_info "当前配置：realtime_price_disable_cache = true"
    elif [ "$result" = "false" ]; then
        log_warning "配置验证成功：实时查价缓存已启用"
        log_info "当前配置：realtime_price_disable_cache = false"
    else
        log_error "配置验证失败，未找到配置项"
        exit 1
    fi
}

# 显示使用说明
show_usage() {
    echo "实时查价缓存配置管理脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  setup     - 添加京东快递缓存配置（默认禁用缓存）"
    echo "  enable    - 启用京东快递缓存"
    echo "  disable   - 禁用京东快递缓存"
    echo "  status    - 查看当前配置状态"
    echo "  help      - 显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 setup     # 初始化配置"
    echo "  $0 disable   # 禁用京东快递缓存"
    echo "  $0 enable    # 启用京东快递缓存"
    echo "  $0 status    # 查看状态"
}

# 启用实时查价缓存
enable_realtime_cache() {
    log_info "启用实时查价缓存..."

    psql "${DB_URL}" -c "
        UPDATE system_configs
        SET config_value = 'false', updated_at = NOW(), updated_by = 'admin'
        WHERE config_group = 'price_cache'
          AND config_key = 'realtime_price_disable_cache';
    " >/dev/null 2>&1

    if [ $? -eq 0 ]; then
        log_success "实时查价缓存已启用"
    else
        log_error "启用失败"
        exit 1
    fi
}

# 禁用实时查价缓存
disable_realtime_cache() {
    log_info "禁用实时查价缓存..."

    psql "${DB_URL}" -c "
        UPDATE system_configs
        SET config_value = 'true', updated_at = NOW(), updated_by = 'admin'
        WHERE config_group = 'price_cache'
          AND config_key = 'realtime_price_disable_cache';
    " >/dev/null 2>&1

    if [ $? -eq 0 ]; then
        log_success "实时查价缓存已禁用"
    else
        log_error "禁用失败"
        exit 1
    fi
}

# 查看配置状态
show_status() {
    log_info "查看京东快递缓存配置状态..."
    
    psql "${DB_URL}" -c "
        SELECT 
            config_group as \"配置组\",
            config_key as \"配置键\",
            config_value as \"配置值\",
            description as \"描述\",
            updated_at as \"更新时间\"
        FROM system_configs 
        WHERE config_group = 'price_cache' 
          AND config_key = 'jd_express_disable_cache';
    "
}

# 主函数
main() {
    case "${1:-setup}" in
        "setup")
            check_psql
            check_database_connection
            add_realtime_cache_config
            verify_config
            ;;
        "enable")
            check_psql
            check_database_connection
            enable_realtime_cache
            verify_config
            ;;
        "disable")
            check_psql
            check_database_connection
            disable_realtime_cache
            verify_config
            ;;
        "status")
            check_psql
            check_database_connection
            show_status
            ;;
        "help"|"-h"|"--help")
            show_usage
            ;;
        *)
            log_error "未知选项: $1"
            show_usage
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
