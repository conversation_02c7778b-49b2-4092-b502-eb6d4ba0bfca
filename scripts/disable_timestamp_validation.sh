#!/bin/bash

# 临时禁用统一网关timestamp验证的脚本
# 使用方法：
#   ./scripts/disable_timestamp_validation.sh enable   # 禁用timestamp验证
#   ./scripts/disable_timestamp_validation.sh disable  # 恢复timestamp验证
#   ./scripts/disable_timestamp_validation.sh status   # 查看当前状态

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查当前状态
check_status() {
    log_info "检查当前timestamp验证状态..."
    
    if [ "$SKIP_TIMESTAMP_VALIDATION" = "true" ]; then
        log_warning "⚠️ TIMESTAMP验证当前已被禁用"
        echo "   环境变量: SKIP_TIMESTAMP_VALIDATION=true"
    else
        log_success "✅ TIMESTAMP验证当前已启用（正常状态）"
        echo "   环境变量: SKIP_TIMESTAMP_VALIDATION=${SKIP_TIMESTAMP_VALIDATION:-未设置}"
    fi
    
    # 检查进程状态
    if pgrep -f "go-kuaidi" > /dev/null; then
        log_info "📊 服务状态: 运行中"
        echo "   进程ID: $(pgrep -f "go-kuaidi")"
    else
        log_warning "📊 服务状态: 未运行"
    fi
    
    echo ""
}

# 禁用timestamp验证
disable_validation() {
    log_warning "🚨 正在禁用timestamp验证..."
    log_warning "⚠️ 注意：这是临时措施，仅用于调试和紧急情况"
    
    # 设置环境变量
    export SKIP_TIMESTAMP_VALIDATION=true
    
    # 写入到当前shell环境
    echo "export SKIP_TIMESTAMP_VALIDATION=true" >> ~/.bashrc
    
    log_success "✅ 环境变量已设置: SKIP_TIMESTAMP_VALIDATION=true"
    
    # 重启服务（如果正在运行）
    if pgrep -f "go-kuaidi" > /dev/null; then
        log_info "🔄 重启服务以应用更改..."
        pkill -f "go-kuaidi" || true
        sleep 2
        
        # 启动服务
        log_info "🚀 启动服务..."
        cd "$(dirname "$0")/.."
        nohup ./go-kuaidi > logs/app.log 2>&1 &
        
        sleep 3
        if pgrep -f "go-kuaidi" > /dev/null; then
            log_success "✅ 服务已重启，timestamp验证已禁用"
        else
            log_error "❌ 服务重启失败，请手动检查"
        fi
    else
        log_info "ℹ️ 服务未运行，下次启动时将应用设置"
    fi
    
    echo ""
    log_warning "🔔 重要提醒："
    echo "   1. 这是临时安全措施，请尽快解决timestamp问题"
    echo "   2. 禁用验证会降低系统安全性"
    echo "   3. 建议在问题解决后立即恢复验证"
    echo "   4. 使用 './scripts/disable_timestamp_validation.sh disable' 恢复验证"
}

# 恢复timestamp验证
enable_validation() {
    log_info "🔒 正在恢复timestamp验证..."
    
    # 取消环境变量
    unset SKIP_TIMESTAMP_VALIDATION
    
    # 从bashrc中移除
    if [ -f ~/.bashrc ]; then
        sed -i '/export SKIP_TIMESTAMP_VALIDATION=true/d' ~/.bashrc
    fi
    
    log_success "✅ 环境变量已清除"
    
    # 重启服务（如果正在运行）
    if pgrep -f "go-kuaidi" > /dev/null; then
        log_info "🔄 重启服务以应用更改..."
        pkill -f "go-kuaidi" || true
        sleep 2
        
        # 启动服务
        log_info "🚀 启动服务..."
        cd "$(dirname "$0")/.."
        nohup ./go-kuaidi > logs/app.log 2>&1 &
        
        sleep 3
        if pgrep -f "go-kuaidi" > /dev/null; then
            log_success "✅ 服务已重启，timestamp验证已恢复"
        else
            log_error "❌ 服务重启失败，请手动检查"
        fi
    else
        log_info "ℹ️ 服务未运行，下次启动时将应用设置"
    fi
    
    echo ""
    log_success "🔐 timestamp验证已恢复正常"
}

# 显示帮助信息
show_help() {
    echo "统一网关timestamp验证管理工具"
    echo ""
    echo "使用方法:"
    echo "  $0 enable    # 禁用timestamp验证（临时措施）"
    echo "  $0 disable   # 恢复timestamp验证（推荐）"
    echo "  $0 status    # 查看当前状态"
    echo "  $0 help      # 显示帮助信息"
    echo ""
    echo "说明:"
    echo "  - enable: 设置 SKIP_TIMESTAMP_VALIDATION=true，禁用验证"
    echo "  - disable: 清除环境变量，恢复正常验证"
    echo "  - status: 检查当前验证状态和服务状态"
    echo ""
    echo "注意事项:"
    echo "  1. 禁用验证是临时安全措施，仅用于紧急情况"
    echo "  2. 建议尽快解决timestamp问题并恢复验证"
    echo "  3. 服务会自动重启以应用更改"
}

# 主逻辑
case "${1:-status}" in
    "enable")
        disable_validation
        ;;
    "disable")
        enable_validation
        ;;
    "status")
        check_status
        ;;
    "help"|"-h"|"--help")
        show_help
        ;;
    *)
        log_error "❌ 无效参数: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
