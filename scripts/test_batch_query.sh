#!/bin/bash

# ============================================================================
# 批量查询功能测试脚本
# ============================================================================
# 
# 此脚本用于测试批量运单号查询功能的正确性和性能
# 
# 使用方法:
#   chmod +x scripts/test_batch_query.sh
#   ./scripts/test_batch_query.sh
#
# 作者: 系统架构师
# 创建时间: 2025-01-28
# 版本: v1.0
# ============================================================================

# 配置参数
BASE_URL="http://localhost:8080"
AUTH_TOKEN=""  # 需要设置实际的认证令牌
TEST_USER="mywl"
TEST_PASSWORD="NNJJ@178..n"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取认证令牌
get_auth_token() {
    log_info "正在获取认证令牌..."
    
    local response=$(curl -s -X POST "${BASE_URL}/api/v1/auth/login" \
        -H "Content-Type: application/json" \
        -d "{\"username\":\"${TEST_USER}\",\"password\":\"${TEST_PASSWORD}\"}")
    
    if [ $? -ne 0 ]; then
        log_error "无法连接到服务器"
        exit 1
    fi
    
    # 提取token（假设响应格式为 {"success":true,"data":{"token":"xxx"}}）
    AUTH_TOKEN=$(echo "$response" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
    
    if [ -z "$AUTH_TOKEN" ]; then
        log_error "获取认证令牌失败: $response"
        exit 1
    fi
    
    log_success "认证令牌获取成功"
}

# 测试单个运单号查询（对照组）
test_single_tracking_query() {
    log_info "测试单个运单号查询..."
    
    local tracking_no="SF1234567890"
    local start_time=$(date +%s.%N)
    
    local response=$(curl -s -X GET "${BASE_URL}/api/v1/express/orders?tracking_no=${tracking_no}" \
        -H "Authorization: Bearer ${AUTH_TOKEN}" \
        -H "Content-Type: application/json")
    
    local end_time=$(date +%s.%N)
    local duration=$(echo "$end_time - $start_time" | bc)
    
    if echo "$response" | grep -q '"success":true'; then
        log_success "单个运单号查询成功 (耗时: ${duration}s)"
        
        # 检查是否不包含批量统计信息
        if echo "$response" | grep -q '"batch_stats"'; then
            log_warning "单个查询不应包含批量统计信息"
        else
            log_success "单个查询正确，不包含批量统计信息"
        fi
    else
        log_error "单个运单号查询失败: $response"
    fi
}

# 测试批量运单号查询 - 逗号分隔格式
test_batch_query_comma_separated() {
    log_info "测试批量查询 - 逗号分隔格式..."
    
    local tracking_nos="SF1234567890,YT9876543210,ZTO5555666677"
    local start_time=$(date +%s.%N)
    
    local response=$(curl -s -X GET "${BASE_URL}/api/v1/express/orders?tracking_nos=${tracking_nos}&batch_mode=true" \
        -H "Authorization: Bearer ${AUTH_TOKEN}" \
        -H "Content-Type: application/json")
    
    local end_time=$(date +%s.%N)
    local duration=$(echo "$end_time - $start_time" | bc)
    
    if echo "$response" | grep -q '"success":true'; then
        log_success "批量查询（逗号分隔）成功 (耗时: ${duration}s)"
        
        # 检查批量统计信息
        if echo "$response" | grep -q '"batch_stats"'; then
            local total_queried=$(echo "$response" | grep -o '"total_queried":[0-9]*' | cut -d':' -f2)
            local found_count=$(echo "$response" | grep -o '"found_count":[0-9]*' | cut -d':' -f2)
            
            log_success "批量统计信息: 查询${total_queried}个，找到${found_count}个"
        else
            log_error "批量查询缺少统计信息"
        fi
    else
        log_error "批量查询（逗号分隔）失败: $response"
    fi
}

# 测试批量运单号查询 - 数组格式
test_batch_query_array_format() {
    log_info "测试批量查询 - 数组格式..."
    
    local start_time=$(date +%s.%N)
    
    local response=$(curl -s -X GET "${BASE_URL}/api/v1/express/orders?tracking_nos[]=SF1234567890&tracking_nos[]=YT9876543210&batch_mode=true" \
        -H "Authorization: Bearer ${AUTH_TOKEN}" \
        -H "Content-Type: application/json")
    
    local end_time=$(date +%s.%N)
    local duration=$(echo "$end_time - $start_time" | bc)
    
    if echo "$response" | grep -q '"success":true'; then
        log_success "批量查询（数组格式）成功 (耗时: ${duration}s)"
    else
        log_error "批量查询（数组格式）失败: $response"
    fi
}

# 测试大批量查询性能
test_large_batch_performance() {
    log_info "测试大批量查询性能（50个运单号）..."
    
    # 生成50个测试运单号
    local tracking_nos=""
    for i in {1..50}; do
        local tracking_no="SF$(printf "%010d" $i)"
        if [ $i -eq 1 ]; then
            tracking_nos="$tracking_no"
        else
            tracking_nos="$tracking_nos,$tracking_no"
        fi
    done
    
    local start_time=$(date +%s.%N)
    
    local response=$(curl -s -X GET "${BASE_URL}/api/v1/express/orders?tracking_nos=${tracking_nos}&batch_mode=true" \
        -H "Authorization: Bearer ${AUTH_TOKEN}" \
        -H "Content-Type: application/json")
    
    local end_time=$(date +%s.%N)
    local duration=$(echo "$end_time - $start_time" | bc)
    
    if echo "$response" | grep -q '"success":true'; then
        log_success "大批量查询成功 (耗时: ${duration}s)"
        
        # 性能评估
        if (( $(echo "$duration < 1.0" | bc -l) )); then
            log_success "性能优秀: 响应时间 < 1秒"
        elif (( $(echo "$duration < 2.0" | bc -l) )); then
            log_warning "性能良好: 响应时间 < 2秒"
        else
            log_warning "性能需要优化: 响应时间 > 2秒"
        fi
    else
        log_error "大批量查询失败: $response"
    fi
}

# 测试批量查询限制
test_batch_query_limits() {
    log_info "测试批量查询限制（超过50个运单号）..."
    
    # 生成51个测试运单号
    local tracking_nos=""
    for i in {1..51}; do
        local tracking_no="SF$(printf "%010d" $i)"
        if [ $i -eq 1 ]; then
            tracking_nos="$tracking_no"
        else
            tracking_nos="$tracking_nos,$tracking_no"
        fi
    done
    
    local response=$(curl -s -X GET "${BASE_URL}/api/v1/express/orders?tracking_nos=${tracking_nos}&batch_mode=true" \
        -H "Authorization: Bearer ${AUTH_TOKEN}" \
        -H "Content-Type: application/json")
    
    if echo "$response" | grep -q '"success":false'; then
        if echo "$response" | grep -q "批量查询运单号数量不能超过"; then
            log_success "批量查询限制验证成功"
        else
            log_warning "批量查询限制错误信息不正确"
        fi
    else
        log_error "批量查询限制验证失败，应该返回错误"
    fi
}

# 测试无效运单号格式
test_invalid_tracking_numbers() {
    log_info "测试无效运单号格式..."
    
    local tracking_nos="SHORT,VERYLONGTRACKINGNO123456789012345,VALID1234567890"
    
    local response=$(curl -s -X GET "${BASE_URL}/api/v1/express/orders?tracking_nos=${tracking_nos}&batch_mode=true" \
        -H "Authorization: Bearer ${AUTH_TOKEN}" \
        -H "Content-Type: application/json")
    
    if echo "$response" | grep -q '"success":false'; then
        if echo "$response" | grep -q "运单号格式不正确"; then
            log_success "无效运单号格式验证成功"
        else
            log_warning "无效运单号格式错误信息不正确"
        fi
    else
        log_error "无效运单号格式验证失败，应该返回错误"
    fi
}

# 并发测试
test_concurrent_batch_queries() {
    log_info "测试并发批量查询（10个并发请求）..."
    
    local tracking_nos="SF1111111111,YT2222222222,ZTO3333333333"
    local concurrent_count=10
    local success_count=0
    
    # 创建临时文件存储结果
    local temp_dir=$(mktemp -d)
    
    # 启动并发请求
    for i in $(seq 1 $concurrent_count); do
        (
            local start_time=$(date +%s.%N)
            local response=$(curl -s -X GET "${BASE_URL}/api/v1/express/orders?tracking_nos=${tracking_nos}&batch_mode=true" \
                -H "Authorization: Bearer ${AUTH_TOKEN}" \
                -H "Content-Type: application/json")
            local end_time=$(date +%s.%N)
            local duration=$(echo "$end_time - $start_time" | bc)
            
            if echo "$response" | grep -q '"success":true'; then
                echo "SUCCESS:$duration" > "$temp_dir/result_$i"
            else
                echo "FAILED:$duration" > "$temp_dir/result_$i"
            fi
        ) &
    done
    
    # 等待所有请求完成
    wait
    
    # 统计结果
    local total_duration=0
    for i in $(seq 1 $concurrent_count); do
        if [ -f "$temp_dir/result_$i" ]; then
            local result=$(cat "$temp_dir/result_$i")
            local status=$(echo "$result" | cut -d':' -f1)
            local duration=$(echo "$result" | cut -d':' -f2)
            
            if [ "$status" = "SUCCESS" ]; then
                success_count=$((success_count + 1))
                total_duration=$(echo "$total_duration + $duration" | bc)
            fi
        fi
    done
    
    # 清理临时文件
    rm -rf "$temp_dir"
    
    local avg_duration=$(echo "scale=3; $total_duration / $success_count" | bc)
    
    log_success "并发测试完成: ${success_count}/${concurrent_count} 成功"
    log_success "平均响应时间: ${avg_duration}s"
    
    if [ $success_count -eq $concurrent_count ]; then
        log_success "并发测试全部成功"
    else
        log_warning "部分并发请求失败"
    fi
}

# 主测试流程
main() {
    echo "============================================================================"
    echo "🚀 批量查询功能测试开始"
    echo "============================================================================"
    
    # 检查依赖
    if ! command -v curl &> /dev/null; then
        log_error "curl 命令未找到，请安装 curl"
        exit 1
    fi
    
    if ! command -v bc &> /dev/null; then
        log_error "bc 命令未找到，请安装 bc"
        exit 1
    fi
    
    # 获取认证令牌
    get_auth_token
    
    echo ""
    echo "============================================================================"
    echo "📋 开始功能测试"
    echo "============================================================================"
    
    # 执行各项测试
    test_single_tracking_query
    echo ""
    
    test_batch_query_comma_separated
    echo ""
    
    test_batch_query_array_format
    echo ""
    
    test_large_batch_performance
    echo ""
    
    test_batch_query_limits
    echo ""
    
    test_invalid_tracking_numbers
    echo ""
    
    test_concurrent_batch_queries
    echo ""
    
    echo "============================================================================"
    echo "✅ 批量查询功能测试完成"
    echo "============================================================================"
    
    log_info "测试建议:"
    log_info "1. 如果性能测试显示响应时间过长，请运行数据库索引优化脚本"
    log_info "2. 如果并发测试失败率较高，请检查服务器资源和数据库连接池配置"
    log_info "3. 建议在生产环境部署前进行更大规模的压力测试"
}

# 执行主函数
main "$@"
