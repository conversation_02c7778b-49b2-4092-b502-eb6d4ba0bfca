#!/bin/bash

# 快递公司管理功能自动化测试脚本
# 用途：验证快递公司启用/禁用功能的实时生效和接口一致性
# 作者：测试团队
# 版本：v1.0

set -e

# ==================== 配置区域 ====================

# 服务配置
BASE_URL="http://localhost:8081"
ADMIN_USERNAME="admin"
ADMIN_PASSWORD="1104030777+.aA..@"

# 测试配置
TEST_COMPANY_CODE="STO"  # 测试用的快递公司代码
TEST_COMPANY_NAME="申通快递"
WAIT_TIME=5  # 等待配置生效的时间（秒）

# 颜色输出配置
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 全局变量
ADMIN_TOKEN=""
USER_TOKEN=""
TEST_COMPANY_ID=""
ORIGINAL_STATUS=""

# ==================== 工具函数 ====================

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%H:%M:%S') $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%H:%M:%S') $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%H:%M:%S') $1"
}

log_test() {
    echo -e "${BLUE}[TEST]${NC} $(date '+%H:%M:%S') $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "命令 $1 未找到，请先安装"
        exit 1
    fi
}

# 检查服务是否运行
check_service() {
    log_info "检查服务是否运行..."
    if ! curl -s "$BASE_URL/health" > /dev/null; then
        log_error "服务未运行，请先启动服务: ./start-local.sh"
        exit 1
    fi
    log_info "服务运行正常"
}

# ==================== 认证函数 ====================

# 管理员登录
login_admin() {
    log_info "正在登录管理员账号..."
    
    local response=$(curl -s -X POST "$BASE_URL/api/v1/admin/auth/login" \
        -H "Content-Type: application/json" \
        -d "{\"username\":\"$ADMIN_USERNAME\",\"password\":\"$ADMIN_PASSWORD\"}")
    
    ADMIN_TOKEN=$(echo "$response" | jq -r '.data.token // empty')
    
    if [ -z "$ADMIN_TOKEN" ] || [ "$ADMIN_TOKEN" = "null" ]; then
        log_error "管理员登录失败"
        echo "响应: $response"
        exit 1
    fi
    
    log_info "管理员登录成功"
}

# 普通用户登录（使用管理员账号）
login_user() {
    log_info "正在登录用户账号..."
    
    local response=$(curl -s -X POST "$BASE_URL/api/v1/auth/login" \
        -H "Content-Type: application/json" \
        -d "{\"username\":\"$ADMIN_USERNAME\",\"password\":\"$ADMIN_PASSWORD\"}")
    
    USER_TOKEN=$(echo "$response" | jq -r '.data.token // empty')
    
    if [ -z "$USER_TOKEN" ] || [ "$USER_TOKEN" = "null" ]; then
        log_error "用户登录失败"
        echo "响应: $response"
        exit 1
    fi
    
    log_info "用户登录成功"
}

# ==================== 快递公司管理函数 ====================

# 获取快递公司信息
get_company_info() {
    local company_code=$1
    
    local response=$(curl -s -X GET "$BASE_URL/api/v1/admin/express/companies" \
        -H "Authorization: Bearer $ADMIN_TOKEN" \
        -H "Content-Type: application/json")
    
    local company_info=$(echo "$response" | jq -r ".data.companies[] | select(.code==\"$company_code\")")
    
    if [ -z "$company_info" ] || [ "$company_info" = "null" ]; then
        log_error "未找到快递公司: $company_code"
        return 1
    fi
    
    TEST_COMPANY_ID=$(echo "$company_info" | jq -r '.id')
    ORIGINAL_STATUS=$(echo "$company_info" | jq -r '.is_active')
    
    log_info "快递公司信息: ID=$TEST_COMPANY_ID, 当前状态=$ORIGINAL_STATUS"
}

# 更新快递公司状态
update_company_status() {
    local company_id=$1
    local is_active=$2
    local company_name=$3
    
    log_info "正在更新快递公司状态: $company_name -> $is_active"
    
    local response=$(curl -s -X PUT "$BASE_URL/api/v1/admin/express/companies/$company_id" \
        -H "Authorization: Bearer $ADMIN_TOKEN" \
        -H "Content-Type: application/json" \
        -d "{
            \"name\": \"$company_name\",
            \"is_active\": $is_active,
            \"sort_order\": 0,
            \"volume_weight_ratio\": 8000
        }")
    
    local success=$(echo "$response" | jq -r '.success // false')
    
    if [ "$success" != "true" ]; then
        log_error "更新快递公司状态失败"
        echo "响应: $response"
        return 1
    fi
    
    log_info "快递公司状态更新成功"
    
    # 等待配置生效
    log_info "等待 $WAIT_TIME 秒让配置生效..."
    sleep $WAIT_TIME
}

# ==================== 测试函数 ====================

# 测试查价接口
test_price_query() {
    local expected_company_code=$1
    local should_exist=$2
    local test_name=$3
    
    log_test "测试查价接口: $test_name"
    
    local response=$(curl -s -X POST "$BASE_URL/api/v1/express/price" \
        -H "Authorization: Bearer $USER_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
            "sender": {
                "province": "广东省",
                "city": "深圳市",
                "area": "南山区"
            },
            "receiver": {
                "province": "北京市",
                "city": "北京市",
                "area": "朝阳区"
            },
            "package": {
                "weight": 1.0,
                "length": 10,
                "width": 10,
                "height": 10
            },
            "query_all_companies": true
        }')
    
    local success=$(echo "$response" | jq -r '.success // false')
    
    if [ "$success" != "true" ]; then
        log_error "查价接口调用失败"
        echo "响应: $response"
        return 1
    fi
    
    local found=$(echo "$response" | jq -r ".data[]? | select(.express_code==\"$expected_company_code\") | .express_code")
    
    if [ "$should_exist" = "true" ]; then
        if [ "$found" = "$expected_company_code" ]; then
            log_info "✅ 测试通过：快递公司 $expected_company_code 在查价结果中"
            return 0
        else
            log_error "❌ 测试失败：快递公司 $expected_company_code 未在查价结果中"
            return 1
        fi
    else
        if [ "$found" = "$expected_company_code" ]; then
            log_error "❌ 测试失败：快递公司 $expected_company_code 不应在查价结果中"
            return 1
        else
            log_info "✅ 测试通过：快递公司 $expected_company_code 不在查价结果中"
            return 0
        fi
    fi
}

# 测试快递公司列表接口
test_company_list() {
    local expected_company_code=$1
    local should_exist=$2
    local test_name=$3
    
    log_test "测试快递公司列表接口: $test_name"
    
    local response=$(curl -s -X GET "$BASE_URL/api/v1/express/companies" \
        -H "Authorization: Bearer $USER_TOKEN" \
        -H "Content-Type: application/json")
    
    local success=$(echo "$response" | jq -r '.success // false')
    
    if [ "$success" != "true" ]; then
        log_error "快递公司列表接口调用失败"
        echo "响应: $response"
        return 1
    fi
    
    local found=$(echo "$response" | jq -r ".data[]? | select(.code==\"$expected_company_code\") | .code")
    
    if [ "$should_exist" = "true" ]; then
        if [ "$found" = "$expected_company_code" ]; then
            log_info "✅ 测试通过：快递公司 $expected_company_code 在公司列表中"
            return 0
        else
            log_error "❌ 测试失败：快递公司 $expected_company_code 未在公司列表中"
            return 1
        fi
    else
        if [ "$found" = "$expected_company_code" ]; then
            log_error "❌ 测试失败：快递公司 $expected_company_code 不应在公司列表中"
            return 1
        else
            log_info "✅ 测试通过：快递公司 $expected_company_code 不在公司列表中"
            return 0
        fi
    fi
}

# 性能测试
test_performance() {
    log_test "执行性能测试"
    
    local start_time=$(date +%s%N)
    
    # 执行10次查价请求
    for i in {1..10}; do
        curl -s -X POST "$BASE_URL/api/v1/express/price" \
            -H "Authorization: Bearer $USER_TOKEN" \
            -H "Content-Type: application/json" \
            -d '{
                "sender": {"province": "广东省", "city": "深圳市", "area": "南山区"},
                "receiver": {"province": "北京市", "city": "北京市", "area": "朝阳区"},
                "package": {"weight": 1.0, "length": 10, "width": 10, "height": 10},
                "query_all_companies": true
            }' > /dev/null
    done
    
    local end_time=$(date +%s%N)
    local duration=$(( (end_time - start_time) / 1000000 ))  # 转换为毫秒
    local avg_response_time=$(( duration / 10 ))
    
    log_info "平均响应时间: ${avg_response_time}ms"
    
    if [ $avg_response_time -lt 200 ]; then
        log_info "✅ 性能测试通过：平均响应时间 < 200ms"
        return 0
    else
        log_warn "⚠️ 性能测试警告：平均响应时间 >= 200ms"
        return 1
    fi
}

# ==================== 主测试流程 ====================

# 清理函数
cleanup() {
    log_info "正在清理测试环境..."
    
    if [ -n "$TEST_COMPANY_ID" ] && [ -n "$ORIGINAL_STATUS" ] && [ -n "$ADMIN_TOKEN" ]; then
        log_info "恢复快递公司原始状态: $ORIGINAL_STATUS"
        update_company_status "$TEST_COMPANY_ID" "$ORIGINAL_STATUS" "$TEST_COMPANY_NAME" || true
    fi
    
    log_info "测试环境清理完成"
}

# 注册清理函数
trap cleanup EXIT

# 主测试函数
main() {
    local test_passed=0
    local test_failed=0
    
    echo "========================================"
    echo "快递公司管理功能自动化测试"
    echo "========================================"
    echo "测试时间: $(date)"
    echo "测试环境: $BASE_URL"
    echo "测试公司: $TEST_COMPANY_NAME ($TEST_COMPANY_CODE)"
    echo "========================================"
    
    # 前置检查
    check_command "curl"
    check_command "jq"
    check_service
    
    # 认证
    login_admin
    login_user
    
    # 获取测试快递公司信息
    get_company_info "$TEST_COMPANY_CODE"
    
    # TC001: 快递公司禁用功能测试
    log_test "开始执行 TC001: 快递公司禁用功能测试"
    
    # 1. 确保快递公司为启用状态
    if [ "$ORIGINAL_STATUS" != "true" ]; then
        update_company_status "$TEST_COMPANY_ID" "true" "$TEST_COMPANY_NAME"
    fi
    
    # 2. 测试启用状态下的查价接口
    if test_price_query "$TEST_COMPANY_CODE" "true" "启用状态查价测试"; then
        ((test_passed++))
    else
        ((test_failed++))
    fi
    
    # 3. 测试启用状态下的公司列表接口
    if test_company_list "$TEST_COMPANY_CODE" "true" "启用状态公司列表测试"; then
        ((test_passed++))
    else
        ((test_failed++))
    fi
    
    # 4. 禁用快递公司
    update_company_status "$TEST_COMPANY_ID" "false" "$TEST_COMPANY_NAME"
    
    # 5. 测试禁用状态下的查价接口
    if test_price_query "$TEST_COMPANY_CODE" "false" "禁用状态查价测试"; then
        ((test_passed++))
    else
        ((test_failed++))
    fi
    
    # 6. 测试禁用状态下的公司列表接口
    if test_company_list "$TEST_COMPANY_CODE" "false" "禁用状态公司列表测试"; then
        ((test_passed++))
    else
        ((test_failed++))
    fi
    
    # TC002: 快递公司启用功能测试
    log_test "开始执行 TC002: 快递公司启用功能测试"
    
    # 7. 重新启用快递公司
    update_company_status "$TEST_COMPANY_ID" "true" "$TEST_COMPANY_NAME"
    
    # 8. 测试重新启用后的查价接口
    if test_price_query "$TEST_COMPANY_CODE" "true" "重新启用查价测试"; then
        ((test_passed++))
    else
        ((test_failed++))
    fi
    
    # TC003: 性能测试
    log_test "开始执行 TC003: 性能测试"
    
    if test_performance; then
        ((test_passed++))
    else
        ((test_failed++))
    fi
    
    # 输出测试结果
    echo "========================================"
    echo "测试结果汇总"
    echo "========================================"
    echo "通过测试: $test_passed"
    echo "失败测试: $test_failed"
    echo "总计测试: $((test_passed + test_failed))"
    echo "通过率: $(( test_passed * 100 / (test_passed + test_failed) ))%"
    echo "========================================"
    
    if [ $test_failed -eq 0 ]; then
        log_info "🎉 所有测试通过！"
        exit 0
    else
        log_error "❌ 有 $test_failed 个测试失败"
        exit 1
    fi
}

# 执行主函数
main "$@"
