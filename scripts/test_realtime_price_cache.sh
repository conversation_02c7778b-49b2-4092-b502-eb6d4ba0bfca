#!/bin/bash

# 实时查价缓存功能测试脚本
# 测试实时查价是否正确绕过缓存逻辑

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置
API_BASE_URL="http://localhost:8081"
TEST_USER_TOKEN=""

# 获取测试用户token
get_test_token() {
    log_info "获取测试用户token..."
    
    response=$(curl -s -X POST "${API_BASE_URL}/api/v1/auth/login" \
        -H "Content-Type: application/json" \
        -d '{
            "username": "mywl",
            "password": "NNJJ@178..n"
        }')

    TEST_USER_TOKEN=$(echo "$response" | jq -r '.access_token // empty')
    
    if [ -z "$TEST_USER_TOKEN" ] || [ "$TEST_USER_TOKEN" = "null" ]; then
        log_error "获取token失败: $response"
        exit 1
    fi
    
    log_success "获取token成功"
}

# 测试京东快递查价（应该不使用缓存）
test_jd_express_price() {
    log_info "测试京东快递查价（应该绕过缓存）..."
    
    # 第一次查询
    log_info "第一次查询京东快递价格..."
    response1=$(curl -s -X POST "${API_BASE_URL}/api/v1/express/price" \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer ${TEST_USER_TOKEN}" \
        -d '{
            "express_code": "JD",
            "from_province": "广东省",
            "from_city": "深圳市",
            "from_district": "南山区",
            "to_province": "北京市",
            "to_city": "北京市",
            "to_district": "朝阳区",
            "weight": 1.0
        }')
    
    echo "第一次查询响应: $response1"
    
    # 等待1秒
    sleep 1
    
    # 第二次查询（如果使用缓存，响应时间会很快）
    log_info "第二次查询京东快递价格..."
    start_time=$(date +%s%N)
    response2=$(curl -s -X POST "${API_BASE_URL}/api/v1/express/price" \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer ${TEST_USER_TOKEN}" \
        -d '{
            "express_code": "JD",
            "from_province": "广东省",
            "from_city": "深圳市",
            "from_district": "南山区",
            "to_province": "北京市",
            "to_city": "北京市",
            "to_district": "朝阳区",
            "weight": 1.0
        }')
    end_time=$(date +%s%N)
    
    echo "第二次查询响应: $response2"
    
    # 计算响应时间（毫秒）
    response_time=$(( (end_time - start_time) / 1000000 ))
    log_info "第二次查询响应时间: ${response_time}ms"
    
    # 检查是否成功
    success1=$(echo "$response1" | jq -r '.success // false')
    success2=$(echo "$response2" | jq -r '.success // false')
    
    if [ "$success1" = "true" ] && [ "$success2" = "true" ]; then
        log_success "京东快递查价测试成功"
        
        # 如果响应时间较长（>500ms），说明没有使用缓存
        if [ "$response_time" -gt 500 ]; then
            log_success "响应时间较长，确认京东快递未使用缓存"
        else
            log_warning "响应时间较短，可能使用了缓存"
        fi
    else
        log_error "京东快递查价测试失败"
        echo "第一次查询: $response1"
        echo "第二次查询: $response2"
    fi
}

# 测试其他快递公司查价（应该使用缓存）
test_other_express_price() {
    log_info "测试其他快递公司查价（应该使用缓存）..."
    
    # 第一次查询中通快递
    log_info "第一次查询中通快递价格..."
    response1=$(curl -s -X POST "${API_BASE_URL}/api/v1/express/price" \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer ${TEST_USER_TOKEN}" \
        -d '{
            "express_code": "ZTO",
            "from_province": "广东省",
            "from_city": "深圳市",
            "from_district": "南山区",
            "to_province": "北京市",
            "to_city": "北京市",
            "to_district": "朝阳区",
            "weight": 1.0
        }')
    
    # 等待1秒
    sleep 1
    
    # 第二次查询（应该使用缓存，响应时间会很快）
    log_info "第二次查询中通快递价格..."
    start_time=$(date +%s%N)
    response2=$(curl -s -X POST "${API_BASE_URL}/api/v1/express/price" \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer ${TEST_USER_TOKEN}" \
        -d '{
            "express_code": "ZTO",
            "from_province": "广东省",
            "from_city": "深圳市",
            "from_district": "南山区",
            "to_province": "北京市",
            "to_city": "北京市",
            "to_district": "朝阳区",
            "weight": 1.0
        }')
    end_time=$(date +%s%N)
    
    # 计算响应时间（毫秒）
    response_time=$(( (end_time - start_time) / 1000000 ))
    log_info "第二次查询响应时间: ${response_time}ms"
    
    # 检查是否成功
    success1=$(echo "$response1" | jq -r '.success // false')
    success2=$(echo "$response2" | jq -r '.success // false')
    
    if [ "$success1" = "true" ] && [ "$success2" = "true" ]; then
        log_success "中通快递查价测试成功"
        
        # 如果响应时间较短（<200ms），说明使用了缓存
        if [ "$response_time" -lt 200 ]; then
            log_success "响应时间较短，确认中通快递使用了缓存"
        else
            log_warning "响应时间较长，可能未使用缓存"
        fi
    else
        log_error "中通快递查价测试失败"
        echo "第一次查询: $response1"
        echo "第二次查询: $response2"
    fi
}

# 检查服务是否运行
check_service() {
    log_info "检查服务是否运行..."
    
    if curl -s "${API_BASE_URL}/health" >/dev/null 2>&1; then
        log_success "服务运行正常"
    else
        log_error "服务未运行，请先启动服务"
        exit 1
    fi
}

# 测试京东快递各种代码变体
test_jd_express_variants() {
    log_info "测试京东快递各种代码变体..."
    
    variants=("JD" "jd" "jingdong" "JINGDONG")
    
    for variant in "${variants[@]}"; do
        log_info "测试快递代码: $variant"
        
        start_time=$(date +%s%N)
        response=$(curl -s -X POST "${API_BASE_URL}/api/v1/express/price" \
            -H "Content-Type: application/json" \
            -H "Authorization: Bearer ${TEST_USER_TOKEN}" \
            -d "{
                \"express_code\": \"$variant\",
                \"from_province\": \"广东省\",
                \"from_city\": \"深圳市\",
                \"from_district\": \"南山区\",
                \"to_province\": \"北京市\",
                \"to_city\": \"北京市\",
                \"to_district\": \"朝阳区\",
                \"weight\": 1.0
            }")
        end_time=$(date +%s%N)
        
        # 计算响应时间（毫秒）
        response_time=$(( (end_time - start_time) / 1000000 ))
        
        # 检查是否正确处理
        success=$(echo "$response" | jq -r '.success // false')
        if [ "$success" = "true" ]; then
            log_success "变体 $variant 处理成功，响应时间: ${response_time}ms"
            # 检查是否绕过缓存（响应时间较长）
            if [ "$response_time" -gt 300 ]; then
                log_success "响应时间较长，确认 $variant 绕过了缓存"
            else
                log_warning "响应时间较短，$variant 可能使用了缓存"
            fi
        else
            log_warning "变体 $variant 处理失败或不支持: $(echo "$response" | jq -r '.message // "未知错误"')"
        fi
        
        # 等待1秒避免请求过于频繁
        sleep 1
    done
}

# 测试混合查询（包含京东+其他快递）
test_mixed_query_with_jd() {
    log_info "测试混合查询（京东+其他快递）..."
    
    start_time=$(date +%s%N)
    response=$(curl -s -X POST "${API_BASE_URL}/api/v1/express/price" \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer ${TEST_USER_TOKEN}" \
        -d '{
            "express_codes": ["JD", "ZTO", "YTO"],
            "from_province": "广东省",
            "from_city": "深圳市",
            "from_district": "南山区",
            "to_province": "北京市",
            "to_city": "北京市",
            "to_district": "朝阳区",
            "weight": 1.0
        }')
    end_time=$(date +%s%N)
    
    # 计算响应时间（毫秒）
    response_time=$(( (end_time - start_time) / 1000000 ))
    log_info "混合查询响应时间: ${response_time}ms"
    
    success=$(echo "$response" | jq -r '.success // false')
    if [ "$success" = "true" ]; then
        log_success "混合查询（包含京东）测试成功"
        # 由于包含京东快递，整个请求应该不使用缓存
        if [ "$response_time" -gt 500 ]; then
            log_success "响应时间较长，确认混合查询正确绕过了缓存"
        else
            log_warning "响应时间较短，混合查询可能使用了缓存"
        fi
        
        # 检查返回的快递数量
        data_count=$(echo "$response" | jq '.data | length // 0')
        log_info "返回的快递公司数量: $data_count"
    else
        log_error "混合查询（包含京东）测试失败"
        echo "响应: $response"
    fi
}

# 主函数
main() {
    log_info "开始京东快递缓存功能测试..."
    
    check_service
    get_test_token
    test_jd_express_price
    test_other_express_price
    test_jd_express_variants
    test_mixed_query_with_jd
    
    log_success "所有测试完成"
}

# 执行主函数
main "$@"
