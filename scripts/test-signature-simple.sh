#!/bin/bash

# 简化的签名测试脚本
# 使用手动验证过的签名算法

set -e

# 配置变量
API_URL="http://localhost:8081/api/gateway/execute"
CLIENT_ID="f2Mpl9Lg8JVYPSWFNkPWOzqt"
CLIENT_SECRET="n5bNOPSWsRcd1ntOWPo13fxtpgJ6evbB"

# 生成毫秒级时间戳
generate_millisec_timestamp() {
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS 原生方法
        echo $(($(date +%s) * 1000))
    else
        # Linux
        date +%s%3N
    fi
}

# 测试API调用
test_api_call() {
    local test_type=${1:-"simple"}
    local timestamp=$(generate_millisec_timestamp)

    echo "使用时间戳: $timestamp"
    echo "测试类型: $test_type"

    # 根据测试类型构建不同的业务参数（使用正确的字段名）
    local business_params
    case $test_type in
        "simple")
            business_params='{"from_province":"北京","from_city":"北京市","to_province":"上海","to_city":"上海市","weight":1}'
            ;;
        "complete")
            business_params='{"from_province":"北京","from_city":"北京市","from_district":"朝阳区","to_province":"上海","to_city":"上海市","to_district":"浦东新区","weight":1}'
            ;;
        "detailed")
            business_params='{"from_province":"北京","from_city":"北京市","from_district":"朝阳区","to_province":"上海","to_city":"上海市","to_district":"浦东新区","weight":1,"goods_name":"测试物品","quantity":1}'
            ;;
        "single_jt")
            business_params='{"from_province":"北京","from_city":"北京市","to_province":"上海","to_city":"上海市","weight":1,"express_code":"JT"}'
            ;;
        "single_yd")
            business_params='{"from_province":"北京","from_city":"北京市","to_province":"上海","to_city":"上海市","weight":1,"express_code":"YD"}'
            ;;
        "single_sto")
            business_params='{"from_province":"北京","from_city":"北京市","to_province":"上海","to_city":"上海市","weight":1,"express_code":"STO"}'
            ;;
        *)
            business_params='{"from_province":"北京","from_city":"北京市","to_province":"上海","to_city":"上海市","weight":1}'
            ;;
    esac

    # 构建请求体（紧凑格式，按字母顺序排列字段）
    local request_body='{"apiMethod":"QUERY_PRICE","businessParams":'$business_params',"timestamp":"'$timestamp'","username":"'$CLIENT_ID'"}'

    echo "请求体:"
    echo "$request_body"

    # 对请求体进行Base64编码
    local body_base64=$(echo -n "$request_body" | base64)
    echo "请求体Base64: $body_base64"

    # 构建待签名字符串
    local string_to_sign="client_id=$CLIENT_ID&nonce=$timestamp&timestamp=$timestamp&body=$body_base64"
    echo "待签名字符串: $string_to_sign"

    # 生成签名
    local signature=$(echo -n "$string_to_sign" | openssl dgst -sha256 -hmac "$CLIENT_SECRET" -binary | base64)
    echo "生成的签名: $signature"

    # 构建完整请求数据
    local request_data='{"apiMethod":"QUERY_PRICE","businessParams":'$business_params',"sign":"'$signature'","timestamp":"'$timestamp'","username":"'$CLIENT_ID'"}'

    echo "完整请求数据:"
    echo "$request_data" | jq . 2>/dev/null || echo "$request_data"

    # 发送请求
    echo "发送请求到 $API_URL"
    local response=$(curl -s -X POST "$API_URL" \
        -H "Content-Type: application/json" \
        -d "$request_data")

    echo "服务器响应:"
    echo "$response" | jq . 2>/dev/null || echo "$response"

    return 0
}

# 显示帮助信息
show_help() {
    echo "🚀 统一网关签名测试工具"
    echo ""
    echo "用法: $0 [测试类型]"
    echo ""
    echo "测试类型:"
    echo "  simple     - 简单测试（默认）"
    echo "  complete   - 完整地址测试"
    echo "  detailed   - 详细地址测试"
    echo "  single_jt  - 单快递公司测试（极兔快递）"
    echo "  single_yd  - 单快递公司测试（韵达速递）"
    echo "  single_sto - 单快递公司测试（申通快递）"
    echo "  all        - 运行所有测试"
    echo "  help       - 显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 simple"
    echo "  $0 single_jt"
    echo "  $0 all"
}

# 运行所有测试
run_all_tests() {
    echo "🚀 运行所有签名测试"
    echo "客户端ID: $CLIENT_ID"
    echo "客户端密钥: ${CLIENT_SECRET:0:8}..."
    echo "=========================================="

    echo ""
    echo "📋 测试1: 简单参数测试"
    echo "----------------------------------------"
    test_api_call "simple"

    echo ""
    echo "📋 测试2: 完整地址测试"
    echo "----------------------------------------"
    test_api_call "complete"

    echo ""
    echo "📋 测试3: 详细地址测试"
    echo "----------------------------------------"
    test_api_call "detailed"

    echo ""
    echo "📋 测试4: 单快递公司测试（极兔快递）"
    echo "----------------------------------------"
    test_api_call "single_jt"

    echo ""
    echo "📋 测试5: 单快递公司测试（韵达速递）"
    echo "----------------------------------------"
    test_api_call "single_yd"

    echo ""
    echo "📋 测试6: 单快递公司测试（申通快递）"
    echo "----------------------------------------"
    test_api_call "single_sto"

    echo ""
    echo "✅ 所有测试完成"
}

# 主函数
main() {
    local test_type=${1:-"simple"}

    case $test_type in
        "help"|"-h"|"--help")
            show_help
            exit 0
            ;;
        "all")
            run_all_tests
            ;;
        *)
            echo "🚀 统一网关签名测试"
            echo "客户端ID: $CLIENT_ID"
            echo "客户端密钥: ${CLIENT_SECRET:0:8}..."
            echo "测试类型: $test_type"
            echo "=========================================="

            test_api_call "$test_type"
            ;;
    esac
}

# 执行主函数
main "$@"
