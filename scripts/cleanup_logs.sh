#!/bin/bash

# Go-<PERSON>aidi 日志清理脚本
# 用于清理过大的日志文件，保持系统性能

LOG_DIR="logs"
MAX_LOG_SIZE_MB=100
RETENTION_DAYS=7

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 检查日志目录是否存在
if [[ ! -d "$LOG_DIR" ]]; then
    log_error "日志目录不存在: $LOG_DIR"
    exit 1
fi

log_info "开始清理日志文件..."

# 1. 清理超过大小限制的日志文件
log_info "检查超过 ${MAX_LOG_SIZE_MB}MB 的日志文件..."
find "$LOG_DIR" -name "*.log" -type f -size +${MAX_LOG_SIZE_MB}M | while read -r file; do
    size_mb=$(du -m "$file" | cut -f1)
    log_warn "发现大文件: $file (${size_mb}MB)"
    
    # 压缩大文件
    if [[ ! -f "${file}.gz" ]]; then
        log_info "压缩文件: $file"
        gzip "$file"
    else
        log_warn "压缩文件已存在，删除原文件: $file"
        rm "$file"
    fi
done

# 2. 清理超过保留天数的日志文件
log_info "清理超过 ${RETENTION_DAYS} 天的日志文件..."
find "$LOG_DIR" -name "*.log*" -type f -mtime +${RETENTION_DAYS} | while read -r file; do
    log_info "删除过期文件: $file"
    rm "$file"
done

# 3. 清理空日志文件
log_info "清理空日志文件..."
find "$LOG_DIR" -name "*.log" -type f -empty | while read -r file; do
    log_info "删除空文件: $file"
    rm "$file"
done

# 4. 显示清理结果
log_info "日志清理完成"
log_info "当前日志文件状态:"
ls -lah "$LOG_DIR"/*.log* 2>/dev/null || log_warn "没有找到日志文件"

# 5. 显示磁盘使用情况
log_info "磁盘使用情况:"
du -sh "$LOG_DIR" 2>/dev/null || log_warn "无法获取目录大小"

log_info "日志清理脚本执行完成" 