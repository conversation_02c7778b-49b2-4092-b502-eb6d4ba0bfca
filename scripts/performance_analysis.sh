#!/bin/bash

# Go快递物流系统性能分析脚本
# 用于收集CPU热点、内存使用、Goroutine等性能数据

set -e

# 配置
SERVER_URL="http://localhost:8081"
PPROF_URL="${SERVER_URL}/debug/pprof"
OUTPUT_DIR="./performance_analysis_$(date +%Y%m%d_%H%M%S)"
DURATION="30s"  # CPU采样时间
CONCURRENT_USERS=100  # 并发用户数
TEST_DURATION="60s"   # 压力测试持续时间

echo "🚀 Go快递物流系统性能分析开始"
echo "时间: $(date)"
echo "输出目录: $OUTPUT_DIR"
echo ""

# 创建输出目录
mkdir -p "$OUTPUT_DIR"

# 检查服务是否运行
echo "📊 检查服务状态..."
if ! curl -s "$SERVER_URL/health" > /dev/null; then
    echo "❌ 服务未运行或无法访问: $SERVER_URL"
    echo "请确保服务已启动并监听在8081端口"
    exit 1
fi
echo "✅ 服务运行正常"
echo ""

# 1. 收集基础系统信息
echo "📋 收集系统信息..."
{
    echo "=== 系统信息 ==="
    echo "时间: $(date)"
    echo "主机: $(hostname)"
    echo "操作系统: $(uname -a)"
    echo ""
    
    echo "=== CPU信息 ==="
    if command -v lscpu &> /dev/null; then
        lscpu | grep -E "^CPU\(s\)|^Model name|^CPU MHz"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        sysctl -n machdep.cpu.brand_string
        sysctl -n hw.ncpu
    fi
    echo ""
    
    echo "=== 内存信息 ==="
    if command -v free &> /dev/null; then
        free -h
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        echo "总内存: $(sysctl -n hw.memsize | awk '{print $1/1024/1024/1024 " GB"}')"
    fi
    echo ""
    
    echo "=== 进程信息 ==="
    if pgrep -f "go-kuaidi" > /dev/null; then
        PID=$(pgrep -f "go-kuaidi")
        echo "进程ID: $PID"
        # 使用简单的ps命令格式，兼容macOS和Linux
        ps -p $PID || echo "无法获取进程详细信息"
    else
        echo "未找到go-kuaidi进程"
    fi
    echo ""
} > "$OUTPUT_DIR/system_info.txt"

# 2. 收集服务性能指标
echo "📊 收集服务性能指标..."
curl -s "$SERVER_URL/performance/metrics" | jq '.' > "$OUTPUT_DIR/service_metrics.json" 2>/dev/null || \
curl -s "$SERVER_URL/performance/metrics" > "$OUTPUT_DIR/service_metrics.json"

curl -s "$SERVER_URL/performance/system" | jq '.' > "$OUTPUT_DIR/system_metrics.json" 2>/dev/null || \
curl -s "$SERVER_URL/performance/system" > "$OUTPUT_DIR/system_metrics.json"

# 3. 收集pprof数据（基础采样）
echo "🔍 收集pprof基础数据..."

# CPU profile
echo "  - CPU profile (${DURATION})..."
curl -s "${PPROF_URL}/profile?seconds=30" > "$OUTPUT_DIR/cpu_profile.pprof"

# Heap profile
echo "  - Heap profile..."
curl -s "${PPROF_URL}/heap" > "$OUTPUT_DIR/heap_profile.pprof"

# Goroutine profile
echo "  - Goroutine profile..."
curl -s "${PPROF_URL}/goroutine" > "$OUTPUT_DIR/goroutine_profile.pprof"

# Allocs profile
echo "  - Allocs profile..."
curl -s "${PPROF_URL}/allocs" > "$OUTPUT_DIR/allocs_profile.pprof"

# Block profile
echo "  - Block profile..."
curl -s "${PPROF_URL}/block" > "$OUTPUT_DIR/block_profile.pprof"

# Mutex profile
echo "  - Mutex profile..."
curl -s "${PPROF_URL}/mutex" > "$OUTPUT_DIR/mutex_profile.pprof"

# 4. 进行压力测试并收集性能数据
echo "🚀 开始压力测试 (${CONCURRENT_USERS}并发用户, ${TEST_DURATION})..."

# 检查是否安装了ab或wrk
if command -v wrk &> /dev/null; then
    echo "使用wrk进行压力测试..."
    
    # 测试价格查询接口
    echo "  - 测试价格查询接口..."
    wrk -t10 -c${CONCURRENT_USERS} -d${TEST_DURATION} \
        -s scripts/wrk_price_query.lua \
        --latency \
        "$SERVER_URL" > "$OUTPUT_DIR/price_query_load_test.txt" 2>&1 &
    PRICE_TEST_PID=$!
    
    # 测试订单列表接口
    echo "  - 测试订单列表接口..."
    wrk -t10 -c${CONCURRENT_USERS} -d${TEST_DURATION} \
        -s scripts/wrk_order_list.lua \
        --latency \
        "$SERVER_URL" > "$OUTPUT_DIR/order_list_load_test.txt" 2>&1 &
    ORDER_TEST_PID=$!
    
    # 等待压力测试完成
    wait $PRICE_TEST_PID $ORDER_TEST_PID
    
elif command -v ab &> /dev/null; then
    echo "使用Apache Bench进行压力测试..."
    
    # 测试健康检查接口
    ab -n 10000 -c ${CONCURRENT_USERS} -g "$OUTPUT_DIR/health_check_gnuplot.dat" \
       "$SERVER_URL/health" > "$OUTPUT_DIR/health_check_load_test.txt" 2>&1 &
    
    # 测试性能指标接口
    ab -n 5000 -c 50 -g "$OUTPUT_DIR/metrics_gnuplot.dat" \
       "$SERVER_URL/performance/metrics" > "$OUTPUT_DIR/metrics_load_test.txt" 2>&1 &
    
    wait
else
    echo "⚠️  未找到wrk或ab工具，跳过压力测试"
    echo "建议安装wrk: brew install wrk (macOS) 或 apt-get install wrk (Ubuntu)"
fi

# 5. 在压力测试期间收集CPU profile
echo "🔍 在负载下收集CPU profile..."
curl -s "${PPROF_URL}/profile?seconds=30" > "$OUTPUT_DIR/cpu_profile_under_load.pprof" &
PROFILE_PID=$!

# 等待profile收集完成
wait $PROFILE_PID

# 6. 收集压力测试后的性能指标
echo "📊 收集压力测试后的性能指标..."
curl -s "$SERVER_URL/performance/metrics" | jq '.' > "$OUTPUT_DIR/service_metrics_after_load.json" 2>/dev/null || \
curl -s "$SERVER_URL/performance/metrics" > "$OUTPUT_DIR/service_metrics_after_load.json"

# 7. 分析pprof数据（如果安装了go工具）
if command -v go &> /dev/null; then
    echo "🔍 分析pprof数据..."
    
    # 分析CPU profile
    echo "  - 分析CPU热点..."
    go tool pprof -text -cum "$OUTPUT_DIR/cpu_profile.pprof" > "$OUTPUT_DIR/cpu_analysis.txt" 2>/dev/null || true
    go tool pprof -text -cum "$OUTPUT_DIR/cpu_profile_under_load.pprof" > "$OUTPUT_DIR/cpu_analysis_under_load.txt" 2>/dev/null || true
    
    # 分析内存使用
    echo "  - 分析内存使用..."
    go tool pprof -text -cum "$OUTPUT_DIR/heap_profile.pprof" > "$OUTPUT_DIR/heap_analysis.txt" 2>/dev/null || true
    
    # 分析Goroutine
    echo "  - 分析Goroutine..."
    go tool pprof -text "$OUTPUT_DIR/goroutine_profile.pprof" > "$OUTPUT_DIR/goroutine_analysis.txt" 2>/dev/null || true
    
    # 生成火焰图（如果支持）
    echo "  - 尝试生成火焰图..."
    go tool pprof -svg "$OUTPUT_DIR/cpu_profile.pprof" > "$OUTPUT_DIR/cpu_flamegraph.svg" 2>/dev/null || true
    go tool pprof -svg "$OUTPUT_DIR/heap_profile.pprof" > "$OUTPUT_DIR/heap_flamegraph.svg" 2>/dev/null || true
else
    echo "⚠️  未找到go工具，跳过pprof数据分析"
fi

# 8. 生成性能分析报告
echo "📝 生成性能分析报告..."
{
    echo "# Go快递物流系统性能分析报告"
    echo "生成时间: $(date)"
    echo ""
    
    echo "## 测试配置"
    echo "- 服务地址: $SERVER_URL"
    echo "- 并发用户数: $CONCURRENT_USERS"
    echo "- 测试持续时间: $TEST_DURATION"
    echo "- CPU采样时间: $DURATION"
    echo ""
    
    echo "## 文件说明"
    echo "- system_info.txt: 系统基础信息"
    echo "- service_metrics.json: 服务性能指标（测试前）"
    echo "- service_metrics_after_load.json: 服务性能指标（测试后）"
    echo "- cpu_profile.pprof: CPU性能数据（基础）"
    echo "- cpu_profile_under_load.pprof: CPU性能数据（负载下）"
    echo "- heap_profile.pprof: 内存使用数据"
    echo "- goroutine_profile.pprof: Goroutine数据"
    echo "- *_analysis.txt: pprof分析结果"
    echo "- *_load_test.txt: 压力测试结果"
    echo ""
    
    echo "## 快速分析命令"
    echo "```bash"
    echo "# 查看CPU热点"
    echo "go tool pprof -text -cum $OUTPUT_DIR/cpu_profile.pprof"
    echo ""
    echo "# 交互式分析CPU"
    echo "go tool pprof $OUTPUT_DIR/cpu_profile.pprof"
    echo ""
    echo "# 查看内存使用"
    echo "go tool pprof -text -cum $OUTPUT_DIR/heap_profile.pprof"
    echo ""
    echo "# 查看Goroutine"
    echo "go tool pprof -text $OUTPUT_DIR/goroutine_profile.pprof"
    echo ""
    echo "# 生成Web界面"
    echo "go tool pprof -http=:8080 $OUTPUT_DIR/cpu_profile.pprof"
    echo "```"
    echo ""
    
    if [[ -f "$OUTPUT_DIR/cpu_analysis.txt" ]]; then
        echo "## CPU热点分析（Top 10）"
        echo "```"
        head -20 "$OUTPUT_DIR/cpu_analysis.txt" 2>/dev/null || echo "分析文件不存在"
        echo "```"
        echo ""
    fi
    
    if [[ -f "$OUTPUT_DIR/heap_analysis.txt" ]]; then
        echo "## 内存使用分析（Top 10）"
        echo "```"
        head -20 "$OUTPUT_DIR/heap_analysis.txt" 2>/dev/null || echo "分析文件不存在"
        echo "```"
        echo ""
    fi
    
} > "$OUTPUT_DIR/README.md"

echo ""
echo "✅ 性能分析完成！"
echo "📁 结果保存在: $OUTPUT_DIR"
echo "📝 查看报告: cat $OUTPUT_DIR/README.md"
echo ""
echo "🔍 建议的下一步操作:"
echo "1. 查看CPU热点: go tool pprof -text -cum $OUTPUT_DIR/cpu_profile.pprof"
echo "2. 交互式分析: go tool pprof $OUTPUT_DIR/cpu_profile.pprof"
echo "3. Web界面分析: go tool pprof -http=:8080 $OUTPUT_DIR/cpu_profile.pprof"
echo "4. 查看压力测试结果: ls $OUTPUT_DIR/*_load_test.txt"
