#!/bin/bash

# Go-<PERSON><PERSON>i 性能测试脚本
# 测试8核32G服务器的订单处理能力

echo "🚀 Go-Kuaidi 性能测试开始"
echo "服务器配置: 8核32G"
echo "测试时间: $(date)"
echo "================================"

# 配置
API_BASE="http://localhost:8081"
ADMIN_TOKEN=""  # 需要先获取管理员token

# 1. 获取管理员token
echo "📝 获取管理员token..."
LOGIN_RESPONSE=$(curl -s -X POST "${API_BASE}/api/v1/admin/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"password"}')

ADMIN_TOKEN=$(echo $LOGIN_RESPONSE | jq -r '.data.token')
if [ "$ADMIN_TOKEN" = "null" ]; then
    echo "❌ 获取管理员token失败"
    exit 1
fi
echo "✅ 管理员token获取成功"

# 2. 基础功能测试
echo ""
echo "🔍 基础功能测试..."

# 测试供应商列表
echo "测试供应商列表API..."
curl -s -H "Authorization: Bearer $ADMIN_TOKEN" \
  "${API_BASE}/api/v1/admin/express/providers" | jq '.success'

# 测试系统配置
echo "测试系统配置API..."
curl -s -H "Authorization: Bearer $ADMIN_TOKEN" \
  "${API_BASE}/api/v1/admin/system-configs" | jq '.success'

# 3. 并发测试
echo ""
echo "⚡ 并发性能测试..."

# 使用Apache Bench进行并发测试
if command -v ab &> /dev/null; then
    echo "使用Apache Bench进行测试..."
    
    # 轻负载测试: 10并发，100请求
    echo "轻负载测试 (10并发, 100请求):"
    ab -n 100 -c 10 -H "Authorization: Bearer $ADMIN_TOKEN" \
       "${API_BASE}/api/v1/admin/express/providers"
    
    # 中负载测试: 50并发，500请求
    echo "中负载测试 (50并发, 500请求):"
    ab -n 500 -c 50 -H "Authorization: Bearer $ADMIN_TOKEN" \
       "${API_BASE}/api/v1/admin/express/providers"
    
    # 高负载测试: 100并发，1000请求
    echo "高负载测试 (100并发, 1000请求):"
    ab -n 1000 -c 100 -H "Authorization: Bearer $ADMIN_TOKEN" \
       "${API_BASE}/api/v1/admin/express/providers"
       
elif command -v wrk &> /dev/null; then
    echo "使用wrk进行测试..."
    
    # 30秒测试，10个连接
    echo "轻负载测试 (10连接, 30秒):"
    wrk -t10 -c10 -d30s -H "Authorization: Bearer $ADMIN_TOKEN" \
        "${API_BASE}/api/v1/admin/express/providers"
    
    # 30秒测试，50个连接
    echo "中负载测试 (50连接, 30秒):"
    wrk -t10 -c50 -d30s -H "Authorization: Bearer $ADMIN_TOKEN" \
        "${API_BASE}/api/v1/admin/express/providers"
    
    # 30秒测试，100个连接
    echo "高负载测试 (100连接, 30秒):"
    wrk -t10 -c100 -d30s -H "Authorization: Bearer $ADMIN_TOKEN" \
        "${API_BASE}/api/v1/admin/express/providers"
else
    echo "⚠️  未找到ab或wrk工具，跳过并发测试"
    echo "安装方法:"
    echo "  macOS: brew install wrk"
    echo "  Ubuntu: apt-get install apache2-utils"
fi

# 4. 数据库性能检查
echo ""
echo "🗄️  数据库性能检查..."

# 检查数据库连接数
echo "当前数据库连接数:"
PGPASSWORD=gjx6ngf4 psql -h 8.138.252.193 -p 5432 -U postgres -d go_kuaidi -t -c \
  "SELECT count(*) FROM pg_stat_activity WHERE state = 'active';"

# 检查慢查询
echo "最近的慢查询:"
PGPASSWORD=gjx6ngf4 psql -h 8.138.252.193 -p 5432 -U postgres -d go_kuaidi -c \
  "SELECT query, mean_exec_time, calls FROM pg_stat_statements ORDER BY mean_exec_time DESC LIMIT 5;" 2>/dev/null || echo "pg_stat_statements未启用"

# 5. Redis性能检查
echo ""
echo "📦 Redis性能检查..."
redis-cli -h 8.138.252.193 -p 6379 -a a63006320 --latency-history -i 1 &
REDIS_PID=$!
sleep 5
kill $REDIS_PID 2>/dev/null

# 6. 系统资源监控
echo ""
echo "💻 系统资源使用情况..."
echo "CPU使用率:"
top -l 1 | grep "CPU usage" || echo "无法获取CPU信息"

echo "内存使用情况:"
free -h 2>/dev/null || vm_stat | head -10

echo ""
echo "🎯 性能测试完成!"
echo "================================"
echo "建议监控指标:"
echo "- QPS (每秒请求数)"
echo "- 响应时间 (平均/P95/P99)"
echo "- 数据库连接数"
echo "- CPU和内存使用率"
echo "- 错误率"
