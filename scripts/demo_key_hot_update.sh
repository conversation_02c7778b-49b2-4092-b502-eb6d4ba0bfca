#!/bin/bash

# 密钥热更新演示脚本
# 演示如何在不停机的情况下更新供应商API密钥

set -e

echo "🔑 密钥热更新演示"
echo "=================="

# 配置
ADMIN_TOKEN="your_admin_token_here"
API_BASE="http://localhost:8081/api/v1"
PROVIDER="kuaidi100"

echo "1. 查看当前供应商状态"
curl -s -H "Authorization: Bearer $ADMIN_TOKEN" \
     "$API_BASE/admin/providers/status" | jq .

echo -e "\n2. 模拟密钥更新（在数据库中）"
echo "SQL: UPDATE system_configs SET config_value = 'new_api_key_12345' WHERE config_key = 'kuaidi100.api_key';"
echo "SQL: UPDATE system_configs SET config_value = 'new_key_id_67890' WHERE config_key = 'kuaidi100.key_id';"

echo -e "\n3. 触发热重载，使新密钥生效"
RELOAD_RESULT=$(curl -s -X POST \
     -H "Authorization: Bearer $ADMIN_TOKEN" \
     "$API_BASE/admin/providers/$PROVIDER/reload")

echo "重载结果:"
echo "$RELOAD_RESULT" | jq .

echo -e "\n4. 验证重载后的状态"
curl -s -H "Authorization: Bearer $ADMIN_TOKEN" \
     "$API_BASE/admin/providers/status" | jq .

echo -e "\n5. 查看重载指标"
curl -s -H "Authorization: Bearer $ADMIN_TOKEN" \
     "$API_BASE/admin/providers/metrics" | jq .

echo -e "\n✅ 密钥热更新完成！"
echo "新密钥已生效，服务未中断。"
