#!/bin/bash

# 生产环境启动脚本 - 企业级nonce管理系统
# 版本: 7.4.00.21
# 更新时间: 2025-01-08

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

# 配置变量
APP_NAME="go-kuaidi"
APP_VERSION="7.4.00.21"
BUILD_DIR="./build"
CONFIG_FILE="./config/config.yaml"
LOG_DIR="./logs"
PID_FILE="./logs/${APP_NAME}.pid"

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    # 检查Go版本
    if ! command -v go &> /dev/null; then
        log_error "Go未安装，请先安装Go 1.19+"
        exit 1
    fi
    
    local go_version=$(go version | awk '{print $3}' | sed 's/go//')
    log_info "Go版本: $go_version"
    
    # 检查Redis连接
    if ! command -v redis-cli &> /dev/null; then
        log_warn "redis-cli未安装，无法测试Redis连接"
    else
        log_info "测试Redis连接..."
        if redis-cli -h 8.138.252.193 -p 6379 -a a63006320 ping > /dev/null 2>&1; then
            log_info "✅ Redis连接正常"
        else
            log_error "❌ Redis连接失败"
            exit 1
        fi
    fi
    
    # 检查PostgreSQL连接
    if command -v psql &> /dev/null; then
        log_info "测试PostgreSQL连接..."
        if PGPASSWORD=gjx6ngf4 psql -h 8.138.252.193 -p 5432 -U postgres -d go_kuaidi -c "SELECT 1;" > /dev/null 2>&1; then
            log_info "✅ PostgreSQL连接正常"
        else
            log_error "❌ PostgreSQL连接失败"
            exit 1
        fi
    else
        log_warn "psql未安装，无法测试PostgreSQL连接"
    fi
}

# 创建必要目录
create_directories() {
    log_info "创建必要目录..."
    
    mkdir -p "$BUILD_DIR"
    mkdir -p "$LOG_DIR"
    mkdir -p "./uploads"
    mkdir -p "./keys"
    
    log_info "✅ 目录创建完成"
}

# 构建应用
build_application() {
    log_info "构建应用程序..."
    
    # 设置构建环境变量
    export CGO_ENABLED=0
    export GOOS=linux
    export GOARCH=amd64
    
    # 构建主应用
    log_info "构建主应用..."
    go build -ldflags="-w -s -X main.Version=$APP_VERSION -X main.BuildTime=$(date -u +%Y-%m-%dT%H:%M:%SZ)" \
        -o "$BUILD_DIR/$APP_NAME" ./cmd/main.go
    
    if [ $? -eq 0 ]; then
        log_info "✅ 主应用构建成功"
    else
        log_error "❌ 主应用构建失败"
        exit 1
    fi
    
    # 构建API客户端工具
    log_info "构建API客户端工具..."
    go build -ldflags="-w -s" -o "$BUILD_DIR/${APP_NAME}-client" ./cmd/apiclient/main.go
    
    if [ $? -eq 0 ]; then
        log_info "✅ API客户端工具构建成功"
    else
        log_warn "⚠️ API客户端工具构建失败"
    fi
}

# 验证配置文件
validate_config() {
    log_info "验证配置文件..."
    
    if [ ! -f "$CONFIG_FILE" ]; then
        log_error "配置文件不存在: $CONFIG_FILE"
        exit 1
    fi
    
    # 检查关键配置项
    if ! grep -q "nonce:" "$CONFIG_FILE"; then
        log_error "配置文件缺少nonce配置"
        exit 1
    fi
    
    if ! grep -q "signature:" "$CONFIG_FILE"; then
        log_error "配置文件缺少signature配置"
        exit 1
    fi
    
    log_info "✅ 配置文件验证通过"
}

# 生成RSA密钥对
generate_keys() {
    log_info "检查RSA密钥对..."
    
    if [ ! -f "./keys/private.pem" ] || [ ! -f "./keys/public.pem" ]; then
        log_info "生成RSA密钥对..."
        
        # 生成私钥
        openssl genpkey -algorithm RSA -out ./keys/private.pem -pkcs8 -pass pass:
        
        # 生成公钥
        openssl rsa -pubout -in ./keys/private.pem -out ./keys/public.pem
        
        # 设置权限
        chmod 600 ./keys/private.pem
        chmod 644 ./keys/public.pem
        
        log_info "✅ RSA密钥对生成完成"
    else
        log_info "✅ RSA密钥对已存在"
    fi
}

# 停止现有进程
stop_existing() {
    log_info "检查现有进程..."
    
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            log_info "停止现有进程 (PID: $pid)..."
            kill -TERM "$pid"
            
            # 等待进程优雅关闭
            local count=0
            while ps -p "$pid" > /dev/null 2>&1 && [ $count -lt 30 ]; do
                sleep 1
                count=$((count + 1))
            done
            
            # 如果进程仍在运行，强制杀死
            if ps -p "$pid" > /dev/null 2>&1; then
                log_warn "强制杀死进程 (PID: $pid)"
                kill -KILL "$pid"
            fi
            
            log_info "✅ 现有进程已停止"
        fi
        rm -f "$PID_FILE"
    fi
}

# 启动应用
start_application() {
    log_info "启动应用程序..."
    
    # 设置环境变量
    export GIN_MODE=release
    export TZ=Asia/Shanghai
    
    # 启动应用
    nohup "$BUILD_DIR/$APP_NAME" \
        --config="$CONFIG_FILE" \
        --log-level=info \
        --log-format=json \
        > "$LOG_DIR/app.log" 2>&1 &
    
    local pid=$!
    echo "$pid" > "$PID_FILE"
    
    # 等待应用启动
    sleep 3
    
    # 检查进程是否正在运行
    if ps -p "$pid" > /dev/null 2>&1; then
        log_info "✅ 应用启动成功 (PID: $pid)"
        
        # 检查健康状态
        log_info "检查应用健康状态..."
        local count=0
        while [ $count -lt 30 ]; do
            if curl -s http://localhost:8080/health > /dev/null 2>&1; then
                log_info "✅ 应用健康检查通过"
                break
            fi
            sleep 1
            count=$((count + 1))
        done
        
        if [ $count -eq 30 ]; then
            log_warn "⚠️ 应用健康检查超时，请检查日志"
        fi
    else
        log_error "❌ 应用启动失败"
        exit 1
    fi
}

# 显示状态信息
show_status() {
    log_info "应用状态信息:"
    echo "----------------------------------------"
    echo "应用名称: $APP_NAME"
    echo "版本: $APP_VERSION"
    echo "PID文件: $PID_FILE"
    echo "日志目录: $LOG_DIR"
    echo "配置文件: $CONFIG_FILE"
    echo "----------------------------------------"
    
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            echo "状态: 运行中 (PID: $pid)"
            echo "内存使用: $(ps -p $pid -o rss= | awk '{print $1/1024 " MB"}')"
            echo "启动时间: $(ps -p $pid -o lstart= | sed 's/^ *//')"
        else
            echo "状态: 已停止"
        fi
    else
        echo "状态: 未运行"
    fi
    
    echo "----------------------------------------"
    echo "API端点:"
    echo "  健康检查: http://localhost:8080/health"
    echo "  API文档: http://localhost:8080/swagger/index.html"
    echo "  nonce统计: http://localhost:8080/api/admin/nonce/stats"
    echo "----------------------------------------"
}

# 主函数
main() {
    log_info "🚀 启动 $APP_NAME v$APP_VERSION (企业级nonce管理系统)"
    
    check_dependencies
    create_directories
    validate_config
    generate_keys
    build_application
    stop_existing
    start_application
    show_status
    
    log_info "🎉 应用启动完成！"
    log_info "📝 查看日志: tail -f $LOG_DIR/app.log"
    log_info "🔍 监控nonce: curl http://localhost:8080/api/admin/nonce/stats"
}

# 执行主函数
main "$@"
