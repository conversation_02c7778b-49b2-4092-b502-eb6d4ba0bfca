#!/bin/bash

# 快递公司管理功能性能监控脚本
# 用途：监控快递公司状态变更对系统性能的影响
# 作者：测试团队
# 版本：v1.0

set -e

# ==================== 配置区域 ====================

# 服务配置
BASE_URL="http://localhost:8081"
ADMIN_USERNAME="admin"
ADMIN_PASSWORD="1104030777+.aA..@"

# 监控配置
MONITOR_DURATION=300  # 监控时长（秒）
SAMPLE_INTERVAL=5     # 采样间隔（秒）
CONCURRENT_REQUESTS=10 # 并发请求数
LOG_FILE="performance_monitor_$(date +%Y%m%d_%H%M%S).log"

# 颜色输出配置
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 全局变量
ADMIN_TOKEN=""
USER_TOKEN=""
MONITOR_PID=""

# ==================== 工具函数 ====================

log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%H:%M:%S') $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%H:%M:%S') $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%H:%M:%S') $1"
}

log_monitor() {
    echo -e "${BLUE}[MONITOR]${NC} $(date '+%H:%M:%S') $1"
}

# 检查依赖
check_dependencies() {
    local deps=("curl" "jq" "bc" "ps" "top")
    
    for dep in "${deps[@]}"; do
        if ! command -v "$dep" &> /dev/null; then
            log_error "依赖 $dep 未找到，请先安装"
            exit 1
        fi
    done
    
    log_info "依赖检查通过"
}

# 认证
authenticate() {
    log_info "正在进行身份认证..."
    
    # 管理员登录
    local admin_response=$(curl -s -X POST "$BASE_URL/api/v1/admin/auth/login" \
        -H "Content-Type: application/json" \
        -d "{\"username\":\"$ADMIN_USERNAME\",\"password\":\"$ADMIN_PASSWORD\"}")
    
    ADMIN_TOKEN=$(echo "$admin_response" | jq -r '.data.token // empty')
    
    if [ -z "$ADMIN_TOKEN" ]; then
        log_error "管理员认证失败"
        exit 1
    fi
    
    # 用户登录
    local user_response=$(curl -s -X POST "$BASE_URL/api/v1/auth/login" \
        -H "Content-Type: application/json" \
        -d "{\"username\":\"$ADMIN_USERNAME\",\"password\":\"$ADMIN_PASSWORD\"}")
    
    USER_TOKEN=$(echo "$user_response" | jq -r '.data.token // empty')
    
    if [ -z "$USER_TOKEN" ]; then
        log_error "用户认证失败"
        exit 1
    fi
    
    log_info "身份认证成功"
}

# ==================== 性能监控函数 ====================

# 获取系统资源使用情况
get_system_metrics() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    # CPU使用率
    local cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1 | tr -d ' ')
    
    # 内存使用率
    local memory_info=$(free | grep Mem)
    local total_mem=$(echo "$memory_info" | awk '{print $2}')
    local used_mem=$(echo "$memory_info" | awk '{print $3}')
    local memory_usage=$(echo "scale=2; $used_mem * 100 / $total_mem" | bc)
    
    # 获取Go进程的资源使用
    local go_pid=$(pgrep -f "go-kuaidi-local" | head -1)
    local go_cpu=0
    local go_memory=0
    
    if [ -n "$go_pid" ]; then
        local go_stats=$(ps -p "$go_pid" -o %cpu,%mem --no-headers 2>/dev/null || echo "0 0")
        go_cpu=$(echo "$go_stats" | awk '{print $1}')
        go_memory=$(echo "$go_stats" | awk '{print $2}')
    fi
    
    echo "$timestamp,CPU:${cpu_usage}%,Memory:${memory_usage}%,Go-CPU:${go_cpu}%,Go-Memory:${go_memory}%"
}

# 测试API响应时间
test_api_response_time() {
    local start_time=$(date +%s%N)
    
    local response=$(curl -s -w "%{http_code},%{time_total}" -X POST "$BASE_URL/api/v1/express/price" \
        -H "Authorization: Bearer $USER_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
            "sender": {"province": "广东省", "city": "深圳市", "area": "南山区"},
            "receiver": {"province": "北京市", "city": "北京市", "area": "朝阳区"},
            "package": {"weight": 1.0, "length": 10, "width": 10, "height": 10},
            "query_all_companies": true
        }')
    
    local http_code=$(echo "$response" | tail -1 | cut -d',' -f1)
    local response_time=$(echo "$response" | tail -1 | cut -d',' -f2)
    
    # 转换为毫秒
    local response_time_ms=$(echo "$response_time * 1000" | bc | cut -d'.' -f1)
    
    echo "$http_code,$response_time_ms"
}

# 并发性能测试
concurrent_performance_test() {
    local test_duration=$1
    local concurrent_count=$2
    
    log_monitor "开始并发性能测试: ${concurrent_count}并发, ${test_duration}秒"
    
    local temp_dir=$(mktemp -d)
    local end_time=$(($(date +%s) + test_duration))
    
    # 启动并发请求
    for ((i=1; i<=concurrent_count; i++)); do
        {
            local request_count=0
            local success_count=0
            local total_response_time=0
            
            while [ $(date +%s) -lt $end_time ]; do
                local result=$(test_api_response_time)
                local http_code=$(echo "$result" | cut -d',' -f1)
                local response_time=$(echo "$result" | cut -d',' -f2)
                
                ((request_count++))
                
                if [ "$http_code" = "200" ]; then
                    ((success_count++))
                    total_response_time=$((total_response_time + response_time))
                fi
                
                sleep 0.1  # 避免过于频繁的请求
            done
            
            echo "$request_count,$success_count,$total_response_time" > "$temp_dir/worker_$i.result"
        } &
    done
    
    # 等待所有并发请求完成
    wait
    
    # 汇总结果
    local total_requests=0
    local total_success=0
    local total_time=0
    
    for ((i=1; i<=concurrent_count; i++)); do
        if [ -f "$temp_dir/worker_$i.result" ]; then
            local worker_result=$(cat "$temp_dir/worker_$i.result")
            local worker_requests=$(echo "$worker_result" | cut -d',' -f1)
            local worker_success=$(echo "$worker_result" | cut -d',' -f2)
            local worker_time=$(echo "$worker_result" | cut -d',' -f3)
            
            total_requests=$((total_requests + worker_requests))
            total_success=$((total_success + worker_success))
            total_time=$((total_time + worker_time))
        fi
    done
    
    # 计算指标
    local success_rate=0
    local avg_response_time=0
    local qps=0
    
    if [ $total_requests -gt 0 ]; then
        success_rate=$(echo "scale=2; $total_success * 100 / $total_requests" | bc)
        qps=$(echo "scale=2; $total_requests / $test_duration" | bc)
    fi
    
    if [ $total_success -gt 0 ]; then
        avg_response_time=$(echo "scale=2; $total_time / $total_success" | bc)
    fi
    
    log_monitor "并发测试结果: 总请求=$total_requests, 成功=$total_success, 成功率=${success_rate}%, 平均响应时间=${avg_response_time}ms, QPS=${qps}"
    
    # 清理临时文件
    rm -rf "$temp_dir"
    
    # 返回结果（用于判断是否通过）
    local pass=1
    if (( $(echo "$success_rate < 99" | bc -l) )); then
        pass=0
    fi
    if (( $(echo "$avg_response_time > 200" | bc -l) )); then
        pass=0
    fi
    
    return $pass
}

# 系统资源监控
system_resource_monitor() {
    log_monitor "开始系统资源监控，持续 $MONITOR_DURATION 秒"
    
    local monitor_log="system_metrics_$(date +%Y%m%d_%H%M%S).csv"
    echo "Timestamp,CPU,Memory,Go-CPU,Go-Memory" > "$monitor_log"
    
    local end_time=$(($(date +%s) + MONITOR_DURATION))
    
    while [ $(date +%s) -lt $end_time ]; do
        local metrics=$(get_system_metrics)
        echo "$metrics" >> "$monitor_log"
        echo "$metrics"
        sleep $SAMPLE_INTERVAL
    done
    
    log_monitor "系统资源监控完成，日志保存到: $monitor_log"
}

# 快递公司状态变更性能影响测试
status_change_impact_test() {
    log_monitor "开始快递公司状态变更性能影响测试"
    
    # 获取申通快递信息
    local company_response=$(curl -s -X GET "$BASE_URL/api/v1/admin/express/companies" \
        -H "Authorization: Bearer $ADMIN_TOKEN")
    
    local sto_info=$(echo "$company_response" | jq -r '.data.companies[] | select(.code=="STO")')
    local sto_id=$(echo "$sto_info" | jq -r '.id')
    local original_status=$(echo "$sto_info" | jq -r '.is_active')
    
    if [ -z "$sto_id" ]; then
        log_error "未找到申通快递公司信息"
        return 1
    fi
    
    log_monitor "申通快递当前状态: $original_status"
    
    # 基准性能测试
    log_monitor "执行基准性能测试..."
    local baseline_pass=0
    if concurrent_performance_test 30 5; then
        baseline_pass=1
    fi
    
    # 执行状态变更
    log_monitor "执行快递公司状态变更..."
    local new_status="false"
    if [ "$original_status" = "false" ]; then
        new_status="true"
    fi
    
    curl -s -X PUT "$BASE_URL/api/v1/admin/express/companies/$sto_id" \
        -H "Authorization: Bearer $ADMIN_TOKEN" \
        -H "Content-Type: application/json" \
        -d "{
            \"name\": \"申通快递\",
            \"is_active\": $new_status,
            \"sort_order\": 0,
            \"volume_weight_ratio\": 8000
        }" > /dev/null
    
    log_monitor "状态变更完成: $original_status -> $new_status"
    
    # 等待配置生效
    sleep 5
    
    # 状态变更后性能测试
    log_monitor "执行状态变更后性能测试..."
    local after_change_pass=0
    if concurrent_performance_test 30 5; then
        after_change_pass=1
    fi
    
    # 恢复原始状态
    log_monitor "恢复原始状态..."
    curl -s -X PUT "$BASE_URL/api/v1/admin/express/companies/$sto_id" \
        -H "Authorization: Bearer $ADMIN_TOKEN" \
        -H "Content-Type: application/json" \
        -d "{
            \"name\": \"申通快递\",
            \"is_active\": $original_status,
            \"sort_order\": 0,
            \"volume_weight_ratio\": 8000
        }" > /dev/null
    
    # 评估结果
    if [ $baseline_pass -eq 1 ] && [ $after_change_pass -eq 1 ]; then
        log_monitor "✅ 状态变更性能影响测试通过"
        return 0
    else
        log_monitor "❌ 状态变更性能影响测试失败"
        return 1
    fi
}

# ==================== 主函数 ====================

cleanup() {
    if [ -n "$MONITOR_PID" ]; then
        kill "$MONITOR_PID" 2>/dev/null || true
    fi
    log_info "监控清理完成"
}

trap cleanup EXIT

main() {
    echo "========================================"
    echo "快递公司管理功能性能监控"
    echo "========================================"
    echo "监控时间: $(date)"
    echo "监控时长: $MONITOR_DURATION 秒"
    echo "采样间隔: $SAMPLE_INTERVAL 秒"
    echo "并发请求: $CONCURRENT_REQUESTS"
    echo "日志文件: $LOG_FILE"
    echo "========================================"
    
    # 检查依赖和认证
    check_dependencies
    authenticate
    
    # 创建日志文件
    {
        echo "========================================"
        echo "性能监控开始: $(date)"
        echo "========================================"
        
        # 执行各项监控测试
        echo "1. 系统资源监控"
        system_resource_monitor &
        MONITOR_PID=$!
        
        sleep 10  # 让系统监控先运行一段时间
        
        echo "2. 快递公司状态变更性能影响测试"
        if status_change_impact_test; then
            echo "状态变更性能测试: PASS"
        else
            echo "状态变更性能测试: FAIL"
        fi
        
        echo "3. 高并发性能测试"
        if concurrent_performance_test 60 $CONCURRENT_REQUESTS; then
            echo "高并发性能测试: PASS"
        else
            echo "高并发性能测试: FAIL"
        fi
        
        # 等待系统监控完成
        wait $MONITOR_PID
        MONITOR_PID=""
        
        echo "========================================"
        echo "性能监控完成: $(date)"
        echo "========================================"
        
    } | tee "$LOG_FILE"
    
    log_info "性能监控完成，详细日志请查看: $LOG_FILE"
}

# 执行主函数
main "$@"
