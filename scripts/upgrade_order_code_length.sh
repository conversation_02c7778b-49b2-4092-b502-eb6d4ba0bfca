#!/bin/bash

# 升级order_code字段长度从1024到2048字符
# 用于支持更长的order_code数据

set -e

echo "🚀 开始升级order_code字段长度..."

# 数据库连接信息
DB_HOST="*************"
DB_PORT="5432"
DB_NAME="go_kuaidi"
DB_USER="postgres"
DB_PASS="gjx6ngf4"

# 升级文件路径
UPGRADE_FILE="sql/migrations/20250106_upgrade_order_code_length.sql"

# 检查升级文件是否存在
if [ ! -f "$UPGRADE_FILE" ]; then
    echo "❌ 升级文件不存在: $UPGRADE_FILE"
    exit 1
fi

echo "📋 升级文件: $UPGRADE_FILE"

# 执行升级
echo "🔧 执行数据库升级..."
PGPASSWORD="$DB_PASS" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -f "$UPGRADE_FILE"

if [ $? -eq 0 ]; then
    echo "✅ order_code字段长度升级成功完成！"
    echo ""
    echo "📊 升级详情："
    echo "   - 原长度: VARCHAR(1024)"
    echo "   - 新长度: VARCHAR(2048)"
    echo "   - 提升幅度: +1024字符"
    echo "   - 支持更复杂的order_code数据"
    echo ""
    echo "🎯 升级优势："
    echo "   - 支持更详细的原始请求数据"
    echo "   - 支持更复杂的JSON结构"
    echo "   - 提供更大的安全余量"
    echo "   - 适应未来数据增长需求"
    echo ""
    echo "🔍 验证升级结果..."
    
    # 验证升级结果
    PGPASSWORD="$DB_PASS" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
    SELECT 
        column_name,
        data_type,
        character_maximum_length,
        is_nullable
    FROM information_schema.columns 
    WHERE table_name = 'order_records' AND column_name = 'order_code';
    "
    
    echo ""
    echo "🎉 升级完成！order_code字段现在支持最大2048字符。"
else
    echo "❌ 升级失败，请检查数据库连接和权限"
    exit 1
fi 