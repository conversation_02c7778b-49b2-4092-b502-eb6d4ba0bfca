#!/bin/bash

# 真实客户端测试脚本
# 使用真实的客户端ID和密钥测试统一网关

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

# 配置变量
API_URL="http://localhost:8081/api/gateway/execute"
CLIENT_ID="f2Mpl9Lg8JVYPSWFNkPWOzqt"
CLIENT_SECRET="n5bNOPSWsRcd1ntOWPo13fxtpgJ6evbB"

# 生成毫秒级时间戳
generate_millisec_timestamp() {
    # 使用更可靠的方法生成毫秒级时间戳
    if command -v gdate >/dev/null 2>&1; then
        # macOS with GNU coreutils
        gdate +%s%3N
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS 原生方法
        echo $(($(date +%s) * 1000))
    else
        # Linux
        date +%s%3N
    fi
}

# 生成签名的函数
generate_signature() {
    local timestamp=$1
    local business_params=$2

    # 构建不包含sign字段的请求体
    local body_no_sign=$(cat <<EOF
{
  "apiMethod": "QUERY_PRICE",
  "businessParams": $business_params,
  "clientId": "$CLIENT_ID",
  "timestamp": "$timestamp"
}
EOF
)

    # 对请求体进行Base64编码
    local body_base64=$(echo -n "$body_no_sign" | base64)

    # 构建签名参数（按照服务端期望的格式：client_id, nonce, timestamp, body）
    local string_to_sign="client_id=$CLIENT_ID&nonce=$timestamp&timestamp=$timestamp&body=$body_base64"

    # 使用HMAC-SHA256生成签名
    local signature=$(echo -n "$string_to_sign" | openssl dgst -sha256 -hmac "$CLIENT_SECRET" -binary | base64)

    # 只输出签名，不输出调试信息到stdout
    echo "$signature"
}

# 测试API调用
test_api_call() {
    local timestamp=$1
    local test_name=$2

    local business_params='{"from":"北京","to":"上海","weight":1}'
    local signature=$(generate_signature "$timestamp" "$business_params")

    local request_data=$(cat <<EOF
{
  "apiMethod": "QUERY_PRICE",
  "businessParams": $business_params,
  "clientId": "$CLIENT_ID",
  "sign": "$signature",
  "timestamp": "$timestamp"
}
EOF
)

    # 发送请求
    local response=$(curl -s -X POST "$API_URL" \
        -H "Content-Type: application/json" \
        -d "$request_data")

    echo "$response"
}

# 测试1：毫秒级时间戳（正常情况）
test_millisec_timestamp() {
    log_info "测试1: 使用真实客户端的毫秒级时间戳"
    
    local timestamp=$(generate_millisec_timestamp)
    local response=$(test_api_call "$timestamp" "真实客户端毫秒级时间戳")
    
    echo "响应: $response"
    
    if echo "$response" | grep -q '"success":true'; then
        log_info "✅ 真实客户端毫秒级时间戳测试通过"
    elif echo "$response" | grep -q "TIMESTAMP_ALREADY_USED"; then
        log_warn "⚠️ 检测到timestamp重复"
    elif echo "$response" | grep -q "INVALID_CLIENT"; then
        log_error "❌ 客户端验证失败，可能客户端不存在或已禁用"
    elif echo "$response" | grep -q "SIGNATURE_VERIFICATION_FAILED"; then
        log_error "❌ 签名验证失败，请检查签名算法"
    else
        log_error "❌ 其他错误"
    fi
    
    echo "----------------------------------------"
}

# 测试2：秒级时间戳（兼容性测试）
test_sec_timestamp() {
    log_info "测试2: 使用真实客户端的秒级时间戳"
    
    local timestamp=$(date +%s)
    local response=$(test_api_call "$timestamp" "真实客户端秒级时间戳")
    
    echo "响应: $response"
    
    if echo "$response" | grep -q '"success":true'; then
        log_info "✅ 真实客户端秒级时间戳测试通过"
    elif echo "$response" | grep -q "TIMESTAMP_ALREADY_USED"; then
        log_warn "⚠️ 检测到timestamp重复"
    elif echo "$response" | grep -q "INVALID_CLIENT"; then
        log_error "❌ 客户端验证失败"
    elif echo "$response" | grep -q "SIGNATURE_VERIFICATION_FAILED"; then
        log_error "❌ 签名验证失败"
    else
        log_error "❌ 其他错误"
    fi
    
    echo "----------------------------------------"
}

# 测试3：重复时间戳（防重放测试）
test_duplicate_timestamp() {
    log_info "测试3: 重复时间戳防重放测试"
    
    local timestamp=$(generate_millisec_timestamp)
    
    # 第一次请求
    log_debug "发送第一次请求..."
    local response1=$(test_api_call "$timestamp" "第一次请求")
    echo "第一次响应: $response1"
    
    # 等待1秒
    sleep 1
    
    # 第二次请求（相同时间戳）
    log_debug "发送第二次请求（相同时间戳）..."
    local response2=$(test_api_call "$timestamp" "第二次请求（重复）")
    echo "第二次响应: $response2"
    
    if echo "$response2" | grep -q "TIMESTAMP_ALREADY_USED\|timestamp已被使用"; then
        log_info "✅ 防重放机制测试通过"
    else
        log_error "❌ 防重放机制测试失败"
    fi
    
    echo "----------------------------------------"
}

# 测试4：签名验证详细测试
test_signature_details() {
    log_info "测试4: 签名验证详细测试"
    
    local timestamp=$(generate_millisec_timestamp)
    local business_params='{"from":"北京","to":"上海","weight":1}'
    
    # 显示签名生成过程
    log_info "签名生成过程："
    log_info "客户端ID: $CLIENT_ID"
    log_info "客户端密钥: ${CLIENT_SECRET:0:8}..."
    log_info "时间戳: $timestamp"
    log_info "业务参数: $business_params"
    
    local signature=$(generate_signature "$timestamp" "$business_params")
    
    local request_data=$(cat <<EOF
{
  "clientId": "$CLIENT_ID",
  "timestamp": "$timestamp",
  "apiMethod": "QUERY_PRICE",
  "businessParams": $business_params,
  "sign": "$signature"
}
EOF
)
    
    log_info "完整请求数据:"
    echo "$request_data" | jq . 2>/dev/null || echo "$request_data"
    
    local response=$(curl -s -X POST "$API_URL" \
        -H "Content-Type: application/json" \
        -d "$request_data")
    
    echo ""
    log_info "服务器响应:"
    echo "$response" | jq . 2>/dev/null || echo "$response"
    
    echo "----------------------------------------"
}

# 检查应用程序状态
check_application() {
    log_info "检查应用程序状态..."
    
    if curl -s "http://localhost:8081/health" > /dev/null 2>&1; then
        log_info "✅ 应用程序正在运行"
        return 0
    else
        log_error "❌ 应用程序未运行或无法访问"
        return 1
    fi
}

# 检查客户端是否存在
check_client_exists() {
    log_info "检查客户端是否存在于数据库中..."
    
    # 这里可以添加数据库查询逻辑
    # 暂时跳过，通过API调用来验证
    log_info "将通过API调用验证客户端存在性"
}

# 主函数
main() {
    log_info "🚀 开始真实客户端测试..."
    echo "=========================================="
    
    # 检查应用程序状态
    if ! check_application; then
        log_error "请先启动应用程序"
        exit 1
    fi
    
    # 检查客户端
    check_client_exists
    
    echo "测试配置:"
    echo "  API URL: $API_URL"
    echo "  客户端ID: $CLIENT_ID"
    echo "  客户端密钥: ${CLIENT_SECRET:0:8}..."
    echo "  当前时间: $(date '+%Y-%m-%d %H:%M:%S')"
    echo "  毫秒时间戳: $(generate_millisec_timestamp)"
    echo "  秒时间戳: $(date +%s)"
    echo "=========================================="
    
    # 执行测试
    test_millisec_timestamp
    test_sec_timestamp
    test_duplicate_timestamp
    test_signature_details
    
    log_info "🎉 真实客户端测试完成！"
    
    echo ""
    echo "📋 测试总结:"
    echo "1. ✅ 毫秒级时间戳支持"
    echo "2. ✅ 秒级时间戳兼容性"
    echo "3. ✅ 防重放机制"
    echo "4. ✅ 签名验证详细过程"
    echo ""
    echo "💡 如果出现客户端验证失败，请确认客户端在数据库中存在且已启用"
}

# 执行主函数
main "$@"
