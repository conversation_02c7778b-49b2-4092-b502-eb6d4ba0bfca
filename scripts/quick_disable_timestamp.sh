#!/bin/bash

# 快速禁用timestamp验证脚本
# 这是一个简化版本，用于紧急情况快速禁用验证

echo "🚨 快速禁用timestamp验证..."

# 设置环境变量
export SKIP_TIMESTAMP_VALIDATION=true

# 检查服务是否运行
if pgrep -f "go-kuaidi" > /dev/null; then
    echo "🔄 重启服务..."
    pkill -f "go-kuaidi" || true
    sleep 2
    
    # 启动服务
    cd "$(dirname "$0")/.."
    SKIP_TIMESTAMP_VALIDATION=true nohup ./go-kuaidi > logs/app.log 2>&1 &
    
    sleep 3
    if pgrep -f "go-kuaidi" > /dev/null; then
        echo "✅ 服务已重启，timestamp验证已禁用"
        echo "⚠️ 注意：这是临时措施，请尽快解决问题"
    else
        echo "❌ 服务重启失败"
    fi
else
    echo "ℹ️ 服务未运行，请手动启动："
    echo "   SKIP_TIMESTAMP_VALIDATION=true ./go-kuaidi"
fi

echo ""
echo "📝 当前状态："
echo "   SKIP_TIMESTAMP_VALIDATION=true"
echo ""
echo "🔄 恢复验证请运行："
echo "   ./scripts/disable_timestamp_validation.sh disable"
