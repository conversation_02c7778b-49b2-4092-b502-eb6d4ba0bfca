#!/bin/bash

# KuaidiNiao Provider Setup Script
# This script helps set up the KuaidiNiao provider configuration

set -e

# Database connection parameters (adjust as needed)
DB_HOST="${DB_HOST:-*************}"
DB_USER="${DB_USER:-postgres}"
DB_NAME="${DB_NAME:-go_kuaidi}"
DB_PORT="${DB_PORT:-5432}"

# Color output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}=== KuaidiNiao Provider Setup ===${NC}"
echo ""

# Function to run SQL command
run_sql() {
    local sql="$1"
    psql -h "$DB_HOST" -U "$DB_USER" -d "$DB_NAME" -c "$sql"
}

# Function to update config
update_config() {
    local config_group="$1"
    local config_key="$2"
    local config_value="$3"
    local description="$4"
    
    echo -e "${YELLOW}Updating config: ${config_group}.${config_key}${NC}"
    run_sql "
        UPDATE system_configs 
        SET config_value = '$config_value', updated_at = NOW() 
        WHERE config_group = '$config_group' AND config_key = '$config_key';
    "
}

# Step 1: Initialize the configuration tables
echo -e "${YELLOW}Step 1: Initializing KuaidiNiao configuration...${NC}"
psql -h "$DB_HOST" -U "$DB_USER" -d "$DB_NAME" -f "$(dirname "$0")/init_kuaidiniao_config.sql"

# Step 2: Prompt for configuration values
echo ""
echo -e "${YELLOW}Step 2: Please provide your KuaidiNiao configuration:${NC}"
echo ""

# Prompt for E-Business ID
echo -n "Enter your KuaidiNiao E-Business ID (商户ID): "
read -r EBUSINESS_ID

if [ -z "$EBUSINESS_ID" ]; then
    echo -e "${RED}Error: E-Business ID is required${NC}"
    exit 1
fi

# Prompt for API Key
echo -n "Enter your KuaidiNiao API Key (API密钥): "
read -r -s API_KEY
echo ""

if [ -z "$API_KEY" ]; then
    echo -e "${RED}Error: API Key is required${NC}"
    exit 1
fi

# Prompt for environment
echo -n "Select environment (1=production, 2=sandbox) [1]: "
read -r ENV_CHOICE

case $ENV_CHOICE in
    2)
        ENVIRONMENT="sandbox"
        BASE_URL="https://sandboxapi.kdniao.com/api/dist"
        ;;
    *)
        ENVIRONMENT="production"
        BASE_URL="https://api.kdniao.com/api/dist"
        ;;
esac

# Prompt for timeout
echo -n "Enter request timeout in seconds [10]: "
read -r TIMEOUT
TIMEOUT=${TIMEOUT:-10}

# Step 3: Update configuration
echo ""
echo -e "${YELLOW}Step 3: Updating configuration in database...${NC}"

update_config "provider_kuaidiniao" "e_business_id" "$EBUSINESS_ID"
update_config "provider_kuaidiniao" "api_key" "$API_KEY"
update_config "provider_kuaidiniao" "base_url" "$BASE_URL"
update_config "provider_kuaidiniao" "environment" "$ENVIRONMENT"
update_config "provider_kuaidiniao" "timeout" "$TIMEOUT"

# Step 4: Ask if user wants to enable the provider
echo ""
echo -n "Do you want to enable the KuaidiNiao provider now? (y/N): "
read -r ENABLE_CHOICE

if [[ $ENABLE_CHOICE =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}Enabling KuaidiNiao provider...${NC}"
    update_config "provider" "kuaidiniao_enabled" "true"
    echo -e "${GREEN}KuaidiNiao provider enabled!${NC}"
else
    echo -e "${YELLOW}KuaidiNiao provider is configured but not enabled.${NC}"
    echo -e "${YELLOW}To enable it later, run:${NC}"
    echo "UPDATE system_configs SET config_value = 'true' WHERE config_group = 'provider' AND config_key = 'kuaidiniao_enabled';"
fi

# Step 5: Show configuration summary
echo ""
echo -e "${GREEN}=== Configuration Summary ===${NC}"
echo "E-Business ID: $EBUSINESS_ID"
echo "API Key: ****** (hidden)"
echo "Environment: $ENVIRONMENT"
echo "Base URL: $BASE_URL"
echo "Timeout: ${TIMEOUT}s"
echo ""

# Step 6: Test configuration (optional)
echo -n "Do you want to test the provider configuration? (y/N): "
read -r TEST_CHOICE

if [[ $TEST_CHOICE =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}Testing provider configuration...${NC}"
    
    # Note: This would require the application to be running
    # For now, just show how to test it manually
    echo -e "${YELLOW}To test the configuration, you can:${NC}"
    echo "1. Restart your Go application"
    echo "2. Use the admin API to reload the provider:"
    echo "   curl -X POST http://your-app-host/api/v1/admin/providers/kuaidiniao/reload"
    echo "3. Try a price query with the provider enabled"
fi

echo ""
echo -e "${GREEN}KuaidiNiao provider setup completed successfully!${NC}"
echo ""
echo -e "${YELLOW}Next steps:${NC}"
echo "1. Restart your application or reload the provider configuration"
echo "2. Test the integration with a price query"
echo "3. Monitor logs for any issues"
echo ""
echo -e "${YELLOW}For support, please refer to the KuaidiNiao integration documentation.${NC}"