#!/bin/bash

# Go-<PERSON>aidi 日志管理脚本
# 用于生产环境的日志维护和监控

set -e

# 配置
LOG_DIR="/Users/<USER>/Desktop/go-kuaidi-7.4.00.21/logs"
BACKUP_DIR="/Users/<USER>/Desktop/go-kuaidi-7.4.00.21/logs/backup"
ALERT_EMAIL="<EMAIL>"
MAX_LOG_SIZE="500M"
RETENTION_DAYS=90

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

# 创建必要的目录
create_directories() {
    log_info "创建日志目录..."
    mkdir -p "$LOG_DIR"
    mkdir -p "$BACKUP_DIR"
    mkdir -p "$LOG_DIR/archive"
}

# 检查日志文件大小
check_log_sizes() {
    log_info "检查日志文件大小..."
    
    for log_file in "$LOG_DIR"/*.log; do
        if [ -f "$log_file" ]; then
            size=$(du -h "$log_file" | cut -f1)
            log_info "日志文件: $(basename "$log_file") - 大小: $size"
            
            # 检查是否超过最大大小
            size_bytes=$(du -b "$log_file" | cut -f1)
            max_bytes=$(echo "$MAX_LOG_SIZE" | sed 's/M//' | awk '{print $1 * 1024 * 1024}')
            
            if [ "$size_bytes" -gt "$max_bytes" ]; then
                log_warn "日志文件 $(basename "$log_file") 超过最大大小限制"
            fi
        fi
    done
}

# 轮转日志文件
rotate_logs() {
    log_info "开始日志轮转..."
    
    for log_file in "$LOG_DIR"/*.log; do
        if [ -f "$log_file" ]; then
            filename=$(basename "$log_file" .log)
            timestamp=$(date '+%Y%m%d_%H%M%S')
            
            # 复制并压缩
            cp "$log_file" "$BACKUP_DIR/${filename}_${timestamp}.log"
            gzip "$BACKUP_DIR/${filename}_${timestamp}.log"
            
            # 清空原文件（保持文件句柄）
            > "$log_file"
            
            log_info "已轮转日志文件: $filename"
        fi
    done
}

# 清理旧日志
cleanup_old_logs() {
    log_info "清理 $RETENTION_DAYS 天前的日志..."
    
    # 清理备份目录中的旧文件
    find "$BACKUP_DIR" -name "*.log.gz" -mtime +$RETENTION_DAYS -delete
    find "$LOG_DIR/archive" -name "*.log.gz" -mtime +$RETENTION_DAYS -delete
    
    log_info "旧日志清理完成"
}

# 分析错误日志
analyze_error_logs() {
    log_info "分析错误日志..."
    
    error_log="$LOG_DIR/error.log"
    if [ -f "$error_log" ]; then
        # 统计今天的错误数量
        today=$(date '+%Y-%m-%d')
        error_count=$(grep "$today" "$error_log" | wc -l)
        
        log_info "今日错误日志数量: $error_count"
        
        # 如果错误数量过多，发送告警
        if [ "$error_count" -gt 100 ]; then
            log_warn "错误日志数量异常，建议检查系统状态"
            # send_alert "错误日志数量异常: $error_count"
        fi
        
        # 显示最近的错误
        log_info "最近5条错误日志:"
        tail -5 "$error_log" | while read line; do
            echo "  $line"
        done
    fi
}

# 生成日志报告
generate_report() {
    log_info "生成日志统计报告..."
    
    report_file="$LOG_DIR/daily_report_$(date '+%Y%m%d').txt"
    
    cat > "$report_file" << EOF
Go-Kuaidi 日志统计报告
生成时间: $(date)
========================================

磁盘使用情况:
$(df -h "$LOG_DIR")

日志文件统计:
$(ls -lh "$LOG_DIR"/*.log 2>/dev/null || echo "无日志文件")

错误统计 (最近24小时):
$(grep "$(date '+%Y-%m-%d')" "$LOG_DIR/error.log" 2>/dev/null | wc -l) 条错误

访问统计 (最近24小时):
$(grep "$(date '+%Y-%m-%d')" "$LOG_DIR/access.log" 2>/dev/null | wc -l) 次访问

系统状态:
- 应用状态: $(pgrep -f "go-kuaidi" > /dev/null && echo "运行中" || echo "未运行")
- 数据库连接: $(PGPASSWORD=gjx6ngf4 psql -h 8.138.252.193 -p 5432 -U postgres -d go_kuaidi -t -c "SELECT 1" 2>/dev/null && echo "正常" || echo "异常")

EOF

    log_info "报告已生成: $report_file"
}

# 监控日志实时输出
monitor_logs() {
    log_info "开始实时监控日志..."
    echo "按 Ctrl+C 停止监控"
    
    # 使用 multitail 或 tail 监控多个日志文件
    if command -v multitail &> /dev/null; then
        multitail "$LOG_DIR/go-kuaidi.log" "$LOG_DIR/error.log" "$LOG_DIR/access.log"
    else
        tail -f "$LOG_DIR"/*.log
    fi
}

# 发送告警（示例）
send_alert() {
    local message="$1"
    log_warn "告警: $message"
    
    # 这里可以集成邮件、钉钉、企业微信等告警方式
    # echo "$message" | mail -s "Go-Kuaidi 系统告警" "$ALERT_EMAIL"
}

# 主函数
main() {
    case "${1:-help}" in
        "check")
            create_directories
            check_log_sizes
            analyze_error_logs
            ;;
        "rotate")
            create_directories
            rotate_logs
            ;;
        "cleanup")
            cleanup_old_logs
            ;;
        "report")
            create_directories
            generate_report
            ;;
        "monitor")
            monitor_logs
            ;;
        "all")
            create_directories
            check_log_sizes
            analyze_error_logs
            cleanup_old_logs
            generate_report
            ;;
        "help"|*)
            echo "Go-Kuaidi 日志管理工具"
            echo ""
            echo "用法: $0 [命令]"
            echo ""
            echo "命令:"
            echo "  check    - 检查日志状态"
            echo "  rotate   - 轮转日志文件"
            echo "  cleanup  - 清理旧日志"
            echo "  report   - 生成统计报告"
            echo "  monitor  - 实时监控日志"
            echo "  all      - 执行所有维护任务"
            echo "  help     - 显示帮助信息"
            ;;
    esac
}

# 执行主函数
main "$@"
