#!/bin/bash

# 设置日志管理定时任务

LOG_SCRIPT="/Users/<USER>/Desktop/go-kuaidi-*********/scripts/log_management.sh"

echo "设置 Go-Kuaidi 日志管理定时任务..."

# 创建临时 crontab 文件
TEMP_CRON=$(mktemp)

# 获取当前用户的 crontab
crontab -l 2>/dev/null > "$TEMP_CRON" || true

# 添加日志管理任务
cat >> "$TEMP_CRON" << EOF

# Go-Kuaidi 日志管理任务
# 每天凌晨2点执行日志维护
0 2 * * * $LOG_SCRIPT all >> /Users/<USER>/Desktop/go-kuaidi-*********/logs/cron.log 2>&1

# 每小时检查日志状态
0 * * * * $LOG_SCRIPT check >> /Users/<USER>/Desktop/go-kuaidi-*********/logs/cron.log 2>&1

# 每周日凌晨3点清理旧日志
0 3 * * 0 $LOG_SCRIPT cleanup >> /Users/<USER>/Desktop/go-kuaidi-*********/logs/cron.log 2>&1

EOF

# 安装新的 crontab
crontab "$TEMP_CRON"

# 清理临时文件
rm "$TEMP_CRON"

echo "✅ 定时任务设置完成！"
echo ""
echo "已设置的任务:"
echo "- 每天凌晨2点: 执行完整日志维护"
echo "- 每小时: 检查日志状态"
echo "- 每周日凌晨3点: 清理旧日志"
echo ""
echo "查看当前定时任务: crontab -l"
echo "查看执行日志: tail -f /Users/<USER>/Desktop/go-kuaidi-*********/logs/cron.log"
