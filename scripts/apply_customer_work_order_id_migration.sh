#!/bin/bash

# 用户自定义工单ID功能数据库迁移脚本
# 创建时间: 2025-01-27
# 描述: 为work_orders表添加customer_work_order_id字段

set -e

# 配置
DB_HOST="*************"
DB_PORT="5432"
DB_NAME="go_kuaidi"
DB_USER="postgres"
DB_PASSWORD="gjx6ngf4"

echo "🚀 开始执行用户自定义工单ID数据库迁移..."

# 1. 执行数据库迁移
echo "📊 执行数据库迁移..."
PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -f sql/migrations/add_customer_work_order_id.sql

# 2. 验证迁移结果
echo "✅ 验证迁移结果..."
PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "
SELECT 
    column_name, 
    data_type, 
    is_nullable, 
    column_default 
FROM information_schema.columns 
WHERE table_name = 'work_orders' AND column_name = 'customer_work_order_id';
"

echo "🎉 用户自定义工单ID数据库迁移完成！"
echo ""
echo "📋 迁移内容:"
echo "  - 添加 customer_work_order_id VARCHAR(100) 字段"
echo "  - 创建唯一索引确保同一用户下工单ID唯一性"
echo "  - 创建普通索引用于查询优化"
echo ""
echo "🔧 下一步操作:"
echo "  1. 重启应用服务以加载新的代码"
echo "  2. 测试用户自定义工单ID功能"
echo "  3. 更新API文档" 