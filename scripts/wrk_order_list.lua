-- wrk订单列表压力测试脚本
-- 模拟真实的订单列表查询请求

-- 请求模板
local order_list_requests = {
    {
        method = "GET",
        path = "/api/v1/express/orders?page=1&page_size=20",
        headers = {
            ["Authorization"] = "Bearer test_token_123"
        }
    },
    {
        method = "GET", 
        path = "/api/v1/express/orders?page=1&page_size=20&status=pending",
        headers = {
            ["Authorization"] = "Bearer test_token_123"
        }
    },
    {
        method = "GET",
        path = "/api/v1/express/orders?page=1&page_size=20&express_type=SF",
        headers = {
            ["Authorization"] = "Bearer test_token_123"
        }
    },
    {
        method = "GET",
        path = "/api/v1/express/orders?page=1&page_size=20&provider=kuaidi100",
        headers = {
            ["Authorization"] = "Bearer test_token_123"
        }
    },
    {
        method = "GET",
        path = "/api/v1/express/orders?page=1&page_size=20&start_time=2025-01-01%2000:00:00&end_time=2025-01-31%2023:59:59",
        headers = {
            ["Authorization"] = "Bearer test_token_123"
        }
    },
    {
        method = "GET",
        path = "/api/v1/express/orders?page=2&page_size=50",
        headers = {
            ["Authorization"] = "Bearer test_token_123"
        }
    },
    {
        method = "GET",
        path = "/api/v1/express/orders?page=1&page_size=10&sort_by=created_at&sort_order=desc",
        headers = {
            ["Authorization"] = "Bearer test_token_123"
        }
    }
}

-- 当前请求索引
local request_index = 1

-- 初始化函数
function init(args)
    -- 设置随机种子
    math.randomseed(os.time())
    
    -- 打印测试信息
    print("订单列表压力测试开始")
    print("请求模板数量: " .. #order_list_requests)
end

-- 构建请求
function request()
    -- 循环使用请求模板
    local req = order_list_requests[request_index]
    request_index = request_index + 1
    if request_index > #order_list_requests then
        request_index = 1
    end
    
    -- 构建wrk请求
    wrk.method = req.method
    wrk.path = req.path
    wrk.body = req.body or ""
    
    -- 设置请求头
    wrk.headers = {}
    for key, value in pairs(req.headers) do
        wrk.headers[key] = value
    end
    
    return wrk.format()
end

-- 响应处理
function response(status, headers, body)
    -- 统计响应状态
    if status ~= 200 then
        print("Error response: " .. status .. " - " .. body)
    end
end

-- 完成回调
function done(summary, latency, requests)
    print("\n=== 订单列表压力测试结果 ===")
    print("总请求数: " .. summary.requests)
    print("总耗时: " .. summary.duration / 1000000 .. "s")
    print("平均QPS: " .. summary.requests / (summary.duration / 1000000))
    print("错误数: " .. summary.errors.connect + summary.errors.read + summary.errors.write + summary.errors.status + summary.errors.timeout)
    
    print("\n延迟统计:")
    print("最小延迟: " .. latency.min / 1000 .. "ms")
    print("最大延迟: " .. latency.max / 1000 .. "ms")
    print("平均延迟: " .. latency.mean / 1000 .. "ms")
    print("50%延迟: " .. latency:percentile(50) / 1000 .. "ms")
    print("90%延迟: " .. latency:percentile(90) / 1000 .. "ms")
    print("95%延迟: " .. latency:percentile(95) / 1000 .. "ms")
    print("99%延迟: " .. latency:percentile(99) / 1000 .. "ms")
end
