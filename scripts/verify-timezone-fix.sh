#!/bin/bash

# 时区修复验证脚本
# 验证整个系统的时区配置是否正确

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

# 配置变量
API_BASE_URL="http://localhost:8081"
TEST_CLIENT_ID="test-client"
ADMIN_API_URL="http://localhost:8081/api/admin"

# 检查系统时区
check_system_timezone() {
    log_info "检查系统时区配置..."
    
    # 检查系统时区
    local system_tz=$(timedatectl show --property=Timezone --value 2>/dev/null || echo "Unknown")
    log_info "系统时区: $system_tz"
    
    # 检查当前时间
    local current_time=$(date '+%Y-%m-%d %H:%M:%S %Z')
    log_info "当前系统时间: $current_time"
    
    # 检查北京时间
    local beijing_time=$(TZ=Asia/Shanghai date '+%Y-%m-%d %H:%M:%S %Z')
    log_info "北京时间: $beijing_time"
    
    echo "----------------------------------------"
}

# 检查应用程序时区
check_application_timezone() {
    log_info "检查应用程序时区配置..."
    
    # 检查健康检查端点
    if curl -s "$API_BASE_URL/health" > /dev/null 2>&1; then
        log_info "✅ 应用程序正在运行"
        
        # 获取健康检查信息
        local health_response=$(curl -s "$API_BASE_URL/health")
        echo "健康检查响应: $health_response"
        
        # 检查时间相关的API响应
        log_info "检查API时间响应..."
        
        # 测试nonce生成时间
        if command -v ./build/nonce-generator &> /dev/null; then
            local nonce=$(./build/nonce-generator -client=$TEST_CLIENT_ID 2>/dev/null | grep -o '[0-9]\{13\}[a-f0-9]*' | head -1)
            if [ ! -z "$nonce" ]; then
                local timestamp=${nonce:0:13}
                local nonce_time=$(date -d "@$((timestamp/1000))" '+%Y-%m-%d %H:%M:%S %Z')
                log_info "Nonce时间戳: $timestamp -> $nonce_time"
            fi
        fi
        
    else
        log_error "❌ 应用程序未运行或无法访问"
        return 1
    fi
    
    echo "----------------------------------------"
}

# 检查数据库时区
check_database_timezone() {
    log_info "检查数据库时区配置..."
    
    # 检查PostgreSQL连接
    if command -v psql &> /dev/null; then
        log_info "测试数据库时区设置..."
        
        # 连接数据库并检查时区
        local db_timezone=$(PGPASSWORD=gjx6ngf4 psql -h 8.138.252.193 -p 5432 -U postgres -d go_kuaidi -t -c "SHOW timezone;" 2>/dev/null | xargs || echo "Unknown")
        log_info "数据库时区: $db_timezone"
        
        # 检查当前数据库时间
        local db_time=$(PGPASSWORD=gjx6ngf4 psql -h 8.138.252.193 -p 5432 -U postgres -d go_kuaidi -t -c "SELECT NOW();" 2>/dev/null | xargs || echo "Unknown")
        log_info "数据库当前时间: $db_time"
        
        # 检查数据库中的时间字段示例
        log_info "检查数据库中的时间字段..."
        local sample_time=$(PGPASSWORD=gjx6ngf4 psql -h 8.138.252.193 -p 5432 -U postgres -d go_kuaidi -t -c "SELECT created_at FROM express_companies LIMIT 1;" 2>/dev/null | xargs || echo "No data")
        log_info "示例记录时间: $sample_time"
        
    else
        log_warn "⚠️ psql未安装，无法检查数据库时区"
    fi
    
    echo "----------------------------------------"
}

# 检查配置文件
check_config_files() {
    log_info "检查配置文件时区设置..."
    
    # 检查主配置文件
    if [ -f "config/config.yaml" ]; then
        log_info "检查 config/config.yaml..."
        
        if grep -q "timezone:" config/config.yaml; then
            log_info "✅ 发现时区配置:"
            grep -A 5 -B 1 "timezone:" config/config.yaml | sed 's/^/  /'
        else
            log_warn "⚠️ 未发现时区配置"
        fi
        
        if grep -q "Asia/Shanghai" config/config.yaml; then
            log_info "✅ 发现北京时区配置"
        else
            log_warn "⚠️ 未发现北京时区配置"
        fi
    else
        log_error "❌ 配置文件不存在: config/config.yaml"
    fi
    
    echo "----------------------------------------"
}

# 测试API时间响应
test_api_timezone() {
    log_info "测试API时间响应..."
    
    # 测试nonce统计API
    if curl -s "$ADMIN_API_URL/nonce/stats" > /dev/null 2>&1; then
        log_info "测试nonce统计API..."
        local stats_response=$(curl -s "$ADMIN_API_URL/nonce/stats")
        echo "Nonce统计响应: $stats_response"
        
        # 提取时间字段
        if echo "$stats_response" | grep -q "last_cleanup_time"; then
            local cleanup_time=$(echo "$stats_response" | grep -o '"last_cleanup_time":"[^"]*"' | cut -d'"' -f4)
            log_info "最后清理时间: $cleanup_time"
        fi
    else
        log_warn "⚠️ 无法访问nonce统计API"
    fi
    
    # 测试时间戳生成
    log_info "测试时间戳生成..."
    local current_timestamp=$(date +%s)
    log_info "当前Unix时间戳: $current_timestamp"
    
    local beijing_timestamp=$(TZ=Asia/Shanghai date +%s)
    log_info "北京时间戳: $beijing_timestamp"
    
    if [ "$current_timestamp" = "$beijing_timestamp" ]; then
        log_info "✅ 时间戳一致"
    else
        log_warn "⚠️ 时间戳不一致"
    fi
    
    echo "----------------------------------------"
}

# 测试前端时区处理
test_frontend_timezone() {
    log_info "检查前端时区工具..."
    
    # 检查前端时区工具文件
    if [ -f "admin-frontend/src/utils/timezone.ts" ]; then
        log_info "✅ 管理员前端时区工具存在"
        
        # 检查关键配置
        if grep -q "Asia/Shanghai" admin-frontend/src/utils/timezone.ts; then
            log_info "✅ 管理员前端配置北京时区"
        else
            log_warn "⚠️ 管理员前端未配置北京时区"
        fi
    else
        log_error "❌ 管理员前端时区工具不存在"
    fi
    
    if [ -f "user-frontend/src/utils/timezone.js" ]; then
        log_info "✅ 用户前端时区工具存在"
        
        # 检查关键配置
        if grep -q "Asia/Shanghai" user-frontend/src/utils/timezone.js; then
            log_info "✅ 用户前端配置北京时区"
        else
            log_warn "⚠️ 用户前端未配置北京时区"
        fi
    else
        log_error "❌ 用户前端时区工具不存在"
    fi
    
    echo "----------------------------------------"
}

# 生成时区测试报告
generate_timezone_report() {
    log_info "生成时区测试报告..."
    
    local report_file="./logs/timezone_verification_$(date +%Y%m%d_%H%M%S).log"
    mkdir -p ./logs
    
    {
        echo "=========================================="
        echo "时区配置验证报告"
        echo "生成时间: $(date '+%Y-%m-%d %H:%M:%S %Z')"
        echo "=========================================="
        echo ""
        
        echo "1. 系统时区信息:"
        echo "   系统时区: $(timedatectl show --property=Timezone --value 2>/dev/null || echo 'Unknown')"
        echo "   当前时间: $(date '+%Y-%m-%d %H:%M:%S %Z')"
        echo "   北京时间: $(TZ=Asia/Shanghai date '+%Y-%m-%d %H:%M:%S %Z')"
        echo ""
        
        echo "2. 应用程序状态:"
        if curl -s "$API_BASE_URL/health" > /dev/null 2>&1; then
            echo "   应用状态: 运行中 ✅"
            echo "   健康检查: $(curl -s "$API_BASE_URL/health")"
        else
            echo "   应用状态: 未运行 ❌"
        fi
        echo ""
        
        echo "3. 数据库时区:"
        if command -v psql &> /dev/null; then
            echo "   数据库时区: $(PGPASSWORD=gjx6ngf4 psql -h 8.138.252.193 -p 5432 -U postgres -d go_kuaidi -t -c "SHOW timezone;" 2>/dev/null | xargs || echo 'Unknown')"
            echo "   数据库时间: $(PGPASSWORD=gjx6ngf4 psql -h 8.138.252.193 -p 5432 -U postgres -d go_kuaidi -t -c "SELECT NOW();" 2>/dev/null | xargs || echo 'Unknown')"
        else
            echo "   数据库检查: psql未安装 ⚠️"
        fi
        echo ""
        
        echo "4. 配置文件检查:"
        if [ -f "config/config.yaml" ]; then
            if grep -q "timezone:" config/config.yaml; then
                echo "   时区配置: 存在 ✅"
                grep -A 5 -B 1 "timezone:" config/config.yaml | sed 's/^/     /'
            else
                echo "   时区配置: 不存在 ⚠️"
            fi
        else
            echo "   配置文件: 不存在 ❌"
        fi
        echo ""
        
        echo "5. 前端工具检查:"
        echo "   管理员前端: $([ -f "admin-frontend/src/utils/timezone.ts" ] && echo "存在 ✅" || echo "不存在 ❌")"
        echo "   用户前端: $([ -f "user-frontend/src/utils/timezone.js" ] && echo "存在 ✅" || echo "不存在 ❌")"
        echo ""
        
        echo "6. 建议和总结:"
        echo "   - 确保所有时间显示使用北京时间"
        echo "   - 数据库存储使用UTC时间"
        echo "   - API响应时间使用北京时间"
        echo "   - 前端显示统一使用时区工具"
        echo ""
        echo "=========================================="
        
    } > "$report_file"
    
    log_info "✅ 时区验证报告已生成: $report_file"
    
    # 显示报告内容
    cat "$report_file"
}

# 主函数
main() {
    log_info "🕐 开始时区配置验证..."
    echo "=========================================="
    
    check_system_timezone
    check_application_timezone
    check_database_timezone
    check_config_files
    test_api_timezone
    test_frontend_timezone
    generate_timezone_report
    
    log_info "🎉 时区配置验证完成！"
    log_info "📝 详细报告请查看: ./logs/timezone_verification_*.log"
}

# 执行主函数
main "$@"
