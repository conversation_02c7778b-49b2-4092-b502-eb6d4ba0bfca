#!/bin/bash

# 快速分析请求失败的脚本
# 使用方法: ./analyze_failures.sh [手机号1] [手机号2] ...

LOG_DIR="logs"
PHONE_NUMBERS=("$@")

echo "🔍 快速失败分析工具"
echo "===================="
echo

if [ ${#PHONE_NUMBERS[@]} -gt 0 ]; then
    echo "🔍 搜索手机号: ${PHONE_NUMBERS[*]}"
else
    echo "🔍 分析所有失败记录"
fi
echo

# 检查日志目录
if [ ! -d "$LOG_DIR" ]; then
    echo "❌ 日志目录不存在: $LOG_DIR"
    exit 1
fi

echo "📄 分析日志文件..."
echo

# 1. 搜索指定手机号的所有记录
if [ ${#PHONE_NUMBERS[@]} -gt 0 ]; then
    echo "📱 指定手机号的所有记录:"
    echo "------------------------"
    for phone in "${PHONE_NUMBERS[@]}"; do
        echo "🔸 手机号: $phone"
        count=$(grep -r "$phone" "$LOG_DIR"/ --include="*.log" | wc -l)
        if [ "$count" -gt 0 ]; then
            echo "   找到 $count 条记录:"
            grep -r "$phone" "$LOG_DIR"/ --include="*.log" -n | head -10 | while read line; do
                echo "   $line"
            done
            if [ "$count" -gt 10 ]; then
                echo "   ... 还有 $((count - 10)) 条记录"
            fi
        else
            echo "   ❌ 未找到任何记录"
        fi
        echo
    done
fi

# 2. 分析签名验证失败
echo "🚨 签名验证失败统计:"
echo "-------------------"
echo "🔸 签名参数提取失败:"
grep -r "签名参数提取失败\|签名参数解析失败" "$LOG_DIR"/ --include="*.log" | wc -l | xargs echo "   次数:"

echo "🔸 时间戳验证失败:"
grep -r "时间戳验证失败\|timestamp验证失败" "$LOG_DIR"/ --include="*.log" | wc -l | xargs echo "   次数:"

echo "🔸 统一网关timestamp验证失败:"
grep -r "统一网关timestamp验证失败" "$LOG_DIR"/ --include="*.log" | wc -l | xargs echo "   次数:"

echo "🔸 客户端验证失败:"
grep -r "客户端验证失败\|客户端已禁用" "$LOG_DIR"/ --include="*.log" | wc -l | xargs echo "   次数:"

echo "🔸 签名验证失败:"
grep -r "签名验证失败" "$LOG_DIR"/ --include="*.log" | wc -l | xargs echo "   次数:"

echo "🔸 nonce/timestamp重复:"
grep -r "NONCE_ALREADY_USED\|TIMESTAMP_ALREADY_USED\|已被使用" "$LOG_DIR"/ --include="*.log" | wc -l | xargs echo "   次数:"

echo "🔸 Redis类型断言失败:"
grep -r "Redis客户端类型断言失败" "$LOG_DIR"/ --include="*.log" | wc -l | xargs echo "   次数:"

echo

# 3. 分析HTTP错误状态码
echo "🌐 HTTP错误状态码统计:"
echo "----------------------"
echo "🔸 400 Bad Request:"
grep -r "status.*400" "$LOG_DIR"/ --include="*.log" | wc -l | xargs echo "   次数:"

echo "🔸 401 Unauthorized:"
grep -r "status.*401" "$LOG_DIR"/ --include="*.log" | wc -l | xargs echo "   次数:"

echo "🔸 403 Forbidden:"
grep -r "status.*403" "$LOG_DIR"/ --include="*.log" | wc -l | xargs echo "   次数:"

echo

# 4. 分析客户端IP
echo "🌐 失败请求的客户端IP统计:"
echo "-------------------------"
grep -r "签名.*失败\|timestamp.*失败\|客户端.*失败" "$LOG_DIR"/ --include="*.log" | \
grep -o '"client_ip":"[^"]*"' | \
sed 's/"client_ip":"//g' | sed 's/"//g' | \
sort | uniq -c | sort -nr | head -10 | \
while read count ip; do
    echo "🔸 $ip: $count 次"
done

echo

# 5. 最近的失败记录
echo "🕒 最近的失败记录 (最多10条):"
echo "----------------------------"
grep -r "ERROR.*失败\|WARN.*失败" "$LOG_DIR"/ --include="*.log" | \
sort -k1,1 -k2,2 | tail -10 | \
while IFS=':' read file line content; do
    echo "🔸 $(basename "$file"): $content"
done

echo

# 6. 特殊分析：查找可能被拦截的请求
echo "🔍 可能被拦截的请求分析:"
echo "----------------------"

# 查找只有开始记录但没有完成记录的请求
echo "🔸 查找不完整的请求流程..."
incomplete_requests=$(grep -r "签名验证开始" "$LOG_DIR"/ --include="*.log" | \
grep -o '"request_id":"[^"]*"' | \
sed 's/"request_id":"//g' | sed 's/"//g' | \
while read req_id; do
    success_count=$(grep -r "$req_id" "$LOG_DIR"/ --include="*.log" | grep "签名验证成功" | wc -l)
    if [ "$success_count" -eq 0 ]; then
        echo "$req_id"
    fi
done | wc -l)

echo "   发现 $incomplete_requests 个不完整的请求流程"

# 查找Redis相关问题
echo "🔸 Redis相关问题:"
redis_issues=$(grep -r "Redis.*失败\|Redis.*断言失败" "$LOG_DIR"/ --include="*.log" | wc -l)
echo "   发现 $redis_issues 个Redis相关问题"

echo

# 7. 生成建议
echo "💡 分析建议:"
echo "----------"

if [ ${#PHONE_NUMBERS[@]} -gt 0 ]; then
    for phone in "${PHONE_NUMBERS[@]}"; do
        count=$(grep -r "$phone" "$LOG_DIR"/ --include="*.log" | wc -l)
        if [ "$count" -eq 0 ]; then
            echo "🔸 手机号 $phone 未找到任何记录，可能原因:"
            echo "   - 请求未到达服务器（网络问题、负载均衡器拦截）"
            echo "   - 客户端实际未发送请求"
            echo "   - 手机号输入错误"
            echo "   - 请求在更早阶段被拦截"
        fi
    done
fi

redis_count=$(grep -r "Redis客户端类型断言失败" "$LOG_DIR"/ --include="*.log" | wc -l)
if [ "$redis_count" -gt 0 ]; then
    echo "🔸 发现 $redis_count 个Redis类型断言失败，建议:"
    echo "   - 检查Redis客户端初始化"
    echo "   - 验证Redis连接配置"
    echo "   - 重启服务以重新初始化Redis连接"
fi

timestamp_count=$(grep -r "timestamp.*失败" "$LOG_DIR"/ --include="*.log" | wc -l)
if [ "$timestamp_count" -gt 0 ]; then
    echo "🔸 发现 $timestamp_count 个timestamp验证失败，建议:"
    echo "   - 客户端使用毫秒级时间戳: Date.now().toString()"
    echo "   - 确保客户端时间同步"
    echo "   - 检查timestamp生成逻辑"
fi

echo
echo "✅ 分析完成！"

# 8. 提供进一步分析的命令
echo
echo "🔧 进一步分析命令:"
echo "-----------------"
echo "# 查看详细的失败日志:"
echo "grep -r '失败.*详细信息' logs/ --include='*.log' | tail -20"
echo
echo "# 查看特定客户端的所有记录:"
echo "grep -r 'client_id.*YOUR_CLIENT_ID' logs/ --include='*.log'"
echo
echo "# 查看特定时间段的记录:"
echo "grep -r '2025-07-08 21:4[0-5]' logs/ --include='*.log' | grep '失败'"
echo
echo "# 运行Go版本的详细分析工具:"
echo "go run scripts/analyze_request_failures.go logs/ ${PHONE_NUMBERS[*]}"
