#!/usr/bin/env python3
import urllib.parse
import json

# 解码我们的测试数据
encoded_data = '%7B%22PushTime%22%3A%222025-07-20+14%3A29%3A30%22%2C%22EBusinessID%22%3A%221778716%22%2C%22Data%22%3A%5B%7B%22ShipperCode%22%3A%22STO%22%2C%22CreateTime%22%3A%222025-07-20+14%3A29%3A35%22%2C%22OrderCode%22%3A%22gk1752990418%22%2C%22EBusinessID%22%3A%221778716%22%2C%22PickerInfo%22%3A%5B%7B%22PersonName%22%3A%22%E5%94%90%E5%85%88%E6%98%8E%22%2C%22PersonTel%22%3A%2213820685646%22%2C%22PickupCode%22%3A%22338191%22%2C%22PersonCode%22%3A%2201257480%22%7D%5D%2C%22Cost%22%3A0.00%2C%22Success%22%3Atrue%2C%22CallRequestType%22%3A%221801%22%2C%22Weight%22%3A2.40%2C%22Reason%22%3A%22%E5%B7%B2%E5%88%86%E9%85%8D%E5%BF%AB%E9%80%92%E5%91%98%22%2C%22LogisticCode%22%3A%22773367560774938%22%2C%22State%22%3A%22103%22%2C%22FetchTime%22%3A%22%22%2C%22KDNOrderCode%22%3A%22KDN2507201310000374%22%2C%22OperateType%22%3A2%7D%5D%2C%22Count%22%3A1%7D'

decoded_data = urllib.parse.unquote(encoded_data)
print('解码后的JSON数据:')
print(decoded_data)
print()

# 解析JSON
data = json.loads(decoded_data)
print('解析后的数据结构:')
print(json.dumps(data, indent=2, ensure_ascii=False))

# 检查PickerInfo
if 'Data' in data and len(data['Data']) > 0:
    order_data = data['Data'][0]
    if 'PickerInfo' in order_data:
        print('\n✅ PickerInfo 存在:')
        print(json.dumps(order_data['PickerInfo'], indent=2, ensure_ascii=False))
    else:
        print('\n❌ PickerInfo 不存在')
else:
    print('\n❌ Data 数组为空或不存在')
