Go-<PERSON>aidi 查价接口性能测试报告
生成时间: 2025年 7月21日 星期一 00时15分15秒 CST
测试配置: 并发用户 1 5 10 20 30 50, 测试时长 30秒

=== 测试结果汇总 ===
并发数 总请求  成功数  成功率%   平均时间ms 最小时间ms 最大时间ms QPS     
--------------------------------------------------------------------------------
1        <USER>         <GROUP>         100.00       2333.33      2000         3000         .40     
5        47         47         100.00       3148.93      2000         4000         1.56    
10       53         53         100.00       5641.50      2000         9000         1.76    
20       61         61         100.00       10622.95     5000         18000        2.03    
30       61         61         100.00       15770.49     5000         25000        2.03    
50       95         91         95.78        24768.42     21000        35000        3.16    

=== 性能分析 ===
最佳性能点: 50并发用户, QPS: 3.16
每日查价承载能力: 191116.80 次 (70%利用率)

=== 系统配置 ===
- 数据库连接池: 200个连接
- Redis连接池: 100个连接
- 查价缓存TTL: 5分钟
- 服务器配置: 8核32GB

=== 优化建议 ===
1. 如果成功率 < 95%，考虑增加数据库连接池
2. 如果响应时间 > 500ms，考虑优化缓存策略
3. 监控CPU和内存使用率，避免超过80%
4. 设置告警: 响应时间>1s, 错误率>5%
