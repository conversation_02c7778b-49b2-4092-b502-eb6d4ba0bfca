# 订单创建问题排查指南

## 📋 概述

本指南帮助您快速定位和解决订单创建过程中的问题。系统已经增强了日志记录，所有关键信息都会记录到日志中。

## 🔍 日志查看方法

### 1. 查看实时日志
```bash
# 查看最新的订单创建日志
docker logs -f kuaidi --tail=100 | grep "customer_order_no"

# 查看特定订单的所有日志
docker logs kuaidi | grep "订单号"
```

### 2. 关键日志标识
- `统一网关下单请求开始` - 请求进入系统
- `开始调用供应商API创建订单` - 开始调用供应商
- `供应商API调用成功/失败` - 供应商响应结果
- `云通/快递100/易达业务失败` - 具体供应商错误

## 🚨 常见问题排查

### 1. 订单创建失败

#### 查看完整错误信息
```bash
# 搜索失败日志
docker logs kuaidi | grep -A 10 -B 5 "订单创建失败\|业务失败\|API调用失败"
```

#### 关键字段说明
- `customer_order_no`: 客户订单号
- `user_id`: 用户ID
- `provider`: 供应商（yuntong/kuaidi100/yida）
- `express_type`: 快递公司代码
- `error_type`: 错误类型
- `sender_info/receiver_info`: 收寄件地址信息
- `package_weight/package_volume`: 包裹重量和体积

### 2. 供应商特定问题

#### 云通供应商问题
```bash
# 查看云通相关错误
docker logs kuaidi | grep "云通" | grep "ERROR"
```

常见错误：
- `当前线路暂未开放商家寄递服务` - 该快递公司在当前线路不支持
- `参数验证失败` - 地址格式或其他参数错误
- `余额不足` - 供应商账户余额不足

#### 快递100供应商问题
```bash
# 查看快递100相关错误
docker logs kuaidi | grep "快递100" | grep "ERROR"
```

常见错误：
- `大于2.49公斤` - 德邦快递重量限制
- `该区域暂时不开放` - 区域不支持

#### 易达供应商问题
```bash
# 查看易达相关错误
docker logs kuaidi | grep "易达" | grep "ERROR"
```

### 3. 网络和技术问题

#### API调用失败
```bash
# 查看网络相关错误
docker logs kuaidi | grep "API调用失败\|网络\|超时\|连接"
```

## 📊 问题分析步骤

### 步骤1：确定问题范围
1. 是单个订单问题还是批量问题？
2. 是特定供应商问题还是全部供应商？
3. 是特定快递公司问题还是全部快递公司？

### 步骤2：查看详细日志
```bash
# 查看特定订单的完整处理流程
docker logs kuaidi | grep "订单号" | sort
```

### 步骤3：分析错误类型
- **BUSINESS_FAILED**: 供应商业务规则限制
- **API_CALL_FAILED**: 网络或API调用问题
- **PARAMETER_ERROR**: 参数验证失败

### 步骤4：查看请求和响应数据
日志中包含完整的请求数据(`request_data`)和响应数据(`full_response`)，可以：
1. 检查发送给供应商的参数是否正确
2. 查看供应商返回的具体错误信息
3. 对比不同供应商的处理结果

## 🛠️ 解决方案

### 1. 供应商不支持问题
- **现象**: 错误信息包含"不支持"、"暂未开放"
- **解决**: 这是正常情况，系统会自动尝试其他供应商

### 2. 地址格式问题
- **现象**: "地址格式不正确"、"参数验证失败"
- **解决**: 检查收寄件地址的省市区格式

### 3. 重量体积限制
- **现象**: "大于2.49公斤"、"体积超限"
- **解决**: 选择支持大件的快递公司

### 4. 账户问题
- **现象**: "余额不足"、"认证失败"
- **解决**: 联系供应商充值或检查配置

### 5. 网络问题
- **现象**: "超时"、"连接失败"
- **解决**: 检查网络连接，稍后重试

## 📈 监控建议

### 1. 关键指标监控
- 订单创建成功率
- 各供应商响应时间
- 错误类型分布

### 2. 告警设置
```bash
# 监控错误率
docker logs kuaidi | grep "ERROR" | wc -l

# 监控特定供应商问题
docker logs kuaidi | grep "云通.*ERROR" | wc -l
```

### 3. 定期检查
- 每日检查错误日志
- 每周分析错误趋势
- 每月优化问题处理

## 🔧 调试技巧

### 1. 实时监控
```bash
# 实时查看订单处理过程
docker logs -f kuaidi | grep -E "(下单请求开始|API调用|业务失败|创建成功)"
```

### 2. 过滤特定信息
```bash
# 只看错误日志
docker logs kuaidi | grep "ERROR"

# 只看特定用户的订单
docker logs kuaidi | grep "user_id.*用户ID"

# 只看特定时间段的日志
docker logs kuaidi --since="2025-07-07T10:00:00" --until="2025-07-07T11:00:00"
```

### 3. 导出日志分析
```bash
# 导出今天的错误日志
docker logs kuaidi | grep "$(date +%Y-%m-%d)" | grep "ERROR" > order_errors_today.log
```

## 📞 联系支持

如果按照本指南仍无法解决问题，请提供：
1. 具体的客户订单号
2. 完整的错误日志
3. 问题发生的时间
4. 用户ID和订单参数

这样可以快速定位和解决问题。
