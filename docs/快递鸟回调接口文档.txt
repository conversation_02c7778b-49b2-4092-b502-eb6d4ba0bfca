5.1推送调度失败通知
5.1.1功能说明
快递鸟向快递公司下单失败，快递鸟推送下单失败状态给客户。
5.1.2基本信息
状态码
99
支持格式
只支持Json格式、UTF-8编码
Content-type：application/x-www-form-urlencoded;charset=UTF-8
批量请求
不支持
超时时间
5000ms
5.1.3状态说明
快递鸟向快递公司下单失败，快递鸟推送下单失败状态给客户。
5.1.4应用场景
寄件人老七下单失败，快递鸟推送失败结果给客户，老七于是选择线下自寄。
5.1.5应用级参数
名称
类型(字符长度)
是否必须
描述
PushTime
String(64)
是
订单下单时间，
格式：yyyy-MM-dd HH:mm:ss
EBusinessID
String(64)
是
用户ID
Data
KDNOrderCode
String(64)
是
快递鸟单号
OrderCode
String(64)
是
商家订单号
Reason
String(255)
否
失败的描述
ShipperCode
String(64)
否
快递公司编码
State
String(8)
是
状态码，固定99
CreateTime
String(50)
是
状态推送时间，
格式：yyyy-MM-dd HH:mm:ss
OperateType
Int(2)
是
操作人 1:快递鸟 2：物流公司
CallRequestType
String(8)
是
调用的接口指令，例：1801
Count
Int(2)
是
推送的个数
5.1.6返回参数
名称
类型(字符长度)
是否必须
描述
EBusinessID
String(10)
是
用户ID
Success
Boolean
是
成功与否(true/false)
UpdateTime
String(50)
是
处理完结时间，
格式yyyy-MM-dd HH:mm:ss
Reason
String(50)
是
描述
5.1.7示列报文
请求示列
JSON
复制代码
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
{
	"RequestData": {
		"PushTime": "2023-02-08 16:54:45",
		"EBusinessID": "350238",
		"Data": [{
			"ShipperCode": "STO",
			"State": "99",
			"CreateTime": "2023-02-08 16:54:49",
			"KDNOrderCode": "KDN2302081610006138",
			"OrderCode": "1623243811540918273",
			"Reason": "网点暂停，无法提供收派服务，带来不便请您理解！",
			"OperateType": 1,
			"CallRequestType": "1801"
		}],
		"Count": 1
	},
	"DataSign": "NDJjYTAyNTFhNmZkNDM3ZTk5ZGY5MDE1ZjE1YjY0YWU=",
	"RequestType": 103
}
响应报文
JSON
复制代码
1
2
3
4
5
6
{
	"EBusinessID": "350238",
	"UpdateTime": "2023-02-08 16:54:59",
	"Success": true,
	"Reason": "成功"
}


5.2推送取消状态
5.2.1功能说明
当订单无法服务时，快递公司将取消订单，同时将状态推送给到客户。
5.2.2基本信息
状态码
203
支持格式
只支持Json格式、UTF-8编码
Content-type：application/x-www-form-urlencoded;charset=UTF-8
批量请求
不支持
超时时间
5000ms
5.2.3状态说明
当订单无法服务时，快递鸟将取消订单，同时将状态推送给到客户。
5.2.4应用场景
因为特殊情况客户无法取消，线下沟通清楚需要取消的。
场景一：老七下单上门取件，因地址和电话填写错误，快递员无法联系到老七和提供上门取件服务，于是，快递员取消订单。  快递鸟确认后，在线取消订单，推给平台。
5.2.5应用级参数
名称
类型(字符长度)
是否必须
描述
PushTime
String(64)
是
订单下单时间，
格式：yyyy-MM-dd HH:mm:ss
EBusinessID
String(64)
是
用户ID
Data
KDNOrderCode
String(64)
是
快递鸟单号
OrderCode
String(64)
是
商家订单号
LogisticCode
String(64)
否
物流运单号
ShipperCode
String(64)
否
快递公司编码
Reason
String(255)
否
取消原因
State
String(8)
是
状态码，固定203
CreateTime
String(50)
是
状态推送时间，
格式：yyyy-MM-dd HH:mm:ss
OperateType
Int(2)
是
操作人 1:快递鸟 2：物流公司
CallRequestType
String(8)
是
调用的接口指令，例：1801
Count
Int(2)
是
推送的个数
5.2.6返回参数
名称
类型(字符长度)
是否必须
描述
EBusinessID
String(10)
是
用户ID
Success
Boolean
是
成功与否(true/false)
UpdateTime
String(50)
是
处理完结时间，
格式yyyy-MM-dd HH:mm:ss
Reason
String(50)
是
描述
5.2.7示列报文
请求示列
JSON
复制代码
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
{
	"RequestData": {
		"PushTime": "2023-02-08 14:07:25",
		"EBusinessID": "350238",
		"Data": [{
			"ShipperCode": "YTO",
			"CreateTime": "2023-02-08 18:10:14",
			"OrderCode": "209413334674090517",
			"EBusinessID": "350238",
			"PickerInfo": [{
				"PickupCode": "343473"
			}],
			"Cost": 0.00,
			"Success": true,
			"CallRequestType": "1801",
			"Weight": 1.00,
			"Reason": "揽收失败 原因：无法联系上客户",
			"LogisticCode": "YT2286453342033",
			"State": "203",
			"FetchTime": "",
			"KDNOrderCode": "KDN2302081410000841",
			"OperateType": 2
		}],
		"Count": 1
	},
	"DataSign": "NDJjYTAyNTFhNmZkNDM3ZTk5ZGY5MDE1ZjE1YjY0YWU=",
	"RequestType": 103
}
响应报文
JSON
复制代码
1
2
3
4
5
6
{
	"EBusinessID": "350238",
	"UpdateTime": "2023-02-08 16:54:59",
	"Success": true,
	"Reason": "成功"
}


5.3推送网点信息
5.3.1功能说明
下单成功后，快递公司分配网点，快递鸟接收到网点信息后将网点信息推送给客户。
5.3.2基本信息
状态码
102
支持格式
只支持Json格式、UTF-8编码
Content-type：application/x-www-form-urlencoded;charset=UTF-8
批量请求
不支持
超时时间
5000ms
5.3.3状态说明
此状态会返回接单的网点信息。
5.3.4应用场景
寄件人老七，在客户平台手机端下单上门取件，没多久后看到网点信息。
5.3.5应用级参数
名称
类型(字符长度)
是否必须
描述
PushTime
String(64)
是
订单下单时间，
格式  yyyy-MM-dd HH:mm:ss
EBusinessID
String(64)
是
用户ID
Data
KDNOrderCode
String(64)
是
快递鸟单号
OrderCode
String(64)
是
商家订单号
LogisticCode
String(64)
否
物流运单号
Reason
String(255)
否
推送的描述
ShipperCode
String(64)
是
快递公司编码
State
String(8)
是
状态码，固定102
CreateTime
Date
是
状态推送时间，
格式  yyyy-MM-dd HH:mm:ss
OperateType
Int(2)
是
操作人 1:快递鸟 2：物流公司
CallRequestType
String(8)
是
调用的接口指令，例：1801
PickerInfo
PickupCode
String(50)
是
取件码
StationCode
String(64)
否
网点编码
StationName
String(64)
否
网点名称
StationTel
String(64)
否
网点电话
StationAddress
String(100)
否
网点地址
Count
Int(2)
是
推送的个数
5.3.6返回参数
名称
类型(字符长度)
是否必须
描述
EBusinessID
String(10)
是
用户ID
Success
Boolean
是
成功与否(true/false)
UpdateTime
String(50)
是
处理完结时间，
格式yyyy-MM-dd HH:mm:ss
Reason
String(50)
是
描述
5.3.7示列报文
请求示列
JSON
复制代码
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
{
	"RequestData": {
		"PushTime": "2023-02-08 17:05:57",
		"EBusinessID": "350238",
		"Data": [{
			"ShipperCode": "YTO",
			"CreateTime": "2023-02-08 17:05:58",
			"OrderCode": "209413935969514541",
			"EBusinessID": "350238",
			"PickerInfo": [{
				"StationName": "河南省开封市杞县平城乡",
				"StationAddress": "",
				"StationTel": "021-****7888",
				"StationCode": "378836",
				"PickupCode": "351814"
			}],
			"Cost": 0.00,
			"Success": true,
			"CallRequestType": "1801",
			"Weight": 1.00,
			"Reason": "【河南省开封市杞县平城乡公司】 接单成功 网点联系电话：021-****7888，业务员电话：151****2201",
			"LogisticCode": "YT2286578488599",
			"State": "102",
			"FetchTime": "",
			"KDNOrderCode": "KDN2302081710000652",
			"OperateType": 2
		}],
		"Count": 1
	},
	"DataSign": "NjgwMjg3ODdlZDVlZTk4MjQwZjFhMjViMjQ0MGEyZTA=",
	"RequestType": 103
}
响应报文
JSON
复制代码
1
2
3
4
5
6
{
	"EBusinessID": "350238",
	"UpdateTime": "2023-02-08 16:54:59",
	"Success": true,
	"Reason": "成功"
}


5.4推送快递员信息
5.4.1功能说明
分配网点成功后，快递公司分配快递员，快递鸟接收到快递员信息后将快递员信息推送给客户。
5.4.2基本信息
状态码
103
支持格式
只支持Json格式、UTF-8编码
Content-type：application/x-www-form-urlencoded;charset=UTF-8
批量请求
不支持
超时时间
5000ms
5.4.3状态说明
此状态会返回快递员信息。
5.4.4应用场景
寄件人老七，在客户平台下单上门取件后，在手机端看到快递员手机号信息，于是拨打快递员电话，确认具体上门取件时间。
5.4.5应用级参数
名称
类型(字符长度)
是否必须
描述
PushTime
String(64)
是
订单下单时间，
格式  yyyy-MM-dd HH:mm:ss
EBusinessID
String(64)
是
用户ID
Data
KDNOrderCode
String(64)
是
快递鸟单号
OrderCode
String(64)
是
商家订单号
LogisticCode
String(64)
否
物流运单号
Reason
String(255)
否
推送的描述
ShipperCode
String(64)
是
快递公司编码
State
String(8)
是
状态码，固定103
CreateTime
Date
是
状态推送时间,
格式  yyyy-MM-dd HH:mm:ss
OperateType
Int(2)
是
操作人 1:快递鸟 2：物流公司
CallRequestType
String(8)
是
调用的接口指令，例：1801
PickerInfo
PickupCode
String(50)
是
取件码
PersonName
String(64)
是
快递员姓名
PersonTel
String(64)
是
快递员手机号码
PersonCode
String(50)
否
快递员编号
Count
Int(2)
是
推送的个数
5.4.6返回参数
名称
类型(字符长度)
是否必须
描述
EBusinessID
String(10)
是
用户ID
Success
Boolean
是
成功与否(true/false)
UpdateTime
String(50)
是
处理完结时间，
格式yyyy-MM-dd HH:mm:ss
Reason
String(50)
是
描述
5.4.7示列报文
请求示列
JSON
复制代码
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
{
	"RequestData": {
		"PushTime": "2023-02-08 16:11:44",
		"EBusinessID": "350238",
		"Data": [{
			"ShipperCode": "YTO",
			"CreateTime": "2023-02-08 17:15:02",
			"OrderCode": "209413598814579288",
			"EBusinessID": "350238",
			"PickerInfo": [{
				"PersonName": "李成伟",
				"PersonTel": "130****0546",
				"PickupCode": "459677",
				"PersonCode": "01257480"
			}],
			"Cost": 0.00,
			"Success": true,
			"CallRequestType": "1801",
			"Weight": 1.00,
			"Reason": "【河北省唐山市遵化市东新庄镇公司】 接单成功 网点联系电话：***********，业务员电话：130****0546",
			"LogisticCode": "YT2286541010110",
			"State": "103",
			"FetchTime": "",
			"KDNOrderCode": "KDN2302081610001345",
			"OperateType": 2
		}],
		"Count": 1
	},
	"DataSign": "MGY1OTg1MTUxMzkwYmM5NDg4ZmZkYzg4MDUyOWVlZTg=",
	"RequestType": 103
}
响应报文
JSON
复制代码
1
2
3
4
5
6
{
	"EBusinessID": "350238",
	"UpdateTime": "2023-02-08 16:54:59",
	"Success": true,
	"Reason": "成功"
}



5.5推送取件状态
5.5.1功能说明
分配快递员成功后，快递员上门取件，快递鸟接收到取件状态后推送给客户。
5.5.2基本信息
状态码
104
支持格式
只支持Json格式、UTF-8编码
Content-type：application/x-www-form-urlencoded;charset=UTF-8
批量请求
不支持
超时时间
5000ms
5.5.3状态说明
1.此状态是一个中间状态，取件的重量并不会作为结算重量，不能以此作为结算的依据，104只是一个取件动作说明。 
5.5.4应用场景
寄件人老七，在客户平台下单上门取件后，快递员在预约时间内上门取件。快递员在他的手机端做取件动作后，快递系统把数据给到快递鸟，快递鸟把取件状态推送给客户，客户选择展示给用户手机端。
5.5.5应用级参数
名称
类型(字符长度)
是否必须
描述
PushTime
String(64)
是
订单下单时间，
格式：yyyy-MM-dd HH:mm:ss
EBusinessID
String(64)
是
用户ID
Data
KDNOrderCode
String(64)
是
快递鸟单号
OrderCode
String(64)
是
商家订单号
LogisticCode
String(64)
否
物流运单号
ShipperCode
String(64)
是
快递公司编码
Reason
String(255)
否
推送的描述
State
String(8)
是
状态码，固定104
CreateTime
String(50)
是
状态推送时间，
格式：yyyy-MM-dd HH:mm:ss
OperateType
Int(2)
是
操作人 1:快递鸟 2：物流公司
CallRequestType
String(8)
是
调用的接口指令，例：1801
Count
Int(2)
是
推送的个数
5.5.6返回参数
名称
类型(字符长度)
是否必须
描述
EBusinessID
String(10)
是
用户ID
Success
Boolean
是
成功与否(true/false)
UpdateTime
String(50)
是
处理完结时间，
格式yyyy-MM-dd HH:mm:ss
Reason
String(50)
是
描述
5.5.7示列报文
请求示列
JSON
复制代码
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
{
	"RequestData": {
		"PushTime": "2023-02-08 15:03:15",
		"EBusinessID": "350238",
		"Data": [{
			"ShipperCode": "YTO",
			"CreateTime": "2023-02-08 17:26:52",
			"OrderCode": "S1168272813-0208150314",
			"EBusinessID": "350238",
			"PickerInfo": [{
				"PickupCode": "990879"
			}],
			"Cost": 0.00,
			"Success": true,
			"CallRequestType": "1801",
			"Weight": 0.50,
			"Reason": "【湖南省株洲市石峰区白马乡公司】 已揽收 取件人: 凌湘毅 (133****5390) 新冠疫苗已接种",
			"LogisticCode": "YT2286491386809",
			"State": "104",
			"FetchTime": "",
			"KDNOrderCode": "KDN2302081510000346",
			"OperateType": 2
		}],
		"Count": 1
	},
	"DataSign": "NDJjYTAyNTFhNmZkNDM3ZTk5ZGY5MDE1ZjE1YjY0YWU=",
	"RequestType": 103
}
响应报文
JSON
复制代码
1
2
3
4
5
6
{
	"EBusinessID": "350238",
	"UpdateTime": "2023-02-08 16:54:59",
	"Success": true,
	"Reason": "成功"
}

5.6推送揽件状态
5.6.1功能说明
1.快递员揽件后，快递鸟接收到揽件状态和重量后将状态及重量推送给客户。
5.6.2基本信息
状态码
301
支持格式
只支持Json格式、UTF-8编码
Content-type：application/x-www-form-urlencoded;charset=UTF-8
批量请求
不支持
超时时间
5000ms
5.6.3状态说明
快递员揽件后，快递鸟接收到揽件状态和重量后，将运费及重量信息推送给客户。
正常订单结算费用快递鸟和平台默认以state:301状态中TotalFee为准(指定申通下单产品除外，指定申通下单产品按state:601中的TotalFee进行结算)。
5.6.4应用场景
寄件人老七，在客户平台下单上门取件后，快递员在预约时间内上门揽件。快递员在他的手机端做揽件动作后，快递系统把数据给到快递鸟，快递鸟接收到揽件状态和重量后，把揽件状态和重量运费数据推送给客户，客户手机端展示给寄件人老七，老七线上支付运费。
5.6.5应用级参数
名称
类型(字符长度)
是否必须
描述
PushTime
String(64)
是
订单下单时间，
格式  yyyy-MM-dd HH:mm:ss
EBusinessID
String(64)
是
用户ID
Data
KDNOrderCode
String(64)
是
快递鸟单号
LogisticCode
String(64)
是
物流运单号
OrderCode
String(64)
是
商家订单号
Reason
String(255)
否
推送的描述
ShipperCode
String(64)
是
快递公司编码
State
String(8)
是
状态码，固定301
CreateTime
String(50)
是
状态推送时间，
格式  yyyy-MM-dd HH:mm:ss
OperateType
Int(2)
是
操作人 1:快递鸟 2：物流公司
CallRequestType
String(8)
是
调用的接口指令，例：1801
Volume
Double(10,3)
否
体积，单位：立方厘米
VolumeWeight
Double(10,3)
否
体积重，体积/8000
ActualWeight
Double（10，2）
否
称重重量
Weight
Double(10,2)
是
重量，例：1.00kg
Cost
Double(10,4)
是
基础运费=首重金额+续重总金额
FirstWeightAmount
Double(10,2)
是
首重金额
ContinuousWeightAmount
Double(10,2)
是
续重总金额
InsureAmount
Double(10,2)
否
保价费
PackageFee
Double(10,2)
否
包装费
OverFee
Double(10,2)
否
超长超重费
OtherFee
Double(10,2)
否
其他费
OtherFeeDetail
String
否
其他费明细，json格式，
如{\"打包服务费\":\"0.50\",\"资源调节费\":\"1.00\"}
TotalFee
Double(10,2)
是
总费用=基础运费+保价费+包装费+超长超重费+其他费
Count
Int(2)
是
推送的个数
5.6.6返回参数
名称
类型(字符长度)
是否必须
描述
EBusinessID
String(10)
是
用户ID
Success
Boolean
是
成功与否(true/false)
UpdateTime
String(50)
是
处理完结时间，
格式yyyy-MM-dd HH:mm:ss
Reason
String(50)
是
描述
5.6.7示列报文
请求示列
响应报文
JSON
复制代码
1
2
3
4
5
6
{
	"EBusinessID": "350238",
	"UpdateTime": "2023-02-08 16:54:59",
	"Success": true,
	"Reason": "成功"
}
5.6.6特殊规则
1.顺丰，德邦360支持返回区间续重收费规则-需要联系快递鸟技术支持做单独配置
样式：
[ {
    "continuousWeightAmount" : 0,
    "firstWeight" : 20.00,
    "cost" : "31.20",
    "isSubsectionContinuousWeightPrice" : 1,
    "continuousWeight" : 0.00,
    "totalFee" : 31.20,
    "firstWeightAmount" : 31.20,
    "weight" : "1",
    "shipperCode" : "SF",
    "subsectionContinuousWeightPrices" : [ ]
  }, {
    "continuousWeightAmount" : 0,
    "firstWeight" : 3.00,
    "cost" : "8.68",
    "isSubsectionContinuousWeightPrice" : 1,
    "continuousWeight" : 0.00,
    "totalFee" : 8.68,
    "firstWeightAmount" : 8.68,
    "weight" : "1",
    "shipperCode" : "DBL",
    "subsectionContinuousWeightPrices" : [ ]
  } ]



5.7推送在途中状态
5.7.1功能说明
订单发车后，快递鸟推送在途中状态给客户。
5.7.2基本信息
状态码
2
支持格式
只支持Json格式、UTF-8编码
Content-type：application/x-www-form-urlencoded;charset=UTF-8
批量请求
不支持
超时时间
5000ms
5.7.3状态说明
订单发车产生第一条运输轨迹后，快递鸟会推送在途中状态给客户告知用户这个订单已经进入运输中。(运单的完整物流轨迹需要单独通过物流轨迹接口进行获取)
5.7.4应用场景
寄件人老七，用上门取件业务下单成功后，当快件发车后，老七收到在途中结果
5.7.5应用级参数
名称
类型(字符长度)
是否必须
描述
PushTime
String(64)
是
订单下单时间，
格式  yyyy-MM-dd HH:mm:ss
EBusinessID
String(64)
是
用户ID
Data
KDNOrderCode
String(64)
是
快递鸟单号
OrderCode
String(64)
是
商家订单号
LogisticCode
String(64)
是
物流运单号
Reason
String(255)
否
推送的描述
ShipperCode
String(64)
是
快递公司编码
State
String(8)
是
状态码，固定2
CreateTime
Date
是
状态推送时间,
格式  yyyy-MM-dd HH:mm:ss
OperateType
Int(2)
是
操作人 1:快递鸟 2：物流公司
CallRequestType
String(8)
是
调用的接口指令，例：1801
Count
Int(2)
是
推送的个数
5.7.6返回参数
名称
类型(字符长度)
是否必须
描述
EBusinessID
String(10)
是
用户ID
Success
Boolean
是
成功与否(true/false)
UpdateTime
String(50)
是
处理完结时间，
格式yyyy-MM-dd HH:mm:ss
Reason
String(50)
是
描述
5.7.7示列报文
请求示列
JSON
复制代码
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
{
	"RequestData": {
		"PushTime": "2023-02-06 23:58:39",
		"EBusinessID": "350238",
		"Data": [{
			"ShipperCode": "YTO",
			"CreateTime": "2023-02-08 19:13:26",
			"OrderCode": "DDL30047293",
			"EBusinessID": "350238",
			"PickerInfo": [{
				"PickupCode": "1733"
			}],
			"Cost": 10.80,
			"Success": true,
			"CallRequestType": "1801",
			"Weight": 2.98,
			"Reason": "【广西南宁市兴宁区三塘】 已发出 下一站 【南宁转运中心公司】",
			"LogisticCode": "YT2270516210628",
			"State": "2",
			"FetchTime": "",
			"KDNOrderCode": "KDN2302062310002306",
			"OperateType": 2
		}],
		"Count": 1
	},
	"DataSign": "NDJjYTAyNTFhNmZkNDM3ZTk5ZGY5MDE1ZjE1YjY0YWU=",
	"RequestType": 103
}
响应报文
JSON
复制代码
1
2
3
4
5
6
{
	"EBusinessID": "350238",
	"UpdateTime": "2023-02-08 16:54:59",
	"Success": true,
	"Reason": "成功"
}


5.8推送签收状态
5.8.1功能说明
订单签收后，快递鸟推送签收状态给客户。
5.8.2基本信息
状态码
3
支持格式
只支持Json格式、UTF-8编码
Content-type：application/x-www-form-urlencoded;charset=UTF-8
批量请求
不支持
超时时间
5000ms
5.8.3状态说明
订单签收后，快递鸟推送签收状态给客户。
5.8.4应用场景
寄件人老七，用上门取件业务下单成功后，当快件签收后，老七收到签收结果。
5.8.5应用级参数
名称
类型(字符长度)
是否必须
描述
PushTime
String(64)
是
订单下单时间，
格式  yyyy-MM-dd HH:mm:ss
EBusinessID
String(64)
是
用户ID
Data
KDNOrderCode
String(64)
是
快递鸟单号
OrderCode
String(64)
是
商家订单号
LogisticCode
String(64)
是
物流运单号
Reason
String(255)
否
推送的描述
ShipperCode
String(64)
是
快递公司编码
State
String(8)
是
状态码，固定3
SignType
String(4)
否
签收类型编码，当订单状态是签收时且客户订阅了3000签收类型才会传递。3001：正常签收 
3002：退货签收 
3003：拒收签收
CreateTime
Date
是
状态推送时间，
格式  yyyy-MM-dd HH:mm:ss
CallRequestType
String(8)
是
调用的接口指令，例：1801
Count
Int(2)
是
推送的个数
5.8.6返回参数
名称
类型(字符长度)
是否必须
描述
EBusinessID
String(10)
是
用户ID
Success
Boolean
是
成功与否(true/false)
UpdateTime
String(50)
是
处理完结时间，
格式yyyy-MM-dd HH:mm:ss
Reason
String(50)
是
描述
5.8.7示列报文
请求示列
JSON
复制代码
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
{
	"RequestData": {
		"PushTime": "2023-02-07 14:09:34",
		"EBusinessID": "350238",
		"Data": [{
			"LogisticCode": "YT2270615791408",
			"ShipperCode": "YTO",
			"State": "3",
			"CreateTime": "2023-02-08 15:13:19",
			"KDNOrderCode": "KDN2302071410001140",
			"OrderCode": "LJ202302071409341064581800",
			"Reason": "【正常签收】客户签收人: 本人签收 已签收  感谢使用圆通速递，期待再次为您服务",
			"SignType": "3001",
			"CallRequestType": "1801"
		}],
		"Count": 1
	},
	"DataSign": "NDJjYTAyNTFhNmZkNDM3ZTk5ZGY5MDE1ZjE1YjY0YWU=",
	"RequestType": 103
}
响应报文
JSON
复制代码
1
2
3
4
5
6
{
	"EBusinessID": "350238",
	"UpdateTime": "2023-02-08 16:54:59",
	"Success": true,
	"Reason": "成功"
}


5.9推送修改订单结果
5.9.1功能说明
寄件人主动修改订单基本信息，快递鸟将订单修改结果推送给客户。
5.9.2基本信息
状态码
402
支持格式
只支持Json格式、UTF-8编码
Content-type：application/x-www-form-urlencoded;charset=UTF-8
批量请求
不支持
超时时间
5000ms
5.9.3状态说明
寄件人主动修改订单基本信息，快递鸟将订单修改结果推送给客户。
PS：
state=402仅代表修改状态，修改是成功，还是失败，需要看402状态中的UpdateStatus字段，
UpdateStatus=0代表修改订单成功，修改成功，会更换新的KDN单号、接单公司，物流单号进行走件
UpdateStatus=1代表修改订单失败，修改失败，还是原KDN单号和接单公司进行走件  
5.9.4应用场景
场景一：
寄件人老七，在上门取件订单没有取件时，修改上门取件订单信息，把寄出地址由家改成公司。快递鸟收到修改信息后，进行业务处理，最终推送修改成功结果给客户，客户展示给寄件人老七，老七从公司寄快递。
场景二：
寄件人老七，在上门取件订单没有取件时，修改上门取件订单信息，把寄出地址由家改成公司。快递鸟收到修改信息后，进行业务处理，最终推送修改失败结果给客户，客户展示给寄件人老七，老七决定继续从家寄快递，或者取消订单重新下单。
5.9.5应用级参数
名称
类型(字符长度)
是否必须
描述
PushTime
String(64)
是
订单下单时间，
格式  yyyy-MM-dd HH:mm:ss
EBusinessID
String(64)
是
用户ID
Data
KDNOrderCode
String(64)
是
快递鸟单号
OrderCode
String(64)
是
商家订单号
State
String(8)
是
状态码，固定402
UpdateStatus
String(4)
是
0=修改成功
1=修改失败
KdnOrderCodeNew
String(64)
否
快递鸟新单号(修改失败无新单号)
ShipperCode
String(64)
否
快递公司编码
LogisticCode
String(64)
否
物流运单号
Reason
String(255)
否
原因描述
CreateTime
String(50)
是
状态推送时间，
格式：yyyy-MM-dd HH:mm:ss
CallRequestType
String(8)
是
调用的接口指令，例：1801
Count
Int(2)
是
推送的个数
5.9.6返回参数
名称
类型(字符长度)
是否必须
描述
EBusinessID
String(10)
是
用户ID
Success
Boolean
是
成功与否(true/false)
UpdateTime
String(50)
是
处理完结时间，
格式yyyy-MM-dd HH:mm:ss
Reason
String(50)
是
描述
5.9.7示列报文
请求示列
修改成功(UpdateStatus=0)
JSON
复制代码
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
{
	"RequestData": {
		"PushTime": "2023-02-08 19:00:48",
		"EBusinessID": "350238",
		"Data": [{
			"UpdateStatus": "0",
			"ShipperCode": "JD",
			"CreateTime": "2023-02-08 19:00:49",
			"OrderCode": "3CB8A4FBB8B73E3F",
			"KdnOrderCodeNew": "KDN2302081910000107",
			"EBusinessID": "350238",
			"Success": true,
			"CallRequestType": "1801",
			"Reason": "订单修改成功",
			"State": "402",
			"KDNOrderCode": "KDN2302081910000086",
			"OperateType": "0"
		}],
		"Count": 1
	},
	"DataSign": "NDJjYTAyNTFhNmZkNDM3ZTk5ZGY5MDE1ZjE1YjY0YWU=",
	"RequestType": 103
}
修改失败(UpdateStatus=1)
JSON
复制代码
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
{
	"RequestData": {
		"PushTime": "2023-02-08 19:00:48",
		"EBusinessID": "350238",
		"Data": [{
			"UpdateStatus": "1",
			"ShipperCode": "",
			"CreateTime": "2023-02-08 19:00:49",
			"OrderCode": "3CB8A4FBB8B73E3F",
			"KdnOrderCodeNew": "",
			"EBusinessID": "350238",
			"Success": true,
			"CallRequestType": "1801",
			"Reason": "订单修改失败，原KDN单号继续走件",
			"State": "402",
			"KDNOrderCode": "KDN2302081910000086",
			"OperateType": "1"
		}],
		"Count": 1
	},
	"DataSign": "NDJjYTAyNTFhNmZkNDM3ZTk5ZGY5MDE1ZjE1YjY0YWU=",
	"RequestType": 103
}
响应报文
JSON
复制代码
1
2
3
4
5
6
{
	"EBusinessID": "350238",
	"UpdateTime": "2023-02-08 16:54:59",
	"Success": true,
	"Reason": "成功"
}


5.10推送调派通知
5.10.1功能说明
快递鸟更换快递公司后，将新快递公司信息推送给到客户。
5.10.2基本信息
状态码
109
支持格式
只支持Json格式、UTF-8编码
Content-type：application/x-www-form-urlencoded;charset=UTF-8
批量请求
不支持
超时时间
5000ms
5.10.3状态说明
1.收到此状态会更新订单的全部的信息，包括预约取件时间，快递公司编码，取件码，运单号等信息。
2.此状态不是必推的，指定下单没有此回调状态推送，智能调度产品下单有此状态推送。
5.10.4应用场景
当一个订单因为特殊原因需要调派的时候，会发送此状态。
场景一：老七下单上门取件，分配给申通，因快递员无法提供服务，于是系统调派到京东快递。京东快递员上门揽件。
5.10.5应用级参数
名称
类型(字符长度)
是否必须
描述
PushTime
String(64)
是
订单下单时间，
格式  yyyy-MM-dd HH:mm:ss
EBusinessID
String(64)
是
用户ID
Data
KDNOrderCode
String(64)
是
快递鸟单号
OrderCode
String(64)
是
商家订单号
ShipperCode
String(64)
是
新快递公司编码
LogisticCode
String(64)
否
新物流运单号
OldShipperCode
String(64)
是
旧快递公司编码
OldLogisticCode
String(64)
否
旧快递公司运单号
Reason
String(255)
否
转单描述
State
String(8)
是
状态码，固定109
startTime
String(50)
是
预约开始时间,
格式：yyyy-MM-dd HH:mm:ss
endTime
String(50)
是
预约结束时间,
格式：yyyy-MM-dd HH:mm:ss
CreateTime
String(50)
是
状态推送时间,
格式：yyyy-MM-dd HH:mm:ss
OperateType
Int(2)
是
操作人 1:快递鸟 2:物流公司
CallRequestType
String(8)
是
调用的接口指令，例：1801
PickerInfo
PickupCode
String(50)
否
取件码，数组格式，例：
"PickerInfo":[{"PickupCode":"265639"}]
Count
Int(2)
是
推送的个数
5.10.6返回参数
名称
类型(字符长度)
是否必须
描述
EBusinessID
String(10)
是
用户ID
Success
Boolean
是
成功与否(true/false)
UpdateTime
String(50)
是
处理完结时间，
格式yyyy-MM-dd HH:mm:ss
Reason
String(50)
是
描述
5.10.7示列报文
请求示列
JSON
复制代码
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
{
	"RequestData": {
		"PushTime": "2023-02-08 09:43:05",
		"EBusinessID": "350238",
		"Data": [{
			"OldShipperCode": "JD",
			"OldLogisticCode": "JDX013991690210",
			"ShipperCode": "SF",
			"CreateTime": "2023-02-08 17:33:28",
			"OrderCode": "1000016757787409744524357",
			"EBusinessID": "350238",
			"PickerInfo": [{
				"PickupCode": "1679"
			}],
			"Cost": 0.00,
			"Success": true,
			"CallRequestType": "1801",
			"Reason": "转单",
			"LogisticCode": "SF1652504104621",
			"State": "109",
			"FetchTime": "",
			"KDNOrderCode": "KDN2302080910004015",
			"startTime": "2023-02-08 11:00:00",
			"endTime": "2023-02-08 13:00:00",
			"OperateType": 2
		}],
		"Count": 1
	},
	"DataSign": "NDJjYTAyNTFhNmZkNDM3ZTk5ZGY5MDE1ZjE1YjY0YWU=",
	"RequestType": 103
}
响应报文
JSON
复制代码
1
2
3
4
5
6
{
	"EBusinessID": "350238",
	"UpdateTime": "2023-02-08 16:54:59",
	"Success": true,
	"Reason": "成功"
}


5.11推送预约时间变更通知
5.11.1功能说明
快递公司修改取件时间，快递鸟将新的预约时间推送给客户，用户通过修改接口主动修改预约时间不会通过此状态进行推送。
5.11.2基本信息
状态码
110
支持格式
只支持Json格式、UTF-8编码
Content-type：application/x-www-form-urlencoded;charset=UTF-8
批量请求
不支持
超时时间
5000ms
5.11.3状态说明
客户无法在预约时间寄出快递，通知快递员修改取件时间，快递鸟将新的预约时间推送给客户。
5.11.4应用场景
寄件人老七预约了上门取件后，快递员联系老七沟通，会晚于预约时间上门取件，老七同意，于是约定新的上门时间，然后快递员在自己的手机端修改成新的预约时间。快递鸟推送快递员修改的新的预约时间给客户。用户老七在手机端可以看到新的预约时间。
5.11.5应用级参数
名称
类型(字符长度)
是否必须
描述
PushTime
String(64)
是
订单下单时间，
格式  yyyy-MM-dd HH:mm:ss
EBusinessID
String(64)
是
用户ID
Data
KDNOrderCode
String(64)
是
快递鸟单号
OrderCode
String(64)
是
商家订单号
LogisticCode
String(64)
否
物流运单号
Reason
String(255)
否
修改取件时间的描述
ShipperCode
String(64)
是
快递公司编码
State
String(8)
是
状态码，固定110
StartDate
String(50)
是
取件开始时间,
格式：yyyy-MM-dd HH:mm:ss
EndDate
String(50)
是
取件结束时间,
格式：yyyy-MM-dd HH:mm:ss
CreateTime
String(50)
是
状态推送时间,
格式：yyyy-MM-dd HH:mm:ss
OperateType
Int(2)
是
操作人 1:快递鸟 2:物流公司
CallRequestType
String(8)
是
调用的接口指令，例：1801
Count
Int(2)
是
推送的个数
5.11.6返回参数
名称
类型(字符长度)
是否必须
描述
EBusinessID
String(10)
是
用户ID
Success
Boolean
是
成功与否(true/false)
UpdateTime
String(50)
是
处理完结时间，
格式yyyy-MM-dd HH:mm:ss
Reason
String(50)
是
描述
5.11.7示列报文
请求示列
JSON
复制代码
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
{
	"RequestData": {
		"PushTime": "2023-02-08 10:13:11",
		"EBusinessID": "350238",
		"Data": [{
			"ShipperCode": "JD",
			"CreateTime": "2023-02-08 18:52:04",
			"OrderCode": "1000016758223736561210574",
			"EBusinessID": "350238",
			"PickerInfo": [{
				"PickupCode": "1255"
			}],
			"Cost": 0.00,
			"EndDate": "2023-02-08 20:30:00",
			"Success": true,
			"CallRequestType": "1801",
			"Reason": "修改原因：客户更改揽收时间；",
			"StartDate": "2023-02-08 20:00:00",
			"LogisticCode": "JDX013992017593",
			"State": "110",
			"FetchTime": "",
			"KDNOrderCode": "KDN2302081010001351",
			"OperateType": 2
		}],
		"Count": 1
	},
	"DataSign": "NDJjYTAyNTFhNmZkNDM3ZTk5ZGY5MDE1ZjE1YjY0YWU=",
	"RequestType": 103
}
响应报文
JSON
复制代码
1
2
3
4
5
6
{
	"EBusinessID": "350238",
	"UpdateTime": "2023-02-08 16:54:59",
	"Success": true,
	"Reason": "成功"
}


5.12推送更换运单号信息
5.12.1功能说明
快递公司更换运单号后，快递鸟将新运单号推送给客户。
5.12.2基本信息
状态码
302
支持格式
只支持Json格式、UTF-8编码
Content-type：application/x-www-form-urlencoded;charset=UTF-8
批量请求
不支持
超时时间
5000ms
5.12.3状态说明
此状态会更新运单号，推送最新运单号给客户。
5.12.4应用场景
快递公司因为单号异常更换了单号，平台更新后通知客户。
场景一：老七选择了上门取件，快递员揽件后，因误操作，需要更换运单号。于是快递鸟把更换后运单号推送给平台，老七在平台手机端看到新的运单号。
5.12.5应用级参数
名称
类型(字符长度)
是否必须
描述
PushTime
String(64)
是
订单下单时间，
格式  yyyy-MM-dd HH:mm:ss
EBusinessID
String(64)
是
用户ID
Data
KDNOrderCode
String(64)
是
快递鸟单号
OrderCode
String(64)
是
商家订单号
LogisticCode
String(64)
是
物流运单号
ShipperCode
String(64)
是
快递公司编码
Reason
String(255)
否
原因描述
State
String(8)
是
状态码，固定302
CreateTime
String(50)
是
状态推送时间,
格式：yyyy-MM-dd HH:mm:ss
OperateType
Int(2)
是
操作人 1:快递鸟 2:物流公司
CallRequestType
String(8)
是
调用的接口指令，例：1801
Count
Int(2)
是
推送的个数
5.12.6返回参数
名称
类型(字符长度)
是否必须
描述
EBusinessID
String(10)
是
用户ID
Success
Boolean
是
成功与否(true/false)
UpdateTime
String(50)
是
处理完结时间，
格式yyyy-MM-dd HH:mm:ss
Reason
String(50)
是
描述
5.12.7示列报文
请求示列
JSON
复制代码
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
{
	"RequestData": {
		"PushTime": "2023-01-10 10:53:26",
		"EBusinessID": "350238",
		"Data": [{
			"ShipperCode": "JD",
			"CreateTime": "2023-02-07 13:56:46",
			"OrderCode": "1000016731858031488936188",
			"EBusinessID": "350238",
			"PickerInfo": [{
				"PickupCode": "9737"
			}],
			"Cost": 10.00,
			"Success": true,
			"CallRequestType": "1801",
			"Weight": 1.00,
			"Reason": "更换运单号",
			"LogisticCode": "JDVC17349565502",
			"State": "302",
			"FetchTime": "",
			"KDNOrderCode": "KDN2301101010005765",
			"OperateType": 2
		}],
		"Count": 1
	},
	"DataSign": "NDJjYTAyNTFhNmZkNDM3ZTk5ZGY5MDE1ZjE1YjY0YWU=",
	"RequestType": 103
}
响应报文
JSON
复制代码
1
2
3
4
5
6
{
	"EBusinessID": "350238",
	"UpdateTime": "2023-02-08 16:54:59",
	"Success": true,
	"Reason": "成功"
}


5.13推送虚假揽件状态
5.13.1功能说明
当订单出现虚假揽件后，快递鸟核实属实，然后推送此订单状态为虚假揽件给客户，此订单快递鸟与平台不结算。
5.13.2基本信息
状态码
206
支持格式
只支持Json格式、UTF-8编码
Content-type：application/x-www-form-urlencoded;charset=UTF-8
批量请求
不支持
超时时间
5000ms
5.13.3状态说明
当订单出现虚假揽件后，快递鸟核实属实，然后推送此订单状态为虚假揽件给客户。用户可以重新下单。
5.13.4应用场景
快递员没有揽件，确回传了揽件的报文，我们收到客诉后核实属实后会推送此状态。
场景一：老七下单上门取件，快递员上门揽件后，老七拿回快递决定不寄了。但是订单状态却是已取件或已揽件。老七反馈给平台或快递鸟监控到，快递鸟对订单做虚假揽件操作。老七重新下单。
5.13.5应用级参数
名称
类型(字符长度)
是否必须
描述
PushTime
String(64)
是
订单下单时间，
格式  yyyy-MM-dd HH:mm:ss
EBusinessID
String(64)
是
用户ID
Data
KDNOrderCode
String(64)
是
快递鸟单号
OrderCode
String(64)
是
商家订单号
LogisticCode
String(64)
是
物流运单号
ShipperCode
String(64)
是
快递公司编码
Reason
String(255)
否
原因描述
State
String(8)
是
状态码，固定206
CreateTime
String(50)
是
状态推送时间,
格式：yyyy-MM-dd HH:mm:ss
OperateType
Int(2)
是
操作人 1:快递鸟 2:物流公司
CallRequestType
String(8)
是
调用的接口指令，例：1801
Count
Int(2)
是
推送的个数
5.13.6返回参数
名称
类型(字符长度)
是否必须
描述
EBusinessID
String(10)
是
用户ID
Success
Boolean
是
成功与否(true/false)
UpdateTime
String(50)
是
处理完结时间，
格式yyyy-MM-dd HH:mm:ss
Reason
String(50)
是
描述
5.13.7示列报文
请求示列
JSON
复制代码
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
{
	"RequestData": {
		"PushTime": "2023-02-02 15:42:06",
		"EBusinessID": "350238",
		"Data": [{
			"ShipperCode": "YTO",
			"CreateTime": "2023-02-08 17:38:45",
			"OrderCode": "exp2302021541522906nj",
			"EBusinessID": "350238",
			"PickerInfo": [{
				"PickupCode": "1150"
			}],
			"Cost": 5.50,
			"Success": true,
			"CallRequestType": "1801",
			"Weight": 1.00,
			"Reason": "商户异常订单",
			"LogisticCode": "YT2284123435822",
			"State": "206",
			"FetchTime": "",
			"KDNOrderCode": "KDN2302021510005154",
			"OperateType": 2
		}],
		"Count": 1
	},
	"DataSign": "NDJjYTAyNTFhNmZkNDM3ZTk5ZGY5MDE1ZjE1YjY0YWU=",
	"RequestType": 103
}
响应报文
JSON
复制代码
1
2
3
4
5
6
{
	"EBusinessID": "350238",
	"UpdateTime": "2023-02-08 16:54:59",
	"Success": true,
	"Reason": "成功"
}


5.14推送线下收费状态
5.14.1功能说明
当订单发生线下收费后，快递鸟核实属实，然后推送此订单状态为线下收费给客户。
5.14.2基本信息
状态码
207
支持格式
只支持Json格式、UTF-8编码
Content-type：application/x-www-form-urlencoded;charset=UTF-8
批量请求
不支持
超时时间
5000ms
5.14.3状态说明
当订单发生线下收费后，快递鸟核实属实，然后推送此订单状态为线下收费给客户，收到此状态需要客户做相应的业务处理。
5.14.4应用场景
快递员线下收取运费，我们收到客诉会推送此状态。
场景一：老七选择上门取件下单，快递员上门揽件后，老七线下使用微信/现金/支付宝等方式直接向快递员支付快递费，没有在平台线上支付上门取件运费。
场景二：老七选择上门取件下单，快递员上门揽件后，老七线下使用微信/现金/支付宝等方式直接向快递员支付快递费，又在平台线上支付了上门取件运费。
5.14.5应用级参数
名称
类型(字符长度)
是否必须
描述
PushTime
String(64)
是
订单下单时间，
格式  yyyy-MM-dd HH:mm:ss
EBusinessID
String(64)
是
用户ID
Data
KDNOrderCode
String(64)
是
快递鸟单号
OrderCode
String(64)
是
商家订单号
LogisticCode
String(64)
是
物流运单号
ShipperCode
String(64)
是
快递公司编码
Reason
String(255)
否
原因描述
State
String(8)
是
状态码，固定207
CreateTime
String(50)
是
状态推送时间,
格式：yyyy-MM-dd HH:mm:ss
OperateType
Int(2)
是
操作人 1:快递鸟 2:物流公司
CallRequestType
String(8)
是
调用的接口指令，例：1801
Count
Int(2)
是
推送的个数
5.14.6返回参数
名称
类型(字符长度)
是否必须
描述
EBusinessID
String(10)
是
用户ID
Success
Boolean
是
成功与否(true/false)
UpdateTime
String(50)
是
处理完结时间，
格式yyyy-MM-dd HH:mm:ss
Reason
String(50)
是
描述
5.14.7示列报文
请求示列
JSON
复制代码
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
{
	"RequestData": {
		"PushTime": "2023-02-16 15:22:52",
		"EBusinessID": "350238",
		"Data": [{
			"ShipperCode": "YTO",
			"CreateTime": "2023-02-17 14:44:40",
			"OrderCode": "202302161522521887",
			"EBusinessID": "350238",
			"PickerInfo": [{
				"PickupCode": "1892"
			}],
			"Cost": 5.50,
			"Success": true,
			"CallRequestType": "1801",
			"Weight": 1.00,
			"Reason": "商户异常订单",
			"LogisticCode": "YT2290019907369",
			"State": "207",
			"FetchTime": "",
			"KDNOrderCode": "KDN2302161510002779",
			"OperateType": 2
		}],
		"Count": 1
	},
	"DataSign": "MGFkYTI5YjJhZjE1NzJiMjUxMzYwN2UxZTUzNGQ5OWY =",
	"RequestType": 103
}
响应报文
JSON
复制代码
1
2
3
4
5
6
{
	"EBusinessID": "350238",
	"UpdateTime": "2023-02-08 16:54:59",
	"Success": true,
	"Reason": "成功"
}


5.15推送重量修正结果
5.15.1功能说明
重量异常修正结果推送给客户。
5.15.2基本信息
状态码
208
支持格式
只支持Json格式、UTF-8编码
Content-type：application/x-www-form-urlencoded;charset=UTF-8
批量请求
不支持
超时时间
5000ms
5.15.3状态说明
因为重量异常，会修正重量，将结果推送给到客户。
5.15.4应用场景
客户和快递公司给的重量不一致，最终确认后重量会通过此状态推送给客户。
场景一：老七使用上门取件寄了两本书，实际重量2KG，但是快递员揽件后，平台线上显示重量20KG，于是老七向平台客户反馈。客户反馈给快递鸟客服，快递鸟客服最终核实为2KG，于是修改系统重量为2KG，并把数据推送给客户。老七愉快的支付了运费。
5.15.5应用级参数
名称
类型(字符长度)
是否必须
描述
PushTime
String(64)
是
订单下单时间，
格式  yyyy-MM-dd HH:mm:ss
EBusinessID
String(64)
是
用户ID
Data
KDNOrderCode
String(64)
是
快递鸟单号
LogisticCode
String(64)
是
物流运单号
OrderCode
String(64)
是
商家订单号
Reason
String(255)
否
推送的描述
ShipperCode
String(64)
是
快递公司编码
State
String(8)
是
状态码，固定208
CreateTime
String(50)
是
状态推送时间，
格式  yyyy-MM-dd HH:mm:ss
OperateType
Int(2)
是
操作人 1:快递鸟 2：物流公司
CallRequestType
String(8)
是
调用的接口指令，例：1801
Volume
Double(10,3)
否
体积，单位：立方厘米
VolumeWeight
Double(10,3)
否
体积重，体积/8000
ActualWeight
Double(10,2)
否
称重重量
Weight
Double(10,2)
是
重量，例：1.00kg
Cost
Double(10,4)
是
基础运费=首重金额+续重总金额
FirstWeightAmount
Double(10,2)
是
首重金额
ContinuousWeightAmount
Double(10,2)
是
续重总金额
InsureAmount
Double(10,2)
否
保价费
PackageFee
Double(10,2)
否
包装费
OverFee
Double(10,2)
否
超长超重费
OtherFee
Double(10,2)
否
其他费
OtherFeeDetail
String
否
其他费明细，json格式，
如{\"打包服务费\":\"0.50\",\"资源调节费\":\"1.00\"}
TotalFee
Double(10,2)
是
总费用=基础运费+保价费+包装费+超长超重费+其他费
Count
Int(2)
是
推送的个数
5.15.6返回参数
名称
类型(字符长度)
是否必须
描述
EBusinessID
String(10)
是
用户ID
Success
Boolean
是
成功与否(true/false)
UpdateTime
String(50)
是
处理完结时间，
格式yyyy-MM-dd HH:mm:ss
Reason
String(50)
是
描述
5.15.7示列报文
请求示列
JSON
复制代码
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
{
	"RequestData": {
		"PushTime": "2024-01-23 11:54:54",
		"EBusinessID": "350238",
		"Data": [{
			"CreateTime": "2024-01-22 15:10:38",
			"EBusinessID": "350238",
			"ShipperCode": "YTO",
			"LogisticCode": "YT2513752425246",
			"KDNOrderCode": "KDNSIT2401221710000002",
			"OrderCode": "2024012217450524138",
			"PickerInfo": [{
				"PickupCode": "1234"
			}],
			"CallRequestType": "1801",
			"State": "208",
			"FetchTime": "",
			"Reason": "修改重量",
			"Weight": 2.0,
			"FirstWeightAmount": "6.50",
			"ContinuousWeightAmount": "2.00",
			"Cost": 8.50,
			"InsureAmount": "0.00",
			"PackageFee": "0.00",
			"OverFee": "0.00",
			"OtherFee": "1.00",
			"OtherFeeDetail": "{\"其他费用\":\"1.00\"}",
			"TotalFee": 9.50,
			"Volume": 19200.0,
			"VolumeWeight": 1.500,
			"Success": true,
			"OperateType": 2
		}],
		"Count": 1
	},
	"DataSign": "NDJjYTAyNTFhNmZkNDM3ZTk5ZGY5MDE1ZjE1YjY0YWU=",
	"RequestType": 103
}
响应报文
JSON
复制代码
1
2
3
4
5
6
{
	"EBusinessID": "350238",
	"UpdateTime": "2023-02-08 16:54:59",
	"Success": true,
	"Reason": "成功"
}


5.16推送订单费用状态(申通)
5.16.1功能说明
1.客户订阅该状态后，推送订单的实际费用给客户。
2.仅支持指定申通下单产品（其他的产品在揽件节点state:301中推送结算费用）。
3.支付方式有以下两种可供参考：
a：收取到state：301揽件状态时，更新订单状态为揽收+展示单号，等待快递鸟推送state：601时在做费用收取（适用于对平台客户付费有一定掌控力度的，一次性收取配送费用）；
b：收取到state：301时，更新订单状态为揽收+展示单号，收取首次费用，待快递鸟推送state：601时做费用二次核算，根据最终的state：601的费用做最终费用收取（适用于对客户付费掌控力度较一般的平台，可能需要做两次费用收取）；
5.16.2基本信息
状态码
601
支持格式
只支持Json格式、UTF-8编码
Content-type：application/x-www-form-urlencoded;charset=UTF-8
批量请求
不支持
超时时间
5000ms
5.16.3状态说明
推送订单的实际结算费用给客户。
5.16.4应用场景
快递员揽件后，快递公司将揽收详情数据给到快递鸟，快递鸟把订单状态，重量运费数据回推给平台，平台接收数据后自行二次处理，并将应付费用明细展示在前端，唤起用户进行实际运费支付。
5.16.5应用级参数
名称
类型(字符长度)
是否必须
描述
PushTime
String(64)
是
订单下单时间，
格式  yyyy-MM-dd HH:mm:ss
EBusinessID
String(64)
是
用户ID
Data
KDNOrderCode
String(64)
是
快递鸟单号
LogisticCode
String(64)
是
物流运单号
OrderCode
String(64)
是
商家订单号
Reason
String(255)
否
推送的描述
ShipperCode
String(64)
是
快递公司编码
State
String(8)
是
状态码，固定601
CreateTime
String(50)
是
状态推送时间，
格式  yyyy-MM-dd HH:mm:ss
OperateType
Int(2)
是
操作人 1:快递鸟 2：物流公司
CallRequestType
String(8)
是
调用的接口指令，例：1801
Volume
Double(10,3)
否
体积，单位：立方厘米
VolumeWeight
Double(10,3)
否
体积重，体积/8000
ActualWeight
Double（10，2）
否
称重重量
Weight
Double(10,2)
是
重量，例：1.00kg
Cost
Double(10,4)
是
基础运费=首重金额+续重总金额
FirstWeightAmount
Double(10,2)
是
首重金额
ContinuousWeightAmount
Double(10,2)
是
续重总金额
InsureAmount
Double(10,2)
否
保价费
PackageFee
Double(10,2)
否
包装费
OverFee
Double(10,2)
否
超长超重费
OtherFee
Double(10,2)
否
其他费
OtherFeeDetail
String
否
其他费明细，json格式，
如{\"打包服务费\":\"0.50\",\"资源调节费\":\"1.00\"}
TotalFee
Double(10,2)
是
总费用=基础运费+保价费+包装费+超长超重费+其他费
Count
Int(2)
是
推送的个数
5.16.6返回参数
名称
类型(字符长度)
是否必须
描述
EBusinessID
String(10)
是
用户ID
Success
Boolean
是
成功与否(true/false)
UpdateTime
String(50)
是
处理完结时间，
格式yyyy-MM-dd HH:mm:ss
Reason
String(50)
是
描述
5.16.7示列报文
请求示列
回推示例报文
JSON
复制代码
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
{
	"RequestData": {
		"PushTime": "2023-02-07 15:28:44",
		"EBusinessID": "350238",
		"Data": [{
			"ShipperCode": "JD",
			"FirstWeightAmount": "6.50",
			"Cost": 12.50,
			"Success": true,
			"Reason": "费用推送完成",
			"PackageFee": "1.00",
			"VolumeWeight": 2.500,
			"OperateType": 2,
			"OverFee": "1.00",
			"CreateTime": "2023-02-07 18:10:22",
			"ContinuousWeightAmount": "6.00",
			"OrderCode": "2001ATIST2023020700003660101",
			"EBusinessID": "350238",
			"PickerInfo": [{
				"PickupCode": "1977"
			}],
			"CallRequestType": "1801",
			"Weight": 3.00,
			"LogisticCode": "JDX013983728385",
			"TotalFee": 25.58,
			"Volume": 19200.000,
			"State": "301",
			"FetchTime": "",
			"KDNOrderCode": "KDN2302071510003275",
			"OtherFee": "1.00",
			"OtherFeeDetail": "{\"打包服务费\":\"1.00\"}",
			"InsureAmount": "10.08"
		}],
		"Count": 1
	},
	"DataSign": "NDJjYTAyNTFhNmZkNDM3ZTk5ZGY5MDE1ZjE1YjY0YWU=",
	"RequestType": 103
}
响应报文
JSON
复制代码
1
2
3
4
5
6
{
	"EBusinessID": "350238",
	"UpdateTime": "2023-02-08 16:54:59",
	"Success": true,
	"Reason": "成功"
}


5.18推送工单处理结果
5.18.1功能说明
推送工单处理结果给客户。
5.18.2基本信息
状态码
401
支持格式
只支持Json格式、UTF-8编码
Content-type：application/x-www-form-urlencoded;charset=UTF-8
批量请求
不支持
超时时间
5000ms
5.18.3状态说明
推送工单处理结果给客户。
5.18.4应用场景
客户提交工单后，快递鸟把处理结果通过回推接口通知客户。
5.18.5应用级参数
名称
类型(字符长度)
是否必须
描述
PushTime
String(64)
是
订单下单时间，
格式  yyyy-MM-dd HH:mm:ss
EBusinessID
String(64)
是
用户ID
Data
KDNOrderCode
String(64)
是
快递鸟单号
LogisticCode
String(64)
是
物流运单号
OrderCode
String(64)
是
商家订单号
ShipperCode
String(64)
是
快递公司编码
Reason
String(255)
是
工单回复内容
TicketSource
String(64)
是
工单来源
1：客户(对接方管理后台提交)
3：快递鸟(快递鸟客服内部提交)
4：云工单(C端用户寄件前端提交) 
TicketId
String(64)
是
工单处理单号
TicketPic
String(1000)
否
工单提交的附件URL，多个凭证以英文逗号“,”隔开
DealResultFiles
String(1000)
否
处理结果附件URL，多个结果附件以英文逗号“,”隔开
State
String(8)
是
状态码，固定401
CreateTime
String(50)
是
状态推送时间,
格式：yyyy-MM-dd HH:mm:ss
OperateType
Int(2)
是
操作人 1:快递鸟 2：物流公司
CallRequestType
String(8)
是
调用的接口指令
Count
Int(2)
是
推送的个数
5.18.6返回参数
名称
类型(字符长度)
是否必须
描述
EBusinessID
String(10)
是
用户ID
Success
Boolean
是
成功与否(true/false)
UpdateTime
String(50)
是
处理完结时间，
格式yyyy-MM-dd HH:mm:ss
Reason
String(50)
是
描述
5.18.7示列报文
请求示列
JSON
复制代码
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
{
	"RequestData": {
		"PushTime": "2023-07-14 21:11:23",
		"EBusinessID": "1803012",
		"Data": [{
			"ShipperCode": "YTO",
			"CreateTime": "2023-07-18 14:54:50",
			"OrderCode": "O202307142111164735",
			"EBusinessID": "350238",
			"PickerInfo": [{
				"PickupCode": "1658"
			}],
			"TicketPic": "http://file.kdfafa.com/images/public/202307162109529735XTK9WW.png",
			"Cost": 31.80,
			"Success": true,
			"CallRequestType": "1801",
			"Weight": 11.50,
			"Reason": "经核实，包裹重量xxKG核实无误，感谢您的支持。",
			"DealResultFiles": "",
			"TicketSource": 4,
			"LogisticCode": "YT2501688292585",
			"TicketId": "CPN2307162110000701",
			"State": "401",
			"FetchTime": "",
			"KDNOrderCode": "KDN2307142110000698",
			"OperateType": 2
		}],
		"Count": 1
	},
	"DataSign": "NDJjYTAyNTFhNmZkNDM3ZTk5ZGY5MDE1ZjE1YjY0YWU=",
	"RequestType": 103
}
响应报文
JSON
复制代码
1
2
3
4
5
6
{
	"EBusinessID": "350238",
	"UpdateTime": "2023-02-08 16:54:59",
	"Success": true,
	"Reason": "成功"
}

5.19推送工单赔付结果
5.19.1功能说明
客户订阅该状态后，推送工单赔付结果给客户。
5.19.2基本信息
状态码
501
支持格式
只支持Json格式、UTF-8编码
Content-type：application/x-www-form-urlencoded;charset=UTF-8
批量请求
不支持
超时时间
5000ms
5.19.3状态说明
推送工单赔付处理结果给客户。
备注：
    业务赔付（一般是指丢破损赔付类，和快递公司相关的）
    服务赔付（一般是指客服核实后，服务不达标/为挽回客户信任时做的赔付）
5.19.4应用场景
快递公司或者快递鸟处理工单赔付后，快递鸟把处理结果通过回推接口通知客户。
5.19.5应用级参数
名称
类型(字符长度)
是否必须
描述
KDNOrderCode
String(64)
是
快递鸟单号
LogisticCode
String(64)
否
运单号
OrderCode
String(64)
是
商家订单号
ShipperCode
String(64)
是
快递公司编码
TicketPayment
String(64)
是
工单号
PaymentNumber
String(64)
是
赔付单号
CustomerSettlement
tinyint(2)
是
运费是否结算    1:是  0:否
(客户与KDN订单运费是否结算)
PaymentTotalAmount
Double(10,2)
是
赔付总金额（两位小数）
业务赔付明细和服务赔付明细总和
BusinessPaymentDetail
Object
业务赔付明细
paymentContent
String(200)
否
赔付内容：工单类型字典编码
(多个编码逗号分隔，“,”)
paymentDetailList
List
否
赔付明细（集合数组结构）
paymentType
tinyint(2)
否
赔付方式
KDN赔付：
1-KDN赔付给用户(下单人)
2-KDN赔付给平台(客户)
渠道赔付：
1-渠道赔付给用户(下单人)
如：paymentType=1  
paymenAmoumt
Double(10,2)
否
赔付金额，保留两位小数。
如：赔付金额（99.00）
payStatus
tinyint(2)
否
赔付方
1-KDN赔付，2-渠道赔付
如：payStatus=1  
ServicePaymentDetail
Object
服务赔付明细
paymentContent
String(200)
否
赔付内容：工单类型字典编码
(多个编码逗号分隔，“,”)
paymenAmoumt
Double(10,2)
否
赔付金额，保留两位小数。
如：赔付金额（99.00）
State
String(8)
是
状态码，固定501
CreateTime
String(50)
是
推送时间，
格式：yyyy-MM-dd HH:mm:ss
CallRequestType
String(8)
是
调用的接口指令 1801
OperateType
Int(2)
是
操作人 1:快递鸟 2:物流公司
Reason
String(50)
是
描述
Success
Boolean
是
成功与否(true/false)
EBusinessID
String(10)
是
用户ID
5.19.6返回参数
名称
类型(字符长度)
是否必须
描述
EBusinessID
String(10)
是
用户ID
Success
Boolean
是
成功与否(true/false)
UpdateTime
String(50)
是
处理完结时间，
格式yyyy-MM-dd HH:mm:ss
Reason
String(50)
是
描述
5.19.7示列报文
请求示列
JSON
复制代码
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
{
	"RequestData": {
		"PushTime": "2022-07-14 21:29:56",
		"EBusinessID": "1693823",
		"Data": [{
			"TicketPayment": "20220714213018787000004",
			"PaymentTotalAmount": "8.73",
			"ShipperCode": "STO",
			"CreateTime": "2022-07-15 11:35:00",
			"ServicePaymentDetail": {
				"paymentAmount": "3.00",
				"paymentContent": "102"
			},
			"OrderCode": "ZRH2022071317221",
			"CallRequestType": "1801",
			"Reason": "工单赔付结果推送",
			"Success": true,
			"LogisticCode": "884000346783980",
			"BusinessPaymentDetail": {
				"paymentContent": "106,11",
				"paymentDetailList": [{
						"paymentAmount": "2.50",
						"paymentStatus": 1,
						"paymentType": 1
					},
					{
						"paymentAmount": "3.23",
						"paymentStatus": 2,
						"paymentType": 1
					}
				]
			},
			"State": "501",
			"KDNOrderCode": "KDNSIT2207142110000002",
			"CustomerSettlement": 1,
			"OperateType": 1,
			"PaymentNumber": "TP20220715112833681000001"
		}],
		"Count": 1
	},
	"DataSign": "ZTE2ZGU1YWVjYjQzMzZkY2JmY2M3MTgwMmM1YmIxYzE =",
	"RequestType": 103
}
响应报文
JSON
复制代码
1
2
3
4
5
6
{
	"EBusinessID": "350238",
	"UpdateTime": "2023-02-08 16:54:59",
	"Success": true,
	"Reason": "成功"
}