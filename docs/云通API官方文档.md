窗体顶端
商户编号：253335
ApiKey：NjkyMTM4MDNkMWFhNjNhNzU3Y2UzNWJmZjUzNDkyMzQ55525 重置
回调地址： 提交
窗体底端
API文档（接入指引）
接口规范及说明 

1.
报文及报文编码： 报文格式 ： Json 格 式 ， 请 求 方 法 的 编 码 格 式 (utf-8) ： "application/x-www-form-urlencoded;charset=utf-8" 交互协议上统一用 UTF-8，避免传递中文数据出现乱码。
2.
3.
应用级参数(RequestData)值为 JSON 格式，使用 UTF-8 对 JSON 串进行 URL 编码后赋值给 RequestData 即可完成应用级参数的组装。
4.

5.
系统级参数 
名称	类型(字符长度)	是否必须	描述
RequestData	String	是	应用级参数，内容为 JSON 格式
EBusinessID	String	是	商户编号
RequestType	String	是	请求接口指令
DataSign	String	是	数据内容签名
DataType	String	是	请求、返回数据格式，目前只支持 JSON 格式，固定值 DataType=2
   所有接口统一使用此系统级参数，根据不同的请求接口指令和应用级参数接入不同的接口。

关于签名
在 POST 请求中会传递 5 个必须(R)参数 RequestData=数据内容(URL 编码:UTF-8) 
EBusinessID=商户编号
RequestType=请求指令类型 
DataSign= 数据内容签名：把(RequestData(未编码)+ApiKey)进行 MD5 加密，然后 Base64 编码， 最后进行 URL(utf-8)编码 
DataType=2(请求和返回数据类型格式为 json) 注： DataSign 生成后，对方接收到数据后，以同样的算法进行签名生成摘要，对比两者的摘要是否相同，如果不同，说明传递过程中 发生数据篡改。

PHP签名：

   /**
    * 电商Sign签名生成
    * @param data 内容   
    * @param ApiKey ApiKey
    * @return DataSign签名
    */
   public function encrypt($data, $ApiKey) {
           return urlencode(base64_encode(md5($data.$ApiKey)));
    }

JAVA签名：

      /**
    * 电商Sign签名生成
    * @param data 内容   
    * @param ApiKey ApiKey
    * @return DataSign签名
    */
    public static String encrypt(String data, String ApiKey) {  
        try {  
            // 拼接数据和ApiKey  
            String combined = data + ApiKey;  
 
            // 计算MD5值  
            MessageDigest md = MessageDigest.getInstance("MD5");  
            byte[] mdBytes = md.digest(combined.getBytes(StandardCharsets.UTF_8));  
 
            // 将MD5字节数组转换为Base64编码  
            String base64Encoded = Base64.getEncoder().encodeToString(mdBytes);  
 
            // URL编码（需要自定义方法）  
            String urlEncoded = urlEncode(base64Encoded);  
 
            return urlEncoded;  
        } catch (NoSuchAlgorithmException e) {  
            // MD5通常不会抛出此异常，但最好还是处理一下  
            throw new RuntimeException("MD5 not supported", e);  
        }  
    }  



API文档（下单接口）
下单接口

基本信息
接口指令	1801
请求方式	POST
支持格式	只支持 Json 格式、UTF-8 编码
批量请求	不支持
接口地址	https://open.yuntongzy.com/express/api/OrderService


接口调用规则
1.
按照文档规则进行传参
2.
3.
预约时提供的收寄方信息必须真实准确。
4.
5.
用户提供的寄件地址若超出快递公司服务范围，则无快递员上门揽件。
6.
7.
因用户原因(如：恶意下单、批量下单长时间不发货等)导致快递公司投诉，将停用此用户接口权限。
8.
9.
接口字段必填项：需客户进行自校验，通过后，再调用接口。
10.

应用级参数
名称	类型	是否必填	描述
ShipperCode	String	是	快递公司编码 （德邦=DB，京东=JD) 参考：《快递公司代码表》
OrderCode	String	是	订单编号(自定义，不可重复)
ChannelType	Int	否	渠道类型：0全国不限制，1只发得物，2只发电商，不传默认为0
ExpType	String	是	德邦（PACKAGE标准，RCP360大件)，京东（P1特惠送，P2特快送）
参考：《快递公司代码表》
PayType	Int	是	付款方式：0现结，1到付，2月结(默认)
shipMethod	String	否	送货方式（德邦专用，PICKSELF = 自提，PICKNOTUPSTAIRS=送货不上楼，PICKUPSTAIRS=送货上楼，SEND_BIG_UPSTAIRS=大件上楼）
returnMethod	Int	否	接口返回方式（德邦专用，1同步返回，0异步返回，默认1）异步返回请接收状态码100和400，100-下单成功，推送带运单号，400-下单失败，带失败原因。
mailNumber	String	否	预埋单号
Receiver	Name	String	是	收件人姓名
	Tel	String	是	收件人电话和手机号码，必填一个 Tel：区号+尾数 Mobile：11 位手机号码，1 开头 11 位数字
	Mobile	String		
	ProvinceName	String	是	收件省 (如广东省，不要缺少“省”；如是直 辖市，请直接传北京、上海等； 如是自治区，请直接传广西壮族自治区 等) 支持字符长度为 20 个以内，不支持数 字与字母
	CityName	String	是	收件市(如深圳市，不要缺少“市； 如是市辖区，请直接传北京市、上海市 等”) 支持字符长度为 20 个以内，不支持数 字与字母
	ExpAreaName	String	是	收件区/县(如福田区，不要缺少“区” 或“县”)
	Address	String	是	收件人详细地址（填写具体的街道、门 牌号，不要将省市区加入其中）
Sender	Name	String	是	寄件人姓名
	Tel	String	是	寄件人电话和手机号码，必填一个 Tel：区号+尾数 Mobile：11 位手机号码，1 开头 11 位数字
	Mobile	String		
	ProvinceName	String	是	寄件省 (如广东省，不要缺少“省”；如是直 辖市，请直接传北京、上海等； 如是自治区，请直接传广西壮族自治区 等) 支持字符长度为 20 个以内，不支持数 字与字母
	CityName	String	是	寄件市(如深圳市，不要缺少“市； 如是市辖区，请直接传北京市、上海市 等”) 支持字符长度为 20 个以内，不支持数 字与字母
	ExpAreaName	String	是	寄件区/县(如福田区，不要缺少“区” 或“县”)
	Address	String	是	寄件人详细地址（填写具体的街道、门 牌号，不要将省市区加入其中）
StartDate	String	否	预约开始时间和结束时间，格式： YYYY-MM-DD HH:MM:SS， 如不传则系统默认分配最近时间上门 取件
EndDate			
Weight	Double	是	包裹总重量 kg
Quantity	Int	是	包裹数
Volume	Double	否	包裹总体积 m3
Remark		String	否	备注。不可大于 60 个字符
AddService	Name	String	否	增值服务名称（未启用）
	Value	String		增值服务值（未启用）
	CustomerID	String		客户标识
Commodity[]	GoodsName	String	否	商品名称 不可大于 50 个字符
	Goodsquantity	Int	否	商品件数
	GoodsPrice	Double	否	商品价格（若不使用保价可不填）
InsureAmount	Double	否	声明价值

返回参数
名称	类型	是否必须	描述
EBusinessID	 String	是 	商户编号
Order	OrderCode	String	是	订单编号
	PlatCode	String	是	平台订单编号
	LogisticCode	String	否	快递单号
	PrintData	Object	否	打印数据
Success	Boolean	是	接口调用成功与否(true/false)
ResultCode	int	是 	返回编号
Reason	String	否	描述
UniquerRequestNumber	String	是 	唯一标识
  
 请求应用级参数报文：      
{ 
     "InsureAmount":1000, 
     "ShipperCode":"JD", 
     "OrderCode":"yfftest20220609111", 
     "Receiver":{ 
            "ProvinceName":"广东省", 
            "Address":"暄悦东街 19-21-23 号保利中悦广场 18 楼", 
            "CityName":"广州市",
            "ExpAreaName":"海珠区",
            "Mobile":"***********", 
            "Name":"华珊" 
      },
     "Quantity":1, 
     "PayType":3, 
     "Sender":{ 
            "ProvinceName":"广东省", 
            "Address":"金花路华宝大厦 A 栋 601", 
            "CityName":"深圳市",
            "ExpAreaName":"福田", 
            "Mobile":"***********", 
            "Name":"颜芬芬" 
      },
     "ExpType":"", 
     "Commodity":[ 
            { 
                  "Goodsquantity":1,
                  "GoodsName":"工牌",
             } 
      ] 
}

响应报文:
{ 
      "Order" : { 
            "OrderCode" : "yfftest202104261043", 
            "PlatCode" : "KDN2105082010005103" ,
            "LogisticCode" : "JDVA6655445525"
      },
      "EBusinessID" : "XXXXX", 
      "UniquerRequestNumber" : "61ce9866-5f99-45e8-80c7-29c27543d641", 
      "ResultCode" : 100, 
      "Reason" : "下单成功", 
      "Success" : true
 }

API文档（取消接口）
订单取消接口

基本信息
接口指令	1802
请求方式	POST
支持格式	只支持 Json 格式、UTF-8 编码
批量请求	不支持
接口地址	https://open.yuntongzy.com/express/api/OrderService

接口调用规则
1.
按照文档规则进行传参 
2.
3.
 已取件/已揽件状态后不允许取消；
4.

应用级参数
名称	类型	是否必须	描述
ShipperCode	String	否	快递公司编码，传入规范的快递 公司编码
OrderCode	String	是	客户订单编号(自定义，不可重 复)
LogisticCode	String	否	快递单号
CancelType	Int	否	取消类型：1=预约信息有误， 2=快递员无法取件
CancelMsg	String	否	取消类型描述

返回参数
名称	类型	是否必须	描述
EBusinessID	String	是	商户编号
Success	Boolean	是	接口调用成功与否(true/false)
ResultCode	int	是	返回编码，100为成功，其他为失败
Reason	String	否	描述
UniquerRequestNumber	String	是	唯一标识

请求示例：
{ 
    "ShipperCode": "STO",
     "OrderCode": "D00037207766555577", 
    "CancelType": 1， 
    "CancelMsg": "预约信息有误"
 }

返回示例
{ 
     "EBusinessID" : "1365742",
     "ResultCode" : 100, 
     "Reason" : "成功", 
     "Success" : true
 }

API文档（查询订单）
订单查询接口

基本信息
接口指令	1804
请求方式	POST
支持格式	只支持 Json 格式、UTF-8 编码
批量请求	不支持
接口地址	https://open.yuntongzy.com/express/api/OrderService

接口调用规则
1.
按照文档规则进行传参 
2.
3.
 揽收前返回下单数据，揽收后提供计费信息查询；
4.

应用级参数
名称	类型	是否必须	描述
ShipperCode	String	否	快递公司编码，传入规范的快递 公司编码
OrderCode	String	是	客户订单编号(自定义，不可重 复)

返回参数
名称	类型	是否必须	描述
EBusinessID	String	是	商户编号
Success	Boolean	是	接口调用成功与否(true/false)
ResultCode	int	是	返回编码，100为成功，其他为失败
Reason	String	否	描述
UniquerRequestNumber	String	是	唯一标识
OrderCode	String	是	订单编号
ShipperCode	String	是	快递公司编码
LogisticCode	String	否	快递单号
Weight	Double	否	重量
Cost	Double	否	运费
State	int	是	订单状态码 100=下单成功 102=分配网点 103=分配快递员 104=已取件 301=已揽件 203=取消订单 2=在途中 3=签收
CreateTime	String	是	下单时间，格式： YYYY-MM-DD HH:MM:SS
PersonName	String	否	取件快递员姓名
PersonTel	String	否	取件快递员电话
PersonCode	String	否	取件快递员工号
StationName	String	否	取件网点名称
StationCode	String	否	取件网点编号
StationAddress	String	否	取件网点地址
StationTel	String	 否	取件网点电话
PickupCode	String	否	取件码
InsureAmount	Double	否	保价费
PackageFee	Double	否	包装费
OverFee	Double	否	超长超重费
OtherFee	Double	否	其他费
OtherFeeDetail	String	否	其他费明细 json 格式，如{“打 包服务费”:2.00}
TotalFee	Double	否	总费用=运费+保价费+包装费 +超长超重费+其他费
Volume	Double	否	体积,单位：立方厘米 
chargedWeight	Double	否	计费重量

请求示例：
{ 
    "OrderCode": "012657018199"
 }

返回示例
{ 
      "EBusinessID": "1237100",
      "Success": true, 
      "ResultCode":  100 , 
      "OrderCode":"72751507950551040", 
      "ShipperCode": "SF", 
      "LogisticCode": "234354243168", 
      "Weight": 2， 
      "Cost": 20.00, 
      "State": 301, 
      "CreateTime": "2019-08-21 11:59:19", 
      "PickupCode": "123456",
      "InsureAmount": 3.00, 
      "PackageFee": 2.00, 
      "OverFee": 3.00, 
      "OtherFee": 2.00, 
      "OtherFeeDetail”:{
            "打包安装服务”:2.00
       }, 
      "TotalFee”: 30.00, 
      "Volume": 8000, 
      "chargedWeight": 10.00
}

API文档（订单推送）
订单推送接口

基本信息
接口指令（RequestType）	103
请求方式	POST
支持格式	只支持 Json 格式、UTF-8 编码
批量请求	不支持
接口地址	商户提供接收地址

接口调用规则
1.
按照文档规则进行传参 
2.

3.
应用级参数
名称	类型	是否必须	描述
PushTime	String	是	推送时间
EBusinessID	String	是	商户ID
Data[]	ShipperCode	String	否	快递公司编码
	OrderCode	String	是	客户订单编号
	LogisticCode	String	否	快递单号
	Reason	String	否	推送的描述
	State	int	是	订单状态码 100=下单成功，400=下单失败， 102=分配网点 ，103=分配快递员， 104=已取件， 301=计费，  208=更新重量， 203=取消订单，  204=揽收失败， 205=作废 ，2=在途中， 3=签收 ，500=异常，501=已转寄
	comments	String	否	调度信息（103推送）
	CreateTime	date	是	推送时间,格式：yyyy-MM-dd HH:mm:ss
	OperateType	Int	是	操作人 1:平台补推 2：物流公司直推
	PickerInfo	PersonName	String	否	取件快递员姓名（103推送）
		PersonTel	String	否	取件快递员电话（103推送）
		PersonCode	String	否	取件快递员工号（103推送）
		StationName	String	否	取件网点名称（102推送）
		StationCode	String	否	取件网点编号（102推送）
		StationAddress	String	否	取件网点地址（102推送）
		StationTel	String	否	取件网点电话（102推送）
		PickupCode	String	否	取件码（102，103推送）
	Weight	Double	否	快递公司称重（301，208，501推送）
	Cost	Double	否	订单成本运费（301，208，501推送）
	InsureAmount	Double	否	保价费（301，208，501推送）
	PackageFee	Double	否	包装费（301，208，501推送）
	OtherFee	Double	否	其他费（301，208，501推送）
	TotalFee	Double	否	运费+增值费（301，208，501推送）
	Volume	Double	否	体积，单位：立方厘米（301，208，501推送）
	OfficialFee	Double	否	官方原价（支持：京东/德邦/顺丰）
（301，208，501推送）
	chargedWeight	Double	否	计费重量（支持：京东，德邦月结订单）
（301，208，501推送）
	BackLogisticCode	String	否	转寄单号（State=501时推送，支持：京东，顺丰部分渠道）
	BackFee	Double	否	转寄费（State=501时推送，支持：京东，顺丰部分渠道）
	Quantity	int	是	包裹数（301，208，501推送）
	ExpType	String	是	快递类型(见快递公司代码表)
	PayType	int	是	支付方式（0现付，1到付，2月结）
	Costing	discount	int	否	计费成本折扣（仅折扣计费的订单有）
（301，208推送）
		upperGround	int	否	首重重量（KG）（301，208推送）
		groundPrice	Double	否	首重价格（元）（301，208推送）
		rateOfStage	Double	否	续重价格（元）（301，208推送）
Count	int	是	推送订单数

返回参数
名称	类型	是否必须	描述
EBusinessID	String	是	商户编号
Success	Boolean	是	接口调用成功与否(true/false)
UpdateTime	Date	是	处 理 完 结 时 间 ， 格 式 yyyy-MM-dd HH:mm:ss
Reason	String	否	返回描述

请求示例：
{
      "PushTime": "2022-08-28+11:36:33",
      "EBusinessID": 25238,
      "Data": [
            {
               "EBusinessID": 25238，
               "PlatCode": "XD202208281136338903",
               "LogisticCode": "73100039470370",
               "State": 100,
               "Reason": "下单成功",
               "ShipperCode": "ZT",
               "CreateTime": "2022-08-28+11:36:33",
                "OrderCode": "CS1661657792",
                "OperateType":2,
                "PickerInfo": [
                            { 
                                   "StationName":"XXX",
                                   "StationTel":"XXX,
                                   "PersonName": "XXX", 
                                   "PersonTel": "***********", 
                                   "PickupCode": "1988", 
                                   "PersonCode": "00854695"
                           }
                    ],
                    "Weight":1,
                    "Volume":0
                    "Cost":10,
                    "InsureAmount":1,
                    "PackageFee":1,
                    "OtherFee":0,
          }
       ],
       "Count": 1
}


返回示例
{
      "UpdateTime": "2021-04-23 14:30:07", 
      "EBusinessID": "1662067", 
      "Reason": "成功", 
      "Success": true 
}

API文档（轨迹查询）
轨迹查询接口

基本信息
接口指令	8001
请求方式	POST
支持格式	只支持 Json 格式、UTF-8 编码
批量请求	不支持
接口地址	https://open.yuntongzy.com/express/api/OrderService

接口调用规则
1.
按照文档规则进行传参 
2.

3.
应用级参数
名称	类型	是否必须	描述
OrderCode	String	否	客户订单编号 和 快递单号 必须传一项
LogisticCode	String	否	客户订单编号 和 快递单号 必须传一项

返回参数
名称	类型	是否必须	描述
EBusinessID	String	是	商户编号
Success	Boolean	是	接口调用成功与否(true/false)
UniquerRequestNumber	string	是	唯一请求ID
Reason	String	是	返回描述
Traces	AcceptStation	String	是	当前轨迹描述
	AcceptTime	String	是	扫描时间
	Location	String	是	所在城市

请求示例：
{
      "LogisticCode": "73199021541907", 
}



返回示例
{
      "EBusinessID": 25238,
      "Success": TRUE,
       "UniquerRequestNumber": "48fe86d1-ac16-c23c-93f8-ed920ac326c8",
       "Reason": "成功",
      "Traces": [
            {
               "AcceptStation": "【北京市】快件离开【北京昌平霍营】发往京南转运中心"，
               "AcceptTime": "2023-03-23 19:30:54",
               "Location": "北京市",
           },
          {
               "AcceptStation": "【北京市】【北京昌平霍营】(***********)的业务员S王为梅(***********)已揽收"，
               "AcceptTime": "2023-03-23 19:30:49",
               "Location": "北京市",
          },
       ]
}


API文档（渠道查询）
渠道查询接口

基本信息
接口指令	1002
请求方式	POST
支持格式	只支持 Json 格式、UTF-8 编码
批量请求	不支持
接口地址	https://open.yuntongzy.com/express/api/OrderService


接口调用规则
1.
按照文档规则进行传参
2.
3.
接口字段必填项：需客户进行自校验，通过后，再调用接口。
4.

应用级参数
名称	类型	是否必填	描述
ShipperCode	String	是	快递公司编码 （德邦=DB，京东=JD) 参考：《快递公司代码表》
ChannelType	Int	否	渠道类型：0全国不限制，1只发得物，2只发电商，不传默认为0
ExpType	String	否	德邦（PACKAGE标准，RCP360大件)，京东（P1特惠送，P2特快送）
参考：《快递公司代码表》
PayType	Int	是	0-现付，1-到付，2-月结 默认2（月结）
returnType	Int	否	返回数据类型，默认0，返回单个渠道产品，1返回单个渠道多个产品
Receiver	Name	String	是	收件人姓名
	Tel	String	是	收件人电话和手机号码，必填一个 Tel：区号+尾数 Mobile：11 位手机号码，1 开头 11 位数字
	Mobile	String		
	ProvinceName	String	是	收件省 (如广东省，不要缺少“省”；如是直 辖市，请直接传北京、上海等； 如是自治区，请直接传广西壮族自治区 等) 支持字符长度为 20 个以内，不支持数 字与字母
	CityName	String	是	收件市(如深圳市，不要缺少“市； 如是市辖区，请直接传北京市、上海市 等”) 支持字符长度为 20 个以内，不支持数 字与字母
	ExpAreaName	String	是	收件区/县(如福田区，不要缺少“区” 或“县”)
	Address	String	是	收件人详细地址（填写具体的街道、门 牌号，不要将省市区加入其中）
Sender	Name	String	是	收件人姓名
	Tel	String	是	收件人电话和手机号码，必填一个 Tel：区号+尾数 Mobile：11 位手机号码，1 开头 11 位数字
	Mobile	String		
	ProvinceName	String	是	收件省 (如广东省，不要缺少“省”；如是直 辖市，请直接传北京、上海等； 如是自治区，请直接传广西壮族自治区 等) 支持字符长度为 20 个以内，不支持数 字与字母
	CityName	String	是	收件市(如深圳市，不要缺少“市； 如是市辖区，请直接传北京市、上海市 等”) 支持字符长度为 20 个以内，不支持数 字与字母
	ExpAreaName	String	是	收件区/县(如福田区，不要缺少“区” 或“县”)
	Address	String	是	收件人详细地址（填写具体的街道、门 牌号，不要将省市区加入其中）
Weight	Double	是	包裹总重量 kg
Quantity	Int	否	包裹数
Volume	Double	否	包裹总体积 m3
InsureAmount	Double	否	声明价值

返回参数
名称	类型	是否必须	描述
EBusinessID	 String	是 	商户编号
Success	Boolean	是	接口调用成功与否(true/false)
ResultCode	int	是 	返回编号
Reason	String	否	描述
UniquerRequestNumber	String	是 	唯一标识
data	upperGround	Double	否	首重重量
	groundPrice	Double	否	首重价格
	rateOfStage	Double	否	续重价格
	totalfee	Double	是	总运费
	omsProductCode	String	是	时效类型
	productName	String	是	时效名称
	discount	Double	否	折扣
	discountfee	Double	否	折扣价格
	channelName	String	是	渠道名称
	discounttype	int	否	折扣类型
	InsureValue	Double	否	保价金额
	InsureAmount	Double	否	保价费
  
 请求应用级参数报文：      
{ 
     "InsureAmount":1000, 
     "ShipperCode":"JD", 
     "OrderCode":"yfftest20220609111", 
     "Receiver":{ 
            "ProvinceName":"广东省", 
            "Address":"暄悦东街 19-21-23 号保利中悦广场 18 楼", 
            "CityName":"广州市",
            "ExpAreaName":"海珠区",
            "Mobile":"***********", 
            "Name":"华珊" 
      },
     "Quantity":1, 
     "PayType":3, 
     "Sender":{ 
            "ProvinceName":"广东省", 
            "Address":"金花路华宝大厦 A 栋 601", 
            "CityName":"深圳市",
            "ExpAreaName":"福田", 
            "Mobile":"***********", 
            "Name":"颜芬芬" 
      },
     "ExpType":"", 
     "Commodity":[ 
            { 
                  "Goodsquantity":1,
                  "GoodsName":"工牌",
             } 
      ] 
}

响应报文:
{       
      "EBusinessID" : "XXXXX", 
      "UniquerRequestNumber" : "61ce9866-5f99-45e8-80c7-29c27543d641", 
      "ResultCode" : 100, 
      "Reason" : "查询成功", 
      "Success" : true,  
      "data" : { 
            "upperGround" : 1, 
            "groundPrice" : 10 ,
            "rateOfStage" : 1,
            "totalfee" : 10,
            "omsProductCode" : P1,
            "productName":"京东特惠送", 
      }
 }

API文档（到付月结下单接口）
到付月结下单接口

基本信息
接口指令	1901
请求方式	POST
支持格式	只支持 Json 格式、UTF-8 编码
批量请求	支持
接口地址	https://open.yuntongzy.com/express/api/OrderService


接口调用规则
1.
按照文档规则进行传参
2.
3.
接口字段必填项：需客户进行自校验，通过后，再调用接口。
4.

应用级参数
名称	类型	是否必填	描述
ShipperCode	String	是	快递公司编码 （顺丰=SF，京东=JD) 参考：《快递公司代码表》
ChannelType	Int	否	渠道类型：0全国不限制，1只发得物，2只发电商，不传默认为0
Orders		Array	是	订单列表（数组类型）
	OrderCode	String	否	商户订单号，如有传参数，必须唯一
	src	String	否	电子存根图片地址，存根图片和运单号+收件方电话必须传一项
	LogisticCode	String	否	运单号，没有传存根图片，单号和收件方电话必填
	Tel	String	否	收件方电话，没有传存根图片，单号和收件方电话必填

返回参数
名称	类型	是否必须	描述
EBusinessID	 String	是 	商户编号
Order	success	Array	否	下单成功的订单数组
	fail	Array	否	下单失败的订单数组
Success	Boolean	是	接口调用成功与否(true/false)
ResultCode	int	是 	返回编号
Reason	String	否	描述
UniquerRequestNumber	String	是 	唯一标识

Order参数
Receiver	Name	String	是	收件人姓名
	Tel	String	是	收件人电话或手机号码
	ProvinceName	String	是	收件省份
	CityName	String	是	收件城市
	ExpAreaName	String	是	收件区/县
	Address	String	是	收件人详细地址
Sender	Name	String	是	寄件人姓名
	Tel	String	是	寄件人电话或手机号码
	ProvinceName	String	是	寄件省份
	CityName	String	是	寄件城市
	ExpAreaName	String	是	寄件区/县
	Address	String	是	寄件人详细地址
ShipperCode	String	是	快递公司代码
OrderCode	String	是	商户订单编号
LogisticCode	String	是	快递单号
ExpType	String	是	时效类型
Weight	Double	是	包裹总重量 kg
Quantity	Int	否	包裹数
Volume	Double	否	包裹总体积 m3
PackageFee	Double	否	包装费
BackFee	Double	否	转寄/退回运费
OfficialFee	Double	否	官方运费（纯运费）
InsureAmount	Double	否	保价费
  
 请求应用级参数报文：      
{ 
      "Orders" : [
            { 
                "OrderCode" : "yfftest202104261043", 
                "src" : "https://open.yuntongzy.com/static/images/100001.png" ,
           },
          { 
                "OrderCode" : "yfftest202104261044", 
                "src" : "https://open.yuntongzy.com/static/images/100002.png" ,
           },
      ],
      "ShipperCode" : "SF", 
      "ChannelType" : 1,
 }


响应报文:
{ 
     "Order":{
                "success" : {  
                         "Receiver":{ 
                                     "ProvinceName":"广东省", 
                                     "Address":"暄悦东街 19-21-23 号保利中悦广场 18 楼", 
                                     "CityName":"广州市",
                                     "ExpAreaName":"海珠区",
                                     "Tel":"***********", 
                                     "Name":"华珊" 
                           },
                          "Sender":{ 
                                     "ProvinceName":"广东省", 
                                     "Address":"暄悦东街 19-21-23 号保利中悦广场 18 楼", 
                                     "CityName":"广州市",
                                     "ExpAreaName":"海珠区",
                                     "Tel":"***********", 
                                     "Name":"华珊" 
                           },
                          "ShipperCode": "SF",
                          "OrderCode" : "2023050466658844",
                          "LogisticCode" : "SF1676774472658",
                          "ExpType":"2", 
                          "Quantity":1,
                          "Weight" : 1,
                          "Volume" : 0, 
                          "PackageFee" : 0,
                          "BackFee" : 0,
                          "OfficialFee" : 18,
                          "InsureAmount" : 0
                 },
               "fail" : {
                        "OrderCode" : "2023050466658844",
                        "LogisticCode" : "",
                         "msg" : "面单解析失败",
                 }
        }
      "EBusinessID" : "XXXXX", 
      "UniquerRequestNumber" : "61ce9866-5f99-45e8-80c7-29c27543d641", 
      "ResultCode" : 100, 
      "Reason" : "下单成功", 
      "Success" : true
 }

API文档（到付月结取消接口）
到付月结订单取消接口

基本信息
接口指令	1902
请求方式	POST
支持格式	只支持 Json 格式、UTF-8 编码
批量请求	不支持
接口地址	https://open.yuntongzy.com/express/api/OrderService

接口调用规则
1.
按照文档规则进行传参 
2.
3.
已计费/已取消状态后不允许取消；获取月结码10分钟内不允许取消
4.

应用级参数
名称	类型	是否必须	描述
ShipperCode	String	否	快递公司编码，传入规范的快递 公司编码
OrderCode	String	是	客户订单编号(自定义，不可重 复)
LogisticCode	String	否	快递单号

返回参数
名称	类型	是否必须	描述
EBusinessID	String	是	商户编号
Success	Boolean	是	接口调用成功与否(true/false)
ResultCode	int	是	返回编码，100为成功，其他为失败
Reason	String	否	描述
UniquerRequestNumber	String	是	唯一标识

请求示例：
{ 
    "ShipperCode": "SF",
     "OrderCode": "D00037207766555577", 
 }

返回示例
{ 
     "EBusinessID" : "1365742",
     "ResultCode" : 100, 
     "Reason" : "取消成功", 
     "Success" : true
 }

API文档（获取到付月结码）
获取到付月结码接口

基本信息
接口指令	1903
请求方式	POST
支持格式	只支持 Json 格式、UTF-8 编码
批量请求	不支持
接口地址	https://open.yuntongzy.com/express/api/OrderService

接口调用规则
1.
按照文档规则进行传参 
2.
3.
已计费/已取消状态后不允许获取月结码
4.

应用级参数
名称	类型	是否必须	描述
ShipperCode	String	否	快递公司编码，传入规范的快递 公司编码
OrderCode	String	是	客户订单编号(自定义，不可重 复)
LogisticCode	String	否	快递单号

返回参数
名称	类型	是否必须	描述
EBusinessID	String	是	商户编号
Success	Boolean	是	接口调用成功与否(true/false)
ResultCode	int	是	返回编码，100为成功，其他为失败
Reason	String	否	描述
UniquerRequestNumber	String	是	唯一标识
data	success	Boolean	是	获取月结码成功
	obj	String	是	月结码
	timeout	int	是	月效期

请求示例：
{ 
    "ShipperCode": "SF",
     "OrderCode": "D00037207766555577", 
 }

返回示例
{ 
     "EBusinessID" : "1365742",
     "ResultCode" : 100, 
     "Reason" : "成功", 
     "Success" : true，
     "data" : {
              "success" : true,
              "obj" : "3FA8B64C79CE4E6F8B5746B4D0EF9D07",
              "timeout" : 300
      }
 }

API文档（到付月结计费推送）
到付月结订单计费推送接口

基本信息
接口指令（RequestType）	104
请求方式	POST
支持格式	只支持 Json 格式、UTF-8 编码
批量请求	不支持
接口地址	商户提供接收地址

接口调用规则
1.
按照文档规则进行传参 
2.

3.
应用级参数
名称	类型	是否必须	描述
ShipperCode	String	否	快递公司编码
MonthCode	String	是	月结码
OrderCode	String	是	客户订单编号
LogisticCode	String	否	快递单号
Reason	String	否	推送的描述
State	int	是	订单状态码  301=计费， 203=取消订单
comments	String	否	调度信息
CreateTime	date	是	推送时间,格式：yyyy-MM-dd HH:mm:ss
OperateType	Int	是	操作人 1:平台补推 2：物流公司直推
Weight	Double	否	快递公司称重（State=301时推送）
Cost	Double	否	订单成本运费（State=301时推送）
InsureAmount	Double	否	保价费
PackageFee	Double	否	包装费
OtherFee	Double	否	其他费
Volume	Double	否	体积，单位：立方厘米
OfficialFee	Double	否	官方原价
chargedWeight	Double	否	计费重量
BackLogisticCode	String	否	转寄单号
BackFee	Double	否	转寄费
Quantity	int	是	包裹数
ExpType	String	是	快递类型(见快递公司代码表)
PayType	int	是	支付方式（0现付，1到付，2月结）
Sender	ProvinceName	String	是	寄件省份
	CityName	String	是	寄件城市
Receiver	ProvinceName	String	是	收件省份
	CityName	String	是	收件城市

返回参数
名称	类型	是否必须	描述
EBusinessID	String	是	商户编号
Success	Boolean	是	接口调用成功与否(true/false)
UpdateTime	Date	是	处 理 完 结 时 间 ， 格 式 yyyy-MM-dd HH:mm:ss
Reason	String	否	返回描述

请求示例：
{
      "PushTime": "2022-08-28+11:36:33",
      "EBusinessID": 25238,
      "Data": [
            {
               "EBusinessID": 25238，
               "PlatCode": "XD202208281136338903",
               "LogisticCode": "SF10039470370",
               "State": 301,
               "Reason": "推送成功",
               "ShipperCode": "SF",
               "CreateTime": "2022-08-28+11:36:33",
               "OrderCode": "CS1661657792",
               "OperateType":2,
               "Weight":1,
               "Volume":0
               "Cost":10,
               "InsureAmount":1,
               "PackageFee":1,
               "OtherFee":0,
          }
       ],
       "Count": 1
}


返回示例
{
      "UpdateTime": "2021-04-23 14:30:07", 
      "EBusinessID": "1662067", 
      "Reason": "成功", 
      "Success": true 
}

API文档（到付月结订单查询）
到付月结订单查询接口

基本信息
接口指令	1904
请求方式	POST
支持格式	只支持 Json 格式、UTF-8 编码
批量请求	支持
接口地址	https://open.yuntongzy.com/express/api/OrderService


接口调用规则
1.
按照文档规则进行传参
2.
3.
接口字段必填项：需客户进行自校验，通过后，再调用接口。
4.

应用级参数
名称	类型	是否必填	描述
MonthCode	String	否	月结码（四选一，至少传一项）
PlatCode	String	否	云通订单号（四选一，至少传一项）
LogisticCode	String	否	运单号（四选一，至少传一项）
OrderCode	String	否	商户订单号（四选一，至少传一项）

返回参数
名称	类型	是否必须	描述
EBusinessID	 String	是 	商户编号
data	Array	是	订单列表
Success	Boolean	是	接口调用成功与否(true/false)
ResultCode	int	是 	返回编号
Reason	String	否	描述
UniquerRequestNumber	String	是 	唯一标识

data参数
Receiver	Name	String	是	收件人姓名
	Tel	String	是	收件人电话或手机号码
	ProvinceName	String	是	收件省份
	CityName	String	是	收件城市
	ExpAreaName	String	是	收件区/县
	Address	String	是	收件人详细地址
Sender	Name	String	是	寄件人姓名
	Tel	String	是	寄件人电话或手机号码
	ProvinceName	String	是	寄件省份
	CityName	String	是	寄件城市
	ExpAreaName	String	是	寄件区/县
	Address	String	是	寄件人详细地址
MonthCode	String	是	月结码
ShipperCode	String	是	快递公司代码
OrderCode	String	是	商户订单编号
LogisticCode	String	是	快递单号
ExpType	String	是	时效类型
Weight	Double	是	包裹总重量 kg
chargedWeight	Double	是	计费重量
Quantity	Int	否	包裹数
Volume	Double	否	包裹总体积 m3
PackageFee	Double	否	包装费
BackFee	Double	否	转寄/退回运费
OfficialFee	Double	否	官方运费（纯运费）
InsureAmount	Double	否	保价费
  
 请求应用级参数报文：      
{ 
      "MonthCode" : "ASDFAKLJOEIOFALKGLAJWDLGMALSEG", 
 }


响应报文:
{ 
     "data":[
                 "Receiver":{ 
                             "ProvinceName":"广东省", 
                             "Address":"暄悦东街 19-21-23 号保利中悦广场 18 楼", 
                             "CityName":"广州市",
                             "ExpAreaName":"海珠区",
                             "Tel":"***********", 
                             "Name":"华珊" 
                   },
                  "Sender":{ 
                             "ProvinceName":"广东省", 
                             "Address":"暄悦东街 19-21-23 号保利中悦广场 18 楼", 
                             "CityName":"广州市",
                             "ExpAreaName":"海珠区",
                             "Tel":"***********", 
                             "Name":"华珊" 
                   },
                  "ShipperCode": "SF",
                  "OrderCode" : "2023050466658844",
                  "LogisticCode" : "SF1676774472658",
                  "ExpType":"2", 
                  "Quantity":1,
                  "Weight" : 1,
                  "Volume" : 0, 
                  "PackageFee" : 0,
                  "BackFee" : 0,
                  "OfficialFee" : 18,
                  "InsureAmount" : 0
        ],
      "EBusinessID" : "XXXXX", 
      "UniquerRequestNumber" : "61ce9866-5f99-45e8-80c7-29c27543d641", 
      "ResultCode" : 100, 
      "Reason" : "成功", 
      "Success" : true
 }

API文档（快递公司代码表）
快递公司	快递代码	ExpType类型
京东快递	JD	P1-标快
P2-特快
京东快运	JDL	25-特快重货
德邦快递	DB	PACKAGE-标准快递
TZKJC-特快专递
RCP-360大件快递
NFLF-精准卡航(新)
NLRF-精准汽运(新)
跨越速运	KY	30-隔日达
40-陆运件
50-同城次日
160-省内次日
220-全国专运
顺丰速运	SF	1-特快
2-标快
255-卡航
231-陆运包裹
中通快递	ZT	ZTBK
申通快递	ST	STBK
圆通快递	YT	YTBK
极兔速递	JT	JTBK
韵达快递	YD	YDBK
菜鸟裹裹	CN	GG = 智能分配
指定产品类型：
CP419085 或 OUT_CP419085= 极兔
K_YUNDA 或 OUT_K_YUNDA = 韵达
K_STO 或 OUT_K_STO = 申通
ZHIMAKAMENPEISONG-0001= 菜鸟直送
K_ZTO 或 OUT_K_ZTO = 中通
K_YTOA 或 OUT_K_YTOA = 圆通
K_EMS 或 OUT_K_EMS = EMS邮政
OUT_D_DBKD = 德邦

API文档（提交工单）
提交工单接口

基本信息
接口指令	1807
请求方式	POST
支持格式	只支持 Json 格式、UTF-8 编码
批量请求	不支持
接口地址	https://open.yuntongzy.com/express/api/OrderService


接口调用规则
1.
按照文档规则进行传参
2.
3.
接口字段必填项：需客户进行自校验，通过后，再调用接口。
4.


应用级参数
名称	类型	是否必填	描述
LogisticCode	String	是	运单号和商家单号必须填一个
OrderCode	String		
ComplaintType	Int	是	工单类型：
2-计费异常
3-取消订单
4-催促取件
5-催促派送
6-拦截转寄
7-线下收费
9-其他问题
ComplaintContent	String	是	工单需求，详细描述问题和需求有助于快速处理
ActualWeight	Double	否	申诉包裹重量，ComplaintType=2时必传
ActualVolume	Int	否	申诉体积，长*宽*高/泡比 以立方厘米为单位
GoodsName	String	否	包裹物品名称，ComplaintType=2时必传，有助于快速核实重量体积
PicList	Array	否	凭证图片，ComplaintType=7时必传，图片url地址以数组的形式提交,示例：["https://wwww.baidu.com/a.png","https://wwww.baidu.com/b.png"]

返回参数
名称	类型	是否必须	描述
EBusinessID	String	是 	商户编号
ComplaintNumber	int	否	工单ID，ResultCode=100时返回
Success	Boolean	是	接口调用成功与否(true/false)
ResultCode	int	是 	返回编号
UniquerRequestNumber	String	是 	唯一标识
  
 请求应用级参数报文：      
{
        "LogisticCode":"DPK202410422975",
        "ComplaintType":2,
        "ComplaintContent":"重量不对。",
        "ActualWeight":1,
        "GoodsName":"衣服",
        "PicList":[
                "https://wwww.baidu.com/a.png",
                "https://wwww.baidu.com/b.png",
         ]
}

响应报文:
{
      "EBusinessID" : "XXXXX",
      "ComplaintNumber": 101,
      "UniquerRequestNumber" : "61ce9866-5f99-45e8-80c7-29c27543d641", 
      "ResultCode" : 100,
      "Success" : true
 }
API文档（查询工单）

查询工单接口


基本信息
接口指令	1818
请求方式	POST
支持格式	只支持 Json 格式、UTF-8 编码
批量请求	不支持
接口地址	https://open.yuntongzy.com/express/api/OrderService


接口调用规则
1.
按照文档规则进行传参
2.
3.
接口字段必填项：需客户进行自校验，通过后，再调用接口。
4.


应用级参数
名称	类型	是否必填	描述
LogisticCode	String	是	运单号和商家单号必须填一个
OrderCode	String		

返回参数
名称	类型	是否必须	描述
EBusinessID	String	是 	商户编号
data[]	OrderCode	String	是	商家单号
	LogisticCode	String	是	运单号
	ComplaintNumber	Int	是	工单ID
	ComplaintType	String	是	工单类型
	createTime	String	是	创建日期
	status	String	是	工单状态
	dealResult	String	是	处理结果
	PicList	Array	否	凭证图片
	DetailTracks	Array	否	处理记录
Success	Boolean	是	接口调用成功与否(true/false)
ResultCode	int	是 	返回编号
UniquerRequestNumber	String	是 	唯一标识
  
 请求应用级参数报文：      
{
        "LogisticCode":"DPK202410422975"
}

响应报文:
{
      "EBusinessID" : "XXXXX",
      "data":[
        {
                "OrderCode":"UIZR202408031716010548",
                 "LogisticCode":"DPK202410422975",
                 "ComplaintNumber":937,
                 "ComplaintType":"计费异常",
                 "createTime":"2024-08-03 19:44:11",
                 "status":"处理中",
                 "dealResult":"再次核实中",
                 "PicList":[
                        "https://wwww.baidu.com"
                  ],
                 "DetailTracks":[
                  {
                        "Time":"2024-08-03 19:45:04",
                        "Name":"云通客服",
                        "Result":"核实中"
                  },
                 {
                        "Time":"2024-08-03 19:45:21",
                        "Name":"云通客服",
                         "Result":"再次核实中"
                 }
                 ]
        }
       ]
      "UniquerRequestNumber" : "61ce9866-5f99-45e8-80c7-29c27543d641", 
      "ResultCode" : 100,
      "Success" : true
 }
API文档（工单回复推送）【联系商务对接】
工单回复推送接口

基本信息
接口指令（RequestType）	105
请求方式	POST
支持格式	只支持 Json 格式、UTF-8 编码
批量请求	不支持
接口地址	商户提供接收地址

接口调用规则
1.
按照文档规则进行传参 
2.

3.
应用级参数
名称	类型	是否必须	描述
PushTime	String	是	推送时间
EBusinessID	String	是	商户ID
Data[]	ShipperCode	String	是	快递公司编码
	OrderCode	String	是	客户订单编号
	LogisticCode	String	是	快递单号
	Reason	String	是	工单回复内容
	State	int	是	401（固定值）
	CreateTime	date	是	推送时间,格式：yyyy-MM-dd HH:mm:ss
	OperateType	Int	是	操作人 1:平台回复
	ComplaintNumber	String	是	工单ID
	ComplaintType	Int	是	工单类型：
2-计费异常
3-取消订单
4-催促取件
5-催促派送
6-拦截转寄
7-线下收费
9-其他问题
	ResultType	Int	是	工单状态：0-未处理，1-无需处理，2-驳回，3-通过
	dealResult	String	否	工单处理结果
	PicList	Array	否	工单回复的图片附件url
Count	int	是	推送工单数

返回参数
名称	类型	是否必须	描述
EBusinessID	String	是	商户编号
Success	Boolean	是	接口调用成功与否(true/false)
UpdateTime	Date	是	处 理 完 结 时 间 ， 格 式 yyyy-MM-dd HH:mm:ss
Reason	String	否	返回描述

请求示例：
{
      "PushTime": "2022-08-28+11:36:33",
      "EBusinessID": 25238,
      "Data": [
            {
               "EBusinessID": 25238，
               "PlatCode": "XD202208281136338903",
               "LogisticCode": "73100039470370",
               "State": 401,
               "Reason": "以为您反馈催促取件",
               "ShipperCode": "ZT",
               "CreateTime": "2022-08-28+11:36:33",
               "OrderCode": "CS1661657792",
               "OperateType":1,
               "ComplaintNumber": "1236",
               "ComplaintType": 7,
               "ResultType": 0,
               "dealResult": "",
               "PicList": []
          }
       ],
       "Count": 1
}


返回示例
{
      "UpdateTime": "2021-04-23 14:30:07", 
      "EBusinessID": "1662067", 
      "Reason": "成功", 
      "Success": true 
}

API文档（工单回复）
回复工单接口

基本信息
接口指令	1819
请求方式	POST
支持格式	只支持 Json 格式、UTF-8 编码
批量请求	不支持
接口地址	https://open.yuntongzy.com/express/api/OrderService


接口调用规则
1.
按照文档规则进行传参
2.
3.
接口字段必填项：需客户进行自校验，通过后，再调用接口。
4.


应用级参数
名称	类型	是否必填	描述
ComplaintNumber	String	是	工单ID
Reason	String	是	回复内容
PicList	Array	否	凭证图片，图片url地址以数组的形式提交,示例：["https://wwww.baidu.com/a.png","https://wwww.baidu.com/b.png"]

返回参数
名称	类型	是否必须	描述
EBusinessID	String	是 	商户编号
Success	Boolean	是	接口调用成功与否(true/false)
ResultCode	int	是 	返回编号
UniquerRequestNumber	String	是 	唯一标识
  
 请求应用级参数报文：      
{
        "ComplaintNumber":10001,
        "Reason":"重量不对。",
        "PicList":[
                "https://wwww.baidu.com/a.png",
                "https://wwww.baidu.com/b.png",
         ]
}

响应报文:
{
      "EBusinessID" : "XXXXX",
      "UniquerRequestNumber" : "61ce9866-5f99-45e8-80c7-29c27543d641", 
      "ResultCode" : 100,
      "Success" : true
 }
