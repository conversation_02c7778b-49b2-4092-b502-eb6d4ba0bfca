# order_code字段设置文档

## 概述

本文档描述了为`order_records`表添加`order_code`字段的完整设置过程，该字段用于存储查价接口返回的order_code，最大长度设置为**2048字符**。

## 字段设计

### 字段规格
- **字段名**: `order_code`
- **数据类型**: `VARCHAR(2048)`
- **用途**: 存储查价接口返回的order_code，用于下单时验证价格一致性
- **索引**: `idx_order_records_order_code`

### 长度分析

根据代码分析，order_code有两种格式：

#### 1. 增强版格式（当前主要使用）
```
ENHANCED_ORDER_CODE_ + Base64编码的JSON数据
```

**长度计算：**
- 前缀：`ENHANCED_ORDER_CODE_` = 22字符
- JSON数据包含：
  - `standard_code`：标准快递代码（如"ZTO"）= 3-10字符
  - `original_code`：原始快递代码（如"ZTO"）= 3-10字符  
  - `provider`：供应商（如"kuaidi100"）= 10-20字符
  - `channel_id`：渠道ID（如"kuaidi100_ZTO"）= 15-25字符
  - `product_code`：产品代码（如"STANDARD"）= 8-15字符
  - `original_request`：完整的原始查价请求（包含寄收地址、重量等信息）= 500-1500字符
  - `cached_data`：缓存相关信息 = 100-300字符
  - `validation_results`：验证结果 = 50-200字符
  - `performance_metrics`：性能指标 = 50-150字符

**JSON数据总长度：约700-2200字符**
- Base64编码后长度：约933-2933字符（Base64编码会增加约33%的长度）
- **总长度：约955-2955字符**

#### 2. 旧版格式（备用）
```
ORDER_CODE_ + Base64编码的字符串
```

**长度计算：**
- 前缀：`ORDER_CODE_` = 11字符
- 原始字符串格式：`标准代码|原始代码|供应商|渠道ID|产品代码`
- 原始字符串长度：约50-100字符
- Base64编码后长度：约67-133字符
- **总长度：约78-144字符**

### 数据库限制
- **PostgreSQL VARCHAR最大长度**: 1,073,741,823字符（约1GB）
- **当前设置**: 2048字符
- **安全余量**: 约1000-2000字符

## 升级历史

### 版本1.0（初始版本）
- **长度**: VARCHAR(1024)
- **状态**: 已废弃

### 版本2.0（当前版本）
- **长度**: VARCHAR(2048)
- **升级原因**: 支持更复杂的JSON数据结构和更详细的原始请求信息
- **升级时间**: 2025年1月6日

## 文件结构

### 数据库迁移文件
```
sql/migrations/
├── 20250106_add_order_code_field.sql          # 初始创建（1024字符）
└── 20250106_upgrade_order_code_length.sql     # 升级到2048字符
```

### 执行脚本
```
scripts/
├── apply_order_code_migration.sh              # 初始迁移脚本
├── upgrade_order_code_length.sh               # 升级脚本
└── test_order_code_length.sh                  # 测试脚本
```

### 文档
```
docs/
└── order_code_field_setup.md                  # 本文档
```

## 使用方法

### 1. 执行升级（推荐）
```bash
# 升级到2048字符长度
./scripts/upgrade_order_code_length.sh
```

### 2. 运行测试
```bash
# 测试2048字符长度支持
./scripts/test_order_code_length.sh
```

### 3. 验证升级结果
```sql
-- 检查字段长度
SELECT column_name, data_type, character_maximum_length, is_nullable
FROM information_schema.columns 
WHERE table_name = 'order_records' AND column_name = 'order_code';
```

## 升级优势

### 1. 支持更复杂的数据结构
- 更详细的原始请求信息
- 完整的缓存数据
- 验证结果和性能指标
- 自定义字段和元数据

### 2. 提供更大的安全余量
- 从1024字符提升到2048字符
- 支持未来数据增长需求
- 减少数据截断风险

### 3. 适应企业级需求
- 支持复杂的业务逻辑
- 完整的审计追踪
- 详细的性能监控

## 注意事项

### 1. 数据兼容性
- 升级过程不会影响现有数据
- 向后兼容1024字符的数据
- 新数据可以使用2048字符

### 2. 性能考虑
- 索引性能不受影响
- 存储空间增加有限
- 查询性能保持稳定

### 3. 监控建议
- 定期检查order_code长度分布
- 监控存储空间使用情况
- 关注查询性能指标

## 故障排除

### 1. 升级失败
```bash
# 检查数据库连接
psql -h 8.138.252.193 -p 5432 -U postgres -d go_kuaidi

# 手动执行升级
psql -h 8.138.252.193 -p 5432 -U postgres -d go_kuaidi -f sql/migrations/20250106_upgrade_order_code_length.sql
```

### 2. 测试失败
```bash
# 检查字段是否存在
SELECT column_name FROM information_schema.columns 
WHERE table_name = 'order_records' AND column_name = 'order_code';

# 检查字段长度
SELECT character_maximum_length FROM information_schema.columns 
WHERE table_name = 'order_records' AND column_name = 'order_code';
```

### 3. 数据验证
```sql
-- 检查现有数据长度
SELECT 
    LENGTH(order_code) as code_length,
    COUNT(*) as count
FROM order_records 
WHERE order_code IS NOT NULL
GROUP BY LENGTH(order_code)
ORDER BY code_length DESC;
```

## 总结

order_code字段已成功升级到2048字符长度，能够满足企业级应用的复杂需求，同时保持良好的性能和兼容性。升级过程安全、可靠，不会影响现有业务运行。 