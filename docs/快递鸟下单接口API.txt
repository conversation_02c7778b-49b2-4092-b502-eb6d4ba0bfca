3.1接口规范及说明
本文档就API接口规范进行详细说明，方便客户快速对接及使用快递鸟服务。
3.1.1报文及报文编码
报文格式：JSON
请求编码格式(utf-8)："application/x-www-form-urlencoded;charset=utf-8"
交互协议上统一用UTF-8，避免传递中文数据出现乱码。
3.1.2接口数据包结构
系统级参数使用Name=Value格式，多个系统级参数之间用&拼接。
应用级参数(RequestData)值为JSON格式，使用UTF-8对JSON串进行URL编码后赋值给RequestData即可完成应用级参数的组装，如下图：

3.1.3JSON示列(C#版本)
//仅作示例ID，不可用来实际使用 
string eBusinessID= "350238";

//仅作示例Key，不可用来实际使用
string ApiKey = "082bff05-66ba-46b3-ae64-804c7cff990c";

//请求地址
string url = "https://api.kdniao.com/api/OOrderService";

//请求和返回的数据格式，目前只支持JSON格式，值为2
string DataType = "2";

//字符编码采用UTF-8
string charset = "UTF-8";

//应用级参数JSON字符串
string jsonStr = "{"ShipperType":2,"ShipperCode":"快递公司编码","OrderCode":"商家订单编号(自定义不可重复)","ExpType":1,"PayType":3,"Receiver":{"ProvinceName":"广东省","CityName":"广州市","ExpAreaName":"海珠区","Address":"暄悦东街19-21-23号保利中悦广场18楼","Name":"李四","Mobile":"***********"},"Sender":{"ProvinceName":"广东省","CityName":"深圳市","ExpAreaName":"福田区","Address":"福报街道金花路华宝大厦A栋601","Name":"张三","Mobile":"***********"},"StartDate":"","EndDate":"","Weight":1.0,"Quantity":1,"Commodity":[{"GoodsName":"YEPPER 男女同款T恤","GoodsCode":"","GoodsPrice":49.0,"Goodsquantity":1,"GoodsPicUrl":"图片URL地址"}],"ActualPaymentAmount":55.0}";

//把(jsonStr+ApiKey)进行MD5加密，然后Base64编码，最后进行URL(utf-8)编码
string datasign = HttpUtility.UrlEncode(base64(MD5(jsonStr + ApiKey, "UTF-8"), "UTF-8"), Encoding.UTF8);

//应用级参数进行URL(utf-8)编码
string requestData=HttpUtility.UrlEncode(jsonStr , Encoding.UTF8);

//组装请求报文参数
string PostStr = "RequestType=1801&EBusinessID=eBusinessID&RequestData=requestData&DataSign=datasign&DataType=DataType";

//通讯协议使用Https协议Post请求方式
string post = this.DoPost(url, PostStr);

3.1.4系统级参数
名称	类型(字符长度)	是否必须	描述
RequestData	String	是		应用级参数，内容为JSON格式
EBusinessID	String	是	用户ID
RequestType	String	是	请求接口指令
DataSign	String	是	数据内容签名
DataType	String	是	请求、返回数据格式，目前只支持JSON格式，固定值DataType=2
*快递鸟所有接口统一使用此系统级参数，根据不同的请求接口指令和应用级参数接入不同的接口。




3.2签名说明
3.2.1关于签名
快递鸟和平台端系统进行对接，有一定的安全机制。采用IP认证加签名的方式对接，具体方案如下
*******防止数据被篡改
在POST请求中会传递5个必须(R)参数
RequestData=数据内容(URL编码:UTF-8)
EBusinessID=用户ID
RequestType=请求指令类型
DataSign= 数据内容签名：把(请求内容(未编码)+ApiKey)进行MD5加密(32位小写)，然后Base64编码，最后进行URL(utf-8)编码
DataType=2(请求和返回数据类型格式为json)
注：
DataSign生成后，对方接收到数据后，以同样的算法进行签名(其中推送接口RequestType为103不需要进行URL编码)，生成摘要，对比两者的摘要是否相同，如果不同，说明传递过程中发生数据篡改。
*******调用接口的身份认证
注册为快递鸟用户后，会生成对应的用户ID和APIKey，用户ID是用户的身份标识，APIKey是用户的秘钥。
举例：
EBusinessID=350238【示例ID，不可用来实际使用】
APIKey=082bff05-66ba-46b3-ae64-804c7cff990c【示例Key，不可用来实际使用】
①假设RequestData (JSON)内容为：
RequestData (JSON)
JSON
复制代码
1
{"ShipperType":2,"ShipperCode":"快递公司编码","OrderCode":"商家订单编号(自定义不可重复)","ExpType":1,"PayType":3,"Receiver":{"ProvinceName":"广东省","CityName":"广州市","ExpAreaName":"海珠区","Address":"暄悦东街19-21-23号保利中悦广场18楼","Name":"李四","Mobile":"***********"},"Sender":{"ProvinceName":"广东省","CityName":"深圳市","ExpAreaName":"福田区","Address":"福报街道金花路华宝大厦A栋601","Name":"张三","Mobile":"***********"},"StartDate":"","EndDate":"","Weight":1.0,"Quantity":1,"Commodity":[{"GoodsName":"YEPPER 男女同款T恤","GoodsCode":"","GoodsPrice":49.0,"Goodsquantity":1,"GoodsPicUrl":"图片URL地址"}],"ActualPaymentAmount":55.0}
经过URL(UTF-8)编码后的内容就为：
RequestData经过URL(UTF-8)转码
JSON
复制代码
1
%7B%22ShipperType%22%3A2%2C%22ShipperCode%22%3A%22%E5%BF%AB%E9%80%92%E5%85%AC%E5%8F%B8%E7%BC%96%E7%A0%81%22%2C%22OrderCode%22%3A%22%E5%95%86%E5%AE%B6%E8%AE%A2%E5%8D%95%E7%BC%96%E5%8F%B7(%E8%87%AA%E5%AE%9A%E4%B9%89%E4%B8%8D%E5%8F%AF%E9%87%8D%E5%A4%8D)%22%2C%22ExpType%22%3A1%2C%22PayType%22%3A3%2C%22Receiver%22%3A%7B%22ProvinceName%22%3A%22%E5%B9%BF%E4%B8%9C%E7%9C%81%22%2C%22CityName%22%3A%22%E5%B9%BF%E5%B7%9E%E5%B8%82%22%2C%22ExpAreaName%22%3A%22%E6%B5%B7%E7%8F%A0%E5%8C%BA%22%2C%22Address%22%3A%22%E6%9A%84%E6%82%A6%E4%B8%9C%E8%A1%9719-21-23%E5%8F%B7%E4%BF%9D%E5%88%A9%E4%B8%AD%E6%82%A6%E5%B9%BF%E5%9C%BA18%E6%A5%BC%22%2C%22Name%22%3A%22%E6%9D%8E%E5%9B%9B%22%2C%22Mobile%22%3A%22***********%22%7D%2C%22Sender%22%3A%7B%22ProvinceName%22%3A%22%E5%B9%BF%E4%B8%9C%E7%9C%81%22%2C%22CityName%22%3A%22%E6%B7%B1%E5%9C%B3%E5%B8%82%22%2C%22ExpAreaName%22%3A%22%E7%A6%8F%E7%94%B0%E5%8C%BA%22%2C%22Address%22%3A%22%E7%A6%8F%E6%8A%A5%E8%A1%97%E9%81%93%E9%87%91%E8%8A%B1%E8%B7%AF%E5%8D%8E%E5%AE%9D%E5%A4%A7%E5%8E%A6A%E6%A0%8B601%22%2C%22Name%22%3A%22%E5%BC%A0%E4%B8%89%22%2C%22Mobile%22%3A%22***********%22%7D%2C%22StartDate%22%3A%22%22%2C%22EndDate%22%3A%22%22%2C%22Weight%22%3A1.0%2C%22Quantity%22%3A1%2C%22Commodity%22%3A%5B%7B%22GoodsName%22%3A%22YEPPER%C2%A0%E7%94%B7%E5%A5%B3%E5%90%8C%E6%AC%BET%E6%81%A4%22%2C%22GoodsCode%22%3A%22%22%2C%22GoodsPrice%22%3A49.0%2C%22Goodsquantity%22%3A1%2C%22GoodsPicUrl%22%3A%22%E5%9B%BE%E7%89%87URL%E5%9C%B0%E5%9D%80%22%7D%5D%2C%22ActualPaymentAmount%22%3A55.0%7D
②那么DataSign签名的内容为：
RequestData请求内容(未编码)+ApiKey
JSON
复制代码
1
{"ShipperType":2,"ShipperCode":"快递公司编码","OrderCode":"商家订单编号(自定义不可重复)","ExpType":1,"PayType":3,"Receiver":{"ProvinceName":"广东省","CityName":"广州市","ExpAreaName":"海珠区","Address":"暄悦东街19-21-23号保利中悦广场18楼","Name":"李四","Mobile":"***********"},"Sender":{"ProvinceName":"广东省","CityName":"深圳市","ExpAreaName":"福田区","Address":"福报街道金花路华宝大厦A栋601","Name":"张三","Mobile":"***********"},"StartDate":"","EndDate":"","Weight":1.0,"Quantity":1,"Commodity":[{"GoodsName":"YEPPER 男女同款T恤","GoodsCode":"","GoodsPrice":49.0,"GoodsQuantity":1,"GoodsPicUrl":"图片URL地址"}],"ActualPaymentAmount":55.0}082bff05-66ba-46b3-ae64-804c7cff990c
经过md5(32位小写)和base64后的内容就为：
OTg4OWI1MGU5ZTQ2ZmMwYzY3OTE3MWI5NGEwYmRiNjE=
在经过URL(UTF-8)编码的内容为：
OTg4OWI1MGU5ZTQ2ZmMwYzY3OTE3MWI5NGEwYmRiNjE%3d
③最终要发送给KDN的数据为：
④接收方收到数据后，得到：
RequestData，DataSign和RequestType等数据。
⑤接收方对EBusinessID 得到APIKey，RequestData+APIKey的数据进行md5和base64后的内容就为：
ZGIzZjUzZjRkZmE1OWFmYWE5YjYxNmQwYjY0YTI4ZTY=
⑥接收方判断签名后的数据跟传递过来的DataSign是否一致，如果一致进行业务操作，如果不一致返回错误。





3.2.2(C#)DataSign签名加密代码
///<summary>
///电商Sign签名
///</summary>
///<param name="content">内容</param>
///<param name="keyValue">APIkey</param>
///<param name="charset">URL编码 </param>
///<returns>DataSign签名</returns>
Public String Encrypt(String content, String keyValue, String charset) {
    if (keyValue != null) {
        return base64(MD5(content + keyValue, charset), charset);
    }
    return base64(MD5(content, charset), charset);
}

///<summary>
/// 字符串MD5加密
///</summary>
///<param name="Text">要加密的字符串</param>
///<returns>密文</returns>
Private string MD5(string Text, string charset) {
    byte[] buffer = System.Text.Encoding.GetEncoding(charset).GetBytes(Text);
    try {
        System.Security.Cryptography.MD5CryptoServiceProvider check;
        check = new System.Security.Cryptography.MD5CryptoServiceProvider();
        byte[] somme = check.ComputeHash(buffer);
        string ret = "";
        foreach(byte a in somme) {
            if (a < 16)
                ret += "0" + a.ToString("X");
            else
                ret += a.ToString("X");
        }
        return ret.ToLower();
    } catch {
        throw;
    }
}
Private static string base64(String str, String charset) {
    returnConvert.ToBase64String(System.Text.Encoding.GetEncoding(charset).GetBytes(str));
}


接口功能
4.2预估运费接口
4.2.1功能说明
1.根据客户填写的收寄地址、预估重量、声明价值计算返回预估运费，仅做参考，不作为最终结算运费，最终结算以揽件实际重量为准。当前端需要展示快递公司的预估运费时使用此接口。
2.续重分段计费规则返回仅支持顺丰快递。
4.2.2基本信息
接口指令
1815
请求方式
POST请求
支持格式
只支持Json格式、UTF-8编码
批量请求
不支持
接口地址
测试地址：http://*************:8081/api/dist
正式地址：https://api.kdniao.com/api/OOrderService
平均响应时长
300ms
请求超时时间
5000ms
4.2.3接口调用规则
按照文档规则进行传参，请求报文中不允许出现以下特殊字符： '   "   #    &    +    <   >   %   \  以及表情包、生僻字等这类信息传参接口会报错，请注意传参时过滤掉这些信息。
4.2.4应用场景
寄件人老七，选择客户的手机端进行寄快递，在填写完寄收信息和预估重量后，看到手机端展示的每家快递公司的快递费是不同的，于是选择申通快递进行下单。
4.2.5应用级参数
名称
类型(字符长度)
是否必须
描述
TransportType
Int(2)
是
运力类型
1=快递类
ShipperType
Int(2)
是
产品类型
具体以商务层面合同沟通为准

3=2小时收
4=半日收
5=当日收
Weight
Double(10,3)
是
包裹总重量(KG)
InsureAmount
Double(10,2)
否
声明价值(正整数)，大于3000不支持保价
根据传入的声明价值计算保费
Receiver
ProvinceName
String(20)
是
收件省
(如广东省，不要缺少“省”；
如是直辖市，请直接传北京、上海等；
如是自治区，请直接传广西壮族自治区等)
支持字符长度为20个以内，不支持数字与字母
CityName
String(20)
是
收件市
(如深圳市，不要缺少“市；
如是市辖区，请直接传北京市、上海市等”)
支持字符长度为20个以内，不支持数字与字母
ExpAreaName
String(20)
否
收件区/县
(如福田区，不要缺少“区”或“县”)
Sender
ProvinceName
String(20)
是
发件省
(如广东省，不要缺少“省”；
如是直辖市，请直接传北京、上海等；
如是自治区，请直接传广西壮族自治区等)
支持字符长度为20个以内，不支持数字与字母
CityName
String(20)
是
发件市
(如深圳市，不要缺少“市；
如是市辖区，请直接传北京市、上海市等”)
支持字符长度为20个以内，不支持数字与字母
ExpAreaName
String(20)
否	
发件区/县
(如福田区，不要缺少“区”或“县”)
4.2.6返回参数
名称
类型(字符长度)
是否必须
描述
EBusinessID
String(10)
是
用户ID
Success
Boolean
是
成功与否(true/false)
ResultCode
String(5)
是
返回编号
Data
shipperCode
String(20)
是
快递公司编码
weight
Double(10,2)
是
总重量(KG)
cost
Double(10,2)
是
基础运费=首重金额+续重总金额
firstWeight
Double(10,2)
是
首重重量(KG)  如：1.00kg
firstWeightAmount
Double(10,2)
是
首重金额
continuousWeight
Double(10,2)
是
续重重量(KG)  如：1.00kg
continuousWeightPrice
Double(10,2)
否
续重单价(元/KG) 如:1.23元/KG
isSubsectionContinuousWeightPrice
Int
否
续重单价是否分段计费：
0-否，1-是，默认0
subsectionContinuousWeightPrices 
分段续重单价数组
continuousWeightPrice
Double(10,2)
否
续重单价(元/KG)  如:1.23元/KG
continuousWeight
Double(10,2)
否
续重重量(KG)  如:1.00kg
continuousWeightAmount
Double(10,2)
是
续重总金额
insureAmount
Double(10,2)
否
声明价值
premiumFee
Double(10,2)
否
保费
totalFee
Double(10,2)
是
总费用=基础运费+保费
Reason
String(50)
是
描述
UniquerRequestNumber
String(50)
是
唯一标识，快递鸟内部使用
4.2.7报文范例
请求示列
返回示列
JSON
复制代码
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
{
	"EBusinessID": "350238",
	"Data": [{
			"shipperCode": "半日收",
			"weight": "3.00",
			"cost": "19.00",
			"firstWeight": 1.00,
			"firstWeightAmount": 9.00,
			"continuousWeight": 2.00,
			"continuousWeightPrice": 5.00,
			"isSubsectionContinuousWeightPrice": 0,
			"continuousWeightAmount": 10.00,
			"insureAmount": 1000.00,
			"premiumFee": 2.00,
			"totalFee": 21.00
		},
		{
			"shipperCode": "JD",
			"weight": "3.00",
			"cost": "21.25",
			"firstWeight": 1.00,
			"firstWeightAmount": 12.75,
			"continuousWeight": 2.00,
			"continuousWeightPrice": 4.25,
			"isSubsectionContinuousWeightPrice": 0,
			"continuousWeightAmount": 8.50,
			"insureAmount": 1000.00,
			"premiumFee": 2.00,
			"totalFee": 23.25
		},
		{
			"shipperCode": "YTO",
			"weight": "3.00",
			"cost": "10.20",
			"firstWeight": 1.00,
			"firstWeightAmount": 5.60,
			"continuousWeight": 2.00,
			"continuousWeightPrice": 2.30,
			"isSubsectionContinuousWeightPrice": 0,
			"continuousWeightAmount": 4.60,
			"insureAmount": 1000.00,
			"premiumFee": 2.00,
			"totalFee": 12.20
		},
		{
			"shipperCode": "SF",
			"weight": "3.00",
			"cost": "20.64",
			"firstWeight": 1.00,
			"firstWeightAmount": 11.18,
			"continuousWeight": 2.00,
			"continuousWeightAmount": 9.46,
			"continuousWeightPrice": 4.73,
			"isSubsectionContinuousWeightPrice": 0,
			"insureAmount": 1000.00,
			"premiumFee": 2.00,
			"totalFee": 22.64
		}
	],
	"UniquerRequestNumber": "62e43be8-8fbe-41c3-af78-3c991cac3590",
	"ResultCode": "100",
	"Reason": "查询成功！",
	"Success": true
}


创建订单API

4.3创建订单接口
4.3.1功能说明
1. 用于创建客户预约快递上门取件订单（以下文中统称为“订单”）
2.客户创建订单成功后（快递鸟已接收到此订单，但未提交到快递公司），快递鸟智能调度向快递公司下单，结果会通过回调类接口把订单状态、快递公司编码、运单号、网点、快递员等信息回推给客户。
4.3.2基本信息
接口指令
1801
请求方式
POST请求
支持格式
只支持Json格式、UTF-8编码
批量请求
不支持
接口地址
测试地址：http://*************:8081/api/dist
正式地址：https://api.kdniao.com/api/OOrderService
平均响应时长
300ms
请求超时时间
8000ms
4.3.3接口调用规则
1. 按照文档规则进行传参，请求报文中不允许出现以下特殊字符： '   "   #    &    +    <   >   %   \  以及表情包、生僻字等这类信息传参接口会报错，请注意传参时过滤掉这些信息。
2. 预约时提供的收寄方信息必须真实准确。
3. 用户提供的寄件地址若超出快递公司服务范围，则无快递员上门揽件。
4. 因用户原因(如:恶意下单、批量下单长时间不发货等)导致快递公司投诉,快递鸟将停用此用户接口权限
5. 接口字段必填项：需客户进行自校验，通过后，调用快递鸟接口。
4.3.4支持预约时间段
通过调用超区校验接口获取可预约时间段
4.3.5应用场景
1. 移动端APP、公众号、电商等提供的散客寄件下单模块，通过上门取件直接预约快递员上门揽件，支持在线支付运费。
2. 电商平台、自营电商售后退货退款时，买家通过平台直接预约快递员上门揽件，支持在线支付运费。
3. 保价相关：
保费，规则如下：
0<声明价值≤500，保费=1；500<声明价值≤1000，保费=2
1000<声明价值≤3000保费=声明价值*0.005；大于3000不支持保价
4.3.6应用级参数
名称
类型(字符长度)
是否必填
描述
ShipperCode
String(50)
是
快递公司编码，商务合作指定运力下单模式，需按照指定的快递公司编码进行传值，智能调度的合作模式字段传值为空
TransportType
Int(2)
是
运力类型
1=快递类
ShipperType
Int(2)
是
产品类型
具体以商务层面合同沟通为准
1=特快（仅支持顺丰）
3=2小时收
4=半日收
5=当日收
OrderCode
String(30)
是
商家订单编号
(自定义且不可重复；同一个OrderCode订单号只能下单一次)
ExpType
Int(2)
是
快递类型。默认为1,预留备用字段
PayType
Int(2)
是	
支付类型：3-月结
Receiver
Company
String(30)
否
收件人公司名称
Name
String(30)
是
收件人姓名
Mobile
String(20)
是
收件人手机号(收件人手机号和收件人座机号二选一必填)
Mobile：11位手机号码，1开头11位数字
收件人座机号(收件人手机号和收件人座机号二选一必填)
Tel：区号-尾数
Tel
String(20)
PostCode
String(10)
否
收件地邮编
ProvinceName
String(20)
是
收件省
(如广东省，不要缺少“省”；
如是直辖市，请直接传北京、上海等；
如是自治区，请直接传广西壮族自治区等)
CityName
String(20)
是
收件市
(如深圳市，不要缺少“市；
如是市辖区，请直接传北京市、上海市等”）
ExpAreaName
String(20)
是
收件区/县
(如福田区，不要缺少“区”或“县”)
Address
String(100)
是
收件人详细地址
(填写具体的街道、门牌号，不要将省市区加入其中，不少于4个汉字)
Sender
Company
String(30)
否
发件人公司名称
Name
String(30)
是
发件人姓名
Mobile
String(20)
是
发件人手机号
（标准的11位手机号
收件人手机号和收件人座机号二选一必填
发件人座机号
座机格式：区号-尾数
Tel
String(20)
PostCode
String(10)
否
发件地邮编
ProvinceName
String(20)
是
发件省
(如广东省，不要缺少“省”；
如是直辖市，请直接传北京、上海等；
如是自治区，请直接传广西壮族自治区等)
支持字符长度为20个以内，不支持数字与字母
CityName
String(20)
是
发件市
(如深圳市，不要缺少“市；
如是市辖区，请直接传北京市、上海市等”)
支持字符长度为20个以内，不支持数字与字母
ExpAreaName
String(20)
是
发件区/县
(如福田区，不要缺少“区”或“县”)
Address
String(100)
是
发件人详细地址
(填写具体的街道、门牌号，不要将省市区加入其中，不少于4个汉字)
StartDate
String(50)
否
通过调用超区校验接口获取可预约的时间段；
预约开始时间和结束时间，格式：YYYY-MM-DD HH:MM:SS，如不传则系统默认分配最近时间上门取件
EndDate
Weight
Double(10,3)
否
包裹总重量kg
德邦：0-60kg，峰值可达300kg

顺丰：0-60kg，峰值可达300kg

京东：0-30kg
圆通：0-60kg

申通：30kg
韵达：50kg

中通：50kg
极兔：30kg
Quantity
Int(2)
否
包裹数
不支持子母件固定传1
Volume
Double(20,3)
否
包裹总体积cm3
Remark
String(60)
否
备注 （需要小哥在paid端看到的信息）
Commodity
(商品明细array)
GoodsName
String(100)
是
商品名称
单个商品名称不可大于50个字符
GoodsQuantity
Int(5)
是
商品件数
GoodsPrice
Double(10)
是
商品单价,（保留小数点后两位）
CategoryCode
String(20)
否
商品类目编码（三级）。类目字典
GoodsCode
String(20)
否
商品编码
GoodsWeight
Double(10,3)
否
商品重量kg
GoodsPicUrl
String(100)
否
图片URL地址，
(图片在下单完结前两月有效,一张)
电商类，门店类，c to b类客户建议直接提供图片信息给到快递鸟，加快售后问题单的处理效率
NotifyUrl
String(200)
否
订单回调通知url
（优先按照传入的回调地址进行消息通知)，若未传入，默认按照快递鸟技术支持配置的url进行回调通知）
PickupCode
String(50)
否
商家自定义取件码 (4位纯数字,需要联系技术支持做后台配置)
ActualPaymentAmount
Double(5,2)
否
商品实际付款金额
电商类，门店类，c to b类客户建议直接提供图片信息给到快递鸟，加快售后问题单的处理效率
ExtendField
String(300)
否
备用字段，不可大于300个字符
InsureAmount
Double(10,2)
否
声明价值(正整数)，不可大于3000
根据传入的声明价值计算保费
若无需保价，则不需要传此参数
AddService
(增值服务array)
Name
String(100)
否
增值服务名称
Value
String(50)
否
增值服务值
CustomerID
String(20)
否
客户标识
4.3.7返回参数
名称
类型(字符长度)
是否必须
描述
EBusinessID
String(10)
是
用户ID
Order
OrderCode
String(30)
是
订单编号
KDNOrderCode
String(30)
是
快递鸟订单编号
Success
Boolean
是
接口调用成功与否(true/false)，实际下单结果以回调类接口通知结果为准
ResultCode
String(5)
是
返回编号
Reason
String(50)
是
描述
UniquerRequestNumber
String(50)
是
唯一标识
StartDate
String(50)
是
预约开始时间
格式：YYYY-MM-DD HH:MM:SS
EndDate
String(50)
是
预约结束时间
格式：YYYY-MM-DD HH:MM:SS
4.3.8报文范例
请求示列
JSON
复制代码
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
{
	"TransportType": 1,
	"ShipperType": 3,
	"ShipperCode": "快递公司编码(指定快递下单必传，智能调度下单不传)",
	"OrderCode": "商家订单编号(自定义不可重复)",
	"ExpType": 1,
	"PayType": 3,
	"Receiver": {
		"ProvinceName": "广东省",
		"CityName": "广州市",
		"ExpAreaName": "海珠区",
		"Address": "暄悦东街19-21-23号保利中悦广场18楼",
		"Name": "李四",
		"Mobile": "***********"
	},
	"Sender": {
		"ProvinceName": "广东省",
		"CityName": "深圳市",
		"ExpAreaName": "福田区",
		"Address": "福报街道金花路华宝大厦A栋601",
		"Name": "张三",
		"Mobile": "***********"
	},
	"StartDate": "2023-02-09 09:00:00",
	"EndDate": "2023-02-09 11:00:00",
	"Weight": 1.0,
	"Quantity": 1,
	"Remark": "贵重物品，轻拿轻放",
	"Commodity": [{
		"CategoryCode": 1088,
		"GoodsName": "YEPPER 男女同款T恤",
		"GoodsCode": "123456",
		"GoodsQuantity": 1,
		"GoodsPrice": 49.0,
		"GoodsWeight": 1.0,
		"GoodsPicUrl": "https://image.mycplife.com/mycp888/w2000h1270/jpg/2022/08/09/6559669146a346719add83d6aa16be0d.jpg"
	}],
	"ActualPaymentAmount": 55.0,
	"InsureAmount": 3000.00
}
返回示列
JSON
复制代码
1
2
3
4
5
6
7
8
9
10
11
12
13
{
    "StartDate": "2023-02-08 13:00:00",
    "Order": {
        "OrderCode": "test20230129005",
        "KDNOrderCode": "KDN2302081110000001"
    },
    "EBusinessID": "350238",
    "UniquerRequestNumber": "311ebd0a-6ec0-4560-8e4f-7192a280f865",
    "EndDate": "2023-02-08 15:00:00",
    "ResultCode": "100",
    "Reason": "快递鸟已接单",
    "Success": true
}


4.4订单取消接口
4.4.1功能说明
1. 用于取消客户创建的订单，并实时同步返回取消结果(主动取消无203取消状态回传)
2. 已取件(state:104)/已揽件(state:301)状态后的订单无法取消
4.4.2基本信息
接口指令
1802
请求方式
POST请求
支持格式
只支持Json格式、UTF-8编码
批量请求
不支持
接口地址
测试地址：http://*************:8081/api/dist
正式地址：https://api.kdniao.com/api/OOrderService
平均响应时长
300ms
请求超时时间
5000ms
4.4.3接口调用规则
1. 按照文档规则进行传参，请求报文中不允许出现以下特殊字符： '   "   #    &    +    <   >   %   \  以及表情包、生僻字等这类信息传参接口会报错，请注意传参时过滤掉这些信息。
2. 已取件(state:104)/已揽件(state:301)状态后不允许取消
4.4.4应用场景
售后场景：
1. 寄件人【老七】，在客户电商平台申请售后审核通过后，选择下单上门取件，因各种原因，取消售后业务（同步取消上门取件）。
2. 寄件人【老七】，在客户电商平台申请售后审核通过后，选择下单上门取件，因各种原因，取消上门取件，重新下单（上门取件）
正向寄件场景：
1. 寄件人【老七】，在客户平台选择下单上门取件，因各种原因，取消上门取件，不再寄快递。
4.4.5应用级参数
名称
类型(字符长度)
是否必须
描述
ShipperCode
String(20)
否
快递公司编码，
传入规范的快递公司编码
OrderCode
String(30)
是
商家订单编号
LogisticCode
String(30)
否
快递单号
CancelType
Int(2)
否
取消类型：
1=预约信息有误，
2=快递员无法取件，
3=上门太慢，
4=运费太贵，
7=联系不上快递员，
8=快递员要求取消，
9=超时未接单，
11=其他(请传值具体取消原因)
若客户未做选择，默认传入此类型
CancelMsg
String(100)
否
取消类型描述
*建议用户在进行取消订单时，平台把取消类型(CancelType)和取消原因(CancelMsg)统一传给快递鸟。
4.4.6返回参数
名称
类型(字符长度)
是否必须
描述
EBusinessID
String(10)
是
用户ID
Success
Boolean
是
成功与否(true/false)
ResultCode
String(5)
是
返回编号
Reason
String(50)
是
描述
4.4.7报文范例
请求示列
JSON
复制代码
1
2
3
4
5
6
{
	"ShipperCode": "STO",
	"OrderCode": "D00037207766555577",
	"CancelType": 1,
	"CancelMsg": "预约信息有误"
}
返回示列
取消成功
JSON
复制代码
1
2
3
4
5
6
{
    "EBusinessID": "350238",
    "ResultCode": "100",
    "Reason": "成功",
    "Success": true
}
取消失败
JSON
复制代码
1
2
3
4
5
6
{
    "EBusinessID": "350238",
    "ResultCode": "10005",
    "Reason": "当前订单状态不能取消",
    "Success": false
}


物流轨迹查询
接口信息
RequestType 接口指令
8001
批量请求
不支持，并发20次/秒
计费规则
40个自然日内同一（物流编码+单号）不限查询次数，计费1单；
查询无轨迹不计费
轨迹排序
默认按照发生时间的升序排列
接口地址
https://api.kdniao.com/api/dist
  支持情况
1.       支持查询国内物流公司轨迹，国际查询支持 DHL、UPS、FEDEX  其他国际物流品牌请选用国际版
2.       点击查看-支持的快递公司列表
3.       顺丰/跨越速运/中通快递：须通过CustomerName传正确的寄件人or收件人的手机号码/座机号 后四位方可查询
   请求示例
   应用级参数
名称
类型(长度)
是否必须
描述
ShipperCode
String(10)
《快递公司编码》
LogisticCode
String(30)
快递单号
CustomerName
String(50)
顺丰必填
跨越必填
中通必填
寄件人or收件人 手机号/座机号后四位
尽量使用寄件人尾号
顺丰可能用虚拟号下单录入系统  作为验证标准
后四位传值错误无法获取到轨迹（可能拉取缓存）
顺丰后四位正确性可通过微信小程序【顺丰速运+】验证
顺丰/中通 开启隐私保护的订单需要使用寄件人手机尾号或者是隐私号码验证
中通仅支持查询签收后6个月内的单号
菜鸟橙运
菜鸟橙运分配的货主编号
Sort
Int(1)
轨迹按时间排序，0-升序，1-降序，默认0
OrderCode
String(30)
订单编号
 
  返回参数
当接口返回无轨迹时请参考下表中的物流状态和返回reason进行处理
名称
类型(字符长度)
是否必须
描述
EBusinessID
String(10)
用户ID
ShipperCode
String(10)
快递公司编码
LogisticCode
String(30)
快递单号
Success
Bool(10)
成功与否(true/false)
Reason
String(50)
失败原因
State
String(5)
普通物流状态（点击查看）
StateEx
String(5)
详细物流状态（点击查看）
Location
String(20)
当前所在城市
Traces.AcceptTime
String(32)
轨迹发生时间，示例：
Thu Mar 30 05:37:57 CST 2023
Traces.AcceptStation
String(500)
轨迹描述
Traces.Location
String(20)
历史节点所在城市
Traces.Action
String(50)
同 StateEx
Traces.Remark
String(20)
备注
OrderCode
String(30)
订单编号
Callback
String(50)
用户自定义回传字段
Station
String(50)
派件网点的名称
StationTel
String(50)
派件网点的电话
StationAdd
String(50)
派件网点的地址
DeliveryMan
String(50)
派件快递员
DeliveryManTel
String(50)
派件快递员手机号
NextCity
String(50)
下一站城市
有轨迹返回示例
JSON
复制代码
{
	"EBusinessID": "1000000",
	"ShipperCode": "ZTO",
	"LogisticCode": "78474409893076",
	"State": "3",
	"StateEx": "302",
	"Location": "襄阳市",
	"Traces": [{
		"Action": "1",
		"AcceptStation": "【沧州市】 沧州肃宁县（0317-3*）肃宁中通（1551*） 已揽收",
		"AcceptTime": "2025-01-09 19:22:16",
		"Location": "沧州市"
	}, {
		"Action": "2",
		"AcceptStation": "【沧州市】 快件已发往 襄樊转运中心",
		"AcceptTime": "2025-01-09 19:22:25",
		"Location": "沧州市"
	}, {
		"Action": "204",
		"AcceptStation": "【沧州市】 快件已到达 沧衡转运中心",
		"AcceptTime": "2025-01-09 22:18:34",
		"Location": "沧州市"
	}, {
		"Action": "2",
		"AcceptStation": "【沧州市】 快件已发往 武汉转运中心",
		"AcceptTime": "2025-01-09 22:21:49",
		"Location": "沧州市"
	}, {
		"Action": "204",
		"AcceptStation": "【武汉市】 快件已到达 武汉转运中心",
		"AcceptTime": "2025-01-10 23:53:28",
		"Location": "武汉市"
	}, {
		"Action": "2",
		"AcceptStation": "【武汉市】 快件已发往 襄樊转运中心",
		"AcceptTime": "2025-01-11 00:01:55",
		"Location": "武汉市"
	}, {
		"Action": "204",
		"AcceptStation": "【襄阳市】 快件已到达 襄樊转运中心",
		"AcceptTime": "2025-01-11 05:39:44",
		"Location": "襄阳市"
	}, {
		"Action": "2",
		"AcceptStation": "【襄阳市】 快件已发往 襄阳襄州西",
		"AcceptTime": "2025-01-11 05:48:46",
		"Location": "襄阳市"
	}, {
		"Action": "2",
		"AcceptStation": "【襄阳市】 快件已到达 襄阳襄州西",
		"AcceptTime": "2025-01-11 07:44:27",
		"Location": "襄阳市"
	}, {
		"Action": "202",
		"AcceptStation": "【襄阳市】襄阳襄州西 的业务员【张婷,1388*】正在为您派件（95720为中通快递员外呼专属号码，请放心接听，如有问题可联系网点:0710-37*,投诉电话:1860*）",
		"AcceptTime": "2025-01-11 07:44:28",
		"Location": "襄阳市"
	}, {
		"Action": "412",
		"AcceptStation": "【襄阳市】 快件已在 菜鸟 的【襄阳星悦里店】暂放，【取件地址：汉津路26号（进英大酒店旁）】，请及时取件。如有疑问请联系业务员：138*，代理点电话：183*，投诉电话：1860*",
		"AcceptTime": "2025-01-11 09:10:47",
		"Location": "襄阳市"
	}, {
		"Action": "302",
		"AcceptStation": "【襄阳市】 您的快递已签收，签收人在【襄阳星悦里店】取件。如有疑问请联系业务员：13*，代理点电话：183*，投诉电话：186*。感谢使用中通快递，期待再次为您服务！",
		"AcceptTime": "2025-01-14 18:23:15",
		"Location": "襄阳市"
	}],
	"DeliveryManTel": "1388*",
	"Success": true
}
无轨迹返回示例
JSON
复制代码
{
    "StateEx": "0",
    "LogisticCode": "YT4841733297234",
    "ShipperCode": "YTO",
    "Traces": [],
    "State": "0",
    "EBusinessID": "1617571",
    "Reason": "暂无轨迹信息",
    "Success": true
}


4.8工单类型查询
4.8.1功能说明
此接口用于获取快递鸟支持的工单类型，提交工单的时候需要根据此类型进行提交。
4.8.2基本信息
接口指令
1817
请求方式
POST请求
支持格式
只支持Json格式、UTF-8编码
批量请求
不支持
接口地址
测试地址：http://*************:8081/api/dist
正式地址：https://api.kdniao.com/api/OOrderService
平均响应时长
300ms
请求超时时间
5000ms
4.8.3接口调用规则
按照文档规则进行传参
4.8.4应用场景
客户在后台或用户端，通过查询工单类型，查看快递鸟支持的工单类型。然后用户选择对应的工单类型进行提交工单。
4.8.5应用级参数
无需传具体的参数，按照 {} 的格式传入，详情见请求示列。
4.8.6返回参数
名称
类型(字符长度)
是否必须
描述
EBusinessID
String(10)
是
用户ID
Success
Boolean
是
成功与否(true/false)
ResultCode
String(5)
是
返回编号
Reason
String(50)
是
描述
Data
ticketTypeCode
String(50)
是
工单类型编码
ticketTypeName
String(50)
是
工单类型名称
4.8.7报文范例
请求示列
JSON
复制代码
1
{}
返回示列
JSON
复制代码
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
{
	"EBusinessID": "350238",
	"Data": [{
		"ticketTypeCode": 1,
		"ticketTypeName": "虚假揽件"
	}, {
		"ticketTypeCode": 2,
		"ticketTypeName": "快递员服务态度差"
	}, {
		"ticketTypeCode": 3,
		"ticketTypeName": "重量/运费异常"
	}, {
		"ticketTypeCode": 5,
		"ticketTypeName": "超时揽件"
	}],
	"ResultCode": "100",
	"Reason": "查询成功",
	"Success": true
}


4.9工单提交接口
4.9.1功能说明
1.此接口用于提交工单，工单处理结果通过推送工单结果接口推回给客户。
2.https://orderadmin.kdniao.com/，平台方添加域名后，图片可正常在快递鸟后台显示，不会触碰防盗链配置的限制 。
4.9.2基本信息
接口指令
1807
请求方式
POST请求
支持格式
只支持Json格式、UTF-8编码
批量请求
不支持
接口地址
测试地址：http://*************:8081/api/dist
正式地址：https://api.kdniao.com/api/OOrderService
平均响应时长
300ms
请求超时时间
5000ms
4.9.3接口调用规则
1.按照文档规则进行传参，请求报文中不允许出现以下特殊字符： '   "   #    &    +    <   >   %   \  以及表情包、生僻字等这类信息传参接口会报错，请注意传参时过滤掉这些信息。
2.同类型工单，待处理、处理中状态，不可二次提交。已处理后，可再次提交相同类型工单。
3.不同类型的工单需要提交不同的信息，在前端页面要加以控制；
4.工单的处理全过程都要展示在工单列表中，供用户查看
4.9.4应用场景
场景一：
对接客户有自己的后台工单系统，为便于统一运营管理，于是对接快递鸟工单接口。
场景二：
客户的用户端提供工单业务，于是对接快递鸟工单接口，让用户直接使用工单。
4.9.5应用级参数
名称
类型(字符长度)
是否必须
描述
MemberID
String(10)
否
平台唯一ID
Mobile
String(20)
是
反馈人手机号
Name
String(20)
是
反馈人姓名
OrderCode
String(20)
是
商家订单号
LogisticCode
String(20)
否
物流运单号
ComplaintType
Int(2)
是
工单类型：
1. 虚假揽件
2. 快递员服务态度差
3. 重量/运费异常
详情请调用工单类型查询接口获取
ComplaintContent
String(200)
是
投诉内容
Source
Int(2)
是
工单数据来源 
1：客户(对接方管理后台提交)
4：云工单(C端用户寄件前端提交)
PicList
(投诉图片列表)
PictureItem
String
否
1.支持使用图片url地址
示例传参格式如下"PicList":[{"PictureItem":"https://ticket.irentals.cn/379de1e454d824f96ab0c7a413cce1f03.jpg"},{"PictureItem":"https://ticket.irentals.cn/8788d7490fc943e7c280a7a4514847990.jpg"}]  
2.图片转base64编码字符串后，再进行base64加密一次
格式： jpg，jpeg，png；
大小：单张图片，2M以内；
图片张数：最大4张；  
提交以下工单类型时，凭证附件必传:
重量/运费异常：必填
（1）重量凭证
（2）商品图片
包裹收到错件： 必填
（1）收到的错件图片
（2）带有快递面单的包裹图片
（3）正确的商品价值凭证
收到后少件：必填
（1）少件商品的图片
（2）少件的商品的价值凭证
包裹破损：必填
（1）带有电子面单的包裹外包装图片
（2）内件破损的图片
（3）破损的商品的价值凭证
快递员私下收费：必填
（1）提供线下付款凭证图片
丢件：必填
（1）丢件商品的图片
（2）丢件的商品的价值凭证
4.9.6返回参数
名称
类型(字符长度)
是否必须
描述
EBusinessID
String(10)
是
用户ID
ResultCode
String(5)
是
返回编号
Success
Boolean
是
成功与否(true/false)
ComplaintNumber
String(50)
是
工单处理单号
Reason
String(50)
是
描述
4.9.7报文范例
请求示列
JSON
复制代码
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
{
	"MemberId": "",
	"Name": "张三",
	"Mobile": ***********,
	"OrderCode": "Test202308111234567",
	"LogisticCode": "SF1160305056637",
	"ComplaintType": 1,
	"ComplaintContent": "快递员没有来取件，订单显示已揽件",
	"PicList": [{
		"PictureItem": "图片转base64编码字符串后，再进行base64加密一次"
	}, {
		"PictureItem": "图片转base64编码字符串后，再进行base64加密一次"
	}],
	"Source": 4
}
       图片传值说明：
          
返回示列
JSON
复制代码
1
2
3
4
5
6
7
{
	"EBusinessID": "3502388",
	"ComplaintNumber": "CPN20220101123456",
	"Success": true,
	"ResultCode": 100,
	"Reason": "处理中"
}


4.10工单详情查询接口
4.10.1功能说明
此接口用于获取快递鸟工单详情。
4.10.2基本信息
接口指令
1818
请求方式
POST请求
支持格式
只支持Json格式、UTF-8编码
批量请求
不支持
接口地址
测试地址：http://*************:8081/api/dist
正式地址：https://api.kdniao.com/api/OOrderService
平均响应时长
300ms
请求超时时间
5000ms
4.10.3接口调用规则
按照文档规则进行传参
4.10.4应用场景
寄件人老七，在客户手机端有多个上门取件工单，老七在进入工单列表页，查看所有的上门取件工单。客户调用此接口，快递鸟返回工单列表给到客户，客户在手机端展示给用户老七。老七点开第一个工单，查看工单详情。
4.10.5应用级参数
名称
类型(字符长度)
是否必须
描述
pageIndex
Int(2)
否
起始页，>=1,默认1
sizePerPage
Int(2)
否
每页数，<=20,默认20
kdnOrderCodes
Array
是
快递鸟订单号集合，array数组格式
source
Int(2)
否
数据来源 
1：客户(对接方管理后台提交)
3：快递鸟(快递鸟客服内部提交)
4：云工单(C端用户寄件前端提交) 
4.10.6返回参数
名称
类型(字符长度)
是否必须
描述
EBusinessID
String(10)
是
用户ID
Success
Boolean
是
成功与否(true/false)
ResultCode
String(5)
是
返回编号
Reason
String(50)
是
描述
Data
pageIndex
Int(2)
是
开始页
sizePerPage
Int(2)
是
每页数
totalCount
Int(2)
是
总条数
rows
array
是
列表数据，下表工单详情列表
4.10.6.1工单详情列表
名称
类型(字符长度)
是否必须
描述
customerId
String(10)
是
用户ID
kdnOrderCode
String(50)
是
快递鸟订单号
ticketType
Int(2)
是
工单类型编码
ticketTypeName
String(100)
是
工单类型名称
ticketNumber
String(30)
是
工单处理单号
createTime
Date
是
工单创建时间，
格式：yyyy-MM-dd HH:mm:ss
status
Int(2)
是
工单状态
0:待处理
1:处理中
2:已处理
dealResult
String(255)
否
工单处理结果
dealResultFiles
String
否
处理结果附件URL，多个以英文逗号“,”隔开
ticketPic
String
否
工单提交附件URL，多个以英文逗号“,”隔开
ticketDetailTracks
Array
否
工单处理日志记录，见下表
source
Int(2)
是
数据来源 
1：客户(对接方管理后台提交)
3：快递鸟(快递鸟客服内部提交)
4：云工单(C端用户寄件前端提交) 
4.10.6.2工单处理日志记录
名称
类型(字符长度)
是否必须
描述
kdnOrderCode
String(50)
是
快递鸟订单号
ticketNumber
String(30)
是
工单处理单号
trackResult
String(255)
否
工单处理结果
trackResultFiles
String
否
处理结果附件URL，多个以英文逗号“,”隔开
trackTime
String(50)
是
处理时间，
格式：yyyy-MM-dd HH:mm:ss
trackOperatorName
String(30)
否
操作人
ticketType、ticketTypeName工单类型对照表
4.10.7报文范例
请求示列
JSON
复制代码
1
2
3
4
5
{
	"pageIndex": 1,
	"sizePerPage": 10,
	"kdnOrderCodes": ["KDN2307182110000225","KDN2307181110000008"]
}
返回示列
JSON
复制代码
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
{
	"EBusinessID": "350238",
	"Data": {
		"pageIndex": 1,
		"sizePerPage": 10,
		"totalCount": 2,
		"rows": [{
			"customerId": "350238",
			"kdnOrderCode": "KDN2307182110000225",
			"ticketType": 11,
			"ticketTypeName": "轨迹签收实际未收到货",
			"ticketNumber": "CPN2307241510003267",
			"createTime": "2023-07-24 15:24:58",
			"status": 2,
			"dealResult": "此单快递公司已经提供签收证明，凭证请查看附件，感谢您的支持！",
			"dealResultFiles": "http://file.kdfafa.com/images/order/202307261457123759CK9GHD.png",
			"ticketPic": null,
			"ticketDetailTracks": [{
					"kdnOrderCode": "KDN2307182110000225",
					"ticketNumber": "CPN2307241510003267",
					"trackResult": "此单快递公司已经提供签收证明，凭证请查看附件，感谢您的支持！",
					"trackResultFiles": "http://file.kdfafa.com/images/order/202307261457123759CK9GHD.png",
					"trackTime": "2023-07-26 14:57:34",
					"trackOperatorName": "快递鸟客服"
				},
				{
					"kdnOrderCode": "KDN2307182110000225",
					"ticketNumber": "CPN2307241510003267",
					"trackResult": "与快递公司核实中，我们将在72小时内核实回复，请耐心等待。",
					"trackResultFiles": "",
					"trackTime": "2023-07-24 16:01:28",
					"trackOperatorName": "快递鸟客服"
				}
			],
			"source": 1
		}, {
			"customerId": "350238",
			"kdnOrderCode": "KDN2307181110000008",
			"ticketType": 99,
			"ticketTypeName": "其它",
			"ticketNumber": "CPN2105121110000010",
			"createTime": "2023-07-22 11:37:06",
			"status": 0,
			"dealResult": null,
			"dealResultFiles": null,
			"ticketPic": null,
			"ticketDetailTracks": null,
			"source": 1
		}]
	},
	"ResultCode": "100",
	"Reason": "查询成功",
	"Success": true
}