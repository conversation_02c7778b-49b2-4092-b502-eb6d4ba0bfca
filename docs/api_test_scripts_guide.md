# Go-Kuaidi 统一网关查价接口测试脚本使用指南

## 📋 概述

本指南介绍如何使用统一网关查价接口测试脚本，帮助您验证和测试系统的两个核心查价接口。

## 🚀 脚本列表

### 1. 完整测试脚本 (`test_unified_gateway_price_interfaces.sh`)
- **用途**: 全面测试两个查价接口的各种场景
- **测试用例**: 12个详细测试用例
- **特点**: 包含完整的测试分析和结果对比

### 2. 快速测试脚本 (`test_quick_price_interfaces.sh`)
- **用途**: 快速验证两个接口的基本功能
- **测试用例**: 2个基本测试用例
- **特点**: 简单快速，适合日常验证

### 3. 原有参考脚本 (`test_jd_dbl_volume_weight.sh`)
- **用途**: 专门测试实时查价的体积重量计算
- **测试用例**: 5个体积重量测试用例
- **特点**: 专注于体积重量计算验证

## 📊 接口对比

### 标准查价接口 (`QUERY_PRICE`)
```json
{
  "apiMethod": "QUERY_PRICE",
  "clientType": "web",
  "accessToken": "your_token",
  "businessParams": {
    "from_province": "北京市",
    "from_city": "北京市",
    "to_province": "上海市",
    "to_city": "上海市",
    "weight": 1.0,
    "goods_name": "测试物品"
  }
}
```

**特点**:
- ✅ 支持所有快递公司（除JD/DBL）
- ✅ 使用缓存机制，性能更好
- ✅ 适合常规业务查价
- ✅ 支持批量查询

### 实时查价接口 (`QUERY_REALTIME_PRICE`)
```json
{
  "apiMethod": "QUERY_REALTIME_PRICE",
  "clientType": "web",
  "accessToken": "your_token",
  "businessParams": {
    "sender": {
      "name": "张三",
      "mobile": "***********",
      "province": "北京市",
      "city": "北京市",
      "district": "朝阳区",
      "address": "三里屯街道1号"
    },
    "receiver": {
      "name": "李四",
      "mobile": "***********",
      "province": "上海市",
      "city": "上海市",
      "district": "浦东新区",
      "address": "陆家嘴街道2号"
    },
    "weight": 2.0,
    "length": 30.0,
    "width": 20.0,
    "height": 10.0,
    "goods_name": "测试物品",
    "quantity": 1,
    "pay_method": 0
  }
}
```

**特点**:
- ✅ 专门支持京东快递(JD)、德邦快递(DBL)
- ✅ 禁用缓存，强制实时查询
- ✅ 高精度计费，与下单价格完全一致
- ✅ 内置熔断器、重试机制、超时控制

## 🔧 使用方法

### 前置条件
1. **系统要求**: macOS/Linux/Windows (WSL)
2. **依赖工具**: 
   - `curl` (HTTP请求)
   - `jq` (JSON处理)
3. **服务状态**: 确保Go-Kuaidi服务正在运行 (localhost:8081)

### 安装依赖
```bash
# macOS
brew install curl jq

# Ubuntu/Debian
sudo apt-get install curl jq

# CentOS/RHEL
sudo yum install curl jq
```

### 运行测试

#### 快速测试 (推荐)
```bash
# 基本功能验证
./test_quick_price_interfaces.sh
```

#### 完整测试
```bash
# 全面测试所有场景
./test_unified_gateway_price_interfaces.sh
```

#### 体积重量专项测试
```bash
# 专门测试体积重量计算
./test_jd_dbl_volume_weight.sh
```

## 📈 测试用例说明

### 标准查价接口测试用例
1. **简单查价测试** - 基本参数验证
2. **详细查价测试** - 完整地址信息
3. **指定快递公司查价** - 单个快递公司查询
4. **体积重量测试** - 轻泡货物测试
5. **长距离查价测试** - 跨省查价

### 实时查价接口测试用例
1. **实时查价实际重量大于体积重量** - 重货测试
2. **实时查价体积重量大于实际重量** - 轻泡货测试
3. **德邦快递实际重量大于体积重量** - 重货测试
4. **德邦快递体积重量大于实际重量** - 轻泡货测试
5. **边界值测试** - 体积重量等于实际重量
6. **大件货物测试** - 大尺寸货物
7. **不同支付方式测试** - 到付模式

## 🔍 结果分析

### 成功响应示例
```json
{
  "success": true,
  "code": 200,
  "message": "查询成功",
  "data": [
    {
      "express_code": "SF",
      "express_name": "顺丰快递",
      "price": 15.00,
      "calc_weight": 1.0,
      "continued_weight_per_kg": 2.00,
      "product_code": "SF_STANDARD",
      "product_name": "顺丰标准快递",
      "order_code": "SF_STANDARD_1234567890"
    }
  ]
}
```

### 失败响应示例
```json
{
  "success": false,
  "code": 400,
  "message": "参数验证失败",
  "error": "weight must be greater than 0"
}
```

### 关键指标
- **success**: 请求是否成功
- **code**: HTTP状态码
- **data**: 价格数据数组
- **express_code**: 快递公司代码
- **price**: 总价格
- **calc_weight**: 计费重量

## 🎯 体积重量计算规则

### 计算公式
- **京东快递**: 体积重量 = 长×宽×高÷8000
- **德邦快递**: 体积重量 = 长×宽×高÷6000
- **计费重量**: max(实际重量, 体积重量)

### 示例计算
```
包裹尺寸: 30cm × 20cm × 10cm = 6000cm³
实际重量: 1.2kg

京东快递体积重量: 6000 ÷ 8000 = 0.75kg
计费重量: max(1.2kg, 0.75kg) = 1.2kg

德邦快递体积重量: 6000 ÷ 6000 = 1.0kg
计费重量: max(1.2kg, 1.0kg) = 1.2kg
```

## 🛠️ 故障排除

### 常见问题

#### 1. Token获取失败
```bash
ERROR: 获取Token失败
```
**解决方案**:
- 检查用户名密码是否正确
- 确认服务是否正在运行
- 检查网络连接

#### 2. 接口调用失败
```bash
ERROR: 接口调用失败
```
**解决方案**:
- 检查服务端口是否正确 (默认8081)
- 确认API路径是否正确
- 检查请求参数格式

#### 3. 依赖工具缺失
```bash
ERROR: curl 命令未找到
```
**解决方案**:
- 安装curl: `brew install curl`
- 安装jq: `brew install jq`

#### 4. 权限问题
```bash
Permission denied
```
**解决方案**:
- 添加执行权限: `chmod +x *.sh`

### 调试技巧

#### 1. 详细日志输出
```bash
# 启用详细模式
bash -x test_quick_price_interfaces.sh
```

#### 2. 单独测试API
```bash
# 直接使用curl测试
curl -X POST "http://localhost:8081/api/gateway/execute" \
  -H "Content-Type: application/json" \
  -d '{"apiMethod": "QUERY_PRICE", ...}'
```

#### 3. 检查服务状态
```bash
# 检查服务是否运行
curl -s http://localhost:8081/health
```

## 📝 自定义测试

### 修改测试参数
1. 编辑脚本中的测试数据
2. 调整地址信息
3. 修改重量和尺寸参数
4. 更改快递公司代码

### 添加新测试用例
```bash
# 在脚本中添加新的测试函数
test_custom_scenario() {
    local test_name="$1"
    local business_params="$2"
    
    # 测试逻辑
    ...
}
```

## 🔄 持续集成

### 集成到CI/CD
```yaml
# GitHub Actions 示例
- name: Run API Tests
  run: |
    ./test_quick_price_interfaces.sh
    ./test_unified_gateway_price_interfaces.sh
```

### 自动化测试脚本
```bash
#!/bin/bash
# 自动化测试脚本
echo "开始自动化测试..."
./test_quick_price_interfaces.sh
if [ $? -eq 0 ]; then
    echo "快速测试通过"
    ./test_unified_gateway_price_interfaces.sh
else
    echo "快速测试失败，跳过完整测试"
fi
```

## 📚 进阶用法

### 批量测试
```bash
# 批量测试多个环境
for env in dev staging prod; do
    echo "Testing $env environment..."
    BASE_URL="https://api-$env.example.com" ./test_quick_price_interfaces.sh
done
```

### 性能测试
```bash
# 并发测试
for i in {1..10}; do
    ./test_quick_price_interfaces.sh &
done
wait
```

### 结果保存
```bash
# 保存测试结果
./test_unified_gateway_price_interfaces.sh > test_results_$(date +%Y%m%d_%H%M%S).log 2>&1
```

## 🤝 贡献指南

### 添加新测试
1. Fork 项目
2. 创建测试用例
3. 更新文档
4. 提交 Pull Request

### 报告问题
- 描述问题详情
- 提供错误日志
- 说明复现步骤

---

## 📞 技术支持

如有问题，请联系技术团队或在项目中提交 Issue。

**Happy Testing! 🎉** 