# Go快递系统文档

## 文档列表

### API文档
- `openapi.yaml` - OpenAPI 3.0规范文档
- `Go-Kuaidi-API.postman_collection.json` - Postman接口测试集合

### 回调文档
- **`workorder-callback-examples.md`** - 工单回调示例文档（新增）
  - 详细的供应商回调格式示例
  - 系统标准化处理说明
  - 用户回调格式和签名验证
  - 完整的配置和错误处理示例

### 开发示例
- `python_sdk_example.py` - Python SDK使用示例

### 其他
- `docs.go` - Go文档生成配置
- `961.txt` - 系统说明文档

## 快速开始

### 工单回调集成

如果您需要接收工单状态变更和回复通知，请参考 `workorder-callback-examples.md` 文档：

1. **配置回调URL**：通过API配置您的回调接收地址
2. **验证签名**：实现MD5签名验证确保数据安全
3. **处理事件**：根据事件类型处理不同的工单状态变更
4. **错误处理**：实现重试机制和错误处理逻辑

### 支持的工单事件

- `workorder.created` - 工单创建
- `workorder.replied` - 工单回复
- `workorder.status_changed` - 状态变更
- `workorder.closed` - 工单关闭

### 支持的供应商

系统统一处理以下供应商的工单回调：
- 快递100 (Kuaidi100)
- 易达 (Yida)
- 云通 (Yuntong)

## 技术支持

如有问题，请联系技术支持：
- 邮箱：<EMAIL>
- 文档：https://docs.go-kuaidi.com
- API文档：https://api.go-kuaidi.com/docs
