# 订单状态变更记录功能设计方案

## 1. 数据模型设计

### 1.1 现有表结构分析
```sql
-- 现有 order_status_history 表结构
CREATE TABLE order_status_history (
    id BIGSERIAL PRIMARY KEY,
    order_no VARCHAR NOT NULL,
    from_status VARCHAR NOT NULL,
    to_status VARCHAR NOT NULL,
    provider VARCHAR NOT NULL,
    raw_status VARCHAR NOT NULL DEFAULT '',
    extra JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

### 1.2 表结构扩展建议
```sql
-- 扩展字段
ALTER TABLE order_status_history 
ADD COLUMN change_source VARCHAR(50) DEFAULT 'callback',  -- 变更来源：callback/manual/system
ADD COLUMN operator_id VARCHAR(255),                      -- 操作人员ID（手动操作时）
ADD COLUMN operator_name VARCHAR(255),                    -- 操作人员姓名
ADD COLUMN change_reason TEXT,                             -- 变更原因/备注
ADD COLUMN user_id VARCHAR(255),                          -- 订单所属用户ID
ADD COLUMN customer_order_no VARCHAR(255);                -- 客户订单号

-- 添加索引
CREATE INDEX idx_order_status_history_order_no ON order_status_history(order_no);
CREATE INDEX idx_order_status_history_user_id ON order_status_history(user_id);
CREATE INDEX idx_order_status_history_customer_order_no ON order_status_history(customer_order_no);
CREATE INDEX idx_order_status_history_created_at ON order_status_history(created_at DESC);
```

### 1.3 变更来源枚举
```go
type ChangeSource string

const (
    ChangeSourceCallback ChangeSource = "callback"    // 供应商回调
    ChangeSourceManual   ChangeSource = "manual"      // 手动操作
    ChangeSourceSystem   ChangeSource = "system"      // 系统自动
    ChangeSourceAPI      ChangeSource = "api"         // API调用
)
```

## 2. 后端架构设计

### 2.1 数据模型定义
```go
// OrderStatusHistory 订单状态历史记录
type OrderStatusHistory struct {
    ID               int64     `json:"id" gorm:"primaryKey;autoIncrement"`
    OrderNo          string    `json:"order_no" gorm:"column:order_no;not null;index"`
    FromStatus       string    `json:"from_status" gorm:"column:from_status;not null"`
    ToStatus         string    `json:"to_status" gorm:"column:to_status;not null"`
    Provider         string    `json:"provider" gorm:"column:provider;not null"`
    RawStatus        string    `json:"raw_status" gorm:"column:raw_status;default:''"`
    ChangeSource     string    `json:"change_source" gorm:"column:change_source;default:'callback'"`
    OperatorID       string    `json:"operator_id" gorm:"column:operator_id"`
    OperatorName     string    `json:"operator_name" gorm:"column:operator_name"`
    ChangeReason     string    `json:"change_reason" gorm:"column:change_reason"`
    UserID           string    `json:"user_id" gorm:"column:user_id;index"`
    CustomerOrderNo  string    `json:"customer_order_no" gorm:"column:customer_order_no;index"`
    Extra            JSON      `json:"extra" gorm:"column:extra;type:jsonb;default:'{}'"`
    CreatedAt        time.Time `json:"created_at" gorm:"column:created_at;autoCreateTime"`
    UpdatedAt        time.Time `json:"updated_at" gorm:"column:updated_at;autoUpdateTime"`
}
```

### 2.2 服务层设计
```go
// OrderStatusHistoryService 订单状态历史服务
type OrderStatusHistoryService interface {
    // RecordStatusChange 记录状态变更
    RecordStatusChange(ctx context.Context, req *RecordStatusChangeRequest) error
    
    // GetOrderStatusHistory 获取订单状态历史
    GetOrderStatusHistory(ctx context.Context, req *GetStatusHistoryRequest) (*GetStatusHistoryResponse, error)
    
    // GetUserOrdersStatusHistory 获取用户所有订单的状态历史
    GetUserOrdersStatusHistory(ctx context.Context, userID string, req *PaginationRequest) (*GetStatusHistoryResponse, error)
}

// RecordStatusChangeRequest 记录状态变更请求
type RecordStatusChangeRequest struct {
    OrderNo         string      `json:"order_no" validate:"required"`
    FromStatus      string      `json:"from_status" validate:"required"`
    ToStatus        string      `json:"to_status" validate:"required"`
    Provider        string      `json:"provider" validate:"required"`
    RawStatus       string      `json:"raw_status"`
    ChangeSource    string      `json:"change_source" validate:"required"`
    OperatorID      string      `json:"operator_id"`
    OperatorName    string      `json:"operator_name"`
    ChangeReason    string      `json:"change_reason"`
    UserID          string      `json:"user_id"`
    CustomerOrderNo string      `json:"customer_order_no"`
    Extra           interface{} `json:"extra"`
}
```

## 3. API接口设计

### 3.1 状态历史查询接口
```
GET /api/v1/express/orders/{order_no}/status-history
GET /api/v1/express/orders/status-history?customer_order_no={customer_order_no}
```

### 3.2 响应数据结构
```go
type StatusHistoryItem struct {
    ID              int64     `json:"id"`
    OrderNo         string    `json:"order_no"`
    CustomerOrderNo string    `json:"customer_order_no"`
    FromStatus      string    `json:"from_status"`
    FromStatusDesc  string    `json:"from_status_desc"`
    ToStatus        string    `json:"to_status"`
    ToStatusDesc    string    `json:"to_status_desc"`
    Provider        string    `json:"provider"`
    ProviderName    string    `json:"provider_name"`
    ChangeSource    string    `json:"change_source"`
    ChangeSourceDesc string   `json:"change_source_desc"`
    OperatorName    string    `json:"operator_name"`
    ChangeReason    string    `json:"change_reason"`
    CreatedAt       time.Time `json:"created_at"`
    Duration        string    `json:"duration"` // 状态持续时间
}

type GetStatusHistoryResponse struct {
    Items []StatusHistoryItem `json:"items"`
    Total int64               `json:"total"`
    Page  int                 `json:"page"`
    Limit int                 `json:"limit"`
}
```

## 4. 前端实现设计

### 4.1 组件结构
```
OrderDetailDialog.vue
├── 基本信息卡片
├── 寄件人/收件人信息卡片
├── 📍 新增：订单状态历史卡片 (OrderStatusTimeline.vue)
└── 物流轨迹卡片
```

### 4.2 状态历史时间线组件
```vue
<!-- OrderStatusTimeline.vue -->
<template>
  <el-card class="status-timeline-card">
    <template #header>
      <div class="timeline-header">
        <span>订单状态历史</span>
        <el-button type="text" @click="refreshHistory" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </template>
    
    <el-timeline v-if="historyItems.length > 0">
      <el-timeline-item
        v-for="item in historyItems"
        :key="item.id"
        :timestamp="formatDateTime(item.created_at)"
        :type="getTimelineType(item.change_source)"
        placement="top"
      >
        <div class="status-change-item">
          <div class="status-change-header">
            <el-tag :type="getStatusType(item.to_status)" size="small">
              {{ item.to_status_desc }}
            </el-tag>
            <span class="change-source">{{ item.change_source_desc }}</span>
          </div>
          
          <div class="status-change-details">
            <span class="status-transition">
              {{ item.from_status_desc }} → {{ item.to_status_desc }}
            </span>
            <span v-if="item.duration" class="duration">
              持续时间：{{ item.duration }}
            </span>
          </div>
          
          <div v-if="item.operator_name" class="operator-info">
            操作人员：{{ item.operator_name }}
          </div>
          
          <div v-if="item.change_reason" class="change-reason">
            变更原因：{{ item.change_reason }}
          </div>
        </div>
      </el-timeline-item>
    </el-timeline>
    
    <el-empty v-else description="暂无状态变更记录" />
  </el-card>
</template>
```

## 5. 实现优先级

### Phase 1: 核心功能（高优先级）
1. ✅ 扩展 order_status_history 表结构
2. ✅ 实现统一的状态变更记录服务
3. ✅ 修改所有状态更新点，确保记录状态变更
4. ✅ 实现状态历史查询API

### Phase 2: 前端展示（中优先级）
1. ✅ 实现 OrderStatusTimeline 组件
2. ✅ 集成到订单详情页面
3. ✅ 添加状态描述和时间格式化

### Phase 3: 增强功能（低优先级）
1. 🔄 添加状态变更统计分析
2. 🔄 支持批量状态变更记录
3. 🔄 添加状态变更通知功能
