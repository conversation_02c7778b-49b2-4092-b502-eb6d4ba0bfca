# 实时查价API文档

## 概述

实时查价API是高性能、实时价格查询接口。该接口支持完整的地址信息传参，直接调用实时API，无缓存，无价格验证，确保查价结果与下单时完全一致。

## 特性

- ✅ **实时查价**: 直接调用供应商API，无缓存机制
- ✅ **完整地址**: 支持详细地址信息，确保价格准确性
- ✅ **高性能**: 内置熔断器、重试机制、超时控制
- ✅ **并发安全**: 支持高并发访问
- ✅ **生产级**: 完整的错误处理和日志记录

## 接口信息

### 基本信息
- **接口路径**: `/api/v1/unified-gateway`
- **请求方法**: `POST`
- **Content-Type**: `application/json`
- **认证方式**: Token认证

### 请求参数

#### 统一网关请求格式
```json
{
  "api_method": "QUERY_REALTIME_PRICE",
  "client_type": "web",
  "username": "your_username",
  "business_params": {
    // 实时查价参数（见下方详细说明）
  }
}
```

#### 京东快递查价参数 (business_params)
```json
{
  "sender": {
    "name": "张三",
    "mobile": "***********",
    "province": "河北省",
    "city": "保定市", 
    "district": "北郭丹镇",
    "address": "南许村1123号"
  },
  "receiver": {
    "name": "李四",
    "mobile": "***********",
    "province": "河北省",
    "city": "邯郸市",
    "district": "某区",
    "address": "人防大厦507"
  },
  "weight": 13.0,
  "length": 30.0,
  "width": 20.0,
  "height": 10.0,
  "volume": 0.006,
  "quantity": 1,
  "goods_name": "测试物品",
  "pay_method": 0
}
```

#### 参数说明

| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| **sender** | object | ✅ | 寄件人信息 |
| sender.name | string | ✅ | 寄件人姓名，1-50个字符 |
| sender.mobile | string | ✅ | 寄件人手机号，支持手机号/固话/国际号码 |
| sender.province | string | ✅ | 寄件人省份，必须包含'省'、'市'、'自治区'或'特别行政区' |
| sender.city | string | ✅ | 寄件人城市 |
| sender.district | string | ✅ | 寄件人区县 |
| sender.address | string | ✅ | 寄件人详细地址，5-200个字符 |
| **receiver** | object | ✅ | 收件人信息 |
| receiver.name | string | ✅ | 收件人姓名，1-50个字符 |
| receiver.mobile | string | ✅ | 收件人手机号，支持手机号/固话/国际号码 |
| receiver.province | string | ✅ | 收件人省份 |
| receiver.city | string | ✅ | 收件人城市 |
| receiver.district | string | ✅ | 收件人区县 |
| receiver.address | string | ✅ | 收件人详细地址，5-200个字符 |
| **weight** | number | ✅ | 包裹重量(kg)，0.1-100 |
| length | number | ❌ | 包裹长度(cm)，0-200 |
| width | number | ❌ | 包裹宽度(cm)，0-200 |
| height | number | ❌ | 包裹高度(cm)，0-200 |
| volume | number | ❌ | 包裹体积(m³)，0-1.0 |
| quantity | number | ❌ | 包裹数量，默认1，0-100 |
| goods_name | string | ❌ | 物品名称，默认"物品"，最多100个字符 |
| pay_method | number | ❌ | 支付方式：0-寄付，1-到付，2-月结，默认0 |

### 响应格式

#### 成功响应
```json
{
  "success": true,
  "code": 200,
  "message": "查询成功",
  "data": {
    "express_code": "JD",
    "express_name": "京东快递",
    "product_code": "JD_STANDARD",
    "product_name": "京东标准快递",
    "price": 15.50,
    "continued_weight_per_kg": 2.00,
    "calc_weight": 13.0,
    "provider": "yuntong",
    "channel_id": "yuntong_jd",
    "query_time": "2025-07-13T10:30:00+08:00",
    "pickup_time_info": null
  }
}
```

#### 错误响应
```json
{
  "success": false,
  "code": 400,
  "message": "参数验证失败: 寄件人手机号格式不正确，请提供有效的手机号或固话号码"
}
```

#### 响应字段说明

| 字段 | 类型 | 说明 |
|------|------|------|
| success | boolean | 请求是否成功 |
| code | number | 状态码：200-成功，400-参数错误，500-服务器错误，503-服务不可用 |
| message | string | 响应消息 |
| **data** | object | 价格数据（仅成功时返回） |
| data.express_code | string | 快递公司代码，固定为"JD" |
| data.express_name | string | 快递公司名称，固定为"京东快递" |
| data.product_code | string | 产品代码 |
| data.product_name | string | 产品名称 |
| data.price | number | 总价格(元) |
| data.continued_weight_per_kg | number | 每公斤续重价格(元) |
| data.calc_weight | number | 计费重量(kg) |
| data.provider | string | 供应商名称 |
| data.channel_id | string | 渠道ID |
| data.query_time | string | 查询时间(ISO 8601格式，北京时间) |
| data.pickup_time_info | object | 预约时间信息(京东快递暂不支持，固定为null) |

### 状态码说明

| 状态码 | 说明 | 处理建议 |
|--------|------|----------|
| 200 | 查询成功 | 正常处理价格数据 |
| 400 | 参数错误 | 检查请求参数格式和内容 |
| 500 | 服务器内部错误 | 稍后重试或联系技术支持 |
| 503 | 服务暂时不可用 | 熔断器开启，稍后重试 |

## 使用示例

### cURL示例
```bash
curl -X POST "https://api.example.com/api/v1/unified-gateway" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "api_method": "QUERY_JD_PRICE",
    "client_type": "web",
    "username": "test_user",
    "business_params": {
      "sender": {
        "name": "张三",
        "mobile": "***********",
        "province": "河北省",
        "city": "保定市",
        "district": "北郭丹镇",
        "address": "南许村1123号"
      },
      "receiver": {
        "name": "李四",
        "mobile": "***********",
        "province": "河北省",
        "city": "邯郸市",
        "district": "某区",
        "address": "人防大厦507"
      },
      "weight": 13.0,
      "goods_name": "测试物品"
    }
  }'
```

### JavaScript示例
```javascript
const response = await fetch('/api/v1/unified-gateway', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer YOUR_TOKEN'
  },
  body: JSON.stringify({
    api_method: 'QUERY_REALTIME_PRICE',
    client_type: 'web',
    username: 'test_user',
    business_params: {
      sender: {
        name: '张三',
        mobile: '***********',
        province: '河北省',
        city: '保定市',
        district: '北郭丹镇',
        address: '南许村1123号'
      },
      receiver: {
        name: '李四',
        mobile: '***********',
        province: '河北省',
        city: '邯郸市',
        district: '某区',
        address: '人防大厦507'
      },
      weight: 13.0,
      goods_name: '测试物品'
    }
  })
});

const result = await response.json();
if (result.success) {
  console.log('价格:', result.data.price);
} else {
  console.error('查询失败:', result.message);
}
```

## 性能特性

### 超时控制
- 查询超时：5秒
- 重试延迟：500毫秒
- 最大重试：2次

### 熔断器机制
- 失败阈值：5次
- 熔断时间：30秒
- 半开状态：允许一次尝试

### 并发安全
- 支持高并发访问
- 线程安全的熔断器状态管理
- 无状态设计，支持水平扩展

## 注意事项

1. **地址准确性**: 请提供准确的详细地址，地址信息直接影响价格计算
2. **手机号格式**: 支持手机号、固话、国际号码多种格式
3. **重量限制**: 包裹重量不能超过100kg
4. **实时性**: 价格为实时查询，可能因供应商价格调整而变化
5. **熔断保护**: 当服务异常时会触发熔断器，请稍后重试
6. **日志记录**: 所有请求都会被记录，便于问题排查

## 错误处理

### 常见错误及解决方案

| 错误信息 | 原因 | 解决方案 |
|----------|------|----------|
| "寄件人手机号格式不正确" | 手机号格式不符合要求 | 检查手机号格式，支持11位手机号或固话 |
| "详细地址过于简单" | 地址信息不够详细 | 提供至少2个字符的详细地址 |
| "包裹重量必须大于0" | 重量参数无效 | 设置有效的重量值(0.1-100kg) |
| "服务暂时不可用" | 熔断器开启 | 等待30秒后重试 |
| "所有供应商查询失败" | 供应商服务异常 | 稍后重试或联系技术支持 |

## 版本历史

- **v1.0.0** (2025-07-13): 初始版本，支持京东快递专用查价功能
