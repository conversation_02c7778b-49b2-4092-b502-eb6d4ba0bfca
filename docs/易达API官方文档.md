易达API
重要：处理问题请自带请求地址+请求参数，提问前尽可能自行比对文档检查参数是否准确。
编辑记录
编辑日期	编辑类型	编辑内容描述	检索编号
2023/08/04 	新增	新增 快递列表 类型：菜鸟速递（CNSD） 新增 产品列表 类型：菜鸟速递（CNSD）	
2023/08/09	新增	1、创建工单 增加上传图片
2、增加 回复工单 接口
3、工单回调 接口 增加图片参数	
2023/09/01	修改	查询订单信息接口下架,请正确对接回调接口实时同步信息。	
2023/09/05	新增	新增 快递列表 类型：百世（BEST） 新增 产品列表 类型：百世快运（BEST_KY）	
2023/09/06 	修改	工单类型: 线下收费增加 1线下全额收费/2线下超重收费 	
2024/02/20 	修改	线下收费工单必须上传线下交易截图	
2025/01/17	新增	增加 修改订单 接口	
2025/02/11 new	新增	工单回调接口重量异常类型新增返回重量信息	
1 开始接入
接口地址	https://www.yida178.cn/prod-api/thirdApi/execute
请求方式	POST
请求数据类型	application/json
字符编码 	统一采用UTF-8字符编码
基础接入指引：
2.1估价接口(非必需，未维护价格体系客户可使用该接口查询预估费用和计费信息)
2.2创建订单(必需)
2.3取消订单(必需)
2.6得物收件地址(非必需，使用得物渠道下单时，得物地址id字段从该接口获取)
3.x各类回调接口(必需)
1.1 通用参数
请求参数
参数名	参数类型	描述	是否必传
username	String	易达登录账户	是
timestamp	String	时间戳 毫秒值【字符串】	是
sign	String	签名	是
apiMethod	String	调用api方法 （见4.8 apiMethod说明）	是
businessParams	Object	业务参数	是

响应参数
参数名	参数类型	描述	是否必传
code	Integer	响应码	是
data	String	响应数据，失败时为null	否
msg	String	响应说明	是
1.2 签名规则
用户名username , 时间戳（毫秒字符串）timestamp ，私钥privateKey（易达后台-<右上角>API接入-获取privateKey） 字段根据key值排序生成JSON字符串。将生成的字符串按照MD5加密之后，转换成大写
例：username=123123，timestamp=1624607934087，privateKey=asdfghjkl
生成JSON字符串：{"privateKey":"asdfghjkl","timestamp":"1624607934087","username":"123123"}
生成sign=2831987466FEB878B205FCC6FB6293A6
注：时间戳timestamp要求在当前时间6分钟之内。
1.2.1 签名示例（JAVA）
样例仅供参考
/**
* 生成sign
* @param username
* @param timestamp
* @param privateKey
* @return
*/
private String createSign(String username, String timestamp, String privateKey) {
  Map<String, Object> map = new TreeMap();
        map.put("privateKey", privateKey);
        map.put("timestamp", timestamp);
        map.put("username", username);
  return SecureUtil.md5(JSON.toJSONString(map)).toUpperCase();
}
1.2.2 签名示例（PHP）
样例仅供参考
public function execute($requestParams){
  
     $timestamp = (string)intval(microtime(1) * 1000);
     
     $sign_Array = [
          "privateKey" => $this->APPSecret,
          "timestamp"  => $timestamp,
          "username"   => $this->username
        ];

     $sign  = strtoupper(MD5(json_encode($sign_Array,320)));

     $body = [
         "apiMethod"        => $this->Method,
         "businessParams"   => $requestParams,
         "sign"             => $sign,
         "timestamp"        => $timestamp,
         "username"         => $this->username
     ];


     $header = ["Content-Type:application/json"];
 
     $curl = curl_init();
     curl_setopt($curl, CURLOPT_URL, $this->requestUrl);
     curl_setopt( $curl, CURLOPT_HTTPHEADER, $header);
     curl_setopt($curl, CURLOPT_POST, 1);
     curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($body,320));
     curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
     $result = curl_exec($curl);
     curl_close($curl);
      
     return json_decode($result, true);
}

2 快递接口
2.1 预下单[获取渠道&价格(按需接入)]
apiMethod：SMART_PRE_ORDER
请求参数说明：
参数名	参数类型	描述	是否必传
deliveryType	String	快递公司	否
deliveryBusiness	String	产品类型	否
customerType	String	寄件通道类型（默认kd）	否
thirdNo	String	商家订单号(字母、数字，字段长度1-30,请保证同一对接账户下唯一)
为兼容部分对接客户无服务器场景。该字段不传的情况，返回的价格规则会由json格式字符串变更为直译价格规则字符串，建议客户携带该字段对接。	是
senderProvince	String	发件省	是
senderCity	String	发件市	是
senderDistrict	String	发件区/县	是
senderAddress	String	发件详细地址（不携带省市区）	是
senderName	String	发件人姓名	是
senderTel	String	发件人电话	2选1必填
senderMobile	String	发件人手机	2选1必填


receiveProvince	String	收件省	否
receiveCity	String	收件市	否
receiveDistrict	String	收件区	否
receiveAddress	String	收件详细地址（不携带省市区）	否
receiveName	String	收件人姓名	否
receiveTel	String	收件人电话	2选1必填
receiveMobile	String	收件人手机	2选1必填
goods	String	托寄物名称	是
packageCount	Integer	包裹数	是
weight	BigDecimal	重量(单位：kg，保留小数点后两位)	是
vloumLong	BigDecimal	长/CM 	否
vloumWidth	BigDecimal	宽/CM	否
vloumHeight	BigDecimal	高/CM	否
guaranteeValueAmount	BigDecimal	保价金额 上限30w元	否
pickUpStartTime	Date	预约取件开始时间yyyy-MM-dd HH:mm:ss	否
pickUpEndTime	Date	预约取件结束时间yyyy-MM-dd HH:mm:ss	否
remark	String	备注	否
onlinePay
	String	是否线上付（默认为Y ） Y（线上）
N （线下）
ALL（线上+线下）	是
payMethod	Integer	10 -线下寄付
20 -线下到付 
30-线上支付(冗余onlinePay字段，会覆盖onlinePay)	否
返回响应说明：
参数名	参数类型	描述	是否必传
data
	Map<String,List<Object>>	返回参数为KV类型
key=快递类型, value=对应的渠道集合	
channelId	Long	渠道ID （用于指定渠道下单）	是
channelName	String	渠道名称	是
calcFeeWeight	BigDecimal	计费重量(KG)-含计抛	是
preOrderFee	BigDecimal	预支付总费用 （元）	是
preDeliveryFee	BigDecimal	预估快递费（元）	是
preBjFee	BigDecimal	预估保价费（元）	否
originalFee	BigDecimal	官网原价(仅供参考)（元）	是
deliveryType	String	快递类型	是
deliveryBusiness	String	产品类型	是
limitWeight	BigDecimal	限重 （KG）	是
lightGoods	BigDecimal	抛比	是
calcFeeType	String	易达计费类型 折扣=discount/通票=profit	是
price 	String	易达计费规则(JSON格式) 根据calcFeeType返回
折扣价格块 / 通票价格块	是
originalPrice	String	原价计费规则 (JSON格式)	是
bjRule	String	保价规则-分段(JSON格式)	是
bjRuleStr	String	保价规则-分段	是
isBest	String	是否为最佳渠道（仅供参考）	否
onlinePay	String	Y（线上）/ N （线下）	是
payMethod	Integer	10-线下寄付
20-线下到付 
30-线上支付	
通票价格块（price，originalPrice）
start	Interger	开始重量	是
end	Interger	结束重量	是
first	BigDecimal	分段首重	是
add	BigDecimal	分段续重	是
通票价格计算方式：直接按照规则命中公斤段之后计费
例如 
price=[{"add":7,"end":0,"first":11,"start":1}]
5公斤计费：11+4*7=39 元
折扣价格块（price）
discount	BigDecimal	折扣比例（小数形式）	是
perAdd	BigDecimal	单笔加收	是
折扣价格计算方式：原价规则计费后*discount+perAdd
例如 
price={"perAdd":0.2,"discount":0.7}
originalPrice=[{"add":5,"end":0,"first":18,"start":1}]
5公斤计费：(18+4*5)*0.7+0.2=26.8 元
保价规则块（bjRule）
start	BigDecimal	保额区间-起	是
end	BigDecimal	保额区间-止	是
bjPrice	BigDecimal	固定保价收费:区间内固定费用	二选一必填

bjRate	BigDecimal	比例保价收费:区间内根据（保费*比例）	
请求体示例
{
    "username":"马XX",
    "timestamp":"1660830349366",
    "sign":"12F9D67D6B522DD5B8202B81267DSS4EE",
    "apiMethod":"SMART_PRE_ORDER",
    "businessParams":{
        "receiveAddress":"三墩镇亲亲家园二期",
        "receiveMobile":"***********",
        "receiveTel":"",
        "receiveName":"小李",
        "receiveProvince":"浙江省",
        "receiveCity":"杭州市",
        "receiveDistrict":"西湖区",
        "senderAddress":"十里大道555号玲珑府2幢",
        "senderMobile":"***********",
        "senderTel":"",
        "senderName":"小王",
        "senderProvince":"江西省",
        "senderCity":"九江市",
        "senderDistrict":"濂溪区",
        "goods":"服装",
        "packageCount":1,
        "weight":1,
        "customerType":"kd",
        "onlinePay":"ALL",
        "payMethod":10,
        "guaranteeValueAmount":3000,
        "thirdNo":"TD965412356"
    }
}
返回响应示例：
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "SF": [
            {
                "channelId": "42",
                "channelName": "标快test1区",
                "preOrderFee": "27.8",
                "preDeliveryFee": "12.8",
                "preBjFee": "15",
                "deliveryType": "SF",
                "limitWeight": "60.0",
                "lightGoods": "6000.0",
                "calcFeeType": "discount",
                "price": "{\"perAdd\":0.2,\"discount\":0.7}",
                "originalPrice": "[{\"add\":5,\"end\":0,\"first\":18,\"start\":1}]",
                "originalFee": "18",
                "isBest": null,
                "bjRuleStr": "保额0至500，保费1元;保额501至1000，保费2元;保额1001至0，费率0.005;",
                "onlinePay": "Y",
                "rebateRatio": null,
                "expReturn": null,
                "deliveryBusiness": "SF_GZ_BK"
            },
            {
                "channelId": "43",
                "channelName": "顺丰标快0422测试1区",
                "preOrderFee": "29.7",
                "preDeliveryFee": "14.7",
                "preBjFee": "15",
                "deliveryType": "SF",
                "limitWeight": "100.0",
                "lightGoods": "6000.0",
                "calcFeeType": "discount",
                "price": "{\"perAdd\":0.3,\"discount\":0.8}",
                "originalPrice": "[{\"add\":5,\"end\":0,\"first\":18,\"start\":1}]",
                "originalFee": "18",
                "isBest": null,
                "bjRuleStr": "保额0至500，保费1元;保额501至1000，保费2元;保额1001至0，费率0.005;",
                "onlinePay": "Y",
                "rebateRatio": null,
                "expReturn": null,
                "deliveryBusiness": "SF_GZ_BK"
            },
        ],
        "DOP": [
            {
                "channelId": "68",
                "channelName": "德邦P测试2205121区",
                "preOrderFee": "25.2",
                "preDeliveryFee": "13.2",
                "preBjFee": "12",
                "deliveryType": "DOP",
                "limitWeight": "60.0",
                "lightGoods": "6000.0",
                "calcFeeType": "discount",
                "price": "{\"perAdd\":0,\"discount\":0.6}",
                "originalPrice": "[{\"add\":4,\"end\":60,\"first\":22,\"start\":3},{\"add\":2.2,\"end\":0,\"first\":206,\"start\":61}]",
                "originalFee": "22",
                "isBest": null,
                "bjRuleStr": "保额0至20000，费率0.004;保额20001至0，费率0.005;",
                "onlinePay": "Y",
                "rebateRatio": null,
                "expReturn": null,
                "deliveryBusiness": "DOP_RCP"
            }
        ],
        "YTO": [
            {
                "channelId": "24",
                "channelName": "圆通测试0408-11区",
                "preOrderFee": "25.7",
                "preDeliveryFee": "10.7",
                "preBjFee": "15",
                "deliveryType": "YTO",
                "limitWeight": "60.0",
                "lightGoods": "6000.0",
                "calcFeeType": "profit",
                "price": "[{\"add\":7,\"end\":0,\"first\":11,\"start\":1}]",
                "originalPrice": "[{\"add\":8,\"end\":0,\"first\":12,\"start\":1}]",
                "originalFee": "12",
                "isBest": null,
                "bjRuleStr": "保额0至500，保费1元;保额501至1000，保费2元;保额1001至0，费率0.005;",
                "onlinePay": "Y",
                "rebateRatio": null,
                "expReturn": null,
                "deliveryBusiness": "YTO_BK"
            }
        ]
    }
}

2.2 创建订单
apiMethod：SUBMIT_ORDER_V2
请求参数说明：
参数名	参数类型	描述	是否必传
thirdNo	String	商家订单号(字段长度1-30,请保证同一对接账户下唯一)	是
deliveryType	String	快递公司	是
customerType	String	寄件通道类型(默认kd)	是
deliveryBusiness	String	产品类型（见 4.2 快递产品列表）	是
channelId	Long	指定下单渠道ID（通过 2.1预下单 接口获取）	否
senderProvince	String	发件省	是
senderCity	String	发件市	是
senderDistrict	String	发件区	是
senderName	String	发件人姓名	是
senderAddress	String	发件详细地址（不携带省市区）	是
senderTel	String	发件人电话	2选1必填
senderMobile	String	发件人手机	2选1必填
poizonAddressId
	Long

receiveProvince	String	收件省	否
receiveCity	String	收件市	否
receiveDistrict	String	收件区	否
receiveName	String	收件人姓名	否
receiveAddress	String	收件详细地址（不携带省市区）	否
receiveTel	String	收件人电话	2选1必填
receiveMobile	String	收件人手机	2选1必填
goods	String	托寄物名称	是
packageCount	Integer	包裹数	是
weight	Double	重量(单位：kg，保留小数点后两位)	是
vloumLong	Double	长/CM	否
vloumWidth	Double	宽/CM	否
vloumHeight	Double	高/CM	否
guaranteeValueAmount	Double	保价金额 保价上限30w元	否
pickUpStartTime	Date	预约取件时间开始yyyy-MM-dd HH:mm:ss	否
pickUpEndTime	Date	预约取件时间结束yyyy-MM-dd HH:mm:ss	否
remark	String	备注	否
onlinePay
	String	是否线上付（默认为Y ） Y（线上）
N （线下）
ALL（线上+线下）	否
payMethod
	Integer	10-线下寄付
20-线下到付 
30-线上月结	否

orderIncrements	List	附加服务列表 （见 4.4 增值服务）	否
type	String	附加服务类型	是
value	String	附加值	否

返回响应参数说明：
参数名	参数类型	描述	是否必传
orderNo	String	易达订单号	是
deliveryId	String	运单号：根据总部渠道确认是否下单返回
同步返回：直接获取到运单号
异步返回：通过易达推送携带	否

pickUpCode	String	取件码	否
childDeliveryIds	List<String>	子单号（包含母单）	否
preOrderFee	BigDecimal	预估订单总费	否
preDeliveryFee	BigDecimal	预估基础快递费	否
originalPrice	String	原价规则	否
calcFeeType	String	计费方式	否
calcFeeWeight	BigDecimal	计费重量	否
price	String	价格规则	否
limitWeight	BigDecimal	限重	否
printInfo	Map<String, Object>	打印信息 不同类型运单的返回内容不同	否

ytoShortAddress
	String	(圆通)三段码	否

请求体示例
{
    "username":"张德帅",
    "timestamp":"1647222328003",
    "sign":"BB253038568AD3EKIPOL4836131C025DH",
    "apiMethod":"SUBMIT_ORDER_V2",
    "businessParams":{
        "deliveryType":"SF",
        "thirdNo":"123456789",
        "senderProvince":"山东省",
        "senderCity":"东营市",
        "senderDistrict":"垦利区",
        "senderAddress":"某个街道某幢楼某个单元603",
        "senderName":"崔进度",
        "senderMobile":"1761289239",
        "senderTel":"",
        "receiveProvince":"北京市",
        "receiveCity":"北京市",
        "receiveDistrict":"海淀区",
        "receiveAddress":"某个街道某幢楼某个单元301",
        "receiveName":"秦拿手",
        "receiveMobile":"***********",
        "receiveTel":"",
        "weight":1,
        "vloumLong":1,
        "vloumWidth":1,
        "vloumHeight":1,
        "vloum":null,
        "goods":"日用品",
        "packageCount":1,
        "remark":"轻拿轻放",
        "guaranteeValueAmount":null,
        "deliveryBusiness":"SF_GZ_BK",
        "onlinePay":"N",
        "offlinePayMethod":10,
        "orderIncrements":[{"type":"PHOTO_RECEIPT","value":"3"}]
    }
}
返回响应示例：
{
    "code":200,
    "data":{
        "orderNo":"16545645615656",
        "deliveryId":"SF45465456456465456",
        "printInfo":{
            "pdfUrl":"http://asdfadfabelram=123124"
        }
    },
    "msg":"操作成功"
}

2.3 取消订单
apiMethod：CANCEL_ORDER 

请求参数说明：
参数名	参数类型	描述	是否必传
deliveryId	String	运单号（二选一必填）	否
orderNo	String	易达订单号（二选一必填）	否

返回响应参数说明：
参数名	参数类型	描述	是否必传
data	Boolean	true 成功 false 失败	是
请求体示例
{
    "apiMethod":"CANCEL_ORDER",
    "businessParams":{
        "deliveryId":"772014386606047",
        "deliveryType":"STO-INT"
    },
    "sign":"E81302F2B060606060E8B0AEAEAE7B01",
    "timestamp":"1635758892000",
    "username":"账号"
}
返回响应示例：
{
    "code":200,
    "data":true,
    "msg":"运单取消成功"
}

2.8 工单接口
2.8.1 创建工单
apiMethod: CREATE_WORK_ORDER
请求参数说明：
参数名	参数类型	描述	是否必传
orderNo	String	订单号 （订单号/运单号两者必传一）	否
deliveryId	String	运单编号[快递单号]（订单号/运单号两者必传一）	否
type	Integer	工单类型 （见 4.7 工单类型）	是
content	String	问题描述 （见 4.7 工单类型 问题描述）	是
weight	BigDecimal	反馈重量（反馈类型为重量异常时填写，不填默认取下单重量）	否
modity	String	商品价值（反馈类型为破损、遗失、错件必填）	否
urls	String	图片地址（多图片用 , 隔开，最多支持上传5张图片）
类型为线下收费时必传	否
notifyUrl	String	回调地址（接收工单回调信息）请认真核对填写回调地址，不填的情况下将不会有任何的工单回复和状态的推送。	否
secondType	Integer	工单类型为 线下收费 时 可传1全额收费 2超重收费 默认1全额收费	是
overweight	BigDecimal	工单类型为 线下收费 且 secondType为2超重收费 时必填	否
返回业务参数说明：
参数名	参数类型	描述	是否必传
status	Integer	工单状态（ 见 4.6 工单状态）	是
taskNo	String	工单编号	是
请求体示例:
{
    "businessParams":{
        "deliveryId":"YDH11111111111",
        "orderNo":"YD222222"
        "notifyUrl":"https://xxxxx/xxx/xxx",
        "weight":"2"
        "type":2,
        "content":"您好，此单客户反馈重量2kg，请核实正确计费重量。",
        "urls":"https://xxx/xxx/p1.jpeg,https://xxx/xxx/p2.jpeg"
    },
    "sign":"60B1E7CCD420CDC8564B58F999999F31",
    "timestamp":"*************",
    "username":"test"
}
返回响应示例：
{
    "msg":"操作成功",
    "code":200,
    "data":{
        "status":1,
        "taskNo":"****************"
    }
} 
2.8.2 回复工单
apiMethod: REPLY_WORK_ORDER
请求参数说明：
参数名	参数类型	描述	是否必传
taskNo	String	工单号	是
content	String	回复内容	是
urls	String	图片地址（多图片用 , 隔开，最多支持上传2张图片）	否
返回业务参数说明：
参数名	参数类型	描述	是否必传
status	Integer	工单状态（ 见 4.6 工单状态）	是
taskNo	String	工单编号	是
请求体示例:
{
    "businessParams":{
        "taskNo":"ST230719299999999_11",
        "content":"再次催促取件"
        "urls":"https://xxx/xxx/p1.jpeg,https://xxx/xxx/p2.jpeg"
    },
    "sign":"60B1E7CCD420CDC8564B58F999999F31",
    "timestamp":"*************",
    "username":"test"
}
返回响应示例：
{
    "msg":"操作成功",
    "code":200,
    "data":{
        "status":1,
        "taskNo":"ST230719299999999_11"
    }
} 
2.8.3 工单回调接口
请求方式	POST
请求数据类型	application/json

回调接收要求：接收回调消息请在1秒内按规定格式返回，以确保消息被准确接收(什么是非准确接收:未按要求格式回传或code不为200)。建议收到回调请求后优先返回接收成功，再异步处理业务逻辑避免超时。超时或返回接收不正确易达会发起重试，多次失败会屏蔽回调通道。为避免影响您的正常业务，请按要求对接。

参数名	参数类型	描述	是否必传
taskNo	String	工单编号	是
status	Integer	工单状态（ 见 4.6 工单状态）	是
content	String	回复内容	否
urls	String	图片地址 （多图片用 , 隔开）	
type	Integer	工单类型（见 4.7 工单类型）	是
data	Object	详情附加信息	是

weight
	BigDecimal
	核实重量（工单类型为重量异常时，重量存在变动，结合业务进行判断）	否
返回参数说明：
参数名	参数类型	描述	是否必须
code	String	响应码--成功为200	是
msg	String	返回信息	是
success	Boolean	true/false	是
推送参数示例：
{
    "content":"已催总部",
    "status":2,
    "taskNo":"****************",
    "urls":"https://xxx/xxx/p1.jpeg,https://xxx/xxx/p2.jpeg"
}
2.9 账户余额查询
apiMethod: ACCOUNT_BALANCE
请求参数说明：
参数名	参数类型	描述	是否必传
userMobile	String	校验手机号和账户是否匹配	是
返回业务参数说明：
参数名	参数类型	描述	是否必传
userName	String	账户名称	是
accountBalance	BigDecimal	账户余额	是
请求体示例:
{
    "businessParams":{
        "userMobile":"***********"
    },
    "sign":"60B1E7CCD420CDC8564B58F999999F31",
    "timestamp":"*************",
    "username":"YD00001"
}
返回响应示例：
{
    "msg":"操作成功",
    "code":200,
    "data":{
        "userName":"YD00001",
        "accountBalance":100000.88
    }
} 

2.10 修改订单
apiMethod: UPDATE_ORDER
请求参数说明：
参数名	参数类型	描述	是否必传
deliveryId	String	运单编号[快递单号]（二选一必填）	是
orderNo	String	易达订单号（二选一必填）	是
pickUpStartTime	Date	预约取件时间开始yyyy-MM-dd HH:mm:ss	是
pickUpEndTime	Date	预约取件结束时间yyyy-MM-dd HH:mm:ss	是
返回业务参数说明：
参数名	参数类型	描述	是否必传
pickUpStartTime	Date	预约取件时间开始yyyy-MM-dd HH:mm:ss	是
pickUpEndTime	Date	预约取件结束时间yyyy-MM-dd HH:mm:ss	是
请求体示例
{
    "apiMethod":"UPDATE_ORDER",
    "businessParams":{
        "deliveryId": "***************",
        "orderNo": "********************",
        "pickUpStartTime": "2025-01-19 00:00:00",
        "pickUpEndTime":"2025-01-19 00:00:00"
    },
    "sign":"E81302F2B060606060E8B0AEAEAE7B01",
    "timestamp":"1635758892000",
    "username":"账号"
}
返回修改成功响应示例：
{
    "code": 200,
    "msg": "运单修改成功",
    "data": {
        "pickUpEndTime": "2025-01-19 11:00:00",
        "pickUpStartTime": "2025-01-19 09:00:00"
    }
}
返回修改失败响应示例：
{
    "code": 500,
    "msg": "运单修改失败==>订单不存在",
    "data": null
}
3 回调说明
请求方式	POST
请求数据类型	application/json

如何配置回调地址：进入易达后台，右上角个人中心，API接入，可自行获取privateKey，可在此处配置回调地址，回调地址会有demo数据校验，请根据文档要求返回，校验通过后配置成功。


回调接收要求：接收回调消息请在1秒内按规定格式返回，以确保消息被准确接收。建议收到回调请求后优先返回接收成功，再异步处理业务逻辑避免超时。超时或返回接收不正确易达会短暂屏蔽回调通道，如发生多次失败系统会自动拉黑您的推送通道，届时可能会丢失拉黑期间回调信息。为避免影响您的正常业务，请按要求对接。

推送参数说明：
参数名	参数类型	描述	是否必传
orderNo	String	易达订单号	是
deliveryId	String	运单编号[快递单号]	否
thirdNo	String	商家订单号	否
deliveryType	String	快递类型 （见 4.1 快递列表）	是
pushType	Integer	推送类型 1-状态推送 2-账单推送 3-揽收推送 5-订单变更	是
contextObj	Object	推送内容 根据pushType决定具体推送内容，请根据下方示例文档接入	是
返回参数说明：
参数名	参数类型	描述	是否必须
code	String	响应码--成功为200	是
msg	String	返回信息	是
success	Boolean	true/false	是
3.1.状态信息
参数名	参数类型	描述	是否必传
ydOrderStatus	String	订单状态（根据易达） 
1-待取件 
11-已取件
2-运输中 
3-已签收 
6-异常 
10-已取消 	是

ydOrderStatusDesc	String	订单状态描述（根据易达）	是
推送参数示例：
{
    "orderNo":"GXSF20221129153252333012",
    "deliveryType":"SF",
    "thirdNo":"123456",
    "pushType":1,
    "deliveryId":"SF1310191795367",
    "contextObj":{
        "ydOrderStatusDesc":"已取消",
        "ydOrderStatus":"10"
    }
}

3.2 计费信息 
参数名	参数类型	描述	数据参考	是否必传
realWeight	BigDecimal	实际重量 (KG)	1	是
realVolume	BigDecimal	实际体积 (CM³)	1000	否
calcFeeWeight	BigDecimal	计费重量（合计抛）	8000	否
packageCount	Integer	包裹数
	1	否
freightBase	String	fee(费用)/weight(重量)，计费基于费用或重量。(解决个别订单更换产品或未进入折扣)	weight	是
feeBlockList	Array[Object]	费用明细（所有金额之和为总费用） 	-	是

type

	Integer
	费用类型（见 4.5 费用清单）（type=0为运费，除运费外其他费用项均为原价不参与优惠）	0	是

name
	String	费用名称（可用于渲染费用明细，请勿使用中文名称比对进行业务逻辑）	运费	是

fee
	BigDecimal	金额 （元）	5.9	是
priceRule	Array[Object]	新计费规则（计费规则发生改变的情况下存在）		否

start
	Interger	开始重量	1	是

end
	Interger	结束重量	30	是

first
	BigDecimal	分段首重	5.5	是

add
	BigDecimal	分段续重	1.2	是
newReceiveProvince	String	新收件省（收件地址发生改变的情况下存在）	浙江	否
推送参数示例：
{
    "orderNo":"SF20220609175727827759",
    "deliveryType":"SF",
    "pushType":2,
    "thirdNo":"123456",
    "deliveryId":"SF7444439943078",
    "contextObj":{
        "realWeight":3,
        "calcFeeWeight":3,
        "packageCount":1,
        "needRebate":"Y",
        "feeBlockList":[
            {
                "fee":10,
                "type":1,
                "name":"保价费"
            },
            {
                "fee":9.8,
                "type":3,
                "name":"耗材费"
            },
            {
                "fee":22.6,
                "type":0,
                "name":"运费"
            }
        ],
        "priceRule": [
            {
                "add": 1.50,
                "end": 20,
                "first": 5.59,
                "start": 1
            },
            {
                "add": 2.50,
                "end": 50,
                "first": 100.59,
                "start": 21
            }
        ]
    }
}

3.3 揽收信息
参数名	参数类型	描述	是否必传
courierName	String	取件人	否
courierPhone	String	取件人联系方式	否
pickUpCode	String	取件码	否
siteName	String	站点	否
推送参数示例：
{
    "orderNo":"ZTO20221204153325708488",
    "deliveryType":"ZTO",
    "pushType":3,
    "thirdNo":"123456",
    "deliveryId":"",
    "contextObj":{
        "courierName":"张贤",
        "courierPhone":"***********",
        "pickUpCode":"112233",
        "siteName":"站点A"
    }
}

3.4 订单变更 
参数名	参数类型	描述	是否必传
businessTypeNew	String	新产品类型	否
deliveryIdNew	String	新运单号	否
推送参数示例：
{
    "contextObj":{
        "businessTypeNew":"RCP",
        "deliveryIdNew":"DPK1234567896"
    },
    "deliveryId":"DPK51523698745",
    "deliveryType":"DOP",
    "thirdNo":"123456",
    "orderNo":"28220211218144603040555",
    "pushType":5
}

返回报文：
{
    "msg":"接收成功",
    "code":200,
    "success":true
}

4 附录
4.1 快递列表
快递公司	deliveryType
京东	JD
圆通	YTO
申通	STO-INT
德邦	DOP
顺丰	SF
极兔	JT
中通	ZTO
韵达	YUND
菜鸟裹裹	CAINIAO
菜鸟速递	CNSD
百世	BEST
跨越速运	KY
EMS	EMS
壹米滴答	YMDD
顺心捷达	SXJD

4.2 快递产品列表
  仅列出可供选择的类型，如有疑问请联系对接人员
快递公司	customerType	产品类型	deliveryBusiness
顺丰 (SF)	kd (快递)	顺丰特快	SF_GZ_TK
		顺丰标快	SF_GZ_BK
		顺丰陆运包裹	SF_LYBG
德邦 (DOP)	kd (快递)	德邦大件快递360	DOP_RCP
		德邦标准快递	DOP_PACKAGE
	ky（快运）
	德邦精准汽运	DOP_LRF
		德邦精准卡航	DOP_FLF
		德邦重包入户	DOP_NZBRH
京东 (JD)	ky (快运)
	京东特快零担	JD_TK_LD
		京东特快重货	JD_TK_ZH
	kd (快递)
	京东特惠送	JD_THS
		京东特快送	JD_TKS
申通 (STO-INT)	kd (快递)	申通标快	STO_INT_BK
极兔 (JT)	kd (快递)	极兔标快	JT_BK
圆通 (YTO)	kd (快递)	圆通标快	YTO_BK
中通（ZTO）	kd (快递)	中通标快	ZTO_BK
韵达 (YUND)	kd (快递)	韵达标快	YUND_BK
菜鸟（CAINIAO）	kd（快递)	菜鸟标快	CAINIAO_BK
菜鸟速递（CNSD）	kd（快递)	菜鸟速递	CNSD
百世（BEST）	ky（快运）	百世快运	BEST_KY
跨越速运（KY）
	ky（快运）
	跨越专运	KY_ZY
		跨越速运省内次日达	KY_SNCRD
		跨越速运陆运	KY_LY
邮政（EMS）	kd（快递）	邮政特快专递	EMS_TKZD
壹米滴答（YMDD）	ky（快运）	壹米滴答标快	YMDD_BK
顺心捷达（SXJD）	ky（快运）	顺心捷达零担	SXJD_LD

4.3 寄件渠道类型
仅列出可供选择的类型，如有疑问请联系对接人员
名称	类型
快递	kd
快运	ky


4.5 费用清单
本清单列举了目前已接入的各个快递可能产生的费用项，费用分为6大类。分别为
快递运费【0】，保价费【1】，春节加派费【2】，耗材费【3】，逆向费【10】，其他费【100】。
其中其他费包含了各类快递的增值费用，以实际返回的费用名称为准，如出现不清晰的费用名称可咨询客服并找总部核实。
费用类型	type	费用名称
快递运费	0	实收快递费
保价费	1	保价费
春节加派费	2	春节加派费
耗材费	3	耗材费
逆向费	10	逆向费用
其他费用	100	其他费用
(具体费用名称，类似装卸费/提货费/送货服务费等等，如无具体明细项，可咨询客服确认)
4.6 工单状态
类型	名称
1	待回复
2	已回复
3	已完结
4.7 工单类型
仅列出可供选择的类型，如有疑问请联系对接人员。
注意：问题描述#处为补充内容，必须填写相应信息，否则影响工单处理进度。
类型	名称	场景	发起工单限制条件	问题描述
1	催揽收	催促揽收	运输状态为待取件，下单时间超30分钟	催促揽收
2	重量异常
	寄件人对重量有异议，需要重新核实
	订单状态为已结算，下单后次月7号前提交	您好，此单客户反馈重量#kg，请核实正确计费重量。
3	虚假揽收	涉及快递员虚假揽件	订单状态非待取件，下单时间超5天	涉及快递员虚假揽件
4	线下收费	快递员线下收费	下单后次月7号前提交	快递员线下收费
5	破损（暂不支持）	客户投诉包裹破损	下单1月内	客户投诉包裹破损
6	遗失（暂不支持）	客户投诉包裹丢失	下单1月内	客户投诉包裹丢失
7	签收未收到货	收件方反馈未收到货	运输状态为已签收，下单后1月内	收件方反馈未收到货
8	错件	收件人收到的包裹非寄件人寄出的包裹	下单后1月内	收件人收到的包裹非寄件人寄出的包裹
9	催物流/揽收后未发出	物流12小时未更新	运输状态非待取件，下单后30分钟至下单后1月内可提交	催物流/揽收后未发出
10	催派送	快递需尽快派送	下单后1月内	快递需尽快派送
11	更改收件信息（暂不支持）	寄件人需要改地址转寄 	订单非派件、已签收状态	您好，此订单请操作更改收件信息为#
12	拦截退回（暂不支持）	快递员已取件，客户联系不想寄了，协助将包裹退回去给寄件人	订单非已签收状态	您好，此订单请操作拦截地址为#
13	核实退回原因	包裹退回中，核实退回原因	下单后1月内	包裹退回中，核实退回原因
14	物流停滞	24小时未更新物流轨迹	下单后1天至次月7号前可提交	物流停滞
15	重新分配快递员	需要重新分配有效的快递员	运输状态为待取件，下单后1月内	您好，此订单分配揽收小哥错误，请重新分配正确的揽收小哥
16	取消运单	已取件退回	下单后1天至次月7号前可提交	已取件退回
4.8 apiMethod说明
名称	类型
预下单	SMART_PRE_ORDER
创建订单	SUBMIT_ORDER_V2
取消订单	CANCEL_ORDER
查询订单信息	QUERY_ORDER_INFO_V2
轨迹查询	DELIVERY_TRACE
得物收件地址查询	QUERY_POIZON_ADDRESS
获取打印信息	QUERY_PRINT_INFO
创建工单	CREATE_WORK_ORDER
修改订单	UPDATE_ORDER
5 注意事项 （必读）

1、保价
部分快递和渠道暂不支持保价， 将返回"不支持保价"。
部分渠道有强制保价请以实际渠道情况为准，如有疑问请咨询客服
保费如存在小数 则向上取整

2、推送补充机制
如果下单时没有返回快递单号且半小时内没有收到我方快递单号推送，或者在贵方向用户收费费用的最终节点还未收到易达侧费用推送， 请调用订单查询接口主动进行查询

3、顺丰预约取件时间说明：
（1）由于预约取件时间如果不传将不会分配快递员，目前顺丰侧预约取件时间必传，且至少为当前时间1小时后
（2）如果预约取件时间不在该寄件地址顺丰网点的营业时间范围内 会导致下单失败，返回报错信息中将包含该寄件地址营业网点的营业时间 。报错信息示例 ：当前地址营运时间为:(1000-1430)
（3）取件快递小哥大概在预约取件时间1小时前进行分配（顺丰因为涉及到小哥和网点的考核，所有订单都是取件前一小时根据小哥排班分配小哥）
（4）如果因疫情或者交通管制 也可能报营业时间错误， 且报错信息中的营业时间不正常
报错信息示例 ：当前地址营运时间为:(1500-1430)

4、取消订单
（1）目前仅支持待取件状态订单取消， 如其他订单状态取消请联系客服
（2 ）德邦订单下单3分钟内禁止取消
（3）未生成（回传/推送）快递单号的订单禁止取消， 如需取消请联系客服处理。
（4）调用接口取消的订单不会推送取消状态

5、代收货款
易达侧代收货款功能目前处于关闭状态， 如下单时传相关字段， 易达侧将忽略该字段进行下单

6、智能寄件
（1）调用2.3.8 智能预下单获取各个渠道的计费信息及渠道对应的channalId和deliveryType
（2）通过智能寄件获取到的channalId和deliveryType，调用2.3.2指定渠道创建订单

7、2378取整模式
计费重量进位规则： （1）10KG以下，续重以0.1KG为计重单位，四舍五入保留1位小数；
例如 ：实际重量2.8 您的折扣为0.9， 那么费用 = （1*20首重+1.8 * 10续重）* 0.9折扣 = 34.2元
（2）10-100KG，续重以0.5KG为计重单位， 例如：10.1 10.2 计费是10 
10.3 10.4 10.5 10.6 10.7是10.5 
10.8 10.9是11
采用2378制（2退3进7退8进）； 100KG及以上，四舍五入取整数。
8、成为代理
成为代理请联系微信： top924428684
