# 🔧 当前Timestamp配置设置

## 📊 配置文件更新总览

### 1. **config/config.yaml**
```yaml
security:
  signature:
    enabled: true
    timestamp_validity_seconds: 1800  # 🚀 优化：30分钟时间戳有效期
    nonce_validity_seconds: 1800      # 🚀 优化：30分钟nonce有效期
    max_request_body_size: 1048576    # 1MB最大请求体
    disable_in_development: false     # 所有环境都强制启用签名验证
    timezone: "Asia/Shanghai"          # 签名验证使用北京时间

  nonce:
    validity_duration: "30m"           # 🚀 优化：30分钟nonce有效期
    max_nonce_length: 64
    min_nonce_length: 16
    redis_key_prefix: "nonce:v2:"
    redis_timeout: "3s"
    timezone: "Asia/Shanghai"
```

### 2. **internal/security/config.go**
```go
func DefaultSecurityConfig() *SecurityConfig {
    return &SecurityConfig{
        Signature: SignatureConfig{
            Enabled:                  true,
            TimestampValiditySeconds: 1800,  // 🚀 优化：30分钟
            NonceValiditySeconds:     1800,  // 🚀 优化：30分钟
            MaxRequestBodySize:       1024 * 1024, // 1MB
            SkipPaths: []string{
                "/health",
                "/oauth/token",
            },
            DisableInDevelopment: false,
        },
    }
}
```

### 3. **cmd/main.go**
```go
// 🔒 强制启用签名验证 - 企业级安全要求
securityConfig.Signature.Enabled = true
securityConfig.Signature.DisableInDevelopment = false
securityConfig.Signature.TimestampValiditySeconds = 1800  // 🚀 优化：30分钟
securityConfig.Signature.NonceValiditySeconds = 1800      // 🚀 优化：30分钟
securityConfig.Signature.MaxRequestBodySize = 1024 * 1024 // 1MB
```

### 4. **internal/security/signature.go**
```go
// 🚀 优化：检查时间戳是否来自未来（允许更大的时钟偏差，适应分布式环境）
if ts.Sub(now) > 2*time.Minute {
    return false
}
```

## 🎯 配置层级和优先级

### **配置生效顺序**：
1. **config.yaml** → 基础配置
2. **DefaultSecurityConfig()** → 代码默认值
3. **cmd/main.go** → 运行时强制设置（最高优先级）

### **实际生效值**：
- **通用API timestamp有效期**：30分钟 (1800秒)
- **通用API 时钟偏差容忍**：2分钟 (120秒)
- **统一网关 timestamp有效期**：30分钟 (专用逻辑)
- **统一网关 时钟偏差容忍**：2分钟 (专用逻辑)
- **统一网关 并发限制**：50次/秒 (专用逻辑)

## 📋 配置对比表

| 配置项 | 优化前 | 优化后 | 适用范围 |
|--------|--------|--------|----------|
| **基础timestamp有效期** | 300秒 (5分钟) | **1800秒 (30分钟)** | 所有API |
| **基础时钟偏差容忍** | 5秒 | **120秒 (2分钟)** | 所有API |
| **统一网关timestamp有效期** | 300秒 (5分钟) | **1800秒 (30分钟)** | /api/gateway/execute |
| **统一网关时钟偏差容忍** | 5秒 | **120秒 (2分钟)** | /api/gateway/execute |
| **统一网关并发限制** | 无 | **50次/秒** | /api/gateway/execute |
| **Redis键过期时间** | 300秒 | **1800秒 (30分钟)** | 所有防重放检查 |

## 🚀 优化特性

### **1. 全系统一致性**
- 所有API都使用30分钟有效期
- 所有API都使用2分钟时钟偏差容忍
- 配置文件、代码默认值、运行时设置保持一致

### **2. 统一网关专门优化**
- 智能并发控制：50次/秒限制
- 分级日志记录：20次/秒警告，50次/秒拒绝
- 毫秒级和秒级时间戳分别处理

### **3. 向后兼容**
- 现有客户端无需修改代码
- 支持秒级和毫秒级时间戳
- 优雅降级：Redis故障时不阻断业务

## 🔍 验证配置生效

### **检查当前配置**：
```bash
# 查看配置文件
cat config/config.yaml | grep -A 10 "signature:"

# 测试API响应时间窗口
curl -X POST http://localhost:8081/api/test/timestamp/server-time
```

### **测试timestamp验证**：
```bash
# 测试30分钟前的timestamp（应该失败）
timestamp=$(($(date +%s) - 1900))  # 31分钟前
echo "测试过期timestamp: $timestamp"

# 测试当前timestamp（应该成功）
timestamp=$(date +%s)
echo "测试当前timestamp: $timestamp"
```

## 📊 预期效果

### **1. 失败率大幅下降**
- timestamp过期失败：预计下降 **90%+**
- 时钟同步失败：预计下降 **95%+**
- 网络延迟导致的失败：预计下降 **85%+**

### **2. 用户体验提升**
- 表单填写时间更宽松
- 网络环境适应性更强
- 高并发场景支持更好

### **3. 系统稳定性**
- 减少不必要的请求拒绝
- 提升整体可用性
- 更好的容错能力

## ⚠️ 注意事项

### **1. 资源使用**
- Redis内存使用可能增加20-30%
- 建议监控Redis内存使用情况

### **2. 安全考虑**
- 虽然时间窗口扩大，但防重放机制依然有效
- 建议定期审查异常高频的客户端

### **3. 监控建议**
- 监控timestamp验证失败率变化
- 关注高频请求警告日志
- 定期检查Redis键过期情况

## 🎉 总结

当前的配置设置已经**全面优化**：

1. **配置一致性**：所有层级的配置都已更新为30分钟
2. **系统兼容性**：通用API和统一网关都受益于优化
3. **用户友好性**：大幅提升高并发订单场景的成功率
4. **安全保障**：在放宽限制的同时保持安全机制有效

这些配置更改将在服务重启后立即生效，为用户提供更好的API调用体验。
