# Go-Kuaidi 快递管理系统数据库架构说明

## 概述
本系统采用PostgreSQL数据库，共包含96个表，按12个业务模块进行组织。每个模块都有明确的职责和数据管理范围。

## 数据库连接信息
- **数据库类型**: PostgreSQL
- **连接地址**: `*************************************************/go_kuaidi`
- **时区设置**: Asia/Shanghai (北京时间)

## 业务模块划分

### 1. 用户管理模块 (7个表)
管理系统用户的基本信息、余额、权限缓存等。

| 表名 | 用途 |
|------|------|
| `users` | 用户基础信息表 - 存储系统用户的基本信息，包括用户名、密码、邮箱等 |
| `user_balances` | 用户余额表 - 管理用户账户余额，支持多账户类型 |
| `user_roles` | 用户角色关联表 - 用户与角色的多对多关系映射 |
| `user_audit_logs` | 用户审计日志表 - 记录用户操作的审计轨迹 |
| `user_login_stats` | 用户登录统计表 - 记录用户登录次数、时间等统计信息 |
| `user_permission_cache` | 用户权限缓存表 - 缓存用户的权限信息，提高访问性能 |
| `user_callback_configs` | 用户回调配置表 - 存储用户个性化的回调通知配置 |

### 2. 权限管理模块 (8个表)
实现细粒度的权限控制系统，支持角色、权限、策略等。

| 表名 | 用途 |
|------|------|
| `roles` | 角色表 - 定义系统中的各种角色，如管理员、普通用户等 |
| `permissions` | 权限表 - 定义系统中的各种权限项 |
| `role_permissions` | 角色权限关联表 - 角色与权限的多对多关系映射 |
| `role_permission_policies` | 角色权限策略表 - 更细粒度的角色权限控制策略 |
| `permission_actions` | 权限动作表 - 定义具体的权限操作动作 |
| `permission_resources` | 权限资源表 - 定义权限控制的资源对象 |
| `permission_policies` | 权限策略表 - 复杂的权限控制策略配置 |
| `permission_audit_logs` | 权限审计日志表 - 记录权限变更的审计信息 |

### 3. 订单管理模块 (13个表)
核心业务模块，管理快递订单的完整生命周期。

| 表名 | 用途 |
|------|------|
| `order_records` | 订单记录表 - 存储快递订单的核心信息，包括寄收件人、费用等 |
| `order_status_history` | 订单状态历史表 - 记录订单状态变化的完整历史轨迹 |
| `order_status_transitions` | 订单状态转换表 - 定义订单状态之间的合法转换规则 |
| `order_status_configs` | 订单状态配置表 - 配置订单各种状态的属性和行为 |
| `order_attempts` | 订单尝试表 - 记录订单创建和处理的各种尝试信息 |
| `order_billing_details` | 订单计费详情表 - 存储订单的详细计费信息 |
| `order_billing_history` | 订单计费历史表 - 记录订单计费变更的历史记录 |
| `order_payments` | 订单支付表 - 记录订单的支付信息和状态 |
| `order_price_validations` | 订单价格验证表 - 记录价格验证的结果和详情 |
| `order_operation_permissions` | 订单操作权限表 - 控制不同用户对订单的操作权限 |
| `order_code_test` | 订单编码测试表 - 用于测试订单编码生成的临时表 |
| `order_records_backup_timezone_migration` | 订单记录备份表 - 时区迁移时的数据备份 |
| `platform_order_sequences` | 平台订单序列表 - 生成唯一的平台订单号序列 |

### 4. 快递公司管理模块 (8个表)
管理快递公司信息、配置和供应商映射关系。

| 表名 | 用途 |
|------|------|
| `express_companies` | 快递公司表 - 存储快递公司的基本信息和配置 |
| `express_company_configs` | 快递公司配置表 - 存储快递公司的详细配置参数 |
| `express_company_services` | 快递公司服务表 - 定义快递公司提供的各种服务类型 |
| `express_company_audit_logs` | 快递公司审计日志表 - 记录快递公司信息变更的审计轨迹 |
| `express_company_provider_mappings` | 快递公司供应商映射表 - 快递公司与供应商的映射关系 |
| `express_mapping_cache` | 快递映射缓存表 - 缓存快递公司映射信息，提高查询性能 |
| `express_providers` | 快递供应商表 - 存储快递供应商的信息和配置 |
| `express_providers_is_active_backup` | 快递供应商活跃状态备份表 - 备份供应商活跃状态信息 |

### 5. 计费管理模块 (13个表)
管理用户余额、交易记录、计费调整等财务相关功能。

| 表名 | 用途 |
|------|------|
| `balance_transactions` | 余额交易表 - 记录用户余额的所有交易记录 |
| `balance_freeze_records` | 余额冻结记录表 - 记录余额冻结和解冻的操作记录 |
| `balance_transactions_backup_20250630` | 余额交易备份表 - 2025年6月30日的余额交易数据备份 |
| `balance_audit_backup_20250610` | 余额审计备份表 - 2025年6月10日的余额审计数据备份 |
| `transaction_audit_backup_20250610` | 交易审计备份表 - 2025年6月10日的交易审计数据备份 |
| `billing_adjustment_audit` | 计费调整审计表 - 记录计费调整的审计信息 |
| `billing_status_configs` | 计费状态配置表 - 配置计费相关的状态和规则 |
| `deposits` | 充值记录表 - 记录用户的充值操作和状态 |
| `weight_fee_adjustments` | 重量费用调整表 - 记录重量相关的费用调整信息 |
| `weight_tier_price_cache` | 重量阶梯价格缓存表 - 缓存重量阶梯定价信息 |
| `weight_tier_query_logs` | 重量阶梯查询日志表 - 记录重量阶梯价格查询的日志 |
| `weight_cache_statistics` | 重量缓存统计表 - 统计重量缓存的使用情况 |
| `dual_weight_update_log` | 双重重量更新日志表 - 记录双重重量更新的操作日志 |

### 6. 管理员模块 (5个表)
专门的管理员功能模块，包括审计、性能监控等。

| 表名 | 用途 |
|------|------|
| `admin_balance_audit_logs` | 管理员余额审计日志表 - 记录管理员对余额操作的审计信息 |
| `admin_balance_audit_logs_enhanced` | 管理员余额审计日志增强表 - 增强版的管理员余额审计日志 |
| `admin_balance_performance_metrics` | 管理员余额性能指标表 - 记录管理员余额操作的性能指标 |
| `admin_config_change_notifications` | 管理员配置变更通知表 - 管理员配置变更的通知记录 |
| `admin_operation_limits` | 管理员操作限制表 - 限制管理员的某些操作权限 |

### 7. 回调处理模块 (3个表)
处理各种系统回调和通知功能。

| 表名 | 用途 |
|------|------|
| `callback_forward_records` | 回调转发记录表 - 记录回调信息的转发处理记录 |
| `callback_idempotency_protection` | 回调幂等性保护表 - 防止重复处理回调的幂等性保护 |
| `unified_callback_records` | 统一回调记录表 - 统一管理各种回调的记录信息 |

### 8. 工单管理模块 (9个表)
客户服务工单系统，支持工单创建、回复、状态跟踪等。

| 表名 | 用途 |
|------|------|
| `work_orders` | 工单表 - 存储客户提交的工单信息 |
| `work_order_attachments` | 工单附件表 - 存储工单相关的附件文件 |
| `work_order_replies` | 工单回复表 - 存储工单的回复和处理记录 |
| `work_order_status_history` | 工单状态历史表 - 记录工单状态变化的历史 |
| `work_order_status_mappings` | 工单状态映射表 - 定义工单状态的映射关系 |
| `work_order_type_mappings` | 工单类型映射表 - 定义工单类型的映射关系 |
| `work_order_type_mappings_backup` | 工单类型映射备份表 - 工单类型映射的备份数据 |
| `work_order_forward_records` | 工单转发记录表 - 记录工单的转发处理记录 |
| `work_orders_type_usage_backup` | 工单类型使用备份表 - 备份工单类型使用统计数据 |

### 9. 系统配置模块 (10个表)
系统级配置管理，包括配置历史、模板、验证规则等。

| 表名 | 用途 |
|------|------|
| `system_configs` | 系统配置表 - 存储系统级别的配置参数 |
| `system_config_history` | 系统配置历史表 - 记录系统配置变更的历史记录 |
| `config_backups` | 配置备份表 - 存储配置的备份信息 |
| `config_change_logs` | 配置变更日志表 - 记录配置变更的详细日志 |
| `config_templates` | 配置模板表 - 存储系统配置的模板信息 |
| `validation_rules` | 验证规则表 - 定义系统中的各种验证规则 |
| `error_messages` | 错误消息表 - 存储系统中的错误消息和国际化文本 |
| `menu_configurations` | 菜单配置表 - 存储系统菜单的配置信息 |
| `search_field_configs` | 搜索字段配置表 - 配置搜索功能的字段设置 |
| `sort_field_configs` | 排序字段配置表 - 配置排序功能的字段设置 |

### 10. 价格查询模块 (10个表)
价格查询和路线预热功能模块。

| 表名 | 用途 |
|------|------|
| `price_query_logs` | 价格查询日志表 - 记录价格查询的日志信息 |
| `warmup_routes` | 预热路线表 - 存储需要预热的快递路线信息 |
| `warmup_routes_backup` | 预热路线备份表 - 预热路线的备份数据 |
| `warmup_statistics` | 预热统计表 - 统计预热操作的相关数据 |
| `warmup_statistics_backup` | 预热统计备份表 - 预热统计的备份数据 |
| `warmup_tasks` | 预热任务表 - 存储预热任务的信息 |
| `warmup_tasks_backup` | 预热任务备份表 - 预热任务的备份数据 |
| `warmup_task_details` | 预热任务详情表 - 存储预热任务的详细信息 |
| `warmup_task_details_backup` | 预热任务详情备份表 - 预热任务详情的备份数据 |
| `route_definitions` | 路线定义表 - 定义快递路线的详细信息 |

### 11. 审计日志模块 (6个表)
全面的审计日志系统，记录系统各种操作。

| 表名 | 用途 |
|------|------|
| `audit_logs` | 审计日志表 - 记录系统的审计日志信息 |
| `api_call_logs` | API调用日志表 - 记录API调用的日志信息 |
| `api_calls` | API调用表 - 记录API调用的基本信息 |
| `idempotency_logs` | 幂等性日志表 - 记录幂等性控制的日志信息 |
| `architecture_cleanup_log` | 架构清理日志表 - 记录架构清理操作的日志 |
| `timezone_migration_log` | 时区迁移日志表 - 记录时区迁移操作的日志 |

### 12. 第三方集成模块 (2个表)
管理第三方应用和OAuth集成。

| 表名 | 用途 |
|------|------|
| `applications` | 应用程序表 - 存储第三方应用程序的信息 |
| `oauth_clients` | OAuth客户端表 - 存储OAuth认证客户端的信息 |

### 13. 数据库管理模块 (2个表)
数据库自身的管理和维护功能。

| 表名 | 用途 |
|------|------|
| `schema_migrations` | 数据库迁移表 - 记录数据库结构变更的迁移信息 |
| `view_backup_v_express_mapping_optimized` | 视图备份表 - 备份快递映射优化视图的数据 |

## 核心业务流程

### 1. 订单处理流程
```
用户下单 → order_records → 价格验证 → order_price_validations → 
扣费处理 → balance_transactions → 状态更新 → order_status_history
```

### 2. 权限控制流程
```
用户登录 → users → 角色查询 → user_roles → 权限检查 → 
permissions → 操作授权 → permission_audit_logs
```

### 3. 计费处理流程
```
订单创建 → 费用计算 → weight_tier_price_cache → 余额扣减 → 
balance_transactions → 计费审计 → billing_adjustment_audit
```

## 数据备份策略

系统包含多个备份表，用于关键数据的备份和恢复：
- 余额交易备份 (按日期)
- 预热任务备份 (定期备份)
- 工单类型映射备份 (配置备份)
- 时区迁移备份 (迁移时备份)

## 性能优化

### 缓存表
- `express_mapping_cache` - 快递公司映射缓存
- `weight_tier_price_cache` - 重量价格缓存
- `user_permission_cache` - 用户权限缓存

### 统计表
- `weight_cache_statistics` - 重量缓存统计
- `warmup_statistics` - 预热统计
- `user_login_stats` - 用户登录统计

## 总结

本系统采用微服务架构思想，数据库设计遵循模块化原则，各个模块职责清晰，支持高并发和高可用性。通过完善的审计日志、权限控制和数据备份机制，确保系统的安全性和可靠性。

**统计信息**：
- 总表数：96个
- 业务模块：12个
- 核心业务表：13个 (订单管理)
- 审计日志表：6个
- 缓存优化表：3个
- 备份表：8个 