# 客户订单号功能实施文档

## 功能概述

本文档描述了快递系统中新增的 `customer_order_no`（客户订单号）功能的完整实施方案。该功能允许客户在创建订单时传入自定义的订单号，并确保在所有相关的API响应和回调通知中都包含并返回该字段。

## 核心特性

### ✅ 已完成的功能

1. **数据库层面**
   - ✅ `order_records` 表已包含 `customer_order_no` 字段（VARCHAR(64) NOT NULL）
   - ✅ 已建立 B-tree 和 Hash 索引用于高效查询
   - ✅ 字段约束完整，确保数据完整性

2. **API接口层面**
   - ✅ 前端 `SimpleOrderRequest` 模型已添加 `customer_order_no` 字段
   - ✅ 后端 `SimpleOrderRequest` 结构体已添加 `customer_order_no` 字段
   - ✅ 订单创建API支持接收和处理 `customer_order_no` 参数
   - ✅ 所有订单查询API响应都包含 `customer_order_no` 字段

3. **回调通知层面**
   - ✅ `UnifiedCallbackData` 结构体已添加 `customer_order_no` 字段
   - ✅ 统一回调构建器正确设置 `customer_order_no` 字段
   - ✅ 工单回调转发器支持查询和包含 `customer_order_no` 字段

4. **数据验证和错误处理**
   - ✅ 客户订单号格式验证（长度1-64字符，仅允许字母、数字、下划线、连字符）
   - ✅ 同一用户客户订单号唯一性检查
   - ✅ 完整的错误处理和友好的错误消息

## 使用方法

### 1. 创建订单时指定客户订单号

**前端请求示例：**
```typescript
const orderRequest: SimpleOrderRequest = {
  customer_order_no: "MY_ORDER_20250105_001", // 🔥 新增：客户自定义订单号
  order_code: "unified_order_code",
  sender_name: "张三",
  sender_mobile: "***********",
  // ... 其他字段
}
```

**后端API响应：**
```json
{
  "success": true,
  "code": 200,
  "message": "订单创建成功",
  "customer_order_no": "MY_ORDER_20250105_001", // 🔥 返回客户订单号
  "order_no": "GK20250105123456789",
  "waybill_no": "SF1234567890123",
  "express_code": "SF",
  "express_name": "顺丰速运",
  "price": "12.50",
  "delivery_time": "2025-01-06 18:00:00"
}
```

### 2. 查询订单时获取客户订单号

**订单详情查询响应：**
```json
{
  "success": true,
  "code": 200,
  "message": "查询成功",
  "data": {
    "customer_order_no": "MY_ORDER_20250105_001", // 🔥 包含客户订单号
    "order_no": "GK20250105123456789",
    "tracking_no": "SF1234567890123",
    // ... 其他字段
  }
}
```

### 3. 回调通知中的客户订单号

**统一回调数据格式：**
```json
{
  "customer_order_no": "MY_ORDER_20250105_001", // 🔥 包含客户订单号
  "order_no": "GK20250105123456789",
  "tracking_no": "SF1234567890123",
  "timestamp": "2025-01-05T10:30:00Z",
  "event_type": 1,
  "data": {
    // 事件具体数据
  }
}
```

## 验证规则

### 客户订单号格式要求

1. **长度限制**：1-64个字符
2. **字符限制**：只允许字母（a-z, A-Z）、数字（0-9）、下划线（_）、连字符（-）
3. **唯一性**：同一用户不能使用相同的客户订单号创建多个订单
4. **可选性**：客户订单号是可选的，如果不提供则系统自动生成

### 有效的客户订单号示例

```
✅ "ORDER_123456"
✅ "ABC123def456"
✅ "ORDER-123_456"
✅ "my-order-2025-01-05"
✅ "USER123_ORDER456"
```

### 无效的客户订单号示例

```
❌ "ORDER@123#456"  // 包含特殊字符
❌ "ORDER 123456"   // 包含空格
❌ "订单123456"     // 包含中文
❌ "THIS_IS_A_VERY_LONG_CUSTOMER_ORDER_NUMBER_THAT_EXCEEDS_64_CHARACTERS_LIMIT" // 超长
```

## 向后兼容性

1. **现有订单**：对于没有传入 `customer_order_no` 的旧订单，系统会自动生成客户订单号
2. **API兼容**：所有现有的API调用都保持兼容，`customer_order_no` 字段为可选
3. **数据库兼容**：现有数据不受影响，新字段有默认值处理

## 技术实现细节

### 数据库结构
```sql
-- order_records 表中的 customer_order_no 字段
customer_order_no character varying(64) NOT NULL

-- 索引
CREATE INDEX idx_order_records_customer_order_no ON order_records USING btree (customer_order_no);
CREATE INDEX idx_order_records_customer_order_no_hash ON order_records USING hash (customer_order_no);
```

### 核心代码文件

1. **前端模型**：`user-frontend/src/api/model/kuaidiModel.ts`
2. **后端处理器**：`api/handler/order_handler.go`
3. **订单服务**：`internal/service/order_service.go`
4. **数据模型**：`internal/model/order.go`
5. **回调处理**：`internal/model/callback.go`
6. **测试文件**：`test/customer_order_no_test.go`

## 测试验证

系统包含完整的测试用例验证功能：

```bash
# 运行客户订单号功能测试
cd test
go test -v customer_order_no_test.go
```

测试覆盖：
- ✅ 客户订单号格式验证
- ✅ 长度和字符限制检查
- ✅ 错误消息验证
- 🔄 订单创建集成测试（需要完整环境）
- 🔄 回调通知测试（需要回调环境）

## 部署说明

1. **数据库**：无需额外的数据库迁移，字段已存在
2. **代码部署**：正常部署新版本代码即可
3. **配置**：无需额外配置，功能开箱即用
4. **监控**：建议监控客户订单号的使用情况和唯一性冲突

## 注意事项

1. **性能影响**：客户订单号查询已优化索引，对性能影响最小
2. **存储空间**：每个订单额外占用最多64字节存储空间
3. **并发安全**：系统已处理并发创建订单时的唯一性检查
4. **错误处理**：提供友好的错误消息，便于客户端处理

## 🔥 新增：统一网关客户订单号支持

### 统一网关API更新

统一网关的创建订单接口现已完全支持 `customer_order_no` 参数：

#### ✅ 请求参数更新
```json
{
  "apiMethod": "CREATE_ORDER",
  "businessParams": {
    "user_id": "user123",
    "customer_order_no": "MY_ORDER_20250705_001", // 🔥 新增：客户自定义订单号
    "order_code": "unified_order_code_from_price_query",
    "sender_name": "张三",
    "sender_mobile": "***********",
    // ... 其他参数
  },
  "clientType": "api",
  "username": "api_user",
  "timestamp": "1751692267155",
  "sign": "signature_hash"
}
```

#### ✅ 响应格式更新
```json
{
  "code": 200,
  "msg": "订单创建成功",
  "success": true,
  "data": {
    "customer_order_no": "MY_ORDER_20250705_001", // 🔥 返回客户订单号
    "order_no": "YD250705131107037536",
    "tracking_no": "312816791325357",
    "express_code": "YD",
    "express_name": "韵达速递",
    "price": 4.49,
    "delivery_time": "2025-07-06 18:00:00"
  }
}
```

#### ✅ 特性说明
1. **向后兼容**：`customer_order_no` 参数是可选的，不传入时系统自动生成
2. **唯一性检查**：系统会验证同一用户的客户订单号唯一性
3. **格式验证**：支持1-64字符，仅允许字母、数字、下划线、连字符
4. **完整回调支持**：所有回调通知都会包含客户订单号

## 🔥 新增：所有回调类型的客户订单号支持

### 回调数据结构完整性

所有回调相关的数据结构都已完整支持 `customer_order_no` 字段：

#### ✅ 核心回调数据结构
- `UnifiedCallbackData` - 统一回调数据
- `StandardizedCallbackData` - 标准化回调数据
- `CallbackData` - 基础回调数据
- `ParsedCallbackData` - 解析后的回调数据
- `UserCallbackRequest` - 用户回调请求
- `UnifiedCallbackRecord` - 统一回调记录

#### ✅ 工单回调数据结构
- `UnifiedWorkOrderCallbackData` - 统一工单回调数据

#### ✅ 供应商适配器修复
1. **快递100适配器**：正确处理平台订单号和客户订单号的映射
2. **易达适配器**：正确使用 `thirdNo` 作为客户订单号
3. **云通适配器**：正确使用 `OrderCode` 作为客户订单号

#### ✅ 智能订单查找
统一回调服务现在支持多策略订单查找：
1. 优先通过客户订单号查找
2. 通过平台订单号查找
3. 通过运单号查找

### 回调通知示例

**统一回调格式（包含客户订单号）：**
```json
{
  "customer_order_no": "MY_ORDER_20250105_001",
  "order_no": "GK20250105123456789",
  "tracking_no": "SF1234567890123",
  "timestamp": "2025-01-05T10:30:00Z",
  "event_type": 1,
  "data": {
    "status": {
      "code": "picked_up",
      "name": "已揽收"
    },
    "courier": {
      "name": "张师傅",
      "phone": "***********"
    },
    "update_time": "2025-01-05T10:30:00Z"
  }
}
```

**工单回调格式（包含客户订单号）：**
```json
{
  "event_type": "workorder.replied",
  "event_time": **********,
  "version": "3.0",
  "work_order_id": "WO20250105001",
  "provider_work_order_id": "PROVIDER_WO_123",
  "order_no": "GK20250105123456789",
  "customer_order_no": "MY_ORDER_20250105_001",
  "tracking_no": "SF1234567890123",
  "status": 1,
  "status_name": "已回复",
  "content": "您的包裹正在派送中",
  "committer": "客服小王"
}
```

## 后续优化建议

1. **批量查询**：支持通过客户订单号批量查询订单
2. **模糊搜索**：在管理后台支持客户订单号的模糊搜索
3. **统计分析**：添加客户订单号使用情况的统计分析
4. **导出功能**：在订单导出中包含客户订单号字段
5. **回调监控**：添加客户订单号在回调中的传递监控
