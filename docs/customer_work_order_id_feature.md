# 用户自定义工单ID功能

## 概述

统一网关的工单创建功能现已支持用户自定义工单ID，类似于订单系统的 `customer_order_no` 功能。

## 功能特性

### ✅ 支持的功能
1. **用户自定义工单ID**：用户可以在创建工单时指定自定义ID
2. **自动生成**：如果用户不提供自定义ID，系统自动生成
3. **唯一性验证**：同一用户下的自定义工单ID必须唯一
4. **格式验证**：支持1-64字符，仅允许字母、数字、下划线、连字符
5. **向后兼容**：完全向后兼容，不影响现有功能

### 🔧 技术实现
- **数据库字段**：`work_orders.customer_work_order_id VARCHAR(100)`
- **唯一索引**：`idx_work_orders_user_customer_id` 确保同一用户下唯一性
- **查询索引**：`idx_work_orders_customer_work_order_id` 优化查询性能

## API 使用说明

### 创建工单请求

#### 请求参数
```json
{
  "apiMethod": "CREATE_WORK_ORDER",
  "businessParams": {
    "work_order_type": 1,
    "content": "催促快递员取件",
    "customer_work_order_id": "MY_WORK_ORDER_20250127_001", // 🔥 新增：用户自定义工单ID（可选）
    "order_no": "YD250705131107037536",
    "tracking_no": "312816791325357",
    "feedback_weight": 2.5,
    "goods_value": 100.00,
    "overweight_amount": 5.00,
    "attachment_urls": ["https://example.com/attachment1.jpg"]
  },
  "clientType": "api",
  "username": "api_user",
  "timestamp": "1751692267155",
  "sign": "signature_hash"
}
```

#### 响应格式
```json
{
  "code": 200,
  "msg": "工单创建成功",
  "success": true,
  "data": {
    "work_order_id": "550e8400-e29b-41d4-a716-446655440000",
    "customer_work_order_id": "MY_WORK_ORDER_20250127_001", // 🔥 新增：返回用户自定义工单ID
    "work_order_type": 1,
    "title": "催取件 - YD250705131107037536",
    "status": 1,
    "provider": "yida",
    "request_id": "req_1751692267_abc123"
  }
}
```

### 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `customer_work_order_id` | string | 否 | 用户自定义工单ID，1-64字符，仅允许字母、数字、下划线、连字符 |

### 验证规则

1. **长度限制**：1-64个字符
2. **字符限制**：仅允许字母、数字、下划线、连字符
3. **唯一性**：同一用户下不能重复
4. **格式验证**：正则表达式 `^[a-zA-Z0-9_-]+$`

### 错误处理

#### 格式错误
```json
{
  "code": 400,
  "msg": "Parameter validation failed",
  "success": false,
  "data": "用户自定义工单ID格式错误: 用户自定义工单ID只能包含字母、数字、下划线和连字符"
}
```

#### 重复错误
```json
{
  "code": 400,
  "msg": "Parameter validation failed",
  "success": false,
  "data": "用户自定义工单ID验证失败: 用户自定义工单ID 'MY_WORK_ORDER_20250127_001' 已存在，请使用其他ID"
}
```

## 数据库变更

### 新增字段
```sql
ALTER TABLE work_orders ADD COLUMN customer_work_order_id VARCHAR(100);
```

### 新增索引
```sql
-- 唯一索引（确保同一用户下唯一性）
CREATE UNIQUE INDEX idx_work_orders_user_customer_id 
ON work_orders(user_id, customer_work_order_id) 
WHERE customer_work_order_id IS NOT NULL;

-- 普通索引（优化查询）
CREATE INDEX idx_work_orders_customer_work_order_id 
ON work_orders(customer_work_order_id);
```

## 代码变更

### 1. 模型层变更
- `model.WorkOrder` 新增 `CustomerWorkOrderID *string` 字段
- `model.CreateWorkOrderRequest` 新增 `CustomerWorkOrderID *string` 字段

### 2. 仓储层变更
- `WorkOrderRepository` 接口新增 `ExistsByCustomerWorkOrderID` 方法
- `PostgresWorkOrderRepository` 实现唯一性检查逻辑

### 3. 服务层变更
- `DefaultWorkOrderService.CreateWorkOrder` 支持用户自定义工单ID
- 新增 `validateCustomerWorkOrderIDUniqueness` 方法

### 4. 网关层变更
- `CreateWorkOrderBusinessParams` 新增 `CustomerWorkOrderID` 字段
- `CreateWorkOrderResponseData` 新增 `CustomerWorkOrderID` 字段
- 新增 `validateCustomerWorkOrderID` 验证方法

## 部署步骤

### 1. 执行数据库迁移
```bash
./scripts/apply_customer_work_order_id_migration.sh
```

### 2. 重启应用服务
```bash
# 重启API服务
docker-compose restart api

# 或重新构建部署
./build.sh
```

### 3. 验证功能
```bash
# 测试创建工单（使用自定义ID）
curl -X POST http://localhost:8080/api/unified \
  -H "Content-Type: application/json" \
  -d '{
    "apiMethod": "CREATE_WORK_ORDER",
    "businessParams": {
      "work_order_type": 1,
      "content": "测试自定义工单ID",
      "customer_work_order_id": "TEST_WO_001",
      "order_no": "YD250705131107037536"
    },
    "clientType": "api",
    "username": "test_user",
    "timestamp": "1751692267155",
    "sign": "test_signature"
  }'
```

## 兼容性说明

### 向后兼容
- ✅ 现有API调用无需修改
- ✅ 不提供 `customer_work_order_id` 时系统自动生成
- ✅ 现有数据库记录不受影响

### 自动生成规则
当用户不提供自定义工单ID时，系统自动生成格式为：
```
wo{timestamp}
```
例如：`wo1751692267`

## 注意事项

1. **唯一性约束**：同一用户下的自定义工单ID必须唯一
2. **格式限制**：严格限制字符类型和长度
3. **性能考虑**：已添加适当索引优化查询性能
4. **错误处理**：提供详细的错误信息帮助用户理解问题

## 测试用例

### 正常场景
1. 提供有效的自定义工单ID
2. 不提供自定义工单ID（系统自动生成）
3. 不同用户使用相同的自定义工单ID

### 异常场景
1. 自定义工单ID格式错误
2. 同一用户重复使用相同的自定义工单ID
3. 自定义工单ID长度超出限制

## 更新日志

- **2025-01-27**：实现用户自定义工单ID功能
  - 添加数据库字段和索引
  - 实现验证逻辑
  - 更新API接口
  - 添加完整文档 