# 统一网关Nonce处理机制说明

## 🔍 当前机制分析

### **统一网关的特殊性**
统一网关 (`/api/gateway/execute`) 与传统API在nonce处理上有重要区别：

1. **请求格式**：
   ```json
   {
     "clientId": "your-client-id",
     "timestamp": "1704715845000",  // 关键：这里是timestamp，不是nonce
     "apiMethod": "QUERY_PRICE",
     "businessParams": {...},
     "sign": "signature"
   }
   ```

2. **内部处理**：
   - 系统内部：`nonce = timestamp`
   - 防重放机制：基于timestamp的唯一性
   - Redis键格式：`nonce:clientId:timestamp`

## 🚨 问题根源

### **用户遇到的错误**
```json
{
  "code": 400,
  "error": "Duplicate request detected",
  "message": "Nonce already used",
  "success": false
}
```

### **原因分析**
1. **秒级时间戳重复**：用户在同一秒内发送多个请求
2. **系统使用timestamp作为nonce**：导致nonce重复
3. **Redis防重放检查**：检测到相同的nonce（timestamp）

## ✅ 解决方案

### **1. 用户端修改（已通知）**
用户需要将timestamp改为**毫秒级**：

```javascript
// ❌ 错误：秒级时间戳（容易重复）
const timestamp = Math.floor(Date.now() / 1000).toString(); // 1704715845

// ✅ 正确：毫秒级时间戳（不易重复）
const timestamp = Date.now().toString(); // 1704715845123
```

### **2. 服务端优化（已实现）**
我们已经实现了完整的统一网关nonce验证机制：

#### **企业级验证逻辑**
```go
// 🚀 统一网关：使用完整的nonce验证（基于timestamp + Redis）
if c.Request.URL.Path == "/api/gateway/execute" {
    // 统一网关使用timestamp作为nonce，需要完整的Redis防重放检查
    gatewayNonceManager := security.NewGatewayNonceManager(redisClient, logger)
    nonceResult, err := gatewayNonceManager.ValidateGatewayNonce(ctx, signatureParams.ClientID, signatureParams.Nonce)
    if err != nil {
        // 处理验证错误
        return
    }

    if !nonceResult.Valid {
        // 处理验证失败，提供详细错误信息
        return
    }

    // 标记timestamp已使用
    gatewayNonceManager.MarkTimestampUsed(ctx, signatureParams.ClientID, signatureParams.Nonce)
}
```

#### **支持秒级和毫秒级时间戳**
```go
// 判断是秒级还是毫秒级时间戳
if timestampInt > 1e12 {
    // 毫秒级时间戳（推荐）
    timestampTime = time.UnixMilli(timestampInt)
} else {
    // 秒级时间戳（兼容）
    timestampTime = time.Unix(timestampInt, 0)
}
```

## 📋 用户迁移指南

### **立即修改（必需）**
```javascript
// 修改前
function generateTimestamp() {
    return Math.floor(Date.now() / 1000).toString(); // 秒级
}

// 修改后
function generateTimestamp() {
    return Date.now().toString(); // 毫秒级
}
```

### **API调用示例**
```javascript
async function callUnifiedGateway(apiMethod, businessParams) {
    const timestamp = Date.now().toString(); // 🔥 毫秒级时间戳
    
    const requestData = {
        clientId: 'your-client-id',
        timestamp: timestamp,
        apiMethod: apiMethod,
        businessParams: businessParams,
        sign: generateSignature(timestamp, businessParams)
    };
    
    const response = await fetch('/api/gateway/execute', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestData)
    });
    
    return response.json();
}
```

### **重试机制（推荐）**
```javascript
async function callGatewayWithRetry(apiMethod, businessParams, maxRetries = 3) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            const timestamp = Date.now().toString(); // 每次重试生成新时间戳
            
            const result = await callUnifiedGateway(apiMethod, businessParams);
            
            if (!result.success && result.error === 'Duplicate request detected') {
                if (attempt < maxRetries) {
                    console.warn(`时间戳重复，第${attempt}次重试...`);
                    await new Promise(resolve => setTimeout(resolve, 10)); // 10ms延迟
                    continue;
                }
            }
            
            return result;
        } catch (error) {
            if (attempt === maxRetries) throw error;
            await new Promise(resolve => setTimeout(resolve, 100 * attempt));
        }
    }
}
```

## 🔧 技术细节

### **企业级nonce管理器特性**
1. **完整的Redis防重放**：使用Redis SET NX确保原子性
2. **智能时间戳解析**：自动识别秒级和毫秒级时间戳
3. **北京时间支持**：所有时间比较使用北京时间
4. **详细错误分类**：提供精确的错误码和错误信息
5. **统计监控**：实时统计nonce使用情况
6. **自动清理**：定期清理过期的timestamp

### **时间戳验证规则**
1. **格式验证**：必须是数字字符串
2. **范围验证**：不能超过±24小时
3. **过期验证**：5分钟有效期
4. **时钟偏差**：允许5秒未来时间
5. **重复检查**：Redis原子性检查防止重复使用

### **兼容性保证**
- ✅ **向后兼容**：同时支持秒级和毫秒级时间戳
- ✅ **平滑迁移**：用户可以逐步迁移到毫秒级
- ✅ **错误提示**：明确的错误信息指导用户修复
- ✅ **接口抽象**：使用接口设计，便于测试和扩展

### **性能优化**
- ✅ **完整验证**：统一网关使用完整的Redis防重放检查
- ✅ **原子操作**：使用Redis SET NX确保并发安全
- ✅ **批量清理**：定期清理过期timestamp，防止内存泄漏
- ✅ **接口抽象**：使用接口避免直接依赖，提高可测试性

## 📊 监控和调试

### **日志记录**
系统会记录详细的验证日志：
```
[DEBUG] 统一网关nonce验证通过 client_id=test-client timestamp=1704715845123
[WARN] 统一网关timestamp格式无效 client_id=test-client timestamp=invalid
[WARN] 统一网关timestamp已过期 client_id=test-client timestamp=1704715845
```

### **错误分类**
| 错误类型 | 错误信息 | 用户操作 |
|---------|---------|---------|
| `timestamp格式无效` | timestamp必须是数字 | 检查timestamp生成逻辑 |
| `timestamp已过期` | 超过5分钟有效期 | 使用当前时间生成timestamp |
| `timestamp来自未来` | 时钟可能不同步 | 检查系统时间同步 |
| `请求重复` | 使用新的timestamp | 确保timestamp唯一性 |

## 🎯 最佳实践

### **1. 时间戳生成**
```javascript
// ✅ 推荐：毫秒级时间戳
const timestamp = Date.now().toString();

// ✅ 备选：带随机后缀（极高并发场景）
const timestamp = Date.now().toString() + Math.random().toString(36).substr(2, 3);
```

### **2. 并发处理**
```javascript
// 高并发场景：确保时间戳唯一性
let lastTimestamp = 0;
function generateUniqueTimestamp() {
    let timestamp = Date.now();
    if (timestamp <= lastTimestamp) {
        timestamp = lastTimestamp + 1;
    }
    lastTimestamp = timestamp;
    return timestamp.toString();
}
```

### **3. 错误处理**
```javascript
try {
    const result = await callUnifiedGateway(apiMethod, params);
    if (!result.success) {
        if (result.error === 'Duplicate request detected') {
            // 时间戳重复，重新生成
            console.warn('时间戳重复，请重试');
        }
        throw new Error(result.message);
    }
    return result;
} catch (error) {
    console.error('API调用失败:', error.message);
    throw error;
}
```

## 🔄 迁移时间表

### **阶段1：立即执行（已完成）**
- ✅ 服务端支持毫秒级时间戳
- ✅ 向后兼容秒级时间戳
- ✅ 优化错误提示

### **阶段2：用户迁移（进行中）**
- 🔄 通知用户修改为毫秒级时间戳
- 🔄 用户测试和验证
- 🔄 监控错误率下降

### **阶段3：完全迁移（未来）**
- ⏳ 所有用户完成迁移
- ⏳ 考虑移除秒级时间戳支持
- ⏳ 进一步优化性能

## 🧪 测试验证

### **自动化测试脚本**
我们提供了完整的测试脚本来验证nonce处理机制：

```bash
# 运行统一网关nonce测试
./scripts/test-gateway-nonce.sh
```

### **测试覆盖范围**
1. **毫秒级时间戳测试**：验证毫秒级时间戳的正常处理
2. **秒级时间戳兼容性**：确保向后兼容
3. **防重放机制测试**：验证重复timestamp的拒绝
4. **过期时间戳检测**：验证过期timestamp的处理
5. **无效格式检测**：验证错误格式的处理
6. **并发请求测试**：验证高并发场景的处理

### **手动测试示例**
```bash
# 测试毫秒级时间戳
curl -X POST http://localhost:8081/api/gateway/execute \
  -H "Content-Type: application/json" \
  -d '{
    "clientId": "test-client",
    "timestamp": "'$(date +%s%3N)'",
    "apiMethod": "QUERY_PRICE",
    "businessParams": {"from":"北京","to":"上海","weight":1},
    "sign": "test_signature"
  }'
```

## 📞 技术支持

如果用户在迁移过程中遇到问题：

1. **检查时间戳格式**：确保使用毫秒级时间戳
2. **验证API调用**：使用我们提供的示例代码
3. **查看错误日志**：根据错误信息进行相应调整
4. **运行测试脚本**：使用 `./scripts/test-gateway-nonce.sh` 验证
5. **联系技术支持**：提供具体的错误信息和timestamp值

---

**总结**：统一网关的nonce机制已经完全重构，使用企业级的Redis防重放检查，支持毫秒级时间戳，提供完整的错误处理和监控功能。用户只需要简单修改timestamp生成逻辑即可解决重复请求问题。
