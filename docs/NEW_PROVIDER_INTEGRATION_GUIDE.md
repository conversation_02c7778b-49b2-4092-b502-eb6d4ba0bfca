# 新供应商对接指南

## 概述

本指南详细说明如何在统一网关系统中集成新的快递供应商。系统采用标准化的适配器模式，确保所有供应商都能无缝集成到统一的API接口中。

## 目录

1. [系统架构概述](#系统架构概述)
2. [开发环境准备](#开发环境准备)
3. [供应商适配器开发](#供应商适配器开发)
4. [配置管理集成](#配置管理集成)
5. [回调处理集成](#回调处理集成)
6. [测试验证](#测试验证)
7. [部署上线](#部署上线)
8. [监控运维](#监控运维)

## 系统架构概述

### 核心组件

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   统一网关API   │────│  供应商管理器   │────│   适配器工厂    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   价格查询服务  │    │   订单管理服务  │    │   回调处理服务  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                                 ▼
                    ┌─────────────────┐
                    │  供应商适配器   │
                    │  (YourAdapter)  │
                    └─────────────────┘
```

### 标准接口

所有供应商适配器必须实现 `ProviderAdapter` 接口：

```go
type ProviderAdapter interface {
    Name() string
    QueryPrice(ctx context.Context, req *model.PriceRequest) ([]model.StandardizedPrice, error)
    CreateOrder(ctx context.Context, req *model.OrderRequest) (*model.OrderResult, error)
    CancelOrder(ctx context.Context, taskId string, orderNo string, reason string) error
    QueryOrder(ctx context.Context, orderNo string, trackingNo string) (*model.OrderInfo, error)
    QueryTrack(ctx context.Context, trackingNo, expressType, phone, pollToken string, from, to string) (*model.TrackInfo, error)
}
```

## 开发环境准备

### 1. 代码结构

为新供应商创建以下文件：

```
internal/adapter/
├── your_provider.go              # 主适配器实现
├── your_provider_types.go        # 数据结构定义
├── your_provider_callback.go     # 回调处理（如需要）
└── your_provider_test.go         # 单元测试

internal/service/callback/
└── your_provider_adapter.go      # 回调适配器

docs/
└── your_provider_integration.md  # 集成文档
```

### 2. 依赖包导入

```go
import (
    "context"
    "encoding/json"
    "fmt"
    "net/http"
    "time"
    
    "github.com/your-org/go-kuaidi/internal/express"
    "github.com/your-org/go-kuaidi/internal/model"
    "github.com/your-org/go-kuaidi/internal/util"
    "go.uber.org/zap"
)
```

## 供应商适配器开发

### 1. 配置结构定义

在 `your_provider_types.go` 中定义配置结构：

```go
// YourProviderConfig 供应商配置
type YourProviderConfig struct {
    AppKey      string `json:"app_key"`      // API密钥
    AppSecret   string `json:"app_secret"`   // API密钥
    BaseURL     string `json:"base_url"`     // API基础URL
    Environment string `json:"environment"`  // 环境：sandbox/production
    Timeout     int    `json:"timeout"`      // 超时时间（秒）
}

// YourProviderAdapter 供应商适配器
type YourProviderAdapter struct {
    config             YourProviderConfig
    client             *http.Client
    logger             *zap.Logger
    mappingService     express.ExpressMappingService
    expressCompanyRepo express.ExpressCompanyRepository
}
```

### 2. 适配器构造函数

```go
// NewYourProviderAdapter 创建供应商适配器
func NewYourProviderAdapter(config YourProviderConfig, expressCompanyRepo express.ExpressCompanyRepository) *YourProviderAdapter {
    logger, _ := zap.NewProduction()
    
    // 优化HTTP客户端配置
    transport := &http.Transport{
        MaxIdleConns:          200,
        MaxIdleConnsPerHost:   50,
        IdleConnTimeout:       60 * time.Second,
        DisableCompression:    false,
        ForceAttemptHTTP2:     true,
        ResponseHeaderTimeout: 8 * time.Second,
        ExpectContinueTimeout: 1 * time.Second,
    }
    
    client := &http.Client{
        Transport: transport,
        Timeout:   time.Duration(config.Timeout) * time.Second,
    }
    
    return &YourProviderAdapter{
        config:             config,
        client:             client,
        logger:             logger,
        expressCompanyRepo: expressCompanyRepo,
    }
}

// Name 返回供应商名称
func (a *YourProviderAdapter) Name() string {
    return "your_provider"
}

// SetMappingService 设置快递映射服务
func (a *YourProviderAdapter) SetMappingService(service express.ExpressMappingService) {
    a.mappingService = service
}
```

### 3. 价格查询实现

```go
// QueryPrice 查询价格 - 实现ProviderAdapter接口
func (a *YourProviderAdapter) QueryPrice(ctx context.Context, req *model.PriceRequest) ([]model.StandardizedPrice, error) {
    a.logger.Info("开始查询价格",
        zap.String("provider", "your_provider"),
        zap.String("from", req.Sender.Province),
        zap.String("to", req.Receiver.Province),
        zap.Float64("weight", req.Package.Weight))
    
    // 1. 参数验证
    if err := a.validatePriceRequest(req); err != nil {
        return nil, fmt.Errorf("参数验证失败: %w", err)
    }
    
    // 2. 转换为供应商API格式
    apiReq := a.convertToPriceAPIRequest(req)
    
    // 3. 调用供应商API
    apiResp, err := a.callPriceAPI(ctx, apiReq)
    if err != nil {
        return nil, fmt.Errorf("调用价格查询API失败: %w", err)
    }
    
    // 4. 转换为标准格式
    standardPrices := a.convertToStandardizedPrices(apiResp, req)
    
    a.logger.Info("价格查询完成",
        zap.String("provider", "your_provider"),
        zap.Int("price_count", len(standardPrices)))
    
    return standardPrices, nil
}

// validatePriceRequest 验证价格查询请求
func (a *YourProviderAdapter) validatePriceRequest(req *model.PriceRequest) error {
    if req.Sender.Province == "" || req.Receiver.Province == "" {
        return fmt.Errorf("寄件地址和收件地址不能为空")
    }
    if req.Package.Weight <= 0 {
        return fmt.Errorf("重量必须大于0")
    }
    return nil
}

// convertToPriceAPIRequest 转换为供应商API请求格式
func (a *YourProviderAdapter) convertToPriceAPIRequest(req *model.PriceRequest) *YourProviderPriceRequest {
    return &YourProviderPriceRequest{
        FromProvince: req.Sender.Province,
        FromCity:     req.Sender.City,
        ToProvince:   req.Receiver.Province,
        ToCity:       req.Receiver.City,
        Weight:       req.Package.Weight,
        // 根据供应商API要求添加其他字段
    }
}

// convertToStandardizedPrices 转换为标准化价格格式
func (a *YourProviderAdapter) convertToStandardizedPrices(apiResp *YourProviderPriceResponse, req *model.PriceRequest) []model.StandardizedPrice {
    var prices []model.StandardizedPrice

    for _, item := range apiResp.PriceList {
        // 计算计费重量（考虑体积重量）
        chargedWeight := a.calculateChargedWeight(req, item.ExpressCode)

        // 生成订单代码用于下单验证
        orderCode := fmt.Sprintf("YOUR_PROVIDER_%s_%d", item.ExpressCode, time.Now().Unix())

        price := model.StandardizedPrice{
            Provider:        "your_provider",
            ExpressCode:     item.ExpressCode,
            ExpressName:     item.ExpressName,
            Price:           item.Price,
            EstimatedTime:   item.EstimatedTime,
            ChargedWeight:   chargedWeight,
            OrderCode:       orderCode,
            SupportsPickup:  item.SupportsPickup,
            PickupTimeInfo:  a.parsePickupTimeInfo(item),
        }

        prices = append(prices, price)
    }

    return prices
}

### 4. 订单创建实现

```go
// CreateOrder 创建订单 - 实现ProviderAdapter接口
func (a *YourProviderAdapter) CreateOrder(ctx context.Context, req *model.OrderRequest) (*model.OrderResult, error) {
    a.logger.Info("开始创建订单",
        zap.String("provider", "your_provider"),
        zap.String("customer_order_no", req.CustomerOrderNo),
        zap.String("express_type", req.ExpressType))

    // 1. 参数验证
    if err := a.validateOrderRequest(req); err != nil {
        return nil, fmt.Errorf("订单参数验证失败: %w", err)
    }

    // 2. 转换为供应商API格式
    apiReq := a.convertToOrderAPIRequest(req)

    // 3. 调用供应商API
    apiResp, err := a.callCreateOrderAPI(ctx, apiReq)
    if err != nil {
        return nil, fmt.Errorf("调用创建订单API失败: %w", err)
    }

    // 4. 转换为标准格式
    result := a.convertToOrderResult(apiResp)
    result.PlatformOrderNo = req.CustomerOrderNo
    result.CustomerOrderNo = req.CustomerOrderNo

    a.logger.Info("订单创建成功",
        zap.String("provider", "your_provider"),
        zap.String("customer_order_no", req.CustomerOrderNo),
        zap.String("order_no", result.OrderNo),
        zap.String("tracking_no", result.TrackingNo))

    return result, nil
}

// validateOrderRequest 验证订单创建请求
func (a *YourProviderAdapter) validateOrderRequest(req *model.OrderRequest) error {
    if req.CustomerOrderNo == "" {
        return fmt.Errorf("客户订单号不能为空")
    }
    if req.Sender.Name == "" || req.Sender.Mobile == "" {
        return fmt.Errorf("寄件人信息不完整")
    }
    if req.Receiver.Name == "" || req.Receiver.Mobile == "" {
        return fmt.Errorf("收件人信息不完整")
    }
    return nil
}
```

### 5. 订单取消实现

```go
// CancelOrder 取消订单 - 实现ProviderAdapter接口
func (a *YourProviderAdapter) CancelOrder(ctx context.Context, taskId string, orderNo string, reason string) error {
    a.logger.Info("开始取消订单",
        zap.String("provider", "your_provider"),
        zap.String("task_id", taskId),
        zap.String("order_no", orderNo),
        zap.String("reason", reason))

    // 1. 参数验证
    if orderNo == "" {
        return fmt.Errorf("订单号不能为空")
    }

    // 2. 构建取消请求
    cancelReq := &YourProviderCancelRequest{
        OrderNo: orderNo,
        Reason:  reason,
    }

    // 3. 调用供应商API
    err := a.callCancelOrderAPI(ctx, cancelReq)
    if err != nil {
        return fmt.Errorf("调用取消订单API失败: %w", err)
    }

    a.logger.Info("订单取消成功",
        zap.String("provider", "your_provider"),
        zap.String("order_no", orderNo))

    return nil
}
```

### 6. 物流跟踪实现

```go
// QueryTrack 查询物流轨迹 - 实现ProviderAdapter接口
func (a *YourProviderAdapter) QueryTrack(ctx context.Context, trackingNo, expressType, phone, pollToken string, from, to string) (*model.TrackInfo, error) {
    a.logger.Info("开始查询物流轨迹",
        zap.String("provider", "your_provider"),
        zap.String("tracking_no", trackingNo),
        zap.String("express_type", expressType))

    // 1. 参数验证
    if trackingNo == "" {
        return nil, fmt.Errorf("运单号不能为空")
    }

    // 2. 构建查询请求
    trackReq := &YourProviderTrackRequest{
        TrackingNo:  trackingNo,
        ExpressType: expressType,
        Phone:       phone,
    }

    // 3. 调用供应商API
    apiResp, err := a.callTrackAPI(ctx, trackReq)
    if err != nil {
        return nil, fmt.Errorf("调用物流跟踪API失败: %w", err)
    }

    // 4. 转换为标准格式
    trackInfo := a.convertToTrackInfo(apiResp)

    a.logger.Info("物流跟踪查询完成",
        zap.String("provider", "your_provider"),
        zap.String("tracking_no", trackingNo),
        zap.String("status", trackInfo.Status))

    return trackInfo, nil
}
```

### 7. 辅助方法实现

```go
// calculateChargedWeight 计算计费重量（考虑体积重量和抛比）
func (a *YourProviderAdapter) calculateChargedWeight(req *model.PriceRequest, expressCode string) float64 {
    // 如果没有体积信息，直接返回实际重量
    if req.Package.Length <= 0 || req.Package.Width <= 0 || req.Package.Height <= 0 {
        return math.Ceil(req.Package.Weight)
    }

    // 计算体积（cm³）
    volumeCm3 := req.Package.Length * req.Package.Width * req.Package.Height

    // 获取快递公司的抛比配置
    volumeRatio := 6000 // 默认抛比，应从数据库获取
    if a.expressCompanyRepo != nil {
        if company, err := a.expressCompanyRepo.GetCompanyByCode(expressCode); err == nil {
            if company.VolumeWeightRatio > 0 {
                volumeRatio = company.VolumeWeightRatio
            }
        }
    }

    // 计算体积重量
    volumeWeight := volumeCm3 / float64(volumeRatio)

    // 取实际重量和体积重量的较大值，向上取整
    chargedWeight := math.Max(req.Package.Weight, volumeWeight)
    return math.Ceil(chargedWeight)
}

// callAPI 通用API调用方法
func (a *YourProviderAdapter) callAPI(ctx context.Context, method, endpoint string, data interface{}) ([]byte, error) {
    // 1. 序列化请求数据
    jsonData, err := json.Marshal(data)
    if err != nil {
        return nil, fmt.Errorf("序列化请求数据失败: %w", err)
    }

    // 2. 创建HTTP请求
    url := a.config.BaseURL + endpoint
    req, err := http.NewRequestWithContext(ctx, method, url, bytes.NewBuffer(jsonData))
    if err != nil {
        return nil, fmt.Errorf("创建HTTP请求失败: %w", err)
    }

    // 3. 设置请求头
    req.Header.Set("Content-Type", "application/json")
    req.Header.Set("Authorization", "Bearer "+a.config.AppKey)

    // 4. 发送请求
    resp, err := a.client.Do(req)
    if err != nil {
        return nil, fmt.Errorf("发送HTTP请求失败: %w", err)
    }
    defer resp.Body.Close()

    // 5. 读取响应
    respData, err := io.ReadAll(resp.Body)
    if err != nil {
        return nil, fmt.Errorf("读取响应数据失败: %w", err)
    }

    // 6. 检查HTTP状态码
    if resp.StatusCode != http.StatusOK {
        return nil, fmt.Errorf("API返回错误状态码: %d, 响应: %s", resp.StatusCode, string(respData))
    }

    return respData, nil
}
```

## 配置管理集成

### 1. 数据库配置表

在 `system_configs` 表中添加供应商配置：

```sql
-- 启用/禁用开关
INSERT INTO system_configs (config_group, config_key, config_value, config_type, description) VALUES
('provider', 'your_provider_enabled', 'false', 'boolean', '供应商启用开关');

-- API配置
INSERT INTO system_configs (config_group, config_key, config_value, config_type, description) VALUES
('provider_your_provider', 'app_key', 'your_app_key', 'string', 'API密钥'),
('provider_your_provider', 'app_secret', 'your_app_secret', 'string', 'API密钥'),
('provider_your_provider', 'base_url', 'https://api.yourprovider.com', 'string', 'API基础URL'),
('provider_your_provider', 'environment', 'production', 'string', '环境配置'),
('provider_your_provider', 'timeout', '10', 'int', '超时时间（秒）');
```

### 2. 配置服务扩展

在 `internal/service/provider_config_service.go` 中添加配置获取方法：

```go
// GetYourProviderConfig 获取供应商配置
func (s *DefaultProviderConfigService) GetYourProviderConfig(ctx context.Context) (*adapter.YourProviderConfig, error) {
    // 检查是否启用
    enabled, err := s.IsProviderEnabled(ctx, "your_provider")
    if err != nil {
        return nil, fmt.Errorf("检查供应商启用状态失败: %w", err)
    }
    if !enabled {
        return nil, fmt.Errorf("供应商未启用，请在数据库 system_configs 表中设置 provider.your_provider_enabled = true")
    }

    // 获取必需配置
    appKey := s.systemConfigService.GetConfigWithDefault("provider_your_provider.app_key", "")
    appSecret := s.systemConfigService.GetConfigWithDefault("provider_your_provider.app_secret", "")
    baseURL := s.systemConfigService.GetConfigWithDefault("provider_your_provider.base_url", "")
    environment := s.systemConfigService.GetConfigWithDefault("provider_your_provider.environment", "production")
    timeout := s.systemConfigService.GetConfigAsIntWithDefault("provider_your_provider.timeout", 10)

    // 验证必需配置
    if appKey == "" {
        return nil, fmt.Errorf("API密钥未配置，请在数据库 system_configs 表中设置 provider_your_provider.app_key")
    }
    if appSecret == "" {
        return nil, fmt.Errorf("API密钥未配置，请在数据库 system_configs 表中设置 provider_your_provider.app_secret")
    }
    if baseURL == "" {
        return nil, fmt.Errorf("API基础URL未配置，请在数据库 system_configs 表中设置 provider_your_provider.base_url")
    }

    config := &adapter.YourProviderConfig{
        AppKey:      appKey,
        AppSecret:   appSecret,
        BaseURL:     baseURL,
        Environment: environment,
        Timeout:     timeout,
    }

    s.logger.Info("供应商配置获取成功",
        zap.String("provider", "your_provider"),
        zap.String("base_url", baseURL),
        zap.String("environment", environment),
        zap.Int("timeout", timeout))

    return config, nil
}
```

### 3. 适配器工厂集成

在 `internal/adapter/provider_factory.go` 中添加适配器创建方法：

```go
// 1. 在接口中添加方法声明
type ProviderAdapterFactory interface {
    CreateKuaidi100Adapter(ctx context.Context) (ProviderAdapter, error)
    CreateYidaAdapter(ctx context.Context) (ProviderAdapter, error)
    CreateYuntongAdapter(ctx context.Context) (ProviderAdapter, error)
    CreateCainiaoAdapter(ctx context.Context) (ProviderAdapter, error)
    CreateYourProviderAdapter(ctx context.Context) (ProviderAdapter, error) // 新增
    IsProviderEnabled(ctx context.Context, providerName string) (bool, error)
}

// 2. 在工厂结构体中添加配置获取器
type DefaultProviderAdapterFactory struct {
    // ... 其他字段
    yourProviderConfigGetter interface {
        GetYourProviderConfig(ctx context.Context) (*YourProviderConfig, error)
    }
}

// 3. 实现创建方法
func (f *DefaultProviderAdapterFactory) CreateYourProviderAdapter(ctx context.Context) (ProviderAdapter, error) {
    config, err := f.yourProviderConfigGetter.GetYourProviderConfig(ctx)
    if err != nil {
        return nil, fmt.Errorf("获取供应商配置失败: %w", err)
    }

    adapter := NewYourProviderAdapter(*config, f.expressCompanyRepo)
    adapter.SetMappingService(f.mappingService)

    f.logger.Info("供应商适配器创建成功",
        zap.String("provider", "your_provider"),
        zap.String("base_url", config.BaseURL),
        zap.String("environment", config.Environment))

    return adapter, nil
}

// 4. 在CreateAdapter方法中添加case
func (f *DefaultProviderAdapterFactory) CreateAdapter(ctx context.Context, providerCode string) (ProviderAdapter, error) {
    switch providerCode {
    case "kuaidi100":
        return f.CreateKuaidi100Adapter(ctx)
    case "yida":
        return f.CreateYidaAdapter(ctx)
    case "yuntong":
        return f.CreateYuntongAdapter(ctx)
    case "cainiao":
        return f.CreateCainiaoAdapter(ctx)
    case "your_provider":
        return f.CreateYourProviderAdapter(ctx) // 新增
    default:
        return nil, fmt.Errorf("不支持的供应商: %s", providerCode)
    }
}
```

### 4. 动态管理器集成

在 `internal/adapter/dynamic_provider_manager.go` 中添加供应商支持：

```go
// 1. 在loadAllAdapters方法中添加到knownProviders列表
func (dm *DynamicProviderManager) loadAllAdapters(ctx context.Context) error {
    dm.logger.Info("加载所有启用的供应商适配器")

    knownProviders := []string{"kuaidi100", "yida", "yuntong", "cainiao", "your_provider"} // 添加新供应商

    // ... 其余代码保持不变
}

// 2. 在checkAndReloadChangedProviders方法中添加
func (dm *DynamicProviderManager) checkAndReloadChangedProviders(ctx context.Context) error {
    knownProviders := []string{"kuaidi100", "yida", "yuntong", "cainiao", "your_provider"} // 添加新供应商

    // ... 其余代码保持不变
}

// 3. 在ReloadAllProviders方法中添加
func (dm *DynamicProviderManager) ReloadAllProviders(ctx context.Context) error {
    dm.logger.Info("重新加载所有供应商适配器")

    knownProviders := []string{"kuaidi100", "yida", "yuntong", "cainiao", "your_provider"} // 添加新供应商

    // ... 其余代码保持不变
}

// 4. 在createAdapter方法中添加case
func (dm *DynamicProviderManager) createAdapter(ctx context.Context, providerCode string) (ProviderAdapter, error) {
    // 使用工厂创建适配器
    if factory, ok := dm.adapterFactory.(*DefaultProviderAdapterFactory); ok {
        return factory.CreateAdapter(ctx, providerCode)
    }

    // 如果不是默认工厂，使用接口方法
    switch providerCode {
    case "kuaidi100":
        return dm.adapterFactory.CreateKuaidi100Adapter(ctx)
    case "yida":
        return dm.adapterFactory.CreateYidaAdapter(ctx)
    case "yuntong":
        return dm.adapterFactory.CreateYuntongAdapter(ctx)
    case "cainiao":
        return dm.adapterFactory.CreateCainiaoAdapter(ctx)
    case "your_provider":
        return dm.adapterFactory.CreateYourProviderAdapter(ctx) // 新增
    default:
        return nil, fmt.Errorf("不支持的供应商: %s", providerCode)
    }
}
```

## 回调处理集成

### 1. 回调适配器实现

创建 `internal/service/callback/your_provider_adapter.go`：

```go
package callback

import (
    "crypto/md5"
    "encoding/hex"
    "encoding/json"
    "fmt"
    "strings"

    "github.com/your-org/go-kuaidi/internal/constants"
    "github.com/your-org/go-kuaidi/internal/model"
    "github.com/your-org/go-kuaidi/internal/util"
)

// YourProviderCallbackAdapter 供应商回调适配器
type YourProviderCallbackAdapter struct {
    appSecret string // 用于签名验证
}

// NewYourProviderCallbackAdapter 创建供应商回调适配器
func NewYourProviderCallbackAdapter(appSecret string) ProviderCallbackAdapter {
    return &YourProviderCallbackAdapter{
        appSecret: appSecret,
    }
}

// ValidateSignature 验证签名
func (a *YourProviderCallbackAdapter) ValidateSignature(rawData []byte, headers map[string]string) error {
    // 获取签名
    signature := headers["X-Signature"]
    if signature == "" {
        return fmt.Errorf("缺少签名头")
    }

    // 计算期望签名
    expectedSignature := a.calculateSignature(rawData)

    // 验证签名
    if signature != expectedSignature {
        return fmt.Errorf("签名验证失败")
    }

    return nil
}

// ParseCallback 解析回调数据
func (a *YourProviderCallbackAdapter) ParseCallback(rawData []byte) (*model.ParsedCallbackData, error) {
    // 解析JSON数据
    var callbackData map[string]interface{}
    if err := json.Unmarshal(rawData, &callbackData); err != nil {
        return nil, fmt.Errorf("解析回调数据失败: %w", err)
    }

    // 提取事件类型
    eventType, ok := callbackData["event_type"].(string)
    if !ok {
        return nil, fmt.Errorf("缺少事件类型")
    }

    // 提取订单号
    orderNo, _ := callbackData["order_no"].(string)

    // 提取运单号
    trackingNo, _ := callbackData["tracking_no"].(string)

    // 构建解析后的回调数据
    parsedData := &model.ParsedCallbackData{
        Provider:    "your_provider",
        EventType:   eventType,
        OrderNo:     orderNo,
        TrackingNo:  trackingNo,
        EventTime:   util.NowBeijing(),
        Data:        callbackData,
        RawData:     string(rawData),
    }

    return parsedData, nil
}

// StandardizeData 标准化回调数据
func (a *YourProviderCallbackAdapter) StandardizeData(data *model.ParsedCallbackData) (*model.StandardizedCallbackData, error) {
    switch data.EventType {
    case "order_created":
        return a.standardizeOrderCreated(data)
    case "order_cancelled":
        return a.standardizeOrderCancelled(data)
    case "pickup_success":
        return a.standardizePickupSuccess(data)
    case "status_updated":
        return a.standardizeStatusUpdated(data)
    case "billing_updated":
        return a.standardizeBillingUpdated(data)
    default:
        return nil, fmt.Errorf("不支持的事件类型: %s", data.EventType)
    }
}

// BuildResponse 构建响应
func (a *YourProviderCallbackAdapter) BuildResponse(data *model.StandardizedCallbackData) *model.CallbackResponse {
    return &model.CallbackResponse{
        Success: constants.ResponseValueTrue,
        Code:    constants.HTTPStatusOK,
        Message: constants.MessageReceived,
        Data: map[string]interface{}{
            "success": true,
            "message": "回调处理成功",
        },
    }
}

// calculateSignature 计算签名
func (a *YourProviderCallbackAdapter) calculateSignature(data []byte) string {
    // 根据供应商的签名算法实现
    h := md5.New()
    h.Write(data)
    h.Write([]byte(a.appSecret))
    return hex.EncodeToString(h.Sum(nil))
}

// standardizeOrderCreated 标准化订单创建事件
func (a *YourProviderCallbackAdapter) standardizeOrderCreated(data *model.ParsedCallbackData) (*model.StandardizedCallbackData, error) {
    rawData := data.Data.(map[string]interface{})

    orderData := &model.OrderCreatedData{
        OrderNo:    data.OrderNo,
        TrackingNo: data.TrackingNo,
        Status:     "created",
        CreateTime: data.EventTime,
    }

    return &model.StandardizedCallbackData{
        Type:      model.CallbackTypeOrderCreated,
        Provider:  data.Provider,
        OrderNo:   data.OrderNo,
        EventTime: data.EventTime,
        Data:      orderData,
        RawData:   rawData,
    }, nil
}

// standardizeBillingUpdated 标准化计费更新事件
func (a *YourProviderCallbackAdapter) standardizeBillingUpdated(data *model.ParsedCallbackData) (*model.StandardizedCallbackData, error) {
    rawData := data.Data.(map[string]interface{})

    // 提取费用信息
    totalFee, _ := rawData["total_fee"].(float64)
    weight, _ := rawData["weight"].(float64)

    billingData := &model.BillingUpdatedData{
        OrderNo:   data.OrderNo,
        TotalFee:  totalFee,
        Weight:    weight,
        UpdateTime: data.EventTime,
        FeeDetails: []model.FeeDetail{
            {
                FeeType: "freight",
                Amount:  totalFee,
                Description: "运费",
            },
        },
    }

    return &model.StandardizedCallbackData{
        Type:      model.CallbackTypeBillingUpdated,
        Provider:  data.Provider,
        OrderNo:   data.OrderNo,
        EventTime: data.EventTime,
        Data:      billingData,
        RawData:   rawData,
    }, nil
}
```

### 2. 回调处理器注册

在 `cmd/main.go` 中注册回调适配器：

```go
// 在createCallbackService函数中添加
func createCallbackService(/* 参数 */) *service.CallbackService {
    // ... 其他代码

    // 注册供应商回调适配器
    yourProviderSecret := systemConfigService.GetConfigWithDefault("provider_your_provider.app_secret", "")
    yourProviderAdapter := callback.NewYourProviderCallbackAdapter(yourProviderSecret)
    callbackService.RegisterProviderAdapter("your_provider", yourProviderAdapter)

    return callbackService
}
```

### 3. 回调路由配置

在 `api/router/router.go` 中添加回调路由：

```go
// 在setupCallbackRoutes函数中添加
func setupCallbackRoutes(r *gin.Engine, callbackHandler *handler.CallbackHandler) {
    callback := r.Group("/api/callback")
    {
        // ... 其他回调路由
        callback.POST("/your_provider", callbackHandler.HandleCallback)
    }
}
```

## 测试验证

### 1. 单元测试

创建 `internal/adapter/your_provider_test.go`：

```go
package adapter

import (
    "context"
    "testing"
    "time"

    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/mock"
    "github.com/your-org/go-kuaidi/internal/model"
)

func TestYourProviderAdapter_QueryPrice(t *testing.T) {
    // 创建测试配置
    config := YourProviderConfig{
        AppKey:      "test_key",
        AppSecret:   "test_secret",
        BaseURL:     "https://api.test.com",
        Environment: "test",
        Timeout:     10,
    }

    // 创建适配器
    adapter := NewYourProviderAdapter(config, nil)

    // 创建测试请求
    req := &model.PriceRequest{
        Sender: model.SenderInfo{
            Province: "北京市",
            City:     "北京市",
            District: "朝阳区",
        },
        Receiver: model.ReceiverInfo{
            Province: "上海市",
            City:     "上海市",
            District: "浦东新区",
        },
        Package: model.PackageInfo{
            Weight: 1.0,
            Length: 10,
            Width:  10,
            Height: 10,
        },
    }

    // 执行测试
    ctx := context.Background()
    prices, err := adapter.QueryPrice(ctx, req)

    // 验证结果
    assert.NoError(t, err)
    assert.NotEmpty(t, prices)

    for _, price := range prices {
        assert.Equal(t, "your_provider", price.Provider)
        assert.NotEmpty(t, price.ExpressCode)
        assert.Greater(t, price.Price, 0.0)
    }
}

func TestYourProviderAdapter_CreateOrder(t *testing.T) {
    // 类似的测试实现
}

func TestYourProviderAdapter_CancelOrder(t *testing.T) {
    // 类似的测试实现
}
```

### 2. 集成测试

创建 `cmd/test_your_provider/main.go`：

```go
package main

import (
    "context"
    "fmt"
    "log"
    "time"

    "github.com/your-org/go-kuaidi/internal/adapter"
    "github.com/your-org/go-kuaidi/internal/model"
)

func main() {
    // 测试配置
    config := adapter.YourProviderConfig{
        AppKey:      "your_test_key",
        AppSecret:   "your_test_secret",
        BaseURL:     "https://api.test.yourprovider.com",
        Environment: "test",
        Timeout:     10,
    }

    // 创建适配器
    adapter := adapter.NewYourProviderAdapter(config, nil)

    // 测试价格查询
    testQueryPrice(adapter)

    // 测试订单创建
    testCreateOrder(adapter)

    // 测试订单取消
    testCancelOrder(adapter)

    // 测试物流跟踪
    testQueryTrack(adapter)
}

func testQueryPrice(adapter *adapter.YourProviderAdapter) {
    fmt.Println("=== 测试价格查询 ===")

    req := &model.PriceRequest{
        Sender: model.SenderInfo{
            Name:     "张三",
            Mobile:   "13800138000",
            Province: "北京市",
            City:     "北京市",
            District: "朝阳区",
            Address:  "三里屯SOHO",
        },
        Receiver: model.ReceiverInfo{
            Name:     "李四",
            Mobile:   "13900139000",
            Province: "上海市",
            City:     "上海市",
            District: "浦东新区",
            Address:  "陆家嘴金融中心",
        },
        Package: model.PackageInfo{
            Weight:    2.5,
            Length:    30,
            Width:     20,
            Height:    15,
            Quantity:  1,
            GoodsName: "测试物品",
        },
    }

    ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
    defer cancel()

    prices, err := adapter.QueryPrice(ctx, req)
    if err != nil {
        log.Printf("价格查询失败: %v", err)
        return
    }

    fmt.Printf("查询到 %d 个价格选项:\n", len(prices))
    for _, price := range prices {
        fmt.Printf("- %s: ¥%.2f (预计%s)\n",
            price.ExpressName, price.Price, price.EstimatedTime)
    }
}

func testCreateOrder(adapter *adapter.YourProviderAdapter) {
    fmt.Println("\n=== 测试订单创建 ===")

    req := &model.OrderRequest{
        CustomerOrderNo: fmt.Sprintf("TEST_%d", time.Now().Unix()),
        ExpressType:     "SF", // 根据实际支持的快递公司调整
        ProductType:     "standard",
        Sender: model.SenderInfo{
            Name:     "张三",
            Mobile:   "13800138000",
            Province: "北京市",
            City:     "北京市",
            District: "朝阳区",
            Address:  "三里屯SOHO",
        },
        Receiver: model.ReceiverInfo{
            Name:     "李四",
            Mobile:   "13900139000",
            Province: "上海市",
            City:     "上海市",
            District: "浦东新区",
            Address:  "陆家嘴金融中心",
        },
        Package: model.PackageInfo{
            Weight:    2.5,
            Length:    30,
            Width:     20,
            Height:    15,
            Quantity:  1,
            GoodsName: "测试物品",
        },
    }

    ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
    defer cancel()

    result, err := adapter.CreateOrder(ctx, req)
    if err != nil {
        log.Printf("订单创建失败: %v", err)
        return
    }

    fmt.Printf("订单创建成功:\n")
    fmt.Printf("- 订单号: %s\n", result.OrderNo)
    fmt.Printf("- 运单号: %s\n", result.TrackingNo)
    fmt.Printf("- 取件码: %s\n", result.PickupCode)
    fmt.Printf("- 费用: ¥%.2f\n", result.Price)
}
```

### 3. 性能测试

创建性能测试脚本：

```go
func BenchmarkYourProviderAdapter_QueryPrice(b *testing.B) {
    config := adapter.YourProviderConfig{
        AppKey:      "test_key",
        AppSecret:   "test_secret",
        BaseURL:     "https://api.test.com",
        Environment: "test",
        Timeout:     10,
    }

    adapter := adapter.NewYourProviderAdapter(config, nil)

    req := &model.PriceRequest{
        // ... 测试数据
    }

    ctx := context.Background()

    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        _, err := adapter.QueryPrice(ctx, req)
        if err != nil {
            b.Fatalf("查询失败: %v", err)
        }
    }
}
```

## 部署上线

### 1. 管理员接口集成

在 `api/handler/admin_provider_reload_handler.go` 中添加供应商支持：

```go
// 在ReloadProvider方法中添加验证
func (h *AdminProviderReloadHandler) ReloadProvider(c *gin.Context) {
    providerCode := c.Param("provider_code")

    // 验证供应商代码
    validProviders := map[string]bool{
        "kuaidi100":     true,
        "yida":          true,
        "yuntong":       true,
        "cainiao":       true,
        "your_provider": true, // 新增
    }

    if !validProviders[providerCode] {
        c.JSON(http.StatusBadRequest, model.APIResponse{
            Success: false,
            Code:    http.StatusBadRequest,
            Message: "不支持的供应商代码",
        })
        return
    }

    // ... 其余代码保持不变
}
```

### 2. 数据库初始化

创建数据库初始化脚本 `scripts/init_your_provider.sql`：

```sql
-- 供应商配置初始化
INSERT INTO system_configs (id, config_group, config_key, config_value, config_type, description, is_encrypted, is_readonly, is_system, created_at, updated_at) VALUES
(gen_random_uuid(), 'provider', 'your_provider_enabled', 'false', 'boolean', '供应商启用开关', false, false, true, NOW(), NOW()),
(gen_random_uuid(), 'provider_your_provider', 'app_key', '', 'string', 'API密钥', true, false, false, NOW(), NOW()),
(gen_random_uuid(), 'provider_your_provider', 'app_secret', '', 'string', 'API密钥', true, false, false, NOW(), NOW()),
(gen_random_uuid(), 'provider_your_provider', 'base_url', 'https://api.yourprovider.com', 'string', 'API基础URL', false, false, false, NOW(), NOW()),
(gen_random_uuid(), 'provider_your_provider', 'environment', 'production', 'string', '环境配置', false, false, false, NOW(), NOW()),
(gen_random_uuid(), 'provider_your_provider', 'timeout', '10', 'int', '超时时间（秒）', false, false, false, NOW(), NOW());

-- 快递公司映射（如果需要）
INSERT INTO express_companies (id, code, name, provider, volume_weight_ratio, created_at, updated_at) VALUES
(gen_random_uuid(), 'YP_SF', '顺丰速运', 'your_provider', 5000, NOW(), NOW()),
(gen_random_uuid(), 'YP_YTO', '圆通速递', 'your_provider', 6000, NOW(), NOW()),
(gen_random_uuid(), 'YP_ZTO', '中通快递', 'your_provider', 6000, NOW(), NOW());

-- 快递代码映射
INSERT INTO express_mappings (id, standard_code, provider, provider_code, created_at, updated_at) VALUES
(gen_random_uuid(), 'SF', 'your_provider', 'YP_SF', NOW(), NOW()),
(gen_random_uuid(), 'YTO', 'your_provider', 'YP_YTO', NOW(), NOW()),
(gen_random_uuid(), 'ZTO', 'your_provider', 'YP_ZTO', NOW(), NOW());
```

### 3. 配置部署

```bash
# 1. 执行数据库初始化
psql -h ************* -U postgres -d go_kuaidi -f scripts/init_your_provider.sql

# 2. 更新配置
psql -h ************* -U postgres -d go_kuaidi -c "
UPDATE system_configs SET config_value = 'your_actual_app_key'
WHERE config_key = 'app_key' AND config_group = 'provider_your_provider';

UPDATE system_configs SET config_value = 'your_actual_app_secret'
WHERE config_key = 'app_secret' AND config_group = 'provider_your_provider';
"

# 3. 启用供应商
psql -h ************* -U postgres -d go_kuaidi -c "
UPDATE system_configs SET config_value = 'true'
WHERE config_key = 'your_provider_enabled' AND config_group = 'provider';
"

# 4. 重启服务或热重载
curl -X POST http://mywl.py258.com/api/v1/admin/providers/your_provider/reload \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

## 监控运维

### 1. 日志监控

添加关键日志记录点：

```go
// 在适配器中添加性能监控日志
func (a *YourProviderAdapter) QueryPrice(ctx context.Context, req *model.PriceRequest) ([]model.StandardizedPrice, error) {
    start := time.Now()

    a.logger.Info("供应商价格查询开始",
        zap.String("provider", "your_provider"),
        zap.String("from", req.Sender.Province),
        zap.String("to", req.Receiver.Province),
        zap.Float64("weight", req.Package.Weight))

    defer func() {
        duration := time.Since(start)
        a.logger.Info("供应商价格查询完成",
            zap.String("provider", "your_provider"),
            zap.Duration("duration", duration))
    }()

    // ... 实际实现
}
```

### 2. 健康检查

实现健康检查接口：

```go
// HealthCheck 健康检查
func (a *YourProviderAdapter) HealthCheck(ctx context.Context) error {
    // 调用供应商的健康检查接口
    req, err := http.NewRequestWithContext(ctx, "GET", a.config.BaseURL+"/health", nil)
    if err != nil {
        return fmt.Errorf("创建健康检查请求失败: %w", err)
    }

    resp, err := a.client.Do(req)
    if err != nil {
        return fmt.Errorf("健康检查请求失败: %w", err)
    }
    defer resp.Body.Close()

    if resp.StatusCode != http.StatusOK {
        return fmt.Errorf("健康检查失败，状态码: %d", resp.StatusCode)
    }

    return nil
}
```

### 3. 指标监控

添加Prometheus指标：

```go
import (
    "github.com/prometheus/client_golang/prometheus"
    "github.com/prometheus/client_golang/prometheus/promauto"
)

var (
    yourProviderRequestTotal = promauto.NewCounterVec(
        prometheus.CounterOpts{
            Name: "your_provider_requests_total",
            Help: "供应商请求总数",
        },
        []string{"method", "status"},
    )

    yourProviderRequestDuration = promauto.NewHistogramVec(
        prometheus.HistogramOpts{
            Name: "your_provider_request_duration_seconds",
            Help: "供应商请求耗时",
        },
        []string{"method"},
    )
)

// 在API调用中添加指标记录
func (a *YourProviderAdapter) callAPI(ctx context.Context, method, endpoint string, data interface{}) ([]byte, error) {
    start := time.Now()

    defer func() {
        duration := time.Since(start)
        yourProviderRequestDuration.WithLabelValues(method).Observe(duration.Seconds())
    }()

    // ... API调用实现

    // 记录成功/失败指标
    if err != nil {
        yourProviderRequestTotal.WithLabelValues(method, "error").Inc()
        return nil, err
    }

    yourProviderRequestTotal.WithLabelValues(method, "success").Inc()
    return respData, nil
}
```

## 最佳实践总结

### 1. 代码质量

- **遵循SOLID原则**：单一职责、开闭原则、里氏替换、接口隔离、依赖倒置
- **错误处理**：使用wrap error，提供详细的错误信息
- **日志记录**：关键操作点添加结构化日志
- **单元测试**：覆盖率不低于80%

### 2. 性能优化

- **HTTP连接池**：复用连接，减少建连开销
- **超时控制**：合理设置各级超时时间
- **并发控制**：避免过多并发请求导致供应商限流
- **缓存策略**：合理使用缓存减少API调用

### 3. 安全考虑

- **敏感信息加密**：API密钥等敏感配置加密存储
- **签名验证**：回调数据必须验证签名
- **参数验证**：严格验证输入参数
- **访问控制**：限制管理接口访问权限

### 4. 运维友好

- **配置热重载**：支持不重启服务更新配置
- **健康检查**：提供健康检查接口
- **监控指标**：暴露关键业务指标
- **故障恢复**：实现熔断、重试等容错机制

## 常见问题与解决方案

### 1. API调用问题

**问题：供应商API返回非标准错误格式**
```go
// 解决方案：统一错误处理
func (a *YourProviderAdapter) handleAPIError(respData []byte, statusCode int) error {
    // 尝试解析标准错误格式
    var standardError struct {
        Code    string `json:"code"`
        Message string `json:"message"`
    }

    if err := json.Unmarshal(respData, &standardError); err == nil {
        return fmt.Errorf("供应商API错误 [%s]: %s", standardError.Code, standardError.Message)
    }

    // 降级处理：返回HTTP状态码和原始响应
    return fmt.Errorf("供应商API错误 [HTTP %d]: %s", statusCode, string(respData))
}
```

**问题：供应商API限流处理**
```go
// 解决方案：实现指数退避重试
func (a *YourProviderAdapter) callAPIWithRetry(ctx context.Context, method, endpoint string, data interface{}) ([]byte, error) {
    maxRetries := 3
    baseDelay := time.Second

    for attempt := 0; attempt <= maxRetries; attempt++ {
        respData, err := a.callAPI(ctx, method, endpoint, data)

        // 成功或非重试错误直接返回
        if err == nil || !a.isRetryableError(err) {
            return respData, err
        }

        // 最后一次尝试失败
        if attempt == maxRetries {
            return nil, fmt.Errorf("重试%d次后仍然失败: %w", maxRetries, err)
        }

        // 指数退避
        delay := baseDelay * time.Duration(1<<attempt)
        a.logger.Warn("API调用失败，准备重试",
            zap.Int("attempt", attempt+1),
            zap.Duration("delay", delay),
            zap.Error(err))

        select {
        case <-ctx.Done():
            return nil, ctx.Err()
        case <-time.After(delay):
            // 继续重试
        }
    }

    return nil, fmt.Errorf("不应该到达这里")
}

func (a *YourProviderAdapter) isRetryableError(err error) bool {
    // 判断是否为可重试错误（网络错误、限流等）
    if strings.Contains(err.Error(), "timeout") ||
       strings.Contains(err.Error(), "rate limit") ||
       strings.Contains(err.Error(), "503") ||
       strings.Contains(err.Error(), "502") {
        return true
    }
    return false
}
```

### 2. 数据转换问题

**问题：供应商时间格式不统一**
```go
// 解决方案：统一时间解析
func (a *YourProviderAdapter) parseTime(timeStr string) (time.Time, error) {
    // 常见时间格式列表
    formats := []string{
        "2006-01-02 15:04:05",
        "2006-01-02T15:04:05Z",
        "2006-01-02T15:04:05.000Z",
        "2006/01/02 15:04:05",
        "20060102150405",
    }

    for _, format := range formats {
        if t, err := time.Parse(format, timeStr); err == nil {
            return t, nil
        }
    }

    return time.Time{}, fmt.Errorf("无法解析时间格式: %s", timeStr)
}
```

**问题：地址标准化处理**
```go
// 解决方案：地址清洗和标准化
func (a *YourProviderAdapter) standardizeAddress(province, city, district, address string) (string, string, string, string) {
    // 省份标准化
    province = a.standardizeProvince(province)

    // 城市标准化
    city = a.standardizeCity(city)

    // 区县标准化
    district = a.standardizeDistrict(district)

    // 详细地址清洗
    address = a.cleanAddress(address)

    return province, city, district, address
}

func (a *YourProviderAdapter) standardizeProvince(province string) string {
    // 省份映射表
    provinceMap := map[string]string{
        "北京":   "北京市",
        "上海":   "上海市",
        "天津":   "天津市",
        "重庆":   "重庆市",
        "内蒙古": "内蒙古自治区",
        "广西":   "广西壮族自治区",
        "西藏":   "西藏自治区",
        "宁夏":   "宁夏回族自治区",
        "新疆":   "新疆维吾尔自治区",
    }

    if standard, exists := provinceMap[province]; exists {
        return standard
    }

    // 如果不在映射表中，检查是否已经是标准格式
    if strings.HasSuffix(province, "省") ||
       strings.HasSuffix(province, "市") ||
       strings.HasSuffix(province, "自治区") {
        return province
    }

    // 默认添加"省"后缀
    return province + "省"
}
```

### 3. 配置管理问题

**问题：敏感配置加密存储**
```go
// 解决方案：配置加密工具
type ConfigEncryption struct {
    key []byte
}

func NewConfigEncryption(key string) *ConfigEncryption {
    return &ConfigEncryption{
        key: []byte(key),
    }
}

func (c *ConfigEncryption) Encrypt(plaintext string) (string, error) {
    block, err := aes.NewCipher(c.key)
    if err != nil {
        return "", err
    }

    gcm, err := cipher.NewGCM(block)
    if err != nil {
        return "", err
    }

    nonce := make([]byte, gcm.NonceSize())
    if _, err = io.ReadFull(rand.Reader, nonce); err != nil {
        return "", err
    }

    ciphertext := gcm.Seal(nonce, nonce, []byte(plaintext), nil)
    return base64.StdEncoding.EncodeToString(ciphertext), nil
}

func (c *ConfigEncryption) Decrypt(ciphertext string) (string, error) {
    data, err := base64.StdEncoding.DecodeString(ciphertext)
    if err != nil {
        return "", err
    }

    block, err := aes.NewCipher(c.key)
    if err != nil {
        return "", err
    }

    gcm, err := cipher.NewGCM(block)
    if err != nil {
        return "", err
    }

    nonceSize := gcm.NonceSize()
    if len(data) < nonceSize {
        return "", fmt.Errorf("密文太短")
    }

    nonce, ciphertext := data[:nonceSize], data[nonceSize:]
    plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
    if err != nil {
        return "", err
    }

    return string(plaintext), nil
}
```

### 4. 性能优化问题

**问题：并发控制和限流**
```go
// 解决方案：令牌桶限流器
type RateLimiter struct {
    limiter *rate.Limiter
    logger  *zap.Logger
}

func NewRateLimiter(requestsPerSecond int, burstSize int, logger *zap.Logger) *RateLimiter {
    return &RateLimiter{
        limiter: rate.NewLimiter(rate.Limit(requestsPerSecond), burstSize),
        logger:  logger,
    }
}

func (r *RateLimiter) Wait(ctx context.Context) error {
    if err := r.limiter.Wait(ctx); err != nil {
        r.logger.Warn("限流等待被中断", zap.Error(err))
        return err
    }
    return nil
}

// 在适配器中使用
func (a *YourProviderAdapter) callAPI(ctx context.Context, method, endpoint string, data interface{}) ([]byte, error) {
    // 限流控制
    if err := a.rateLimiter.Wait(ctx); err != nil {
        return nil, fmt.Errorf("限流等待失败: %w", err)
    }

    // ... 实际API调用
}
```

**问题：连接池优化**
```go
// 解决方案：自定义HTTP传输配置
func (a *YourProviderAdapter) createOptimizedHTTPClient() *http.Client {
    transport := &http.Transport{
        // 连接池配置
        MaxIdleConns:        200,              // 最大空闲连接数
        MaxIdleConnsPerHost: 50,               // 每个主机最大空闲连接数
        MaxConnsPerHost:     100,              // 每个主机最大连接数
        IdleConnTimeout:     60 * time.Second, // 空闲连接超时

        // 连接建立配置
        DialContext: (&net.Dialer{
            Timeout:   5 * time.Second,  // 连接超时
            KeepAlive: 30 * time.Second, // Keep-Alive间隔
        }).DialContext,

        // TLS配置
        TLSHandshakeTimeout: 5 * time.Second,
        TLSClientConfig: &tls.Config{
            InsecureSkipVerify: false, // 生产环境必须验证证书
        },

        // HTTP/2配置
        ForceAttemptHTTP2:     true,
        MaxResponseHeaderBytes: 1 << 20, // 1MB

        // 其他优化
        DisableCompression:    false,            // 启用压缩
        ResponseHeaderTimeout: 8 * time.Second,  // 响应头超时
        ExpectContinueTimeout: 1 * time.Second,  // Expect: 100-continue超时
    }

    return &http.Client{
        Transport: transport,
        Timeout:   time.Duration(a.config.Timeout) * time.Second,
    }
}
```

## 故障排查指南

### 1. 日志分析

**关键日志字段**
```go
// 标准化日志格式
func (a *YourProviderAdapter) logAPICall(method, endpoint string, req, resp interface{}, duration time.Duration, err error) {
    fields := []zap.Field{
        zap.String("provider", "your_provider"),
        zap.String("method", method),
        zap.String("endpoint", endpoint),
        zap.Duration("duration", duration),
        zap.String("request_id", a.getRequestID()),
    }

    if err != nil {
        fields = append(fields, zap.Error(err))
        a.logger.Error("API调用失败", fields...)
    } else {
        a.logger.Info("API调用成功", fields...)
    }

    // 调试模式下记录请求响应详情
    if a.config.Environment == "debug" {
        a.logger.Debug("API调用详情",
            zap.Any("request", req),
            zap.Any("response", resp))
    }
}
```

**日志查询示例**
```bash
# 查询特定供应商的错误日志
grep "your_provider" /var/log/go-kuaidi.log | grep "ERROR"

# 查询API调用性能
grep "API调用完成" /var/log/go-kuaidi.log | grep "your_provider" | awk '{print $NF}' | sort -n

# 查询特定订单的处理流程
grep "customer_order_no.*TEST_12345" /var/log/go-kuaidi.log
```

### 2. 常见错误码

| 错误类型 | 错误码 | 描述 | 解决方案 |
|---------|--------|------|----------|
| 认证错误 | AUTH_001 | API密钥无效 | 检查配置中的app_key和app_secret |
| 参数错误 | PARAM_001 | 必填参数缺失 | 检查请求参数完整性 |
| 地址错误 | ADDR_001 | 地址格式不正确 | 使用地址标准化函数 |
| 限流错误 | RATE_001 | 请求频率过高 | 实现退避重试机制 |
| 网络错误 | NET_001 | 网络连接超时 | 检查网络连接和超时配置 |

### 3. 性能基准

**响应时间基准**
- 价格查询：< 2秒
- 订单创建：< 5秒
- 订单取消：< 3秒
- 物流跟踪：< 2秒

**并发能力基准**
- 价格查询：100 QPS
- 订单创建：50 QPS
- 回调处理：200 QPS

## 版本升级指南

### 1. 适配器版本管理

```go
// 版本信息结构
type ProviderVersion struct {
    Version     string    `json:"version"`
    APIVersion  string    `json:"api_version"`
    ReleaseDate time.Time `json:"release_date"`
    Features    []string  `json:"features"`
    Deprecated  []string  `json:"deprecated"`
}

// 获取版本信息
func (a *YourProviderAdapter) GetVersion() *ProviderVersion {
    return &ProviderVersion{
        Version:     "1.0.0",
        APIVersion:  "v2.1",
        ReleaseDate: time.Date(2025, 7, 16, 0, 0, 0, 0, time.UTC),
        Features: []string{
            "价格查询",
            "订单创建",
            "订单取消",
            "物流跟踪",
            "回调处理",
        },
        Deprecated: []string{},
    }
}
```

### 2. 向后兼容性

```go
// 版本兼容性检查
func (a *YourProviderAdapter) checkCompatibility(requiredVersion string) error {
    current := a.GetVersion().Version

    // 使用语义化版本比较
    if !a.isVersionCompatible(current, requiredVersion) {
        return fmt.Errorf("版本不兼容: 当前版本 %s, 要求版本 %s", current, requiredVersion)
    }

    return nil
}
```

## 安全最佳实践

### 1. 输入验证

```go
// 严格的参数验证
func (a *YourProviderAdapter) validateInput(input interface{}) error {
    // 使用validator库进行验证
    validate := validator.New()

    if err := validate.Struct(input); err != nil {
        return fmt.Errorf("参数验证失败: %w", err)
    }

    // 自定义业务规则验证
    switch v := input.(type) {
    case *model.PriceRequest:
        return a.validatePriceRequest(v)
    case *model.OrderRequest:
        return a.validateOrderRequest(v)
    default:
        return fmt.Errorf("不支持的输入类型: %T", input)
    }
}

// 防止SQL注入和XSS攻击
func (a *YourProviderAdapter) sanitizeInput(input string) string {
    // 移除危险字符
    input = strings.ReplaceAll(input, "'", "")
    input = strings.ReplaceAll(input, "\"", "")
    input = strings.ReplaceAll(input, "<", "")
    input = strings.ReplaceAll(input, ">", "")

    // 限制长度
    if len(input) > 1000 {
        input = input[:1000]
    }

    return input
}
```

### 2. 敏感数据处理

```go
// 敏感数据脱敏
func (a *YourProviderAdapter) maskSensitiveData(data map[string]interface{}) map[string]interface{} {
    sensitiveFields := []string{"app_key", "app_secret", "mobile", "phone", "id_card"}

    masked := make(map[string]interface{})
    for k, v := range data {
        if a.isSensitiveField(k, sensitiveFields) {
            if str, ok := v.(string); ok && len(str) > 4 {
                masked[k] = str[:2] + "****" + str[len(str)-2:]
            } else {
                masked[k] = "****"
            }
        } else {
            masked[k] = v
        }
    }

    return masked
}
```

## 完整代码模板

### 1. 数据结构定义模板 (your_provider_types.go)

```go
package adapter

import (
    "time"
)

// YourProviderConfig 供应商配置
type YourProviderConfig struct {
    AppKey      string `json:"app_key" validate:"required"`
    AppSecret   string `json:"app_secret" validate:"required"`
    BaseURL     string `json:"base_url" validate:"required,url"`
    Environment string `json:"environment" validate:"oneof=sandbox production"`
    Timeout     int    `json:"timeout" validate:"min=1,max=60"`
}

// API请求结构体
type YourProviderPriceRequest struct {
    FromProvince string  `json:"from_province"`
    FromCity     string  `json:"from_city"`
    ToProvince   string  `json:"to_province"`
    ToCity       string  `json:"to_city"`
    Weight       float64 `json:"weight"`
    Length       float64 `json:"length,omitempty"`
    Width        float64 `json:"width,omitempty"`
    Height       float64 `json:"height,omitempty"`
}

type YourProviderPriceResponse struct {
    Code      string                     `json:"code"`
    Message   string                     `json:"message"`
    Success   bool                       `json:"success"`
    PriceList []YourProviderPriceItem    `json:"price_list"`
}

type YourProviderPriceItem struct {
    ExpressCode     string  `json:"express_code"`
    ExpressName     string  `json:"express_name"`
    Price           float64 `json:"price"`
    EstimatedTime   string  `json:"estimated_time"`
    SupportsPickup  bool    `json:"supports_pickup"`
    PickupTimeSlots []struct {
        StartTime string `json:"start_time"`
        EndTime   string `json:"end_time"`
    } `json:"pickup_time_slots,omitempty"`
}

type YourProviderOrderRequest struct {
    CustomerOrderNo string                    `json:"customer_order_no"`
    ExpressCode     string                    `json:"express_code"`
    Sender          YourProviderAddressInfo   `json:"sender"`
    Receiver        YourProviderAddressInfo   `json:"receiver"`
    Package         YourProviderPackageInfo   `json:"package"`
    PickupTime      string                    `json:"pickup_time,omitempty"`
}

type YourProviderAddressInfo struct {
    Name     string `json:"name"`
    Mobile   string `json:"mobile"`
    Province string `json:"province"`
    City     string `json:"city"`
    District string `json:"district"`
    Address  string `json:"address"`
}

type YourProviderPackageInfo struct {
    Weight    float64 `json:"weight"`
    Length    float64 `json:"length,omitempty"`
    Width     float64 `json:"width,omitempty"`
    Height    float64 `json:"height,omitempty"`
    Quantity  int     `json:"quantity"`
    GoodsName string  `json:"goods_name"`
}

type YourProviderOrderResponse struct {
    Code    string                   `json:"code"`
    Message string                   `json:"message"`
    Success bool                     `json:"success"`
    Data    YourProviderOrderResult  `json:"data"`
}

type YourProviderOrderResult struct {
    OrderNo     string  `json:"order_no"`
    TrackingNo  string  `json:"tracking_no"`
    PickupCode  string  `json:"pickup_code,omitempty"`
    Price       float64 `json:"price"`
    Status      string  `json:"status"`
    CreateTime  string  `json:"create_time"`
}

type YourProviderCancelRequest struct {
    OrderNo string `json:"order_no"`
    Reason  string `json:"reason"`
}

type YourProviderTrackRequest struct {
    TrackingNo  string `json:"tracking_no"`
    ExpressType string `json:"express_type,omitempty"`
    Phone       string `json:"phone,omitempty"`
}

type YourProviderTrackResponse struct {
    Code    string                    `json:"code"`
    Message string                    `json:"message"`
    Success bool                      `json:"success"`
    Data    YourProviderTrackResult   `json:"data"`
}

type YourProviderTrackResult struct {
    TrackingNo string                     `json:"tracking_no"`
    Status     string                     `json:"status"`
    StatusDesc string                     `json:"status_desc"`
    TrackList  []YourProviderTrackItem    `json:"track_list"`
}

type YourProviderTrackItem struct {
    Time        string `json:"time"`
    Context     string `json:"context"`
    Location    string `json:"location,omitempty"`
    Status      string `json:"status"`
    StatusCode  string `json:"status_code,omitempty"`
}
```

### 2. 完整适配器实现模板

```go
package adapter

import (
    "bytes"
    "context"
    "crypto/md5"
    "encoding/hex"
    "encoding/json"
    "fmt"
    "io"
    "math"
    "net/http"
    "sort"
    "strconv"
    "strings"
    "time"

    "github.com/go-playground/validator/v10"
    "github.com/your-org/go-kuaidi/internal/express"
    "github.com/your-org/go-kuaidi/internal/model"
    "github.com/your-org/go-kuaidi/internal/util"
    "go.uber.org/zap"
    "golang.org/x/time/rate"
)

// YourProviderAdapter 供应商适配器
type YourProviderAdapter struct {
    config             YourProviderConfig
    client             *http.Client
    logger             *zap.Logger
    validator          *validator.Validate
    rateLimiter        *rate.Limiter
    mappingService     express.ExpressMappingService
    expressCompanyRepo express.ExpressCompanyRepository
}

// NewYourProviderAdapter 创建供应商适配器
func NewYourProviderAdapter(config YourProviderConfig, expressCompanyRepo express.ExpressCompanyRepository) *YourProviderAdapter {
    logger, _ := zap.NewProduction()

    // 创建优化的HTTP客户端
    client := createOptimizedHTTPClient(config.Timeout)

    // 创建验证器
    validator := validator.New()

    // 创建限流器 (每秒50个请求，突发100个)
    rateLimiter := rate.NewLimiter(50, 100)

    return &YourProviderAdapter{
        config:             config,
        client:             client,
        logger:             logger,
        validator:          validator,
        rateLimiter:        rateLimiter,
        expressCompanyRepo: expressCompanyRepo,
    }
}

// Name 返回供应商名称
func (a *YourProviderAdapter) Name() string {
    return "your_provider"
}

// SetMappingService 设置快递映射服务
func (a *YourProviderAdapter) SetMappingService(service express.ExpressMappingService) {
    a.mappingService = service
}

// QueryPrice 查询价格 - 实现ProviderAdapter接口
func (a *YourProviderAdapter) QueryPrice(ctx context.Context, req *model.PriceRequest) ([]model.StandardizedPrice, error) {
    start := time.Now()

    a.logger.Info("开始查询价格",
        zap.String("provider", "your_provider"),
        zap.String("from", fmt.Sprintf("%s-%s", req.Sender.Province, req.Sender.City)),
        zap.String("to", fmt.Sprintf("%s-%s", req.Receiver.Province, req.Receiver.City)),
        zap.Float64("weight", req.Package.Weight))

    defer func() {
        duration := time.Since(start)
        a.logger.Info("价格查询完成",
            zap.String("provider", "your_provider"),
            zap.Duration("duration", duration))
    }()

    // 1. 参数验证
    if err := a.validatePriceRequest(req); err != nil {
        return nil, fmt.Errorf("参数验证失败: %w", err)
    }

    // 2. 限流控制
    if err := a.rateLimiter.Wait(ctx); err != nil {
        return nil, fmt.Errorf("限流等待失败: %w", err)
    }

    // 3. 转换为供应商API格式
    apiReq := a.convertToPriceAPIRequest(req)

    // 4. 调用供应商API
    apiResp, err := a.callPriceAPIWithRetry(ctx, apiReq)
    if err != nil {
        return nil, fmt.Errorf("调用价格查询API失败: %w", err)
    }

    // 5. 转换为标准格式
    standardPrices := a.convertToStandardizedPrices(apiResp, req)

    a.logger.Info("价格查询成功",
        zap.String("provider", "your_provider"),
        zap.Int("price_count", len(standardPrices)))

    return standardPrices, nil
}

// CreateOrder 创建订单 - 实现ProviderAdapter接口
func (a *YourProviderAdapter) CreateOrder(ctx context.Context, req *model.OrderRequest) (*model.OrderResult, error) {
    start := time.Now()

    a.logger.Info("开始创建订单",
        zap.String("provider", "your_provider"),
        zap.String("customer_order_no", req.CustomerOrderNo),
        zap.String("express_type", req.ExpressType))

    defer func() {
        duration := time.Since(start)
        a.logger.Info("订单创建完成",
            zap.String("provider", "your_provider"),
            zap.String("customer_order_no", req.CustomerOrderNo),
            zap.Duration("duration", duration))
    }()

    // 1. 参数验证
    if err := a.validateOrderRequest(req); err != nil {
        return nil, fmt.Errorf("订单参数验证失败: %w", err)
    }

    // 2. 限流控制
    if err := a.rateLimiter.Wait(ctx); err != nil {
        return nil, fmt.Errorf("限流等待失败: %w", err)
    }

    // 3. 转换为供应商API格式
    apiReq := a.convertToOrderAPIRequest(req)

    // 4. 调用供应商API
    apiResp, err := a.callCreateOrderAPIWithRetry(ctx, apiReq)
    if err != nil {
        return nil, fmt.Errorf("调用创建订单API失败: %w", err)
    }

    // 5. 转换为标准格式
    result := a.convertToOrderResult(apiResp)
    result.PlatformOrderNo = req.CustomerOrderNo
    result.CustomerOrderNo = req.CustomerOrderNo

    a.logger.Info("订单创建成功",
        zap.String("provider", "your_provider"),
        zap.String("customer_order_no", req.CustomerOrderNo),
        zap.String("order_no", result.OrderNo),
        zap.String("tracking_no", result.TrackingNo),
        zap.Float64("price", result.Price))

    return result, nil
}

// CancelOrder 取消订单 - 实现ProviderAdapter接口
func (a *YourProviderAdapter) CancelOrder(ctx context.Context, taskId string, orderNo string, reason string) error {
    start := time.Now()

    a.logger.Info("开始取消订单",
        zap.String("provider", "your_provider"),
        zap.String("task_id", taskId),
        zap.String("order_no", orderNo),
        zap.String("reason", reason))

    defer func() {
        duration := time.Since(start)
        a.logger.Info("订单取消完成",
            zap.String("provider", "your_provider"),
            zap.String("order_no", orderNo),
            zap.Duration("duration", duration))
    }()

    // 1. 参数验证
    if orderNo == "" {
        return fmt.Errorf("订单号不能为空")
    }
    if reason == "" {
        reason = "用户取消"
    }

    // 2. 限流控制
    if err := a.rateLimiter.Wait(ctx); err != nil {
        return fmt.Errorf("限流等待失败: %w", err)
    }

    // 3. 构建取消请求
    cancelReq := &YourProviderCancelRequest{
        OrderNo: orderNo,
        Reason:  reason,
    }

    // 4. 调用供应商API
    err := a.callCancelOrderAPIWithRetry(ctx, cancelReq)
    if err != nil {
        return fmt.Errorf("调用取消订单API失败: %w", err)
    }

    a.logger.Info("订单取消成功",
        zap.String("provider", "your_provider"),
        zap.String("order_no", orderNo))

    return nil
}

// QueryOrder 查询订单 - 实现ProviderAdapter接口
func (a *YourProviderAdapter) QueryOrder(ctx context.Context, orderNo string, trackingNo string) (*model.OrderInfo, error) {
    // 实现订单查询逻辑
    // 这里可以复用QueryTrack的逻辑，或者调用专门的订单查询API
    trackInfo, err := a.QueryTrack(ctx, trackingNo, "", "", "", "", "")
    if err != nil {
        return nil, err
    }

    return &model.OrderInfo{
        OrderNo:    orderNo,
        TrackingNo: trackingNo,
        Status:     trackInfo.Status,
        StatusDesc: trackInfo.StatusDesc,
        UpdateTime: trackInfo.UpdateTime,
    }, nil
}

// QueryTrack 查询物流轨迹 - 实现ProviderAdapter接口
func (a *YourProviderAdapter) QueryTrack(ctx context.Context, trackingNo, expressType, phone, pollToken string, from, to string) (*model.TrackInfo, error) {
    start := time.Now()

    a.logger.Info("开始查询物流轨迹",
        zap.String("provider", "your_provider"),
        zap.String("tracking_no", trackingNo),
        zap.String("express_type", expressType))

    defer func() {
        duration := time.Since(start)
        a.logger.Info("物流轨迹查询完成",
            zap.String("provider", "your_provider"),
            zap.String("tracking_no", trackingNo),
            zap.Duration("duration", duration))
    }()

    // 1. 参数验证
    if trackingNo == "" {
        return nil, fmt.Errorf("运单号不能为空")
    }

    // 2. 限流控制
    if err := a.rateLimiter.Wait(ctx); err != nil {
        return nil, fmt.Errorf("限流等待失败: %w", err)
    }

    // 3. 构建查询请求
    trackReq := &YourProviderTrackRequest{
        TrackingNo:  trackingNo,
        ExpressType: expressType,
        Phone:       phone,
    }

    // 4. 调用供应商API
    apiResp, err := a.callTrackAPIWithRetry(ctx, trackReq)
    if err != nil {
        return nil, fmt.Errorf("调用物流跟踪API失败: %w", err)
    }

    // 5. 转换为标准格式
    trackInfo := a.convertToTrackInfo(apiResp)

    a.logger.Info("物流跟踪查询成功",
        zap.String("provider", "your_provider"),
        zap.String("tracking_no", trackingNo),
        zap.String("status", trackInfo.Status))

    return trackInfo, nil
}
```

### 3. 快速启动检查清单

**开发阶段检查清单：**
- [ ] 创建适配器文件结构
- [ ] 实现所有必需接口方法
- [ ] 添加参数验证和错误处理
- [ ] 实现重试和限流机制
- [ ] 编写单元测试
- [ ] 编写集成测试

**配置阶段检查清单：**
- [ ] 在数据库中添加供应商配置
- [ ] 更新适配器工厂
- [ ] 更新动态管理器
- [ ] 添加回调适配器
- [ ] 配置路由和处理器

**测试阶段检查清单：**
- [ ] 价格查询功能测试
- [ ] 订单创建功能测试
- [ ] 订单取消功能测试
- [ ] 物流跟踪功能测试
- [ ] 回调处理功能测试
- [ ] 性能压力测试
- [ ] 错误场景测试

**部署阶段检查清单：**
- [ ] 生产环境配置验证
- [ ] 数据库脚本执行
- [ ] 服务重启或热重载
- [ ] 监控指标配置
- [ ] 日志配置验证
- [ ] 健康检查验证

**上线后检查清单：**
- [ ] 监控供应商API调用成功率
- [ ] 监控响应时间指标
- [ ] 检查错误日志
- [ ] 验证回调处理
- [ ] 确认计费准确性

通过遵循本指南和使用提供的代码模板，您可以快速、安全、高质量地集成新的快递供应商到统一网关系统中。
```
```
```
```
```
