# 工单管理API文档

## 概述

本文档详细说明了Go快递系统的工单管理API接口，包括工单创建、查询、回复等功能的完整使用指南。

> **重要说明**：本文档基于Go快递系统v3.0版本，提供RESTful API接口，支持JWT认证和OAuth 2.0授权。

## 目录

1. [快速开始](#快速开始)
2. [认证授权](#认证授权)
3. [工单管理API](#工单管理api)
4. [回调配置API](#回调配置api)
5. [数据模型](#数据模型)
6. [错误处理](#错误处理)
7. [SDK示例](#sdk示例)

---

## 快速开始

### 基础信息

- **API基础URL**: `https://api.go-kuaidi.com`
- **认证方式**: JWT Bearer Token 或 OAuth 2.0
- **数据格式**: JSON
- **字符编码**: UTF-8

### 快速示例

```bash
# 1. 获取访问令牌
curl -X POST "https://api.go-kuaidi.com/oauth/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "grant_type=client_credentials&client_id=your_client_id&client_secret=your_client_secret&scope=workorder:read workorder:write"

# 2. 创建工单
curl -X POST "https://api.go-kuaidi.com/api/v1/workorders" \
  -H "Authorization: Bearer your_access_token" \
  -H "Content-Type: application/json" \
  -d '{
    "work_order_type": 1,
    "content": "包裹未按时取件，请催促快递员",
    "tracking_no": "SF1234567890"
  }'

# 3. 查询工单
curl -X GET "https://api.go-kuaidi.com/api/v1/workorders/your_work_order_id" \
  -H "Authorization: Bearer your_access_token"
```

---

## 认证授权

### OAuth 2.0 认证

#### 获取访问令牌

```http
POST /oauth/token
Content-Type: application/x-www-form-urlencoded

grant_type=client_credentials&client_id=your_client_id&client_secret=your_client_secret&scope=workorder:read workorder:write
```

**响应示例**:
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "Bearer",
  "expires_in": 3600,
  "scope": "workorder:read workorder:write"
}
```

#### 权限范围

| 权限 | 说明 |
|------|------|
| `workorder:read` | 查询工单信息 |
| `workorder:write` | 创建和回复工单 |
| `callback:read` | 查询回调配置 |
| `callback:write` | 配置回调设置 |

### 用户名密码登录

```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "username": "your_username",
  "password": "your_password"
}
```

**响应示例**:
```json
{
  "success": true,
  "code": 200,
  "message": "Login successful",
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "Bearer",
  "expires_in": 3600,
  "user_info": {
    "id": "user_id",
    "username": "your_username",
    "email": "<EMAIL>",
    "client_id": "your_client_id"
  }
}
```

---

## 工单管理API

### 创建工单

```http
POST /api/v1/workorders
Authorization: Bearer your_access_token
Content-Type: application/json

{
  "work_order_type": 1,
  "content": "包裹未按时取件，请催促快递员",
  "tracking_no": "SF1234567890",
  "feedback_weight": 2.5,
  "goods_value": 100.00,
  "attachment_urls": [
    "https://example.com/image1.jpg"
  ]
}
```

**请求参数**:

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| `work_order_type` | integer | 是 | 工单类型 (1-19) |
| `content` | string | 是 | 问题描述 |
| `order_no` | string | 否 | 客户订单号 |
| `tracking_no` | string | 否 | 运单号 |
| `feedback_weight` | number | 否 | 反馈重量(kg) |
| `goods_value` | number | 否 | 商品价值(元) |
| `overweight_amount` | number | 否 | 超重金额(元) |
| `attachment_urls` | array | 否 | 附件URL列表 |

**响应示例**:
```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "user_id": "user_123",
    "work_order_type": 1,
    "title": "催取件工单",
    "content": "包裹未按时取件，请催促快递员",
    "status": 1,
    "priority": 2,
    "tracking_no": "SF1234567890",
    "provider": "sf",
    "created_at": "2025-07-05T10:30:00Z",
    "type_name": "催取件",
    "status_name": "待处理"
  }
}
```

### 查询工单详情

```http
GET /api/v1/workorders/{id}
Authorization: Bearer your_access_token
```

**响应示例**:
```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "user_id": "user_123",
    "work_order_type": 1,
    "title": "催取件工单",
    "content": "包裹未按时取件，请催促快递员",
    "status": 3,
    "priority": 2,
    "tracking_no": "SF1234567890",
    "provider": "sf",
    "created_at": "2025-07-05T10:30:00Z",
    "updated_at": "2025-07-05T11:00:00Z",
    "type_name": "催取件",
    "status_name": "已回复",
    "replies": [
      {
        "id": "reply_id",
        "work_order_id": "550e8400-e29b-41d4-a716-446655440000",
        "reply_type": 2,
        "content": "您的工单已处理，快递员将在2小时内联系您",
        "committer": "客服",
        "created_at": "2025-07-05T11:00:00Z",
        "attachments": []
      }
    ],
    "attachments": [
      {
        "id": "attachment_id",
        "file_name": "image1.jpg",
        "file_url": "https://example.com/image1.jpg",
        "file_size": 102400,
        "upload_type": 1,
        "created_at": "2025-07-05T10:30:00Z"
      }
    ]
  }
}
```

### 查询工单列表

```http
GET /api/v1/workorders?page=1&page_size=20&status=1&work_order_type=1
Authorization: Bearer your_access_token
```

**查询参数**:

| 参数 | 类型 | 说明 |
|------|------|------|
| `page` | integer | 页码，默认1 |
| `page_size` | integer | 每页数量，默认20 |
| `provider` | string | 供应商筛选 |
| `status` | integer | 工单状态筛选 |
| `work_order_type` | integer | 工单类型筛选 |
| `order_no` | string | 订单号筛选 |
| `tracking_no` | string | 运单号筛选 |
| `start_date` | string | 开始日期 (YYYY-MM-DD) |
| `end_date` | string | 结束日期 (YYYY-MM-DD) |

**响应示例**:
```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {
    "items": [
      {
        "id": "550e8400-e29b-41d4-a716-446655440000",
        "work_order_type": 1,
        "title": "催取件工单",
        "status": 3,
        "tracking_no": "SF1234567890",
        "created_at": "2025-07-05T10:30:00Z",
        "type_name": "催取件",
        "status_name": "已回复",
        "latest_reply": "您的工单已处理，快递员将在2小时内联系您"
      }
    ],
    "total": 1,
    "page": 1,
    "page_size": 20,
    "total_pages": 1
  }
}
```

### 回复工单

```http
POST /api/v1/workorders/{id}/replies
Authorization: Bearer your_access_token
Content-Type: application/json

{
  "content": "感谢处理，已收到快递员电话",
  "attachment_urls": [
    "https://example.com/receipt.jpg"
  ]
}
```

**响应示例**:
```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": "reply_id",
    "work_order_id": "550e8400-e29b-41d4-a716-446655440000",
    "reply_type": 1,
    "content": "感谢处理，已收到快递员电话",
    "created_at": "2025-07-05T12:00:00Z",
    "attachments": [
      {
        "id": "attachment_id",
        "file_name": "receipt.jpg",
        "file_url": "https://example.com/receipt.jpg",
        "file_size": 51200,
        "upload_type": 2,
        "created_at": "2025-07-05T12:00:00Z"
      }
    ]
  }
}
```

### 上传附件

```http
POST /api/v1/workorders/attachments
Authorization: Bearer your_access_token
Content-Type: multipart/form-data

file: [binary data]
```

**响应示例**:
```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {
    "file_url": "https://example.com/uploads/20250705/image.jpg",
    "file_name": "image.jpg",
    "file_size": 102400
  }
}
```

### 删除工单

```http
DELETE /api/v1/workorders/{id}
Authorization: Bearer your_access_token
```

**响应示例**:
```json
{
  "success": true,
  "code": 200,
  "message": "工单删除成功"
}
```

---

## 回调配置API

### 获取回调配置

```http
GET /api/v1/user/callback/config
Authorization: Bearer your_access_token
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "callback_url": "https://your-domain.com/callback/workorder",
    "callback_secret": "your_secret_key",
    "enabled": true,
    "retry_count": 3,
    "timeout_seconds": 30,
    "subscribed_events": [
      "workorder.created",
      "workorder.replied",
      "workorder.status_changed",
      "workorder.closed"
    ]
  }
}
```

### 更新回调配置

```http
POST /api/v1/user/callback/config
Authorization: Bearer your_access_token
Content-Type: application/json

{
  "callback_url": "https://your-domain.com/callback/workorder",
  "callback_secret": "your_secret_key",
  "enabled": true,
  "retry_count": 3,
  "timeout_seconds": 30,
  "subscribed_events": [
    "workorder.replied",
    "workorder.closed"
  ]
}
```

### 测试回调

```http
POST /api/v1/user/callback/test
Authorization: Bearer your_access_token
Content-Type: application/json

{
  "callback_url": "https://your-domain.com/callback/test"
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "测试回调发送成功",
  "data": {
    "status_code": 200,
    "response_time": 150,
    "response_body": "{\"success\":true}"
  }
}
```

### 回调事件类型

| 事件类型 | 事件名称 | 触发条件 |
|----------|----------|----------|
| `workorder.created` | 工单创建 | 工单创建成功时触发 |
| `workorder.replied` | 工单回复 | 收到客服回复时触发 |
| `workorder.status_changed` | 状态变更 | 工单状态改变时触发 |
| `workorder.closed` | 工单关闭 | 工单完结时触发 |

### 回调数据格式

系统会向您配置的回调URL发送POST请求：

```http
POST https://your-domain.com/callback/workorder
Content-Type: application/json
X-Signature: a1b2c3d4e5f6...
X-Event-Type: workorder.replied
X-Timestamp: 1625472600

{
  "event_type": "workorder.replied",
  "event_time": 1625472600,
  "version": "3.0",
  "work_order_id": "550e8400-e29b-41d4-a716-446655440000",
  "work_order_type": 1,
  "work_order_type_name": "催取件",
  "order_no": "ORDER123456",
  "tracking_no": "SF1234567890",
  "status": 3,
  "status_name": "已回复",
  "content": "您的工单已处理，快递员将在2小时内联系您",
  "committer": "客服",
  "attachment_urls": [
    "https://example.com/attachment1.jpg"
  ],
  "updated_at": "2025-07-05T10:30:00Z"
}
```

### 签名验证

回调请求包含MD5签名，验证方法：

```
签名字符串 = event_type + event_time + work_order_id + callback_secret
签名 = MD5(签名字符串).toLowerCase()
```

**验证示例 (PHP)**:
```php
function verifySignature($eventType, $eventTime, $workOrderId, $secret, $signature) {
    $signStr = $eventType . $eventTime . $workOrderId . $secret;
    $expectedSignature = md5($signStr);
    return $signature === $expectedSignature;
}
```

---

## 签名验证

### 签名生成算法

用户回调使用MD5签名验证：

```
签名字符串 = event_type + event_time + work_order_id + callback_secret
签名 = MD5(签名字符串).toLowerCase()
```

### 示例代码

#### Go语言验证示例
```go
import (
    "crypto/md5"
    "fmt"
    "strconv"
)

func verifySignature(eventType string, eventTime int64, workOrderID, secret, signature string) bool {
    signStr := fmt.Sprintf("%s%d%s%s", eventType, eventTime, workOrderID, secret)
    hash := md5.Sum([]byte(signStr))
    expectedSignature := fmt.Sprintf("%x", hash)
    return signature == expectedSignature
}
```

#### PHP验证示例
```php
function verifySignature($eventType, $eventTime, $workOrderId, $secret, $signature) {
    $signStr = $eventType . $eventTime . $workOrderId . $secret;
    $expectedSignature = md5($signStr);
    return $signature === $expectedSignature;
}
```

#### Python验证示例
```python
import hashlib

def verify_signature(event_type, event_time, work_order_id, secret, signature):
    sign_str = f"{event_type}{event_time}{work_order_id}{secret}"
    expected_signature = hashlib.md5(sign_str.encode()).hexdigest()
    return signature == expected_signature
```

### 请求头说明

| 请求头 | 说明 | 示例 |
|--------|------|------|
| `X-Signature` | MD5签名 | `a1b2c3d4e5f6...` |
| `X-Event-Type` | 事件类型 | `workorder.replied` |
| `X-Timestamp` | 时间戳 | `1625472600` |
| `User-Agent` | 用户代理 | `go-kuaidi-callback/3.0` |

---

## 错误处理

### 用户回调响应要求

用户的回调接口应该返回以下格式的响应：

#### 成功响应
```json
{
  "success": true,
  "message": "处理成功"
}
```

#### 失败响应
```json
{
  "success": false,
  "message": "处理失败的原因"
}
```

### 重试机制

- **重试次数**：默认3次，可配置最多5次
- **重试间隔**：5秒、10秒、30秒（指数退避）
- **超时时间**：默认30秒，可配置
- **重试条件**：HTTP状态码非200或响应body中success为false

### 错误状态码

| HTTP状态码 | 处理方式 | 说明 |
|------------|----------|------|
| 200 | 检查响应body | 根据success字段判断 |
| 4xx | 不重试 | 客户端错误，不进行重试 |
| 5xx | 重试 | 服务器错误，进行重试 |
| 超时 | 重试 | 网络超时，进行重试 |

### 供应商回调响应格式

系统对不同供应商返回相应的响应格式：

#### 快递100响应格式
```json
{
  "code": 200,
  "success": true,
  "message": "回调处理成功"
}
```

#### 易达响应格式
```json
{
  "success": true,
  "code": "200",
  "msg": "回调处理成功"
}
```

#### 云通响应格式
```json
{
  "EBusinessID": "your_business_id",
  "Success": true,
  "Reason": "回调处理成功",
  "UpdateTime": "2025-07-05 10:30:00"
}
```

### 错误响应示例

#### 数据解析失败
```json
{
  "success": false,
  "code": "400",
  "message": "回调数据格式错误"
}
```

#### 工单不存在
```json
{
  "success": true,
  "message": "已接收，处理中"
}
```
> 注意：即使工单不存在，系统也会返回成功响应，避免供应商重复推送

---

## 配置示例

### 用户回调配置

#### 获取配置
```http
GET /api/v1/user/callback/config
Authorization: Bearer your_jwt_token

Response:
{
  "success": true,
  "data": {
    "callback_url": "https://your-domain.com/callback/workorder",
    "callback_secret": "your_secret_key",
    "enabled": true,
    "retry_count": 3,
    "timeout_seconds": 30,
    "subscribed_events": [
      "workorder.created",
      "workorder.replied",
      "workorder.status_changed",
      "workorder.closed"
    ]
  }
}
```

#### 更新配置
```http
POST /api/v1/user/callback/config
Authorization: Bearer your_jwt_token
Content-Type: application/json

{
  "callback_url": "https://your-domain.com/callback/workorder",
  "callback_secret": "your_secret_key",
  "enabled": true,
  "retry_count": 3,
  "timeout_seconds": 30,
  "subscribed_events": [
    "workorder.replied",
    "workorder.closed"
  ]
}
```

### 配置字段说明

| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| `callback_url` | string | 是 | 回调接收URL |
| `callback_secret` | string | 否 | 签名密钥 |
| `enabled` | boolean | 是 | 是否启用回调 |
| `retry_count` | integer | 否 | 重试次数(1-5) |
| `timeout_seconds` | integer | 否 | 超时时间(5-60秒) |
| `subscribed_events` | array | 否 | 订阅的事件类型 |

### 测试回调

```http
POST /api/v1/user/callback/test
Authorization: Bearer your_jwt_token
Content-Type: application/json

{
  "callback_url": "https://your-domain.com/callback/test"
}

Response:
{
  "success": true,
  "message": "测试回调发送成功",
  "data": {
    "status_code": 200,
    "response_time": 150,
    "response_body": "{\"success\":true}"
  }
}
```

---

## 最佳实践

### 1. 安全建议
- 始终验证签名
- 使用HTTPS接收回调
- 设置合理的超时时间
- 记录所有回调请求日志

### 2. 性能优化
- 异步处理回调数据
- 避免在回调处理中执行耗时操作
- 合理设置重试次数和间隔

### 3. 错误处理
- 返回明确的错误信息
- 区分临时错误和永久错误
- 实现幂等性处理

### 4. 监控告警
- 监控回调成功率
- 设置回调失败告警
- 定期检查回调配置

---

## 常见问题

### Q: 如何确保回调的幂等性？
A: 系统会在回调数据中包含唯一的`work_order_id`，用户可以根据此ID进行幂等性处理。

### Q: 回调失败后如何重新获取数据？
A: 可以通过工单查询API主动获取最新的工单状态和回复信息。

### Q: 是否支持批量回调？
A: 目前只支持单个工单事件的回调，不支持批量回调。

### Q: 回调URL可以使用内网地址吗？
A: 不建议使用内网地址，建议使用公网可访问的HTTPS地址。

### Q: 如何处理重复的回调？
A: 系统会自动去重，用户端建议根据`work_order_id`和`event_time`进行幂等性处理。

### Q: 回调数据中的供应商信息为什么都显示为"go-kuaidi"？
A: 为了保护商业机密和提供统一体验，系统会隐藏真实的供应商信息。

---

## 开发调试

### 回调日志查看

用户可以通过API查看回调记录：

```http
GET /api/v1/user/callback/records?page=1&page_size=20
Authorization: Bearer your_jwt_token

Response:
{
  "success": true,
  "data": {
    "items": [
      {
        "id": "record_id",
        "work_order_id": "550e8400-e29b-41d4-a716-446655440000",
        "event_type": "workorder.replied",
        "callback_url": "https://your-domain.com/callback",
        "status": "success",
        "http_status": 200,
        "request_at": "2025-07-05T10:30:00Z",
        "response_at": "2025-07-05T10:30:01Z",
        "retry_count": 0,
        "error_message": null
      }
    ],
    "total": 1,
    "page": 1,
    "page_size": 20
  }
}
```

### 本地测试工具

#### 使用ngrok进行本地测试
```bash
# 安装ngrok
npm install -g ngrok

# 启动本地服务
node your-callback-server.js

# 创建公网隧道
ngrok http 3000

# 使用生成的URL配置回调
```

#### 简单的Node.js回调服务器示例
```javascript
const express = require('express');
const crypto = require('crypto');
const app = express();

app.use(express.json());

app.post('/callback/workorder', (req, res) => {
  const signature = req.headers['x-signature'];
  const eventType = req.headers['x-event-type'];
  const timestamp = req.headers['x-timestamp'];

  // 验证签名
  const secret = 'your_callback_secret';
  const { work_order_id } = req.body;
  const signStr = `${eventType}${timestamp}${work_order_id}${secret}`;
  const expectedSignature = crypto.createHash('md5').update(signStr).digest('hex');

  if (signature !== expectedSignature) {
    return res.status(400).json({
      success: false,
      message: '签名验证失败'
    });
  }

  // 处理回调数据
  console.log('收到工单回调:', req.body);

  res.json({
    success: true,
    message: '处理成功'
  });
});

app.listen(3000, () => {
  console.log('回调服务器启动在端口 3000');
});
```

---

## 联系支持

如有问题，请联系技术支持：
- 邮箱：<EMAIL>
- 文档：https://docs.go-kuaidi.com
- API文档：https://api.go-kuaidi.com/docs

---

## 更新日志

### v3.0 (2025-07-05)
- 新增工单回调转发机制
- 统一回调数据格式
- 支持签名验证
- 增加重试机制

### v2.0 (2025-06-01)
- 支持多供应商回调
- 优化错误处理

### v1.0 (2025-05-01)
- 初始版本发布
