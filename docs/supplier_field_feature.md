# 回调转发供应商字段功能

## 功能概述

在统一回调端点（`/api/v1/callbacks/*`）的转发逻辑中新增了供应商标识字段，用于标识回调数据来源的供应商。

## 功能特性

### 1. 配置控制
- **配置组**: `callback`
- **配置键**: `include_supplier_code`
- **配置类型**: `boolean`
- **默认值**: `false` (保持向后兼容性)
- **描述**: 控制回调转发中是否包含供应商标识字段

### 2. 字段定义
- **字段名**: `supplier_code`
- **字段类型**: `string`
- **JSON标签**: `"supplier_code,omitempty"`
- **可选性**: 可选字段，当配置禁用时不会出现在JSON中

### 3. 供应商标识
支持的供应商代码：
- `cainiao` - 菜鸟裹裹
- `kuaidi100` - 快递100
- `yida` - 易达
- `yuntong` - 云通

## 数据结构

### UnifiedCallbackData 结构
```go
type UnifiedCallbackData struct {
    CustomerOrderNo string      `json:"customer_order_no"`           // 客户订单号
    OrderNo         string      `json:"order_no"`                    // 订单号
    PlatformOrderNo string      `json:"platform_order_no"`           // 平台订单号
    TrackingNo      string      `json:"tracking_no"`                 // 运单号
    Timestamp       string      `json:"timestamp"`                   // 时间戳
    EventType       int         `json:"event_type"`                  // 事件类型
    SupplierCode    string      `json:"supplier_code,omitempty"`     // 🔥 新增：供应商标识字段
    Data            interface{} `json:"data"`                        // 事件数据
}
```

## 配置管理

### 数据库配置
```sql
-- 查看回调配置
SELECT config_key, config_value, config_type, description 
FROM system_configs 
WHERE config_group = 'callback' 
ORDER BY display_order;

-- 启用供应商字段
UPDATE system_configs 
SET config_value = 'true' 
WHERE config_group = 'callback' AND config_key = 'include_supplier_code';

-- 禁用供应商字段
UPDATE system_configs 
SET config_value = 'false' 
WHERE config_group = 'callback' AND config_key = 'include_supplier_code';
```

### 动态生效
配置变更后无需重启服务，系统会动态读取最新配置。

## 使用示例

### 启用供应商字段时的回调数据
```json
{
  "customer_order_no": "CUSTOMER_001",
  "order_no": "ORDER_001",
  "platform_order_no": "PLATFORM_001",
  "tracking_no": "TRACK_001",
  "timestamp": "2025-01-20T10:30:00+08:00",
  "event_type": 1,
  "supplier_code": "cainiao",
  "data": {
    "status": {
      "code": "picked_up",
      "name": "已取件"
    },
    "update_time": "2025-01-20T10:30:00+08:00",
    "description": "已取件"
  }
}
```

### 禁用供应商字段时的回调数据
```json
{
  "customer_order_no": "CUSTOMER_001",
  "order_no": "ORDER_001",
  "platform_order_no": "PLATFORM_001",
  "tracking_no": "TRACK_001",
  "timestamp": "2025-01-20T10:30:00+08:00",
  "event_type": 1,
  "data": {
    "status": {
      "code": "picked_up",
      "name": "已取件"
    },
    "update_time": "2025-01-20T10:30:00+08:00",
    "description": "已取件"
  }
}
```

## 向后兼容性

- **默认禁用**: 新功能默认禁用，确保现有系统不受影响
- **可选字段**: 使用 `omitempty` 标签，禁用时字段不会出现在JSON中
- **渐进式启用**: 可以根据需要逐步启用此功能

## 技术实现

### 核心组件
1. **UnifiedCallbackBuilder**: 根据配置决定是否包含供应商字段
2. **SystemConfigService**: 提供配置读取服务
3. **CallbackStandardizer**: 确保所有供应商回调都设置了Provider字段

### 配置读取逻辑
```go
// 获取配置
includeSupplier, err := systemConfigService.GetBoolConfig(ctx, "callback", "include_supplier_code", false)
if err != nil {
    // 配置获取失败时使用默认值false
    includeSupplier = false
}

// 根据配置设置字段
if includeSupplier && data.Provider != "" {
    callbackData.SupplierCode = data.Provider
}
```

## 测试验证

### 单元测试
- 测试数据结构的正确性
- 测试配置开关功能
- 测试向后兼容性

### 集成测试
- 验证数据库配置
- 验证JSON序列化
- 验证实际回调转发

## 注意事项

1. **配置变更**: 修改配置后会立即生效，无需重启服务
2. **性能影响**: 新增字段对性能影响微乎其微
3. **日志记录**: 配置获取失败时会记录警告日志
4. **错误处理**: 配置服务异常时会使用默认值，确保系统稳定性

---

**版本**: v1.0  
**更新时间**: 2025-01-20  
**作者**: Go快递系统开发团队
