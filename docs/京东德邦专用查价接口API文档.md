# 京东德邦专用查价接口API文档

## 接口概述

京东德邦专用查价接口专门用于查询京东快递和德邦快递的实时价格，支持体积重量计算，适用于需要精确计费的业务场景。

**接口地址：** `POST /api/gateway/execute`

**认证方式：** API签名认证（HMAC-SHA256）

**支持快递公司：** 京东快递(JD)、德邦快递(DBL)

---

## 认证方式

### 签名算法
- **算法：** HMAC-SHA256
- **密钥：** 您的API密钥
- **签名参数：** 所有业务参数按字典序排序后拼接

### 签名参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| client_id | string | 是 | 您的客户端ID |
| timestamp | int64 | 是 | 当前时间戳（秒） |
| nonce | string | 是 | 随机字符串，32位 |
| sign | string | 是 | 签名值 |

### 签名示例
```bash
# 1. 按字典序排序所有参数
# 2. 拼接成 key1=value1&key2=value2 格式
# 3. 使用HMAC-SHA256算法签名
# 4. 将签名结果进行Base64编码

# 示例参数
client_id=your_client_id
timestamp=1752408143
nonce=abc123def456ghi789
apiMethod=QUERY_JD_PRICE
clientType=api
businessParams={"sender":{...},"receiver":{...},"weight":1.2}

# 拼接字符串
apiMethod=QUERY_JD_PRICE&businessParams={"sender":{...},"receiver":{...},"weight":1.2}&clientType=api&client_id=your_client_id&nonce=abc123def456ghi789&timestamp=1752408143

# 使用密钥签名
sign = base64(hmac_sha256(secret_key, string_to_sign))
```

---

## 请求参数

### 请求头
```
Content-Type: application/json
```

### 请求体
```json
{
  "apiMethod": "QUERY_REALTIME_PRICE",
  "clientType": "api",
  "client_id": "your_client_id",
  "timestamp": 1752408143,
  "nonce": "abc123def456ghi789",
  "sign": "base64_encoded_signature",
  "businessParams": {
    "sender": {
      "name": "寄件人姓名",
      "mobile": "***********",
      "province": "北京市",
      "city": "北京市",
      "district": "朝阳区",
      "address": "详细地址"
    },
    "receiver": {
      "name": "收件人姓名",
      "mobile": "***********",
      "province": "上海市",
      "city": "上海市",
      "district": "浦东新区",
      "address": "详细地址"
    },
    "weight": 1.2,
    "length": 30.0,
    "width": 20.0,
    "height": 10.0,
    "goods_name": "测试物品"
  }
}
```

### 业务参数说明

#### sender（寄件人信息）
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| name | string | 是 | 寄件人姓名 |
| mobile | string | 是 | 寄件人手机号 |
| province | string | 是 | 寄件省份 |
| city | string | 是 | 寄件城市 |
| district | string | 是 | 寄件区县 |
| address | string | 是 | 详细地址 |

#### receiver（收件人信息）
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| name | string | 是 | 收件人姓名 |
| mobile | string | 是 | 收件人手机号 |
| province | string | 是 | 收件省份 |
| city | string | 是 | 收件城市 |
| district | string | 是 | 收件区县 |
| address | string | 是 | 详细地址 |

#### 包裹信息
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| weight | float | 是 | 重量(kg) |
| length | float | 否 | 长度(cm) |
| width | float | 否 | 宽度(cm) |
| height | float | 否 | 高度(cm) |
| goods_name | string | 否 | 物品名称，默认"物品" |

---

## 响应格式

### 成功响应
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "success": true,
    "code": 200,
    "message": "查询成功",
    "data": [
      {
        "express_code": "JD",
        "express_name": "标快",
        "product_code": "P1",
        "product_name": "标快",
        "price": 26.68,
        "continued_weight_per_kg": 0,
        "calc_weight": 6,
        "order_code": "JD_ORDER_CODE_SkR8SkR8eXVudG9uZ3xKROaZruaVoy1KRlNI5YWo5Zu96YCa55SofFAx",
        "expires_at": "2025-07-13 22:41:42"
      },
      {
        "express_code": "DBL",
        "express_name": "debangkuaidi",
        "product_code": "德邦大件360",
        "product_name": "德邦大件360",
        "price": 41.48,
        "continued_weight_per_kg": 2.72,
        "calc_weight": 13,
        "order_code": "DBL_ORDER_CODE_REJMfERCTHxrdWFpZGkxMDB8a3VhaWRpMTAwX2RlYmFuZ2t1YWlkaXzlvrfpgqblpKfku7YzNjA=",
        "expires_at": "2025-07-13 22:41:42"
      }
    ]
  },
  "success": true
}
```

### 响应字段说明

#### 顶层响应
| 字段名 | 类型 | 说明 |
|--------|------|------|
| code | int | 状态码，200表示成功 |
| msg | string | 状态消息 |
| data | object | 响应数据 |
| success | boolean | 是否成功 |

#### 价格数据
| 字段名 | 类型 | 说明 |
|--------|------|------|
| express_code | string | 快递公司代码（JD/DBL） |
| express_name | string | 快递公司名称 |
| product_code | string | 产品代码 |
| product_name | string | 产品名称 |
| price | float | 总价格（元） |
| continued_weight_per_kg | float | 续重价格（元/kg） |
| calc_weight | float | 计费重量（kg） |
| order_code | string | 下单代码（用于后续下单） |
| expires_at | string | 价格有效期 |

---

## 体积重量计算规则

### 京东快递
- **抛比：** 8000
- **计算公式：** 体积重量 = 长(cm) × 宽(cm) × 高(cm) ÷ 8000
- **计费重量：** max(实际重量, 体积重量)

### 德邦快递
- **抛比：** 6000
- **计算公式：** 体积重量 = 长(cm) × 宽(cm) × 高(cm) ÷ 6000
- **计费重量：** max(实际重量, 体积重量)

### 示例
```json
{
  "weight": 1.0,
  "length": 30.0,
  "width": 20.0,
  "height": 10.0
}
```

**京东快递体积重量：** 30 × 20 × 10 ÷ 8000 = 0.75kg  
**德邦快递体积重量：** 30 × 20 × 10 ÷ 6000 = 1.0kg  
**计费重量：** max(1.0, 0.75) = 1.0kg（京东），max(1.0, 1.0) = 1.0kg（德邦）

---

## 错误码说明

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 400 | 请求参数错误 | 检查必填参数是否完整 |
| 401 | 认证失败 | 检查签名参数和算法 |
| 403 | 权限不足 | 检查API密钥和权限 |
| 500 | 服务器内部错误 | 联系技术支持 |

### 错误响应示例
```json
{
  "code": 400,
  "msg": "操作失败",
  "data": {
    "success": false,
    "code": 400,
    "message": "寄件人手机号不能为空"
  },
  "success": false
}
```

---

## 调用示例

### cURL示例
```bash
curl -X POST "http://your-domain.com/api/gateway/execute" \
  -H "Content-Type: application/json" \
  -d '{
    "apiMethod": "QUERY_REALTIME_PRICE",
    "clientType": "api",
    "client_id": "your_client_id",
    "timestamp": 1752408143,
    "nonce": "abc123def456ghi789",
    "sign": "your_signature",
    "businessParams": {
      "sender": {
        "name": "张三",
        "mobile": "***********",
        "province": "北京市",
        "city": "北京市",
        "district": "朝阳区",
        "address": "某某街道某某号"
      },
      "receiver": {
        "name": "李四",
        "mobile": "***********",
        "province": "上海市",
        "city": "上海市",
        "district": "浦东新区",
        "address": "某某街道某某号"
      },
      "weight": 1.2,
      "length": 30.0,
      "width": 20.0,
      "height": 10.0,
      "goods_name": "测试物品"
    }
  }'
```

### Python示例
```python
import requests
import hashlib
import hmac
import base64
import time
import uuid

def generate_signature(params, secret_key):
    # 按字典序排序参数
    sorted_params = sorted(params.items())
    # 拼接参数字符串
    param_string = '&'.join([f"{k}={v}" for k, v in sorted_params])
    # 使用HMAC-SHA256签名
    signature = hmac.new(
        secret_key.encode('utf-8'),
        param_string.encode('utf-8'),
        hashlib.sha256
    ).digest()
    # Base64编码
    return base64.b64encode(signature).decode('utf-8')

# 请求参数
business_params = {
    "sender": {
        "name": "张三",
        "mobile": "***********",
        "province": "北京市",
        "city": "北京市",
        "district": "朝阳区",
        "address": "某某街道某某号"
    },
    "receiver": {
        "name": "李四",
        "mobile": "***********",
        "province": "上海市",
        "city": "上海市",
        "district": "浦东新区",
        "address": "某某街道某某号"
    },
    "weight": 1.2,
    "length": 30.0,
    "width": 20.0,
    "height": 10.0,
    "goods_name": "测试物品"
}

# 签名参数
params = {
    "apiMethod": "QUERY_JD_PRICE",
    "clientType": "api",
    "client_id": "your_client_id",
    "timestamp": int(time.time()),
    "nonce": str(uuid.uuid4()).replace("-", ""),
    "businessParams": business_params
}

# 生成签名
secret_key = "your_secret_key"
params["sign"] = generate_signature(params, secret_key)

# 发送请求
response = requests.post(
    "http://your-domain.com/api/gateway/execute",
    json=params,
    headers={"Content-Type": "application/json"}
)

print(response.json())
```

---

## 注意事项

1. **时间戳有效期：** 请求时间戳与服务器时间差不能超过5分钟
2. **Nonce唯一性：** 每次请求的nonce必须唯一，建议使用UUID
3. **价格有效期：** 返回的价格有效期为30分钟
4. **体积重量：** 提供长宽高时会自动计算体积重量
5. **计费重量：** 系统会自动选择实际重量和体积重量中的较大值
6. **快递公司：** 此接口固定返回京东快递和德邦快递的价格
7. **实时查询：** 此接口为实时查询，不依赖缓存

---

## 技术支持

如有问题，请联系技术支持：
- 邮箱：<EMAIL>
- 电话：400-xxx-xxxx
- 在线客服：http://your-domain.com/support 