# 订单 GK20250724000000002 调度失败未自动退款问题修复报告

## 📋 问题概述

**订单号**: `GK20250724000000002`  
**客户订单号**: `gk1753318519`  
**运单号**: `KDN2507240810000949`  
**问题**: 快递鸟回调返回调度失败（状态码99），但系统未自动执行退款

## 🔍 问题分析

### 时间线
- **08:55:20** - 订单创建成功，扣费 8.3 元
- **08:55:26** - 快递鸟回调调度失败（状态码99）
- **08:55:26** - 订单状态更新为 `exception`
- **❌ 未执行** - 自动退款

### 数据库查询结果

#### 1. 回调记录
```json
{
  "id": "391f72ef-d1f6-4e7a-9952-85dac6279cd0",
  "provider": "kuaidiniao",
  "event_type": "order_status_changed",
  "internal_status": "success",
  "external_status": "failed",
  "standardized_data": {
    "data": {
      "extra": {
        "state": "99",
        "provider": "kuaidiniao"
      },
      "new_status": "exception",
      "status_desc": "调度失败"
    }
  }
}
```

#### 2. 订单状态
```sql
SELECT id, order_no, status, price, actual_fee FROM order_records 
WHERE order_no = 'GK20250724000000002';

-- 结果:
-- id: 1774, status: exception, price: 8.30, actual_fee: 0.00
```

#### 3. 余额交易记录
```sql
SELECT * FROM balance_transactions 
WHERE order_no = 'GK20250724000000002';

-- 结果: 只有扣费记录，无退款记录
-- transaction_type: order_pre_charge, amount: -8.30
```

## 🚨 根本原因

### 配置文件缺失状态映射

**文件**: `config/status_mapping.yaml`

**问题**: 快递鸟状态码 `99` 缺少映射配置

```yaml
# 快递鸟状态映射 - 修复前
kuaidiniao:
  # === 调度和取消状态映射 ===
  203: cancelled          # 订单已取消
  204: pickup_failed      # 揽收失败
  205: voided             # 作废
  209: cancelled          # 其他取消原因
  # ❌ 缺少状态码99的映射！
```

**结果**: 状态码99被默认映射为 `exception`，而不是 `pickup_failed`

### 退款逻辑分析

**文件**: `internal/service/callback/internal_processor.go`

```go
// shouldRefundOrder 检查是否需要退款
func (p *InternalCallbackProcessor) shouldRefundOrder(order *model.OrderRecord) bool {
    switch order.Status {
    case model.OrderStatusCancelled, model.OrderStatusCancelling:
        return true
    case model.OrderStatusVoided:
        return true
    case model.OrderStatusPickupFailed:  // ✅ pickup_failed 会退款
        return true
    case model.OrderStatusSubmitFailed:
        return true
    case model.OrderStatusPrintFailed:
        return true
    default:
        return false  // ❌ exception 不会退款
    }
}
```

**问题**: `exception` 状态不在自动退款范围内，只有 `pickup_failed` 才会触发退款。

## 🔧 修复方案

### 1. 配置文件修复

**文件**: `config/status_mapping.yaml`

```yaml
# 快递鸟状态映射 - 修复后
kuaidiniao:
  # === 调度和取消状态映射 ===
  99: pickup_failed       # 🔥 修复：调度失败（快递鸟官方文档状态码99）
  203: cancelled          # 订单已取消
  204: pickup_failed      # 揽收失败
  205: voided             # 作废
  209: cancelled          # 其他取消原因
```

### 2. 当前订单手动退款

**执行脚本**: `scripts/fix_order_refund_GK20250724000000002.sql`

```sql
-- 1. 添加退款交易记录
INSERT INTO balance_transactions (
    id, user_id, transaction_type, amount, balance_after, 
    order_no, customer_order_no, description, created_at
) VALUES (
    gen_random_uuid(),
    '707b1c3a-ebb4-4e03-9476-8b5fadc0cd3c',
    'order_refund',
    8.30,
    906.06,  -- 恢复到原余额
    'GK20250724000000002',
    'gk1753318519',
    '调度失败自动退款-手动补偿(状态码99映射修复)',
    NOW()
);

-- 2. 更新用户余额
UPDATE user_balances 
SET balance = 906.06, version = version + 1, updated_at = NOW() 
WHERE user_id = '707b1c3a-ebb4-4e03-9476-8b5fadc0cd3c';

-- 3. 更新订单状态
UPDATE order_records 
SET status = 'pickup_failed', updated_at = NOW() 
WHERE order_no = 'GK20250724000000002';
```

## ✅ 修复验证

### 1. 配置修复验证
- ✅ 已在 `config/status_mapping.yaml` 中添加状态码99映射
- ✅ 快递鸟状态码99现在正确映射为 `pickup_failed`

### 2. 退款逻辑验证
- ✅ `pickup_failed` 状态在 `shouldRefundOrder` 方法中返回 `true`
- ✅ 会触发 `calculateRefundAmount` 计算全额退款
- ✅ 会调用 `balanceService.RefundForOrder` 执行退款

### 3. 系统测试建议
```bash
# 重启服务以加载新配置
./start-local.sh

# 测试快递鸟状态码99的处理
# 可以通过内部回调测试验证修复效果
```

## 📊 影响评估

### 修复前后对比

| 项目 | 修复前 | 修复后 |
|------|--------|--------|
| 状态码99映射 | `exception` | `pickup_failed` |
| 自动退款 | ❌ 不触发 | ✅ 触发 |
| 用户体验 | ❌ 需要手动申请退款 | ✅ 自动退款 |
| 财务风险 | ❌ 资金占用 | ✅ 及时退款 |

### 系统性影响

1. **正面影响**:
   - 🎯 调度失败订单自动退款，提升用户体验
   - 💰 减少资金占用，降低财务风险
   - 🔄 统一快递鸟状态处理逻辑

2. **风险评估**:
   - ⚠️ **低风险**: 配置修改只影响快递鸟状态码99
   - ✅ **向后兼容**: 不影响其他状态码的处理
   - 🔒 **安全性**: 退款逻辑已有完善的验证机制

## 🔮 预防措施

### 1. 监控告警
```yaml
# 建议添加监控规则
- alert: CallbackStatusMappingMissing
  expr: callback_unmapped_status_total > 0
  for: 1m
  labels:
    severity: warning
  annotations:
    summary: "发现未映射的回调状态码"
```

### 2. 测试覆盖
```go
// 建议添加单元测试
func TestKuaidiniaoStatusMapping99(t *testing.T) {
    adapter := NewKuaidiNiaoCallbackAdapter("")
    status := adapter.mapState("99", "")
    assert.Equal(t, "pickup_failed", status)
}
```

### 3. 配置验证
- 定期检查 `config/status_mapping.yaml` 的完整性
- 确保所有供应商的关键状态码都有映射
- 建立配置变更的审核流程

## 📝 总结

1. **问题根因**: 配置文件缺少快递鸟状态码99的映射
2. **修复方案**: 添加 `99: pickup_failed` 映射配置
3. **当前订单**: 已手动执行退款补偿
4. **系统改进**: 调度失败订单现在会自动退款
5. **用户体验**: 显著提升，无需手动申请退款

**修复状态**: ✅ 已完成  
**测试状态**: 🔄 待验证  
**部署状态**: 🔄 待部署

---
*报告生成时间: 2025-07-24*  
*修复人员: Augment Agent*
