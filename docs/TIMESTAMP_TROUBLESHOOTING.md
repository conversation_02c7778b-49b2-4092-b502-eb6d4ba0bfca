# 统一网关 Timestamp 问题排查指南

## 🚨 常见问题及解决方案

### 1. "请求重复，请使用新的timestamp"

**原因**：相同的timestamp被重复使用

**解决方案**：
```javascript
// ❌ 错误：使用固定或重复的timestamp
const timestamp = "1704715845";

// ✅ 正确：每次请求生成新的毫秒级timestamp
const timestamp = Date.now().toString();
```

### 2. "timestamp已过期，请重新生成"

**原因**：timestamp超过30分钟有效期

**解决方案**：
```javascript
// ✅ 确保使用当前时间
const timestamp = Date.now().toString();

// ❌ 避免使用缓存的旧timestamp
// const timestamp = cachedTimestamp; // 可能已过期
```

### 3. "timestamp来自未来，请检查系统时间"

**原因**：客户端时间与服务器时间不同步

**解决方案**：
1. 同步系统时间到北京时间
2. 检查网络延迟
3. 使用服务器返回的时间校准

### 4. "同一秒内请求过于频繁"

**原因**：使用秒级时间戳且单秒内超过50次请求

**解决方案**：
```javascript
// ❌ 秒级时间戳（高并发时可能超限）
const timestamp = Math.floor(Date.now() / 1000).toString();

// ✅ 毫秒级时间戳（推荐，无并发限制）
const timestamp = Date.now().toString();
```

## 🛠️ 最佳实践

### 1. 推荐的timestamp生成方式

```javascript
// 🔥 最佳实践：毫秒级时间戳
function generateTimestamp() {
    return Date.now().toString();
}

// 🔥 高并发场景：确保唯一性
let lastTimestamp = 0;
function generateUniqueTimestamp() {
    let timestamp = Date.now();
    if (timestamp <= lastTimestamp) {
        timestamp = lastTimestamp + 1;
    }
    lastTimestamp = timestamp;
    return timestamp.toString();
}
```

### 2. 完整的API调用示例

```javascript
async function callUnifiedGateway(apiMethod, businessParams) {
    const timestamp = Date.now().toString(); // 毫秒级时间戳
    
    const requestData = {
        clientType: "api",
        username: "your-client-id",
        timestamp: timestamp,
        apiMethod: apiMethod,
        businessParams: businessParams,
        sign: generateSignature(timestamp, businessParams)
    };
    
    try {
        const response = await fetch('/api/gateway/execute', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(requestData)
        });
        
        const result = await response.json();
        
        if (!result.success) {
            // 处理timestamp相关错误
            if (result.code === 'TIMESTAMP_ALREADY_USED') {
                console.warn('时间戳重复，建议使用毫秒级时间戳');
                // 可以自动重试一次
                return callUnifiedGateway(apiMethod, businessParams);
            }
            throw new Error(result.message);
        }
        
        return result;
    } catch (error) {
        console.error('API调用失败:', error.message);
        throw error;
    }
}
```

### 3. 错误处理和重试机制

```javascript
async function callWithRetry(apiMethod, businessParams, maxRetries = 3) {
    for (let i = 0; i < maxRetries; i++) {
        try {
            return await callUnifiedGateway(apiMethod, businessParams);
        } catch (error) {
            if (error.message.includes('timestamp') && i < maxRetries - 1) {
                console.warn(`第${i + 1}次重试，原因: ${error.message}`);
                await new Promise(resolve => setTimeout(resolve, 100)); // 等待100ms
                continue;
            }
            throw error;
        }
    }
}
```

## 🔍 调试工具

### 1. 检查timestamp格式

```javascript
function validateTimestamp(timestamp) {
    // 检查是否为数字
    if (!/^\d+$/.test(timestamp)) {
        return { valid: false, error: 'timestamp必须是纯数字' };
    }
    
    const ts = parseInt(timestamp);
    const now = Date.now();
    
    // 判断是秒级还是毫秒级
    if (ts > 1e12) {
        // 毫秒级
        const diff = Math.abs(now - ts);
        if (diff > 10 * 60 * 1000) { // 10分钟
            return { valid: false, error: '毫秒级时间戳已过期或来自未来' };
        }
        return { valid: true, type: 'millisecond', diff: diff };
    } else {
        // 秒级
        const nowSec = Math.floor(now / 1000);
        const diff = Math.abs(nowSec - ts);
        if (diff > 10 * 60) { // 10分钟
            return { valid: false, error: '秒级时间戳已过期或来自未来' };
        }
        return { valid: true, type: 'second', diff: diff };
    }
}

// 使用示例
const timestamp = Date.now().toString();
const validation = validateTimestamp(timestamp);
console.log('Timestamp验证结果:', validation);
```

### 2. 获取服务器时间

```javascript
async function getServerTime() {
    try {
        const response = await fetch('/api/gateway/execute', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                clientType: "api",
                username: "test",
                timestamp: "0", // 故意使用无效timestamp
                apiMethod: "QUERY_PRICE",
                businessParams: {},
                sign: "test"
            })
        });
        
        const result = await response.json();
        if (result.server_time) {
            console.log('服务器时间（毫秒）:', result.server_time);
            console.log('本地时间（毫秒）:', Date.now());
            console.log('时间差（毫秒）:', Date.now() - result.server_time);
        }
    } catch (error) {
        console.error('获取服务器时间失败:', error);
    }
}
```

## 📊 系统优化

### 🚀 大幅优化的timestamp验证机制特性：

1. **大幅扩展有效期**：从5分钟扩展到**30分钟**，适应高并发和网络延迟
2. **智能重复检查**：
   - 毫秒级时间戳：严格唯一性检查，30分钟过期
   - 秒级时间戳：允许同一秒内最多**50次请求**，适应高并发订单场景
3. **增强错误信息**：提供详细的错误原因和解决建议
4. **大幅放宽时钟偏差容忍**：允许**2分钟**的时钟偏差，适应分布式环境
5. **服务器时间返回**：错误响应中包含服务器当前时间
6. **智能日志记录**：超过20次/秒时记录警告，超过50次/秒才拒绝请求

### 监控和日志：

系统会记录详细的timestamp验证日志，便于问题排查：

```
[DEBUG] 统一网关timestamp验证通过 client_id=test timestamp=1704715845123 is_millisec=true validity=30m0s
[WARN] 同一秒内高频请求 client_id=test timestamp=1704715845 count=25
[WARN] 同一秒内请求次数过多 client_id=test timestamp=1704715845 count=51
[ERROR] timestamp已过期 client_id=test timestamp=1704715845 validity=30m0s
```

## 🎯 迁移建议

如果您当前使用秒级时间戳，建议立即升级到毫秒级：

```javascript
// 修改前
function generateTimestamp() {
    return Math.floor(Date.now() / 1000).toString(); // 秒级
}

// 修改后
function generateTimestamp() {
    return Date.now().toString(); // 毫秒级
}
```

这样可以显著减少timestamp重复的问题，提高API调用成功率。
