# 批量查询功能性能优化指南

## 📋 概述

本文档提供了批量运单号查询功能的性能优化建议和最佳实践，确保系统在高并发和大数据量场景下的稳定运行。

## 🎯 性能目标

### 响应时间目标
- **单次批量查询（≤20个运单号）**: < 200ms
- **大批量查询（21-50个运单号）**: < 500ms
- **并发查询（10个并发用户）**: 平均响应时间 < 300ms

### 吞吐量目标
- **峰值QPS**: ≥ 100 requests/second
- **并发用户数**: ≥ 50 concurrent users
- **错误率**: < 1%

## 🔧 数据库优化

### 1. 核心索引优化

```sql
-- 批量查询核心索引
CREATE INDEX CONCURRENTLY idx_order_records_user_tracking_batch 
ON order_records (user_id, tracking_no) 
WHERE tracking_no IS NOT NULL AND tracking_no != '';

-- 覆盖索引，减少回表查询
CREATE INDEX CONCURRENTLY idx_order_records_batch_covering 
ON order_records (user_id, tracking_no, status, created_at) 
INCLUDE (id, platform_order_no, order_no, express_type, provider, price, actual_fee)
WHERE tracking_no IS NOT NULL AND tracking_no != '';
```

### 2. 查询优化策略

#### PostgreSQL ANY操作符优化
```sql
-- 优化前：多个OR条件
SELECT * FROM order_records 
WHERE user_id = ? AND (tracking_no = ? OR tracking_no = ? OR tracking_no = ?)

-- 优化后：使用ANY操作符
SELECT * FROM order_records 
WHERE user_id = ? AND tracking_no = ANY(?)
```

#### 分页查询优化
```sql
-- 使用游标分页替代OFFSET分页（大偏移量时）
SELECT * FROM order_records 
WHERE user_id = ? AND id > ? 
ORDER BY id LIMIT 20;
```

### 3. 数据库配置优化

```sql
-- 工作内存优化（用于排序和哈希操作）
ALTER SYSTEM SET work_mem = '256MB';

-- 共享缓冲区优化
ALTER SYSTEM SET shared_buffers = '2GB';

-- SSD存储优化
ALTER SYSTEM SET random_page_cost = 1.1;

-- 有效缓存大小
ALTER SYSTEM SET effective_cache_size = '8GB';
```

## 🚀 应用层优化

### 1. 连接池配置

```go
// 数据库连接池优化
db.SetMaxOpenConns(100)        // 最大连接数
db.SetMaxIdleConns(20)         // 最大空闲连接数
db.SetConnMaxLifetime(time.Hour) // 连接最大生存时间
db.SetConnMaxIdleTime(30 * time.Minute) // 连接最大空闲时间
```

### 2. 查询优化

#### 批量大小限制
```go
const (
    MaxBatchSize = 50  // 最大批量查询数量
    OptimalBatchSize = 20  // 最优批量查询数量
)
```

#### 查询缓存策略
```go
// Redis缓存热点查询结果
func (s *OrderService) getCachedBatchQuery(trackingNos []string) (*model.OrderListData, bool) {
    cacheKey := fmt.Sprintf("batch_query:%s", strings.Join(trackingNos, ","))
    // 缓存逻辑...
}
```

### 3. 并发控制

#### 限流器配置
```go
// 使用令牌桶限流器
rateLimiter := rate.NewLimiter(rate.Limit(100), 200) // 100 QPS，突发200

// 在处理器中使用
if !rateLimiter.Allow() {
    return errors.New("请求频率过高，请稍后重试")
}
```

#### 超时控制
```go
// 设置查询超时
ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
defer cancel()

result, err := s.orderRepository.ListWithFilter(ctx, req)
```

## 📊 监控和告警

### 1. 关键指标监控

```go
// Prometheus指标定义
var (
    batchQueryDuration = prometheus.NewHistogramVec(
        prometheus.HistogramOpts{
            Name: "batch_query_duration_seconds",
            Help: "批量查询响应时间分布",
            Buckets: []float64{0.1, 0.2, 0.5, 1.0, 2.0, 5.0},
        },
        []string{"batch_size", "status"},
    )
    
    batchQueryCounter = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "batch_query_total",
            Help: "批量查询请求总数",
        },
        []string{"batch_size", "status"},
    )
)
```

### 2. 性能告警规则

```yaml
# Prometheus告警规则
groups:
- name: batch_query_performance
  rules:
  - alert: BatchQueryHighLatency
    expr: histogram_quantile(0.95, batch_query_duration_seconds) > 1.0
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "批量查询响应时间过高"
      
  - alert: BatchQueryHighErrorRate
    expr: rate(batch_query_total{status="error"}[5m]) / rate(batch_query_total[5m]) > 0.05
    for: 2m
    labels:
      severity: critical
    annotations:
      summary: "批量查询错误率过高"
```

## 🔍 性能测试

### 1. 基准测试

```bash
# 运行性能测试脚本
./scripts/test_batch_query.sh

# 数据库索引优化
psql -d go_kuaidi -f scripts/optimize_batch_query_indexes.sql
```

### 2. 压力测试

```bash
# 使用Apache Bench进行压力测试
ab -n 1000 -c 10 -H "Authorization: Bearer $TOKEN" \
   "http://localhost:8080/api/v1/express/orders?tracking_nos=SF1,YT2,ZTO3"

# 使用wrk进行更复杂的压力测试
wrk -t12 -c400 -d30s --script=batch_query_test.lua http://localhost:8080/
```

### 3. 性能分析

```go
// 使用pprof进行性能分析
import _ "net/http/pprof"

// 在main函数中启动pprof服务器
go func() {
    log.Println(http.ListenAndServe("localhost:6060", nil))
}()
```

## 📈 性能优化检查清单

### 数据库层面
- [ ] 创建批量查询核心索引
- [ ] 创建覆盖索引减少回表查询
- [ ] 优化PostgreSQL配置参数
- [ ] 定期执行ANALYZE更新统计信息
- [ ] 监控索引使用情况

### 应用层面
- [ ] 配置合适的数据库连接池
- [ ] 实现查询结果缓存
- [ ] 添加请求限流器
- [ ] 设置合理的查询超时
- [ ] 实现批量大小限制

### 监控层面
- [ ] 配置性能指标监控
- [ ] 设置性能告警规则
- [ ] 实现慢查询日志
- [ ] 配置错误率监控
- [ ] 建立性能基线

### 测试层面
- [ ] 执行功能测试
- [ ] 进行性能基准测试
- [ ] 执行并发压力测试
- [ ] 验证错误处理机制
- [ ] 测试极限场景

## 🚨 故障排查

### 常见性能问题

1. **响应时间过长**
   - 检查数据库索引是否生效
   - 分析SQL执行计划
   - 检查数据库连接池配置
   - 监控服务器资源使用情况

2. **并发性能差**
   - 检查数据库连接池大小
   - 分析锁等待情况
   - 检查应用层限流配置
   - 监控数据库连接数

3. **内存使用过高**
   - 检查查询结果集大小
   - 分析内存泄漏情况
   - 优化数据结构
   - 调整垃圾回收参数

### 性能调优步骤

1. **识别瓶颈**
   ```bash
   # 查看慢查询
   SELECT query, mean_time, calls FROM pg_stat_statements 
   WHERE query LIKE '%tracking_no = ANY%' 
   ORDER BY mean_time DESC;
   ```

2. **分析执行计划**
   ```sql
   EXPLAIN (ANALYZE, BUFFERS) 
   SELECT * FROM order_records 
   WHERE user_id = ? AND tracking_no = ANY(?);
   ```

3. **优化查询**
   - 添加或调整索引
   - 重写查询语句
   - 优化数据模型

4. **验证改进**
   - 重新运行性能测试
   - 对比优化前后指标
   - 监控生产环境表现

## 📚 参考资源

- [PostgreSQL性能调优指南](https://wiki.postgresql.org/wiki/Performance_Optimization)
- [Go数据库最佳实践](https://go.dev/doc/database/manage-connections)
- [Prometheus监控最佳实践](https://prometheus.io/docs/practices/)
- [系统性能测试指南](https://github.com/wg/wrk)

---

**注意**: 在生产环境中应用这些优化建议前，请务必在测试环境中验证效果，并做好数据备份。
