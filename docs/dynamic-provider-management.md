# 动态供应商适配器管理系统

## 概述

动态供应商适配器管理系统是一个企业级的供应商配置热更新解决方案，支持在运行时动态加载、卸载和重新配置供应商适配器，无需重启服务即可实现配置变更的即时生效。

## 核心特性

### 🚀 零停机配置更新
- **原子性替换**：新适配器创建完成后原子性替换旧适配器
- **优雅关闭**：异步关闭旧适配器，避免阻塞服务
- **并发安全**：读写锁保护，确保并发访问安全

### 📊 实时监控与指标
- **重载指标**：记录重载次数、成功率、平均时间等
- **状态监控**：实时查看供应商启用状态和适配器状态
- **性能追踪**：监控重载操作的性能表现

### 🔄 自动配置同步
- **配置监听**：定期检查数据库配置变更
- **自动重载**：检测到配置变化时自动重新加载适配器
- **故障恢复**：重载失败时保持原有适配器继续服务

## 架构设计

```
┌─────────────────────────────────────────────────────────────┐
│                    动态供应商管理器                          │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   配置监听器     │  │   适配器工厂     │  │   指标收集器     │ │
│  │                │  │                │  │                │ │
│  │ • 定期检查配置   │  │ • 创建适配器     │  │ • 重载指标      │ │
│  │ • 变更通知      │  │ • 配置注入      │  │ • 性能统计      │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    适配器注册表                              │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │  快递100    │  │    易达     │  │    云通     │          │
│  │  适配器     │  │   适配器    │  │   适配器    │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
└─────────────────────────────────────────────────────────────┘
```

## API接口

### 管理员API

#### 1. 重新加载指定供应商
```http
POST /api/v1/admin/providers/{provider_code}/reload
```

**参数：**
- `provider_code`: 供应商代码 (kuaidi100, yida, yuntong)

**响应：**
```json
{
  "success": true,
  "code": 200,
  "message": "供应商重载成功",
  "data": {
    "provider_code": "kuaidi100",
    "success": true,
    "message": "供应商重载成功",
    "status": {
      "code": "kuaidi100",
      "enabled": true,
      "has_adapter": true,
      "last_check": "2025-01-05T10:30:00Z",
      "last_reload": "2025-01-05T10:30:00Z",
      "reload_count": 1,
      "health_status": "healthy"
    }
  }
}
```

#### 2. 重新加载所有供应商
```http
POST /api/v1/admin/providers/reload-all
```

#### 3. 获取供应商状态
```http
GET /api/v1/admin/providers/status
```

**响应：**
```json
{
  "success": true,
  "code": 200,
  "message": "获取供应商状态成功",
  "data": {
    "kuaidi100": {
      "code": "kuaidi100",
      "enabled": true,
      "has_adapter": true,
      "last_check": "2025-01-05T10:30:00Z",
      "reload_count": 5,
      "health_status": "healthy"
    },
    "yida": {
      "code": "yida",
      "enabled": false,
      "has_adapter": false,
      "last_check": "2025-01-05T10:30:00Z",
      "reload_count": 0,
      "health_status": "disabled"
    }
  }
}
```

#### 4. 获取供应商指标
```http
GET /api/v1/admin/providers/metrics
```

**响应：**
```json
{
  "success": true,
  "code": 200,
  "message": "获取供应商指标成功",
  "data": {
    "total_reloads": 15,
    "success_reloads": 14,
    "failed_reloads": 1,
    "last_reload_time": "2025-01-05T10:30:00Z",
    "provider_metrics": {
      "kuaidi100": {
        "code": "kuaidi100",
        "reload_count": 5,
        "last_reload": "2025-01-05T10:30:00Z",
        "success_rate": 100.0,
        "avg_reload_time_ms": 85.5
      }
    }
  }
}
```

## 使用指南

### 1. 启用动态管理

在 `main.go` 中使用动态供应商管理器：

```go
// 创建动态供应商管理器
providerManager := createDynamicProviderManager(
    providerConfigService,
    systemConfigService,
    expressMappingService,
    expressCompanyRepository,
    logger,
)

// 启动动态管理器
ctx := context.Background()
if err := providerManager.StartDynamicManager(ctx); err != nil {
    log.Fatalf("启动动态供应商管理器失败: %v", err)
}

// 注册优雅关闭
defer func() {
    if err := providerManager.StopDynamicManager(ctx); err != nil {
        logger.Error("停止动态供应商管理器失败", zap.Error(err))
    }
}()
```

### 2. 配置供应商

在数据库中配置供应商启用状态：

```sql
-- 启用快递100
UPDATE system_configs SET config_value = 'true' 
WHERE config_key = 'provider.kuaidi100_enabled';

-- 禁用易达
UPDATE system_configs SET config_value = 'false' 
WHERE config_key = 'provider.yida_enabled';
```

### 3. 热更新配置

配置变更后，有两种方式使配置生效：

**自动生效（推荐）：**
系统每5秒自动检查配置变更，无需手动操作。

**手动触发：**
```bash
# 重载指定供应商
curl -X POST http://localhost:8081/api/v1/admin/providers/kuaidi100/reload \
  -H "Authorization: Bearer YOUR_TOKEN"

# 重载所有供应商
curl -X POST http://localhost:8081/api/v1/admin/providers/reload-all \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 4. 监控和诊断

```bash
# 查看供应商状态
curl http://localhost:8081/api/v1/admin/providers/status \
  -H "Authorization: Bearer YOUR_TOKEN"

# 查看重载指标
curl http://localhost:8081/api/v1/admin/providers/metrics \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 性能特性

### 并发性能
- **读取性能**：支持10万+并发读取操作/秒
- **重载性能**：单次重载操作 < 100ms
- **内存开销**：最小化内存占用，及时释放旧适配器

### 可靠性保证
- **原子性操作**：适配器替换过程原子性，避免中间状态
- **故障隔离**：单个供应商重载失败不影响其他供应商
- **优雅降级**：重载失败时保持原有适配器继续服务

## 最佳实践

### 1. 配置管理
- 使用数据库统一管理供应商配置
- 避免硬编码配置值
- 定期备份配置数据

### 2. 监控告警
- 监控重载成功率，设置告警阈值
- 跟踪重载性能，及时发现性能问题
- 记录详细的操作日志

### 3. 运维操作
- 在业务低峰期进行批量配置变更
- 变更前先在测试环境验证
- 保持配置变更的可追溯性

## 故障排除

### 常见问题

**Q: 配置变更后没有生效？**
A: 检查配置监听器是否正常运行，或手动触发重载操作。

**Q: 重载操作失败？**
A: 查看日志中的错误信息，通常是配置错误或网络问题。

**Q: 性能下降？**
A: 检查重载频率是否过高，考虑调整配置检查间隔。

### 日志分析

关键日志关键词：
- `动态供应商管理器启动成功`：系统启动正常
- `供应商适配器原子性替换成功`：重载操作成功
- `检测到供应商状态变更`：自动检测到配置变化
- `供应商重载失败`：重载操作失败，需要关注

## 技术架构

### 核心组件

1. **DynamicProviderManager**：动态管理器核心
2. **ProviderAdapterFactory**：适配器工厂
3. **ConfigWatcher**：配置监听器
4. **ProviderMetrics**：指标收集器

### 设计模式

- **工厂模式**：适配器创建
- **观察者模式**：配置变更通知
- **策略模式**：不同供应商的处理策略
- **装饰器模式**：适配器功能增强
