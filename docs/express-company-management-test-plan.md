# 快递公司管理功能测试计划和验收标准

## 📋 测试概述

### 测试目标
验证快递公司的启用/禁用功能在不重启服务的情况下能够正常工作，并确保查价接口能够正确显示可用的快递公司列表。

### 测试范围
1. **动态配置管理**：快递公司状态变更无需重启服务即可生效
2. **查价接口响应**：启用/禁用状态变更后，查价接口能够实时反映快递公司可用性
3. **数据库驱动配置**：所有配置变更通过数据库实现，符合系统架构要求
4. **多供应商支持**：测试涵盖菜鸟、快递100、易达、云通等所有集成供应商

### 验收标准
1. **实时生效**：快递公司状态变更后，无需重启服务，查价接口立即反映变更
2. **接口一致性**：所有查价相关接口（统一网关、单独供应商接口）都能正确过滤禁用的快递公司
3. **数据完整性**：状态变更不影响现有订单的跟踪和回调功能
4. **性能要求**：配置变更对系统性能无明显影响，API响应时间保持在200ms以内

## 🏗️ 系统架构分析

### 核心组件
1. **ExpressCompanyStatusManager**：统一状态管理器，负责快递公司状态检查
2. **DynamicProviderManager**：动态供应商管理器，支持热重载
3. **StatusChangeListener**：状态变更监听器，自动触发配置重载
4. **RedisDistributedCacheService**：分布式缓存服务，确保状态同步

### 数据库表结构
- `express_companies`：快递公司基础信息表（包含is_active字段）
- `express_providers`：供应商信息表
- `express_company_provider_mappings`：快递公司与供应商映射关系表
- `system_configs`：系统配置表（供应商启用状态）

### 关键API接口
- **管理员接口**：`PUT /api/v1/admin/express/companies/{id}` - 更新快递公司状态
- **查价接口**：`POST /api/v1/express/price` - 统一查价接口
- **公司列表**：`GET /api/v1/express/companies` - 获取启用的快递公司列表
- **供应商重载**：`POST /api/v1/admin/providers/reload-all` - 手动触发重载

## 🧪 测试用例设计

### TC001: 快递公司禁用功能测试
**测试目标**：验证快递公司禁用后，查价接口不再返回该快递公司的价格

**前置条件**：
- 服务正常运行
- 申通快递（STO）当前为启用状态
- 至少有一个供应商支持申通快递

**测试步骤**：
1. 调用查价接口，验证申通快递价格正常返回
2. 通过管理员接口禁用申通快递
3. 等待5秒（配置监听器检测周期）
4. 再次调用查价接口，验证申通快递价格不再返回
5. 验证其他快递公司价格正常返回

**预期结果**：
- 禁用前：查价接口返回申通快递价格
- 禁用后：查价接口不返回申通快递价格，其他快递公司正常

### TC002: 快递公司启用功能测试
**测试目标**：验证快递公司启用后，查价接口能够返回该快递公司的价格

**前置条件**：
- 服务正常运行
- 韵达快递（YD）当前为禁用状态
- 至少有一个供应商支持韵达快递

**测试步骤**：
1. 调用查价接口，验证韵达快递价格不返回
2. 通过管理员接口启用韵达快递
3. 等待5秒（配置监听器检测周期）
4. 再次调用查价接口，验证韵达快递价格正常返回

**预期结果**：
- 启用前：查价接口不返回韵达快递价格
- 启用后：查价接口返回韵达快递价格

### TC003: 多供应商一致性测试
**测试目标**：验证快递公司状态变更在所有供应商中保持一致

**前置条件**：
- 服务正常运行
- 中通快递（ZTO）在多个供应商中都有映射关系

**测试步骤**：
1. 分别调用各供应商的查价接口，验证中通快递价格都能返回
2. 禁用中通快递
3. 等待5秒
4. 分别调用各供应商的查价接口，验证中通快递价格都不返回

**预期结果**：
- 所有供应商的行为保持一致
- 禁用后，所有供应商都不返回中通快递价格

### TC004: 性能影响测试
**测试目标**：验证状态变更对系统性能的影响

**前置条件**：
- 服务正常运行
- 准备性能监控工具

**测试步骤**：
1. 记录变更前的API响应时间基线
2. 执行快递公司状态变更操作
3. 在变更后立即进行高频查价请求（100次/秒，持续1分钟）
4. 记录API响应时间和成功率

**预期结果**：
- API响应时间保持在200ms以内
- 成功率保持在99%以上
- 无明显性能抖动

### TC005: 缓存一致性测试
**测试目标**：验证分布式缓存的一致性

**前置条件**：
- Redis缓存服务正常运行
- 多个应用实例（如果有）

**测试步骤**：
1. 在实例A上禁用快递公司
2. 立即在实例B上调用查价接口
3. 验证缓存失效和状态同步

**预期结果**：
- 缓存能够及时失效
- 所有实例的状态保持一致

## 🔧 测试环境准备

### 环境配置
```bash
# 数据库连接
DATABASE_URL="*************************************************/go_kuaidi"

# 服务端口
SERVER_PORT="8081"

# 测试账号
管理员账号: admin
管理员密码: **********+.aA..@
```

### 启动服务
```bash
cd /Users/<USER>/Desktop/go-kuaidi-*********
./start-local.sh
```

### 测试数据准备
```sql
-- 确保测试快递公司存在且状态已知
UPDATE express_companies SET is_active = true WHERE code = 'STO';
UPDATE express_companies SET is_active = false WHERE code = 'YD';
UPDATE express_companies SET is_active = true WHERE code = 'ZTO';

-- 验证供应商映射关系
SELECT c.code, c.name, c.is_active, p.code as provider_code, m.is_supported
FROM express_companies c
JOIN express_company_provider_mappings m ON c.id = m.company_id
JOIN express_providers p ON m.provider_id = p.id
WHERE c.code IN ('STO', 'YD', 'ZTO')
ORDER BY c.code, p.code;
```

## 📝 测试执行指南

### 手动测试步骤

#### 1. 登录管理员账号
```bash
curl -X POST http://localhost:8081/api/v1/admin/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "**********+.aA..@"
  }'
```

#### 2. 获取快递公司列表
```bash
curl -X GET http://localhost:8081/api/v1/admin/express/companies \
  -H "Authorization: Bearer {TOKEN}" \
  -H "Content-Type: application/json"
```

#### 3. 更新快递公司状态
```bash
# 禁用申通快递
curl -X PUT http://localhost:8081/api/v1/admin/express/companies/{STO_ID} \
  -H "Authorization: Bearer {TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "申通快递",
    "is_active": false,
    "sort_order": 0,
    "volume_weight_ratio": 8000
  }'
```

#### 4. 测试查价接口
```bash
# 统一查价接口
curl -X POST http://localhost:8081/api/v1/express/price \
  -H "Authorization: Bearer {USER_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "sender": {
      "province": "广东省",
      "city": "深圳市",
      "area": "南山区"
    },
    "receiver": {
      "province": "北京市",
      "city": "北京市",
      "area": "朝阳区"
    },
    "package": {
      "weight": 1.0,
      "length": 10,
      "width": 10,
      "height": 10
    },
    "query_all_companies": true
  }'
```

### 自动化测试脚本

#### 测试脚本框架
```bash
#!/bin/bash
# express_company_test.sh

set -e

# 配置
BASE_URL="http://localhost:8081"
ADMIN_USERNAME="admin"
ADMIN_PASSWORD="**********+.aA..@"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 登录获取Token
login_admin() {
    log_info "正在登录管理员账号..."
    ADMIN_TOKEN=$(curl -s -X POST "$BASE_URL/api/v1/admin/auth/login" \
        -H "Content-Type: application/json" \
        -d "{\"username\":\"$ADMIN_USERNAME\",\"password\":\"$ADMIN_PASSWORD\"}" \
        | jq -r '.data.token')
    
    if [ "$ADMIN_TOKEN" = "null" ] || [ -z "$ADMIN_TOKEN" ]; then
        log_error "管理员登录失败"
        exit 1
    fi
    log_info "管理员登录成功"
}

# 获取快递公司ID
get_company_id() {
    local company_code=$1
    local company_id=$(curl -s -X GET "$BASE_URL/api/v1/admin/express/companies" \
        -H "Authorization: Bearer $ADMIN_TOKEN" \
        | jq -r ".data.companies[] | select(.code==\"$company_code\") | .id")
    echo "$company_id"
}

# 更新快递公司状态
update_company_status() {
    local company_id=$1
    local is_active=$2
    local company_name=$3
    
    log_info "正在更新快递公司状态: $company_name -> $is_active"
    
    curl -s -X PUT "$BASE_URL/api/v1/admin/express/companies/$company_id" \
        -H "Authorization: Bearer $ADMIN_TOKEN" \
        -H "Content-Type: application/json" \
        -d "{
            \"name\": \"$company_name\",
            \"is_active\": $is_active,
            \"sort_order\": 0,
            \"volume_weight_ratio\": 8000
        }" > /dev/null
    
    log_info "快递公司状态更新完成"
}

# 测试查价接口
test_price_query() {
    local expected_company_code=$1
    local should_exist=$2
    
    log_info "正在测试查价接口..."
    
    local response=$(curl -s -X POST "$BASE_URL/api/v1/express/price" \
        -H "Authorization: Bearer $USER_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
            "sender": {
                "province": "广东省",
                "city": "深圳市",
                "area": "南山区"
            },
            "receiver": {
                "province": "北京市",
                "city": "北京市",
                "area": "朝阳区"
            },
            "package": {
                "weight": 1.0,
                "length": 10,
                "width": 10,
                "height": 10
            },
            "query_all_companies": true
        }')
    
    local found=$(echo "$response" | jq -r ".data[] | select(.express_code==\"$expected_company_code\") | .express_code")
    
    if [ "$should_exist" = "true" ]; then
        if [ "$found" = "$expected_company_code" ]; then
            log_info "✅ 测试通过：快递公司 $expected_company_code 在查价结果中"
        else
            log_error "❌ 测试失败：快递公司 $expected_company_code 未在查价结果中"
            return 1
        fi
    else
        if [ "$found" = "$expected_company_code" ]; then
            log_error "❌ 测试失败：快递公司 $expected_company_code 不应在查价结果中"
            return 1
        else
            log_info "✅ 测试通过：快递公司 $expected_company_code 不在查价结果中"
        fi
    fi
}

# 主测试流程
main() {
    log_info "开始快递公司管理功能测试"
    
    # 登录
    login_admin
    
    # 获取测试快递公司ID
    STO_ID=$(get_company_id "STO")
    if [ -z "$STO_ID" ]; then
        log_error "未找到申通快递公司"
        exit 1
    fi
    
    # TC001: 快递公司禁用功能测试
    log_info "执行 TC001: 快递公司禁用功能测试"
    
    # 1. 启用申通快递
    update_company_status "$STO_ID" "true" "申通快递"
    sleep 5  # 等待配置生效
    
    # 2. 测试查价接口（应该包含申通）
    test_price_query "STO" "true"
    
    # 3. 禁用申通快递
    update_company_status "$STO_ID" "false" "申通快递"
    sleep 5  # 等待配置生效
    
    # 4. 测试查价接口（不应包含申通）
    test_price_query "STO" "false"
    
    log_info "TC001 测试完成"
    
    # 恢复状态
    update_company_status "$STO_ID" "true" "申通快递"
    
    log_info "所有测试完成"
}

# 执行测试
main
```

## 📊 测试报告模板

### 测试执行记录
```
测试日期：2025-01-XX
测试环境：本地开发环境
测试人员：[测试人员姓名]
服务版本：go-kuaidi-*********

测试结果汇总：
- 总测试用例数：5
- 通过用例数：X
- 失败用例数：X
- 跳过用例数：X

详细结果：
TC001: 快递公司禁用功能测试 - [PASS/FAIL]
TC002: 快递公司启用功能测试 - [PASS/FAIL]
TC003: 多供应商一致性测试 - [PASS/FAIL]
TC004: 性能影响测试 - [PASS/FAIL]
TC005: 缓存一致性测试 - [PASS/FAIL]

问题记录：
1. [问题描述]
   - 复现步骤：
   - 预期结果：
   - 实际结果：
   - 严重程度：

修复建议：
1. [修复建议]
```

## 🚀 持续集成建议

### CI/CD集成
```yaml
# .github/workflows/express-company-test.yml
name: Express Company Management Test

on:
  push:
    paths:
      - 'internal/express/**'
      - 'api/handler/express_company_handler.go'
  pull_request:
    paths:
      - 'internal/express/**'

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      
      - name: Setup Go
        uses: actions/setup-go@v2
        with:
          go-version: 1.23.0
          
      - name: Start Test Database
        run: |
          docker run -d --name test-postgres \
            -e POSTGRES_PASSWORD=testpass \
            -e POSTGRES_DB=go_kuaidi_test \
            -p 5432:5432 postgres:13
            
      - name: Run Express Company Tests
        run: |
          export DATABASE_URL="postgresql://postgres:testpass@localhost:5432/go_kuaidi_test"
          go test ./internal/express/... -v
          
      - name: Run Integration Tests
        run: |
          ./scripts/express_company_test.sh
```

## 📋 检查清单

### 测试前检查
- [ ] 服务正常启动
- [ ] 数据库连接正常
- [ ] Redis缓存服务运行
- [ ] 测试账号可用
- [ ] 测试数据准备完成

### 测试执行检查
- [ ] TC001: 快递公司禁用功能测试
- [ ] TC002: 快递公司启用功能测试
- [ ] TC003: 多供应商一致性测试
- [ ] TC004: 性能影响测试
- [ ] TC005: 缓存一致性测试

### 测试后检查
- [ ] 测试数据清理
- [ ] 服务状态恢复
- [ ] 测试报告生成
- [ ] 问题记录整理

## 🔍 详细验收标准

### 功能验收标准

#### 1. 实时生效验收
- **标准**：快递公司状态变更后5秒内生效
- **验证方法**：
  ```bash
  # 变更状态后立即测试
  time_start=$(date +%s)
  update_company_status "$COMPANY_ID" "false" "测试快递"

  # 轮询检查直到生效
  while true; do
    if ! test_price_query "TEST" "false"; then
      time_end=$(date +%s)
      duration=$((time_end - time_start))
      echo "状态变更生效时间: ${duration}秒"
      break
    fi
    sleep 1
  done
  ```

#### 2. 接口一致性验收
- **标准**：所有查价接口行为一致
- **验证接口列表**：
  - `/api/v1/express/price` - 统一查价接口
  - `/api/v1/express/companies` - 快递公司列表
  - `/api/v1/public/express/companies` - 公开快递公司信息
- **验证方法**：对比各接口返回的快递公司列表

#### 3. 数据完整性验收
- **标准**：状态变更不影响现有订单
- **验证方法**：
  ```sql
  -- 检查现有订单状态
  SELECT platform_order_no, express_code, order_status, tracking_status
  FROM orders
  WHERE created_at > NOW() - INTERVAL '1 day'
  AND express_code = '被禁用的快递公司代码';
  ```

#### 4. 性能验收标准
- **API响应时间**：< 200ms (P95)
- **成功率**：> 99%
- **并发处理**：支持100 QPS
- **内存使用**：变更过程中内存增长 < 10MB

### 技术验收标准

#### 1. 缓存一致性
```bash
# 验证Redis缓存
redis-cli GET "express:company:status:STO"
redis-cli GET "express:provider:status:kuaidi100"

# 验证本地缓存失效
curl -X POST http://localhost:8081/api/v1/admin/cache/clear
```

#### 2. 日志记录
- **状态变更日志**：必须记录变更操作
- **查价拒绝日志**：必须记录被拒绝的查价请求
- **性能监控日志**：记录响应时间和错误率

#### 3. 监控指标
```go
// 关键监控指标
metrics.ExpressCompanyStatusChangeCounter.Inc()
metrics.PriceQueryRejectionCounter.WithLabelValues(companyCode).Inc()
metrics.ConfigReloadDuration.Observe(duration.Seconds())
```

## 🧪 高级测试场景

### 场景1：高并发状态变更测试
```bash
#!/bin/bash
# concurrent_status_change_test.sh

# 并发变更多个快递公司状态
companies=("STO" "YTO" "ZTO" "YD" "JT")

for company in "${companies[@]}"; do
  {
    company_id=$(get_company_id "$company")
    update_company_status "$company_id" "false" "$company"
    sleep 2
    update_company_status "$company_id" "true" "$company"
  } &
done

wait
echo "并发状态变更测试完成"
```

### 场景2：供应商故障恢复测试
```bash
# 模拟供应商故障
curl -X POST http://localhost:8081/api/v1/admin/providers/kuaidi100/reload

# 验证其他供应商正常工作
test_price_query_all_providers
```

### 场景3：数据库连接中断测试
```bash
# 模拟数据库连接问题
# 验证系统降级策略和恢复能力
```

## 📈 性能基准测试

### 基准测试脚本
```bash
#!/bin/bash
# performance_benchmark.sh

# 基准测试配置
CONCURRENT_USERS=50
TEST_DURATION=60
TARGET_QPS=100

# 使用Apache Bench进行压力测试
ab -n $((TARGET_QPS * TEST_DURATION)) \
   -c $CONCURRENT_USERS \
   -T "application/json" \
   -p price_query_payload.json \
   http://localhost:8081/api/v1/express/price

# 分析结果
echo "基准测试完成，分析响应时间分布..."
```

### 性能监控脚本
```bash
#!/bin/bash
# monitor_performance.sh

# 监控系统资源使用
while true; do
  timestamp=$(date '+%Y-%m-%d %H:%M:%S')
  cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)
  memory_usage=$(free | grep Mem | awk '{printf "%.2f", $3/$2 * 100.0}')

  echo "$timestamp,CPU:${cpu_usage}%,Memory:${memory_usage}%" >> performance.log
  sleep 5
done
```

## 🔧 故障排查指南

### 常见问题及解决方案

#### 1. 状态变更不生效
**症状**：快递公司状态变更后，查价接口仍返回旧状态

**排查步骤**：
```bash
# 1. 检查数据库状态
psql -h ************* -U postgres -d go_kuaidi -c "
SELECT code, name, is_active, updated_at
FROM express_companies
WHERE code = 'STO';"

# 2. 检查缓存状态
redis-cli GET "express:company:status:STO"

# 3. 检查配置监听器
curl -X GET http://localhost:8081/api/v1/admin/providers/status

# 4. 手动触发重载
curl -X POST http://localhost:8081/api/v1/admin/providers/reload-all
```

#### 2. 查价接口响应慢
**症状**：状态变更后查价接口响应时间增加

**排查步骤**：
```bash
# 1. 检查数据库连接
psql -h ************* -U postgres -d go_kuaidi -c "SELECT 1;"

# 2. 检查Redis连接
redis-cli ping

# 3. 分析慢查询
tail -f logs/go-kuaidi-local-*.log | grep "slow"

# 4. 检查供应商状态
curl -X GET http://localhost:8081/api/v1/admin/providers/metrics
```

#### 3. 缓存不一致
**症状**：不同接口返回的快递公司状态不一致

**解决方案**：
```bash
# 清除所有缓存
curl -X POST http://localhost:8081/api/v1/admin/cache/clear

# 重载所有供应商
curl -X POST http://localhost:8081/api/v1/admin/providers/reload-all

# 验证一致性
./scripts/verify_cache_consistency.sh
```

## 📋 测试报告示例

### 测试执行报告
```
快递公司管理功能测试报告
=====================================

测试信息：
- 测试日期：2025-01-10
- 测试环境：本地开发环境 (localhost:8081)
- 测试版本：go-kuaidi-*********
- 测试人员：测试工程师
- 测试时长：45分钟

测试结果汇总：
=====================================
总测试用例数：8
通过用例数：7
失败用例数：1
跳过用例数：0
通过率：87.5%

详细测试结果：
=====================================
✅ TC001: 快递公司禁用功能测试 - PASS
   - 执行时间：2分钟
   - 状态变更生效时间：3秒
   - 查价接口响应正常

✅ TC002: 快递公司启用功能测试 - PASS
   - 执行时间：2分钟
   - 状态变更生效时间：4秒
   - 查价接口响应正常

✅ TC003: 多供应商一致性测试 - PASS
   - 测试供应商：kuaidi100, yida, yuntong
   - 所有供应商行为一致

❌ TC004: 性能影响测试 - FAIL
   - API响应时间：平均250ms (超出200ms标准)
   - 成功率：99.2%
   - 问题：高并发时响应时间偶尔超标

✅ TC005: 缓存一致性测试 - PASS
   - Redis缓存同步正常
   - 本地缓存失效及时

✅ TC006: 高并发状态变更测试 - PASS
   - 并发变更5个快递公司
   - 无数据竞争问题

✅ TC007: 故障恢复测试 - PASS
   - 供应商重载功能正常
   - 系统自动恢复

✅ TC008: 接口一致性测试 - PASS
   - 所有相关接口行为一致

问题记录：
=====================================
1. 性能问题 - 中等严重程度
   - 问题描述：高并发时API响应时间偶尔超过200ms标准
   - 复现步骤：100 QPS压力测试，持续1分钟
   - 预期结果：P95响应时间 < 200ms
   - 实际结果：P95响应时间 = 250ms
   - 影响范围：高并发场景下的用户体验

修复建议：
=====================================
1. 优化数据库查询性能
   - 添加适当的数据库索引
   - 优化查询语句
   - 考虑增加连接池大小

2. 优化缓存策略
   - 增加缓存预热机制
   - 优化缓存失效策略
   - 考虑使用本地缓存减少Redis访问

3. 监控改进
   - 添加更详细的性能监控
   - 设置性能告警阈值
   - 定期进行性能回归测试

结论：
=====================================
快递公司管理功能基本满足需求，核心功能正常工作。
存在性能优化空间，建议在高并发场景下进一步优化。
建议修复性能问题后重新测试。
```

---

**注意事项**：
1. 测试过程中请确保不影响生产环境
2. 测试完成后及时恢复快递公司状态
3. 如发现问题，请详细记录复现步骤
4. 性能测试建议在低峰期进行
5. 建议定期执行回归测试确保功能稳定性
