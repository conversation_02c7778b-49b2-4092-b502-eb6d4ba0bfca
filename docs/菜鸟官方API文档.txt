菜鸟裹裹开放平台接入白皮书1.0
一、概述
1.开放平台介绍
菜鸟裹裹开放平台是物流行业的数字化中枢，精简对接流程，赋能物流商与商家。平台集成了全面的接入指南与API文档，辅以直观的客户控制台，助力商家自助式接入，畅享菜鸟一站式物流服务，实现高效、便捷的供应链管理。
2.核心名词解释
LINK： 全称为Logistics Integration NetworK，即物流集成网络，它是部署在物流云上，向全球菜鸟合作伙伴提供的数据交换的统一对接平台。
CP：全称为cainiao partner,即菜鸟合作伙伴。
取件码：2小时上门取件要用到四位数取件码，取件码在揽收时是强校验的。
对货码：当日取需要用到对货码，对货码无需强校验，但用户可能一次会下多笔订单，此时需要用到对货码来进行核对，避免贴错单号，混淆物品。
二、产品介绍
1.两小时上门取件
尊享极速响应，下单后两小时内即刻上门，专为紧急寄件需求打造。
2.当日取件
灵活寄件，专为非急件设计，以亲民价格提供从容寄件体验。
当日取的下单逻辑为：
当日17点前下单，可以下今天的实时单（今日上门揽收）和明天后天的预约单；
当日17点后下单，只能下明天后天的预约单，不支持下今天的实时单
3.大件
重型以及大型物品专用的上门取件服务。
三、开放平台接入流程
接入流程图：
系统交互图：
1.登录LINK客户控制台
打开【菜鸟开放平台】https://open.cainiao.com/，点击右上角的【客户控制台】登录，登录可使用菜鸟账号或者淘宝账号（最好是企业账号，便于交接）。
2.新建应用
第一步。在应用管理内点击“新建应用”
业务类型选择：菜鸟裹裹国内快递
资质类型选择：普通用户资质
“应用名称”和“应用描述”。应用名称请根据自身产品名称填写便于识别，如XXX电商平台，XXX工具。然后点击右下方“创建”。
3.应用配置
3.1 API配置
3.2 资源code配置
API配置完成后点击下一步，进入【资源code配置】页面（注意：资源code会随机生成，此code用于公共请求参数的logistic_provider_id字段，同时用于【菜鸟裹裹开放平台】提交申请时）
3.3 预览检查信息
点击【下一步】进入配置更新预览页面检查所添加的API是否正确，若不正确可点击【上一步】返回进行修改，确认无误后点击【正式提交】。正式提交后会有负责LINK接入的同学进行对接。
4.提交接入申请
（1）登录【菜鸟裹裹开放平台】提交接入申请，网址：https://open-express.cainiao.com/#/
1）首次使用寄件开放平台，请先阅读“帮助文档”
2）点击“立即登录”，进入登录页面
3）首次登录，如果没有菜鸟账号，请先注册菜鸟账号
4）建议使用企业账号注册菜鸟账号，避免员工离职后个人账号无法继续使用
5）如果同一个服务商，承接多个商家的对接需求，不同商家需要使用不同的菜鸟账号进行登录，做数据隔离（比如服务商A承接了雀巢和宝洁两个公司对接裹裹寄件服务的需求，那么在开发雀巢项目和宝洁项目时，需要分开使用不同的菜鸟账号登录平台，否则数据无法隔离）
（2）登录成功后选择产品接入：两小时/当日取，点击接入跳转至个人信息，填写相关信息后提交，即申请提交完成，等待业务审核通过后进行联调测试。
（3）等待运营进行审核，审核通过后客户页面会变成【集成开发】，此时客户可进行联调。
（4）同时支付方式选择“商家付费”的客户，可以点击左侧导航的“代扣签约”完成支付宝账户的代扣设置。
5.联调测试流程
审核通过后，开始进行联调。进入应用列表页面，找到创建的应用，点击更多，点击下面的“进入联调”按钮。
注意： 2小时件必须要先执行4917这个任务，当日取先执行6910这个任务  
此处点击【执行回传】按钮，然后去系统发起调用，调用URL：
http://linkdaily.tbsandbox.com/bifrost/link.do
注意事项：（当日取业务需要先联系联调同学重新分配任务，自动分配的任务是2小时件的）
1.2小时件必须要先执行4917这个任务，当日取先执行6910这个任务  
2.一个任务里有多个用例的需要前面的用例执行通过之后才能执行下一个用例
3.注册接口先点“生成报文”按钮，再点“执行下发”
4.如果需要修改联调URL，需要重新去编辑应用修改，改完联调地址后，直接点击【进入联调】就可以同步
6. 发布上线
完成上述流程后，再次进入菜鸟裹裹开放平台https://open-express.cainiao.com/#/提交上线申请，等待审核完成即可发布上线。
四、FAQ
1、预查询相关
Q1: 查询寄件服务详情（GUOGUO_QUERY_SEND_SERVICE_DETAIL）请求参数中，senderInfo里面的寄件人姓名和寄件人手机号不填是否可行（这边理解服务预查询跟用户的名称和手机号应该没有关系呢）？
A：寄件人和收件人的姓名和手机号可以不传，但是寄件人和收件人的地址必须传。收件人地址也是必传是因为要查询计费模板。
Q2: 寄件服务类型code有哪些枚举值？
A: 寄件服务类型code如下：
3000000040/2小时上门(承诺2小时内为您取走退货包裹，并自动录入运单号)
3000000080/当日取
Q3: 查询寄件服务详情（GUOGUO_QUERY_SEND_SERVICE_DETAIL）响应参数中，预约单的时间段开始时间结束时间是否都是整点，如09:00？实时单选项没有体现具体日期，应该就是指当天日期？
A：预约单开始和结束都是整点。 实时单就是当天。
Q4：预查询返回空列表代表当前地区无服务吗?
{"result"：{"data"：{"available ServiceItemList":[]} ,"success":"true"} }
A：是的
Q：2小时和当日取都是这个逻辑吧?
A：是的
Q5：预查询接口返回的版本号是固定的吗
例：调用GUO GUO_QUERY_SEND_SERVICE_DETAIL这个接口返回的这个值是固定的吗?
A：不固定
Q：那同一个地址和同一个产品类型，多次调用预查询,返回的version一样吗
A：和这些没关系，接口给你返回什么就用什么
Q6：预发环境调用预查询接口报hsf服务超时
例：预发环境调用GUO_GUO_QUERY_SENDSERVICEDETAIL报hsf服务响应超时
{
"success":false，
"errorCode":"S04"
"errorMsg":"对方服务器ReadTimeOut:HSFTimeOutException-HSF-0002\nerrormessage:Invalid call is
removed because this request is already timeout.Timeout value is : 3000"
}
A：技术在部署，等部署完成重试即可
Q7：订单详情查询接口没有返回物流详情
例：调用GUOGUO_QUERY_SEND_ORDER_FULL_DETAIL接口时没有返回物流详情
A：缺少请求参数needLogisticsDetail=true
2、创建订单相关
Q1: 创建裹裹上门取件服务订单（GUOGUO_CREATE_SEND_ORDER）请求参数中，外部订单信息列表字段，如果有多种或多件物品，每种物品是这个列表的一项？每项都需要一个外部订单号？要这个外部订单号是用来做什么的？
A: 如果外部订单有多个，每个物品是一项，每一项都有一个外部订单Id。外部订单id不能重复，否则会被幂等掉。
外部订单号会和寄件订单关联，其作为幂等键，如果外部订单id相同，则会返回相同的寄件订单id。
Q2: 物品类型能否都选其他？
A: 外部订单物品类型：
Q3：调用创建订单接口时报accessCode无效
A：检查accessCode是否正确， 是否是裹裹开放平台上申请的值
Q4：创建订单时报参数非法
调用GUO_GUO_CREATE_SEND_ORDER接口时报错：{"result":{"success":"false","statusMessage":"参数非
法", "statusCode":"2001"} }
A：externalUserld、externalUserMobile这2个字段没传，这2个是必填字段
Q：当日取调用GUO_GUO_CREATE_SEND_ORDER接口时报错{
"result":{
"success":"false"，
"statusMessage":"参数非法"，
"statusCode":"2001"
}
}
A：当日取的扩展字段是必填的， 加上这个："extension Map"：{
"canPrint":"false"
}，
Q5：菜鸟裹裹外部接入创建订单接口预发环境报系统开小差
例：当日件itemid:3000000088 这个类型的创建订单时报系统开小差了
A：解决方案：
"extensionMap":{
"canPrint":"false"
},
这个扩展字段传一下
3、公共参数相关
Q1: 各接口请求参数中，接入编码accessCode从何处获取到？
A: accessCode是寄件服务接入时，由寄件服务开发分配的一个唯一的uuid字符串。所有结果必传，作为接入业务身份标识。
Q2: externalUserId 外部用户ID这个是应该填写什么内容？（是整个考拉统一的一个id还是登陆用户在考拉的账号id）
A: 外部用户id用于唯一标识下单的这个用户。应该是考拉系统中的唯一的id。下单时，会根据这个externalUserId和externalUserMobile创建一个菜鸟账号，作为下单用户id。
Q3: 各接口响应参数中，状态编码statusCode、状态描述statusMessage有哪些枚举值？
A: 
参数校验异常。只要系统层面做了完整校验，逻辑上不会出现。
2001 : 参数非法（必填参数为空的情况）
2304 : 无效的外部用户ID(长度超长)
2301 : 寄件人信息无效
2302 : 收件人信息无效
账号异常。如果不是实名认证的手机号，会创建账号失败。
3208 : 创建菜鸟账号失败
3209 ：访问编码accessCode无效
3212：查询菜鸟账号不存在
3301：小件员状态错误
业务异常。逻辑上，服务预查询返回可下单，到用户提交订单存在时间差，会因为时间或运营配置变化，导致下单失败。
5002 : 城市未开放
5003 : 运营区未开放
5007 : 超过下单截止时间
5009 : 当前不支持下实时单
5010 : 预约容量已满
5011 : 当前不支持下预约单
5012 : 运力容量不足
5013 : 超过开放截止时间
5020 : 没有空闲的快递小哥了，建议您预约其它时间
其他
其他的情况可以统一处理为“系统开小差了”
Q4: 公共请求参数中，消息类型msg_type是各接口的英文名？CP编号如何从账号中获取到？请求签名的加密方式就是MD5 https://global.link.cainiao.com/#/homepage/doc/13?_k=mzfexg ？
A: 这些对接时LINK会有开发人员支持，可以咨询LINK联调的开发人员。
4、联调相关
Q: 目前有账号了，LINK平台的API自测工具能否使用？是否需要什么配置？联调之前能否先自测？沙箱环境和彩虹桥环境有什么区别？
A: 一般接入都需要现在彩虹桥环境进行开发和测试。 彩虹桥开发测试通过后，再到预发环境进行联调测试。没有问题后，配置上线。彩虹桥环境就是一个沙箱环境，服务响应都是mock的。LINK平台的工具到了预发环境和线上环境才能使用。
5、其他
Q1：实时单和预约单怎么区分?
A：2小时有预约单和实时单，预约单就是你现在可以预约明天后天的揽收时段，实时单就是你现在下单，揽收时间
就是从现在开始的2小时，17:43到19:43
当日取17点之后只能下预约单，时间段就是9-24点，可预约的日期通过预查询接口返回，17点之前可以下实时单和
预约单。
Q2：订单什么情况下不能取消了?
A：揽收后就不支持接口取消，揽收后需要取消订单可以在群里联系裹裹客服处理
Q：推送订单重量价格在哪个里面没有找到
A：裹裹事件推送接口推送订单完结状态时会推送
Q3: 裹裹外部接入商品有哪些
A：服务商品。目前CP合作的服务商品有以下几个：
3000000040——2小时上门取件。支持实时单和预约单。
3000000080——当日取件。支持实时单和预约单。
五、对接人员
技术：负责联调测试@王燕；
运营：负责协调接入总体流程，解答业务问题
商务：负责引入客户方技术人员，辅助协调接入流程
六、结语
菜鸟开放平台诚邀各行业伙伴加入，共同推进快递服务的智能化、高效化进程。通过紧密合作，我们不仅能为终端用户提供更加快捷、安心的寄件体验，也能为合作伙伴创造更多价值，实现共赢的未来。立即行动，开启您的高效寄件之旅!
七、附录
附录1 API详情
LINK API是一套标准化接口，适用于所有服务商品，请求和响应对于不同的服务商品可能不同，以请求示例和响应示例为准。
1.1 两小时API接口详情
1）服务预查询接口
API详情:
https://open.cainiao.com/api-doc/detail?category=link&type=cainiao_moduan_management&apiId=GUOGUO_QUERY_SEND_SERVICE_DETAIL
返回值中的时间列表说明：
dateSelectable可以忽略，目前都是false。
只需要关注timeList里面的selectable，表示这个时段能否下单，下单时用户要选一个可选的时间段。
full表示当天是否全天已满，如果需要用于展示“当天已约满”的场景，也可以使用。
服务商品。 目前的服务商品有：
3000000040—— 2小时上门取件。 支持实时单和预约单。
2）创建服务订单接口
API详情:
https://open.cainiao.com/api-doc/detail?category=link&type=cainiao_moduan_management&apiId=GUOGUO_CREATE_SEND_ORDER
备注:itemId固定为3000000040、itemVersion在预查询接口中会返回
请求示例-实时单
请求示例-预约单
3）查询服务订单信息接口
API详情:
https://open.cainiao.com/api-doc/detail?category=link&type=cainiao_moduan_management&apiId=GUOGUO_QUERY_SEND_ORDER_FULL_DETAIL
备注:
返回信息
返回字段
返回条件
订单状态
收寄件人信息
寄件人信息：senderInfo
收件人信息：receiverInfo
所有订单状态
时间信息
期待揽收时间：expectGotDateStr
所有订单状态
预约开始时间：appointGotStartTimeStr
预约结束时间：
appointGotEndTimeStr
预约单
小件员信息
参考响应示例：courierInfo
所有订单状态
支付宝支付链接
参考响应示例：payInfo.aliPayUrl
消费者自行支付场景且需要支付链接，请提前告知菜鸟商务同学
订单核价成功后
支付信息
参考响应示例：payInfo
订单支付成功后
包裹信息
参考响应示例：packageInfoDTO
所有订单状态
物流详情最新节点
packageInfoDTO.latestLogisticsDetail
入参needLogisticsDetail为true
回单之后
物流详情H5
packageInfoDTO.logisticsDetailUrl
入参needLogisticsDetail为true
回单之后
物流状态枚举：packageInfoDTO.latestLogisticsDetail.status
4）取消寄件订单接口
API详情:
https://open.cainiao.com/api-doc/detail?category=link&type=cainiao_moduan_management&apiId=GUOGUO_CANCEL_SEND_ORDER
5）订单修改接口
1、在快递员揽收前，都可以发起修改；揽收后无法修改
2、修改前，可调用预查询接口，查询变更的收寄信息对应的可服务时段
3、然后确认发起修改
4、修改后的信息推送
API详情:
https://open.cainiao.com/api-doc/detail?category=link&type=cainiao_moduan_management&apiId=GUOGUO_MODIFY_SEND_ORDER
6）物流详情查询接口
API详情:
https://open.cainiao.com/api-doc/detail?category=link&type=cainiao_moduan_management&apiId=GUOGUO_QUERY_LOGISTICS_DETAIL
事件枚举，对应返回值中的action
1.2 当日取API接口详情
1）服务预查询接口
接口职责: 下单前的预判断、判断寄件地址是否可以使用市场版ToB服务
LINK API详情:
https://open.cainiao.com/api-doc/detail?category=link&type=cainiao_moduan_management&apiId=GUOGUO_QUERY_SEND_SERVICE_DETAIL
请求示例
响应示例：正常返回
响应示例：如果datailList返回结果为空，代表输入的寄件地址没有开放服务或者库存不足，无法寄件
2）创建服务订单接口
接口职责：创建服务订单，派单给快递员，获取面单号
LINK API详情:
https://open.cainiao.com/api-doc/detail?category=link&type=cainiao_moduan_management&apiId=GUOGUO_CREATE_SEND_ORDER
请求示例：
响应示例：正常返回
响应示例：异常返回
3）查询服务订单信息接口
接口职责: 获取服务订单的详细信息
LINK API详情:
https://open.cainiao.com/api-doc/detail?category=link&type=cainiao_moduan_management&apiId=GUOGUO_QUERY_SEND_ORDER_FULL_DETAIL
请求示例
订单状态枚举值：orderStatusCode、orderStatusDesc
物流状态枚举值：packageInfo.latestLogisticsDetail.status
4）取消服务订单
接口职责: 取消寄件服务订单，揽收后不允许取消，同一个externalUserMobile一天允许取消10次
API LINK详情:
https://open.cainiao.com/api-doc/detail?category=link&type=cainiao_moduan_management&apiId=GUOGUO_CANCEL_SEND_ORDER
5）物流详情查询接口
API详情
https://open.cainiao.com/api-doc/detail?category=link&type=cainiao_moduan_management&apiId=GUOGUO_QUERY_LOGISTICS_DETAIL
事件枚举，对应返回值中的action
1.3 事件推送
https://open.cainiao.com/api-doc/detail?category=link&type=cainiao_moduan_management&apiId=GUOGUO_PUSH_ORDER_EVENT
1）寄件全链路事件推送
事件编码
事件描述
推送时机
备注说明
推送事件数据
CREATE_ORDER
创建订单
订单创建成功后
下单同步寻求运力同步取运单号
"accountId":"***********" //下单人ID
"orderId":"****************" //服务订单
"accountType":"1" //下单人类型、对接入方菜鸟分配的均为1
"gotCode":"7877" //取件码
"checkPackageCode":"1" //对货码
"mailNo":"YT1233453" //运单号
"logisticsCompanyCode
SEEK_DELIVERY_SUCCESS
寻求运力成功
寻求到运力后
由于当前服务商品在下单时会同步寻求运力，所以寻求运力成功消息和创建订单消息可能同时到达，不保证顺序
"courierName":"杨改蝉" // 快递员名称
"courierCompany":"圆通快递" //快递公司名称
"courierMobile":"***********" //快递员手机号
"gotCode":"7287#1" //取件码#对货码
OUT_ORDER_COURIER_UPDATE
更新外部快递员真实信息
寻求到运力后大概1分钟内
针对于对总甩单给网点的订单寻求到运力后会推送默认官方信息，需要等网点回传，才能获取到快递员真实信息
"courierName":"杨改蝉" // 快递员名称
"courierCompany":"圆通快递" //快递公司名称
"courierMobile":"***********" //快递员手机号
"gotCode":"7287#1" //取件码#对货码
CHANGE_DELIVERY_USER_SUCCESS
改派成功
网点进行改派
一般是网点行为
"courierName":"张小林" //改派后的快递员名称
"courierCompany":"申通快递" //改派后的快递公司名称
"courierMobile":"***********" //改派后的快递员手机号
MODIFY_EXPECT_GOT_TIME_SUCCESS
修改期望揽收截止时间
调用服务订单修改接口且只修改了期望揽收时间
揽收后不能进行修改
"expectGotStartDate":"2023-03-28 17:00:00", //期望揽收起始时间
"expectGotEndDate":"2023-03-28 19:00:00" //期望揽收截止时间
GOT_SUCCESS
取件成功
快递员上门取到包裹后
"lpCode":"LP00566961998734" //非阿里集团直接忽略
COURIER_CHECK_BILL_SUCCESS
核价成功
快递员进行包裹核重在实操侧发送账单后
"weight":"1000" //快递员核价重量
"basePrice":"1200" //快递总价格
PAY_SUCCESS
支付成功
消费者支付成功或代扣成功后
如果是代扣场景，快递员发送账单后会立刻发起代扣，如果代扣失败例如：代扣账户金额不足，系统会进行重试持续15天，直到代扣成功为止
"payTime":"2023-03-27 20:10:23" //支付时间
"payStatus":"10" //支付状态 0未支付、5支付中、10、支付成功、20支付失败、30支付取消
FINISH_ORDER
订单完结
订单支付成功后
订单已经回单后且订单支付成功后，订单会被更新为已完结
"mailNo":"773212749909654"
"logisticsCompanyName":"申通快递"
"totalPrice":"1500"
"weight":"1000"
"logisticsCompanyCode":"STO"
"basePrice":"1500"
CANCEL_ORDER
取消订单
调用取消服务订单
揽收后不能进行取消
"orderCancelReasonDesc":"开放平台接入方取消
2）物流详情事件推送
在事件消息体eventData里保存了 事件推送的全部信息：
"mailNo": "78766513425104", // 运单号
"itemId": "3000000088", // 服务商品
"logisticsAction": "STA_SEND_SIGN", //物流行为
"lpCode": "LP00633707719690", // lp号
"logisticsCompName": "中通快递", // 物流公司名称
"logisticsStatus": "SIGN", // 物流状态
"logisticsCreate": "2024-03-13 18:04:22", // 物流节点创建时间
"logisticsStandardDesc": "可以不填" // 物流节点详情描述
事件编码
事件描述
推送事件实例
ACCEPT
已揽件
TRANSPORT
运输中
DELIVERING
派送中
FAILED
温馨提示中（异常提醒）
REJECT
拒签
AGENT_SIGN
待取件
STA_DELIVERING
驿站派送中
SIGN
已签收
ORDER_TRANSER
已转单
REVERSE_RETURN
退货返回
附录2 logisticsAction和logisticsStatus映射关系
物流状态
物流状态编码
物流事件
已揽件
ACCEPT
GOT
TMS_ACCEPT
JIJIAN_CONSIGN
DANNIAO_ACCEPT
运输中
TRANSPORT
TMS_STATION_IN
DEPARTURE
TMS_STATION_OUT
ARRIVAL
TMS_AC_OUT
TMS_AC_IN
TRUNK_SIGN
派送中
DELIVERING
TMS_DELIVERING
温馨提示中（异常提醒）
FAILED
FAILED
ERROR
N_ERROR
拒签
REJECT
TMS_REJECT
TMS_FAILED
待取件
AGENT_SIGN
SH_INBOUND
STA_INBOUND
INBOUND
驿站派送中
STA_DELIVERING
STA_START_DELIVERY
STA_DELIVERY
STA_AGENCY_DELIVERY
SH_DELIVERY
STA_WAIT_DELIVERY
已签收
SIGN
SIGNED
TMS_SIGN
SH_SIGNED
STA_SIGN
XNSIGN
STA_SEND_SIGN
SH_SEND_SIGN
CONFIRM_SIGN
已转单
ORDER_TRANSER
TRANSFER_ORDER
退货返回
REVERSE_RETURN
INVERSE_TMS_AC_IN
INVERSE_AC_ACCEPT
INVERSE_TMS_SITE_IN
INVERSE_AC_IN
INVERSE_AC_ARRIVAL
TMS_CANCEL_SUCCESS
INVERSE_TMS_AC_OUT
INVERSE_AC_OUT
INVERSE_TMS_SITE_OUT
附录3 物流公司对应编码表
CP中文名称
cpCode
京东快递
LE04284890
天天快递
TTKDEX
极兔速递（百世快递）
HTKY
德邦快递
DBKD
德邦快递
D_DBKD
圆通速递
YTO
圆通速递
CP468398
申通快递
STO
中通快递
ZTO
韵达快递
YUNDA
中国邮政
POST
中国邮政
POSTB
EMS
EMS
顺丰速运
SF
全峰快速
QFKD
快捷快递
FAST
优速快递
UC
优速快递
LE18114590
国通快递
GTO
宅急送
ZJS
万象快递
DISTRIBUTOR_11997360
如风达
BJRFD-001
点我达
DWD
中铁物流
K_ZTKY
苏宁物流
SNWL
丹鸟KD（即菜鸟速递）
ZMKMKD
丹鸟KD（即菜鸟速递）
DISTRIBUTOR_30363231
丹鸟KD（即菜鸟速递）
CP570969
速腾快递
DISTRIBUTOR_13509344
附录4 订单状态
状态编码
状态描述
-1
订单已取消
0
订单已创建
20
已分配运力
30
已取件
40
已完结
附录5 事件类型
事件编码
事件描述
包含的扩展数据
备注
CREATE_ORDER
创建订单
CANCEL_ORDER
取消订单
取消类型, 取消原因编码, 取消原因描述
SEEK_DELIVERY_SUCCESS
寻求运力成功
取件码，小件员手机号，小件员姓名，物流公司名称
GOT_SUCCESS
取件成功
UPLOAD_MAIL_NO_SUCCESS
回单成功
物流公司编码, 物流公司名称, 运单号
该事件中，订单仍然处于“取件成功”状态。按需开放。
FINISH_ORDER
订单完结
物流公司编码, 物流公司名称, 运单号, 订单总价, 核价的重量
COURIER_CHECK_BILL_SUCCESS
核价成功
基础价，运费，小件员调价，声明物品保值，保价费，用户加价，非标准费用之外的额外费用
总价=basePrice + packageFee + insurancePrice - courierAdjustFee + additionalFee + userBonusFee，如果返回事件数据没有相应字段，则表示0
CHANGE_DELIVERY_USER_SUCCESS
改派成功
改派的小件员姓名，小件员手机号，物流公司名称
MODIFY_EXPECT_GOT_TIME_SUCCESS
修改上门时间成功
修改后的上门时间
PAY_SUCCESS
支付成功
支付状态，支付时间
附录6 事件扩展数据
Key
类型
描述
gotCode
String
取件码
logisticsCompanyCode
String
物流公司编码
logisticsCompanyName
String
物流公司名称
mailNo
String
运单号
totalPrice
Long
总价（单位分）
weight
Long
核价的重量（单位克）
orderCancelType
Integer
取消类型
orderCancelReasonCode
String
取消原因编码
orderCancelReasonDesc
String
取消原因描述
totalPrice
Long
总价（单位分）
weight
Long
核价的重量（单位克）
insurancePrice
Long
保价金额（单位分）
insuranceValue
Long
保价价值（单位分）
courierAdjustFee
Long
小件员减价（单位分）
userBonusFee
Long
用户加价（单位分）
packageFee
Long
包装费（单位分）
basePrice
Long
基础价格（寄件基础价=首重价格+续重价格，单位分）
additionalFee
Long
非标准费用之外的额外费用（分）
payTime
String
支付时间
payStatus
Integer
支付状态，0-未支付，5-支付中，10-支付成功，20-支付失败，30-支付取消
courierMobile
String
小件员手机号
courierName
String
小件员姓名
expectGotStartDate
String
期望上门开始时间
expectGotEndDate
String
期望上门结束时间
附录7 Result中的ErrorCode
参数校验异常。只要系统层面做了完整校验，逻辑上不会出现。
2001 : 参数非法（必填参数为空的情况）
2304 : 无效的外部用户ID(长度超长)
2301 : 寄件人信息无效
2302 : 收件人信息无效
AREA_ID_INVALID: 收件人区域Id不合法(必须大于0)
DIVSION_IS_NULL：行政区为空
PARAM_INVALID：收件人姓名不能超过32个字
账号异常。如果不是实名认证的手机号，会创建账号失败。
3208 : 创建菜鸟账号失败
FAILED_IN_BLACK_LIST：账号行为异常，已被冻结
业务异常。逻辑上，服务预查询返回可下单，到用户提交订单存在时间差，会因为时间或运营配置变化，导致下单失败。
3205：该地区尚未完成服务准备
4017：预约时间不合法
5002 : 城市未开放
5003 : 运营区未开放
5007 : 超过下单截止时间
5009 : 快递员已约满
5010 : 预约容量已满
5011 : 当前不支持下预约单
5012 : 运力容量不足
5013 : 超过开放截止时间
5020 : 没有空闲的快递小哥了，建议您预约其它时间
5027：该收件地址暂不支持裹裹寄件服务
5031: 预约时间已失效，请重新选择
5050：因疫情原因暂停寄件服务
5063：服务暂未开通
32141：当前快递员未在服务时间，建议选择【菜鸟官方上门服务】重新下单
其他
其他的情况可以统一处理为“系统开小差了”