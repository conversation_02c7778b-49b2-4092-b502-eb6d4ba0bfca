# 原始回调数据管理功能

## 功能概述

原始回调数据管理功能是一个专为管理员设计的强大工具，用于查看、分析和重推供应商的原始回调数据。这个功能特别适用于：

- 🔍 **问题排查**：当订单状态异常时，可以查看原始回调数据进行问题诊断
- 🔧 **状态修复**：修复状态映射错误后，可以重推相关回调让系统重新处理
- 🚨 **故障恢复**：处理系统故障期间丢失或处理失败的回调
- 🧪 **测试验证**：测试和验证回调处理逻辑的正确性

## 主要特性

### 1. 数据查看与筛选
- **多维度筛选**：支持按供应商、事件类型、订单号、运单号、时间范围筛选
- **智能搜索**：支持模糊搜索订单号和运单号
- **分页显示**：支持大数据量的分页浏览（每页20-200条可调）
- **实时统计**：显示总记录数、成功率、各供应商统计等关键指标

### 2. 数据分析与展示
- **原始数据预览**：可查看完整的原始回调数据
- **解析后数据**：显示系统解析后的结构化数据
- **事件类型识别**：自动识别不同供应商的事件类型
- **处理状态跟踪**：显示回调的处理状态和错误信息

### 3. 重推功能
- **单个重推**：点击即可重推单条回调记录
- **批量重推**：选择多条记录进行批量重推
- **条件重推**：按指定条件批量重推符合要求的所有回调
- **进度跟踪**：实时显示重推进度和结果

### 4. 数据导出
- **CSV导出**：支持将筛选后的数据导出为CSV格式
- **中文支持**：导出文件支持Excel正确显示中文
- **自定义范围**：可按筛选条件导出指定范围的数据

## 支持的供应商

| 供应商 | 代码 | 主要事件类型 |
|--------|------|-------------|
| 菜鸟 | cainiao | CREATE_ORDER, SEEK_DELIVERY_SUCCESS, GOT_SUCCESS, FINISH_ORDER, ACCEPT, TRANSPORT, DELIVERING, SIGN, FAILED, REJECT |
| 快递100 | kuaidi100 | status_update, track_push |
| 易达 | yida | push_type_1, push_type_2, push_type_3 |
| 云通 | yuntong | state_1, state_2, state_3 |
| 快递鸟 | kuaidiniao | success, failed |

## 使用指南

### 1. 访问管理界面

在管理员后台中，导航到：**回调管理** → **原始回调管理**

### 2. 数据筛选

使用顶部的筛选器可以快速定位需要的数据：

```
供应商：选择特定供应商（如菜鸟、快递100等）
事件类型：选择特定事件类型（如FINISH_ORDER、SIGN等）
订单号：输入订单号进行搜索
运单号：输入运单号进行搜索
时间范围：选择时间范围（默认显示最近7天）
```

### 3. 查看详情

- **原始数据**：点击"原始数据预览"列可查看完整的原始回调数据
- **详细信息**：点击"详情"按钮查看解析后的结构化数据和处理状态

### 4. 重推操作

#### 单个重推
1. 在表格中找到需要重推的记录
2. 点击"重推"按钮
3. 确认重推操作
4. 系统将重新处理该回调

#### 批量重推
1. 勾选需要重推的多条记录
2. 点击"批量重推"按钮
3. 确认重推操作
4. 系统将依次处理所有选中的回调

#### 按条件批量重推
1. 点击"按条件批量重推"按钮
2. 设置筛选条件（供应商、事件类型、时间范围等）
3. 确认重推操作
4. 系统将处理所有符合条件的回调

### 5. 数据导出

1. 设置筛选条件（可选）
2. 点击"导出数据"按钮
3. 系统将生成CSV文件并自动下载

## 技术架构

### 后端API

```
GET  /api/v1/admin/raw-callbacks/records              # 获取回调记录列表
GET  /api/v1/admin/raw-callbacks/records/:id          # 获取回调记录详情
POST /api/v1/admin/raw-callbacks/retry/:id            # 重推单个回调
POST /api/v1/admin/raw-callbacks/batch-retry          # 批量重推回调
POST /api/v1/admin/raw-callbacks/batch-retry-by-condition # 按条件批量重推
GET  /api/v1/admin/raw-callbacks/statistics           # 获取统计信息
GET  /api/v1/admin/raw-callbacks/export               # 导出数据
```

### 数据库表

主要使用 `callback_receiver` 数据库中的 `callback_raw_data` 表：

```sql
CREATE TABLE callback_raw_data (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    provider VARCHAR(50) NOT NULL,
    raw_body TEXT NOT NULL,
    received_at TIMESTAMP NOT NULL,
    processed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_callback_raw_data_provider ON callback_raw_data(provider);
CREATE INDEX idx_callback_raw_data_received_at ON callback_raw_data(received_at);
```

### 前端组件

- **Vue 3 + TypeScript**：现代化的前端框架
- **Element Plus**：企业级UI组件库
- **响应式设计**：适配不同屏幕尺寸
- **实时更新**：支持数据的实时刷新

## 安全与权限

### 权限控制
- **管理员专用**：只有管理员账户可以访问此功能
- **操作审计**：所有重推操作都会记录操作日志
- **二次确认**：重要操作需要用户确认

### 数据安全
- **敏感信息保护**：原始回调数据可能包含敏感信息，仅限管理员查看
- **操作限制**：批量操作有数量限制，防止系统过载
- **访问日志**：记录所有访问和操作日志

## 性能优化

### 查询优化
- **索引优化**：在关键字段上建立索引
- **分页查询**：避免一次性加载大量数据
- **条件筛选**：支持高效的条件筛选

### 批量处理
- **限制数量**：单次批量重推限制在1000条以内
- **异步处理**：大批量操作采用异步处理
- **进度反馈**：实时显示处理进度

## 故障排查

### 常见问题

1. **数据加载失败**
   - 检查数据库连接
   - 确认权限配置
   - 查看服务日志

2. **重推失败**
   - 检查回调处理服务状态
   - 确认原始数据格式正确
   - 查看错误日志

3. **导出失败**
   - 检查数据量是否过大
   - 确认磁盘空间充足
   - 查看浏览器下载设置

### 日志查看

重要操作都会记录在系统日志中：

```bash
# 查看回调处理日志
tail -f logs/callback.log

# 查看管理员操作日志
tail -f logs/admin.log
```

## 演示环境

为了方便测试和演示，我们提供了一个独立的演示服务：

```bash
# 启动演示服务
go run cmd/demo/raw_callback_demo.go

# 访问地址
http://localhost:8082/api/v1/admin/raw-callbacks
```

演示服务包含5条不同供应商的示例数据，支持所有API功能的测试。

## 更新日志

### v1.0.0 (2025-08-03)
- ✨ 初始版本发布
- 🎯 支持5大供应商的原始回调数据管理
- 🔍 完整的筛选、搜索、分页功能
- 🔄 单个、批量、条件重推功能
- 📊 统计分析和数据导出功能
- 🛡️ 完整的权限控制和安全保护

---

**注意**：此功能涉及核心业务数据，请谨慎操作。建议在测试环境充分验证后再在生产环境使用。
