# 快递系统架构重构方案

## 🔥 当前架构问题

### 主要问题
1. **缓存层级混乱** - 5层缓存互相冲突
2. **无缓存方法泛滥** - 每个方法都要维护两个版本
3. **缓存失效机制复杂** - 需要手动清理多个地方
4. **职责分散** - 适配器内部还要处理映射逻辑
5. **配置管理混乱** - 缓存和数据库状态不一致

## 🚀 新架构设计

### 核心设计原则
1. **单一数据源** - 所有配置只从数据库读取
2. **统一缓存层** - 只在一个地方管理缓存
3. **清晰职责分离** - 每个组件只负责自己的事情
4. **事件驱动更新** - 配置变更自动通知所有组件

### 架构分层

```
┌─────────────────────────────────────────┐
│                API层                    │
│  ┌─────────────────────────────────────┐ │
│  │        统一网关API                   │ │
│  └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
                    │
┌─────────────────────────────────────────┐
│               业务层                     │
│  ┌─────────────────────────────────────┐ │
│  │      价格查询协调器                  │ │
│  └─────────────────────────────────────┘ │
│                    │                     │
│  ┌─────────────────────────────────────┐ │
│  │       映射过滤器                     │ │
│  └─────────────────────────────────────┘ │
│                    │                     │
│  ┌─────────────────────────────────────┐ │
│  │      供应商路由器                    │ │
│  └─────────────────────────────────────┘ │
│                    │                     │
│  ┌─────────────────────────────────────┐ │
│  │    快递100  菜鸟  快递鸟  易达  云通  │ │
│  │         (纯净适配器)                 │ │
│  └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
                    │
┌─────────────────────────────────────────┐
│               服务层                     │
│  ┌─────────────────────────────────────┐ │
│  │       统一配置服务                   │ │
│  └─────────────────────────────────────┘ │
│                    │                     │
│  ┌─────────────────────────────────────┐ │
│  │        事件总线                      │ │
│  └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
                    │
┌─────────────────────────────────────────┐
│               数据层                     │
│  ┌─────────────────────────────────────┐ │
│  │    智能缓存管理器    │   PostgreSQL   │ │
│  │  ┌─────────────────┐ │               │ │
│  │  │ Redis │ Memory  │ │               │ │
│  │  └─────────────────┘ │               │ │
│  └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
```

## 🔧 核心组件设计

### 1. 统一配置服务 (UnifiedConfigService)

**职责：**
- 统一管理所有供应商和快递公司配置
- 提供统一的配置查询接口
- 配置变更时发送事件通知

**接口设计：**
```go
type UnifiedConfigService interface {
    // 获取启用的供应商列表
    GetEnabledProviders(ctx context.Context) ([]Provider, error)
    
    // 获取供应商支持的快递公司
    GetProviderCompanies(ctx context.Context, providerCode string) ([]Company, error)
    
    // 检查快递公司是否启用
    IsCompanyEnabled(ctx context.Context, companyCode, providerCode string) (bool, error)
    
    // 获取快递公司映射代码
    GetCompanyMapping(ctx context.Context, standardCode, providerCode string) (string, error)
}
```

### 2. 智能缓存管理器 (SmartCacheManager)

**职责：**
- 统一管理所有缓存
- 自动缓存预热和失效
- 监听配置变更事件自动更新缓存

**特性：**
- 多级缓存策略（内存 + Redis）
- 智能缓存预热
- 事件驱动的缓存更新
- 缓存命中率监控

### 3. 映射过滤器 (MappingFilter)

**职责：**
- 根据配置过滤启用的快递公司
- 处理快递公司代码映射
- 统一的映射逻辑，适配器无需关心

**接口设计：**
```go
type MappingFilter interface {
    // 过滤启用的快递公司
    FilterEnabledCompanies(ctx context.Context, providerCode string, companies []string) ([]string, error)
    
    // 获取映射后的快递公司代码
    GetMappedCode(ctx context.Context, standardCode, providerCode string) (string, error)
}
```

### 4. 供应商路由器 (ProviderRouter)

**职责：**
- 根据快递公司路由到对应的供应商
- 并行调用多个供应商
- 统一的错误处理和重试机制

### 5. 纯净适配器 (Clean Adapters)

**职责：**
- 只负责与供应商API交互
- 不处理映射逻辑
- 不处理缓存逻辑
- 专注于数据转换和API调用

## 🎯 重构收益

### 1. 简化管理
- **统一配置入口** - 所有配置只在一个地方管理
- **统一缓存策略** - 只有一个缓存管理器
- **清晰的职责分离** - 每个组件职责明确

### 2. 提高性能
- **智能缓存** - 自动预热和失效
- **并行查询** - 供应商并行调用
- **减少数据库查询** - 统一的缓存策略

### 3. 易于维护
- **事件驱动** - 配置变更自动生效
- **统一接口** - 标准化的组件接口
- **可测试性** - 每个组件都可以独立测试

### 4. 易于扩展
- **插件化适配器** - 新增供应商只需实现适配器接口
- **配置化路由** - 通过配置控制供应商选择
- **监控友好** - 统一的监控和日志

## 📋 实施计划

### 阶段1：核心服务重构 (1-2天)
1. 创建统一配置服务
2. 创建智能缓存管理器
3. 创建事件总线

### 阶段2：业务层重构 (2-3天)
1. 创建映射过滤器
2. 创建供应商路由器
3. 重构价格查询协调器

### 阶段3：适配器重构 (2-3天)
1. 简化所有适配器，移除映射逻辑
2. 移除适配器内的缓存逻辑
3. 统一适配器接口

### 阶段4：测试和优化 (1-2天)
1. 全面测试
2. 性能优化
3. 监控完善

## 🔧 技术实现要点

### 1. 事件总线实现
```go
type EventBus interface {
    Publish(event Event) error
    Subscribe(eventType string, handler EventHandler) error
}

type Event struct {
    Type      string
    Data      interface{}
    Timestamp time.Time
}
```

### 2. 缓存策略
- **L1缓存**: 内存缓存，TTL 5分钟
- **L2缓存**: Redis缓存，TTL 30分钟
- **事件失效**: 配置变更时立即失效相关缓存

### 3. 配置热更新
- 数据库配置变更 -> 发送事件 -> 缓存自动更新 -> 业务逻辑立即生效

这个新架构将大大简化系统的复杂性，提高可维护性和性能。您觉得这个方案如何？
