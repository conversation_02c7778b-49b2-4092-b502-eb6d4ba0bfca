快递公司寄件接口
一、商家寄件下单接口
选择快递公司进行下单。

1.1 接口格式
提供统一格式的HTTP POST或GET调用接口,并返回统一格式JSON数据。

1.2 请求地址
正式请求地址：https://poll.kuaidi100.com/order/borderapi.do（订单将推至快递公司）

沙箱请求地址：https://api.kuaidi100.com/apiMock/border（订单将不推至快递公司：沙箱下单后，可通过：调试工具-商家寄件-下单回调，调试订单各阶段状态。）

请求参数：

参数名	是否必填	类型	说明
method	是	string	业务类型（默认：bOrder）
key	是	string	授权码，请到快递100页面申请企业版接口获取
sign	是	string	32位大写签名，用于验证身份，按MD5 (param +t+key+ secret)的顺序进行MD5加密，不需要加上“+”号。secret在企业管理后台获取
t	是	string	时间戳如：1576123932000
param	是	param	由其他字段拼接
param数据结构：

参数名	是否必填	类型	说明
kuaidicom	是	string	快递公司的编码，一律用小写字母，见《快递公司编码》
recManName	是	string	收件人姓名
recManMobile	是	string	收件人的手机号，手机号和电话号二者其一必填
recManPrintAddr	是	string	收件人所在完整地址，如广东深圳市深圳市南山区科技南十二路2号金蝶软件园
sendManName	是	string	寄件人姓名
sendManMobile	是	string	寄件人的手机号，手机号和电话号二者其一必填
sendManPrintAddr	是	string	寄件人所在的完整地址，如广东深圳市深圳市南山区科技南十二路2号金蝶软件园B10
callBackUrl	是	string	callBackUrl订单信息回调地址
cargo	否	string	物品名称,例：文件。当kuaidicom=jd，yuantong时，必填
payment	否	string	支付方式，SHIPPER: 寄付（默认）。不支持到付
serviceType	否	string	业务类型，默认为标准快递，各快递公司业务类型对照参考：七、业务类型参数表
weight	否	string	物品总重量KG，不需带单位，例：1.5
remark	否	string	备注
dayType	否	string	预约日期，例如：今天/明天/后天（预约取件，该字段按照格式必填）
pickupStartTime	否	string	预约起始时间（HH:mm），例如：09:00，顺丰必填
pickupEndTime	否	string	预约截止时间（HH:mm），例如：10:00，顺丰必填，预约起始时间和预约截止时间间隔需≥1小时；中通有严格要求：需要严格按照五个时间段 9:00-11:00、11:00-13:00、13:00-15:00、15:00-17:00、17:00-19:00进行预约时间的传参，超过19点，自动预约第二天的时间；
channelSw	否	string	渠道ID，如有多个同品牌运力，请联系商务提供后传入
下单扩展属性			
valinsPay	否	string	保价额度，单位：元 （当前仅京东、德邦、圆通、极兔和申通支持线上保价，其他快递传值无效，具体费率可咨询商务）
realName	否	string	寄件人实名信息（圆通、极兔支持 ）
sendIdCardType	否	string	寄件人证件类型，1：居民身份证 ；2：港澳居民来往内地通行证 ；3：台湾居民来往大陆通行证 ；4：中国公民护照（圆通、极兔支持 ）
sendIdCard	否		寄件人证件号码 （圆通、极兔支持 ）
passwordSigning	否	string	是否口令签收，Y：需要 N: 不需要，默认值为N（德邦快递专属参数）
op	否	string	是否开启订阅功能 0：不开启(默认) 1：开启 说明开启订阅功能时：pollCallBackUrl必须填入 此功能只针对有快递单号的单
pollCallBackUrl	否	string	如果op设置为1时，pollCallBackUrl必须填入，用于跟踪回调，回调内容通过五、快递信息推送接口返回（免费服务）
resultv2	否	string	添加此字段表示开通行政区域解析功能 。
0：关闭（默认）
1：开通行政区域解析功能以及物流轨迹增加物流状态名称 (详见：快递信息推送接口文档)
3：开通行政区域解析功能以及物流轨迹增加物流状态名称，同时返回地图内容(详见：地图轨迹推送接口文档)
returnType	否	string	面单返回类型，默认为空，不返回面单内容。10：设备打印，20：生成图片短链回调。
siid	否	string	设备码，returnType为10时必填
tempid	否	string	模板编码，通过管理后台的电子面单模板信息获取 ，returnType不为空时必填
printCallBackUrl	否	string	打印状态回调地址，returnType为10时必填
salt	否	string	签名用随机字符串，用于验证签名sign。salt值不为null时，推送数据将包含该加密签名，加密方式：md5(param+salt)。注意： salt值为空串时，推送的数据也会包含sign，此时可忽略sign的校验。
thirdOrderId	否	string	平台订单号，最大32位。若此参数与之前的重复，48小时内返回第一次下单内容，否则会重新下单。
pickupCode	否	string	取件码自定义传入，需传入4位数字，仅用于极兔，其他快递公司传入无效
1.3 返回结果
字段	类型	说明	备注
result	boolean	提交结果	true提交成功，false失败
returnCode	string	返回编码	
message	string	返回报文描述	
data	data		
data数据结构

字段	类型	说明	备注
taskId	string	任务ID	
orderId	string	订单ID	
kuaidinum	string	快递单号	
pollToken	string	查询密钥，调用实时快递查询接口时入参此字段可免费查询该快递单号，一个快递单号对应一个密钥	
eOrder	string	快递面单附属属性，根据各个快递公司返回属性	
eOrder数据结构

字段	类型	说明	备注
bulkpen	string	大头笔	用于显示于电子面单上规定位置，非必需，是否有值取决于快递公司
orgCode	string	始发地区域编码	
orgName	string	始发地/始发网点名称	
destCode	string	目的地区域编码	
destName	string	目的地/到达网点	
orgSortingCode	string	始发分拣编码	
orgSortingName	string	始发分拣名称	
destSortingCode	string	目的分栋编码	
destSortingName	string	目的分栋中心名称	
orgExtra	string	始发其他信息	
destExtra	string	目的其他信息	
pkgCode	string	集包编码	
pkgName	string	集包地名称	
road	string	路区	
qrCode	string	二维码	
kdComOrderNum	string	快递公司订单号	
expressCode	string	快递业务类型编码	
expressName	string	快递业务类型名称	
waterMark	string	水印	
agingName	string	时效	
abFlag	string	电子产品类型图标	
proCode	string	时效产品图标	
codingMapping	string	进港映射码	
codingMappingOut	string	出港信息	
printIcon	string	图标名称	
destRouteLabel	string	目的地（路由信息）	
twoDimensionCode	string	二维码信息	
xbFlag	string	顺丰面单标识，快运必填，xbFlag=1，打印SX标；xbFlag=2，打印融通标	
注意：eOrder实际返回的字段各家快递公司不同，以实际返回为准

1.4 提供数据内容
请求参数示例

method = bOrder
key = ******
sign = 4BBDE07660E5EFF90873642CFAE9A8DD
t = 1647258957705
param = {
	"kuaidicom": "yuantong",
	"recManName": "王超",
	"recManMobile": "13800138000",
	"recManPrintAddr": "西藏日喀则市定日县珠穆朗玛峰",
	"sendManName": "王大",
	"sendManMobile": "13800138000",
	"sendManPrintAddr": "西藏日喀则市定日县珠穆朗玛峰",
	"cargo": "文件",
	"callBackUrl": "http: //www.baidu.com",
	"payment": "SHIPPER",
	"serviceType": "标准快递",
	"weight": "1",
	"remark": "",
	"salt": "",
	"dayType": "",
	"pickupStartTime": "",
	"pickupEndTime": "",
	"passwordSigning": "Y",
	"valinsPay": "",
	"op": "0",
	"pollCallBackUrl": "",
	"resultv2": "0",
	"returnType": "",
	"siid": "",
	"tempid": "",
	"printCallBackUrl": ""
}
Copy
返回结果示例

{
	"result": true,
	"returnCode": "200",
	"message": "提交成功",
	"data": {
		"taskId": "", // 任务ID
		"orderId": "" // 订单ID
		"kuaidinum": "" // 快递单号
		"eOrder": "[\"destName\":null,\"orgName\":null,\"pkgCode\":null,\"pkgName\":null,\"bulkpen\":\"上海-S33-K27-P\",\"orgExtra\":null}]"
	}
}
Copy
说明：

信息代码	信息内容描述	原因及建议处理方式
200	提交成功	提交成功
400	参数错误等	请根据技术文档请求，注意参数类型及是否必填
500	服务器错误	快递100的服务器出理间隙或临时性异常，有时如果因为不按规范提交请求，比如快递公司参数写错等，也会报此错误
501	重复提交	重复提交了请求
503	验证签名失败	请检查加密方式，param + t + key + secret 的顺序进行MD5加密，加密后字符串转大写,不用加上“+”号
600	您不是合法的用户（即授权Key出错）	账号无可用余额，需要充值
601	KEY已过期	账号无可用余额，需要充值
700	错误的回调地址	检查回调地址，或者联系快递100工作人员
二、下单回调接口
订单有状态变更是会触发回调，回调后如果没有得到合作方正确返回，会重复回调多2次，即最多回调3次。间隔30分钟。

2.1 接口格式
提供统一格式的HTTP POST,并返回统一格式JSON数据。

请求报头：Content-Type=application/x-www-form-urlencoded;charset=UTF-8

2.2 请求地址
请求参数：

字段	类型	说明	备注
taskId	string	任务ID	
sign	string	签名	32位大写签名，MD5 (param +salt)
param	param	参数主体	
param数据结构：

参数名	是否必填	类型	说明
kuaidicom	是	string	快递公司的编码，一律用小写字母，见《快递公司编码》,选填。
kuaidinum	是	string	快递单号，单号的最大长度是32个字符。
status	是	string	状态码
message	是	string	状态描述
data	是	data	订单内容
data数据结构：

参数名	是否必填	类型	说明
orderId	是	string	平台订单ID
status	是	int	订单状态说明： 0：'下单成功'； 1：'已接单'； 2：'收件中'； 9：'用户主动取消'；10：'已取件'； 11：'揽货失败'；12：'已退回'； 13：'已签收'； 14：'异常签收'；15：'已结算' ；99：'订单已取消'；101：'运输中'；200：'已出单'；201：'出单失败'；610：'下单失败'；155：'修改重量'(注意需要在工单系统中发起异常反馈并由快递100服务人员确认调重后才会有此状态回调，回调内容包含修改重量后的重量、运费、费用明细、业务类型)；166：订单复活（订单被取消，但是实际包裹已经发出，正常计费）；400：派送中
cancelMsg9	否	string	用户取消原因
cancelMsg99	否	string	系统取消或下单失败原因
courierName	否	string	快递员姓名
courierMobile	否	string	快递员电话
netTel	否	string	网点电话，目前仅圆通会推送
netCode	否	string	网点编码，目前仅圆通会推送
weight	否	string	计费重量，单位：kg
defPrice	否	string	标准运费，单位：元
freight	否	string	折后运费，单位：元
volume	否	string	体积，单位：cm³
actualWeight	否	string	称重重量，单位：kg
ΘfeeDetails	否	list	费用明细，明细项目请展开
└ feeType	否	string	费用类型,参考：八、费用类型(feeType)说明
└ feeDesc	否	string	费用名称
└ amount	否	string	费用明细金额，单位：元
└ payStatus	否	string	支付状态，支付失败：-1；未支付：0；已支付：1 ；无需支付：2；已退款：3
printTaskId	否	string	打印任务ID
label	否	string	面单短链，下单请求中returnType为20时返回
pickupCode	否	string	取件码，目前只有中通、申通、极兔会返回，在状态1时返回
pollToken	是	string	查询密钥，调用实时快递查询接口时入参此字段可免费查询该快递单号，一个快递单号对应一个密钥。
2.3 返回结果
字段	说明	备注
result	提交结果	true提交成功，false失败
returnCode	返回编码	
message	返回报文描述	
注意：不按照规定字段和格式响应，默认接收推送失败，会造成重复推送

2.4 提供数据内容
请求参数示例

taskId = ** ** **
	sign = ** ** **
	param = {
		"kuaidicom": "yuantong",
		"kuaidinum": "1234567890",
		"status": "200",
		"message": "成功",
		"data": {
			"orderId": "*****",
			"status": "0",
			"courierName": "王大",
			"courierMobile": "13800138000",
			"weight": "1",
			"defPrice": "15.0",
			"freight": "5.1",
			"volume": "120",
			"actualWeight": "1",
			"feeDetails": [{
				"feeType": "PACKAGINGFEE",
				"feeDesc": "包装费",
				"amount": "0.8",
				"payStatus": 1
			}]，
			"printTaskId": "*****",
			"imgBase64": "*****"


		}
	}
Copy
返回结果示例

{

		"result":true,

		"returnCode":"200",

		"message":"成功"

}
Copy
2.4 回调响应报文及错误码解释
字段名称	字段含义
result	true表示成功，false表示失败。如果提交回调接口的地址失败，30分钟后重新回调，3次仍旧失败的，自动放弃
returnCode	200: 提交成功 500: 服务器错误 其他错误请自行定义
message	返回的提示
三、商家寄件下单取消接口
对下完单且取件前的订单进行取消操作。对于不需邮寄的订单请及时进行取消，当月订单最晚需在次月8日前取消，否则将无法取消。

3.1 接口格式
提供统一格式的HTTP POST或GET调用接口,并返回统一格式JSON数据。

3.2 请求地址
https://poll.kuaidi100.com/order/borderapi.do

请求参数：

参数名	是否必填	类型	说明
method	是	string	业务类型（默认：cancel）
key	是	string	授权码，请到快递100页面申请企业版接口获取
sign	是	string	32位大写，签名，用于验证身份，按MD5 (param +t+key+ secret)的顺序进行MD5加密，不需要加上“+”号secret在授权邮件里面有
t	是	string	时间戳如：1576123932000
param	是	param	由其他字段拼接
param数据结构：

参数名	是否必填	类型	说明
taskId	是	string	任务ID
orderId	是	string	订单ID
cancelMsg	是	string	取消原因，例：暂时不寄件了，最大长度不超过30字符
3.3 返回结果
字段	类型	说明	备注
result	boolean	提交结果	true提交成功，false失败
returnCode	string	返回编码	
message	string	返回报文描述	
data	data		这里默认为空
3.4 提供数据内容
请求参数示例

method = cancel
key = ******
sign = 4BBDE07660E5EFF90873642CFAE9A8DD
t = 1647258957705
param = {
	"taskId": "*****",
	"orderId": "*****",
	"cancelMsg": "暂时不寄件了"
}
Copy
返回结果示例

{

		"result":true,

		"returnCode":"200",

		"message":"取消成功",

		"data":{}

}
Copy
说明：

信息代码	信息内容描述	原因及建议处理方式
200	提交成功	提交成功
400	参数错误等	请根据技术文档请求，注意参数类型及是否必填
500	服务器错误	快递100的服务器出理间隙或临时性异常，有时如果因为不按规范提交请求，比如快递公司参数写错等，或者对已取消的订单重复操作取消，也会报此错误
501	重复提交	重复提交了请求
503	验证签名失败	请检查加密方式，param + t + key + secret 的顺序进行MD5加密，加密后字符串转大写,不用加上“+”号
600	您不是合法的用户（即授权Key出错）	账号无可用余额，需要充值
601	KEY已过期	账号无可用余额，需要充值
700	错误的回调地址	检查回调地址，或者联系快递100工作人员
四、商家寄件下单价格接口
查看从出发地到目的地的价格

4.1 接口格式
提供统一格式的HTTP POST或GET调用接口,并返回统一格式JSON数据。

4.2 请求地址
https://poll.kuaidi100.com/order/borderapi.do

请求参数：

参数名	是否必填	类型	说明
method	是	string	业务类型（默认：price）
key	是	string	授权码，请到快递100页面申请企业版接口获取
sign	是	string	32位大写，签名，用于验证身份，按MD5 (param +t+key+ secret)的顺序进行MD5加密，不需要加上“+”号secret在授权邮件里面有
t	是	string	时间戳如：1576123932000
param	是	param	由其他字段拼接
param数据结构：

参数名	是否必填	类型	说明
kuaidiCom	是	string	快递公司编码
sendManPrintAddr	是	string	出发地地址，最小颗粒到市级，例如：广东省深圳市
recManPrintAddr	是	string	目的地地址，最小颗粒到市级，例如：广东省深圳市
weight	否	string	重量，单位：kg，默认：1KG
serviceType	否	string	业务类型
channelSw	否	string	渠道ID，如有多个同品牌运力，请联系商务提供后传入
4.3 返回结果
字段	类型	说明	备注
result	boolean	提交结果	true提交成功，false失败
returnCode	string	返回编码	
message	string	返回报文描述	
data	data	运力对象	
data数据结构：

参数名	类型	说明
defFirstPrice	string	标准首重价格，单位：元
defOverPrice	string	标准续重价格，单位：元
defPrice	string	标准总价，单位：元
firstPrice	string	折后首重价格，单位：元
overPrice	string	折后续重价格，单位：元
price	string	折后总价，单位：元
serviceType	string	业务类型
4.4 提供数据内容
请求参数示例

method = price
key = ******
sign = 4BBDE07660E5EFF90873642CFAE9A8DD
t = 1647258957705
param = {
	"kuaidicom": "yuantong",
	"sendManPrintAddr": "西藏日喀则市定日县珠穆朗玛峰",
	"recManPrintAddr": "西藏日喀则市定日县珠穆朗玛峰",
	"weight": "1",
	"serviceType": "标准快递"
}
Copy
返回结果示例

​

{
	"data": {
		"firstPrice": "8.0",
		"defPrice": "8.0",
		"defFirstPrice": "8.0",
		"price": "8.0",
		"serviceType": "标准快递",
		"overPrice": "0",
		"defOverPrice": "0",
		"kuaidiCom": "zhongtong"
	},
	"message": "成功",
	"result": true,
	"returnCode": "200"
}
Copy
说明：

信息代码	信息内容描述	原因及建议处理方式
200	提交成功	提交成功
400	参数错误等	请根据技术文档请求，注意参数类型及是否必填
500	服务器错误	快递100的服务器出理间隙或临时性异常，有时如果因为不按规范提交请求，比如快递公司参数写错等，也会报此错误
503	验证签名失败	请检查加密方式，param + t + key + secret 的顺序进行MD5加密，加密后字符串转大写,不用加上“+”号
600	您不是合法的用户（即授权Key出错）	账号无可用余额，需要充值
601	KEY已过期	账号无可用余额，需要充值
五、实时快递查询接口
下单后可携带pollToken请求该接口实时查询物流轨迹信息。

注意：请控制每一单查询频率至少在半小时以上，否则会造成锁单。

5.1 请求地址
https://poll.kuaidi100.com/poll/query.do

5.2 请求类型
post

5.3 输入参数
请求参数（header）

名称	类型	默认值
Content-Type	string	application/x-www-form-urlencoded
请求参数（body）

名称	类型	是否必需	示例值	描述
customer	String	是		授权码，请申请企业版获取
sign	String	是		签名， 用于验证身份， 按param + key + customer 的顺序进行MD5加密（注意加密后字符串一定要转32位大写）， 不需要加上“+”号
pollToken	string	是		查询密钥，仅通过商家寄件下单后调用实时快递查询时入参该字段可免扣费查询
Θparam	Object	是		由其他字段拼接
└ com	string	是	yuantong	查询的快递公司的编码
└ num	string	是	12345678	查询的快递单号， 单号的最小长度6个字符，最大长度32个字符
└ phone	string	否	13888888888	收、寄件人的电话号码（手机和固定电话均可，只能填写一个，顺丰速运、顺丰快运必填，其他快递公司选填。如座机号码有分机号，分机号无需传入；如号码是电商虚拟号码需传入“-“后的后四位。查看详情）
└ from	string	否	广东深圳	出发地城市
└ to	string	否	北京朝阳	目的地城市，到达目的地后会加大监控频率
└ resultv2	string	否	1	添加此字段表示开通行政区域解析功能。空：关闭（默认），1：开通行政区域解析功能以及物流轨迹增加物流状态名称 4: 开通行政解析功能以及物流轨迹增加物流高级状态名称、状态值并且返回出发、目的及当前城市信息
└show	String	否	0	返回格式：0：json格式（默认），1：xml，2：html，3：text
└order	String	否	desc	返回结果排序:desc降序（默认）,asc 升序
5.4 请求参数示例
customer = **********
sign = ******************
pollToken = ******************
param = {
    "com": "ems",
    "num": "em263999513jp",
    "phone": "13868688888",
    "from": "广东省深圳市南山区",
    "to": "北京市朝阳区",
    "resultv2": "4",
    "show": "0",
    "order": "desc"
}
Copy
5.5 返回结果
字段名称	类型	字段含义
message	String	消息体，请忽略
state	String	快递单当前状态，默认为0在途，1揽收，2疑难，3签收，4退签，5派件，8清关，14拒签等10个基础物流状态，如需要返回高级物流状态，请参考 resultv2 传值
status	String	通讯状态，请忽略
condition	String	快递单明细状态标记，暂未实现，请忽略
ischeck	String	是否签收标记，0未签收，1已签收，请忽略，明细状态请参考state字段
com	String	快递公司编码,一律用小写字母
nu	String	单号
Θdata	data	最新查询结果，数组，包含多项，全量，倒序（即时间最新的在最前），每项都是对象，对象包含字段请展开
└ context	String	内容
└ time	String	时间，原始格式
└ ftime	String	格式化后时间
└status	String	本数据元对应的物流状态名称或者高级状态名称，实时查询接口中提交resultv2=1或者resultv2=4标记后才会出现
└statusCode	String	本数据元对应的高级物流状态值，实时查询接口中提交resultv2=4标记后才会出现
└areaCode	String	本数据元对应的行政区域的编码，实时查询接口中提交resultv2=1或者resultv2=4标记后才会出现
└areaName	String	本数据元对应的行政区域的名称，实时查询接口中提交resultv2=1或者resultv2=4标记后才会出现
└areaCenter	String	本数据元对应的行政区域经纬度，实时查询接口中提交resultv2=4标记后才会出现
└location	String	本数据元对应的快件当前地点，实时查询接口中提交resultv2=4标记后才会出现
└areaPinYin	String	本数据元对应的行政区域拼音，实时查询接口中提交resultv2=4标记后才会出现


5.6 运单快递状态（state)说明
物流状态值	物流状态名称	高级物流状态值	高级物流状态名称	含义
1	揽收	1	揽收	快件揽件
101	已下单	已经下快件单
102	待揽收	待快递公司揽收
103	已揽收	快递公司已经揽收
0	在途	0	在途	快件在途中
1001	到达派件城市	快件到达收件人城市
1002	干线	快件处于运输过程中
1003	转递	快件发往到新的收件地址
5	派件	5	派件	快件正在派件
501	投柜或驿站	快件已经投递到快递柜或者快递驿站
3	签收	3	签收	快件已签收
301	本人签收	收件人正常签收
302	派件异常后签收	快件显示派件异常，但后续正常签收
303	代签	快件已被代签
304	投柜或站签收	快件已从快递柜或者驿站取出签收
6	退回	6	退回	快件正处于返回发货人的途中
4	退签	4	退签	此快件单已退签
401	已销单	此快件单已撤销
14	拒签	收件人拒签快件
7	转投	7	转投	快件转给其他快递公司邮寄
2	疑难	2	疑难	快件存在疑难
201	超时未签收	快件长时间派件后未签收
202	超时未更新	快件长时间没有派件或签收
203	拒收	收件人发起拒收快递,待发货方确认
204	派件异常	快件派件时遇到异常情况
205	柜或驿站超时未取	快件在快递柜或者驿站长时间未取
206	无法联系	无法联系到收件人
207	超区	超出快递公司的服务区范围
208	滞留	快件滞留在网点，没有派送
209	破损	快件破损
210	销单	寄件人申请撤销寄件
8	清关	8	清关	快件清关
10	待清关	快件等待清关
11	清关中	快件正在清关流程中
12	已清关	快件已完成清关流程
13	清关异常	货物在清关过程中出现异常
14	拒签	\	\	收件人拒签快件
注：如需物流状态高级状态名称及状态值需要 resultv2 传 “4” 返回

5.7 正确返回示例
JSON格式

{
    "message": "ok",
    "nu": "JT0004301991791",
    "ischeck": "0",
    "com": "jtexpress",
    "status": "200",
    "data": [
        {
            "time": "2021-12-15 17:19:28",
            "context": "【杭州市】您的包裹已存放至【驿站】，记得早点来取它回家！",
            "ftime": "2021-12-15 17:19:28",
            "areaCode": "CN330102000000",//本数据元对应的行政区域编码，resultv2=1或者resultv2=4才会展示
            "areaName": "浙江,杭州市,上城区",//本数据元对应的行政区域名称，resultv2=1或者resultv2=4才会展示
            "status": "投柜或驿站",//本数据元对应的物流状态名称或者高级物流状态名称，resultv2=1或者resultv2=4才会展示
            "location": "浙江省 杭州市 上城区", //本数据元对应的快件当前地点，resultv2=4才会展示
            "areaCenter": "120.184349,30.25446", //本数据元对应的行政区域经纬度，resultv2=4才会展示
            "areaPinYin": "shang cheng qu",//本数据元对应的行政区域拼音，resultv2=4才会展示
            "statusCode": "501"//本数据元对应的高级物流状态值，resultv2=4才会展示
        },
        {
            "time": "2021-12-15 14:17:31",
            "context": "【杭州市】【杭州网点】的极兔小哥正在派件",
            "ftime": "2021-12-15 14:17:31",
            "areaCode": "CN330102000000",
            "areaName": "浙江,杭州市,上城区",
            "status": "派件",
            "location": "浙江省 杭州市 上城区",
            "areaCenter": "120.184349,30.25446",
            "areaPinYin": "shang cheng qu",
            "statusCode": "5"
        },
        {
            "time": "2021-12-15 13:58:18",
            "context": "【杭州市】 快件到达【杭州网点】",
            "ftime": "2021-12-15 13:58:18",
            "areaCode": "CN330102000000",
            "areaName": "浙江,杭州市,上城区",
            "status": "在途",
            "location": "浙江省 杭州市 上城区",
            "areaCenter": "120.184349,30.25446",
            "areaPinYin": "shang cheng qu",
            "statusCode": "0"
        },
        {
            "time": "2021-12-15 04:11:20",
            "context": "【杭州市】快件离开【杭州转运中心】已发往【杭州江干四季青网点】",
            "ftime": "2021-12-15 04:11:20",
            "areaCode": "CN330109000000",
            "areaName": "浙江,杭州市,萧山区",
            "status": "干线",
            "location": "浙江省 杭州市 萧山区",
            "areaCenter": "120.493286,30.28333",
            "areaPinYin": "xiao shan qu",
            "statusCode": "1002"
        },
        {
            "time": "2021-12-15 02:09:52",
            "context": "【杭州市】 快件到达【杭州转运中心】",
            "ftime": "2021-12-15 02:09:52",
            "areaCode": "CN330109000000",
            "areaName": "浙江,杭州市,萧山区",
            "status": "干线",
            "location": "浙江省 杭州市 萧山区",
            "areaCenter": "120.493286,30.28333",
            "areaPinYin": "xiao shan qu",
            "statusCode": "1002"
        },
        {
            "time": "2021-12-14 21:08:34",
            "context": "【上海市】快件离开【上海浦西转运中心】已发往【杭州转运中心】",
            "ftime": "2021-12-14 21:08:34",
            "areaCode": "CN310118000000",
            "areaName": "上海,上海,青浦区",
            "status": "干线",
            "location": "上海 上海市 青浦区",
            "areaCenter": "121.124178,31.150681",
            "areaPinYin": "qing pu qu",
            "statusCode": "1002"
        },
        {
            "time": "2021-12-14 20:54:22",
            "context": "【上海市】 快件到达【上海浦西转运中心】",
            "ftime": "2021-12-14 20:54:22",
            "areaCode": "CN310118000000",
            "areaName": "上海,上海,青浦区",
            "status": "干线",
            "location": "上海 上海市 青浦区",
            "areaCenter": "121.124178,31.150681",
            "areaPinYin": "qing pu qu",
            "statusCode": "1002"
        },
        {
            "time": "2021-12-14 17:25:58",
            "context": "【上海市】快件离开【上海杨浦黄兴路网点】已发往【上海浦西转运中心】",
            "ftime": "2021-12-14 17:25:58",
            "areaCode": "CN310110000000",
            "areaName": "上海,上海,杨浦区",
            "status": "干线",
            "location": "上海 上海市 杨浦区",
            "areaCenter": "121.526077,31.259541",
            "areaPinYin": "yang pu qu",
            "statusCode": "1002"
        },
        {
            "time": "2021-12-14 09:03:58",
            "context": "【上海市】【上海杨浦黄兴路网点】已取件。",
            "ftime": "2021-12-14 09:03:58",
            "areaCode": "CN310110000000",
            "areaName": "上海,上海,杨浦区",
            "status": "揽收",
            "location": "上海 上海市 杨浦区",
            "areaCenter": "121.526077,31.259541",
            "areaPinYin": "yang pu qu",
            "statusCode": "1"
        }
    ],
    "state": "5",
    "condition": "00",
    "routeInfo": {
        "from": {
            "number": "CN310110000000",
            "name": "上海,上海,杨浦区"
        },//本数据元对应的出发地城市信息，resultv2=4才会展示
        "cur": {
            "number": "CN330102000000",
            "name": "浙江,杭州市,上城区"
        },//本数据元对应的当前城市信息，resultv2=4才会展示
        "to": null
    },//本数据元对应的目的地城市信息，resultv2=4才会展示
    "isLoop": false
}
}
Copy
5.8 错误返回示例
JSON格式

{
    "result": false,
    "returnCode": "400",
    "message": "找不到对应公司"
}
Copy
5.9 信息代码含义
信息代码	信息内容描述	原因及建议处理方式
200	查询成功	查询成功
400	找不到对应公司	提交数据不完整或者账号未充值, 检查提交的格式是否为x-www-form-urlencoded的post格式
408	快递公司参数异常：验证码错误	电话号码校验不通过，检查是否提交了收、寄件人正确的电话号码
500	查询无结果，请隔段时间再查	表示查询失败，或没有POST提交，或polltoken不正确
501	服务器错误	快递100的服务器出现间歇或临时性异常，有时如果因为不按规范提交请求，比如快递公司参数没有按照文档规定填写等，也会报此错误
502	服务器繁忙	快递100的服务器出现间歇或临时性异常，请联系快递100排查原因
503	验证签名失败	请检查加密方式，param + key + customer 的顺序进行MD5加密，加密后字符串转大写
601	key已过期	没有可用单量，账号需要充值使用
六、快递信息推送接口
6.1 推送请求地址
由贵司在下单开启订阅功能中通过pollCallBackUrl字段提供

6.2 推送请求类型
post

6.3 推送输入参数*（以下为订阅参数resultv2=1时的推送返回参数示例，如使用地图订阅（resultv2=3），接口推送返回参数请参考地图轨迹推送接口文档）
请求参数（header）

名称	类型	默认值
Content-Type	string	application/x-www-form-urlencoded
请求参数（body）

名称	类型	示例值	描述
sign	String		订阅参数salt值不为null时，推送数据将包含该加密签名，加密方式：md5(param+salt)。注意： salt值为空串时，推送的数据也会包含sign。
Θparam			由其他字段拼接
└ status	String	polling	监控状态:polling:监控中，shutdown:结束，abort:中止，updateall：重新推送。其中当快递单为已签收时status=shutdown，当message为“3天查询无记录”或“60天无变化时”status= abort ，对于status=abort的状态，需要增加额外的处理逻辑
└ billstatus	String	got	包括got、sending、check三个状态，由于意义不大，已弃用，请忽略
└ message	String		监控状态相关消息，如:3天查询无记录，60天无变化
└ autoCheck	String	1	快递公司编码是否出错，0为本推送信息对应的是贵司提交的原始快递公司编码，1为本推送信息对应的是我方纠正后的新的快递公司编码。一个单如果我们连续3天都查不到结果，我方会（1）判断一次贵司提交的快递公司编码是否正确，如果正确，给贵司的回调接口（callbackurl）推送带有如下字段的信息：autoCheck=0、comOld与comNew都为空；（2）如果贵司提交的快递公司编码出错，我们会帮忙用正确的快递公司编码+原来的运单号重新提交订阅并开启监控（后续如果监控到单号有更新就给贵司的回调接口（callbackurl）推送带有如下字段的信息：autoCheck=1、comOld=原来的公司编码、comNew=新的公司编码）；并且给贵方的回调接口（callbackurl）推送一条含有如下字段的信息：status=abort、autoCheck=0、comOld为空、comNew=纠正后的快递公司编码。
└ comOld	String	yuantong	贵司提交的原始的快递公司编码。详细见autoCheck后说明。若开启了国际版（即在订阅请求中增加字段interCom=1），则回调请求中暂无此字段
└ comNew	String	ems	我司纠正后的新的快递公司编码。详细见autoCheck后说明。若开启了国际版（即在订阅请求中增加字段interCom=1），则回调请求中暂无此字段
ΘlastResult	lastResult		最新查询结果，若在订阅报文中通过interCom字段开通了国际版，则此lastResult表示出发国的查询结果，全量，倒序（即时间最新的在最前）
└- message	String		消息体，请忽略
└- state	String	0	快递单当前状态，默认为0在途，1揽收，2疑难，3签收，4退签，5派件，8清关，14拒签等10个基础物流状态，如需要返回高级物流状态，请参考 resultv2 传值
└- status	String	200	通讯状态，请忽略
└- condition	String	F00	快递单明细状态标记，暂未实现，请忽略
└- ischeck	String	0	是否签收标记，0未签收，1已签收
└- com	String	yuantong	快递公司编码,一律用小写字母
└- nu	String	V030344422	单号
└- data	Object		数组，包含多个对象，每个对象字段如展开所示
└-- context	String	上海分拨中心/装件入车扫描	内容
└-- time	String	2012-08-28 16:33:19	时间，原始格式
└-- ftime	String	2012-08-28 16:33:19	格式化后时间
└-- status	String	在途	物流状态名称或者高级状态名称，提交resultv2=1或者resultv2=4标记后才会出现
└-- statusCode	String	1002	本数据元对应的高级物流状态值，提交resultv2=4标记后才会出现
└-- areaCode	String	310000000000	本数据元对应的行政区域的编码，提交resultv2=1或者resultv2=4标记后才会出现
└-- areaName	String	上海市	本数据元对应的行政区域的名称，提交resultv2=1或者resultv2=4标记后才会出现
└--areaCenter	String	17.200983,39.084158	本数据元对应的行政区域经纬度，提交resultv2=4标记后才会出现
└--location	String	深圳中心	本数据元对应的快件当前位置，提交resultv2=4标记后才会出现
└--areaPinYin	String	tianjin	本数据元对应的行政区域拼音，提交resultv2=4标记后才会出现
Θ destResult	destResult		表示最新的目的国家的查询结果，只有在订阅报文中通过interCom=1字段开通了国际版才会显示此数据元，全量，倒序（即时间最新的在最前）
└- message	String		消息体，请忽略
└- state	String	0	快递单当前状态，默认为0在途，1揽收，2疑难，3签收，4退签，5派件，8清关，14拒签等10个基础物流状态，如需要返回高级物流状态，请参考 resultv2 传值
└- status	String	200	通讯状态，请忽略
└- condition	String	F00	快递单明细状态标记，暂未实现，请忽略
└- ischeck	String	0	是否签收标记，0未签收，1已签收
└- com	String	yuantong	快递公司编码,一律用小写字母
└- nu	String	V030344422	单号
Θ data	data		数组，包含多个对象，每个对象字段如展开所示
└-- context	String	上海分拨中心/装件入车扫描	内容
└-- time	String	2012-08-28 16:33:19	时间，原始格式
└-- ftime	String	2012-08-28 16:33:19	格式化后时间
└-- status	String	在途	本数据元对应的物流状态名称或者高级状态名称，提交resultv2=1或者resultv2=4标记后才会出现
└-- areaCode	String	310000000000	本数据元对应的行政区域的编码，提交resultv2=1或者resultv2=4标记后才会出现
└-- areaName	String	上海市	本数据元对应的行政区域的名称，提交resultv2=1或者resultv2=4标记后才会出现
└--areaCenter	String	17.200983,39.084158	本数据元对应的行政区域经纬度，提交resultv2=4标记后才会出现
└--location	String	深圳中心	本数据元对应的快件当前位置，提交resultv2=4标记后才会出现
└--areaPinYin	String	tianjin	本数据元对应的行政区域拼音，提交resultv2=4标记后才会出现
6.4 推送输入参数示例
{
	"status": "polling",
	"billstatus": "got",
	"message": "寄件",
	"lastResult": {
		"message": "ok",
		"nu": "YT6186594166532",
		"ischeck": "0",
		"com": "yuantong",
		"status": "200",
		"data": [{
				"time": "2021-12-15 20:15:14",
				"context": "【苏州转运中心】 已发出 下一站 【无锡转运中心公司】",
				"ftime": "2021-12-15 20:15:14",
				"areaCode": "CN320500000000",
				"areaName": "江苏,苏州市",
				"status": "干线",
				"location": "",
				"areaCenter": "120.585315,31.298886",
				"areaPinYin": "su zhou shi",
				"statusCode": "1002"
			},
			{
				"time": "2021-12-15 20:11:25",
				"context": "【苏州转运中心公司】 已收入",
				"ftime": "2021-12-15 20:11:25",
				"areaCode": "CN320500000000",
				"areaName": "江苏,苏州市",
				"status": "干线",
				"location": "",
				"areaCenter": "120.585315,31.298886",
				"areaPinYin": "su zhou shi",
				"statusCode": "1002"
			},
			{
				"time": "2021-12-15 19:18:27",
				"context": "【江苏省无锡市锡新开发区公司】 已收入",
				"ftime": "2021-12-15 19:18:27",
				"areaCode": "CN320200000000",
				"areaName": "江苏,无锡市",
				"status": "在途",
				"location": "",
				"areaCenter": "120.31191,31.491169",
				"areaPinYin": "wu xi shi",
				"statusCode": "0"
			},
			{
				"time": "2021-12-15 17:10:09",
				"context": "【江苏省苏州市北桥公司】 已揽收",
				"ftime": "2021-12-15 17:10:09",
				"areaCode": "CN320507004000",
				"areaName": "江苏,苏州市,相城区,北桥",
				"status": "揽收",
				"location": "",
				"areaCenter": "120.606531,31.505825",
				"areaPinYin": "bei qiao jie dao",
				"statusCode": "1"
			}
		],
		"state": "0",
		"condition": "F00",
		"routeInfo": {
			"from": {
				"number": "CN320507004000",
				"name": "江苏,苏州市,相城区,北桥"
			},
			"cur": {
				"number": "CN320200000000",
				"name": "江苏,无锡市"
			},
			"to": null
		},
		"isLoop": false
	}
}
Copy
6.5 运单签收状态服务说明
物流状态值	物流状态名称	高级物流状态值	高级物流状态名称	含义
1	揽收	1	揽收	快件揽件
101	已下单	已经下快件单
102	待揽收	待快递公司揽收
103	已揽收	快递公司已经揽收
0	在途	0	在途	快件在途中
1001	到达派件城市	快件到达收件人城市
1002	干线	快件处于运输过程中
1003	转递	快件发往到新的收件地址
5	派件	5	派件	快件正在派件
501	投柜或驿站	快件已经投递到快递柜或者快递驿站
3	签收	3	签收	快件已签收
301	本人签收	收件人正常签收
302	派件异常后签收	快件显示派件异常，但后续正常签收
303	代签	快件已被代签
304	投柜或驿站签收	快件已由快递柜或者驿站签收
6	退回	6	退回	快件正处于返回发货人的途中
4	退签	4	退签	此快件单已退签
401	已销单	此快件单已撤销
14	拒签	收件人拒签快件
7	转投	7	转投	快件转给其他快递公司邮寄
2	疑难	2	疑难	快件存在疑难
201	超时未签收	快件长时间派件后未签收
202	超时未更新	快件长时间没有派件或签收
203	拒收	收件人发起拒收快递,待发货方确认
204	派件异常	快件派件时遇到异常情况
205	柜或驿站超时未取	快件在快递柜或者驿站长时间未取
206	无法联系	无法联系到收件人
207	超区	超出快递公司的服务区范围
208	滞留	快件滞留在网点，没有派送
209	破损	快件破损
8	清关	8	清关	快件清关
10	待清关	快件等待清关
11	清关中	快件正在清关流程中
12	已清关	快件已完成清关流程
13	清关异常	货物在清关过程中出现异常
14	拒签	\	\	收件人拒签快件
注：如需物流状态高级状态名称及状态值需要 resultv2 传 “4” 返回

6.6 推送响应报文及错误码解释
字段名称	字段含义
result	true表示成功，false表示失败。如果提交回调接口的地址失败，30分钟后重新回调，3次仍旧失败的，自动放弃
returnCode	200: 提交成功 500: 服务器错误 其他错误请自行定义
message	返回的提示
6.7 推送返回示例
当我方调用贵方的回调接口（pollCallBackUrl）时，贵方需要先将我方提交的数据保存至贵方的数据库，接着向我方返回是否成功接收的响应报文及代码，即贵公司直接在回调接口的地址的response中填写如下内容：

{
    "result":true,
    "returnCode":"200",
    "message":"成功"
}
Copy
注意：对于status= abort（message中包含“3天查询无记录”或者“60天无变化”）的快递单，也需要返回成功接收的响应报文及代码。

七、快递公司编码
快递公司	编码
京东	jd
德邦	debangkuaidi
顺丰	shunfeng
极兔	jtexpress
圆通	yuantong
申通	shentong
中通	zhongtong
韵达	yunda
菜鸟直送(丹鸟)	cainiaozhisong
EMS	ems
跨越	kuayue
八、业务类型参数表
快递公司名称	业务类型
jd（京东）	特惠送
debangkuaidi（德邦）	标准快递
德邦大件360
精准卡航
精准汽运
shunfeng（顺丰）	顺丰标快
顺丰特快
jtexpress（极兔）	标准快递
yuantong（圆通）	标准快递
shentong（申通）	标准快递
zhongtong（中通）	标准快递
yunda（韵达）	标准快递
ems（邮政）	标准快递
kuayue（跨越）	专运
九、费用类型(feeType)说明
类型	说明
INSURANCEFEE	保价费
PACKAGINGFEE	包装费
COLLECTIONFEE	代收货款
SIGNANDRETURN	签单返还
HANDLINGCHARGES	装卸服务
PICKUPSERVICEFEE	提货服务
DELIVERYSERVICEFEE	配送服务
RESOURCECONDITIONINGCHARGES	资源调节费
ANIMALQUARANTINECERTIFICATE	动检证
SPECIALWAREHOUSINGFEE	特殊入仓
SURCHARGE	附加费
EXPRESSCOMPENSATION	快递赔付费
RECEIVEDELIVERYFEECLAIMS	收派服务费-理赔
EXPRESSADDRCHANGEFEE	快递改址费
EXCEEDEDAREAFEE	超区服务费
OVERLENGTHOVERWEIGHTFEE	超长超重附加费
RETURNFEE	逆向费用
FRESHFEE	保鲜服务费
FULLINSURANCEFEE	顺丰足额保
OTHERFEE	其他费用
十、商家寄件订单详情查询接口
查询通过商家寄件下单接口下单的订单详情信息

10.1 接口格式
提供统一格式的HTTP POST或GET调用接口,并返回统一格式JSON数据。

10.2 请求地址
https://order.kuaidi100.com/order/borderapi.do

10.3 请求参数
参数名	是否必填	类型	说明
method	是	string	业务类型（默认：detail）
key	是	string	授权码，请到快递100页面申请企业版接口获取
sign	是	string	32位大写签名，用于验证身份，按MD5 (param +t+key+ secret)的顺序进行MD5加密，不需要加上“+”号secret在授权邮件里面有
t	是	string	时间戳如：1576123932000
param	是	param	由其他字段拼接
param数据结构：

参数名	是否必填	类型	说明
taskId	是	string	下单时返回的任务ID
10.4 返回结果
字段	类型	说明	备注
result	boolean	提交结果	true提交成功，false失败
returnCode	string	返回编码	
message	string	返回报文描述	
data	data		
data数据结构

字段	类型	说明
taskId	string	任务ID
createTime	date	下单时间
orderId	string	订单ID
kuaidiCom	string	快递公司
kuaidiNum	string	快递单号
sendName	string	寄件人姓名
sendMobile	string	寄件人联系方式
sendProvince	string	寄件人省份
sendCity	string	寄件人城市
sendDistrict	string	寄件人区
sendAddr	string	寄件人详细地址
recName	string	收件人姓名
recMobile	string	收件人联系方式
recProvince	string	收件人省份
recCity	string	收件人城市
recDistrict	string	收件人区
recAddr	string	收件人详细地址
valins	string	保价费用
cargo	string	物品名称
serviceType	string	业务类型
preWeight	string	用户端输入重量，单位：kg
lastWeight	string	快递公司返回最终重量 ，单位：kg
comment	string	备注内容
dayType	string	预约日期
pickupStartTime	string	预约时间
pickupEndTime	string	预约截止时间
payment	string	支付方式，SHIPPER: 寄付，CONSIGNEE: 到付， MONTHLY:月结
freight	string	折后运费，单位：元
courierName	string	快递员姓名
courierMobile	string	快递员联系方式
status	Integer	订单状态： 0：'下单成功'； 1：'已接单'； 2：'收件中'； 9：'用户主动取消'； 10；'已取件'； 11；'揽货失败'； 12；'已退回'； 13；'已签收'； 14；'异常签收'；15；'已结算' 99；'订单已取消'101；'运输中'；200：'已出单'201：'出单失败'；610：'下单失败'；155：'修改重量'(注意需要在工单系统中发起异常反馈并由快递100服务人员确认调重后才会有此状态回调，回调内容包含修改重量后的重量、运费、费用明细、业务类型)；166：订单复活（订单被取消，但是实际包裹已经发出，正常计费）；400：派送中
payStatus	Integer	支付状态，0:预扣款，1：已支付，3：退款中，4：已退款
defPrice	Integer	标准运费，单位：元
ΘfeeDetails	list	费用明细，明细项目请展开
└ feeType	string	费用类型，参考八、费用类型(feeType)说明
└ feeDesc	string	费用名称
└ amount	string	费用明细金额，单位：元
└ payStatus	string	支付状态，支付失败：-1；未支付：0；已支付：1 ；无需支付：2；已退款：3
10.5 返回参数示例
{
	"data": {
		"cargo": "普货",
		"comment": null,
		"courierMobile": null,
		"courierName": null,
		"createTime": "2022-04-22 01:23:40",
		"dayType": null,
		"freight": null,
		"feeDetails": [{
			"feeType": "",
			"feeDesc": "",
			"amount": "",
			"payStatus": ""
		}]，
		"kuaidiCom": "jd",
		"kuaidiNum": "JDVC13641480904",
		"lastWeight": null,
		"orderId": "20274573",
		"payStatus": 4,
		"payment": "SHIPPER",
		"pickupEndTime": null,
		"pickupStartTime": null,
		"preWeight": "1",
		"defPrice": null,
		"recAddr": "长安塘村长兴工业区12号3楼",
		"recCity": "东莞市",
		"recDistrict": "东坑镇",
		"recMobile": "13800138000",
		"recName": "李谬朵",
		"recProvince": "广东",
		"sendAddr": "蔡子池街道新大陆国际监控室",
		"sendCity": "衡阳市",
		"sendDistrict": "耒阳市",
		"sendMobile": "13800138000",
		"sendName": "李新红",
		"sendProvince": "湖南",
		"serviceType": "特惠送",
		"status": 9,
		"taskId": "44B0108CF44728E6F8A121177E3212B2",
		"valins": null
	},
	"message": "成功",
	"result": true,
	"returnCode": "200"
}
Copy
说明：

信息代码	信息内容描述	原因及建议处理方式
200	提交成功	提交成功
400	参数错误等	请根据技术文档请求，注意参数类型及是否必填
500	服务器错误	快递100的服务器出理间隙或临时性异常，有时如果因为不按规范提交请求，比如快递公司参数写错等，也会报此错误
503	验证签名失败	请检查加密方式，param + t + key + secret 的顺序进行MD5加密，加密后字符串转大写,不用加上“+”号
600	您不是合法的用户（即授权Key出错）	账号无可用余额，需要充值
601	KEY已过期	账号无可用余额，需要充值
十一、电子面单复打接口
该接口支持在提交打印请求2天内的打印任务进行复打10次的操作（仅对下单时returnType=10的支持复打操作）。

11.1 接口格式
提供统一格式的HTTP POST或GET调用接口,并返回统一格式JSON数据。

11.2 请求地址
https://api.kuaidi100.com/label/order

请求参数：

参数名	是否必填	类型	说明
method	是	string	业务类型（默认：printOld）
key	是	string	授权码，请申请企业版获取
sign	是	string	32位大写，签名，用于验证身份，按MD5 (param +t+key+ secret)的顺序进行MD5加密，不需要加上“+”号secret在企业管理后台查看
t	是	string	时间戳如：1576123932000
param	是	param	由其他字段拼接
param数据结构：

参数名	是否必填	类型	说明
printTaskId	是	string	任务ID
siid	否	string	快递100打印机,不填默认为下单时填入的siid
11.3 返回结果
字段	类型	说明	备注
success	boolean	提交结果	true提交成功，false失败
code	string	返回编码	
message	String	返回报文描述	
data	String	图片复打时会有返回	
11.4 提供数据内容
打印设备复打成功返回示例

{
	"code": 200,
	"message": "success",
	"time": 0,
	"success": true
}
Copy
错误示例：

{
	"code": 30009,
	"message": "查无此单",
	"time": 0,
	"success": false
}
Copy
11.5 返回信息代码含义
信息代码	信息内容描述	原因及建议处理方式
-1	服务器错误	快递100的服务器出现间歇或临时性异常，有时如果因为不按规范提交请求，比如快递公司参数写错等，也会报此错误
200	提交成功	提交成功
30001	参数错误	请根据技术文档请求，注意参数类型及是否必填
30002	验证签名失败	检查加密方式，param +t+key+ secret的顺序进行MD5加密，加密后字符串转32位大写,不用加上“+”号
30003	账号信息不正确	检查key是否正确
30004	账号单量不足	单量不足需要充值
30009	查无此单	可能调用过期或复打次数超过限制
-------------------------------------
来源：快递100API开放平台
标题：快递寄件接口-官方快递-快递100API开放平台
链接：https://api.kuaidi100.com/document/603cb649a62a19500e19866b

商家寄件工单接口
企业用户可以将异常反馈工单功能集成到自有平台或系统使用，反馈重量异常、虚假揽收等订单情况。本接口有流量控制，同一用户在5秒内调用接口超过100次将被限制访问。目前支持的快递品牌有圆通、申通和德邦。

一、创建工单
1.1 接口格式
提供统一格式的HTTP POST调用接口,并返回统一格式JSON数据。

1.2 请求地址
https://api.kuaidi100.com/workorder/api/create

请求参数（header）：

名称	类型	默认值
Content-Type	string	application/x-www-form-urlencoded
请求参数（query）：

参数名	是否必填	类型	说明
key	是	string	授权码，请到快递100页面申请企业版接口获取
sign	是	string	32位大写签名，用于验证身份，按MD5 (param +t+key+ secret)的顺序进行MD5加密，不需要加上“+”号。secret在企业管理后台获取
t	是	string	时间戳如：1576123932000
param	是	param	由其他字段拼接
param数据结构：

参数名	是否必填	类型	说明
secondType	是	int	反馈类型（参考附录6.1）
kuaidinum	是	string	快递单号
desc	是	object	问题描述
callBackUrl	否	string	工单结果回调地址（非必填）
msgCallBackUrl	否	string	工单留言回调地址（非必填）
telWeight	否	string	反馈重量（反馈类型为重量异常时必填）不能超过五个字
modityValue	否	string	商品价值（反馈类型为破损、遗失、少件、错件必填）
desc数据结构：

参数名	是否必填	类型	说明
content	是	string	问题内容
attach	否	List	附件信息（参考附录6.2）
1.3 返回结果
字段	类型	说明	备注
code	int	响应码	200表示提交成功
data	object	返回数据	
message	string	返回信息	
time	int	响应时间	
success	boolean	是否成功	
data数据结构：

字段	类型	说明	备注
id	int	工单id	
status	int	处理状态	
1.4 提供数据内容
请求参数示例

sign=E059DFE9D75FF74ADEE66A2AE4881AA8
key=******
t=1681293665905
	param={
    	    "kuaidinum":"asdsd123123123",
    	    "telWeight":"1",
    	    "callBackUrl":"http://127.0.0.1:9100/apitest/apiOrder/callback",
   	        "secondType":4,
   	        "desc":{
        	    "attach":[
           		{
                	  "type":0,
                	  "uri":"http://xxxxxxxxxxxxxxxxxxxxx"
           		}
                     ]
                  },
              "content":"重量异常"
             }
Copy
返回结果示例

{
    "code": 200,
    "data": {
        "id": 1042,
        "status": 0
    },
    "message": "success",
    "time": 0,
    "success": true
}
Copy
二、查询工单详情
查询创建的工单详情信息

2.1 接口格式
提供统一格式的HTTP POST调用接口,并返回统一格式JSON数据。

2.2 请求地址
https://api.kuaidi100.com/workorder/api/status

请求参数（header）：

名称	类型	默认值
Content-Type	string	application/x-www-form-urlencoded
请求参数（query）：

参数名	是否必填	类型	说明
key	是	string	授权码，请到快递100页面申请企业版接口获取
sign	是	string	32位大写签名，用于验证身份，按MD5 (param +t+key+ secret)的顺序进行MD5加密，不需要加上“+”号。secret在企业管理后台获取
t	是	string	时间戳如：1576123932000
param	是	param	由其他字段拼接
param数据结构:

参数名	是否必填	类型	说明
consultId	是	int	工单id，例如：1
2.3 返回结果
字段	类型	说明	备注
code	int	响应码	200表示提交成功
data	object	返回数据	
message	string	返回信息	
time	int	响应时间	
success	boolean	是否成功	
data数据结构：

字段	类型	说明	备注
id	int	工单id	
telWeight	string	反馈重量	
promise	string	承诺描述	
replyList	List	回复列表	
secondType	int	反馈类型	
opRecords	List	处理结果	
secondDesc	string	反馈类型描述	
statusDesc	string	处理状态描述	
weight	string	重量	
kuaidiNum	string	快递单号	
kuaidiCom	string	快递公司	
createTime	Date	创建时间	
lastModified	Date	修改时间	
status	int	处理状态	
desc	string	描述	
replyList数据结构：

字段	类型	说明	备注
committer	string	处理人员	
content	string	留言内容	
lastModified	Date	修改时间	
attach	List	附件	
opRecords数据结构：

字段	类型	说明	备注
id	int	工单id	
content	string	处理内容	
result	string	处理结果	
2.4 提供数据内容
请求参数示例

t=1681293665905
param={
    	"consultId":1056
}
sign=E059DFE9D75FF74ADEE66A2AE4881AA8
key=******
Copy
返回结果示例

{
   	"code": 200,
    	"data": {
        	"id": 1056,
        	"telWeight": null,
        	"promise": "用户您好，当前您反馈“催取件”问题我们将持续协助您处理，在此期间请您优先联系已分配给您的快递员号码，如超时未能得到解决建议您下单给平台的其他快递品牌寄件。感谢您的理解。",
        	"replyList": [
            		{
                	"committer": "超级管理员勿改",
                	"content": "您好，您的问题已经收到，我们会尽快处理。感谢您对快递100的支持！",
                	"lastModified": "2023-04-12 20:52:00",
                	"attach": null
            		},
            		{
                	"committer": "深圳清峰电子商务有限公司",
                	"content": "催取件",
                	"lastModified": "2023-04-12 20:47:31",
                	"attach": [
                    			{
                       			"uri": "http://xxx.com/downloadfile/cew3Dca3b-xFI2ILewy80_3l8g9oX8q2IBEt9h0kG5XCldLqJYzZcg",
                        		"type": 0
                   			 }
                		  ]
            		}
        		],
        	"secondType": 1,
       	 	"opRecords": null,
        	"secondDesc": "催取件",
        	"statusDesc": "已完结",
        	"weight": "0",
        	"kuaidiNum": "9382000001103st",
        	"kuaidiCom": "shentong",
        	"createTime": "2023-04-12 20:47:31",
        	"lastModified": "2023-04-12 20:52:06",
        	"status": 3,
        	"desc": null
    },
    "message": "success",
    "time": 0,
    "success": true
}
Copy
三、工单留言
3.1 接口格式
提供统一格式的HTTP POST调用接口,并返回统一格式JSON数据。

3.2 请求地址
https://api.kuaidi100.com/workorder/api/reply

请求参数（header）：

名称	类型	默认值
Content-Type	string	application/x-www-form-urlencoded
请求参数（query）：

参数名	是否必填	类型	说明
key	是	string	授权码，请到快递100页面申请企业版接口获取
sign	是	string	32位大写签名，用于验证身份，按MD5 (param +t+key+ secret)的顺序进行MD5加密，不需要加上“+”号。secret在企业管理后台获取
t	是	string	时间戳如：1576123932000
param	是	param	由其他字段拼接
method	是	string	新增留言: addReply，查询留言: queryReply
param数据结构（查询留言）:

参数名	是否必填	类型	说明
consultId	是	int	工单id，例如：1
param数据结构（新增留言）:

参数名	是否必填	类型	说明
consultId	是	int	工单id，例如：1
content	是	string	留言内容
attach	否	List	附件
3.3 返回结果
字段	类型	说明	备注
code	int	响应码	200表示提交成功
data	object	返回数据	
message	string	返回信息	
time	int	响应时间	
success	boolean	是否成功	
data数据结构(查询留言):

字段	类型	说明	备注
committer	string	处理人员	
content	string	留言内容	
lastModified	Date	留言时间	
attach	List	附件	
3.4 提供数据内容
查询留言： 请求参数示例

t=1681293665905
param={
    	"consultId":"1056"
      }
sign=E059DFE9D75FF74ADEE66A2AE4881AA8
key=******
method=queryReply
Copy
返回结果示例

{
   		"code": 200,
    		"data": [
        			{
            			"committer": "测试环境帐号",
            			"content": "testAPI",
            			"lastModified": "2023-04-10 14:10:03",
            			"attach": null
        			},
        			{
            			"committer": "测试环境帐号",
            			"content": "testApi",
            			"lastModified": "2023-04-10 15:03:12",
            			"attach": [
                					{
                    						"uri": "1",
                    						"type": 0
                					}
            					]
        			  }
   			 ],
    		"message": "success",
    		"time": 0,
    		"success": true
}
Copy
新增留言： 请求参数示例

t=1681293665905
param={
    	"consultId":1023,
        "content":"testApi",
        "attach":[
                  {"uri":"xxxxx","type":0}
                 ]
      }
sign=E059DFE9D75FF74ADEE66A2AE4881AA8
key=******
method=addReply
Copy
返回结果示例

{
    "code": 200,
    "data": null,
    "message": "success",
    "time": 0,
    "success": true
}
Copy
四、上传附件
上传提交工单内的图片文件

4.1 接口格式
提供统一格式的HTTP POST调用接口,并返回统一格式JSON数据。

4.2 请求地址
https://api.kuaidi100.com/workorder/api/upload

请求参数（header）：

名称	类型	默认值
Content-Type	string	multipart/form-data
请求参数（query）：

参数名	是否必填	类型	说明
key	是	string	授权码，请到快递100页面申请企业版接口获取
sign	是	string	32位大写签名，用于验证身份，按MD5 (param +t+key+ secret)的顺序进行MD5加密，不需要加上“+”号。secret在企业管理后台获取
t	是	string	时间戳如：1576123932000
param	是	param	由其他字段拼接
file	是	file	图片
param数据结构:

参数名	是否必填	类型	说明
fileName	是	string	图片名称，例如：pic1
4.3 返回结果
字段	类型	说明	备注
code	int	响应码	200表示提交成功
data	object	返回数据	
message	string	返回信息	
time	int	响应时间	
success	boolean	是否成功	
4.4 提供数据内容
请求参数示例

t=1681293665905
param={
    	"fileName":"testImg"
      }
sign=E059DFE9D75FF74ADEE66A2AE4881AA8
key=******
file=file
Copy
返回结果示例

{
   "code": 200,
   "data":
"http://file.kuaidi100.com/downloadfile/0fPfDVqPF_LNPnxoS3dtjpb5CQEGB9nJAuAAnDgi_tNWzZLnMYRHBA",
   "message": "success",
   "time": 0,
   "success": true
}
Copy
五、工单结果回调接口
工单提交时有填写回调地址是会触发回调。当调用回调地址不成功时，会隔半小时重试一次，总共重试三次。 不成功的情况包含但不限于：回调接口返回失败、回调接口三秒超时未响应、响应格式不对。

5.1 接口格式
提供统一格式的HTTP POST调用接口,并返回统一格式JSON数据。

5.2 请求地址
由贵司通过创建工单接口的callbackurl字段提供

请求参数

Header：

名称	类型	默认值
Content-Type	string	application/json
body:

参数名	是否必填	类型	说明
workorderId	是	number	工单id
status	是	int	处理状态
result	否	string	处理结果
5.3 返回结果
字段	类型	说明	备注
code	int	响应码	200表示提交成功
data	object	返回数据	
message	string	返回信息	
time	int	响应时间	
success	boolean	是否成功	
六、工单留言回调接口
工单提交时有填写回调地址是会触发回调。当调用回调地址不成功时，会隔半小时重试一次，总共重试三次。 不成功的情况包含但不限于：回调接口返回失败、回调接口三秒超时未响应、响应格式不对。

6.1 接口格式
提供统一格式的HTTP POST接口,并返回统一格式JSON数据。

6.2 请求地址
由贵司通过创建工单接口的msgCallBackUrl字段提供；

请求参数：

参数名	是否必填	类型	说明
workorderId	是	string	工单id
committer	否	string	处理人员
content	否	string	留言内容
lastModified	否	Date	留言时间
attach	否	List	附件
6.3 返回结果
字段	类型	说明	备注
code	int	响应码	200表示提交成功
data	object	返回数据	
message	string	返回信息	
time	int	响应时间	
success	boolean	是否成功	
七、附录
7.1 反馈类型secondType参数字典
secondType	反馈类型
1	催取件
4	重量异常
5	虚假揽收
7	线下收费
10	破损
11	遗失
12	签收未收到货
13	服务态度差
14	少件
15	错件
16	催物流
17	催派送
18	更改收件信息
19	拦截退回
20	核实退回原因
7.2 attach数据结构
参数名	是否必填	类型	说明
uri	是	string	图片链接
type	否	int	附件类型（0：图片）
7.3 处理状态status参数字典
status	处理状态
0	未受理
1	未回复
2	已回复
3	已完结
-------------------------------------
来源：快递100API开放平台
标题：快递寄件接口-官方快递-快递100API开放平台
链接：https://api.kuaidi100.com/document/5f0ff095bc8da837cbd8aef10