package handler

import (
	"net/http"
	"strconv"

	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/service"
	"github.com/your-org/go-kuaidi/internal/util"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// OrderStatusHistoryHandler 订单状态历史处理器
type OrderStatusHistoryHandler struct {
	statusHistoryService service.OrderStatusHistoryService
	logger               *zap.Logger
}

// NewOrderStatusHistoryHandler 创建订单状态历史处理器
func NewOrderStatusHistoryHandler(
	statusHistoryService service.OrderStatusHistoryService,
	logger *zap.Logger,
) *OrderStatusHistoryHandler {
	return &OrderStatusHistoryHandler{
		statusHistoryService: statusHistoryService,
		logger:               logger,
	}
}

// GetOrderStatusHistory 获取订单状态历史
// @Summary 获取订单状态历史
// @Description 根据订单号或客户订单号获取状态变更历史
// @Tags 订单状态历史
// @Accept json
// @Produce json
// @Param order_no path string false "订单号"
// @Param customer_order_no query string false "客户订单号"
// @Param page query int false "页码" default(1)
// @Param limit query int false "每页数量" default(20)
// @Success 200 {object} response.Response{data=model.GetStatusHistoryResponse}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/express/orders/{order_no}/status-history [get]
func (h *OrderStatusHistoryHandler) GetOrderStatusHistory(c *gin.Context) {
	orderNo := c.Param("order_no")
	customerOrderNo := c.Query("customer_order_no")

	// 验证参数
	if orderNo == "" && customerOrderNo == "" {
		h.logger.Warn("缺少必要参数", zap.String("path", c.Request.URL.Path))
		util.ErrorResponse(c, http.StatusBadRequest, "订单号或客户订单号不能为空", "")
		return
	}

	// 解析分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))

	if page <= 0 {
		page = 1
	}
	if limit <= 0 || limit > 100 {
		limit = 20
	}

	// 构建请求
	req := &model.GetStatusHistoryRequest{
		OrderNo:         orderNo,
		CustomerOrderNo: customerOrderNo,
		Page:            page,
		Limit:           limit,
	}

	// 🔥 增强：从JWT中获取用户ID（如果有的话）
	if uid := h.extractUserID(c); uid != "" {
		req.UserID = uid
	}

	// 查询状态历史
	result, err := h.statusHistoryService.GetOrderStatusHistory(c.Request.Context(), req)
	if err != nil {
		h.logger.Error("查询订单状态历史失败",
			zap.String("order_no", orderNo),
			zap.String("customer_order_no", customerOrderNo),
			zap.Error(err))
		util.ErrorResponse(c, http.StatusInternalServerError, "查询状态历史失败", "")
		return
	}

	h.logger.Info("查询订单状态历史成功",
		zap.String("order_no", orderNo),
		zap.String("customer_order_no", customerOrderNo),
		zap.Int64("total", result.Total))

	util.SuccessResponse(c, "查询成功", result)
}

// GetOrderStatusHistoryByCustomerOrderNo 根据客户订单号获取状态历史
// @Summary 根据客户订单号获取状态历史
// @Description 根据客户订单号获取状态变更历史
// @Tags 订单状态历史
// @Accept json
// @Produce json
// @Param customer_order_no path string true "客户订单号"
// @Param page query int false "页码" default(1)
// @Param limit query int false "每页数量" default(20)
// @Success 200 {object} response.Response{data=model.GetStatusHistoryResponse}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/express/orders/status-history/{customer_order_no} [get]
func (h *OrderStatusHistoryHandler) GetOrderStatusHistoryByCustomerOrderNo(c *gin.Context) {
	customerOrderNo := c.Param("customer_order_no")

	if customerOrderNo == "" {
		h.logger.Warn("客户订单号为空", zap.String("path", c.Request.URL.Path))
		util.ErrorResponse(c, http.StatusBadRequest, "客户订单号不能为空", "")
		return
	}

	// 解析分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))

	if page <= 0 {
		page = 1
	}
	if limit <= 0 || limit > 100 {
		limit = 20
	}

	// 构建请求
	req := &model.GetStatusHistoryRequest{
		CustomerOrderNo: customerOrderNo,
		Page:            page,
		Limit:           limit,
	}

	// 🔥 增强：从JWT中获取用户ID（如果有的话）
	if uid := h.extractUserID(c); uid != "" {
		req.UserID = uid
	}

	// 查询状态历史
	result, err := h.statusHistoryService.GetOrderStatusHistory(c.Request.Context(), req)
	if err != nil {
		h.logger.Error("根据客户订单号查询状态历史失败",
			zap.String("customer_order_no", customerOrderNo),
			zap.Error(err))
		util.ErrorResponse(c, http.StatusInternalServerError, "查询状态历史失败", "")
		return
	}

	h.logger.Info("根据客户订单号查询状态历史成功",
		zap.String("customer_order_no", customerOrderNo),
		zap.Int64("total", result.Total))

	util.SuccessResponse(c, "查询成功", result)
}

// GetUserOrdersStatusHistory 获取用户所有订单的状态历史
// @Summary 获取用户所有订单的状态历史
// @Description 获取当前用户所有订单的状态变更历史
// @Tags 订单状态历史
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param limit query int false "每页数量" default(20)
// @Success 200 {object} response.Response{data=model.GetStatusHistoryResponse}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/express/orders/status-history [get]
func (h *OrderStatusHistoryHandler) GetUserOrdersStatusHistory(c *gin.Context) {
	// 🔥 增强：从JWT中获取用户ID（支持多种键名）
	uid := h.extractUserID(c)
	if uid == "" {
		h.logger.Warn("未找到用户ID",
			zap.String("path", c.Request.URL.Path),
			zap.Any("context_keys", h.getContextKeys(c)))
		util.ErrorResponse(c, http.StatusUnauthorized, "用户未认证", "")
		return
	}

	// 解析分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))

	if page <= 0 {
		page = 1
	}
	if limit <= 0 || limit > 100 {
		limit = 20
	}

	// 查询用户订单状态历史
	result, err := h.statusHistoryService.GetUserOrdersStatusHistory(c.Request.Context(), uid, page, limit)
	if err != nil {
		h.logger.Error("查询用户订单状态历史失败",
			zap.String("user_id", uid),
			zap.Error(err))
		util.ErrorResponse(c, http.StatusInternalServerError, "查询状态历史失败", "")
		return
	}

	h.logger.Info("查询用户订单状态历史成功",
		zap.String("user_id", uid),
		zap.Int64("total", result.Total))

	util.SuccessResponse(c, "查询成功", result)
}

// 🔥 新增：提取用户ID的辅助方法
func (h *OrderStatusHistoryHandler) extractUserID(c *gin.Context) string {
	// 尝试多种键名获取用户ID
	userIDKeys := []string{"user_id", "userID", "client_id"}

	for _, key := range userIDKeys {
		if userID, exists := c.Get(key); exists {
			if uid, ok := userID.(string); ok && uid != "" {
				h.logger.Debug("成功提取用户ID",
					zap.String("key", key),
					zap.String("user_id", uid))
				return uid
			}
		}
	}

	// 如果都没有找到，记录调试信息
	h.logger.Debug("未能提取用户ID",
		zap.Any("context_keys", h.getContextKeys(c)))

	return ""
}

// 🔥 新增：获取上下文中所有键的辅助方法（用于调试）
func (h *OrderStatusHistoryHandler) getContextKeys(c *gin.Context) []string {
	keys := make([]string, 0)

	// 通过反射获取gin.Context中的Keys字段
	if c.Keys != nil {
		for key := range c.Keys {
			keys = append(keys, key)
		}
	}

	return keys
}
