package handler

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/your-org/go-kuaidi/internal/service"
	"go.uber.org/zap"
)

// AdminTimeoutHandler 管理员超时处理接口
type AdminTimeoutHandler struct {
	timeoutService    *service.CancellationTimeoutService
	scheduledService  *service.ScheduledTaskService
	logger            *zap.Logger
}

// NewAdminTimeoutHandler 创建管理员超时处理接口
func NewAdminTimeoutHandler(
	timeoutService *service.CancellationTimeoutService,
	scheduledService *service.ScheduledTaskService,
	logger *zap.Logger,
) *AdminTimeoutHandler {
	return &AdminTimeoutHandler{
		timeoutService:   timeoutService,
		scheduledService: scheduledService,
		logger:           logger,
	}
}

// GetTimeoutStatistics 获取超时统计信息
// GET /api/v1/admin/timeout/statistics
func (h *AdminTimeoutHandler) GetTimeoutStatistics(c *gin.Context) {
	ctx := c.Request.Context()

	stats, err := h.timeoutService.GetTimeoutStatistics(ctx)
	if err != nil {
		h.logger.Error("获取超时统计失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取超时统计失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取超时统计成功",
		"data":    stats,
	})
}

// ProcessTimeoutOrders 手动处理超时订单
// POST /api/v1/admin/timeout/process
func (h *AdminTimeoutHandler) ProcessTimeoutOrders(c *gin.Context) {
	ctx := c.Request.Context()

	h.logger.Info("管理员手动触发超时订单处理")

	err := h.timeoutService.ProcessTimeoutCancellations(ctx)
	if err != nil {
		h.logger.Error("处理超时订单失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "处理超时订单失败",
			"error":   err.Error(),
		})
		return
	}

	// 获取处理后的统计信息
	stats, err := h.timeoutService.GetTimeoutStatistics(ctx)
	if err != nil {
		h.logger.Warn("获取处理后统计信息失败", zap.Error(err))
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "超时订单处理完成",
		"data":    stats,
	})
}

// GetScheduledTaskStatus 获取定时任务状态
// GET /api/v1/admin/timeout/scheduled-task/status
func (h *AdminTimeoutHandler) GetScheduledTaskStatus(c *gin.Context) {
	status := h.scheduledService.GetStatus()

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取定时任务状态成功",
		"data":    status,
	})
}

// StartScheduledTask 启动定时任务
// POST /api/v1/admin/timeout/scheduled-task/start
func (h *AdminTimeoutHandler) StartScheduledTask(c *gin.Context) {
	ctx := c.Request.Context()

	err := h.scheduledService.Start(ctx)
	if err != nil {
		h.logger.Error("启动定时任务失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "启动定时任务失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "定时任务启动成功",
	})
}

// StopScheduledTask 停止定时任务
// POST /api/v1/admin/timeout/scheduled-task/stop
func (h *AdminTimeoutHandler) StopScheduledTask(c *gin.Context) {
	err := h.scheduledService.Stop()
	if err != nil {
		h.logger.Error("停止定时任务失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "停止定时任务失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "定时任务停止成功",
	})
}

// GetProviderTimeoutOrders 获取指定供应商的超时订单
// GET /api/v1/admin/timeout/orders/:provider
func (h *AdminTimeoutHandler) GetProviderTimeoutOrders(c *gin.Context) {
	provider := c.Param("provider")
	if provider == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "供应商参数不能为空",
		})
		return
	}

	// 获取分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	ctx := c.Request.Context()

	// 这里需要实现获取指定供应商超时订单的逻辑
	// 暂时返回统计信息
	stats, err := h.timeoutService.GetTimeoutStatistics(ctx)
	if err != nil {
		h.logger.Error("获取超时统计失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取超时订单失败",
			"error":   err.Error(),
		})
		return
	}

	providerStats, exists := stats.ProviderStats[provider]
	if !exists {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "未找到指定供应商的数据",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取供应商超时订单成功",
		"data": gin.H{
			"provider": provider,
			"stats":    providerStats,
			"page":     page,
			"limit":    limit,
		},
	})
}
