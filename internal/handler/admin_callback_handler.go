package handler

import (
	"encoding/json"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"go.uber.org/zap"

	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/repository"
	"github.com/your-org/go-kuaidi/internal/service/callback"
	"github.com/your-org/go-kuaidi/internal/util"
)

// AdminCallbackHandler 管理员回调处理器
type AdminCallbackHandler struct {
	logger             *zap.Logger
	callbackRepository repository.CallbackRepository
	unifiedForwarder   *callback.UnifiedCallbackForwarder
	callbackService    *callback.UnifiedCallbackService
}

// NewAdminCallbackHandler 创建管理员回调处理器
func NewAdminCallbackHandler(
	logger *zap.Logger,
	callbackRepository repository.CallbackRepository,
	unifiedForwarder *callback.UnifiedCallbackForwarder,
	callbackService *callback.UnifiedCallbackService,
) *AdminCallbackHandler {
	return &AdminCallbackHandler{
		logger:             logger,
		callbackRepository: callbackRepository,
		unifiedForwarder:   unifiedForwarder,
		callbackService:    callbackService,
	}
}

// RetryCallbackRequest 重试回调请求
type RetryCallbackRequest struct {
	CallbackRecordID string `json:"callback_record_id" binding:"required"` // 回调记录ID
	ForceRetry       bool   `json:"force_retry"`                           // 是否强制重试（忽略幂等性检查）
	Reason           string `json:"reason"`                                // 重试原因
}

// RetryCallbackResponse 重试回调响应
type RetryCallbackResponse struct {
	Success          bool   `json:"success"`
	Message          string `json:"message"`
	CallbackRecordID string `json:"callback_record_id"`
	ForwardRecordID  string `json:"forward_record_id,omitempty"`
	RetryReason      string `json:"retry_reason,omitempty"`
}

// RetryCallback 手动重试回调转发
// @Summary 手动重试回调转发
// @Description 管理员手动重试指定的回调记录转发，用于修复遗漏的转发
// @Tags 管理员-回调管理
// @Accept json
// @Produce json
// @Param request body RetryCallbackRequest true "重试请求"
// @Success 200 {object} RetryCallbackResponse "重试成功"
// @Failure 400 {object} model.ErrorResponse "请求参数错误"
// @Failure 404 {object} model.ErrorResponse "回调记录不存在"
// @Failure 500 {object} model.ErrorResponse "服务器内部错误"
// @Router /api/v1/admin/callbacks/retry [post]
func (h *AdminCallbackHandler) RetryCallback(c *gin.Context) {
	var req RetryCallbackRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("绑定重试回调请求参数失败", zap.Error(err))
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Success: false,
			Code:    http.StatusBadRequest,
			Message: "请求参数格式错误: " + err.Error(),
		})
		return
	}

	ctx := c.Request.Context()

	// 1. 验证回调记录ID格式
	callbackID, err := uuid.Parse(req.CallbackRecordID)
	if err != nil {
		h.logger.Error("回调记录ID格式无效",
			zap.String("callback_record_id", req.CallbackRecordID),
			zap.Error(err))
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Success: false,
			Code:    http.StatusBadRequest,
			Message: "回调记录ID格式无效",
		})
		return
	}

	// 2. 查询回调记录
	callbackRecord, err := h.callbackRepository.GetCallbackRecord(ctx, callbackID)
	if err != nil {
		h.logger.Error("查询回调记录失败",
			zap.String("callback_record_id", req.CallbackRecordID),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, model.ErrorResponse{
			Success: false,
			Code:    http.StatusInternalServerError,
			Message: "查询回调记录失败",
		})
		return
	}

	if callbackRecord == nil {
		h.logger.Warn("回调记录不存在",
			zap.String("callback_record_id", req.CallbackRecordID))
		c.JSON(http.StatusNotFound, model.ErrorResponse{
			Success: false,
			Code:    http.StatusNotFound,
			Message: "回调记录不存在",
		})
		return
	}

	// 3. 检查回调记录状态
	if callbackRecord.UserID == "" {
		h.logger.Warn("回调记录没有关联用户，无法转发",
			zap.String("callback_record_id", req.CallbackRecordID))
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Success: false,
			Code:    http.StatusBadRequest,
			Message: "回调记录没有关联用户，无法转发",
		})
		return
	}

	// 4. 解析标准化数据
	var standardizedData model.StandardizedCallbackData
	if err := json.Unmarshal(callbackRecord.StandardizedData, &standardizedData); err != nil {
		h.logger.Error("解析回调标准化数据失败",
			zap.String("callback_record_id", req.CallbackRecordID),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, model.ErrorResponse{
			Success: false,
			Code:    http.StatusInternalServerError,
			Message: "解析回调数据失败",
		})
		return
	}

	h.logger.Info("开始手动重试回调转发",
		zap.String("callback_record_id", req.CallbackRecordID),
		zap.String("user_id", callbackRecord.UserID),
		zap.String("order_no", callbackRecord.OrderNo),
		zap.String("tracking_no", callbackRecord.TrackingNo),
		zap.String("event_type", standardizedData.EventType),
		zap.Bool("force_retry", req.ForceRetry),
		zap.String("reason", req.Reason))

	// 5. 强制重试时清理幂等性缓存
	if req.ForceRetry {
		h.clearForwardIdempotencyCache(&standardizedData, callbackRecord.ID.String())
	}

	// 6. 执行转发
	err = h.unifiedForwarder.Forward(ctx, callbackRecord, &standardizedData)

	// 7. 更新回调记录的外部处理状态
	now := util.NowBeijing()
	if err != nil {
		// 转发失败
		callbackRecord.ExternalStatus = model.CallbackStatusFailed
		callbackRecord.ExternalError = err.Error()
		callbackRecord.ExternalProcessedAt = &now
		callbackRecord.UpdatedAt = now

		h.logger.Error("手动重试回调转发失败",
			zap.String("callback_record_id", req.CallbackRecordID),
			zap.Error(err))

		// 更新数据库记录
		if updateErr := h.callbackRepository.UpdateCallbackRecord(ctx, callbackRecord); updateErr != nil {
			h.logger.Error("更新回调记录失败状态失败", zap.Error(updateErr))
		}

		c.JSON(http.StatusInternalServerError, RetryCallbackResponse{
			Success:          false,
			Message:          "回调转发失败: " + err.Error(),
			CallbackRecordID: req.CallbackRecordID,
			RetryReason:      req.Reason,
		})
		return
	}

	// 转发成功
	callbackRecord.ExternalStatus = model.CallbackStatusSuccess
	callbackRecord.ExternalProcessedAt = &now
	callbackRecord.UpdatedAt = now

	h.logger.Info("手动重试回调转发成功",
		zap.String("callback_record_id", req.CallbackRecordID),
		zap.String("user_id", callbackRecord.UserID),
		zap.String("order_no", callbackRecord.OrderNo))

	// 更新数据库记录
	if updateErr := h.callbackRepository.UpdateCallbackRecord(ctx, callbackRecord); updateErr != nil {
		h.logger.Error("更新回调记录成功状态失败", zap.Error(updateErr))
	}

	// 8. 查询转发记录ID（用于响应）
	forwardRecords, _ := h.callbackRepository.GetForwardRecordsByCallbackID(ctx, callbackRecord.ID)
	var forwardRecordID string
	if len(forwardRecords) > 0 {
		// 获取最新的转发记录
		latestRecord := forwardRecords[0]
		for _, record := range forwardRecords {
			if record.CreatedAt.After(latestRecord.CreatedAt) {
				latestRecord = record
			}
		}
		forwardRecordID = latestRecord.ID.String()
	}

	c.JSON(http.StatusOK, RetryCallbackResponse{
		Success:          true,
		Message:          "回调转发重试成功",
		CallbackRecordID: req.CallbackRecordID,
		ForwardRecordID:  forwardRecordID,
		RetryReason:      req.Reason,
	})
}

// clearForwardIdempotencyCache 清理转发幂等性缓存（强制重试时使用）
func (h *AdminCallbackHandler) clearForwardIdempotencyCache(data *model.StandardizedCallbackData, recordID string) {
	h.logger.Info("清理转发幂等性缓存（强制重试）",
		zap.String("order_no", data.OrderNo),
		zap.String("customer_order_no", data.CustomerOrderNo),
		zap.String("event_type", data.EventType),
		zap.String("record_id", recordID))

	// 调用 UnifiedCallbackService 的公共方法清理缓存
	h.callbackService.ClearForwardIdempotencyCache(data, recordID)
}
