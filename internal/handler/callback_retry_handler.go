package handler

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"go.uber.org/zap"

	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/repository"
	"github.com/your-org/go-kuaidi/internal/service/callback"
	"github.com/your-org/go-kuaidi/internal/util"
)

// CallbackRetryHandler 回调重试管理处理器
type CallbackRetryHandler struct {
	callbackRepository repository.CallbackRepository
	retryScheduler     *callback.CallbackRetryScheduler
	logger             *zap.Logger
}

// NewCallbackRetryHandler 创建回调重试管理处理器
func NewCallbackRetryHandler(
	callbackRepository repository.CallbackRepository,
	retryScheduler *callback.CallbackRetryScheduler,
	logger *zap.Logger,
) *CallbackRetryHandler {
	return &CallbackRetryHandler{
		callbackRepository: callbackRepository,
		retryScheduler:     retryScheduler,
		logger:             logger,
	}
}

// GetRetryRecords 获取重试记录列表
// @Summary 获取重试记录列表
// @Description 获取用户的回调重试记录列表，支持分页
// @Tags 回调重试
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Success 200 {object} model.APIResponse{data=model.RetryRecordListResponse}
// @Failure 400 {object} model.APIResponse
// @Failure 500 {object} model.APIResponse
// @Router /api/v1/callback/retry/records [get]
func (h *CallbackRetryHandler) GetRetryRecords(c *gin.Context) {
	userID := c.GetString("user_id")
	if userID == "" {
		util.ErrorResponse(c, http.StatusUnauthorized, "用户未认证", "")
		return
	}

	// 解析分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	offset := (page - 1) * pageSize

	// 获取重试记录
	records, err := h.callbackRepository.GetRetryRecordsByUserID(c.Request.Context(), userID, pageSize, offset)
	if err != nil {
		h.logger.Error("获取重试记录失败", zap.Error(err), zap.String("user_id", userID))
		util.ErrorResponse(c, http.StatusInternalServerError, "获取重试记录失败", "")
		return
	}

	// 获取总数
	total, err := h.callbackRepository.GetRetryRecordsCount(c.Request.Context(), userID)
	if err != nil {
		h.logger.Error("获取重试记录总数失败", zap.Error(err), zap.String("user_id", userID))
		util.ErrorResponse(c, http.StatusInternalServerError, "获取重试记录总数失败", "")
		return
	}

	// 构建响应
	response := &model.RetryRecordListResponse{
		Records:    records,
		Total:      total,
		Page:       page,
		PageSize:   pageSize,
		TotalPages: (total + int64(pageSize) - 1) / int64(pageSize),
	}

	util.SuccessResponse(c, "获取重试记录成功", response)
}

// GetRetryRecord 获取重试记录详情
// @Summary 获取重试记录详情
// @Description 根据ID获取重试记录的详细信息
// @Tags 回调重试
// @Accept json
// @Produce json
// @Param id path string true "重试记录ID"
// @Success 200 {object} model.APIResponse{data=model.CallbackRetryRecord}
// @Failure 400 {object} model.APIResponse
// @Failure 404 {object} model.APIResponse
// @Failure 500 {object} model.APIResponse
// @Router /api/v1/callback/retry/records/{id} [get]
func (h *CallbackRetryHandler) GetRetryRecord(c *gin.Context) {
	userID := c.GetString("user_id")
	if userID == "" {
		util.ErrorResponse(c, http.StatusUnauthorized, "用户未认证", "")
		return
	}

	// 解析ID
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		util.ErrorResponse(c, http.StatusBadRequest, "无效的重试记录ID", "")
		return
	}

	// 获取重试记录
	record, err := h.callbackRepository.GetRetryRecord(c.Request.Context(), id)
	if err != nil {
		h.logger.Error("获取重试记录失败", zap.Error(err), zap.String("id", idStr))
		util.ErrorResponse(c, http.StatusNotFound, "重试记录不存在", "")
		return
	}

	// 验证用户权限
	if record.UserID != userID {
		util.ErrorResponse(c, http.StatusForbidden, "无权访问此重试记录", "")
		return
	}

	util.SuccessResponse(c, "获取重试记录成功", record)
}

// ManualRetry 手动重试
// @Summary 手动重试回调
// @Description 手动触发指定转发记录的重试
// @Tags 回调重试
// @Accept json
// @Produce json
// @Param id path string true "转发记录ID"
// @Success 200 {object} model.APIResponse
// @Failure 400 {object} model.APIResponse
// @Failure 404 {object} model.APIResponse
// @Failure 500 {object} model.APIResponse
// @Router /api/v1/callback/retry/manual/{id} [post]
func (h *CallbackRetryHandler) ManualRetry(c *gin.Context) {
	userID := c.GetString("user_id")
	if userID == "" {
		util.ErrorResponse(c, http.StatusUnauthorized, "用户未认证", "")
		return
	}

	// 解析ID
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		util.ErrorResponse(c, http.StatusBadRequest, "无效的转发记录ID", "")
		return
	}

	// 获取转发记录
	forwardRecord, err := h.callbackRepository.GetForwardRecord(c.Request.Context(), id)
	if err != nil {
		h.logger.Error("获取转发记录失败", zap.Error(err), zap.String("id", idStr))
		util.ErrorResponse(c, http.StatusNotFound, "转发记录不存在", "")
		return
	}

	// 验证用户权限
	if forwardRecord.UserID != userID {
		util.ErrorResponse(c, http.StatusForbidden, "无权操作此转发记录", "")
		return
	}

	// 检查是否可以重试
	if forwardRecord.Status == model.CallbackStatusSuccess {
		util.ErrorResponse(c, http.StatusBadRequest, "该回调已成功，无需重试", "")
		return
	}

	// 获取回调记录
	callbackRecord, err := h.callbackRepository.GetCallbackRecord(c.Request.Context(), forwardRecord.CallbackRecordID)
	if err != nil {
		h.logger.Error("获取回调记录失败", zap.Error(err))
		util.ErrorResponse(c, http.StatusInternalServerError, "获取回调记录失败", "")
		return
	}

	// 调度重试
	if err := h.retryScheduler.ScheduleRetry(c.Request.Context(), forwardRecord, callbackRecord); err != nil {
		h.logger.Error("调度手动重试失败", zap.Error(err), zap.String("forward_record_id", idStr))
		util.ErrorResponse(c, http.StatusInternalServerError, "调度重试失败", "")
		return
	}

	h.logger.Info("手动重试已调度",
		zap.String("forward_record_id", idStr),
		zap.String("user_id", userID))

	util.SuccessResponse(c, "重试已调度，请稍后查看结果", nil)
}

// GetRetryStatus 获取重试状态
// @Summary 获取重试状态
// @Description 获取重试调度器的运行状态和统计信息
// @Tags 回调重试
// @Accept json
// @Produce json
// @Success 200 {object} model.APIResponse{data=model.RetryStatusResponse}
// @Failure 500 {object} model.APIResponse
// @Router /api/v1/callback/retry/status [get]
func (h *CallbackRetryHandler) GetRetryStatus(c *gin.Context) {
	// 获取重试指标
	metrics := h.retryScheduler.GetMetrics()
	isRunning := h.retryScheduler.IsRunning()

	response := &model.RetryStatusResponse{
		IsRunning: isRunning,
		Metrics:   metrics,
	}

	util.SuccessResponse(c, "获取重试状态成功", response)
}

// CancelRetry 取消重试
// @Summary 取消重试
// @Description 取消指定转发记录的所有待重试任务
// @Tags 回调重试
// @Accept json
// @Produce json
// @Param id path string true "转发记录ID"
// @Success 200 {object} model.APIResponse
// @Failure 400 {object} model.APIResponse
// @Failure 404 {object} model.APIResponse
// @Failure 500 {object} model.APIResponse
// @Router /api/v1/callback/retry/cancel/{id} [post]
func (h *CallbackRetryHandler) CancelRetry(c *gin.Context) {
	userID := c.GetString("user_id")
	if userID == "" {
		util.ErrorResponse(c, http.StatusUnauthorized, "用户未认证", "")
		return
	}

	// 解析ID
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		util.ErrorResponse(c, http.StatusBadRequest, "无效的转发记录ID", "")
		return
	}

	// 获取转发记录
	forwardRecord, err := h.callbackRepository.GetForwardRecord(c.Request.Context(), id)
	if err != nil {
		h.logger.Error("获取转发记录失败", zap.Error(err), zap.String("id", idStr))
		util.ErrorResponse(c, http.StatusNotFound, "转发记录不存在", "")
		return
	}

	// 验证用户权限
	if forwardRecord.UserID != userID {
		util.ErrorResponse(c, http.StatusForbidden, "无权操作此转发记录", "")
		return
	}

	// 取消待重试记录
	if err := h.callbackRepository.CancelPendingRetryRecords(c.Request.Context(), id); err != nil {
		h.logger.Error("取消重试失败", zap.Error(err), zap.String("forward_record_id", idStr))
		util.ErrorResponse(c, http.StatusInternalServerError, "取消重试失败", "")
		return
	}

	h.logger.Info("重试已取消",
		zap.String("forward_record_id", idStr),
		zap.String("user_id", userID))

	util.SuccessResponse(c, "重试已取消", nil)
}
