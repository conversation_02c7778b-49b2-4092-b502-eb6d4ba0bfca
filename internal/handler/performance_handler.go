package handler

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/your-org/go-kuaidi/internal/service"
	"go.uber.org/zap"
)

// PerformanceHandler 性能监控处理器
type PerformanceHandler struct {
	priceService *service.EnhancedPriceService
	logger       *zap.Logger
}

// NewPerformanceHandler 创建性能监控处理器
func NewPerformanceHandler(priceService *service.EnhancedPriceService, logger *zap.Logger) *PerformanceHandler {
	return &PerformanceHandler{
		priceService: priceService,
		logger:       logger,
	}
}

// GetPriceQueryMetrics 获取价格查询性能指标
// @Summary 获取价格查询性能指标
// @Description 获取价格查询的详细性能指标，包括响应时间、缓存命中率等
// @Tags 性能监控
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "性能指标数据"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Router /api/v1/performance/price-metrics [get]
func (h *PerformanceHandler) GetPriceQueryMetrics(c *gin.Context) {
	// 获取性能指标
	metrics := h.priceService.GetPerformanceMetrics()
	if metrics == nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "无法获取性能指标",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取性能指标成功",
		"data":    metrics,
	})
}

// GetPerformanceReport 获取性能报告
// @Summary 获取详细性能报告
// @Description 获取包含性能分析和建议的详细报告
// @Tags 性能监控
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "性能报告"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Router /api/v1/performance/report [get]
func (h *PerformanceHandler) GetPerformanceReport(c *gin.Context) {
	// 获取性能报告
	report := h.priceService.GetPerformanceReport()
	if report == nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "无法生成性能报告",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取性能报告成功",
		"data":    report,
	})
}

// ResetPerformanceMetrics 重置性能指标
// @Summary 重置性能指标
// @Description 清空所有性能统计数据，重新开始统计
// @Tags 性能监控
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "重置成功"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Router /api/v1/performance/reset [post]
func (h *PerformanceHandler) ResetPerformanceMetrics(c *gin.Context) {
	// 重置性能指标
	err := h.priceService.ResetPerformanceMetrics()
	if err != nil {
		h.logger.Error("重置性能指标失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "重置性能指标失败",
			"error":   err.Error(),
		})
		return
	}

	h.logger.Info("性能指标已重置")
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "性能指标重置成功",
	})
}

// GetSystemHealth 获取系统健康状态
// @Summary 获取系统健康状态
// @Description 获取系统整体健康状态，包括性能指标是否正常
// @Tags 性能监控
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "系统健康状态"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Router /api/v1/performance/health [get]
func (h *PerformanceHandler) GetSystemHealth(c *gin.Context) {
	// 获取性能指标
	metrics := h.priceService.GetPerformanceMetrics()
	if metrics == nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "无法获取系统健康状态",
		})
		return
	}

	// 判断系统健康状态
	isHealthy := true
	issues := []string{}

	// 检查平均响应时间（目标：200ms以内）
	if metrics.AverageResponseTime.Milliseconds() > 200 {
		isHealthy = false
		issues = append(issues, "平均响应时间超过200ms")
	}

	// 检查缓存命中率（目标：95%以上）
	if metrics.CacheHitRate < 0.95 && metrics.TotalQueries > 10 {
		isHealthy = false
		issues = append(issues, "缓存命中率低于95%")
	}

	// 检查并发查询数量（警告：超过100个并发）
	if metrics.ConcurrentQueries > 100 {
		issues = append(issues, "并发查询数量较高")
	}

	status := "healthy"
	if !isHealthy {
		status = "unhealthy"
	} else if len(issues) > 0 {
		status = "warning"
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取系统健康状态成功",
		"data": gin.H{
			"status":              status,
			"is_healthy":          isHealthy,
			"issues":              issues,
			"total_queries":       metrics.TotalQueries,
			"success_rate":        float64(metrics.SuccessfulQueries) / float64(metrics.TotalQueries) * 100,
			"avg_response_ms":     metrics.AverageResponseTime.Milliseconds(),
			"cache_hit_rate":      metrics.CacheHitRate * 100,
			"concurrent_queries":  metrics.ConcurrentQueries,
			"last_updated":        metrics.LastUpdated,
		},
	})
}

// GetPerformanceOptimizationSuggestions 获取性能优化建议
// @Summary 获取性能优化建议
// @Description 基于当前性能指标提供优化建议
// @Tags 性能监控
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "优化建议"
// @Router /api/v1/performance/suggestions [get]
func (h *PerformanceHandler) GetPerformanceOptimizationSuggestions(c *gin.Context) {
	// 获取性能指标
	metrics := h.priceService.GetPerformanceMetrics()
	if metrics == nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "无法获取性能指标",
		})
		return
	}

	suggestions := []map[string]interface{}{}

	// 响应时间优化建议
	if metrics.AverageResponseTime.Milliseconds() > 200 {
		suggestions = append(suggestions, map[string]interface{}{
			"category":    "响应时间",
			"priority":    "高",
			"issue":       "平均响应时间超过200ms",
			"current":     metrics.AverageResponseTime.Milliseconds(),
			"target":      200,
			"suggestions": []string{
				"检查数据库查询是否有优化空间",
				"增加缓存使用",
				"优化供应商映射查询",
				"考虑使用连接池",
			},
		})
	}

	// 缓存命中率优化建议
	if metrics.CacheHitRate < 0.95 && metrics.TotalQueries > 10 {
		suggestions = append(suggestions, map[string]interface{}{
			"category":    "缓存效率",
			"priority":    "中",
			"issue":       "缓存命中率低于95%",
			"current":     metrics.CacheHitRate * 100,
			"target":      95,
			"suggestions": []string{
				"检查缓存配置是否合理",
				"增加缓存过期时间",
				"预热常用查询",
				"优化缓存键设计",
			},
		})
	}

	// 并发处理建议
	if metrics.ConcurrentQueries > 100 {
		suggestions = append(suggestions, map[string]interface{}{
			"category":    "并发处理",
			"priority":    "中",
			"issue":       "并发查询数量较高",
			"current":     metrics.ConcurrentQueries,
			"target":      100,
			"suggestions": []string{
				"考虑增加工作池大小",
				"实施请求限流",
				"优化查询逻辑减少处理时间",
				"监控系统资源使用情况",
			},
		})
	}

	// 如果性能良好，提供维护建议
	if len(suggestions) == 0 {
		suggestions = append(suggestions, map[string]interface{}{
			"category":    "维护",
			"priority":    "低",
			"issue":       "系统性能良好",
			"suggestions": []string{
				"继续监控性能指标",
				"定期清理缓存",
				"保持数据库索引优化",
				"定期更新性能基准",
			},
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取优化建议成功",
		"data": gin.H{
			"suggestions":     suggestions,
			"total_queries":   metrics.TotalQueries,
			"avg_response_ms": metrics.AverageResponseTime.Milliseconds(),
			"cache_hit_rate":  metrics.CacheHitRate * 100,
			"generated_at":    metrics.LastUpdated,
		},
	})
}
