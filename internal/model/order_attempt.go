package model

import (
	"time"
)

// OrderAttempt 下单尝试记录
type OrderAttempt struct {
	ID                string    `json:"id" gorm:"primaryKey;type:varchar(36)"`
	CustomerOrderNo   string    `json:"customer_order_no" gorm:"type:varchar(100);index"`
	UserID            string    `json:"user_id" gorm:"type:varchar(36);index"`
	Provider          string    `json:"provider" gorm:"type:varchar(50);index"`
	ExpressType       string    `json:"express_type" gorm:"type:varchar(50)"`
	AttemptStage      string    `json:"attempt_stage" gorm:"type:varchar(50);index"` // 尝试阶段
	StageDescription  string    `json:"stage_description" gorm:"type:text"`          // 阶段描述
	Success           bool      `json:"success" gorm:"index"`                        // 是否成功
	ErrorMessage      string    `json:"error_message" gorm:"type:text"`              // 错误信息
	ErrorCode         string    `json:"error_code" gorm:"type:varchar(100)"`         // 错误代码
	RequestData       string    `json:"request_data" gorm:"type:text"`               // 请求数据
	ResponseData      string    `json:"response_data" gorm:"type:text"`              // 响应数据
	ProcessingTimeMs  int64     `json:"processing_time_ms"`                          // 处理时间(毫秒)
	RetryCount        int       `json:"retry_count" gorm:"default:0"`                // 重试次数
	Metadata          string    `json:"metadata" gorm:"type:text"`                   // 元数据(JSON)
	CreatedAt         time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt         time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}

// TableName 指定表名
func (OrderAttempt) TableName() string {
	return "order_attempts"
}

// OrderAttemptStage 下单尝试阶段常量
const (
	// 订单创建阶段
	StageOrderCreateStart        = "ORDER_CREATE_START"        // 开始创建订单
	StageOrderValidation         = "ORDER_VALIDATION"          // 订单参数验证
	StageOrderValidationFailed   = "ORDER_VALIDATION_FAILED"   // 订单参数验证失败
	StagePriceValidation         = "PRICE_VALIDATION"          // 价格验证
	StagePriceValidationFailed   = "PRICE_VALIDATION_FAILED"   // 价格验证失败
	StageProviderSelection       = "PROVIDER_SELECTION"        // 供应商选择
	StageProviderSelectFailed    = "PROVIDER_SELECT_FAILED"    // 供应商选择失败
	StageProviderSelected        = "PROVIDER_SELECTED"         // 供应商已选择
	StageProviderAdapterNotFound = "PROVIDER_ADAPTER_NOT_FOUND" // 供应商适配器未找到

	// 供应商API调用阶段
	StageProviderAPICallStart   = "PROVIDER_API_CALL_START"   // 开始调用供应商API
	StageProviderAPICallSuccess = "PROVIDER_API_CALL_SUCCESS" // 供应商API调用成功
	StageProviderAPICallFailed  = "PROVIDER_API_CALL_FAILED"  // 供应商API调用失败
	StageProviderAPITimeout     = "PROVIDER_API_TIMEOUT"      // 供应商API超时
	StageProviderAPIRetry       = "PROVIDER_API_RETRY"        // 供应商API重试

	// 数据库操作阶段
	StageDBSaveStart   = "DB_SAVE_START"   // 开始保存到数据库
	StageDBSaveSuccess = "DB_SAVE_SUCCESS" // 数据库保存成功
	StageDBSaveFailed  = "DB_SAVE_FAILED"  // 数据库保存失败

	// 余额操作阶段
	StageBalancePreCharge        = "BALANCE_PRE_CHARGE"         // 余额预扣费
	StageBalancePreChargeFailed  = "BALANCE_PRE_CHARGE_FAILED"  // 余额预扣费失败
	StageBalancePreChargeSuccess = "BALANCE_PRE_CHARGE_SUCCESS" // 余额预扣费成功
	StageBalanceConfirm          = "BALANCE_CONFIRM"            // 余额确认扣费
	StageBalanceConfirmFailed    = "BALANCE_CONFIRM_FAILED"     // 余额确认扣费失败
	StageBalanceRefund           = "BALANCE_REFUND"             // 余额退款
	StageBalanceRefundFailed     = "BALANCE_REFUND_FAILED"      // 余额退款失败

	// 订单完成阶段
	StageOrderCreateSuccess = "ORDER_CREATE_SUCCESS" // 订单创建成功
	StageOrderCreateFailed  = "ORDER_CREATE_FAILED"  // 订单创建失败
)

// OrderAttemptRequest 创建下单尝试记录请求
type OrderAttemptRequest struct {
	CustomerOrderNo  string                 `json:"customer_order_no"`
	UserID           string                 `json:"user_id"`
	Provider         string                 `json:"provider"`
	ExpressType      string                 `json:"express_type"`
	AttemptStage     string                 `json:"attempt_stage"`
	StageDescription string                 `json:"stage_description"`
	Success          bool                   `json:"success"`
	ErrorMessage     string                 `json:"error_message"`
	ErrorCode        string                 `json:"error_code"`
	RequestData      interface{}            `json:"request_data"`
	ResponseData     interface{}            `json:"response_data"`
	ProcessingTimeMs int64                  `json:"processing_time_ms"`
	RetryCount       int                    `json:"retry_count"`
	Metadata         map[string]interface{} `json:"metadata"`
}

// OrderAttemptSummary 下单尝试汇总
type OrderAttemptSummary struct {
	CustomerOrderNo   string    `json:"customer_order_no"`
	UserID            string    `json:"user_id"`
	TotalAttempts     int       `json:"total_attempts"`
	SuccessfulStages  int       `json:"successful_stages"`
	FailedStages      int       `json:"failed_stages"`
	LastAttemptStage  string    `json:"last_attempt_stage"`
	LastAttemptTime   time.Time `json:"last_attempt_time"`
	FinalSuccess      bool      `json:"final_success"`
	TotalProcessingMs int64     `json:"total_processing_ms"`
	ProvidersAttempted []string `json:"providers_attempted"`
	ErrorMessages     []string  `json:"error_messages"`
}

// OrderAttemptFilter 下单尝试查询过滤器
type OrderAttemptFilter struct {
	CustomerOrderNo string    `json:"customer_order_no"`
	UserID          string    `json:"user_id"`
	Provider        string    `json:"provider"`
	ExpressType     string    `json:"express_type"`
	AttemptStage    string    `json:"attempt_stage"`
	Success         *bool     `json:"success"`
	StartTime       time.Time `json:"start_time"`
	EndTime         time.Time `json:"end_time"`
	Limit           int       `json:"limit"`
	Offset          int       `json:"offset"`
}
