package model

import (
	"time"

	"github.com/your-org/go-kuaidi/internal/util"
)

// OrderRecordResponse 订单记录API响应结构
// 用于API返回，自动处理时区转换
type OrderRecordResponse struct {
	ID              int64  `json:"id"`
	PlatformOrderNo string `json:"platform_order_no"` // 🔥 新增：平台生成的全局唯一订单号
	CustomerOrderNo string `json:"customer_order_no"` // 客户订单号
	OrderNo         string `json:"order_no"`          // 🔥 兼容性字段：主要订单号（优先显示平台订单号）
	ProviderOrderNo string `json:"provider_order_no"` // 🔥 新增：供应商返回的订单号
	TrackingNo      string `json:"tracking_no"`       // 运单号
	ExpressType     string `json:"express_type"`      // 快递类型
	ExpressName     string `json:"express_name"`      // 快递公司名称
	ProductType     string `json:"product_type"`      // 产品类型
	Provider        string `json:"provider"`          // 供应商
	ProviderName    string `json:"provider_name"`     // 供应商名称
	Status          string `json:"status"`            // 订单状态
	StatusDesc      string `json:"status_desc"`       // 状态描述

	// 兼容性字段（保留原有字段）
	Weight float64 `json:"weight"` // 重量（兼容性，建议使用OrderWeight）
	Price  float64 `json:"price"`  // 价格（主要价格字段）

	// 费用相关字段
	ActualFee              float64 `json:"actual_fee"`               // 实收费用
	InsuranceFee           float64 `json:"insurance_fee"`            // 保价费
	OverweightFee          float64 `json:"overweight_fee"`           // 超重费用
	UnderweightFee         float64 `json:"underweight_fee"`          // 超轻费用
	WeightAdjustmentReason string  `json:"weight_adjustment_reason"` // 重量调整原因
	BillingStatus          string  `json:"billing_status"`           // 计费状态: pending, confirmed (settled已废弃)

	// 重量体积相关字段
	OrderVolume   float64 `json:"order_volume"`   // 下单体积(m³)
	ActualWeight  float64 `json:"actual_weight"`  // 实际重量(kg)
	ActualVolume  float64 `json:"actual_volume"`  // 实际体积(m³)
	ChargedWeight float64 `json:"charged_weight"` // 计费重量(kg)

	// 其他字段
	SenderInfo   string `json:"sender_info"`   // 寄件人信息
	ReceiverInfo string `json:"receiver_info"` // 收件人信息
	TaskId       string `json:"task_id"`       // 任务ID
	PollToken    string `json:"poll_token"`    // 轮询令牌

	// 揽件员信息字段
	CourierName  string `json:"courier_name"`  // 快递员姓名
	CourierPhone string `json:"courier_phone"` // 快递员电话
	CourierCode  string `json:"courier_code"`  // 快递员工号
	StationName  string `json:"station_name"`  // 网点名称
	PickupCode   string `json:"pickup_code"`   // 取件码

	// 🔥 新增：失败订单相关字段
	FailureReason  string `json:"failure_reason,omitempty"`  // 失败原因类型
	FailureMessage string `json:"failure_message,omitempty"` // 失败详细信息
	FailureStage   string `json:"failure_stage,omitempty"`   // 失败阶段
	FailureTime    string `json:"failure_time,omitempty"`    // 失败时间
	CanRetry       bool   `json:"can_retry"`                 // 是否可重试

	// 时间字段 - 自动转换为北京时间
	CreatedAt time.Time `json:"created_at"` // 创建时间
	UpdatedAt time.Time `json:"updated_at"` // 更新时间
}

// FromOrderRecord 从OrderRecord转换为OrderRecordResponse
// 自动处理时区转换
func (r *OrderRecordResponse) FromOrderRecord(order *OrderRecord) {
	*r = OrderRecordResponse{
		ID:              order.ID,
		PlatformOrderNo: order.PlatformOrderNo,
		CustomerOrderNo: order.CustomerOrderNo,
		OrderNo:         order.GetPrimaryOrderNo(), // 🔥 兼容性：优先显示平台订单号
		ProviderOrderNo: order.OrderNo,             // 🔥 新增：供应商订单号
		TrackingNo:      order.TrackingNo,
		ExpressType:     order.ExpressType,
		ProductType:     order.ProductType,
		Provider:        order.Provider,
		Status:          order.Status,
		Weight:          order.Weight,
		Price:           order.Price,
		ActualFee:       order.ActualFee,
		InsuranceFee:    order.InsuranceFee,
		BillingStatus:   order.BillingStatus,
		OrderVolume:     order.OrderVolume,
		ActualWeight:    order.ActualWeight,
		ActualVolume:    order.ActualVolume,
		ChargedWeight:   order.ChargedWeight,
		SenderInfo:      order.SenderInfo,
		ReceiverInfo:    order.ReceiverInfo,
		TaskId:          order.TaskId,
		PollToken:       order.PollToken,
		CourierName:     order.CourierName,
		CourierPhone:    order.CourierPhone,
		CourierCode:     order.CourierCode,
		StationName:     order.StationName,
		PickupCode:      order.PickupCode,
		// 🔥 新增：失败订单相关字段
		FailureReason:  order.FailureReason,
		FailureMessage: order.FailureMessage,
		FailureStage:   order.FailureStage,
		FailureTime:    order.FailureTime,
		CanRetry:       order.CanRetry,
		// 时间字段自动转换为北京时间
		CreatedAt: util.ToBeijing(order.CreatedAt),
		UpdatedAt: util.ToBeijing(order.UpdatedAt),
	}
}

// ToOrderRecordResponse 将OrderRecord转换为OrderRecordResponse
func ToOrderRecordResponse(order *OrderRecord) *OrderRecordResponse {
	resp := &OrderRecordResponse{}
	resp.FromOrderRecord(order)
	return resp
}
