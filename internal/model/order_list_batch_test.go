package model

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestValidateOrderListRequest_BatchQuery 测试批量查询验证逻辑
func TestValidateOrderListRequest_BatchQuery(t *testing.T) {
	t.Run("批量查询基本验证", func(t *testing.T) {
		req := &OrderListRequest{
			TrackingNos: []string{"SF1234567890", "YT9876543210", "ZTO5555666677"},
		}

		err := ValidateOrderListRequest(req)
		assert.NoError(t, err)
		assert.True(t, req.BatchMode, "应该自动启用批量模式")
		assert.Equal(t, 50, req.MaxBatchSize, "应该设置默认最大批量大小")
		assert.Empty(t, req.TrackingNo, "应该清空单个运单号字段")
	})

	t.Run("批量查询数量限制", func(t *testing.T) {
		// 创建超过限制的运单号列表
		trackingNos := make([]string, 51)
		for i := 0; i < 51; i++ {
			trackingNos[i] = "SF123456789" + string(rune('0'+i%10))
		}

		req := &OrderListRequest{
			TrackingNos: trackingNos,
		}

		err := ValidateOrderListRequest(req)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "批量查询运单号数量不能超过50个")
	})

	t.Run("运单号格式验证", func(t *testing.T) {
		req := &OrderListRequest{
			TrackingNos: []string{
				"SF1234567890",    // 有效
				"SHORT",           // 太短，无效
				"VERYLONGTRACKINGNO123456789012345", // 太长，无效
				"YT9876543210",    // 有效
			},
		}

		err := ValidateOrderListRequest(req)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "运单号格式不正确")
	})

	t.Run("运单号去重处理", func(t *testing.T) {
		req := &OrderListRequest{
			TrackingNos: []string{
				"SF1234567890",
				"YT9876543210",
				"SF1234567890", // 重复
				"  ZTO5555666677  ", // 带空白字符
				"YT9876543210", // 重复
				"", // 空字符串
			},
		}

		err := ValidateOrderListRequest(req)
		assert.NoError(t, err)
		assert.Equal(t, 3, len(req.TrackingNos), "应该去重并清理空白字符")
		assert.Contains(t, req.TrackingNos, "SF1234567890")
		assert.Contains(t, req.TrackingNos, "YT9876543210")
		assert.Contains(t, req.TrackingNos, "ZTO5555666677")
	})

	t.Run("空运单号列表验证", func(t *testing.T) {
		req := &OrderListRequest{
			TrackingNos: []string{"", "   ", "\t", "\n"},
		}

		err := ValidateOrderListRequest(req)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "没有有效的运单号")
	})

	t.Run("批量查询与单个查询冲突处理", func(t *testing.T) {
		req := &OrderListRequest{
			TrackingNo:  "SF1111111111", // 单个运单号
			TrackingNos: []string{"SF1234567890", "YT9876543210"}, // 批量运单号
		}

		err := ValidateOrderListRequest(req)
		assert.NoError(t, err)
		assert.True(t, req.BatchMode)
		assert.Empty(t, req.TrackingNo, "应该清空单个运单号字段，避免冲突")
	})

	t.Run("非批量查询模式验证", func(t *testing.T) {
		req := &OrderListRequest{
			TrackingNo: "SF1234567890",
			Page:       1,
			PageSize:   20,
		}

		err := ValidateOrderListRequest(req)
		assert.NoError(t, err)
		assert.False(t, req.BatchMode, "不应该启用批量模式")
		assert.Equal(t, "SF1234567890", req.TrackingNo)
	})
}

// TestBatchQueryStats 测试批量查询统计信息结构
func TestBatchQueryStats(t *testing.T) {
	t.Run("批量查询统计信息创建", func(t *testing.T) {
		stats := &BatchQueryStats{
			TotalQueried: 5,
			FoundCount:   3,
			NotFound:     []string{"SF1111111111", "YT2222222222"},
			QueryTime:    "125.50ms",
		}

		assert.Equal(t, 5, stats.TotalQueried)
		assert.Equal(t, 3, stats.FoundCount)
		assert.Equal(t, 2, len(stats.NotFound))
		assert.Equal(t, "125.50ms", stats.QueryTime)
	})

	t.Run("空的未找到列表", func(t *testing.T) {
		stats := &BatchQueryStats{
			TotalQueried: 3,
			FoundCount:   3,
			NotFound:     []string{},
			QueryTime:    "85.20ms",
		}

		assert.Equal(t, 0, len(stats.NotFound))
		assert.Equal(t, stats.TotalQueried, stats.FoundCount)
	})
}

// TestOrderListData_BatchStats 测试订单列表数据中的批量统计信息
func TestOrderListData_BatchStats(t *testing.T) {
	t.Run("包含批量统计信息的订单列表数据", func(t *testing.T) {
		data := &OrderListData{
			Items:      []*OrderListItem{},
			Total:      3,
			Page:       1,
			PageSize:   20,
			TotalPages: 1,
			HasNext:    false,
			HasPrev:    false,
			BatchStats: &BatchQueryStats{
				TotalQueried: 5,
				FoundCount:   3,
				NotFound:     []string{"SF1111111111", "YT2222222222"},
				QueryTime:    "95.30ms",
			},
		}

		assert.NotNil(t, data.BatchStats)
		assert.Equal(t, 5, data.BatchStats.TotalQueried)
		assert.Equal(t, 3, data.BatchStats.FoundCount)
		assert.Equal(t, 2, len(data.BatchStats.NotFound))
	})

	t.Run("不包含批量统计信息的普通查询", func(t *testing.T) {
		data := &OrderListData{
			Items:      []*OrderListItem{},
			Total:      10,
			Page:       1,
			PageSize:   20,
			TotalPages: 1,
			HasNext:    false,
			HasPrev:    false,
			BatchStats: nil, // 普通查询不包含批量统计
		}

		assert.Nil(t, data.BatchStats)
	})
}

// BenchmarkValidateOrderListRequest_BatchQuery 批量查询验证性能测试
func BenchmarkValidateOrderListRequest_BatchQuery(b *testing.B) {
	// 创建50个运单号的测试数据
	trackingNos := make([]string, 50)
	for i := 0; i < 50; i++ {
		trackingNos[i] = "SF12345678" + string(rune('0'+i%10)) + string(rune('0'+(i/10)%10))
	}

	req := &OrderListRequest{
		TrackingNos: trackingNos,
		Page:        1,
		PageSize:    20,
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		// 重置请求状态
		reqCopy := *req
		reqCopy.TrackingNos = make([]string, len(trackingNos))
		copy(reqCopy.TrackingNos, trackingNos)
		
		_ = ValidateOrderListRequest(&reqCopy)
	}
}
