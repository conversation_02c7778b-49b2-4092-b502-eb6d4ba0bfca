package model

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"time"
)

// JSONB 自定义类型用于处理PostgreSQL的JSONB字段
type JSONB map[string]interface{}

// Value 实现driver.Valuer接口
func (j JSONB) Value() (driver.Value, error) {
	if j == nil {
		return nil, nil
	}
	return json.Marshal(j)
}

// Scan 实现sql.Scanner接口
func (j *JSONB) Scan(value interface{}) error {
	if value == nil {
		*j = nil
		return nil
	}

	var bytes []byte
	switch v := value.(type) {
	case []byte:
		bytes = v
	case string:
		bytes = []byte(v)
	default:
		return errors.New("cannot scan into JSONB")
	}

	return json.Unmarshal(bytes, j)
}

// RawCallbackData 原始回调数据模型（匹配实际表结构）
type RawCallbackData struct {
	ID         string     `json:"id" gorm:"primaryKey;type:uuid;default:gen_random_uuid()"`
	Provider   string     `json:"provider" gorm:"type:varchar(20);not null;index"`
	RawBody    string     `json:"raw_body" gorm:"type:text;not null"`
	Headers    JSONB      `json:"headers,omitempty" gorm:"type:jsonb"`
	ClientIP   string     `json:"client_ip,omitempty" gorm:"type:varchar(45)"`
	ReceivedAt *time.Time `json:"received_at,omitempty" gorm:"type:timestamp with time zone;default:now()"`
	Processed  bool       `json:"processed" gorm:"type:boolean;default:false;index"`
	CreatedAt  *time.Time `json:"created_at,omitempty" gorm:"type:timestamp with time zone;default:now()"`
	Hash       string     `json:"hash" gorm:"type:varchar(64);not null;uniqueIndex"`
}

// TableName 指定表名
func (RawCallbackData) TableName() string {
	return "callback_raw_data"
}

// RawCallbackStatistics 原始回调统计数据
type RawCallbackStatistics struct {
	TotalRecords   int64                          `json:"total_records"`
	SuccessRecords int64                          `json:"success_records"`
	FailedRecords  int64                          `json:"failed_records"`
	PendingRecords int64                          `json:"pending_records"`
	SuccessRate    float64                        `json:"success_rate"`
	ProviderStats  map[string]ProviderStatistics  `json:"provider_stats"`
	EventTypeStats map[string]EventTypeStatistics `json:"event_type_stats"`
	DailyStats     []DailyStatistics              `json:"daily_stats"`
}

// ProviderStatistics 供应商统计
type ProviderStatistics struct {
	Total   int64 `json:"total"`
	Success int64 `json:"success"`
	Failed  int64 `json:"failed"`
	Pending int64 `json:"pending"`
}

// EventTypeStatistics 事件类型统计
type EventTypeStatistics struct {
	Total   int64 `json:"total"`
	Success int64 `json:"success"`
	Failed  int64 `json:"failed"`
	Pending int64 `json:"pending"`
}

// DailyStatistics 每日统计
type DailyStatistics struct {
	Date    string `json:"date"`
	Total   int64  `json:"total"`
	Success int64  `json:"success"`
	Failed  int64  `json:"failed"`
	Pending int64  `json:"pending"`
}
