package model

import (
	"time"
	"github.com/your-org/go-kuaidi/internal/util"
)

// BeijingTime 北京时间类型，自动处理时区转换
type BeijingTime struct {
	time.Time
}

// NewBeijingTime 创建北京时间
func NewBeijingTime(t time.Time) BeijingTime {
	return BeijingTime{Time: util.ToBeijing(t)}
}

// Now 获取当前北京时间
func NowBeijing() BeijingTime {
	return BeijingTime{Time: util.NowBeijing()}
}

// MarshalJSON 序列化为JSON时自动转换为北京时间
func (bt BeijingTime) MarshalJSON() ([]byte, error) {
	// 转换为北京时间后序列化
	beijingTime := util.ToBeijing(bt.Time)
	return beijingTime.MarshalJSON()
}

// UnmarshalJSON 从JSON反序列化时自动转换为北京时间
func (bt *BeijingTime) UnmarshalJSON(data []byte) error {
	var t time.Time
	if err := t.UnmarshalJSON(data); err != nil {
		return err
	}
	bt.Time = util.ToBeijing(t)
	return nil
}

// String 返回北京时间的字符串表示
func (bt BeijingTime) String() string {
	return util.ToBeijing(bt.Time).Format("2006-01-02 15:04:05")
}