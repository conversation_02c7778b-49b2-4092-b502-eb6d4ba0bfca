package model

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

// TestOrderRecordResponse_Enhancement 测试订单记录响应增强
func TestOrderRecordResponse_Enhancement(t *testing.T) {
	// 创建测试订单记录
	order := &OrderRecord{
		ID:              1,
		PlatformOrderNo: "GK20250711000000001",
		CustomerOrderNo: "MY_ORDER_001",
		OrderNo:         "YD250711131107037536", // 供应商订单号
		TrackingNo:      "312816791325357",
		ExpressType:     "YTO",
		Provider:        "易达",
		Status:          "shipped",
		Price:           10.50,
		ActualFee:       11.00,
		Weight:          1.5,
		UserID:          "test_user",
		CreatedAt:       time.Date(2025, 7, 11, 10, 30, 0, 0, time.UTC),
		UpdatedAt:       time.Date(2025, 7, 11, 11, 30, 0, 0, time.UTC),
	}

	t.Run("FromOrderRecord转换测试", func(t *testing.T) {
		response := &OrderRecordResponse{}
		response.FromOrderRecord(order)

		// 验证新增字段
		assert.Equal(t, "GK20250711000000001", response.PlatformOrderNo, "平台订单号应该正确设置")
		assert.Equal(t, "MY_ORDER_001", response.CustomerOrderNo, "客户订单号应该正确设置")
		assert.Equal(t, "YD250711131107037536", response.ProviderOrderNo, "供应商订单号应该正确设置")
		assert.Equal(t, "312816791325357", response.TrackingNo, "运单号应该正确设置")

		// 验证兼容性字段：OrderNo应该优先显示平台订单号
		assert.Equal(t, "GK20250711000000001", response.OrderNo, "OrderNo应该优先显示平台订单号")

		// 验证其他字段
		assert.Equal(t, int64(1), response.ID)
		assert.Equal(t, "YTO", response.ExpressType)
		assert.Equal(t, "易达", response.Provider)
		assert.Equal(t, "shipped", response.Status)
		assert.Equal(t, 10.50, response.Price)
		assert.Equal(t, 11.00, response.ActualFee)
	})

	t.Run("ToOrderRecordResponse转换测试", func(t *testing.T) {
		response := ToOrderRecordResponse(order)

		assert.NotNil(t, response)
		assert.Equal(t, "GK20250711000000001", response.PlatformOrderNo)
		assert.Equal(t, "MY_ORDER_001", response.CustomerOrderNo)
		assert.Equal(t, "YD250711131107037536", response.ProviderOrderNo)
		assert.Equal(t, "GK20250711000000001", response.OrderNo) // 兼容性字段
	})

	t.Run("无平台订单号的兼容性测试", func(t *testing.T) {
		// 模拟旧数据：没有平台订单号
		oldOrder := &OrderRecord{
			ID:              2,
			PlatformOrderNo: "", // 空平台订单号
			CustomerOrderNo: "OLD_ORDER_001",
			OrderNo:         "YD250711131107037537", // 供应商订单号
			TrackingNo:      "312816791325358",
			ExpressType:     "YTO",
			Provider:        "易达",
			Status:          "pending",
			Price:           8.50,
			UserID:          "test_user",
			CreatedAt:       time.Now(),
			UpdatedAt:       time.Now(),
		}

		response := ToOrderRecordResponse(oldOrder)

		// 验证字段设置
		assert.Equal(t, "", response.PlatformOrderNo, "平台订单号应该为空")
		assert.Equal(t, "OLD_ORDER_001", response.CustomerOrderNo)
		assert.Equal(t, "YD250711131107037537", response.ProviderOrderNo)

		// 验证兼容性：无平台订单号时，OrderNo应该显示供应商订单号
		assert.Equal(t, "YD250711131107037537", response.OrderNo, "无平台订单号时应显示供应商订单号")
	})
}

// TestOrderListItem_Enhancement 测试订单列表项增强
func TestOrderListItem_Enhancement(t *testing.T) {
	t.Run("字段结构验证", func(t *testing.T) {
		item := &OrderListItem{
			ID:              1,
			PlatformOrderNo: "GK20250711000000001",
			CustomerOrderNo: "MY_ORDER_001",
			OrderNo:         "GK20250711000000001", // 兼容性字段
			ProviderOrderNo: "YD250711131107037536",
			TrackingNo:      "312816791325357",
			ExpressType:     "YTO",
			Provider:        "易达",
			Status:          "shipped",
			Price:           10.50,
			Weight:          1.5,
		}

		// 验证所有新增字段都存在
		assert.Equal(t, "GK20250711000000001", item.PlatformOrderNo)
		assert.Equal(t, "MY_ORDER_001", item.CustomerOrderNo)
		assert.Equal(t, "GK20250711000000001", item.OrderNo)
		assert.Equal(t, "YD250711131107037536", item.ProviderOrderNo)
		assert.Equal(t, "312816791325357", item.TrackingNo)

		// 验证其他字段
		assert.Equal(t, int64(1), item.ID)
		assert.Equal(t, "YTO", item.ExpressType)
		assert.Equal(t, "易达", item.Provider)
		assert.Equal(t, "shipped", item.Status)
		assert.Equal(t, 10.50, item.Price)
		assert.Equal(t, 1.5, item.Weight)
	})

	t.Run("向后兼容性验证", func(t *testing.T) {
		// 模拟旧数据结构
		item := &OrderListItem{
			ID:              2,
			PlatformOrderNo: "", // 空平台订单号
			CustomerOrderNo: "OLD_ORDER_001",
			OrderNo:         "YD250711131107037537", // 在旧系统中这里存储供应商订单号
			ProviderOrderNo: "YD250711131107037537",
			TrackingNo:      "312816791325358",
			ExpressType:     "STO",
			Provider:        "申通",
			Status:          "pending",
		}

		// 验证向后兼容性
		assert.Equal(t, "", item.PlatformOrderNo)
		assert.Equal(t, "OLD_ORDER_001", item.CustomerOrderNo)
		assert.Equal(t, "YD250711131107037537", item.OrderNo)
		assert.Equal(t, "YD250711131107037537", item.ProviderOrderNo)
	})
}

// TestOrderResult_Enhancement 测试订单结果增强
func TestOrderResult_Enhancement(t *testing.T) {
	t.Run("字段结构验证", func(t *testing.T) {
		result := &OrderResult{
			PlatformOrderNo: "GK20250711000000001",
			CustomerOrderNo: "MY_ORDER_001",
			OrderNo:         "YD250711131107037536", // 供应商订单号
			TrackingNo:      "312816791325357",
			TaskId:          "TASK_001",
			PollToken:       "POLL_TOKEN_001",
			PickupCode:      "PICKUP_001",
			Price:           10.50,
		}

		// 验证所有新增字段都存在
		assert.Equal(t, "GK20250711000000001", result.PlatformOrderNo)
		assert.Equal(t, "MY_ORDER_001", result.CustomerOrderNo)
		assert.Equal(t, "YD250711131107037536", result.OrderNo)
		assert.Equal(t, "312816791325357", result.TrackingNo)

		// 验证其他字段
		assert.Equal(t, "TASK_001", result.TaskId)
		assert.Equal(t, "POLL_TOKEN_001", result.PollToken)
		assert.Equal(t, "PICKUP_001", result.PickupCode)
		assert.Equal(t, 10.50, result.Price)
	})
}

// TestAPIResponseCompatibility 测试API响应兼容性
func TestAPIResponseCompatibility(t *testing.T) {
	t.Run("新旧字段共存", func(t *testing.T) {
		// 创建包含新旧字段的响应
		response := &OrderRecordResponse{
			ID:              1,
			PlatformOrderNo: "GK20250711000000001", // 新字段
			CustomerOrderNo: "MY_ORDER_001",
			OrderNo:         "GK20250711000000001",  // 兼容性字段，显示平台订单号
			ProviderOrderNo: "YD250711131107037536", // 新字段，存储供应商订单号
			TrackingNo:      "312816791325357",
			ExpressType:     "YTO",
			Provider:        "易达",
			Status:          "shipped",
			Price:           10.50,
		}

		// 验证新字段存在
		assert.NotEmpty(t, response.PlatformOrderNo, "平台订单号字段应该存在")
		assert.NotEmpty(t, response.ProviderOrderNo, "供应商订单号字段应该存在")

		// 验证兼容性字段
		assert.NotEmpty(t, response.OrderNo, "兼容性OrderNo字段应该存在")
		assert.Equal(t, response.PlatformOrderNo, response.OrderNo, "兼容性字段应该显示平台订单号")

		// 验证字段语义正确性
		assert.NotEqual(t, response.OrderNo, response.ProviderOrderNo, "OrderNo和ProviderOrderNo应该有不同的语义")
	})

	t.Run("字段语义清晰性", func(t *testing.T) {
		// 验证字段语义定义清晰
		response := &OrderRecordResponse{}

		// 通过字段标签验证JSON字段名
		// 这里可以通过反射检查字段标签，但为了简化测试，直接验证字段存在性
		assert.NotNil(t, &response.PlatformOrderNo, "平台订单号字段应该存在")
		assert.NotNil(t, &response.CustomerOrderNo, "客户订单号字段应该存在")
		assert.NotNil(t, &response.OrderNo, "兼容性订单号字段应该存在")
		assert.NotNil(t, &response.ProviderOrderNo, "供应商订单号字段应该存在")
		assert.NotNil(t, &response.TrackingNo, "运单号字段应该存在")

		t.Log("✅ 所有订单标识字段都已正确定义")
		t.Log("📋 字段语义:")
		t.Log("  - platform_order_no: 平台生成的全局唯一订单号")
		t.Log("  - customer_order_no: 客户传入的订单号")
		t.Log("  - order_no: 兼容性字段，优先显示平台订单号")
		t.Log("  - provider_order_no: 供应商返回的订单号")
		t.Log("  - tracking_no: 快递运单号")
	})
}
