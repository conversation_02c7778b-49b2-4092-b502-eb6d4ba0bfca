package model

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestOrderRecord_PlatformOrderNo 测试平台订单号相关功能
func TestOrderRecord_PlatformOrderNo(t *testing.T) {
	t.Run("GetPrimaryOrderNo - 优先返回平台订单号", func(t *testing.T) {
		order := &OrderRecord{
			PlatformOrderNo: "GK20250711000000001",
			OrderNo:         "YD250711131107037536",
		}

		primaryOrderNo := order.GetPrimaryOrderNo()
		assert.Equal(t, "GK20250711000000001", primaryOrderNo)
	})

	t.Run("GetPrimaryOrderNo - 无平台订单号时返回供应商订单号", func(t *testing.T) {
		order := &OrderRecord{
			PlatformOrderNo: "",
			OrderNo:         "YD250711131107037536",
		}

		primaryOrderNo := order.GetPrimaryOrderNo()
		assert.Equal(t, "YD250711131107037536", primaryOrderNo)
	})

	t.Run("GetProviderOrderNo - 获取供应商订单号", func(t *testing.T) {
		order := &OrderRecord{
			PlatformOrderNo: "GK20250711000000001",
			OrderNo:         "YD250711131107037536",
		}

		providerOrderNo := order.GetProviderOrderNo()
		assert.Equal(t, "YD250711131107037536", providerOrderNo)
	})

	t.Run("SetPlatformOrderNo - 设置平台订单号", func(t *testing.T) {
		order := &OrderRecord{}
		order.SetPlatformOrderNo("GK20250711000000001")

		assert.Equal(t, "GK20250711000000001", order.PlatformOrderNo)
		assert.True(t, order.HasPlatformOrderNo())
	})

	t.Run("SetProviderOrderNo - 设置供应商订单号", func(t *testing.T) {
		order := &OrderRecord{}
		order.SetProviderOrderNo("YD250711131107037536")

		assert.Equal(t, "YD250711131107037536", order.OrderNo)
		assert.Equal(t, "YD250711131107037536", order.GetProviderOrderNo())
	})

	t.Run("HasPlatformOrderNo - 检查平台订单号存在性", func(t *testing.T) {
		// 有平台订单号
		order1 := &OrderRecord{PlatformOrderNo: "GK20250711000000001"}
		assert.True(t, order1.HasPlatformOrderNo())

		// 无平台订单号
		order2 := &OrderRecord{PlatformOrderNo: ""}
		assert.False(t, order2.HasPlatformOrderNo())

		// nil情况
		order3 := &OrderRecord{}
		assert.False(t, order3.HasPlatformOrderNo())
	})
}

// TestOrderRecord_GetOrderIdentifiers 测试获取订单标识符
func TestOrderRecord_GetOrderIdentifiers(t *testing.T) {
	t.Run("完整的订单标识符", func(t *testing.T) {
		order := &OrderRecord{
			PlatformOrderNo: "GK20250711000000001",
			CustomerOrderNo: "MY_ORDER_001",
			OrderNo:         "YD250711131107037536",
			TrackingNo:      "312816791325357",
		}

		identifiers := order.GetOrderIdentifiers()

		expected := map[string]string{
			"platform_order_no": "GK20250711000000001",
			"customer_order_no": "MY_ORDER_001",
			"provider_order_no": "YD250711131107037536",
			"tracking_no":       "312816791325357",
		}

		assert.Equal(t, expected, identifiers)
	})

	t.Run("部分订单标识符", func(t *testing.T) {
		order := &OrderRecord{
			CustomerOrderNo: "MY_ORDER_001",
			OrderNo:         "YD250711131107037536",
		}

		identifiers := order.GetOrderIdentifiers()

		expected := map[string]string{
			"customer_order_no": "MY_ORDER_001",
			"provider_order_no": "YD250711131107037536",
		}

		assert.Equal(t, expected, identifiers)
		assert.NotContains(t, identifiers, "platform_order_no")
		assert.NotContains(t, identifiers, "tracking_no")
	})

	t.Run("空订单标识符", func(t *testing.T) {
		order := &OrderRecord{}

		identifiers := order.GetOrderIdentifiers()

		assert.Empty(t, identifiers)
	})
}

// TestOrderRecord_ValidateOrderIdentifiers 测试订单标识符验证
func TestOrderRecord_ValidateOrderIdentifiers(t *testing.T) {
	t.Run("有效的订单标识符", func(t *testing.T) {
		order := &OrderRecord{
			PlatformOrderNo: "GK20250711000000001",
			CustomerOrderNo: "MY_ORDER_001",
		}

		err := order.ValidateOrderIdentifiers()
		assert.NoError(t, err)
	})

	t.Run("缺少客户订单号", func(t *testing.T) {
		order := &OrderRecord{
			PlatformOrderNo: "GK20250711000000001",
			CustomerOrderNo: "",
		}

		err := order.ValidateOrderIdentifiers()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "客户订单号不能为空")
	})

	t.Run("平台订单号格式错误 - 长度不正确", func(t *testing.T) {
		order := &OrderRecord{
			PlatformOrderNo: "GK2025071100000001", // 少一位
			CustomerOrderNo: "MY_ORDER_001",
		}

		err := order.ValidateOrderIdentifiers()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "平台订单号格式不正确，长度应为19位")
	})

	t.Run("平台订单号格式错误 - 前缀不正确", func(t *testing.T) {
		order := &OrderRecord{
			PlatformOrderNo: "XX20250711000000001", // 错误前缀
			CustomerOrderNo: "MY_ORDER_001",
		}

		err := order.ValidateOrderIdentifiers()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "平台订单号格式不正确，应以GK开头")
	})

	t.Run("无平台订单号时不验证格式", func(t *testing.T) {
		order := &OrderRecord{
			PlatformOrderNo: "", // 空平台订单号
			CustomerOrderNo: "MY_ORDER_001",
		}

		err := order.ValidateOrderIdentifiers()
		assert.NoError(t, err)
	})
}

// TestOrderRecord_FieldSemantics 测试字段语义
func TestOrderRecord_FieldSemantics(t *testing.T) {
	t.Run("字段语义验证", func(t *testing.T) {
		order := &OrderRecord{
			PlatformOrderNo: "GK20250711000000001",  // 平台生成的全局唯一订单号
			CustomerOrderNo: "MY_ORDER_001",         // 客户传入的订单号
			OrderNo:         "YD250711131107037536", // 供应商返回的订单号
			TrackingNo:      "312816791325357",      // 快递运单号
		}

		// 验证字段语义正确性
		assert.Equal(t, "GK20250711000000001", order.PlatformOrderNo, "平台订单号应该是平台生成的")
		assert.Equal(t, "MY_ORDER_001", order.CustomerOrderNo, "客户订单号应该是客户传入的")
		assert.Equal(t, "YD250711131107037536", order.OrderNo, "OrderNo现在表示供应商订单号")
		assert.Equal(t, "312816791325357", order.TrackingNo, "运单号应该是快递公司的")

		// 验证兼容性方法
		assert.Equal(t, "GK20250711000000001", order.GetPrimaryOrderNo(), "主要订单号应该是平台订单号")
		assert.Equal(t, "YD250711131107037536", order.GetProviderOrderNo(), "供应商订单号应该是OrderNo字段")
	})

	t.Run("向后兼容性验证", func(t *testing.T) {
		// 模拟旧数据：只有OrderNo，没有PlatformOrderNo
		order := &OrderRecord{
			CustomerOrderNo: "MY_ORDER_001",
			OrderNo:         "YD250711131107037536", // 旧数据中这里存储的是供应商订单号
		}

		// 验证向后兼容性
		assert.Equal(t, "YD250711131107037536", order.GetPrimaryOrderNo(), "无平台订单号时应返回OrderNo")
		assert.Equal(t, "YD250711131107037536", order.GetProviderOrderNo(), "供应商订单号应该是OrderNo")
		assert.False(t, order.HasPlatformOrderNo(), "旧数据没有平台订单号")

		// 验证可以后续添加平台订单号
		order.SetPlatformOrderNo("GK20250711000000001")
		assert.Equal(t, "GK20250711000000001", order.GetPrimaryOrderNo(), "添加平台订单号后应优先返回")
		assert.True(t, order.HasPlatformOrderNo(), "添加后应该有平台订单号")
	})
}

// TestOrderRecord_JSONSerialization 测试JSON序列化
func TestOrderRecord_JSONSerialization(t *testing.T) {
	t.Run("JSON序列化包含所有字段", func(t *testing.T) {
		order := &OrderRecord{
			ID:              1,
			PlatformOrderNo: "GK20250711000000001",
			CustomerOrderNo: "MY_ORDER_001",
			OrderNo:         "YD250711131107037536",
			TrackingNo:      "312816791325357",
			Provider:        "易达",
			Status:          "已下单",
		}

		// 这里可以添加JSON序列化测试
		// 由于当前结构体使用标准json标签，序列化应该正常工作
		assert.NotEmpty(t, order.PlatformOrderNo)
		assert.NotEmpty(t, order.CustomerOrderNo)
		assert.NotEmpty(t, order.OrderNo)
		assert.NotEmpty(t, order.TrackingNo)

		t.Logf("订单信息: ID=%d, 平台订单号=%s, 客户订单号=%s, 供应商订单号=%s, 运单号=%s",
			order.ID, order.PlatformOrderNo, order.CustomerOrderNo, order.OrderNo, order.TrackingNo)
	})
}

// BenchmarkOrderRecord_GetPrimaryOrderNo 性能基准测试
func BenchmarkOrderRecord_GetPrimaryOrderNo(b *testing.B) {
	order := &OrderRecord{
		PlatformOrderNo: "GK20250711000000001",
		OrderNo:         "YD250711131107037536",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = order.GetPrimaryOrderNo()
	}
}

// BenchmarkOrderRecord_GetOrderIdentifiers 性能基准测试
func BenchmarkOrderRecord_GetOrderIdentifiers(b *testing.B) {
	order := &OrderRecord{
		PlatformOrderNo: "GK20250711000000001",
		CustomerOrderNo: "MY_ORDER_001",
		OrderNo:         "YD250711131107037536",
		TrackingNo:      "312816791325357",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = order.GetOrderIdentifiers()
	}
}
