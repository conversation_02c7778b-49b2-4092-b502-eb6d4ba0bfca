package benchmark

import (
	"context"
	"fmt"
	"runtime"
	"sync"
	"sync/atomic"
	"time"

	"github.com/your-org/go-kuaidi/internal/memory"
	"github.com/your-org/go-kuaidi/internal/util"
	"go.uber.org/zap"
)

// PerformanceBenchmark 性能基准测试器
// 基于第一性原理设计：全面性能测试 + 实时监控 + 智能分析
type PerformanceBenchmark struct {
	logger *zap.Logger

	// 测试配置
	config *BenchmarkConfig

	// 监控组件
	gcOptimizer   *memory.GCOptimizer
	jsonOptimizer *util.JSONOptimizer

	// 测试结果
	results *BenchmarkResults

	// 控制
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup
}

// BenchmarkConfig 基准测试配置
type BenchmarkConfig struct {
	// 测试持续时间
	Duration time.Duration

	// 并发级别
	ConcurrencyLevels []int

	// 测试场景
	Scenarios []TestScenario

	// 性能目标
	PerformanceTargets *PerformanceTargets

	// 监控间隔
	MonitorInterval time.Duration
}

// TestScenario 测试场景
type TestScenario struct {
	Name        string
	Description string
	TestFunc    func(ctx context.Context, concurrency int) (*ScenarioResult, error)
}

// PerformanceTargets 性能目标
type PerformanceTargets struct {
	MaxCPUUsage     float64       // 最大CPU使用率
	MaxMemoryUsage  uint64        // 最大内存使用量（字节）
	MaxResponseTime time.Duration // 最大响应时间
	MinThroughput   int64         // 最小吞吐量（请求/秒）
	MaxGCPauseTime  time.Duration // 最大GC暂停时间
}

// BenchmarkResults 基准测试结果
type BenchmarkResults struct {
	// 测试概要
	StartTime time.Time
	EndTime   time.Time
	Duration  time.Duration

	// 场景结果
	ScenarioResults map[string]*ScenarioResult

	// 系统性能
	SystemMetrics *SystemMetrics

	// 性能评估
	PerformanceScore float64
	PassedTargets    []string
	FailedTargets    []string

	mutex sync.RWMutex
}

// ScenarioResult 场景测试结果
type ScenarioResult struct {
	Name            string
	Concurrency     int
	TotalRequests   int64
	SuccessRequests int64
	FailedRequests  int64
	TotalDuration   time.Duration
	AverageLatency  time.Duration
	MinLatency      time.Duration
	MaxLatency      time.Duration
	Throughput      float64 // 请求/秒
	ErrorRate       float64 // 错误率

	// 延迟分布
	LatencyPercentiles map[int]time.Duration // P50, P90, P95, P99
}

// SystemMetrics 系统指标
type SystemMetrics struct {
	// CPU指标
	CPUUsage float64
	CPUCores int

	// 内存指标
	HeapAlloc    uint64
	HeapSys      uint64
	HeapInuse    uint64
	GCCount      uint32
	GCPauseTotal time.Duration
	GCPauseAvg   time.Duration

	// Goroutine指标
	GoroutineCount int

	// 网络指标
	NetworkConnections int

	// 自定义指标
	JSONOperations int64
	PoolHitRate    float64
}

// NewPerformanceBenchmark 创建性能基准测试器
func NewPerformanceBenchmark(logger *zap.Logger, config *BenchmarkConfig) *PerformanceBenchmark {
	if config == nil {
		config = &BenchmarkConfig{
			Duration:          5 * time.Minute,
			ConcurrencyLevels: []int{10, 50, 100, 500, 1000},
			MonitorInterval:   5 * time.Second,
			PerformanceTargets: &PerformanceTargets{
				MaxCPUUsage:     70.0,
				MaxMemoryUsage:  2 * 1024 * 1024 * 1024, // 2GB
				MaxResponseTime: 200 * time.Millisecond,
				MinThroughput:   1000,
				MaxGCPauseTime:  10 * time.Millisecond,
			},
		}
	}

	ctx, cancel := context.WithCancel(context.Background())

	return &PerformanceBenchmark{
		logger:        logger,
		config:        config,
		gcOptimizer:   memory.GetGlobalGCOptimizer(),
		jsonOptimizer: util.GetGlobalJSONOptimizer(),
		results: &BenchmarkResults{
			ScenarioResults: make(map[string]*ScenarioResult),
		},
		ctx:    ctx,
		cancel: cancel,
	}
}

// RunBenchmark 运行基准测试
func (b *PerformanceBenchmark) RunBenchmark() (*BenchmarkResults, error) {
	b.logger.Info("开始性能基准测试",
		zap.Duration("duration", b.config.Duration),
		zap.Ints("concurrency_levels", b.config.ConcurrencyLevels))

	b.results.StartTime = time.Now()

	// 启动系统监控
	b.startSystemMonitoring()

	// 运行测试场景
	for _, scenario := range b.config.Scenarios {
		for _, concurrency := range b.config.ConcurrencyLevels {
			b.logger.Info("运行测试场景",
				zap.String("scenario", scenario.Name),
				zap.Int("concurrency", concurrency))

			result, err := b.runScenario(scenario, concurrency)
			if err != nil {
				b.logger.Error("测试场景失败",
					zap.String("scenario", scenario.Name),
					zap.Int("concurrency", concurrency),
					zap.Error(err))
				continue
			}

			key := fmt.Sprintf("%s_%d", scenario.Name, concurrency)
			b.results.mutex.Lock()
			b.results.ScenarioResults[key] = result
			b.results.mutex.Unlock()
		}
	}

	b.results.EndTime = time.Now()
	b.results.Duration = b.results.EndTime.Sub(b.results.StartTime)

	// 收集最终系统指标
	b.collectSystemMetrics()

	// 评估性能
	b.evaluatePerformance()

	b.logger.Info("性能基准测试完成",
		zap.Duration("total_duration", b.results.Duration),
		zap.Float64("performance_score", b.results.PerformanceScore))

	return b.results, nil
}

// runScenario 运行单个测试场景
func (b *PerformanceBenchmark) runScenario(scenario TestScenario, concurrency int) (*ScenarioResult, error) {
	ctx, cancel := context.WithTimeout(b.ctx, b.config.Duration)
	defer cancel()

	startTime := time.Now()

	// 执行测试
	result, err := scenario.TestFunc(ctx, concurrency)
	if err != nil {
		return nil, err
	}

	result.TotalDuration = time.Since(startTime)
	result.Name = scenario.Name
	result.Concurrency = concurrency

	// 计算吞吐量
	if result.TotalDuration > 0 {
		result.Throughput = float64(result.SuccessRequests) / result.TotalDuration.Seconds()
	}

	// 计算错误率
	if result.TotalRequests > 0 {
		result.ErrorRate = float64(result.FailedRequests) / float64(result.TotalRequests) * 100
	}

	return result, nil
}

// startSystemMonitoring 启动系统监控
func (b *PerformanceBenchmark) startSystemMonitoring() {
	b.wg.Add(1)
	go func() {
		defer b.wg.Done()

		ticker := time.NewTicker(b.config.MonitorInterval)
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				b.collectSystemMetrics()

			case <-b.ctx.Done():
				return
			}
		}
	}()
}

// collectSystemMetrics 收集系统指标
func (b *PerformanceBenchmark) collectSystemMetrics() {
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)

	// 获取GC统计
	gcStats := b.gcOptimizer.GetStats()

	// 获取JSON优化器统计
	jsonStats := b.jsonOptimizer.GetStats()

	metrics := &SystemMetrics{
		CPUCores:       runtime.NumCPU(),
		HeapAlloc:      memStats.HeapAlloc,
		HeapSys:        memStats.HeapSys,
		HeapInuse:      memStats.HeapInuse,
		GCCount:        gcStats.NumGC,
		GCPauseTotal:   gcStats.PauseTotal,
		GCPauseAvg:     gcStats.PauseAvg,
		GoroutineCount: runtime.NumGoroutine(),
		JSONOperations: jsonStats.MarshalCount + jsonStats.UnmarshalCount,
	}

	// 计算池命中率
	if jsonStats.PoolHits+jsonStats.PoolMisses > 0 {
		metrics.PoolHitRate = float64(jsonStats.PoolHits) / float64(jsonStats.PoolHits+jsonStats.PoolMisses)
	}

	b.results.mutex.Lock()
	b.results.SystemMetrics = metrics
	b.results.mutex.Unlock()
}

// evaluatePerformance 评估性能
func (b *PerformanceBenchmark) evaluatePerformance() {
	targets := b.config.PerformanceTargets
	metrics := b.results.SystemMetrics

	var score float64 = 100.0
	var passed, failed []string

	// 检查CPU使用率
	if metrics.CPUUsage <= targets.MaxCPUUsage {
		passed = append(passed, "CPU使用率")
	} else {
		failed = append(failed, "CPU使用率")
		score -= 20
	}

	// 检查内存使用
	if metrics.HeapAlloc <= targets.MaxMemoryUsage {
		passed = append(passed, "内存使用")
	} else {
		failed = append(failed, "内存使用")
		score -= 20
	}

	// 检查GC暂停时间
	if metrics.GCPauseAvg <= targets.MaxGCPauseTime {
		passed = append(passed, "GC暂停时间")
	} else {
		failed = append(failed, "GC暂停时间")
		score -= 15
	}

	// 检查响应时间和吞吐量
	for _, result := range b.results.ScenarioResults {
		if result.AverageLatency <= targets.MaxResponseTime {
			passed = append(passed, fmt.Sprintf("%s响应时间", result.Name))
		} else {
			failed = append(failed, fmt.Sprintf("%s响应时间", result.Name))
			score -= 10
		}

		if result.Throughput >= float64(targets.MinThroughput) {
			passed = append(passed, fmt.Sprintf("%s吞吐量", result.Name))
		} else {
			failed = append(failed, fmt.Sprintf("%s吞吐量", result.Name))
			score -= 10
		}
	}

	// 确保分数不为负数
	if score < 0 {
		score = 0
	}

	b.results.PerformanceScore = score
	b.results.PassedTargets = passed
	b.results.FailedTargets = failed
}

// CreatePriceQueryScenario 创建价格查询测试场景
func CreatePriceQueryScenario() TestScenario {
	return TestScenario{
		Name:        "价格查询",
		Description: "测试价格查询API的性能",
		TestFunc: func(ctx context.Context, concurrency int) (*ScenarioResult, error) {
			var totalRequests, successRequests, failedRequests int64
			var totalLatency time.Duration
			var minLatency, maxLatency time.Duration = time.Hour, 0

			// 使用WaitGroup等待所有goroutine完成
			var wg sync.WaitGroup

			// 启动并发测试
			for i := 0; i < concurrency; i++ {
				wg.Add(1)
				go func() {
					defer wg.Done()

					for {
						select {
						case <-ctx.Done():
							return
						default:
							start := time.Now()

							// 模拟价格查询操作
							err := simulatePriceQuery()
							latency := time.Since(start)

							atomic.AddInt64(&totalRequests, 1)

							if err != nil {
								atomic.AddInt64(&failedRequests, 1)
							} else {
								atomic.AddInt64(&successRequests, 1)
							}

							// 更新延迟统计（需要加锁）
							if latency < minLatency {
								minLatency = latency
							}
							if latency > maxLatency {
								maxLatency = latency
							}

							totalLatency += latency
						}
					}
				}()
			}

			wg.Wait()

			var avgLatency time.Duration
			if totalRequests > 0 {
				avgLatency = totalLatency / time.Duration(totalRequests)
			}

			return &ScenarioResult{
				TotalRequests:   totalRequests,
				SuccessRequests: successRequests,
				FailedRequests:  failedRequests,
				AverageLatency:  avgLatency,
				MinLatency:      minLatency,
				MaxLatency:      maxLatency,
			}, nil
		},
	}
}

// simulatePriceQuery 模拟价格查询
func simulatePriceQuery() error {
	// 模拟一些CPU密集型操作
	data := map[string]interface{}{
		"sender_province":   "北京市",
		"sender_city":       "北京市",
		"receiver_province": "上海市",
		"receiver_city":     "上海市",
		"weight":            1.5,
		"volume":            0.001,
	}

	// 使用优化的JSON序列化
	jsonOptimizer := util.GetGlobalJSONOptimizer()
	_, err := jsonOptimizer.FastMarshal(data)
	if err != nil {
		return err
	}

	// 模拟网络延迟
	time.Sleep(time.Millisecond * 10)

	return nil
}

// Shutdown 关闭基准测试器
func (b *PerformanceBenchmark) Shutdown() {
	b.cancel()
	b.wg.Wait()
	b.logger.Info("性能基准测试器已关闭")
}
