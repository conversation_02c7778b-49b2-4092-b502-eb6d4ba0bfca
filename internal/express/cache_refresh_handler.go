package express

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"
)

// CacheRefreshHandler 缓存刷新处理器
// 响应配置变更事件，自动刷新相关缓存
type CacheRefreshHandler struct {
	cacheService          *ExpressMappingCacheService
	dynamicProviderManager DynamicProviderManager
	logger                *zap.Logger
	
	// 性能优化配置
	batchRefreshInterval time.Duration
	maxBatchSize         int
	
	// 批量刷新队列
	refreshQueue chan RefreshRequest
	stopChan     chan struct{}
}

// DynamicProviderManager 动态供应商管理器接口（避免循环依赖）
type DynamicProviderManager interface {
	ReloadProvider(ctx context.Context, providerCode string) error
	RefreshAllProviders(ctx context.Context) error
}

// RefreshRequest 刷新请求
type RefreshRequest struct {
	Type         RefreshType
	CompanyCode  string
	ProviderCode string
	Timestamp    time.Time
}

// RefreshType 刷新类型
type RefreshType string

const (
	RefreshTypeCompany  RefreshType = "company"
	RefreshTypeProvider RefreshType = "provider"
	RefreshTypeMapping  RefreshType = "mapping"
	RefreshTypeAll      RefreshType = "all"
)

// NewCacheRefreshHandler 创建缓存刷新处理器
func NewCacheRefreshHandler(
	cacheService *ExpressMappingCacheService,
	dynamicProviderManager DynamicProviderManager,
	logger *zap.Logger,
) *CacheRefreshHandler {
	return &CacheRefreshHandler{
		cacheService:           cacheService,
		dynamicProviderManager: dynamicProviderManager,
		logger:                 logger,
		batchRefreshInterval:   2 * time.Second, // 批量刷新间隔
		maxBatchSize:          10,               // 最大批量大小
		refreshQueue:          make(chan RefreshRequest, 100),
		stopChan:              make(chan struct{}),
	}
}

// HandleConfigChange 处理配置变更事件
func (h *CacheRefreshHandler) HandleConfigChange(ctx context.Context, event ConfigChangeEvent) error {
	h.logger.Info("收到配置变更事件",
		zap.String("type", string(event.Type)),
		zap.String("company_code", event.CompanyCode),
		zap.String("provider_code", event.ProviderCode),
		zap.Bool("enabled", event.Enabled),
		zap.String("operator_id", event.OperatorID))
	
	// 根据事件类型创建刷新请求
	var refreshReq RefreshRequest
	switch event.Type {
	case ConfigChangeTypeCompany:
		refreshReq = RefreshRequest{
			Type:        RefreshTypeCompany,
			CompanyCode: event.CompanyCode,
			Timestamp:   event.Timestamp,
		}
	case ConfigChangeTypeProvider:
		refreshReq = RefreshRequest{
			Type:         RefreshTypeProvider,
			ProviderCode: event.ProviderCode,
			Timestamp:    event.Timestamp,
		}
	case ConfigChangeTypeMapping:
		refreshReq = RefreshRequest{
			Type:         RefreshTypeMapping,
			CompanyCode:  event.CompanyCode,
			ProviderCode: event.ProviderCode,
			Timestamp:    event.Timestamp,
		}
	default:
		return fmt.Errorf("未知的配置变更类型: %s", event.Type)
	}
	
	// 将刷新请求加入队列
	select {
	case h.refreshQueue <- refreshReq:
		h.logger.Debug("刷新请求已加入队列", zap.Any("request", refreshReq))
	default:
		h.logger.Warn("刷新队列已满，直接执行刷新", zap.Any("request", refreshReq))
		// 队列满时直接执行刷新
		return h.executeRefresh(ctx, refreshReq)
	}
	
	return nil
}

// Start 启动缓存刷新处理器
func (h *CacheRefreshHandler) Start(ctx context.Context) {
	go h.processBatchRefresh(ctx)
	h.logger.Info("缓存刷新处理器启动成功")
}

// Stop 停止缓存刷新处理器
func (h *CacheRefreshHandler) Stop() {
	close(h.stopChan)
	h.logger.Info("缓存刷新处理器停止成功")
}

// processBatchRefresh 处理批量刷新
func (h *CacheRefreshHandler) processBatchRefresh(ctx context.Context) {
	ticker := time.NewTicker(h.batchRefreshInterval)
	defer ticker.Stop()
	
	var batch []RefreshRequest
	
	for {
		select {
		case <-ctx.Done():
			h.logger.Info("缓存刷新处理器收到上下文取消信号")
			return
		case <-h.stopChan:
			h.logger.Info("缓存刷新处理器收到停止信号")
			return
		case req := <-h.refreshQueue:
			batch = append(batch, req)
			
			// 如果批量大小达到上限，立即处理
			if len(batch) >= h.maxBatchSize {
				h.processBatch(ctx, batch)
				batch = batch[:0] // 清空批量
			}
		case <-ticker.C:
			// 定时处理批量
			if len(batch) > 0 {
				h.processBatch(ctx, batch)
				batch = batch[:0] // 清空批量
			}
		}
	}
}

// processBatch 处理批量刷新请求
func (h *CacheRefreshHandler) processBatch(ctx context.Context, batch []RefreshRequest) {
	if len(batch) == 0 {
		return
	}
	
	h.logger.Info("开始处理批量刷新请求", zap.Int("batch_size", len(batch)))
	
	// 去重和优化批量请求
	optimizedBatch := h.optimizeBatch(batch)
	
	// 执行刷新
	for _, req := range optimizedBatch {
		if err := h.executeRefresh(ctx, req); err != nil {
			h.logger.Error("执行刷新失败",
				zap.Any("request", req),
				zap.Error(err))
		}
	}
	
	h.logger.Info("批量刷新请求处理完成",
		zap.Int("original_size", len(batch)),
		zap.Int("optimized_size", len(optimizedBatch)))
}

// optimizeBatch 优化批量请求（去重、合并）
func (h *CacheRefreshHandler) optimizeBatch(batch []RefreshRequest) []RefreshRequest {
	// 使用map去重
	requestMap := make(map[string]RefreshRequest)
	
	for _, req := range batch {
		key := h.getRequestKey(req)
		
		// 保留最新的请求
		if existing, exists := requestMap[key]; !exists || req.Timestamp.After(existing.Timestamp) {
			requestMap[key] = req
		}
	}
	
	// 检查是否需要全量刷新
	hasProviderChange := false
	for _, req := range requestMap {
		if req.Type == RefreshTypeProvider {
			hasProviderChange = true
			break
		}
	}
	
	// 如果有供应商变更，执行全量刷新更高效
	if hasProviderChange && len(requestMap) > 3 {
		return []RefreshRequest{{
			Type:      RefreshTypeAll,
			Timestamp: time.Now(),
		}}
	}
	
	// 转换为切片
	result := make([]RefreshRequest, 0, len(requestMap))
	for _, req := range requestMap {
		result = append(result, req)
	}
	
	return result
}

// getRequestKey 获取请求的唯一键
func (h *CacheRefreshHandler) getRequestKey(req RefreshRequest) string {
	switch req.Type {
	case RefreshTypeCompany:
		return fmt.Sprintf("company:%s", req.CompanyCode)
	case RefreshTypeProvider:
		return fmt.Sprintf("provider:%s", req.ProviderCode)
	case RefreshTypeMapping:
		return fmt.Sprintf("mapping:%s:%s", req.CompanyCode, req.ProviderCode)
	case RefreshTypeAll:
		return "all"
	default:
		return fmt.Sprintf("unknown:%s:%s", req.CompanyCode, req.ProviderCode)
	}
}

// executeRefresh 执行刷新操作
func (h *CacheRefreshHandler) executeRefresh(ctx context.Context, req RefreshRequest) error {
	startTime := time.Now()
	
	switch req.Type {
	case RefreshTypeCompany:
		// 快递公司状态变更，刷新映射缓存
		err := h.cacheService.RefreshCache(ctx)
		if err != nil {
			return fmt.Errorf("刷新快递公司缓存失败: %w", err)
		}
		
		h.logger.Info("快递公司缓存刷新成功",
			zap.String("company_code", req.CompanyCode),
			zap.Duration("duration", time.Since(startTime)))
		
	case RefreshTypeProvider:
		// 供应商状态变更，重新加载供应商适配器
		err := h.dynamicProviderManager.ReloadProvider(ctx, req.ProviderCode)
		if err != nil {
			return fmt.Errorf("重新加载供应商适配器失败: %w", err)
		}
		
		// 同时刷新映射缓存
		err = h.cacheService.RefreshCache(ctx)
		if err != nil {
			h.logger.Warn("刷新映射缓存失败", zap.Error(err))
		}
		
		h.logger.Info("供应商适配器重新加载成功",
			zap.String("provider_code", req.ProviderCode),
			zap.Duration("duration", time.Since(startTime)))
		
	case RefreshTypeMapping:
		// 映射关系变更，刷新映射缓存
		err := h.cacheService.RefreshCache(ctx)
		if err != nil {
			return fmt.Errorf("刷新映射缓存失败: %w", err)
		}
		
		h.logger.Info("映射关系缓存刷新成功",
			zap.String("company_code", req.CompanyCode),
			zap.String("provider_code", req.ProviderCode),
			zap.Duration("duration", time.Since(startTime)))
		
	case RefreshTypeAll:
		// 全量刷新
		err := h.dynamicProviderManager.RefreshAllProviders(ctx)
		if err != nil {
			return fmt.Errorf("全量刷新供应商适配器失败: %w", err)
		}
		
		err = h.cacheService.RefreshCache(ctx)
		if err != nil {
			return fmt.Errorf("全量刷新映射缓存失败: %w", err)
		}
		
		h.logger.Info("全量缓存刷新成功",
			zap.Duration("duration", time.Since(startTime)))
		
	default:
		return fmt.Errorf("未知的刷新类型: %s", req.Type)
	}
	
	return nil
}
