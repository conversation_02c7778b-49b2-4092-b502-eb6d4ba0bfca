package express

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/go-redis/redis/v8"
	"go.uber.org/zap"
)

// ConfigChangeNotifier 配置变更通知器
// 实现实时配置热重载机制，支持Redis发布/订阅和WebSocket通知
type ConfigChangeNotifier interface {
	// PublishConfigChange 发布配置变更事件
	PublishConfigChange(ctx context.Context, event ConfigChangeEvent) error
	
	// SubscribeConfigChanges 订阅配置变更事件
	SubscribeConfigChanges(ctx context.Context, handler ConfigChangeHandler) error
	
	// NotifyCompanyStatusChange 通知快递公司状态变更
	NotifyCompanyStatusChange(ctx context.Context, companyCode string, enabled bool, operatorID string) error
	
	// NotifyProviderStatusChange 通知供应商状态变更
	NotifyProviderStatusChange(ctx context.Context, providerCode string, enabled bool, operatorID string) error
	
	// NotifyMappingStatusChange 通知映射关系状态变更
	NotifyMappingStatusChange(ctx context.Context, companyCode, providerCode string, enabled bool, operatorID string) error
	
	// RegisterChangeHandler 注册配置变更处理器
	RegisterChangeHandler(handler ConfigChangeHandler)
	
	// Start 启动通知器
	Start(ctx context.Context) error
	
	// Stop 停止通知器
	Stop() error
}

// ConfigChangeHandler 配置变更处理器接口
type ConfigChangeHandler interface {
	HandleConfigChange(ctx context.Context, event ConfigChangeEvent) error
}

// ConfigChangeEvent 配置变更事件
type ConfigChangeEvent struct {
	Type         ConfigChangeType `json:"type"`
	CompanyCode  string          `json:"company_code,omitempty"`
	ProviderCode string          `json:"provider_code,omitempty"`
	Enabled      bool            `json:"enabled"`
	OperatorID   string          `json:"operator_id"`
	Timestamp    time.Time       `json:"timestamp"`
	RequestID    string          `json:"request_id,omitempty"`
}

// ConfigChangeType 配置变更类型
type ConfigChangeType string

const (
	ConfigChangeTypeCompany  ConfigChangeType = "company_status"
	ConfigChangeTypeProvider ConfigChangeType = "provider_status"
	ConfigChangeTypeMapping  ConfigChangeType = "mapping_status"
)

// RedisConfigChangeNotifier Redis配置变更通知器实现
type RedisConfigChangeNotifier struct {
	redisClient *redis.Client
	logger      *zap.Logger
	
	// 订阅相关
	pubsub   *redis.PubSub
	handlers []ConfigChangeHandler
	
	// 控制相关
	stopChan chan struct{}
	wg       sync.WaitGroup
	mutex    sync.RWMutex
	
	// 配置
	channelPrefix string
	retryInterval time.Duration
	maxRetries    int
}

// NewRedisConfigChangeNotifier 创建Redis配置变更通知器
func NewRedisConfigChangeNotifier(redisClient *redis.Client, logger *zap.Logger) ConfigChangeNotifier {
	return &RedisConfigChangeNotifier{
		redisClient:   redisClient,
		logger:        logger,
		handlers:      make([]ConfigChangeHandler, 0),
		stopChan:      make(chan struct{}),
		channelPrefix: "express_config_change",
		retryInterval: 5 * time.Second,
		maxRetries:    3,
	}
}

// PublishConfigChange 发布配置变更事件
func (n *RedisConfigChangeNotifier) PublishConfigChange(ctx context.Context, event ConfigChangeEvent) error {
	// 序列化事件
	eventData, err := json.Marshal(event)
	if err != nil {
		return fmt.Errorf("序列化配置变更事件失败: %w", err)
	}
	
	// 发布到Redis
	channel := fmt.Sprintf("%s:%s", n.channelPrefix, event.Type)
	err = n.redisClient.Publish(ctx, channel, eventData).Err()
	if err != nil {
		return fmt.Errorf("发布配置变更事件失败: %w", err)
	}
	
	n.logger.Info("配置变更事件发布成功",
		zap.String("channel", channel),
		zap.String("type", string(event.Type)),
		zap.String("company_code", event.CompanyCode),
		zap.String("provider_code", event.ProviderCode),
		zap.Bool("enabled", event.Enabled),
		zap.String("operator_id", event.OperatorID))
	
	return nil
}

// SubscribeConfigChanges 订阅配置变更事件
func (n *RedisConfigChangeNotifier) SubscribeConfigChanges(ctx context.Context, handler ConfigChangeHandler) error {
	n.RegisterChangeHandler(handler)
	return nil
}

// NotifyCompanyStatusChange 通知快递公司状态变更
func (n *RedisConfigChangeNotifier) NotifyCompanyStatusChange(ctx context.Context, companyCode string, enabled bool, operatorID string) error {
	event := ConfigChangeEvent{
		Type:        ConfigChangeTypeCompany,
		CompanyCode: companyCode,
		Enabled:     enabled,
		OperatorID:  operatorID,
		Timestamp:   time.Now(),
	}
	return n.PublishConfigChange(ctx, event)
}

// NotifyProviderStatusChange 通知供应商状态变更
func (n *RedisConfigChangeNotifier) NotifyProviderStatusChange(ctx context.Context, providerCode string, enabled bool, operatorID string) error {
	event := ConfigChangeEvent{
		Type:         ConfigChangeTypeProvider,
		ProviderCode: providerCode,
		Enabled:      enabled,
		OperatorID:   operatorID,
		Timestamp:    time.Now(),
	}
	return n.PublishConfigChange(ctx, event)
}

// NotifyMappingStatusChange 通知映射关系状态变更
func (n *RedisConfigChangeNotifier) NotifyMappingStatusChange(ctx context.Context, companyCode, providerCode string, enabled bool, operatorID string) error {
	event := ConfigChangeEvent{
		Type:         ConfigChangeTypeMapping,
		CompanyCode:  companyCode,
		ProviderCode: providerCode,
		Enabled:      enabled,
		OperatorID:   operatorID,
		Timestamp:    time.Now(),
	}
	return n.PublishConfigChange(ctx, event)
}

// RegisterChangeHandler 注册配置变更处理器
func (n *RedisConfigChangeNotifier) RegisterChangeHandler(handler ConfigChangeHandler) {
	n.mutex.Lock()
	defer n.mutex.Unlock()
	
	n.handlers = append(n.handlers, handler)
	n.logger.Info("配置变更处理器注册成功", zap.Int("total_handlers", len(n.handlers)))
}

// Start 启动通知器
func (n *RedisConfigChangeNotifier) Start(ctx context.Context) error {
	// 订阅所有配置变更频道
	channels := []string{
		fmt.Sprintf("%s:%s", n.channelPrefix, ConfigChangeTypeCompany),
		fmt.Sprintf("%s:%s", n.channelPrefix, ConfigChangeTypeProvider),
		fmt.Sprintf("%s:%s", n.channelPrefix, ConfigChangeTypeMapping),
	}
	
	n.pubsub = n.redisClient.Subscribe(ctx, channels...)
	
	// 启动消息处理协程
	n.wg.Add(1)
	go n.handleMessages(ctx)
	
	n.logger.Info("配置变更通知器启动成功", zap.Strings("channels", channels))
	return nil
}

// Stop 停止通知器
func (n *RedisConfigChangeNotifier) Stop() error {
	close(n.stopChan)
	
	if n.pubsub != nil {
		err := n.pubsub.Close()
		if err != nil {
			n.logger.Error("关闭Redis订阅失败", zap.Error(err))
		}
	}
	
	n.wg.Wait()
	n.logger.Info("配置变更通知器停止成功")
	return nil
}

// handleMessages 处理订阅消息
func (n *RedisConfigChangeNotifier) handleMessages(ctx context.Context) {
	defer n.wg.Done()
	
	ch := n.pubsub.Channel()
	
	for {
		select {
		case <-ctx.Done():
			n.logger.Info("配置变更通知器收到上下文取消信号")
			return
		case <-n.stopChan:
			n.logger.Info("配置变更通知器收到停止信号")
			return
		case msg := <-ch:
			if msg == nil {
				continue
			}
			
			// 解析事件
			var event ConfigChangeEvent
			if err := json.Unmarshal([]byte(msg.Payload), &event); err != nil {
				n.logger.Error("解析配置变更事件失败",
					zap.String("payload", msg.Payload),
					zap.Error(err))
				continue
			}
			
			// 处理事件
			n.processEvent(ctx, event)
		}
	}
}

// processEvent 处理配置变更事件
func (n *RedisConfigChangeNotifier) processEvent(ctx context.Context, event ConfigChangeEvent) {
	n.mutex.RLock()
	handlers := make([]ConfigChangeHandler, len(n.handlers))
	copy(handlers, n.handlers)
	n.mutex.RUnlock()
	
	// 并发处理所有处理器
	for _, handler := range handlers {
		go func(h ConfigChangeHandler) {
			defer func() {
				if r := recover(); r != nil {
					n.logger.Error("配置变更处理器执行失败",
						zap.Any("event", event),
						zap.Any("panic", r))
				}
			}()
			
			if err := h.HandleConfigChange(ctx, event); err != nil {
				n.logger.Error("配置变更处理失败",
					zap.Any("event", event),
					zap.Error(err))
			}
		}(handler)
	}
	
	n.logger.Debug("配置变更事件处理完成",
		zap.Any("event", event),
		zap.Int("handler_count", len(handlers)))
}
