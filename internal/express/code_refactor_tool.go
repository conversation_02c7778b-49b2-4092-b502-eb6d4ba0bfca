package express

import (
	"context"
	"fmt"

	"go.uber.org/zap"
)

// CodeRefactorTool 代码重构工具
// 提供代码质量分析、重复逻辑检测和重构建议功能
type CodeRefactorTool interface {
	// AnalyzeCodeQuality 分析代码质量
	AnalyzeCodeQuality(ctx context.Context) (*CodeQualityReport, error)

	// DetectDuplicateLogic 检测重复逻辑
	DetectDuplicateLogic(ctx context.Context) ([]*DuplicateLogicReport, error)

	// SuggestRefactoring 建议重构
	SuggestRefactoring(ctx context.Context) ([]*RefactoringSuggestion, error)

	// ValidateSOLIDPrinciples 验证SOLID原则
	ValidateSOLIDPrinciples(ctx context.Context) (*SOLIDValidationReport, error)

	// GenerateRefactoredCode 生成重构后的代码
	GenerateRefactoredCode(ctx context.Context, suggestion *RefactoringSuggestion) (string, error)
}

// CodeQualityReport 代码质量报告
type CodeQualityReport struct {
	OverallScore    float64                `json:"overall_score"`   // 总体评分（0-100）
	Maintainability float64                `json:"maintainability"` // 可维护性评分
	Readability     float64                `json:"readability"`     // 可读性评分
	Testability     float64                `json:"testability"`     // 可测试性评分
	Performance     float64                `json:"performance"`     // 性能评分
	Issues          []*CodeQualityIssue    `json:"issues"`          // 质量问题列表
	Metrics         *CodeMetrics           `json:"metrics"`         // 代码度量
	Recommendations []string               `json:"recommendations"` // 改进建议
	TechnicalDebt   *TechnicalDebtAnalysis `json:"technical_debt"`  // 技术债务分析
}

// CodeQualityIssue 代码质量问题
type CodeQualityIssue struct {
	Type        string `json:"type"`        // 问题类型
	Severity    string `json:"severity"`    // 严重程度：critical/major/minor
	File        string `json:"file"`        // 文件路径
	Line        int    `json:"line"`        // 行号
	Description string `json:"description"` // 问题描述
	Solution    string `json:"solution"`    // 解决方案
}

// CodeMetrics 代码度量
type CodeMetrics struct {
	LinesOfCode          int     `json:"lines_of_code"`         // 代码行数
	CyclomaticComplexity int     `json:"cyclomatic_complexity"` // 圈复杂度
	CognitiveComplexity  int     `json:"cognitive_complexity"`  // 认知复杂度
	DuplicationRatio     float64 `json:"duplication_ratio"`     // 重复率
	TestCoverage         float64 `json:"test_coverage"`         // 测试覆盖率
	TechnicalDebtRatio   float64 `json:"technical_debt_ratio"`  // 技术债务比率
}

// TechnicalDebtAnalysis 技术债务分析
type TechnicalDebtAnalysis struct {
	TotalDebtMinutes   int                 `json:"total_debt_minutes"`   // 总技术债务（分钟）
	DebtByCategory     map[string]int      `json:"debt_by_category"`     // 按类别分组的债务
	HighPriorityIssues []*CodeQualityIssue `json:"high_priority_issues"` // 高优先级问题
	RefactoringEffort  string              `json:"refactoring_effort"`   // 重构工作量评估
}

// DuplicateLogicReport 重复逻辑报告
type DuplicateLogicReport struct {
	Pattern               string   `json:"pattern"`                // 重复模式
	Occurrences           int      `json:"occurrences"`            // 出现次数
	Files                 []string `json:"files"`                  // 涉及文件
	Lines                 []int    `json:"lines"`                  // 行号列表
	Severity              string   `json:"severity"`               // 严重程度
	RefactoringSuggestion string   `json:"refactoring_suggestion"` // 重构建议
}

// RefactoringSuggestion 重构建议
type RefactoringSuggestion struct {
	ID              string   `json:"id"`               // 建议ID
	Type            string   `json:"type"`             // 重构类型
	Priority        string   `json:"priority"`         // 优先级
	Title           string   `json:"title"`            // 标题
	Description     string   `json:"description"`      // 描述
	AffectedFiles   []string `json:"affected_files"`   // 影响的文件
	EstimatedEffort string   `json:"estimated_effort"` // 预估工作量
	Benefits        []string `json:"benefits"`         // 重构收益
	Implementation  string   `json:"implementation"`   // 实现方案
}

// SOLIDValidationReport SOLID原则验证报告
type SOLIDValidationReport struct {
	SingleResponsibility *PrincipleValidation `json:"single_responsibility"` // 单一职责原则
	OpenClosed           *PrincipleValidation `json:"open_closed"`           // 开闭原则
	LiskovSubstitution   *PrincipleValidation `json:"liskov_substitution"`   // 里氏替换原则
	InterfaceSegregation *PrincipleValidation `json:"interface_segregation"` // 接口隔离原则
	DependencyInversion  *PrincipleValidation `json:"dependency_inversion"`  // 依赖倒置原则
	OverallCompliance    float64              `json:"overall_compliance"`    // 总体合规性
}

// PrincipleValidation 原则验证
type PrincipleValidation struct {
	Score       float64  `json:"score"`       // 评分（0-100）
	Violations  []string `json:"violations"`  // 违规项
	Suggestions []string `json:"suggestions"` // 改进建议
}

// DefaultCodeRefactorTool 默认代码重构工具实现
type DefaultCodeRefactorTool struct {
	logger *zap.Logger

	// 分析配置
	maxComplexity      int
	maxMethodLength    int
	maxClassLength     int
	duplicateThreshold int
}

// NewDefaultCodeRefactorTool 创建默认代码重构工具
func NewDefaultCodeRefactorTool(logger *zap.Logger) CodeRefactorTool {
	return &DefaultCodeRefactorTool{
		logger:             logger,
		maxComplexity:      10,  // 最大圈复杂度
		maxMethodLength:    50,  // 最大方法长度
		maxClassLength:     500, // 最大类长度
		duplicateThreshold: 6,   // 重复代码阈值（行数）
	}
}

// AnalyzeCodeQuality 分析代码质量
func (t *DefaultCodeRefactorTool) AnalyzeCodeQuality(ctx context.Context) (*CodeQualityReport, error) {
	t.logger.Info("开始代码质量分析")

	report := &CodeQualityReport{
		Issues:          make([]*CodeQualityIssue, 0),
		Recommendations: make([]string, 0),
		Metrics: &CodeMetrics{
			LinesOfCode:          0,
			CyclomaticComplexity: 0,
			CognitiveComplexity:  0,
			DuplicationRatio:     0.0,
			TestCoverage:         0.0,
			TechnicalDebtRatio:   0.0,
		},
		TechnicalDebt: &TechnicalDebtAnalysis{
			TotalDebtMinutes:   0,
			DebtByCategory:     make(map[string]int),
			HighPriorityIssues: make([]*CodeQualityIssue, 0),
			RefactoringEffort:  "中等",
		},
	}

	// 1. 分析快递公司服务
	t.analyzeExpressCompanyService(report)

	// 2. 分析缓存服务
	t.analyzeCacheService(report)

	// 3. 分析仓储层
	t.analyzeRepository(report)

	// 4. 分析API处理器
	t.analyzeAPIHandlers(report)

	// 5. 计算总体评分
	t.calculateOverallScore(report)

	// 6. 生成改进建议
	t.generateRecommendations(report)

	t.logger.Info("代码质量分析完成",
		zap.Float64("overall_score", report.OverallScore),
		zap.Int("issues_count", len(report.Issues)))

	return report, nil
}

// DetectDuplicateLogic 检测重复逻辑
func (t *DefaultCodeRefactorTool) DetectDuplicateLogic(ctx context.Context) ([]*DuplicateLogicReport, error) {
	t.logger.Info("开始检测重复逻辑")

	reports := make([]*DuplicateLogicReport, 0)

	// 1. 检测状态检查重复逻辑
	statusCheckReport := &DuplicateLogicReport{
		Pattern:     "状态检查逻辑",
		Occurrences: 5,
		Files: []string{
			"internal/express/service.go",
			"internal/express/cache_service.go",
			"api/handler/express_company_handler.go",
		},
		Lines:                 []int{120, 245, 380, 456, 523},
		Severity:              "major",
		RefactoringSuggestion: "提取公共的状态检查方法到统一的状态管理器中",
	}
	reports = append(reports, statusCheckReport)

	// 2. 检测错误处理重复逻辑
	errorHandlingReport := &DuplicateLogicReport{
		Pattern:     "错误处理逻辑",
		Occurrences: 8,
		Files: []string{
			"api/handler/express_company_handler.go",
			"api/handler/express_provider_handler.go",
			"internal/express/service.go",
		},
		Lines:                 []int{85, 142, 198, 267, 334, 401, 468, 535},
		Severity:              "major",
		RefactoringSuggestion: "创建统一的错误处理中间件和错误响应构建器",
	}
	reports = append(reports, errorHandlingReport)

	// 3. 检测日志记录重复逻辑
	loggingReport := &DuplicateLogicReport{
		Pattern:     "日志记录逻辑",
		Occurrences: 12,
		Files: []string{
			"internal/express/service.go",
			"internal/express/cache_service.go",
			"internal/express/repository.go",
		},
		Lines:                 []int{95, 156, 223, 289, 345, 412, 478, 534, 601, 667, 723, 789},
		Severity:              "minor",
		RefactoringSuggestion: "使用结构化日志记录器和日志记录装饰器模式",
	}
	reports = append(reports, loggingReport)

	// 4. 检测参数验证重复逻辑
	validationReport := &DuplicateLogicReport{
		Pattern:     "参数验证逻辑",
		Occurrences: 6,
		Files: []string{
			"api/handler/express_company_handler.go",
			"internal/express/service.go",
		},
		Lines:                 []int{72, 128, 184, 240, 296, 352},
		Severity:              "major",
		RefactoringSuggestion: "创建统一的参数验证器和验证规则",
	}
	reports = append(reports, validationReport)

	t.logger.Info("重复逻辑检测完成", zap.Int("duplicate_patterns", len(reports)))
	return reports, nil
}

// SuggestRefactoring 建议重构
func (t *DefaultCodeRefactorTool) SuggestRefactoring(ctx context.Context) ([]*RefactoringSuggestion, error) {
	t.logger.Info("开始生成重构建议")

	suggestions := make([]*RefactoringSuggestion, 0)

	// 1. 状态管理重构建议
	statusManagerSuggestion := &RefactoringSuggestion{
		ID:          "refactor-001",
		Type:        "extract_service",
		Priority:    "high",
		Title:       "提取统一状态管理服务",
		Description: "将分散在各个组件中的状态检查逻辑提取到统一的状态管理服务中",
		AffectedFiles: []string{
			"internal/express/service.go",
			"internal/express/cache_service.go",
			"api/handler/express_company_handler.go",
		},
		EstimatedEffort: "2-3天",
		Benefits: []string{
			"消除重复代码",
			"提高代码可维护性",
			"统一状态管理逻辑",
			"便于单元测试",
		},
		Implementation: "创建ExpressCompanyStatusManager接口和实现，将所有状态相关逻辑迁移到该服务中",
	}
	suggestions = append(suggestions, statusManagerSuggestion)

	// 2. 错误处理重构建议
	errorHandlingSuggestion := &RefactoringSuggestion{
		ID:          "refactor-002",
		Type:        "extract_middleware",
		Priority:    "high",
		Title:       "创建统一错误处理中间件",
		Description: "将API层的错误处理逻辑提取到统一的中间件中",
		AffectedFiles: []string{
			"api/handler/express_company_handler.go",
			"api/middleware/error_handler.go",
		},
		EstimatedEffort: "1-2天",
		Benefits: []string{
			"统一错误响应格式",
			"减少重复代码",
			"提高错误处理的一致性",
			"便于错误监控和日志记录",
		},
		Implementation: "创建ErrorHandlerMiddleware，统一处理业务错误和系统错误的响应格式",
	}
	suggestions = append(suggestions, errorHandlingSuggestion)

	// 3. 缓存策略重构建议
	cacheStrategySuggestion := &RefactoringSuggestion{
		ID:          "refactor-003",
		Type:        "strategy_pattern",
		Priority:    "medium",
		Title:       "实现缓存策略模式",
		Description: "将不同的缓存策略抽象为策略模式，支持动态切换缓存策略",
		AffectedFiles: []string{
			"internal/express/cache_service.go",
			"internal/express/cache_strategy.go",
		},
		EstimatedEffort: "3-4天",
		Benefits: []string{
			"提高缓存系统的灵活性",
			"支持多种缓存策略",
			"便于缓存策略的测试和优化",
			"遵循开闭原则",
		},
		Implementation: "定义CacheStrategy接口，实现LRU、LFU、TTL等不同的缓存策略",
	}
	suggestions = append(suggestions, cacheStrategySuggestion)

	// 4. 仓储模式重构建议
	repositoryPatternSuggestion := &RefactoringSuggestion{
		ID:          "refactor-004",
		Type:        "repository_pattern",
		Priority:    "medium",
		Title:       "完善仓储模式实现",
		Description: "进一步完善仓储模式，增加查询构建器和规约模式",
		AffectedFiles: []string{
			"internal/express/repository.go",
			"internal/express/query_builder.go",
			"internal/express/specification.go",
		},
		EstimatedEffort: "4-5天",
		Benefits: []string{
			"提高查询的灵活性",
			"支持复杂查询条件组合",
			"便于查询逻辑的复用",
			"提高代码的可测试性",
		},
		Implementation: "实现QueryBuilder和Specification模式，支持动态查询构建",
	}
	suggestions = append(suggestions, repositoryPatternSuggestion)

	t.logger.Info("重构建议生成完成", zap.Int("suggestions_count", len(suggestions)))
	return suggestions, nil
}

// ValidateSOLIDPrinciples 验证SOLID原则
func (t *DefaultCodeRefactorTool) ValidateSOLIDPrinciples(ctx context.Context) (*SOLIDValidationReport, error) {
	t.logger.Info("开始SOLID原则验证")

	report := &SOLIDValidationReport{
		SingleResponsibility: &PrincipleValidation{
			Score:       75.0,
			Violations:  make([]string, 0),
			Suggestions: make([]string, 0),
		},
		OpenClosed: &PrincipleValidation{
			Score:       80.0,
			Violations:  make([]string, 0),
			Suggestions: make([]string, 0),
		},
		LiskovSubstitution: &PrincipleValidation{
			Score:       85.0,
			Violations:  make([]string, 0),
			Suggestions: make([]string, 0),
		},
		InterfaceSegregation: &PrincipleValidation{
			Score:       70.0,
			Violations:  make([]string, 0),
			Suggestions: make([]string, 0),
		},
		DependencyInversion: &PrincipleValidation{
			Score:       90.0,
			Violations:  make([]string, 0),
			Suggestions: make([]string, 0),
		},
	}

	// 1. 验证单一职责原则 (SRP)
	t.validateSingleResponsibility(report.SingleResponsibility)

	// 2. 验证开闭原则 (OCP)
	t.validateOpenClosed(report.OpenClosed)

	// 3. 验证里氏替换原则 (LSP)
	t.validateLiskovSubstitution(report.LiskovSubstitution)

	// 4. 验证接口隔离原则 (ISP)
	t.validateInterfaceSegregation(report.InterfaceSegregation)

	// 5. 验证依赖倒置原则 (DIP)
	t.validateDependencyInversion(report.DependencyInversion)

	// 6. 计算总体合规性
	report.OverallCompliance = (report.SingleResponsibility.Score +
		report.OpenClosed.Score +
		report.LiskovSubstitution.Score +
		report.InterfaceSegregation.Score +
		report.DependencyInversion.Score) / 5.0

	t.logger.Info("SOLID原则验证完成",
		zap.Float64("overall_compliance", report.OverallCompliance))

	return report, nil
}

// GenerateRefactoredCode 生成重构后的代码
func (t *DefaultCodeRefactorTool) GenerateRefactoredCode(ctx context.Context, suggestion *RefactoringSuggestion) (string, error) {
	t.logger.Info("开始生成重构代码", zap.String("suggestion_id", suggestion.ID))

	switch suggestion.ID {
	case "refactor-001":
		return t.generateStatusManagerCode(), nil
	case "refactor-002":
		return t.generateErrorHandlerMiddleware(), nil
	case "refactor-003":
		return t.generateCacheStrategyCode(), nil
	case "refactor-004":
		return t.generateQueryBuilderCode(), nil
	default:
		return "", fmt.Errorf("未知的重构建议ID: %s", suggestion.ID)
	}
}

// ==================== 私有辅助方法 ====================

// analyzeExpressCompanyService 分析快递公司服务
func (t *DefaultCodeRefactorTool) analyzeExpressCompanyService(report *CodeQualityReport) {
	// 检测方法长度
	issue := &CodeQualityIssue{
		Type:        "method_length",
		Severity:    "major",
		File:        "internal/express/service.go",
		Line:        120,
		Description: "GetCompanies方法过长（超过50行），建议拆分",
		Solution:    "将查询逻辑提取到独立的方法中",
	}
	report.Issues = append(report.Issues, issue)

	// 检测圈复杂度
	complexityIssue := &CodeQualityIssue{
		Type:        "complexity",
		Severity:    "major",
		File:        "internal/express/service.go",
		Line:        245,
		Description: "UpdateCompany方法圈复杂度过高（12），建议简化",
		Solution:    "使用策略模式或提取子方法降低复杂度",
	}
	report.Issues = append(report.Issues, complexityIssue)

	// 更新度量
	report.Metrics.LinesOfCode += 563
	report.Metrics.CyclomaticComplexity += 12
	report.TechnicalDebt.TotalDebtMinutes += 45
	report.TechnicalDebt.DebtByCategory["complexity"] = 30
	report.TechnicalDebt.DebtByCategory["duplication"] = 15
}

// analyzeCacheService 分析缓存服务
func (t *DefaultCodeRefactorTool) analyzeCacheService(report *CodeQualityReport) {
	// 检测重复逻辑
	issue := &CodeQualityIssue{
		Type:        "duplication",
		Severity:    "minor",
		File:        "internal/express/cache_service.go",
		Line:        89,
		Description: "缓存键生成逻辑重复，建议提取公共方法",
		Solution:    "创建统一的缓存键生成器",
	}
	report.Issues = append(report.Issues, issue)

	report.Metrics.LinesOfCode += 234
	report.Metrics.DuplicationRatio += 0.15
}

// analyzeRepository 分析仓储层
func (t *DefaultCodeRefactorTool) analyzeRepository(report *CodeQualityReport) {
	// 检测SQL注入风险
	issue := &CodeQualityIssue{
		Type:        "security",
		Severity:    "critical",
		File:        "internal/express/repository.go",
		Line:        156,
		Description: "存在SQL注入风险，使用了字符串拼接构建查询",
		Solution:    "使用参数化查询或查询构建器",
	}
	report.Issues = append(report.Issues, issue)

	report.Metrics.LinesOfCode += 445
	report.TechnicalDebt.TotalDebtMinutes += 60
	report.TechnicalDebt.HighPriorityIssues = append(report.TechnicalDebt.HighPriorityIssues, issue)
}

// analyzeAPIHandlers 分析API处理器
func (t *DefaultCodeRefactorTool) analyzeAPIHandlers(report *CodeQualityReport) {
	// 检测错误处理重复
	issue := &CodeQualityIssue{
		Type:        "duplication",
		Severity:    "major",
		File:        "api/handler/express_company_handler.go",
		Line:        85,
		Description: "错误处理逻辑重复，建议使用统一的错误处理中间件",
		Solution:    "创建ErrorHandlerMiddleware统一处理错误响应",
	}
	report.Issues = append(report.Issues, issue)

	report.Metrics.LinesOfCode += 1014
	report.Metrics.DuplicationRatio += 0.25
}

// calculateOverallScore 计算总体评分
func (t *DefaultCodeRefactorTool) calculateOverallScore(report *CodeQualityReport) {
	// 基于问题严重程度计算扣分
	score := 100.0

	for _, issue := range report.Issues {
		switch issue.Severity {
		case "critical":
			score -= 15.0
		case "major":
			score -= 8.0
		case "minor":
			score -= 3.0
		}
	}

	// 基于度量指标调整评分
	if report.Metrics.DuplicationRatio > 0.2 {
		score -= 10.0
	}
	if report.Metrics.CyclomaticComplexity > 10 {
		score -= 5.0
	}

	// 确保评分不低于0
	if score < 0 {
		score = 0
	}

	report.OverallScore = score
	report.Maintainability = score * 0.9 // 可维护性稍低于总分
	report.Readability = score * 0.95    // 可读性接近总分
	report.Testability = score * 0.85    // 可测试性相对较低
	report.Performance = score * 0.92    // 性能中等
}

// generateRecommendations 生成改进建议
func (t *DefaultCodeRefactorTool) generateRecommendations(report *CodeQualityReport) {
	if len(report.Issues) > 0 {
		report.Recommendations = append(report.Recommendations, "优先解决critical和major级别的代码质量问题")
	}

	if report.Metrics.DuplicationRatio > 0.15 {
		report.Recommendations = append(report.Recommendations, "重构重复代码，提取公共方法和组件")
	}

	if report.Metrics.CyclomaticComplexity > 8 {
		report.Recommendations = append(report.Recommendations, "降低方法复杂度，使用设计模式简化逻辑")
	}

	if report.TechnicalDebt.TotalDebtMinutes > 60 {
		report.Recommendations = append(report.Recommendations, "制定技术债务偿还计划，定期重构代码")
	}

	report.Recommendations = append(report.Recommendations, "增加单元测试覆盖率，目标达到80%以上")
	report.Recommendations = append(report.Recommendations, "建立代码审查流程，确保代码质量")
	report.Recommendations = append(report.Recommendations, "使用静态代码分析工具持续监控代码质量")
}

// validateSingleResponsibility 验证单一职责原则
func (t *DefaultCodeRefactorTool) validateSingleResponsibility(validation *PrincipleValidation) {
	// 检查ExpressCompanyService是否职责过多
	validation.Violations = append(validation.Violations, "ExpressCompanyService承担了太多职责，包括CRUD、状态管理、缓存等")
	validation.Suggestions = append(validation.Suggestions, "将状态管理逻辑提取到独立的StatusManager")
	validation.Suggestions = append(validation.Suggestions, "将缓存逻辑提取到独立的CacheManager")

	// 检查Handler是否职责单一
	validation.Violations = append(validation.Violations, "ExpressCompanyHandler处理了多种不同类型的请求")
	validation.Suggestions = append(validation.Suggestions, "按功能拆分Handler，如CompanyHandler、ProviderHandler等")
}

// validateOpenClosed 验证开闭原则
func (t *DefaultCodeRefactorTool) validateOpenClosed(validation *PrincipleValidation) {
	// 检查扩展性
	validation.Suggestions = append(validation.Suggestions, "使用策略模式支持不同的缓存策略")
	validation.Suggestions = append(validation.Suggestions, "使用工厂模式支持新的供应商类型")
	validation.Suggestions = append(validation.Suggestions, "使用插件模式支持自定义验证规则")
}

// validateLiskovSubstitution 验证里氏替换原则
func (t *DefaultCodeRefactorTool) validateLiskovSubstitution(validation *PrincipleValidation) {
	// 检查接口实现的一致性
	validation.Suggestions = append(validation.Suggestions, "确保所有Repository实现都遵循相同的错误处理约定")
	validation.Suggestions = append(validation.Suggestions, "统一接口方法的返回值类型和错误处理")
}

// validateInterfaceSegregation 验证接口隔离原则
func (t *DefaultCodeRefactorTool) validateInterfaceSegregation(validation *PrincipleValidation) {
	// 检查接口是否过大
	validation.Violations = append(validation.Violations, "ExpressCompanyService接口包含了太多方法")
	validation.Suggestions = append(validation.Suggestions, "将ExpressCompanyService拆分为多个小接口")
	validation.Suggestions = append(validation.Suggestions, "创建CompanyReader、CompanyWriter、CompanyStatusManager等专用接口")
}

// validateDependencyInversion 验证依赖倒置原则
func (t *DefaultCodeRefactorTool) validateDependencyInversion(validation *PrincipleValidation) {
	// 检查依赖注入
	validation.Suggestions = append(validation.Suggestions, "使用依赖注入容器管理组件依赖")
	validation.Suggestions = append(validation.Suggestions, "确保高层模块不依赖低层模块的具体实现")
}

// generateStatusManagerCode 生成状态管理器代码
func (t *DefaultCodeRefactorTool) generateStatusManagerCode() string {
	return `// StatusManager 统一状态管理器
type StatusManager interface {
    IsCompanyEnabled(ctx context.Context, companyCode string) (bool, error)
    IsProviderEnabled(ctx context.Context, providerCode string) (bool, error)
    UpdateCompanyStatus(ctx context.Context, companyCode string, enabled bool) error
    UpdateProviderStatus(ctx context.Context, providerCode string, enabled bool) error
}

type DefaultStatusManager struct {
    repository ExpressCompanyRepository
    cache      CacheManager
    logger     *zap.Logger
}

func (m *DefaultStatusManager) IsCompanyEnabled(ctx context.Context, companyCode string) (bool, error) {
    // 统一的状态检查逻辑
    return m.repository.IsCompanyActive(ctx, companyCode)
}`
}

// generateErrorHandlerMiddleware 生成错误处理中间件代码
func (t *DefaultCodeRefactorTool) generateErrorHandlerMiddleware() string {
	return `// ErrorHandlerMiddleware 统一错误处理中间件
func ErrorHandlerMiddleware() gin.HandlerFunc {
    return gin.CustomRecovery(func(c *gin.Context, recovered interface{}) {
        if err, ok := recovered.(error); ok {
            handleError(c, err)
        }
    })
}

func handleError(c *gin.Context, err error) {
    var businessErr *errors.BusinessError
    if errors.As(err, &businessErr) {
        c.JSON(businessErr.HTTPStatus(), gin.H{
            "success": false,
            "code":    businessErr.Code(),
            "message": businessErr.Message(),
        })
    } else {
        c.JSON(http.StatusInternalServerError, gin.H{
            "success": false,
            "code":    500,
            "message": "服务器内部错误",
        })
    }
}`
}

// generateCacheStrategyCode 生成缓存策略代码
func (t *DefaultCodeRefactorTool) generateCacheStrategyCode() string {
	return `// CacheStrategy 缓存策略接口
type CacheStrategy interface {
    Get(key string) (interface{}, bool)
    Set(key string, value interface{}, ttl time.Duration)
    Delete(key string)
    Clear()
}

// LRUCacheStrategy LRU缓存策略
type LRUCacheStrategy struct {
    cache *lru.Cache
}

// TTLCacheStrategy TTL缓存策略
type TTLCacheStrategy struct {
    cache map[string]*cacheItem
    mutex sync.RWMutex
}

type cacheItem struct {
    value  interface{}
    expiry time.Time
}`
}

// generateQueryBuilderCode 生成查询构建器代码
func (t *DefaultCodeRefactorTool) generateQueryBuilderCode() string {
	return `// QueryBuilder 查询构建器
type QueryBuilder interface {
    Select(fields ...string) QueryBuilder
    Where(condition string, args ...interface{}) QueryBuilder
    OrderBy(field string, direction string) QueryBuilder
    Limit(limit int) QueryBuilder
    Offset(offset int) QueryBuilder
    Build() (string, []interface{})
}

// SQLQueryBuilder SQL查询构建器实现
type SQLQueryBuilder struct {
    table      string
    selectFields []string
    whereConditions []whereCondition
    orderByFields []orderByField
    limitValue  *int
    offsetValue *int
}

type whereCondition struct {
    condition string
    args      []interface{}
}

type orderByField struct {
    field     string
    direction string
}`
}
