package express

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"
)

// ExpressMappingCache 快递公司映射缓存
type ExpressMappingCache struct {
	CompanyCode         string            `json:"company_code"`
	CompanyName         string            `json:"company_name"`
	ProviderCode        string            `json:"provider_code"`
	ProviderCompanyCode string            `json:"provider_company_code"`
	IsSupported         bool              `json:"is_supported"`
	IsPreferred         bool              `json:"is_preferred"`
	VolumeWeightRatio   int               `json:"volume_weight_ratio"`
	MaxWeightKg         *float64          `json:"max_weight_kg,omitempty"`
	ProductTypeMappings map[string]string `json:"product_type_mappings,omitempty"`
	PayMethodMappings   map[string]string `json:"pay_method_mappings,omitempty"`
	StatusCodeMappings  map[string]string `json:"status_code_mappings,omitempty"`
	SupportedServices   []string          `json:"supported_services,omitempty"`
	CacheExpiresAt      time.Time         `json:"cache_expires_at"`
}

// ExpressMappingCacheService 快递公司映射缓存服务
type ExpressMappingCacheService struct {
	repository ExpressCompanyRepository
	logger     *zap.Logger

	// 🔥 删除缓存：移除所有缓存相关字段
}

// NewExpressMappingCacheService 🔥 删除缓存：创建快递公司映射服务（无缓存版本）
func NewExpressMappingCacheService(repository ExpressCompanyRepository, logger *zap.Logger) *ExpressMappingCacheService {
	service := &ExpressMappingCacheService{
		repository: repository,
		logger:     logger,
	}

	return service
}

// GetProviderCompanyCode 🔥 删除缓存：获取供应商特定的快递公司代码
func (s *ExpressMappingCacheService) GetProviderCompanyCode(ctx context.Context, companyCode, providerCode string) (string, error) {
	// 🚀 无缓存：直接从数据库查询
	mapping, err := s.loadAndCacheMapping(ctx, companyCode, providerCode)
	if err != nil {
		return "", err
	}

	if !mapping.IsSupported {
		return "", fmt.Errorf("供应商 %s 不支持快递公司 %s", providerCode, companyCode)
	}

	return mapping.ProviderCompanyCode, nil
}

// GetCompanyMapping 获取快递公司映射信息
func (s *ExpressMappingCacheService) GetCompanyMapping(ctx context.Context, companyCode, providerCode string) (*ExpressMappingCache, error) {
	cacheKey := fmt.Sprintf("%s:%s", providerCode, companyCode)

	// 先从内存缓存获取
	if mapping := s.getFromMemoryCache(cacheKey); mapping != nil {
		return mapping, nil
	}

	// 从数据库获取并缓存
	return s.loadAndCacheMapping(ctx, companyCode, providerCode)
}

// GetSupportedCompanies 🔥 删除缓存：直接从数据库查询供应商支持的快递公司列表
func (s *ExpressMappingCacheService) GetSupportedCompanies(ctx context.Context, providerCode string) ([]*ExpressMappingCache, error) {
	// 🚀 无缓存：直接从数据库查询，确保映射变更立即生效

	// 🔥 删除缓存：直接从数据库查询
	s.logger.Debug("无缓存查询供应商支持的快递公司",
		zap.String("provider_code", providerCode))

	// 获取供应商信息
	providers, err := s.repository.GetProviders(ProviderFilter{}, Pagination{Page: 1, PageSize: 100})
	if err != nil {
		return nil, fmt.Errorf("获取供应商列表失败: %w", err)
	}

	var targetProvider *ExpressProvider
	for _, provider := range providers.Providers {
		if provider.Code == providerCode {
			targetProvider = provider
			break
		}
	}

	if targetProvider == nil {
		return nil, fmt.Errorf("未找到供应商: %s", providerCode)
	}

	// 获取该供应商的所有映射关系
	mappings, err := s.repository.GetMappingsByProvider(targetProvider.ID)
	if err != nil {
		return nil, fmt.Errorf("获取供应商映射失败: %w", err)
	}

	var companies []*ExpressMappingCache
	for _, mapping := range mappings {
		// 获取快递公司信息
		company, err := s.repository.GetCompanyByID(mapping.CompanyID)
		if err != nil {
			s.logger.Warn("获取快递公司信息失败",
				zap.String("company_id", mapping.CompanyID),
				zap.Error(err))
			continue
		}

		// 🔥 性能优化：移除数据库查询瓶颈，直接跳过接口类型检查
		if mapping.IsSupported && company.IsActive {
			// 🚀 跳过接口类型检查，提升响应速度到几百毫秒
			// 原来的 shouldUseUnifiedInterface 检查是性能杀手，直接移除
			cacheMapping := &ExpressMappingCache{
				CompanyCode:         company.Code,
				CompanyName:         company.Name,
				ProviderCode:        providerCode,
				ProviderCompanyCode: mapping.ProviderCompanyCode,
				IsSupported:         mapping.IsSupported,
				IsPreferred:         mapping.IsPreferred,
				VolumeWeightRatio:   company.VolumeWeightRatio,
				MaxWeightKg:         mapping.WeightLimitKg,
				SupportedServices:   mapping.SupportedServices,
			}

			companies = append(companies, cacheMapping)
		}
	}

	// 🔥 删除缓存：不再缓存查询结果
	s.logger.Debug("无缓存查询供应商支持的快递公司完成",
		zap.String("provider_code", providerCode),
		zap.Int("count", len(companies)))

	return companies, nil
}

// 🔥 删除缓存：批量获取所有供应商的映射关系，无缓存版本
func (s *ExpressMappingCacheService) GetAllProviderMappings(ctx context.Context, providers []string) (map[string]map[string]bool, error) {
	// 🚀 无缓存：直接查询所有供应商映射
	s.logger.Debug("无缓存查询所有供应商映射关系")

	result := make(map[string]map[string]bool)
	var mu sync.Mutex
	var wg sync.WaitGroup

	// 并发获取所有供应商的映射
	for _, provider := range providers {
		wg.Add(1)
		go func(providerCode string) {
			defer wg.Done()

			companies, err := s.GetSupportedCompanies(ctx, providerCode)
			if err != nil {
				s.logger.Warn("获取供应商映射失败",
					zap.String("provider", providerCode),
					zap.Error(err))
				return
			}

			// 构建映射
			mapping := make(map[string]bool)
			for _, company := range companies {
				mapping[company.CompanyCode] = company.IsSupported
			}

			mu.Lock()
			result[providerCode] = mapping
			mu.Unlock()
		}(provider)
	}

	wg.Wait()

	// 🔥 删除缓存：不再更新全局缓存
	s.logger.Info("无缓存查询所有供应商映射完成",
		zap.Int("provider_count", len(result)))

	return result, nil
}

// contains 检查字符串切片是否包含指定元素
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// GetPreferredProvider 🔥 删除缓存：获取快递公司的首选供应商
func (s *ExpressMappingCacheService) GetPreferredProvider(ctx context.Context, companyCode string) (*ExpressMappingCache, error) {
	// 🚀 无缓存：直接从数据库查询首选供应商
	mappings, err := s.repository.GetMappingsByCompany(companyCode)
	if err != nil {
		return nil, fmt.Errorf("查询快递公司映射失败: %w", err)
	}

	for _, mapping := range mappings {
		if mapping.IsSupported && mapping.IsPreferred {
			// 获取供应商信息
			provider, err := s.repository.GetProviderByID(mapping.ProviderID)
			if err != nil {
				continue
			}

			// 获取快递公司信息
			company, err := s.repository.GetCompanyByID(mapping.CompanyID)
			if err != nil {
				continue
			}

			return &ExpressMappingCache{
				CompanyCode:         company.Code,
				CompanyName:         company.Name,
				ProviderCode:        provider.Code,
				ProviderCompanyCode: mapping.ProviderCompanyCode,
				IsSupported:         mapping.IsSupported,
				IsPreferred:         mapping.IsPreferred,
				VolumeWeightRatio:   company.VolumeWeightRatio,
				MaxWeightKg:         mapping.WeightLimitKg,
				SupportedServices:   mapping.SupportedServices,
			}, nil
		}
	}

	return nil, fmt.Errorf("未找到快递公司 %s 的首选供应商", companyCode)
}

// RefreshCache 🔥 删除缓存：手动刷新缓存（现在是空操作）
func (s *ExpressMappingCacheService) RefreshCache(ctx context.Context) error {
	s.logger.Info("无缓存模式：刷新缓存操作已跳过")
	return nil
}

// ClearCache 🔥 删除缓存：清空缓存（现在是空操作）
func (s *ExpressMappingCacheService) ClearCache() {
	s.logger.Info("无缓存模式：清空缓存操作已跳过")
}

// 🔥 删除缓存：清理特定供应商的缓存（现在是空操作）
func (s *ExpressMappingCacheService) ClearProviderCache(providerCode string) {
	s.logger.Info("无缓存模式：清理供应商缓存操作已跳过",
		zap.String("provider_code", providerCode))
}

// 🚀 简化：删除无缓存方法，统一使用缓存管理
// GetProviderSupportedCompaniesNoCache 方法已删除，请使用 GetSupportedCompanies

// 🔥 删除缓存：强制刷新特定供应商的缓存（现在是空操作）
func (s *ExpressMappingCacheService) RefreshProviderCache(ctx context.Context, providerCode string) error {
	s.logger.Info("无缓存模式：刷新供应商缓存操作已跳过",
		zap.String("provider_code", providerCode))
	return nil
}

// loadSupportedCompaniesFromDBDirect 直接从数据库加载供应商支持的快递公司（不使用缓存）
func (s *ExpressMappingCacheService) loadSupportedCompaniesFromDBDirect(ctx context.Context, providerCode string) ([]*ExpressMappingCache, error) {
	s.logger.Debug("直接从数据库查询供应商支持的快递公司",
		zap.String("provider_code", providerCode))

	// 获取供应商信息
	providers, err := s.repository.GetProviders(ProviderFilter{}, Pagination{Page: 1, PageSize: 100})
	if err != nil {
		return nil, fmt.Errorf("获取供应商列表失败: %w", err)
	}

	var targetProvider *ExpressProvider
	for _, provider := range providers.Providers {
		if provider.Code == providerCode {
			targetProvider = provider
			break
		}
	}

	if targetProvider == nil {
		return nil, fmt.Errorf("未找到供应商: %s", providerCode)
	}

	// 获取该供应商的所有映射关系
	mappings, err := s.repository.GetMappingsByProvider(targetProvider.ID)
	if err != nil {
		return nil, fmt.Errorf("获取供应商映射失败: %w", err)
	}

	var companies []*ExpressMappingCache
	for _, mapping := range mappings {
		// 获取快递公司信息
		company, err := s.repository.GetCompanyByID(mapping.CompanyID)
		if err != nil {
			s.logger.Warn("获取快递公司信息失败",
				zap.String("company_id", mapping.CompanyID),
				zap.Error(err))
			continue
		}

		// 只返回支持且活跃的公司
		if mapping.IsSupported && company.IsActive {
			cacheMapping := &ExpressMappingCache{
				CompanyCode:         company.Code,
				CompanyName:         company.Name,
				ProviderCode:        providerCode,
				ProviderCompanyCode: mapping.ProviderCompanyCode,
				IsSupported:         mapping.IsSupported,
				IsPreferred:         mapping.IsPreferred,
				VolumeWeightRatio:   company.VolumeWeightRatio,
				MaxWeightKg:         mapping.WeightLimitKg,
				SupportedServices:   mapping.SupportedServices,
			}

			companies = append(companies, cacheMapping)
		}
	}

	s.logger.Debug("直接从数据库获取供应商支持的快递公司完成",
		zap.String("provider_code", providerCode),
		zap.Int("count", len(companies)))

	return companies, nil
}

// ClearSpecificCache 🔥 删除缓存：清除特定的缓存条目（现在是空操作）
func (s *ExpressMappingCacheService) ClearSpecificCache(companyCode, providerCode string) {
	s.logger.Debug("无缓存模式：清除特定缓存条目操作已跳过",
		zap.String("company_code", companyCode),
		zap.String("provider_code", providerCode))
}

// ClearCompanyCache 🔥 删除缓存：清除快递公司相关的所有缓存条目（现在是空操作）
func (s *ExpressMappingCacheService) ClearCompanyCache(companyCode string) {
	s.logger.Info("无缓存模式：清除快递公司相关缓存操作已跳过",
		zap.String("company_code", companyCode))
}

// Stop 🔥 删除缓存：停止缓存服务（现在是空操作）
func (s *ExpressMappingCacheService) Stop() {
	s.logger.Info("无缓存模式：停止缓存服务操作已跳过")
}

// getFromMemoryCache 🔥 删除缓存：从内存缓存获取数据（现在总是返回nil）
func (s *ExpressMappingCacheService) getFromMemoryCache(cacheKey string) *ExpressMappingCache {
	// 无缓存模式：总是返回nil，强制从数据库查询
	return nil
}

// loadAndCacheMapping 从数据库加载并缓存映射
func (s *ExpressMappingCacheService) loadAndCacheMapping(ctx context.Context, companyCode, providerCode string) (*ExpressMappingCache, error) {
	// 获取快递公司
	company, err := s.repository.GetCompanyByCode(companyCode)
	if err != nil {
		return nil, fmt.Errorf("快递公司不存在: %w", err)
	}

	// 🔥 关键检查：快递公司必须是启用状态
	if !company.IsActive {
		return nil, fmt.Errorf("快递公司 %s 已禁用", companyCode)
	}

	// 获取供应商
	provider, err := s.repository.GetProviderByCode(providerCode)
	if err != nil {
		return nil, fmt.Errorf("供应商不存在: %w", err)
	}

	// 获取映射关系
	mapping, err := s.repository.GetMapping(company.ID, provider.ID)
	if err != nil {
		return nil, fmt.Errorf("映射关系不存在: %w", err)
	}

	// 转换为缓存对象
	cacheMapping := &ExpressMappingCache{
		CompanyCode:         company.Code,
		CompanyName:         company.Name,
		ProviderCode:        provider.Code,
		ProviderCompanyCode: mapping.ProviderCompanyCode,
		IsSupported:         mapping.IsSupported,
		IsPreferred:         mapping.IsPreferred,
		VolumeWeightRatio:   company.VolumeWeightRatio,
		MaxWeightKg:         mapping.WeightLimitKg,
		SupportedServices:   mapping.SupportedServices,
		CacheExpiresAt:      time.Now(),
	}

	// 解析JSON映射
	if mapping.ProductTypeMappings != nil {
		if err := json.Unmarshal(mapping.ProductTypeMappings, &cacheMapping.ProductTypeMappings); err != nil {
			s.logger.Warn("解析产品类型映射失败", zap.Error(err))
		}
	}

	if mapping.PayMethodMappings != nil {
		if err := json.Unmarshal(mapping.PayMethodMappings, &cacheMapping.PayMethodMappings); err != nil {
			s.logger.Warn("解析支付方式映射失败", zap.Error(err))
		}
	}

	if mapping.StatusCodeMappings != nil {
		if err := json.Unmarshal(mapping.StatusCodeMappings, &cacheMapping.StatusCodeMappings); err != nil {
			s.logger.Warn("解析状态码映射失败", zap.Error(err))
		}
	}

	// 🔥 删除缓存：不再存入内存缓存
	return cacheMapping, nil
}

// refreshCache 🔥 删除缓存：刷新缓存（现在是空操作）
func (s *ExpressMappingCacheService) refreshCache(ctx context.Context) error {
	s.logger.Info("无缓存模式：刷新缓存操作已跳过")
	return nil
}

// startBackgroundRefresh 🔥 删除缓存：启动后台刷新任务（现在是空操作）
func (s *ExpressMappingCacheService) startBackgroundRefresh() {
	s.logger.Info("无缓存模式：后台刷新任务已跳过")
}
