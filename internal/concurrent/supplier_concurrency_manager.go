package concurrent

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/your-org/go-kuaidi/internal/pool"
	"go.uber.org/zap"
	"golang.org/x/time/rate"
)

// SupplierConcurrencyManager 供应商并发管理器
// 基于第一性原理设计：智能并发控制 + 熔断保护 + 性能监控
type SupplierConcurrencyManager struct {
	logger *zap.Logger
	
	// 供应商特定的工作池
	supplierPools map[string]*pool.WorkerPool
	
	// 供应商特定的限流器
	supplierLimiters map[string]*rate.Limiter
	
	// 供应商特定的熔断器
	supplierBreakers map[string]*CircuitBreaker
	
	// 全局并发控制
	globalSemaphore chan struct{}
	
	// 性能统计
	stats *ConcurrencyStats
	
	// 配置
	config *ConcurrencyConfig
	
	// 互斥锁
	mutex sync.RWMutex
}

// ConcurrencyConfig 并发配置
type ConcurrencyConfig struct {
	// 全局配置
	MaxGlobalConcurrency int
	
	// 供应商特定配置
	SupplierConfigs map[string]*SupplierConfig
	
	// 默认配置
	DefaultWorkerCount int
	DefaultQueueSize   int
	DefaultRateLimit   float64
	DefaultBurst       int
	
	// 熔断器配置
	CircuitBreakerConfig *CircuitBreakerConfig
}

// SupplierConfig 供应商配置
type SupplierConfig struct {
	WorkerCount int
	QueueSize   int
	RateLimit   float64 // 每秒请求数
	Burst       int     // 突发请求数
	Timeout     time.Duration
}

// CircuitBreakerConfig 熔断器配置
type CircuitBreakerConfig struct {
	FailureThreshold   int
	RecoveryTimeout    time.Duration
	HalfOpenMaxRequest int
}

// ConcurrencyStats 并发统计
type ConcurrencyStats struct {
	TotalRequests       int64
	SuccessRequests     int64
	FailedRequests      int64
	TimeoutRequests     int64
	CircuitBreakerTrips int64
	RateLimitRejects    int64
	
	// 供应商特定统计
	SupplierStats map[string]*SupplierStats
	
	mutex sync.RWMutex
}

// SupplierStats 供应商统计
type SupplierStats struct {
	Requests        int64
	Successes       int64
	Failures        int64
	Timeouts        int64
	AverageLatency  time.Duration
	TotalLatency    time.Duration
}

// CircuitBreaker 熔断器
type CircuitBreaker struct {
	failureCount    int
	successCount    int
	lastFailureTime time.Time
	state           CircuitBreakerState
	config          *CircuitBreakerConfig
	mutex           sync.RWMutex
}

// CircuitBreakerState 熔断器状态
type CircuitBreakerState int

const (
	CircuitBreakerClosed CircuitBreakerState = iota
	CircuitBreakerOpen
	CircuitBreakerHalfOpen
)

// NewSupplierConcurrencyManager 创建供应商并发管理器
func NewSupplierConcurrencyManager(logger *zap.Logger, config *ConcurrencyConfig) *SupplierConcurrencyManager {
	if config == nil {
		config = &ConcurrencyConfig{
			MaxGlobalConcurrency: 1000,
			DefaultWorkerCount:   20,
			DefaultQueueSize:     200,
			DefaultRateLimit:     50.0,
			DefaultBurst:         100,
			SupplierConfigs:      make(map[string]*SupplierConfig),
			CircuitBreakerConfig: &CircuitBreakerConfig{
				FailureThreshold:   10,
				RecoveryTimeout:    30 * time.Second,
				HalfOpenMaxRequest: 5,
			},
		}
	}
	
	manager := &SupplierConcurrencyManager{
		logger:           logger,
		supplierPools:    make(map[string]*pool.WorkerPool),
		supplierLimiters: make(map[string]*rate.Limiter),
		supplierBreakers: make(map[string]*CircuitBreaker),
		globalSemaphore:  make(chan struct{}, config.MaxGlobalConcurrency),
		config:           config,
		stats: &ConcurrencyStats{
			SupplierStats: make(map[string]*SupplierStats),
		},
	}
	
	// 初始化默认供应商配置
	manager.initializeSupplierConfigs()
	
	return manager
}

// initializeSupplierConfigs 初始化供应商配置
func (m *SupplierConcurrencyManager) initializeSupplierConfigs() {
	suppliers := []string{"kuaidi100", "yida", "yuntong", "cainiao", "jd", "dbl"}
	
	for _, supplier := range suppliers {
		if _, exists := m.config.SupplierConfigs[supplier]; !exists {
			m.config.SupplierConfigs[supplier] = &SupplierConfig{
				WorkerCount: m.config.DefaultWorkerCount,
				QueueSize:   m.config.DefaultQueueSize,
				RateLimit:   m.config.DefaultRateLimit,
				Burst:       m.config.DefaultBurst,
				Timeout:     5 * time.Second,
			}
		}
		
		// 根据供应商特性调整配置
		m.optimizeSupplierConfig(supplier)
	}
}

// optimizeSupplierConfig 优化供应商配置
func (m *SupplierConcurrencyManager) optimizeSupplierConfig(supplier string) {
	config := m.config.SupplierConfigs[supplier]
	
	switch supplier {
	case "kuaidi100":
		// 快递100：响应快，可以提高并发
		config.WorkerCount = 30
		config.RateLimit = 80.0
		config.Timeout = 600 * time.Millisecond
		
	case "yida":
		// 易达：中等响应速度
		config.WorkerCount = 25
		config.RateLimit = 60.0
		config.Timeout = 800 * time.Millisecond
		
	case "yuntong":
		// 云通：较慢但稳定
		config.WorkerCount = 20
		config.RateLimit = 40.0
		config.Timeout = 1000 * time.Millisecond
		
	case "cainiao":
		// 菜鸟：功能丰富但较慢
		config.WorkerCount = 15
		config.RateLimit = 30.0
		config.Timeout = 1200 * time.Millisecond
		
	case "jd", "dbl":
		// 京东和德邦：专线服务
		config.WorkerCount = 10
		config.RateLimit = 20.0
		config.Timeout = 1500 * time.Millisecond
	}
}

// GetOrCreateSupplierPool 获取或创建供应商工作池
func (m *SupplierConcurrencyManager) GetOrCreateSupplierPool(supplier string) *pool.WorkerPool {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	
	if pool, exists := m.supplierPools[supplier]; exists {
		return pool
	}
	
	config := m.config.SupplierConfigs[supplier]
	if config == nil {
		config = &SupplierConfig{
			WorkerCount: m.config.DefaultWorkerCount,
			QueueSize:   m.config.DefaultQueueSize,
		}
	}
	
	workerPool := pool.NewWorkerPool(config.WorkerCount, config.QueueSize)
	workerPool.Start()
	
	m.supplierPools[supplier] = workerPool
	
	m.logger.Info("创建供应商工作池",
		zap.String("supplier", supplier),
		zap.Int("worker_count", config.WorkerCount),
		zap.Int("queue_size", config.QueueSize))
	
	return workerPool
}

// GetOrCreateSupplierLimiter 获取或创建供应商限流器
func (m *SupplierConcurrencyManager) GetOrCreateSupplierLimiter(supplier string) *rate.Limiter {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	
	if limiter, exists := m.supplierLimiters[supplier]; exists {
		return limiter
	}
	
	config := m.config.SupplierConfigs[supplier]
	if config == nil {
		config = &SupplierConfig{
			RateLimit: m.config.DefaultRateLimit,
			Burst:     m.config.DefaultBurst,
		}
	}
	
	limiter := rate.NewLimiter(rate.Limit(config.RateLimit), config.Burst)
	m.supplierLimiters[supplier] = limiter
	
	m.logger.Info("创建供应商限流器",
		zap.String("supplier", supplier),
		zap.Float64("rate_limit", config.RateLimit),
		zap.Int("burst", config.Burst))
	
	return limiter
}

// GetOrCreateSupplierBreaker 获取或创建供应商熔断器
func (m *SupplierConcurrencyManager) GetOrCreateSupplierBreaker(supplier string) *CircuitBreaker {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	
	if breaker, exists := m.supplierBreakers[supplier]; exists {
		return breaker
	}
	
	breaker := &CircuitBreaker{
		state:  CircuitBreakerClosed,
		config: m.config.CircuitBreakerConfig,
	}
	
	m.supplierBreakers[supplier] = breaker
	
	m.logger.Info("创建供应商熔断器",
		zap.String("supplier", supplier))
	
	return breaker
}

// ExecuteWithConcurrencyControl 执行带并发控制的操作
func (m *SupplierConcurrencyManager) ExecuteWithConcurrencyControl(
	ctx context.Context,
	supplier string,
	operation func(ctx context.Context) error,
) error {
	startTime := time.Now()
	
	// 1. 全局并发控制
	select {
	case m.globalSemaphore <- struct{}{}:
		defer func() { <-m.globalSemaphore }()
	case <-ctx.Done():
		return ctx.Err()
	}
	
	// 2. 供应商限流检查
	limiter := m.GetOrCreateSupplierLimiter(supplier)
	if !limiter.Allow() {
		m.recordRateLimitReject(supplier)
		return fmt.Errorf("供应商 %s 请求频率超限", supplier)
	}
	
	// 3. 熔断器检查
	breaker := m.GetOrCreateSupplierBreaker(supplier)
	if !breaker.AllowRequest() {
		m.recordCircuitBreakerTrip(supplier)
		return fmt.Errorf("供应商 %s 熔断器开启", supplier)
	}
	
	// 4. 执行操作
	err := operation(ctx)
	duration := time.Since(startTime)
	
	// 5. 记录结果
	if err != nil {
		breaker.RecordFailure()
		m.recordFailure(supplier, duration)
	} else {
		breaker.RecordSuccess()
		m.recordSuccess(supplier, duration)
	}
	
	return err
}

// SubmitTask 提交任务到供应商工作池
func (m *SupplierConcurrencyManager) SubmitTask(
	supplier string,
	task pool.Task,
	timeout time.Duration,
) error {
	workerPool := m.GetOrCreateSupplierPool(supplier)
	return workerPool.SubmitWithTimeout(task, timeout)
}

// AllowRequest 检查是否允许请求
func (cb *CircuitBreaker) AllowRequest() bool {
	cb.mutex.Lock()
	defer cb.mutex.Unlock()
	
	now := time.Now()
	
	switch cb.state {
	case CircuitBreakerClosed:
		return true
		
	case CircuitBreakerOpen:
		if now.Sub(cb.lastFailureTime) >= cb.config.RecoveryTimeout {
			cb.state = CircuitBreakerHalfOpen
			cb.successCount = 0
			return true
		}
		return false
		
	case CircuitBreakerHalfOpen:
		return cb.successCount < cb.config.HalfOpenMaxRequest
		
	default:
		return false
	}
}

// RecordSuccess 记录成功
func (cb *CircuitBreaker) RecordSuccess() {
	cb.mutex.Lock()
	defer cb.mutex.Unlock()
	
	cb.successCount++
	
	if cb.state == CircuitBreakerHalfOpen && cb.successCount >= cb.config.HalfOpenMaxRequest {
		cb.state = CircuitBreakerClosed
		cb.failureCount = 0
	}
}

// RecordFailure 记录失败
func (cb *CircuitBreaker) RecordFailure() {
	cb.mutex.Lock()
	defer cb.mutex.Unlock()
	
	cb.failureCount++
	cb.lastFailureTime = time.Now()
	
	if cb.failureCount >= cb.config.FailureThreshold {
		cb.state = CircuitBreakerOpen
	}
}

// 统计记录方法
func (m *SupplierConcurrencyManager) recordSuccess(supplier string, duration time.Duration) {
	m.stats.mutex.Lock()
	defer m.stats.mutex.Unlock()
	
	m.stats.TotalRequests++
	m.stats.SuccessRequests++
	
	if _, exists := m.stats.SupplierStats[supplier]; !exists {
		m.stats.SupplierStats[supplier] = &SupplierStats{}
	}
	
	supplierStats := m.stats.SupplierStats[supplier]
	supplierStats.Requests++
	supplierStats.Successes++
	supplierStats.TotalLatency += duration
	supplierStats.AverageLatency = supplierStats.TotalLatency / time.Duration(supplierStats.Requests)
}

func (m *SupplierConcurrencyManager) recordFailure(supplier string, duration time.Duration) {
	m.stats.mutex.Lock()
	defer m.stats.mutex.Unlock()
	
	m.stats.TotalRequests++
	m.stats.FailedRequests++
	
	if _, exists := m.stats.SupplierStats[supplier]; !exists {
		m.stats.SupplierStats[supplier] = &SupplierStats{}
	}
	
	supplierStats := m.stats.SupplierStats[supplier]
	supplierStats.Requests++
	supplierStats.Failures++
}

func (m *SupplierConcurrencyManager) recordRateLimitReject(supplier string) {
	m.stats.mutex.Lock()
	defer m.stats.mutex.Unlock()
	m.stats.RateLimitRejects++
}

func (m *SupplierConcurrencyManager) recordCircuitBreakerTrip(supplier string) {
	m.stats.mutex.Lock()
	defer m.stats.mutex.Unlock()
	m.stats.CircuitBreakerTrips++
}

// GetStats 获取统计信息
func (m *SupplierConcurrencyManager) GetStats() *ConcurrencyStats {
	m.stats.mutex.RLock()
	defer m.stats.mutex.RUnlock()
	
	// 深拷贝统计信息
	statsCopy := &ConcurrencyStats{
		TotalRequests:       m.stats.TotalRequests,
		SuccessRequests:     m.stats.SuccessRequests,
		FailedRequests:      m.stats.FailedRequests,
		TimeoutRequests:     m.stats.TimeoutRequests,
		CircuitBreakerTrips: m.stats.CircuitBreakerTrips,
		RateLimitRejects:    m.stats.RateLimitRejects,
		SupplierStats:       make(map[string]*SupplierStats),
	}
	
	for supplier, stats := range m.stats.SupplierStats {
		statsCopy.SupplierStats[supplier] = &SupplierStats{
			Requests:       stats.Requests,
			Successes:      stats.Successes,
			Failures:       stats.Failures,
			Timeouts:       stats.Timeouts,
			AverageLatency: stats.AverageLatency,
			TotalLatency:   stats.TotalLatency,
		}
	}
	
	return statsCopy
}

// Shutdown 关闭管理器
func (m *SupplierConcurrencyManager) Shutdown() {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	
	// 关闭所有工作池
	for supplier, workerPool := range m.supplierPools {
		workerPool.Stop()
		m.logger.Info("关闭供应商工作池", zap.String("supplier", supplier))
	}
	
	// 清空资源
	m.supplierPools = make(map[string]*pool.WorkerPool)
	m.supplierLimiters = make(map[string]*rate.Limiter)
	m.supplierBreakers = make(map[string]*CircuitBreaker)
}
