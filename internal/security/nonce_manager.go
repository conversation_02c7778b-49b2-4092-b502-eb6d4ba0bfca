package security

import (
	"context"
	"crypto/rand"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"strconv"
	"sync"
	"time"

	"github.com/go-redis/redis/v8"
	"go.uber.org/zap"
)

// NonceManager 企业级nonce管理器接口
type NonceManager interface {
	// GenerateNonce 生成高强度nonce
	GenerateNonce(clientID string) (string, error)

	// ValidateNonce 验证nonce是否有效且未使用
	ValidateNonce(ctx context.Context, clientID, nonce string) (*NonceValidationResult, error)

	// MarkNonceUsed 标记nonce已使用
	MarkNonceUsed(ctx context.Context, clientID, nonce string) error

	// CleanupExpiredNonces 清理过期的nonce（定期任务）
	CleanupExpiredNonces(ctx context.Context) error

	// GetNonceStats 获取nonce使用统计
	GetNonceStats(ctx context.Context) (*NonceStats, error)
}

// NonceValidationResult nonce验证结果
type NonceValidationResult struct {
	Valid       bool      `json:"valid"`
	Used        bool      `json:"used"`
	ExpiresAt   time.Time `json:"expires_at"`
	GeneratedAt time.Time `json:"generated_at"`
	ErrorCode   string    `json:"error_code,omitempty"`
	ErrorMsg    string    `json:"error_msg,omitempty"`
}

// NonceStats nonce使用统计
type NonceStats struct {
	TotalGenerated   int64     `json:"total_generated"`
	TotalValidated   int64     `json:"total_validated"`
	TotalUsed        int64     `json:"total_used"`
	TotalExpired     int64     `json:"total_expired"`
	TotalDuplicate   int64     `json:"total_duplicate"`
	LastCleanupTime  time.Time `json:"last_cleanup_time"`
	ActiveNonceCount int64     `json:"active_nonce_count"`
}

// NonceConfig nonce管理配置
type NonceConfig struct {
	// 基础配置
	ValidityDuration time.Duration `json:"validity_duration"` // nonce有效期
	MaxNonceLength   int           `json:"max_nonce_length"`  // 最大nonce长度
	MinNonceLength   int           `json:"min_nonce_length"`  // 最小nonce长度

	// Redis配置
	RedisKeyPrefix string        `json:"redis_key_prefix"` // Redis键前缀
	RedisTimeout   time.Duration `json:"redis_timeout"`    // Redis操作超时

	// 性能配置
	BatchSize       int           `json:"batch_size"`       // 批量操作大小
	CleanupInterval time.Duration `json:"cleanup_interval"` // 清理间隔

	// 安全配置
	EnableStrictMode  bool `json:"enable_strict_mode"`   // 启用严格模式
	MaxNoncePerClient int  `json:"max_nonce_per_client"` // 每个客户端最大nonce数

	// 监控配置
	EnableMetrics   bool          `json:"enable_metrics"`   // 启用指标收集
	MetricsInterval time.Duration `json:"metrics_interval"` // 指标收集间隔
}

// DefaultNonceManager 默认nonce管理器实现
type DefaultNonceManager struct {
	config      NonceConfig
	redisClient *redis.Client
	logger      *zap.Logger
	stats       *NonceStats
	statsMutex  sync.RWMutex
}

// NewNonceManager 创建新的nonce管理器
func NewNonceManager(config NonceConfig, redisClient *redis.Client, logger *zap.Logger) NonceManager {
	if config.ValidityDuration == 0 {
		config.ValidityDuration = 5 * time.Minute
	}
	if config.MaxNonceLength == 0 {
		config.MaxNonceLength = 64
	}
	if config.MinNonceLength == 0 {
		config.MinNonceLength = 16
	}
	if config.RedisKeyPrefix == "" {
		config.RedisKeyPrefix = "nonce:v2:"
	}
	if config.RedisTimeout == 0 {
		config.RedisTimeout = 3 * time.Second
	}
	if config.BatchSize == 0 {
		config.BatchSize = 100
	}
	if config.CleanupInterval == 0 {
		config.CleanupInterval = 10 * time.Minute
	}
	if config.MaxNoncePerClient == 0 {
		config.MaxNoncePerClient = 1000
	}
	if config.MetricsInterval == 0 {
		config.MetricsInterval = 1 * time.Minute
	}

	manager := &DefaultNonceManager{
		config:      config,
		redisClient: redisClient,
		logger:      logger,
		stats: &NonceStats{
			LastCleanupTime: time.Now(),
		},
	}

	// 启动后台任务
	go manager.startBackgroundTasks()

	return manager
}

// GenerateNonce 生成高强度nonce
func (nm *DefaultNonceManager) GenerateNonce(clientID string) (string, error) {
	// 生成时间戳部分（毫秒级，北京时间）
	timestamp := time.Now().UnixMilli()

	// 生成随机部分（16字节）
	randomBytes := make([]byte, 16)
	if _, err := rand.Read(randomBytes); err != nil {
		nm.logger.Error("生成随机字节失败", zap.Error(err))
		return "", fmt.Errorf("生成随机字节失败: %w", err)
	}

	// 生成客户端指纹（防止跨客户端nonce重用）
	clientHash := sha256.Sum256([]byte(clientID))
	clientFingerprint := hex.EncodeToString(clientHash[:4]) // 8字符

	// 组合nonce: timestamp(13) + random(32) + client(8) = 53字符
	nonce := fmt.Sprintf("%d%s%s",
		timestamp,
		hex.EncodeToString(randomBytes),
		clientFingerprint,
	)

	// 更新统计
	nm.updateStats(func(stats *NonceStats) {
		stats.TotalGenerated++
	})

	nm.logger.Debug("生成nonce成功",
		zap.String("client_id", clientID),
		zap.String("nonce", nonce[:16]+"..."), // 只记录前16字符
		zap.Int("length", len(nonce)))

	return nonce, nil
}

// ValidateNonce 验证nonce是否有效且未使用
func (nm *DefaultNonceManager) ValidateNonce(ctx context.Context, clientID, nonce string) (*NonceValidationResult, error) {
	// 更新统计
	nm.updateStats(func(stats *NonceStats) {
		stats.TotalValidated++
	})

	// 基础格式验证
	if err := nm.validateNonceFormat(nonce); err != nil {
		return &NonceValidationResult{
			Valid:     false,
			ErrorCode: "INVALID_FORMAT",
			ErrorMsg:  err.Error(),
		}, nil
	}

	// 验证nonce是否属于该客户端
	if err := nm.validateNonceOwnership(clientID, nonce); err != nil {
		return &NonceValidationResult{
			Valid:     false,
			ErrorCode: "INVALID_OWNERSHIP",
			ErrorMsg:  err.Error(),
		}, nil
	}

	// 检查nonce是否已使用
	timeoutCtx, cancel := context.WithTimeout(ctx, nm.config.RedisTimeout)
	defer cancel()

	nonceKey := nm.buildNonceKey(clientID, nonce)
	exists, err := nm.redisClient.Exists(timeoutCtx, nonceKey).Result()
	if err != nil {
		nm.logger.Error("检查nonce是否存在失败",
			zap.String("client_id", clientID),
			zap.String("nonce_key", nonceKey),
			zap.Error(err))
		return nil, fmt.Errorf("检查nonce状态失败: %w", err)
	}

	if exists > 0 {
		// nonce已使用
		nm.updateStats(func(stats *NonceStats) {
			stats.TotalDuplicate++
		})

		return &NonceValidationResult{
			Valid:     false,
			Used:      true,
			ErrorCode: "NONCE_ALREADY_USED",
			ErrorMsg:  "nonce已被使用",
		}, nil
	}

	// 解析nonce中的时间戳
	generatedAt, err := nm.extractTimestampFromNonce(nonce)
	if err != nil {
		return &NonceValidationResult{
			Valid:     false,
			ErrorCode: "INVALID_TIMESTAMP",
			ErrorMsg:  err.Error(),
		}, nil
	}

	// 检查是否过期
	expiresAt := generatedAt.Add(nm.config.ValidityDuration)
	if time.Now().After(expiresAt) {
		nm.updateStats(func(stats *NonceStats) {
			stats.TotalExpired++
		})

		return &NonceValidationResult{
			Valid:       false,
			GeneratedAt: generatedAt,
			ExpiresAt:   expiresAt,
			ErrorCode:   "NONCE_EXPIRED",
			ErrorMsg:    "nonce已过期",
		}, nil
	}

	return &NonceValidationResult{
		Valid:       true,
		Used:        false,
		GeneratedAt: generatedAt,
		ExpiresAt:   expiresAt,
	}, nil
}

// MarkNonceUsed 标记nonce已使用
func (nm *DefaultNonceManager) MarkNonceUsed(ctx context.Context, clientID, nonce string) error {
	timeoutCtx, cancel := context.WithTimeout(ctx, nm.config.RedisTimeout)
	defer cancel()

	nonceKey := nm.buildNonceKey(clientID, nonce)

	// 使用SET NX确保原子性
	result, err := nm.redisClient.SetNX(timeoutCtx, nonceKey, time.Now().Unix(), nm.config.ValidityDuration).Result()
	if err != nil {
		nm.logger.Error("标记nonce已使用失败",
			zap.String("client_id", clientID),
			zap.String("nonce_key", nonceKey),
			zap.Error(err))
		return fmt.Errorf("标记nonce已使用失败: %w", err)
	}

	if !result {
		// nonce已存在，说明已被使用
		nm.updateStats(func(stats *NonceStats) {
			stats.TotalDuplicate++
		})
		return fmt.Errorf("nonce已被使用")
	}

	// 更新统计
	nm.updateStats(func(stats *NonceStats) {
		stats.TotalUsed++
	})

	// 安全地截取nonce用于日志显示
	nonceDisplay := nonce
	if len(nonce) > 16 {
		nonceDisplay = nonce[:16] + "..."
	}

	nm.logger.Debug("标记nonce已使用",
		zap.String("client_id", clientID),
		zap.String("nonce", nonceDisplay))

	return nil
}

// CleanupExpiredNonces 清理过期的nonce
func (nm *DefaultNonceManager) CleanupExpiredNonces(ctx context.Context) error {
	timeoutCtx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	pattern := nm.config.RedisKeyPrefix + "*"

	// 使用SCAN命令分批获取键
	var cursor uint64
	var deletedCount int64

	for {
		keys, nextCursor, err := nm.redisClient.Scan(timeoutCtx, cursor, pattern, int64(nm.config.BatchSize)).Result()
		if err != nil {
			nm.logger.Error("扫描nonce键失败", zap.Error(err))
			return fmt.Errorf("扫描nonce键失败: %w", err)
		}

		if len(keys) > 0 {
			// 批量删除过期键
			deleted, err := nm.redisClient.Del(timeoutCtx, keys...).Result()
			if err != nil {
				nm.logger.Warn("删除过期nonce失败", zap.Error(err))
			} else {
				deletedCount += deleted
			}
		}

		cursor = nextCursor
		if cursor == 0 {
			break
		}
	}

	// 更新统计
	nm.updateStats(func(stats *NonceStats) {
		stats.LastCleanupTime = time.Now()
	})

	nm.logger.Info("清理过期nonce完成",
		zap.Int64("deleted_count", deletedCount))

	return nil
}

// GetNonceStats 获取nonce使用统计
func (nm *DefaultNonceManager) GetNonceStats(ctx context.Context) (*NonceStats, error) {
	nm.statsMutex.RLock()
	defer nm.statsMutex.RUnlock()

	// 获取当前活跃nonce数量
	timeoutCtx, cancel := context.WithTimeout(ctx, nm.config.RedisTimeout)
	defer cancel()

	pattern := nm.config.RedisKeyPrefix + "*"
	activeCount := int64(0)

	var cursor uint64
	for {
		keys, nextCursor, err := nm.redisClient.Scan(timeoutCtx, cursor, pattern, 100).Result()
		if err != nil {
			nm.logger.Warn("获取活跃nonce数量失败", zap.Error(err))
			break
		}

		activeCount += int64(len(keys))
		cursor = nextCursor
		if cursor == 0 {
			break
		}
	}

	// 复制统计数据
	stats := *nm.stats
	stats.ActiveNonceCount = activeCount

	return &stats, nil
}

// 辅助方法

// validateNonceFormat 验证nonce格式
func (nm *DefaultNonceManager) validateNonceFormat(nonce string) error {
	if len(nonce) < nm.config.MinNonceLength {
		return fmt.Errorf("nonce长度不足，最小长度: %d", nm.config.MinNonceLength)
	}

	if len(nonce) > nm.config.MaxNonceLength {
		return fmt.Errorf("nonce长度超限，最大长度: %d", nm.config.MaxNonceLength)
	}

	// 检查是否只包含十六进制字符和数字
	for _, char := range nonce {
		if !((char >= '0' && char <= '9') || (char >= 'a' && char <= 'f') || (char >= 'A' && char <= 'F')) {
			return fmt.Errorf("nonce包含非法字符")
		}
	}

	return nil
}

// validateNonceOwnership 验证nonce所有权
func (nm *DefaultNonceManager) validateNonceOwnership(clientID, nonce string) error {
	if len(nonce) < 53 { // 最小长度检查
		return fmt.Errorf("nonce格式无效")
	}

	// 提取客户端指纹（最后8字符）
	nonceFingerprint := nonce[len(nonce)-8:]

	// 计算期望的客户端指纹
	clientHash := sha256.Sum256([]byte(clientID))
	expectedFingerprint := hex.EncodeToString(clientHash[:4])

	if nonceFingerprint != expectedFingerprint {
		return fmt.Errorf("nonce不属于该客户端")
	}

	return nil
}

// extractTimestampFromNonce 从nonce中提取时间戳
func (nm *DefaultNonceManager) extractTimestampFromNonce(nonce string) (time.Time, error) {
	if len(nonce) < 13 {
		return time.Time{}, fmt.Errorf("nonce格式无效")
	}

	// 提取时间戳部分（前13字符）
	timestampStr := nonce[:13]
	timestamp, err := strconv.ParseInt(timestampStr, 10, 64)
	if err != nil {
		return time.Time{}, fmt.Errorf("解析时间戳失败: %w", err)
	}

	return time.UnixMilli(timestamp), nil
}

// buildNonceKey 构建Redis键
func (nm *DefaultNonceManager) buildNonceKey(clientID, nonce string) string {
	return fmt.Sprintf("%s%s:%s", nm.config.RedisKeyPrefix, clientID, nonce)
}

// updateStats 更新统计信息
func (nm *DefaultNonceManager) updateStats(updater func(*NonceStats)) {
	nm.statsMutex.Lock()
	defer nm.statsMutex.Unlock()
	updater(nm.stats)
}

// startBackgroundTasks 启动后台任务
func (nm *DefaultNonceManager) startBackgroundTasks() {
	// 清理任务
	cleanupTicker := time.NewTicker(nm.config.CleanupInterval)
	defer cleanupTicker.Stop()

	// 指标收集任务
	var metricsTicker *time.Ticker
	if nm.config.EnableMetrics {
		metricsTicker = time.NewTicker(nm.config.MetricsInterval)
		defer metricsTicker.Stop()
	}

	for {
		select {
		case <-cleanupTicker.C:
			ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
			if err := nm.CleanupExpiredNonces(ctx); err != nil {
				nm.logger.Error("定期清理nonce失败", zap.Error(err))
			}
			cancel()

		case <-metricsTicker.C:
			if nm.config.EnableMetrics {
				ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
				if stats, err := nm.GetNonceStats(ctx); err == nil {
					nm.logger.Info("nonce使用统计",
						zap.Int64("total_generated", stats.TotalGenerated),
						zap.Int64("total_used", stats.TotalUsed),
						zap.Int64("total_duplicate", stats.TotalDuplicate),
						zap.Int64("active_count", stats.ActiveNonceCount))
				}
				cancel()
			}
		}
	}
}
