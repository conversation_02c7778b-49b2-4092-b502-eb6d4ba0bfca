package database

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"sync"
	"time"

	"go.uber.org/zap"
)

// QueryOptimizer 查询优化器
// 基于第一性原理设计：智能查询优化 + 缓存策略 + 性能监控
type QueryOptimizer struct {
	db     *sql.DB
	logger *zap.Logger

	// 查询缓存
	queryCache      map[string]*CachedResult
	queryCacheMutex sync.RWMutex

	// 查询统计
	queryStats      map[string]*QueryStats
	queryStatsMutex sync.RWMutex

	// 配置
	config *OptimizerConfig
}

// OptimizerConfig 优化器配置
type OptimizerConfig struct {
	// 缓存配置
	CacheEnabled    bool
	CacheTTL        time.Duration
	MaxCacheEntries int

	// 查询优化配置
	EnableQueryRewrite    bool
	EnableIndexHints      bool
	EnableParallelQueries bool

	// 性能阈值
	SlowQueryThreshold time.Duration

	// 连接池优化
	MaxOpenConns    int
	MaxIdleConns    int
	ConnMaxLifetime time.Duration
}

// CachedResult 缓存结果
type CachedResult struct {
	Data      interface{}
	ExpiresAt time.Time
	HitCount  int64
}

// QueryStats 查询统计
type QueryStats struct {
	QueryPattern    string
	ExecutionCount  int64
	TotalDuration   time.Duration
	AverageDuration time.Duration
	MinDuration     time.Duration
	MaxDuration     time.Duration
	LastExecuted    time.Time
}

// NewQueryOptimizer 创建查询优化器
func NewQueryOptimizer(db *sql.DB, logger *zap.Logger, config *OptimizerConfig) *QueryOptimizer {
	if config == nil {
		config = &OptimizerConfig{
			CacheEnabled:       true,
			CacheTTL:           5 * time.Minute,
			MaxCacheEntries:    1000,
			EnableQueryRewrite: true,
			EnableIndexHints:   true,
			SlowQueryThreshold: 100 * time.Millisecond,
			MaxOpenConns:       200,
			MaxIdleConns:       100,
			ConnMaxLifetime:    30 * time.Minute,
		}
	}

	optimizer := &QueryOptimizer{
		db:         db,
		logger:     logger,
		config:     config,
		queryCache: make(map[string]*CachedResult),
		queryStats: make(map[string]*QueryStats),
	}

	// 优化数据库连接池
	optimizer.optimizeConnectionPool()

	// 启动缓存清理协程
	go optimizer.startCacheCleanup()

	return optimizer
}

// optimizeConnectionPool 优化数据库连接池
func (o *QueryOptimizer) optimizeConnectionPool() {
	o.db.SetMaxOpenConns(o.config.MaxOpenConns)
	o.db.SetMaxIdleConns(o.config.MaxIdleConns)
	o.db.SetConnMaxLifetime(o.config.ConnMaxLifetime)
	o.db.SetConnMaxIdleTime(5 * time.Minute)

	o.logger.Info("数据库连接池已优化",
		zap.Int("max_open_conns", o.config.MaxOpenConns),
		zap.Int("max_idle_conns", o.config.MaxIdleConns),
		zap.Duration("conn_max_lifetime", o.config.ConnMaxLifetime))
}

// OptimizeOrderListQuery 优化订单列表查询
func (o *QueryOptimizer) OptimizeOrderListQuery(ctx context.Context, userID string, filters map[string]interface{}, page, pageSize int) (string, []interface{}) {
	// 构建优化的查询
	var conditions []string
	var args []interface{}
	argIndex := 1

	// 用户ID条件（必须）
	conditions = append(conditions, fmt.Sprintf("user_id = $%d", argIndex))
	args = append(args, userID)
	argIndex++

	// 时间筛选优化
	if startTime, ok := filters["start_time"].(string); ok && startTime != "" {
		conditions = append(conditions, fmt.Sprintf("created_at >= $%d", argIndex))
		args = append(args, startTime)
		argIndex++
	}

	if endTime, ok := filters["end_time"].(string); ok && endTime != "" {
		conditions = append(conditions, fmt.Sprintf("created_at <= $%d", argIndex))
		args = append(args, endTime)
		argIndex++
	}

	// 状态筛选
	if status, ok := filters["status"].(string); ok && status != "" {
		conditions = append(conditions, fmt.Sprintf("status = $%d", argIndex))
		args = append(args, status)
		argIndex++
	}

	// 快递公司筛选
	if expressType, ok := filters["express_type"].(string); ok && expressType != "" {
		conditions = append(conditions, fmt.Sprintf("express_type = $%d", argIndex))
		args = append(args, expressType)
		argIndex++
	}

	// 供应商筛选
	if provider, ok := filters["provider"].(string); ok && provider != "" {
		conditions = append(conditions, fmt.Sprintf("provider = $%d", argIndex))
		args = append(args, provider)
		argIndex++
	}

	// 构建WHERE子句
	whereClause := "WHERE " + strings.Join(conditions, " AND ")

	// 优化的查询语句
	// 使用索引提示和优化的字段选择
	query := fmt.Sprintf(`
		SELECT 
			id, COALESCE(platform_order_no, '') as platform_order_no,
			customer_order_no, order_no, tracking_no, express_type, 
			product_type, provider, status, weight, price,
			sender_info, receiver_info, package_info,
			created_at, updated_at,
			COALESCE(actual_fee, 0) as actual_fee,
			COALESCE(billing_status, 'pending') as billing_status
		FROM order_records 
		%s
		ORDER BY created_at DESC, id DESC
		LIMIT $%d OFFSET $%d`,
		whereClause, argIndex, argIndex+1)

	// 添加分页参数
	offset := (page - 1) * pageSize
	args = append(args, pageSize, offset)

	return query, args
}

// ExecuteWithCache 带缓存的查询执行
func (o *QueryOptimizer) ExecuteWithCache(ctx context.Context, cacheKey string, query string, args []interface{}) (*sql.Rows, error) {
	startTime := time.Now()

	// 检查缓存
	if o.config.CacheEnabled {
		if cached := o.getFromCache(cacheKey); cached != nil {
			o.logger.Debug("查询命中缓存", zap.String("cache_key", cacheKey))
			// 这里需要根据实际情况返回缓存的结果
			// 由于sql.Rows不能直接缓存，这里需要特殊处理
		}
	}

	// 执行查询
	rows, err := o.db.QueryContext(ctx, query, args...)
	duration := time.Since(startTime)

	// 记录查询统计
	o.recordQueryStats(query, duration)

	// 检查慢查询
	if duration > o.config.SlowQueryThreshold {
		o.logger.Warn("检测到慢查询",
			zap.Duration("duration", duration),
			zap.String("query", query),
			zap.Any("args", args))
	}

	return rows, err
}

// getFromCache 从缓存获取结果
func (o *QueryOptimizer) getFromCache(key string) *CachedResult {
	o.queryCacheMutex.RLock()
	defer o.queryCacheMutex.RUnlock()

	cached, exists := o.queryCache[key]
	if !exists {
		return nil
	}

	// 检查是否过期
	if time.Now().After(cached.ExpiresAt) {
		// 异步删除过期缓存
		go o.removeFromCache(key)
		return nil
	}

	// 增加命中次数
	cached.HitCount++
	return cached
}

// removeFromCache 从缓存中删除
func (o *QueryOptimizer) removeFromCache(key string) {
	o.queryCacheMutex.Lock()
	defer o.queryCacheMutex.Unlock()
	delete(o.queryCache, key)
}

// recordQueryStats 记录查询统计
func (o *QueryOptimizer) recordQueryStats(query string, duration time.Duration) {
	// 提取查询模式（去除具体参数）
	pattern := o.extractQueryPattern(query)

	o.queryStatsMutex.Lock()
	defer o.queryStatsMutex.Unlock()

	stats, exists := o.queryStats[pattern]
	if !exists {
		stats = &QueryStats{
			QueryPattern: pattern,
			MinDuration:  duration,
			MaxDuration:  duration,
		}
		o.queryStats[pattern] = stats
	}

	// 更新统计信息
	stats.ExecutionCount++
	stats.TotalDuration += duration
	stats.AverageDuration = stats.TotalDuration / time.Duration(stats.ExecutionCount)
	stats.LastExecuted = time.Now()

	if duration < stats.MinDuration {
		stats.MinDuration = duration
	}
	if duration > stats.MaxDuration {
		stats.MaxDuration = duration
	}
}

// extractQueryPattern 提取查询模式
func (o *QueryOptimizer) extractQueryPattern(query string) string {
	// 简化查询模式提取，实际应该更复杂
	query = strings.TrimSpace(query)
	if strings.HasPrefix(strings.ToUpper(query), "SELECT") {
		if strings.Contains(strings.ToUpper(query), "FROM ORDER_RECORDS") {
			return "SELECT_ORDER_RECORDS"
		}
	}
	return "UNKNOWN"
}

// startCacheCleanup 启动缓存清理
func (o *QueryOptimizer) startCacheCleanup() {
	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		o.cleanupExpiredCache()
	}
}

// cleanupExpiredCache 清理过期缓存
func (o *QueryOptimizer) cleanupExpiredCache() {
	o.queryCacheMutex.Lock()
	defer o.queryCacheMutex.Unlock()

	now := time.Now()
	for key, cached := range o.queryCache {
		if now.After(cached.ExpiresAt) {
			delete(o.queryCache, key)
		}
	}
}

// GetStats 获取优化器统计信息
func (o *QueryOptimizer) GetStats() map[string]interface{} {
	o.queryCacheMutex.RLock()
	o.queryStatsMutex.RLock()
	defer o.queryCacheMutex.RUnlock()
	defer o.queryStatsMutex.RUnlock()

	return map[string]interface{}{
		"cache_entries":  len(o.queryCache),
		"query_patterns": len(o.queryStats),
		"cache_enabled":  o.config.CacheEnabled,
		"cache_ttl":      o.config.CacheTTL.String(),
	}
}
