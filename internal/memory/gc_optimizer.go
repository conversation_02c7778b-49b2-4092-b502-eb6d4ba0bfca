package memory

import (
	"runtime"
	"runtime/debug"
	"strings"
	"sync"
	"time"

	"go.uber.org/zap"
)

// GCOptimizer 垃圾回收优化器
// 基于第一性原理设计：智能GC调优 + 内存池管理 + 性能监控
type GCOptimizer struct {
	logger *zap.Logger

	// GC配置
	config *GCConfig

	// 内存池管理
	poolManager *PoolManager

	// 性能统计
	stats *GCStats

	// 监控协程控制
	stopChan chan struct{}
	wg       sync.WaitGroup
}

// GCConfig GC配置
type GCConfig struct {
	// GC目标百分比（默认100）
	GCPercent int

	// 内存限制（字节）
	MemoryLimit int64

	// 监控间隔
	MonitorInterval time.Duration

	// 自动调优开关
	AutoTuning bool

	// 强制GC阈值（内存使用率）
	ForceGCThreshold float64

	// 内存池配置
	PoolConfig *PoolConfig
}

// PoolConfig 内存池配置
type PoolConfig struct {
	// 小对象池配置（<1KB）
	SmallObjectPoolSize int
	SmallObjectMaxSize  int

	// 中等对象池配置（1KB-64KB）
	MediumObjectPoolSize int
	MediumObjectMaxSize  int

	// 大对象池配置（64KB-1MB）
	LargeObjectPoolSize int
	LargeObjectMaxSize  int

	// 缓冲区池配置
	BufferPoolSizes []int // 不同大小的缓冲区
}

// GCStats GC统计信息
type GCStats struct {
	// GC次数
	NumGC uint32

	// GC暂停时间
	PauseTotal time.Duration
	PauseAvg   time.Duration
	PauseMax   time.Duration

	// 内存使用
	HeapAlloc    uint64
	HeapSys      uint64
	HeapIdle     uint64
	HeapInuse    uint64
	HeapReleased uint64

	// 对象统计
	Mallocs  uint64
	Frees    uint64
	LiveObjs uint64

	// 池统计
	PoolHits   int64
	PoolMisses int64

	// 最后更新时间
	LastUpdate time.Time

	mutex sync.RWMutex
}

// PoolManager 内存池管理器
type PoolManager struct {
	// 不同大小的字节切片池
	bytesPools map[int]*sync.Pool

	// 字符串构建器池
	stringBuilderPool *sync.Pool

	// 通用对象池
	objectPools map[string]*sync.Pool

	// 统计信息
	hits   int64
	misses int64
	mutex  sync.RWMutex
}

// NewGCOptimizer 创建GC优化器
func NewGCOptimizer(logger *zap.Logger, config *GCConfig) *GCOptimizer {
	if config == nil {
		config = &GCConfig{
			GCPercent:        100,
			MemoryLimit:      0, // 0表示不限制
			MonitorInterval:  30 * time.Second,
			AutoTuning:       true,
			ForceGCThreshold: 0.85, // 85%内存使用率时强制GC
			PoolConfig: &PoolConfig{
				SmallObjectPoolSize:  1000,
				SmallObjectMaxSize:   1024,
				MediumObjectPoolSize: 500,
				MediumObjectMaxSize:  65536,
				LargeObjectPoolSize:  100,
				LargeObjectMaxSize:   1048576,
				BufferPoolSizes:      []int{64, 256, 1024, 4096, 16384, 65536},
			},
		}
	}

	optimizer := &GCOptimizer{
		logger:      logger,
		config:      config,
		poolManager: NewPoolManager(config.PoolConfig),
		stats:       &GCStats{},
		stopChan:    make(chan struct{}),
	}

	// 应用初始GC配置
	optimizer.applyGCConfig()

	// 启动监控协程
	optimizer.startMonitoring()

	return optimizer
}

// NewPoolManager 创建内存池管理器
func NewPoolManager(config *PoolConfig) *PoolManager {
	pm := &PoolManager{
		bytesPools:  make(map[int]*sync.Pool),
		objectPools: make(map[string]*sync.Pool),
	}

	// 初始化字节切片池
	for _, size := range config.BufferPoolSizes {
		pm.bytesPools[size] = &sync.Pool{
			New: func() interface{} {
				return make([]byte, 0, size)
			},
		}
	}

	// 初始化字符串构建器池
	pm.stringBuilderPool = &sync.Pool{
		New: func() interface{} {
			return &strings.Builder{}
		},
	}

	return pm
}

// applyGCConfig 应用GC配置
func (o *GCOptimizer) applyGCConfig() {
	// 设置GC目标百分比
	debug.SetGCPercent(o.config.GCPercent)

	// 设置内存限制（Go 1.19+）
	if o.config.MemoryLimit > 0 {
		debug.SetMemoryLimit(o.config.MemoryLimit)
	}

	o.logger.Info("应用GC配置",
		zap.Int("gc_percent", o.config.GCPercent),
		zap.Int64("memory_limit", o.config.MemoryLimit))
}

// startMonitoring 启动监控
func (o *GCOptimizer) startMonitoring() {
	o.wg.Add(1)
	go func() {
		defer o.wg.Done()

		ticker := time.NewTicker(o.config.MonitorInterval)
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				o.updateStats()
				if o.config.AutoTuning {
					o.autoTuneGC()
				}

			case <-o.stopChan:
				return
			}
		}
	}()
}

// updateStats 更新统计信息
func (o *GCOptimizer) updateStats() {
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)

	o.stats.mutex.Lock()
	defer o.stats.mutex.Unlock()

	// 更新GC统计
	o.stats.NumGC = memStats.NumGC
	o.stats.PauseTotal = time.Duration(memStats.PauseTotalNs)
	if memStats.NumGC > 0 {
		o.stats.PauseAvg = o.stats.PauseTotal / time.Duration(memStats.NumGC)
	}

	// 计算最大暂停时间
	if len(memStats.PauseNs) > 0 {
		var maxPause uint64
		for _, pause := range memStats.PauseNs {
			if pause > maxPause {
				maxPause = pause
			}
		}
		o.stats.PauseMax = time.Duration(maxPause)
	}

	// 更新内存统计
	o.stats.HeapAlloc = memStats.HeapAlloc
	o.stats.HeapSys = memStats.HeapSys
	o.stats.HeapIdle = memStats.HeapIdle
	o.stats.HeapInuse = memStats.HeapInuse
	o.stats.HeapReleased = memStats.HeapReleased

	// 更新对象统计
	o.stats.Mallocs = memStats.Mallocs
	o.stats.Frees = memStats.Frees
	o.stats.LiveObjs = memStats.Mallocs - memStats.Frees

	// 更新池统计
	o.poolManager.mutex.RLock()
	o.stats.PoolHits = o.poolManager.hits
	o.stats.PoolMisses = o.poolManager.misses
	o.poolManager.mutex.RUnlock()

	o.stats.LastUpdate = time.Now()
}

// autoTuneGC 自动调优GC
func (o *GCOptimizer) autoTuneGC() {
	o.stats.mutex.RLock()
	heapInuse := o.stats.HeapInuse
	heapSys := o.stats.HeapSys
	pauseAvg := o.stats.PauseAvg
	o.stats.mutex.RUnlock()

	// 计算内存使用率
	memoryUsage := float64(heapInuse) / float64(heapSys)

	// 如果内存使用率过高，强制GC
	if memoryUsage > o.config.ForceGCThreshold {
		o.logger.Warn("内存使用率过高，强制GC",
			zap.Float64("memory_usage", memoryUsage),
			zap.Float64("threshold", o.config.ForceGCThreshold))
		runtime.GC()
		return
	}

	// 根据GC暂停时间调整GC百分比
	if pauseAvg > 10*time.Millisecond {
		// GC暂停时间过长，降低GC频率
		newPercent := o.config.GCPercent + 20
		if newPercent <= 200 {
			debug.SetGCPercent(newPercent)
			o.config.GCPercent = newPercent
			o.logger.Info("调整GC百分比（降低频率）",
				zap.Int("new_percent", newPercent),
				zap.Duration("pause_avg", pauseAvg))
		}
	} else if pauseAvg < 2*time.Millisecond && memoryUsage > 0.7 {
		// GC暂停时间短且内存使用率高，提高GC频率
		newPercent := o.config.GCPercent - 20
		if newPercent >= 50 {
			debug.SetGCPercent(newPercent)
			o.config.GCPercent = newPercent
			o.logger.Info("调整GC百分比（提高频率）",
				zap.Int("new_percent", newPercent),
				zap.Duration("pause_avg", pauseAvg))
		}
	}
}

// GetBytes 从池中获取字节切片
func (pm *PoolManager) GetBytes(size int) []byte {
	// 找到合适的池
	poolSize := pm.findPoolSize(size)
	if poolSize == 0 {
		pm.recordMiss()
		return make([]byte, 0, size)
	}

	pool := pm.bytesPools[poolSize]
	bytes := pool.Get().([]byte)
	pm.recordHit()

	// 重置切片长度但保持容量
	return bytes[:0]
}

// PutBytes 将字节切片放回池中
func (pm *PoolManager) PutBytes(bytes []byte) {
	if bytes == nil {
		return
	}

	capacity := cap(bytes)
	poolSize := pm.findPoolSize(capacity)
	if poolSize == 0 {
		return // 不适合放入池中
	}

	// 检查容量是否匹配
	if capacity == poolSize {
		pool := pm.bytesPools[poolSize]
		pool.Put(bytes)
	}
}

// findPoolSize 找到合适的池大小
func (pm *PoolManager) findPoolSize(size int) int {
	for _, poolSize := range []int{64, 256, 1024, 4096, 16384, 65536} {
		if size <= poolSize {
			return poolSize
		}
	}
	return 0 // 太大，不适合池化
}

// GetStringBuilder 从池中获取字符串构建器
func (pm *PoolManager) GetStringBuilder() *strings.Builder {
	sb := pm.stringBuilderPool.Get().(*strings.Builder)
	sb.Reset()
	pm.recordHit()
	return sb
}

// PutStringBuilder 将字符串构建器放回池中
func (pm *PoolManager) PutStringBuilder(sb *strings.Builder) {
	if sb != nil {
		pm.stringBuilderPool.Put(sb)
	}
}

// recordHit 记录池命中
func (pm *PoolManager) recordHit() {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()
	pm.hits++
}

// recordMiss 记录池未命中
func (pm *PoolManager) recordMiss() {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()
	pm.misses++
}

// ForceGC 强制垃圾回收
func (o *GCOptimizer) ForceGC() {
	start := time.Now()
	runtime.GC()
	duration := time.Since(start)

	o.logger.Info("强制GC完成", zap.Duration("duration", duration))
}

// GetStats 获取统计信息
func (o *GCOptimizer) GetStats() *GCStats {
	o.stats.mutex.RLock()
	defer o.stats.mutex.RUnlock()

	// 返回统计信息的副本
	return &GCStats{
		NumGC:        o.stats.NumGC,
		PauseTotal:   o.stats.PauseTotal,
		PauseAvg:     o.stats.PauseAvg,
		PauseMax:     o.stats.PauseMax,
		HeapAlloc:    o.stats.HeapAlloc,
		HeapSys:      o.stats.HeapSys,
		HeapIdle:     o.stats.HeapIdle,
		HeapInuse:    o.stats.HeapInuse,
		HeapReleased: o.stats.HeapReleased,
		Mallocs:      o.stats.Mallocs,
		Frees:        o.stats.Frees,
		LiveObjs:     o.stats.LiveObjs,
		PoolHits:     o.stats.PoolHits,
		PoolMisses:   o.stats.PoolMisses,
		LastUpdate:   o.stats.LastUpdate,
	}
}

// GetPoolManager 获取内存池管理器
func (o *GCOptimizer) GetPoolManager() *PoolManager {
	return o.poolManager
}

// Shutdown 关闭优化器
func (o *GCOptimizer) Shutdown() {
	close(o.stopChan)
	o.wg.Wait()
	o.logger.Info("GC优化器已关闭")
}

// 全局GC优化器实例
var globalGCOptimizer *GCOptimizer
var globalGCOptimizerOnce sync.Once

// GetGlobalGCOptimizer 获取全局GC优化器
func GetGlobalGCOptimizer() *GCOptimizer {
	globalGCOptimizerOnce.Do(func() {
		logger, _ := zap.NewProduction()
		globalGCOptimizer = NewGCOptimizer(logger, nil)
	})
	return globalGCOptimizer
}

// 便捷函数

// OptimizedGetBytes 优化的获取字节切片
func OptimizedGetBytes(size int) []byte {
	return GetGlobalGCOptimizer().GetPoolManager().GetBytes(size)
}

// OptimizedPutBytes 优化的放回字节切片
func OptimizedPutBytes(bytes []byte) {
	GetGlobalGCOptimizer().GetPoolManager().PutBytes(bytes)
}

// OptimizedGetStringBuilder 优化的获取字符串构建器
func OptimizedGetStringBuilder() *strings.Builder {
	return GetGlobalGCOptimizer().GetPoolManager().GetStringBuilder()
}

// OptimizedPutStringBuilder 优化的放回字符串构建器
func OptimizedPutStringBuilder(sb *strings.Builder) {
	GetGlobalGCOptimizer().GetPoolManager().PutStringBuilder(sb)
}
