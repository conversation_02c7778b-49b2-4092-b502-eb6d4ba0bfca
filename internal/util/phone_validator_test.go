package util

import (
	"testing"
)

func TestPhoneValidator(t *testing.T) {
	validator := GetPhoneValidator()

	tests := []struct {
		name        string
		phone       string
		expectValid bool
		expectType  PhoneType
		expectError string
	}{
		// Valid mobile numbers
		{
			name:        "Valid mobile **********0",
			phone:       "**********0",
			expectValid: true,
			expectType:  PhoneTypeMobile,
		},
		{
			name:        "Valid mobile with spaces",
			phone:       "138 0013 8000",
			expectValid: true,
			expectType:  PhoneTypeMobile,
		},
		{
			name:        "Valid mobile with international prefix",
			phone:       "+86**********0",
			expectValid: true,
			expectType:  PhoneTypeMobile,
		},
		{
			name:        "Valid mobile 15912345678",
			phone:       "15912345678",
			expectValid: true,
			expectType:  PhoneTypeMobile,
		},
		{
			name:        "Valid mobile 18888888888",
			phone:       "18888888888",
			expectValid: true,
			expectType:  PhoneTypeMobile,
		},

		// Valid landline numbers
		{
			name:        "Valid landline 010-12345678",
			phone:       "010-12345678",
			expectValid: true,
			expectType:  PhoneTypeLandline,
		},
		{
			name:        "Valid landline 0755-********",
			phone:       "0755-********",
			expectValid: true,
			expectType:  PhoneTypeLandline,
		},
		{
			name:        "Valid 400 number",
			phone:       "************",
			expectValid: true,
			expectType:  PhoneTypeLandline,
		},
		{
			name:        "Valid 800 number",
			phone:       "************",
			expectValid: true,
			expectType:  PhoneTypeLandline,
		},
		{
			name:        "Valid bank service 95588",
			phone:       "95588",
			expectValid: true,
			expectType:  PhoneTypeLandline,
		},
		{
			name:        "Valid telecom service 10086",
			phone:       "10086",
			expectValid: true,
			expectType:  PhoneTypeLandline,
		},

		// Invalid numbers
		{
			name:        "Empty phone",
			phone:       "",
			expectValid: false,
			expectError: "EMPTY_PHONE",
		},
		{
			name:        "Too long phone",
			phone:       "**********0****************************************",
			expectValid: false,
			expectError: "PHONE_TOO_LONG",
		},
		{
			name:        "Invalid mobile - wrong second digit",
			phone:       "***********",
			expectValid: false,
			expectError: "INVALID_FORMAT",
		},
		{
			name:        "Invalid mobile - too short",
			phone:       "**********",
			expectValid: false,
			expectError: "INVALID_FORMAT",
		},
		{
			name:        "Invalid format",
			phone:       "abcdefghijk",
			expectValid: false,
			expectError: "INVALID_FORMAT",
		},
		{
			name:        "Invalid landline format",
			phone:       "123456",
			expectValid: false,
			expectError: "INVALID_FORMAT",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := validator.ValidatePhone(tt.phone)
			
			if result.IsValid != tt.expectValid {
				t.Errorf("ValidatePhone(%q).IsValid = %v, want %v", tt.phone, result.IsValid, tt.expectValid)
			}
			
			if result.Type != tt.expectType {
				t.Errorf("ValidatePhone(%q).Type = %v, want %v", tt.phone, result.Type, tt.expectType)
			}
			
			if !tt.expectValid && result.ErrorCode != tt.expectError {
				t.Errorf("ValidatePhone(%q).ErrorCode = %q, want %q", tt.phone, result.ErrorCode, tt.expectError)
			}
			
			if tt.expectValid && result.Cleaned == "" {
				t.Errorf("ValidatePhone(%q).Cleaned should not be empty for valid phone", tt.phone)
			}
		})
	}
}

func TestPhoneValidatorForAPI(t *testing.T) {
	validator := GetPhoneValidator()
	
	tests := []struct {
		name           string
		phone          string
		expectTel      string
		expectMobile   string
	}{
		{
			name:         "Mobile number",
			phone:        "**********0",
			expectTel:    "",
			expectMobile: "**********0",
		},
		{
			name:         "Landline number",
			phone:        "010-12345678",
			expectTel:    "010-12345678",
			expectMobile: "",
		},
		{
			name:         "Invalid number",
			phone:        "invalid",
			expectTel:    "",
			expectMobile: "",
		},
		{
			name:         "Empty number",
			phone:        "",
			expectTel:    "",
			expectMobile: "",
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tel, mobile := validator.FormatForAPI(tt.phone)
			
			if tel != tt.expectTel {
				t.Errorf("FormatForAPI(%q) tel = %q, want %q", tt.phone, tel, tt.expectTel)
			}
			
			if mobile != tt.expectMobile {
				t.Errorf("FormatForAPI(%q) mobile = %q, want %q", tt.phone, mobile, tt.expectMobile)
			}
		})
	}
}

func TestPhoneValidatorQuick(t *testing.T) {
	validator := GetPhoneValidator()
	
	tests := []struct {
		name   string
		phone  string
		expect bool
	}{
		{"Valid mobile", "**********0", true},
		{"Valid landline", "010-12345678", true},
		{"Invalid mobile", "***********", false},
		{"Empty phone", "", false},
		{"Invalid format", "abcd", false},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := validator.ValidatePhoneQuick(tt.phone)
			if result != tt.expect {
				t.Errorf("ValidatePhoneQuick(%q) = %v, want %v", tt.phone, result, tt.expect)
			}
		})
	}
}

func BenchmarkPhoneValidator(b *testing.B) {
	validator := GetPhoneValidator()
	testPhones := []string{
		"**********0",
		"010-12345678",
		"************",
		"invalid",
		"",
	}
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		phone := testPhones[i%len(testPhones)]
		validator.ValidatePhone(phone)
	}
}

func BenchmarkPhoneValidatorQuick(b *testing.B) {
	validator := GetPhoneValidator()
	testPhones := []string{
		"**********0",
		"010-12345678",
		"************",
		"invalid",
		"",
	}
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		phone := testPhones[i%len(testPhones)]
		validator.ValidatePhoneQuick(phone)
	}
}