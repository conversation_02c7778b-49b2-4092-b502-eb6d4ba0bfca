package util

import (
	"bytes"
	"encoding/json"
	"fmt"
	"reflect"
	"sync"
	"time"

	"github.com/json-iterator/go"
	"go.uber.org/zap"
)

// JSONOptimizer JSON性能优化器
// 基于第一性原理设计：高性能序列化 + 内存池 + 缓存策略
type JSONOptimizer struct {
	logger *zap.Logger
	
	// 使用json-iterator替代标准库，性能提升2-3倍
	jsonAPI jsoniter.API
	
	// 字节缓冲池，减少内存分配
	bufferPool sync.Pool
	
	// 编码器池，复用编码器实例
	encoderPool sync.Pool
	
	// 解码器池，复用解码器实例
	decoderPool sync.Pool
	
	// 性能统计
	stats *JSONStats
	mutex sync.RWMutex
}

// JSONStats JSON操作统计
type JSONStats struct {
	MarshalCount   int64
	UnmarshalCount int64
	MarshalTime    time.Duration
	UnmarshalTime  time.Duration
	PoolHits       int64
	PoolMisses     int64
}

// NewJSONOptimizer 创建JSON优化器
func NewJSONOptimizer(logger *zap.Logger) *JSONOptimizer {
	// 配置json-iterator以获得最佳性能
	jsonAPI := jsoniter.Config{
		EscapeHTML:             false, // 不转义HTML，提升性能
		SortMapKeys:           false, // 不排序map键，提升性能
		ValidateJsonRawMessage: false, // 不验证RawMessage，提升性能
		UseNumber:             false, // 使用float64而不是Number类型
	}.Froze()
	
	optimizer := &JSONOptimizer{
		logger:  logger,
		jsonAPI: jsonAPI,
		stats:   &JSONStats{},
	}
	
	// 初始化缓冲池
	optimizer.bufferPool = sync.Pool{
		New: func() interface{} {
			return bytes.NewBuffer(make([]byte, 0, 1024)) // 预分配1KB
		},
	}
	
	// 初始化编码器池
	optimizer.encoderPool = sync.Pool{
		New: func() interface{} {
			buf := optimizer.getBuffer()
			return jsonAPI.NewEncoder(buf)
		},
	}
	
	// 初始化解码器池
	optimizer.decoderPool = sync.Pool{
		New: func() interface{} {
			return jsonAPI.NewDecoder(bytes.NewReader(nil))
		},
	}
	
	return optimizer
}

// getBuffer 从池中获取缓冲区
func (o *JSONOptimizer) getBuffer() *bytes.Buffer {
	buf := o.bufferPool.Get().(*bytes.Buffer)
	buf.Reset()
	o.recordPoolHit()
	return buf
}

// putBuffer 将缓冲区放回池中
func (o *JSONOptimizer) putBuffer(buf *bytes.Buffer) {
	// 如果缓冲区太大，不放回池中，避免内存泄漏
	if buf.Cap() > 64*1024 { // 64KB
		return
	}
	o.bufferPool.Put(buf)
}

// FastMarshal 高性能JSON序列化
func (o *JSONOptimizer) FastMarshal(v interface{}) ([]byte, error) {
	startTime := time.Now()
	defer func() {
		o.recordMarshalTime(time.Since(startTime))
	}()
	
	// 处理nil值
	if v == nil {
		return []byte("null"), nil
	}
	
	// 处理简单类型，避免反射开销
	switch val := v.(type) {
	case string:
		return o.jsonAPI.Marshal(val)
	case int, int8, int16, int32, int64:
		return o.jsonAPI.Marshal(val)
	case uint, uint8, uint16, uint32, uint64:
		return o.jsonAPI.Marshal(val)
	case float32, float64:
		return o.jsonAPI.Marshal(val)
	case bool:
		return o.jsonAPI.Marshal(val)
	case []byte:
		// 对于字节数组，直接返回（假设已经是JSON）
		if json.Valid(val) {
			return val, nil
		}
		return o.jsonAPI.Marshal(string(val))
	}
	
	// 使用缓冲池进行复杂对象序列化
	buf := o.getBuffer()
	defer o.putBuffer(buf)
	
	encoder := o.jsonAPI.NewEncoder(buf)
	if err := encoder.Encode(v); err != nil {
		return nil, fmt.Errorf("JSON序列化失败: %w", err)
	}
	
	// 移除编码器添加的换行符
	result := buf.Bytes()
	if len(result) > 0 && result[len(result)-1] == '\n' {
		result = result[:len(result)-1]
	}
	
	// 复制结果，因为buf会被重用
	output := make([]byte, len(result))
	copy(output, result)
	
	return output, nil
}

// FastUnmarshal 高性能JSON反序列化
func (o *JSONOptimizer) FastUnmarshal(data []byte, v interface{}) error {
	startTime := time.Now()
	defer func() {
		o.recordUnmarshalTime(time.Since(startTime))
	}()
	
	// 验证输入
	if len(data) == 0 {
		return fmt.Errorf("JSON数据为空")
	}
	
	if !json.Valid(data) {
		return fmt.Errorf("无效的JSON数据")
	}
	
	// 使用json-iterator进行反序列化
	return o.jsonAPI.Unmarshal(data, v)
}

// MarshalToString 序列化为字符串（常用于数据库存储）
func (o *JSONOptimizer) MarshalToString(v interface{}) (string, error) {
	data, err := o.FastMarshal(v)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

// UnmarshalFromString 从字符串反序列化
func (o *JSONOptimizer) UnmarshalFromString(s string, v interface{}) error {
	return o.FastUnmarshal([]byte(s), v)
}

// SafeMarshal 安全序列化，出错时返回默认值
func (o *JSONOptimizer) SafeMarshal(v interface{}, defaultValue string) string {
	result, err := o.MarshalToString(v)
	if err != nil {
		o.logger.Warn("JSON序列化失败，使用默认值",
			zap.Error(err),
			zap.String("default", defaultValue),
			zap.String("type", reflect.TypeOf(v).String()))
		return defaultValue
	}
	return result
}

// SafeUnmarshal 安全反序列化，出错时不修改目标对象
func (o *JSONOptimizer) SafeUnmarshal(data []byte, v interface{}) error {
	if len(data) == 0 {
		return nil // 空数据不处理
	}
	
	err := o.FastUnmarshal(data, v)
	if err != nil {
		o.logger.Warn("JSON反序列化失败",
			zap.Error(err),
			zap.String("data", string(data)),
			zap.String("type", reflect.TypeOf(v).String()))
	}
	return err
}

// CompactJSON 压缩JSON，移除不必要的空白字符
func (o *JSONOptimizer) CompactJSON(data []byte) ([]byte, error) {
	buf := o.getBuffer()
	defer o.putBuffer(buf)
	
	if err := json.Compact(buf, data); err != nil {
		return nil, fmt.Errorf("JSON压缩失败: %w", err)
	}
	
	result := make([]byte, buf.Len())
	copy(result, buf.Bytes())
	return result, nil
}

// PrettyJSON 格式化JSON，用于调试和日志
func (o *JSONOptimizer) PrettyJSON(data []byte) ([]byte, error) {
	buf := o.getBuffer()
	defer o.putBuffer(buf)
	
	if err := json.Indent(buf, data, "", "  "); err != nil {
		return nil, fmt.Errorf("JSON格式化失败: %w", err)
	}
	
	result := make([]byte, buf.Len())
	copy(result, buf.Bytes())
	return result, nil
}

// ValidateJSON 验证JSON格式
func (o *JSONOptimizer) ValidateJSON(data []byte) bool {
	return json.Valid(data)
}

// recordMarshalTime 记录序列化时间
func (o *JSONOptimizer) recordMarshalTime(duration time.Duration) {
	o.mutex.Lock()
	defer o.mutex.Unlock()
	o.stats.MarshalCount++
	o.stats.MarshalTime += duration
}

// recordUnmarshalTime 记录反序列化时间
func (o *JSONOptimizer) recordUnmarshalTime(duration time.Duration) {
	o.mutex.Lock()
	defer o.mutex.Unlock()
	o.stats.UnmarshalCount++
	o.stats.UnmarshalTime += duration
}

// recordPoolHit 记录池命中
func (o *JSONOptimizer) recordPoolHit() {
	o.mutex.Lock()
	defer o.mutex.Unlock()
	o.stats.PoolHits++
}

// GetStats 获取性能统计
func (o *JSONOptimizer) GetStats() JSONStats {
	o.mutex.RLock()
	defer o.mutex.RUnlock()
	return *o.stats
}

// ResetStats 重置统计信息
func (o *JSONOptimizer) ResetStats() {
	o.mutex.Lock()
	defer o.mutex.Unlock()
	o.stats = &JSONStats{}
}

// GetAveragePerformance 获取平均性能指标
func (o *JSONOptimizer) GetAveragePerformance() map[string]interface{} {
	stats := o.GetStats()
	
	result := map[string]interface{}{
		"marshal_count":   stats.MarshalCount,
		"unmarshal_count": stats.UnmarshalCount,
		"pool_hits":       stats.PoolHits,
		"pool_misses":     stats.PoolMisses,
	}
	
	if stats.MarshalCount > 0 {
		result["avg_marshal_time_ns"] = stats.MarshalTime.Nanoseconds() / stats.MarshalCount
	}
	
	if stats.UnmarshalCount > 0 {
		result["avg_unmarshal_time_ns"] = stats.UnmarshalTime.Nanoseconds() / stats.UnmarshalCount
	}
	
	if stats.PoolHits+stats.PoolMisses > 0 {
		result["pool_hit_rate"] = float64(stats.PoolHits) / float64(stats.PoolHits+stats.PoolMisses)
	}
	
	return result
}

// 全局JSON优化器实例
var globalJSONOptimizer *JSONOptimizer
var globalJSONOptimizerOnce sync.Once

// GetGlobalJSONOptimizer 获取全局JSON优化器
func GetGlobalJSONOptimizer() *JSONOptimizer {
	globalJSONOptimizerOnce.Do(func() {
		logger, _ := zap.NewProduction()
		globalJSONOptimizer = NewJSONOptimizer(logger)
	})
	return globalJSONOptimizer
}

// 便捷函数，使用全局优化器

// OptimizedMarshal 优化的JSON序列化
func OptimizedMarshal(v interface{}) ([]byte, error) {
	return GetGlobalJSONOptimizer().FastMarshal(v)
}

// OptimizedUnmarshal 优化的JSON反序列化
func OptimizedUnmarshal(data []byte, v interface{}) error {
	return GetGlobalJSONOptimizer().FastUnmarshal(data, v)
}

// OptimizedMarshalToString 优化的序列化为字符串
func OptimizedMarshalToString(v interface{}) (string, error) {
	return GetGlobalJSONOptimizer().MarshalToString(v)
}

// SafeJSONMarshal 安全的JSON序列化
func SafeJSONMarshal(v interface{}, defaultValue string) string {
	return GetGlobalJSONOptimizer().SafeMarshal(v, defaultValue)
}
