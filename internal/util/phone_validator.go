package util

import (
	"regexp"
	"strings"
	"sync"
)

// PhoneType 电话类型
type PhoneType int

const (
	PhoneTypeUnknown  PhoneType = iota
	PhoneTypeMobile             // 手机号
	PhoneTypeLandline           // 固定电话
)

// PhoneValidationResult 验证结果
type PhoneValidationResult struct {
	IsValid   bool      `json:"is_valid"`
	Type      PhoneType `json:"type"`
	Cleaned   string    `json:"cleaned"`
	ErrorCode string    `json:"error_code,omitempty"`
}

// PhoneValidator 高性能电话号码验证器
type PhoneValidator struct {
	mobileRegex   *regexp.Regexp
	landlineRegex *regexp.Regexp
	cleanerRegex  *regexp.Regexp
	maxLength     int
}

var (
	// 单例模式，避免重复创建
	validatorInstance *PhoneValidator
	validatorOnce     sync.Once
)

// GetPhoneValidator 获取验证器单例
func GetPhoneValidator() *PhoneValidator {
	validatorOnce.Do(func() {
		validatorInstance = &PhoneValidator{
			// 预编译正则表达式，避免运行时编译
			mobileRegex: regexp.MustCompile(`^1[3-9]\d{9}$`),
			// 🔥 修改：支持更宽松的固定电话格式，包括无区号的7-8位固定电话
			landlineRegex: regexp.MustCompile(`^(0\d{2,3}[\-\s]?\d{7,8}|400[\-\s]?\d{3}[\-\s]?\d{4}|800[\-\s]?\d{3}[\-\s]?\d{4}|95\d{3,4}|10\d{3}|\d{7,8})$`),
			cleanerRegex:  regexp.MustCompile(`[\s\-\+\(\)]+`),
			maxLength:     50, // 防止恶意输入
		}
	})
	return validatorInstance
}

// ValidatePhone 验证电话号码（生产级实现）
func (v *PhoneValidator) ValidatePhone(phone string) PhoneValidationResult {
	// 1. 快速长度检查（避免处理超长字符串）
	if len(phone) == 0 {
		return PhoneValidationResult{
			IsValid:   false,
			Type:      PhoneTypeUnknown,
			ErrorCode: "EMPTY_PHONE",
		}
	}

	if len(phone) > v.maxLength {
		return PhoneValidationResult{
			IsValid:   false,
			Type:      PhoneTypeUnknown,
			ErrorCode: "PHONE_TOO_LONG",
		}
	}

	// 2. 高效清理（单次正则替换）
	cleaned := v.cleanerRegex.ReplaceAllString(phone, "")

	// 3. 移除国际前缀（避免多次替换）
	if strings.HasPrefix(cleaned, "+86") {
		cleaned = cleaned[3:]
	} else if strings.HasPrefix(cleaned, "86") && len(cleaned) > 11 {
		cleaned = cleaned[2:]
	}

	// 4. 快速手机号检查（最常见情况优先）
	if len(cleaned) == 11 && v.mobileRegex.MatchString(cleaned) {
		return PhoneValidationResult{
			IsValid: true,
			Type:    PhoneTypeMobile,
			Cleaned: cleaned,
		}
	}

	// 5. 固定电话检查
	if v.landlineRegex.MatchString(phone) {
		return PhoneValidationResult{
			IsValid: true,
			Type:    PhoneTypeLandline,
			Cleaned: phone, // 保留原格式用于API
		}
	}

	return PhoneValidationResult{
		IsValid:   false,
		Type:      PhoneTypeUnknown,
		ErrorCode: "INVALID_FORMAT",
	}
}

// FormatForAPI 为API调用格式化电话号码
func (v *PhoneValidator) FormatForAPI(phone string) (tel string, mobile string) {
	result := v.ValidatePhone(phone)

	if !result.IsValid {
		return "", ""
	}

	switch result.Type {
	case PhoneTypeMobile:
		return "", result.Cleaned
	case PhoneTypeLandline:
		return result.Cleaned, ""
	default:
		return "", ""
	}
}

// ValidatePhoneQuick 快速验证（仅返回布尔值，最高性能）
func (v *PhoneValidator) ValidatePhoneQuick(phone string) bool {
	if len(phone) == 0 || len(phone) > v.maxLength {
		return false
	}

	cleaned := v.cleanerRegex.ReplaceAllString(phone, "")
	if strings.HasPrefix(cleaned, "+86") {
		cleaned = cleaned[3:]
	}

	// 快速路径：直接检查最常见的手机号格式
	if len(cleaned) == 11 && cleaned[0] == '1' && cleaned[1] >= '3' && cleaned[1] <= '9' {
		return true
	}

	return v.landlineRegex.MatchString(phone)
}
