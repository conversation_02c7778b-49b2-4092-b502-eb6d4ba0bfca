package util

import (
	"fmt"
	"regexp"
	"strings"
	"unicode/utf8"
)

// 🔒 查询参数验证器 - 防止SQL注入和恶意输入

// QueryValidator 查询验证器
type QueryValidator struct {
	// 允许的字符正则表达式
	allowedCharsRegex *regexp.Regexp
	// 危险关键词列表
	dangerousKeywords []string
	// 最大长度限制
	maxLength int
}

// NewQueryValidator 创建查询验证器
func NewQueryValidator() *QueryValidator {
	// 允许字母、数字、中文、常见符号
	// 简化版本：允许所有非控制字符，在应用层做更细致的验证
	allowedCharsRegex := regexp.MustCompile(`^[^\x00-\x1f\x7f<>'";&|`+"`"+`\\]*$`)
	
	// 危险SQL关键词
	dangerousKeywords := []string{
		"SELECT", "INSERT", "UPDATE", "DELETE", "DROP", "CREATE", "ALTER", "EXEC", "EXECUTE",
		"UNION", "SCRIPT", "JAVASCRIPT", "VBSCRIPT", "ONLOAD", "ONERROR", "EVAL",
		"--", "/*", "*/", ";", "xp_", "sp_", "WAITFOR", "BENCHMARK",
	}

	return &QueryValidator{
		allowedCharsRegex: allowedCharsRegex,
		dangerousKeywords: dangerousKeywords,
		maxLength:         200, // 默认最大长度
	}
}

// ValidateSearchKeyword 验证搜索关键词
func (v *QueryValidator) ValidateSearchKeyword(keyword string) error {
	if keyword == "" {
		return nil
	}

	// 长度检查
	if utf8.RuneCountInString(keyword) > v.maxLength {
		return fmt.Errorf("搜索关键词长度超过限制（最大%d字符）", v.maxLength)
	}

	// 字符检查
	if !v.allowedCharsRegex.MatchString(keyword) {
		return fmt.Errorf("搜索关键词包含非法字符")
	}

	// 危险关键词检查
	upperKeyword := strings.ToUpper(keyword)
	for _, dangerous := range v.dangerousKeywords {
		if strings.Contains(upperKeyword, dangerous) {
			return fmt.Errorf("搜索关键词包含敏感内容")
		}
	}

	return nil
}

// ValidateOrderNumber 验证订单号格式
func (v *QueryValidator) ValidateOrderNumber(orderNo string) error {
	if orderNo == "" {
		return nil
	}

	// 长度检查（订单号通常不超过50字符）
	if len(orderNo) > 50 {
		return fmt.Errorf("订单号长度超过限制")
	}

	// 订单号格式检查：只允许字母数字和连字符
	orderNoRegex := regexp.MustCompile(`^[a-zA-Z0-9\-_]+$`)
	if !orderNoRegex.MatchString(orderNo) {
		return fmt.Errorf("订单号格式不正确")
	}

	return nil
}

// ValidateUserID 验证用户ID
func (v *QueryValidator) ValidateUserID(userID string) error {
	if userID == "" {
		return fmt.Errorf("用户ID不能为空")
	}

	// UUID格式检查
	uuidRegex := regexp.MustCompile(`^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$`)
	if !uuidRegex.MatchString(userID) {
		return fmt.Errorf("用户ID格式不正确")
	}

	return nil
}

// ValidatePaginationParams 验证分页参数
func (v *QueryValidator) ValidatePaginationParams(page, pageSize int) error {
	if page < 1 {
		return fmt.Errorf("页码必须大于0")
	}

	if pageSize < 1 || pageSize > 100 {
		return fmt.Errorf("页大小必须在1-100之间")
	}

	// 检查深度分页
	if page > 1000 {
		return fmt.Errorf("页码过大，建议使用游标分页")
	}

	return nil
}

// ValidateStatusFilter 验证状态筛选参数
func (v *QueryValidator) ValidateStatusFilter(status string) error {
	if status == "" {
		return nil
	}

	allowedStatuses := []string{
		"pending", "completed", "failed", "processing", 
		"cancelled", "shipped", "delivered",
	}

	for _, allowed := range allowedStatuses {
		if status == allowed {
			return nil
		}
	}

	return fmt.Errorf("状态值不合法")
}

// ValidateTransactionType 验证交易类型
func (v *QueryValidator) ValidateTransactionType(txType string) error {
	if txType == "" {
		return nil
	}

	allowedTypes := []string{
		"user_deposit", "admin_deposit", "order_pre_charge", 
		"order_cancel_refund", "balance_adjustment",
	}

	for _, allowed := range allowedTypes {
		if txType == allowed {
			return nil
		}
	}

	return fmt.Errorf("交易类型不合法")
}

// ValidateStatusFilter 验证状态筛选参数（全局便捷函数）
func ValidateStatusFilter(status string) error {
	return GlobalQueryValidator.ValidateStatusFilter(status)
}

// ValidateTransactionTypeGlobal 全局交易类型验证
func ValidateTransactionTypeGlobal(txType string) error {
	return GlobalQueryValidator.ValidateTransactionType(txType)
}

// ValidateTimeRange 验证时间范围
func (v *QueryValidator) ValidateTimeRange(startTime, endTime string) error {
	if startTime == "" && endTime == "" {
		return nil
	}

	// 时间格式检查
	timeRegex := regexp.MustCompile(`^\d{4}-\d{2}-\d{2}(\s\d{2}:\d{2}:\d{2})?$`)
	
	if startTime != "" && !timeRegex.MatchString(startTime) {
		return fmt.Errorf("开始时间格式不正确")
	}

	if endTime != "" && !timeRegex.MatchString(endTime) {
		return fmt.Errorf("结束时间格式不正确")
	}

	return nil
}

// SanitizeInput 清理输入内容
func (v *QueryValidator) SanitizeInput(input string) string {
	if input == "" {
		return ""
	}

	// 移除前后空格
	input = strings.TrimSpace(input)

	// 移除多余空格
	spaceRegex := regexp.MustCompile(`\s+`)
	input = spaceRegex.ReplaceAllString(input, " ")

	// 转义特殊字符
	input = strings.ReplaceAll(input, "'", "''")
	input = strings.ReplaceAll(input, "\\", "\\\\")

	return input
}

// ValidateQueryComplexity 验证查询复杂度
func (v *QueryValidator) ValidateQueryComplexity(filters map[string]interface{}) error {
	// 限制同时使用的筛选条件数量
	activeFilters := 0
	for _, value := range filters {
		if value != nil && value != "" {
			activeFilters++
		}
	}

	if activeFilters > 10 {
		return fmt.Errorf("筛选条件过多，请简化查询")
	}

	return nil
}

// GlobalQueryValidator 全局验证器实例
var GlobalQueryValidator = NewQueryValidator()

// 便捷验证函数

// ValidateSearchKeywordGlobal 全局搜索关键词验证
func ValidateSearchKeywordGlobal(keyword string) error {
	return GlobalQueryValidator.ValidateSearchKeyword(keyword)
}

// ValidateOrderNumberGlobal 全局订单号验证
func ValidateOrderNumberGlobal(orderNo string) error {
	return GlobalQueryValidator.ValidateOrderNumber(orderNo)
}

// ValidateUserIDGlobal 全局用户ID验证
func ValidateUserIDGlobal(userID string) error {
	return GlobalQueryValidator.ValidateUserID(userID)
}

// ValidatePaginationParamsGlobal 全局分页参数验证
func ValidatePaginationParamsGlobal(page, pageSize int) error {
	return GlobalQueryValidator.ValidatePaginationParams(page, pageSize)
}

// SanitizeInputGlobal 全局输入清理
func SanitizeInputGlobal(input string) string {
	return GlobalQueryValidator.SanitizeInput(input)
}