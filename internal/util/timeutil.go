package util

import "time"

// Beijing 表示东八区固定时区（CST Zhong Guo）。
var Beijing = time.FixedZone("CST", 8*3600)

// ToBeijing 将时间转换为北京时间（保留原始时间点）。
// 如果 t 已经在北京时区，则直接返回原值。
func ToBeijing(t time.Time) time.Time {
	if t.Location() == Beijing {
		return t
	}
	return t.In(Beijing)
}

// NowBeijing 返回北京时间的当前时间
func NowBeijing() time.Time {
	return time.Now().In(Beijing)
}

// ParseTimeInBeijing 解析时间字符串并返回北京时间
func ParseTimeInBeijing(layout, value string) (time.Time, error) {
	t, err := time.Parse(layout, value)
	if err != nil {
		return time.Time{}, err
	}
	return ToBeijing(t), nil
}

// FormatBeijingTime 格式化北京时间为字符串
func FormatBeijingTime(t time.Time, layout string) string {
	return ToBeijing(t).Format(layout)
}

// BeijingTimestamp 返回北京时间的Unix时间戳（秒）
func BeijingTimestamp() int64 {
	return NowBeijing().Unix()
}

// BeijingTimestampMilli 返回北京时间的Unix时间戳（毫秒）
func BeijingTimestampMilli() int64 {
	return NowBeijing().UnixMilli()
}

// FormatBeijingTimeDefault 使用默认格式格式化北京时间
func FormatBeijingTimeDefault(t time.Time) string {
	return FormatBeijingTime(t, "2006-01-02 15:04:05")
}

// TodayBeijing 返回北京时间的今天开始时间
func TodayBeijing() time.Time {
	now := NowBeijing()
	return time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, Beijing)
}

// TomorrowBeijing 返回北京时间的明天开始时间
func TomorrowBeijing() time.Time {
	return TodayBeijing().Add(24 * time.Hour)
}

// IsBeijingTime 检查时间是否为北京时区
func IsBeijingTime(t time.Time) bool {
	return t.Location() == Beijing
}

// GetBeijingTimeZoneInfo 返回北京时区信息
func GetBeijingTimeZoneInfo() string {
	return "Asia/Shanghai (Beijing Time, UTC+8)"
}

// FixTimezoneForDisplay 修复显示时的时区问题
// 数据库迁移到TIMESTAMP WITHOUT TIME ZONE后，所有时间都是北京时间值
// 只需要为其添加正确的时区标记
func FixTimezoneForDisplay(t time.Time) time.Time {
	// 数据库现在存储的是TIMESTAMP WITHOUT TIME ZONE
	// 所有时间值都是北京时间，只需要添加北京时区标记
	return time.Date(t.Year(), t.Month(), t.Day(), 
					t.Hour(), t.Minute(), t.Second(), 
					t.Nanosecond(), Beijing)
}

// EnsureBeijingTimezone 确保时间具有北京时区标记
// 用于处理从数据库读取的TIMESTAMP WITHOUT TIME ZONE
func EnsureBeijingTimezone(t time.Time) time.Time {
	// 如果时间没有时区信息（Local或UTC），则标记为北京时区
	if t.Location() == time.Local || t.Location() == time.UTC || t.Location().String() == "" {
		return time.Date(t.Year(), t.Month(), t.Day(), 
						t.Hour(), t.Minute(), t.Second(), 
						t.Nanosecond(), Beijing)
	}
	return t
}
