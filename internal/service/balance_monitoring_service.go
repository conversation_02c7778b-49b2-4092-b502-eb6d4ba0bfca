package service

import (
	"fmt"
	"sync"
	"sync/atomic"
	"time"

	"go.uber.org/zap"
)

// DegradationConfig 降级配置
type DegradationConfig struct {
	Enabled               bool          `json:"enabled"`
	ErrorRateThreshold    float64       `json:"error_rate_threshold"`
	ResponseTimeThreshold time.Duration `json:"response_time_threshold"`
	DegradationMode       string        `json:"degradation_mode"`
	AutoRecovery          bool          `json:"auto_recovery"`
	RecoveryInterval      time.Duration `json:"recovery_interval"`
}

// BalanceMonitoringService 余额监控服务 - 企业级监控解决方案
type BalanceMonitoringService struct {
	logger                *zap.Logger
	metrics               *BalanceMetrics
	balanceAlertManager   *BalanceAlertManager
	balanceCircuitBreaker *BalanceCircuitBreaker
	degradationConfig     *DegradationConfig
	emergencyMode         bool
	emergencyModeMutex    sync.RWMutex
	healthCheckInterval   time.Duration
	stopChan              chan struct{}
}

// BalanceMetrics 余额服务专用指标
type BalanceMetrics struct {
	// 操作计数器
	TotalOperations      int64 `json:"total_operations"`
	SuccessfulOperations int64 `json:"successful_operations"`
	FailedOperations     int64 `json:"failed_operations"`
	VersionConflictCount int64 `json:"version_conflict_count"`

	// 性能指标
	AverageResponseTime time.Duration `json:"average_response_time"`
	P95ResponseTime     time.Duration `json:"p95_response_time"`
	P99ResponseTime     time.Duration `json:"p99_response_time"`

	// 队列指标
	QueueSize         int64         `json:"queue_size"`
	QueueWaitTime     time.Duration `json:"queue_wait_time"`
	ProcessingWorkers int           `json:"processing_workers"`

	// 错误率
	ErrorRate    float64 `json:"error_rate"`
	ConflictRate float64 `json:"conflict_rate"`

	// 时间窗口
	WindowStart    time.Time     `json:"window_start"`
	WindowDuration time.Duration `json:"window_duration"`

	// 响应时间分布
	responseTimeSamples []time.Duration
	samplesMutex        sync.RWMutex
}

// BalanceAlertManager 余额专用告警管理器
type BalanceAlertManager struct {
	logger                 *zap.Logger
	webhookURLs            []string
	balanceAlertThresholds *BalanceAlertThresholds
	activeBalanceAlerts    map[string]*BalanceAlert
	alertsMutex            sync.RWMutex
	cooldownPeriod         time.Duration
}

// BalanceAlertThresholds 余额告警阈值
type BalanceAlertThresholds struct {
	ErrorRateThreshold    float64       `json:"error_rate_threshold"`    // 错误率阈值 5%
	ConflictRateThreshold float64       `json:"conflict_rate_threshold"` // 冲突率阈值 10%
	ResponseTimeThreshold time.Duration `json:"response_time_threshold"` // 响应时间阈值 1s
	QueueSizeThreshold    int64         `json:"queue_size_threshold"`    // 队列大小阈值 1000
	ConsecutiveFailures   int           `json:"consecutive_failures"`    // 连续失败次数 10
}

// BalanceAlert 余额告警信息
type BalanceAlert struct {
	ID         string                 `json:"id"`
	Type       BalanceAlertType       `json:"type"`
	Level      BalanceAlertLevel      `json:"level"`
	Message    string                 `json:"message"`
	Metrics    map[string]interface{} `json:"metrics"`
	CreatedAt  time.Time              `json:"created_at"`
	ResolvedAt *time.Time             `json:"resolved_at,omitempty"`
	Status     BalanceAlertStatus     `json:"status"`
}

// BalanceAlertType 余额告警类型
type BalanceAlertType string

const (
	BalanceAlertTypeErrorRate      BalanceAlertType = "balance_error_rate"
	BalanceAlertTypeConflictRate   BalanceAlertType = "balance_conflict_rate"
	BalanceAlertTypeResponseTime   BalanceAlertType = "balance_response_time"
	BalanceAlertTypeQueueOverflow  BalanceAlertType = "balance_queue_overflow"
	BalanceAlertTypeCircuitBreaker BalanceAlertType = "balance_circuit_breaker"
	BalanceAlertTypeEmergencyMode  BalanceAlertType = "balance_emergency_mode"
)

// BalanceAlertLevel 余额告警级别
type BalanceAlertLevel string

const (
	BalanceAlertLevelInfo     BalanceAlertLevel = "info"
	BalanceAlertLevelWarning  BalanceAlertLevel = "warning"
	BalanceAlertLevelError    BalanceAlertLevel = "error"
	BalanceAlertLevelCritical BalanceAlertLevel = "critical"
)

// BalanceAlertStatus 余额告警状态
type BalanceAlertStatus string

const (
	BalanceAlertStatusActive   BalanceAlertStatus = "active"
	BalanceAlertStatusResolved BalanceAlertStatus = "resolved"
)

// BalanceCircuitBreaker 余额专用熔断器
type BalanceCircuitBreaker struct {
	name             string
	state            BalanceCircuitState
	failureThreshold int64
	successThreshold int64
	timeout          time.Duration
	lastFailureTime  time.Time
	mutex            sync.RWMutex
	logger           *zap.Logger
}

// BalanceCircuitState 余额熔断器状态
type BalanceCircuitState int

const (
	BalanceCircuitStateClosed BalanceCircuitState = iota
	BalanceCircuitStateOpen
	BalanceCircuitStateHalfOpen
)

// NewBalanceMonitoringService 创建余额监控服务
func NewBalanceMonitoringService(logger *zap.Logger) *BalanceMonitoringService {
	service := &BalanceMonitoringService{
		logger:                logger,
		metrics:               NewBalanceMetrics(),
		balanceAlertManager:   NewBalanceAlertManager(logger),
		balanceCircuitBreaker: NewBalanceCircuitBreaker("balance_service", logger),
		degradationConfig:     getDefaultBalanceDegradationConfig(),
		healthCheckInterval:   30 * time.Second,
		stopChan:              make(chan struct{}),
	}

	// 启动监控任务
	go service.startMonitoring()

	return service
}

// NewBalanceMetrics 创建余额指标
func NewBalanceMetrics() *BalanceMetrics {
	return &BalanceMetrics{
		WindowStart:         time.Now(),
		WindowDuration:      1 * time.Minute,
		responseTimeSamples: make([]time.Duration, 0, 1000),
	}
}

// NewBalanceAlertManager 创建余额告警管理器
func NewBalanceAlertManager(logger *zap.Logger) *BalanceAlertManager {
	return &BalanceAlertManager{
		logger:                 logger,
		webhookURLs:            []string{},
		balanceAlertThresholds: getDefaultBalanceAlertThresholds(),
		activeBalanceAlerts:    make(map[string]*BalanceAlert),
		cooldownPeriod:         5 * time.Minute,
	}
}

// NewBalanceCircuitBreaker 创建余额熔断器
func NewBalanceCircuitBreaker(name string, logger *zap.Logger) *BalanceCircuitBreaker {
	return &BalanceCircuitBreaker{
		name:             name,
		state:            BalanceCircuitStateClosed,
		failureThreshold: 10,
		successThreshold: 5,
		timeout:          60 * time.Second,
		logger:           logger,
	}
}

// getDefaultBalanceAlertThresholds 获取默认余额告警阈值
func getDefaultBalanceAlertThresholds() *BalanceAlertThresholds {
	return &BalanceAlertThresholds{
		ErrorRateThreshold:    0.05,            // 5%
		ConflictRateThreshold: 0.10,            // 10%
		ResponseTimeThreshold: 1 * time.Second, // 1秒
		QueueSizeThreshold:    1000,            // 1000个操作
		ConsecutiveFailures:   10,              // 连续10次失败
	}
}

// getDefaultBalanceDegradationConfig 获取默认降级配置
func getDefaultBalanceDegradationConfig() *DegradationConfig {
	return &DegradationConfig{
		Enabled:               true,
		ErrorRateThreshold:    0.15,            // 15%
		ResponseTimeThreshold: 2 * time.Second, // 2秒
		DegradationMode:       "fast_fail",
		AutoRecovery:          true,
		RecoveryInterval:      5 * time.Minute,
	}
}

// RecordOperation 记录操作指标
func (m *BalanceMetrics) RecordOperation(success bool, responseTime time.Duration, isConflict bool) {
	atomic.AddInt64(&m.TotalOperations, 1)

	if success {
		atomic.AddInt64(&m.SuccessfulOperations, 1)
	} else {
		atomic.AddInt64(&m.FailedOperations, 1)
	}

	if isConflict {
		atomic.AddInt64(&m.VersionConflictCount, 1)
	}

	// 记录响应时间样本
	m.samplesMutex.Lock()
	m.responseTimeSamples = append(m.responseTimeSamples, responseTime)

	// 保持样本数量在合理范围内
	if len(m.responseTimeSamples) > 1000 {
		m.responseTimeSamples = m.responseTimeSamples[100:] // 保留最新900个样本
	}
	m.samplesMutex.Unlock()

	// 更新平均响应时间（简化计算）
	m.AverageResponseTime = (m.AverageResponseTime + responseTime) / 2
}

// CalculateRates 计算错误率和冲突率
func (m *BalanceMetrics) CalculateRates() {
	total := atomic.LoadInt64(&m.TotalOperations)
	if total == 0 {
		m.ErrorRate = 0
		m.ConflictRate = 0
		return
	}

	failed := atomic.LoadInt64(&m.FailedOperations)
	conflicts := atomic.LoadInt64(&m.VersionConflictCount)

	m.ErrorRate = float64(failed) / float64(total)
	m.ConflictRate = float64(conflicts) / float64(total)
}

// startMonitoring 启动监控任务
func (s *BalanceMonitoringService) startMonitoring() {
	ticker := time.NewTicker(s.healthCheckInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			s.performHealthCheck()
		case <-s.stopChan:
			return
		}
	}
}

// performHealthCheck 执行健康检查
func (s *BalanceMonitoringService) performHealthCheck() {
	s.logger.Debug("🔍 执行余额服务健康检查")

	// 计算指标
	s.metrics.CalculateRates()

	// 检查告警条件
	s.checkAlerts()

	// 检查熔断器状态
	s.balanceCircuitBreaker.checkState()
}

// checkAlerts 检查告警条件
func (s *BalanceMonitoringService) checkAlerts() {
	thresholds := s.balanceAlertManager.balanceAlertThresholds

	// 检查错误率
	if s.metrics.ErrorRate > thresholds.ErrorRateThreshold {
		s.balanceAlertManager.TriggerBalanceAlert(BalanceAlertTypeErrorRate, BalanceAlertLevelError,
			fmt.Sprintf("余额服务错误率过高: %.2f%% (阈值: %.2f%%)",
				s.metrics.ErrorRate*100, thresholds.ErrorRateThreshold*100),
			map[string]interface{}{
				"current_rate": s.metrics.ErrorRate,
				"threshold":    thresholds.ErrorRateThreshold,
			})
	}

	// 检查冲突率
	if s.metrics.ConflictRate > thresholds.ConflictRateThreshold {
		s.balanceAlertManager.TriggerBalanceAlert(BalanceAlertTypeConflictRate, BalanceAlertLevelWarning,
			fmt.Sprintf("余额版本冲突率过高: %.2f%% (阈值: %.2f%%)",
				s.metrics.ConflictRate*100, thresholds.ConflictRateThreshold*100),
			map[string]interface{}{
				"current_rate": s.metrics.ConflictRate,
				"threshold":    thresholds.ConflictRateThreshold,
			})
	}
}

// TriggerBalanceAlert 触发余额告警
func (am *BalanceAlertManager) TriggerBalanceAlert(alertType BalanceAlertType, level BalanceAlertLevel, message string, metrics map[string]interface{}) {
	am.alertsMutex.Lock()
	defer am.alertsMutex.Unlock()

	alertID := string(alertType)

	// 检查是否已有相同类型的活跃告警
	if existingAlert, exists := am.activeBalanceAlerts[alertID]; exists {
		if existingAlert.Status == BalanceAlertStatusActive {
			// 更新现有告警
			existingAlert.Message = message
			existingAlert.Metrics = metrics
			return
		}
	}

	// 创建新告警
	alert := &BalanceAlert{
		ID:        alertID,
		Type:      alertType,
		Level:     level,
		Message:   message,
		Metrics:   metrics,
		CreatedAt: time.Now(),
		Status:    BalanceAlertStatusActive,
	}

	am.activeBalanceAlerts[alertID] = alert

	am.logger.Error("🚨 触发余额告警",
		zap.String("alert_type", string(alertType)),
		zap.String("level", string(level)),
		zap.String("message", message))
}

// checkState 检查熔断器状态
func (cb *BalanceCircuitBreaker) checkState() {
	cb.mutex.RLock()
	defer cb.mutex.RUnlock()

	if cb.state == BalanceCircuitStateOpen {
		cb.logger.Debug("余额熔断器状态检查",
			zap.String("name", cb.name),
			zap.String("state", "open"),
			zap.Duration("since_failure", time.Since(cb.lastFailureTime)))
	}
}

// GetMetrics 获取监控指标
func (s *BalanceMonitoringService) GetMetrics() *BalanceMetrics {
	return s.metrics
}

// GetActiveAlerts 获取活跃告警
func (s *BalanceMonitoringService) GetActiveAlerts() []*BalanceAlert {
	s.balanceAlertManager.alertsMutex.RLock()
	defer s.balanceAlertManager.alertsMutex.RUnlock()

	alerts := make([]*BalanceAlert, 0)
	for _, alert := range s.balanceAlertManager.activeBalanceAlerts {
		if alert.Status == BalanceAlertStatusActive {
			alerts = append(alerts, alert)
		}
	}
	return alerts
}

// HealthCheck 健康检查
func (s *BalanceMonitoringService) HealthCheck() error {
	if s.IsEmergencyMode() {
		return fmt.Errorf("余额服务处于紧急模式")
	}

	if s.balanceCircuitBreaker.state == BalanceCircuitStateOpen {
		return fmt.Errorf("余额熔断器开启")
	}

	return nil
}

// IsEmergencyMode 检查是否在紧急模式
func (s *BalanceMonitoringService) IsEmergencyMode() bool {
	s.emergencyModeMutex.RLock()
	defer s.emergencyModeMutex.RUnlock()
	return s.emergencyMode
}

// WithMonitoring 在监控保护下执行余额操作
func (s *BalanceMonitoringService) WithMonitoring(operation func() error) error {
	startTime := time.Now()

	// 执行操作
	err := operation()

	responseTime := time.Since(startTime)
	isConflict := err != nil && isBalanceVersionConflictError(err)

	// 记录指标
	s.metrics.RecordOperation(err == nil, responseTime, isConflict)

	return err
}

// Stop 停止监控服务
func (s *BalanceMonitoringService) Stop() {
	s.logger.Info("⏹️ 停止余额监控服务")
	close(s.stopChan)
}

// isBalanceVersionConflictError 检查是否是余额版本冲突错误
func isBalanceVersionConflictError(err error) bool {
	if err == nil {
		return false
	}
	errStr := err.Error()
	return len(errStr) >= 8 && (balanceStringContains(errStr, "余额版本冲突") || balanceStringContains(errStr, "version conflict"))
}

func balanceStringContains(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
