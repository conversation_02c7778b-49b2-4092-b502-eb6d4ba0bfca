package service

import (
	"context"
	"fmt"
	"strings"

	"go.uber.org/zap"

	"github.com/your-org/go-kuaidi/internal/adapter"
)

// ProviderConfigService 供应商配置服务接口
type ProviderConfigService interface {
	// GetKuaidi100Config 获取快递100配置
	GetKuaidi100Config(ctx context.Context) (*adapter.Kuaidi100Config, error)

	// GetYidaConfig 获取易达配置
	GetYidaConfig(ctx context.Context) (*adapter.YidaConfig, error)

	// GetYuntongConfig 获取云通配置
	GetYuntongConfig(ctx context.Context) (*adapter.YuntongConfig, error)

	// GetCainiaoConfig 获取菜鸟裹裹配置
	GetCainiaoConfig(ctx context.Context) (*adapter.CainiaoConfig, error)

	// GetKuaidiNiaoConfig 获取快递鸟配置
	GetKuaidiNiaoConfig(ctx context.Context) (*adapter.KuaidiNiaoConfig, error)

	// GetKuaidi100WorkorderConfig 获取快递100工单配置
	GetKuaidi100WorkorderConfig(ctx context.Context) (*adapter.Kuaidi100Config, error)

	// GetYidaWorkorderConfig 获取易达工单配置
	GetYidaWorkorderConfig(ctx context.Context) (*adapter.YidaConfig, error)

	// GetYuntongWorkorderConfig 获取云通工单配置
	GetYuntongWorkorderConfig(ctx context.Context) (*adapter.YuntongConfig, error)

	// GetKuaidiniaoWorkorderConfig 获取快递鸟工单配置
	GetKuaidiniaoWorkorderConfig(ctx context.Context) (*adapter.KuaidiNiaoConfig, error)

	// IsProviderEnabled 检查供应商是否启用
	IsProviderEnabled(ctx context.Context, providerName string) (bool, error)

	// IsWorkorderEnabled 检查工单是否启用
	IsWorkorderEnabled(ctx context.Context, providerName string) (bool, error)

	// ValidateProviderConfig 验证供应商配置完整性
	ValidateProviderConfig(ctx context.Context, providerName string) error
}

// DefaultProviderConfigService 默认供应商配置服务实现
type DefaultProviderConfigService struct {
	systemConfigService SystemConfigServiceInterface
	logger              *zap.Logger
}

// NewProviderConfigService 创建供应商配置服务
func NewProviderConfigService(systemConfigService SystemConfigServiceInterface, logger *zap.Logger) ProviderConfigService {
	return &DefaultProviderConfigService{
		systemConfigService: systemConfigService,
		logger:              logger,
	}
}

// GetKuaidi100Config 获取快递100配置
func (s *DefaultProviderConfigService) GetKuaidi100Config(ctx context.Context) (*adapter.Kuaidi100Config, error) {
	// 检查是否启用
	enabled, err := s.IsProviderEnabled(ctx, "kuaidi100")
	if err != nil {
		return nil, fmt.Errorf("检查快递100启用状态失败: %w", err)
	}
	if !enabled {
		return nil, fmt.Errorf("快递100供应商未启用，请在数据库 system_configs 表中设置 provider_kuaidi100_enabled = true")
	}

	// 获取必需配置
	apiKey := s.systemConfigService.GetConfigWithDefault("provider_kuaidi100.api_key", "")
	secret := s.systemConfigService.GetConfigWithDefault("provider_kuaidi100.secret", "")
	customer := s.systemConfigService.GetConfigWithDefault("provider_kuaidi100.customer", "")
	baseURL := s.systemConfigService.GetConfigWithDefault("provider_kuaidi100.base_url", "https://poll.kuaidi100.com/order/borderapi.do")

	// 验证必需配置
	if apiKey == "" {
		return nil, fmt.Errorf("快递100 API密钥未配置，请在数据库 system_configs 表中设置 provider_kuaidi100.api_key")
	}
	if secret == "" {
		return nil, fmt.Errorf("快递100 Secret密钥未配置，请在数据库 system_configs 表中设置 provider_kuaidi100.secret")
	}
	if customer == "" {
		return nil, fmt.Errorf("快递100客户编号未配置，请在数据库 system_configs 表中设置 provider_kuaidi100.customer")
	}

	// 获取可选配置
	timeout := s.systemConfigService.GetConfigAsIntWithDefault("provider_kuaidi100.timeout", 10)

	config := &adapter.Kuaidi100Config{
		Key:      apiKey,
		Secret:   secret,
		Customer: customer,
		BaseURL:  baseURL,
		Timeout:  timeout,
	}

	s.logger.Info("快递100配置获取成功",
		zap.String("base_url", baseURL),
		zap.Int("timeout", timeout))

	return config, nil
}

// GetKuaidi100ConfigForTracking 获取快递100配置用于物流跟踪（不检查启用状态）
// 物流跟踪功能不应受供应商启用/禁用开关影响，因为用户有权查询已下订单的物流状态
func (s *DefaultProviderConfigService) GetKuaidi100ConfigForTracking(ctx context.Context) (*adapter.Kuaidi100Config, error) {
	// 获取必需配置（不检查启用状态）
	apiKey := s.systemConfigService.GetConfigWithDefault("provider_kuaidi100.api_key", "")
	if apiKey == "" {
		return nil, fmt.Errorf("快递100 API Key未配置，请在数据库 system_configs 表中设置 provider_kuaidi100.api_key")
	}

	secret := s.systemConfigService.GetConfigWithDefault("provider_kuaidi100.secret", "")
	if secret == "" {
		return nil, fmt.Errorf("快递100 Secret未配置，请在数据库 system_configs 表中设置 provider_kuaidi100.secret")
	}

	customer := s.systemConfigService.GetConfigWithDefault("provider_kuaidi100.customer", "")
	if customer == "" {
		return nil, fmt.Errorf("快递100 Customer未配置，请在数据库 system_configs 表中设置 provider_kuaidi100.customer")
	}

	// 获取可选配置
	baseURL := s.systemConfigService.GetConfigWithDefault("provider_kuaidi100.base_url", "https://api.kuaidi100.com")
	timeout := s.systemConfigService.GetConfigAsIntWithDefault("provider_kuaidi100.timeout", 10)

	config := &adapter.Kuaidi100Config{
		Key:      apiKey,
		Secret:   secret,
		Customer: customer,
		BaseURL:  baseURL,
		Timeout:  timeout,
	}

	s.logger.Info("快递100配置获取成功（物流跟踪）",
		zap.String("base_url", baseURL),
		zap.Int("timeout", timeout))

	return config, nil
}

// GetYidaConfig 获取易达配置
func (s *DefaultProviderConfigService) GetYidaConfig(ctx context.Context) (*adapter.YidaConfig, error) {
	// 检查是否启用
	enabled, err := s.IsProviderEnabled(ctx, "yida")
	if err != nil {
		return nil, fmt.Errorf("检查易达启用状态失败: %w", err)
	}
	if !enabled {
		return nil, fmt.Errorf("易达供应商未启用，请在数据库 system_configs 表中设置 provider_yida_enabled = true")
	}

	// 获取必需配置
	username := s.systemConfigService.GetConfigWithDefault("provider_yida.username", "")
	privateKey := s.systemConfigService.GetConfigWithDefault("provider_yida.private_key", "")
	baseURL := s.systemConfigService.GetConfigWithDefault("provider_yida.base_url", "https://www.yida178.cn/prod-api/thirdApi/execute")

	// 验证必需配置
	if username == "" {
		return nil, fmt.Errorf("易达用户名未配置，请在数据库 system_configs 表中设置 provider_yida.username")
	}
	if privateKey == "" {
		return nil, fmt.Errorf("易达私钥未配置，请在数据库 system_configs 表中设置 provider_yida.private_key")
	}

	// 获取可选配置
	timeout := s.systemConfigService.GetConfigAsIntWithDefault("provider_yida.timeout", 10)

	config := &adapter.YidaConfig{
		AppKey:    username,
		AppSecret: privateKey,
		BaseURL:   baseURL,
		Timeout:   timeout,
	}

	s.logger.Info("易达配置获取成功",
		zap.String("base_url", baseURL),
		zap.Int("timeout", timeout))

	return config, nil
}

// GetYuntongConfig 获取云通配置
func (s *DefaultProviderConfigService) GetYuntongConfig(ctx context.Context) (*adapter.YuntongConfig, error) {
	// 检查是否启用
	enabled, err := s.IsProviderEnabled(ctx, "yuntong")
	if err != nil {
		return nil, fmt.Errorf("检查云通启用状态失败: %w", err)
	}
	if !enabled {
		return nil, fmt.Errorf("云通供应商未启用，请在数据库 system_configs 表中设置 provider_yuntong_enabled = true")
	}

	// 获取必需配置
	businessID := s.systemConfigService.GetConfigWithDefault("provider_yuntong.business_id", "")
	apiKey := s.systemConfigService.GetConfigWithDefault("provider_yuntong.api_key", "")
	baseURL := s.systemConfigService.GetConfigWithDefault("provider_yuntong.base_url", "https://open.yuntongzy.com/express/api/OrderService")

	// 验证必需配置
	if businessID == "" {
		return nil, fmt.Errorf("云通商户ID未配置，请在数据库 system_configs 表中设置 provider_yuntong.business_id")
	}
	if apiKey == "" {
		return nil, fmt.Errorf("云通API密钥未配置，请在数据库 system_configs 表中设置 provider_yuntong.api_key")
	}

	// 获取可选配置
	timeout := s.systemConfigService.GetConfigAsIntWithDefault("provider_yuntong.timeout", 10)

	config := &adapter.YuntongConfig{
		EBusinessID: businessID,
		ApiKey:      apiKey,
		BaseURL:     baseURL,
		Timeout:     timeout,
	}

	s.logger.Info("云通配置获取成功",
		zap.String("base_url", baseURL),
		zap.Int("timeout", timeout))

	return config, nil
}

// GetCainiaoConfig 获取菜鸟裹裹配置
func (s *DefaultProviderConfigService) GetCainiaoConfig(ctx context.Context) (*adapter.CainiaoConfig, error) {
	// 检查是否启用
	enabled, err := s.IsProviderEnabled(ctx, "cainiao")
	if err != nil {
		return nil, fmt.Errorf("检查菜鸟裹裹启用状态失败: %w", err)
	}
	if !enabled {
		return nil, fmt.Errorf("菜鸟裹裹供应商未启用，请在数据库 system_configs 表中设置 provider_cainiao.enabled = true")
	}

	// 获取必需配置
	accessCode := s.systemConfigService.GetConfigWithDefault("provider_cainiao.access_code", "")
	logisticProviderID := s.systemConfigService.GetConfigWithDefault("provider_cainiao.logistic_provider_id", "")
	cpCode := s.systemConfigService.GetConfigWithDefault("provider_cainiao.cp_code", "")
	secretKey := s.systemConfigService.GetConfigWithDefault("provider_cainiao.secret_key", "")

	// 验证必需配置
	if accessCode == "" {
		return nil, fmt.Errorf("菜鸟裹裹接入编码未配置，请在数据库 system_configs 表中设置 provider_cainiao.access_code")
	}
	if logisticProviderID == "" {
		return nil, fmt.Errorf("菜鸟裹裹资源code未配置，请在数据库 system_configs 表中设置 provider_cainiao.logistic_provider_id")
	}
	if cpCode == "" {
		return nil, fmt.Errorf("菜鸟裹裹CP编号未配置，请在数据库 system_configs 表中设置 provider_cainiao.cp_code")
	}
	if secretKey == "" {
		return nil, fmt.Errorf("菜鸟裹裹密钥未配置，请在数据库 system_configs 表中设置 provider_cainiao.secret_key")
	}

	// 获取可选配置
	msgTypePrefix := s.systemConfigService.GetConfigWithDefault("provider_cainiao.msg_type_prefix", "GUOGUO_")
	environment := s.systemConfigService.GetConfigWithDefault("provider_cainiao.environment", "sandbox")
	timeout := s.systemConfigService.GetConfigAsIntWithDefault("provider_cainiao.timeout", 30)

	// 根据环境选择API地址
	var baseURL string
	if environment == "production" {
		baseURL = s.systemConfigService.GetConfigWithDefault("provider_cainiao.base_url_production", "https://link.cainiao.com/gateway/link.do")
	} else {
		baseURL = s.systemConfigService.GetConfigWithDefault("provider_cainiao.base_url_sandbox", "http://linkdaily.tbsandbox.com/bifrost/link.do")
	}

	config := &adapter.CainiaoConfig{
		AccessCode:         accessCode,
		LogisticProviderID: logisticProviderID,
		CPCode:             cpCode,
		SecretKey:          secretKey,
		MsgTypePrefix:      msgTypePrefix,
		BaseURL:            baseURL,
		Environment:        environment,
		Timeout:            timeout,
	}

	s.logger.Info("菜鸟裹裹配置获取成功",
		zap.String("environment", environment),
		zap.String("base_url", baseURL),
		zap.Int("timeout", timeout))

	return config, nil
}

// GetKuaidiNiaoConfig 获取快递鸟配置
func (s *DefaultProviderConfigService) GetKuaidiNiaoConfig(ctx context.Context) (*adapter.KuaidiNiaoConfig, error) {
	// 检查是否启用
	enabled, err := s.IsProviderEnabled(ctx, "kuaidiniao")
	if err != nil {
		return nil, fmt.Errorf("检查快递鸟启用状态失败: %w", err)
	}
	if !enabled {
		return nil, fmt.Errorf("快递鸟供应商未启用，请在数据库 system_configs 表中设置 provider.kuaidiniao_enabled = true")
	}

	// 获取必需配置
	eBusinessID := s.systemConfigService.GetConfigWithDefault("provider_kuaidiniao.e_business_id", "")
	apiKey := s.systemConfigService.GetConfigWithDefault("provider_kuaidiniao.api_key", "")
	baseURL := s.systemConfigService.GetConfigWithDefault("provider_kuaidiniao.base_url", "")

	// 验证必需配置
	if eBusinessID == "" {
		return nil, fmt.Errorf("快递鸟商户ID未配置，请在数据库 system_configs 表中设置 provider_kuaidiniao.e_business_id")
	}
	if apiKey == "" {
		return nil, fmt.Errorf("快递鸟API密钥未配置，请在数据库 system_configs 表中设置 provider_kuaidiniao.api_key")
	}
	if baseURL == "" {
		return nil, fmt.Errorf("快递鸟API基础URL未配置，请在数据库 system_configs 表中设置 provider_kuaidiniao.base_url")
	}

	// 获取可选配置
	environment := s.systemConfigService.GetConfigWithDefault("provider_kuaidiniao.environment", "production")
	timeout := s.systemConfigService.GetConfigAsIntWithDefault("provider_kuaidiniao.timeout", 10)

	config := &adapter.KuaidiNiaoConfig{
		EBusinessID: eBusinessID,
		ApiKey:      apiKey,
		BaseURL:     baseURL,
		Environment: environment,
		Timeout:     timeout,
	}

	s.logger.Info("快递鸟配置获取成功",
		zap.String("base_url", baseURL),
		zap.String("environment", environment),
		zap.Int("timeout", timeout))

	return config, nil
}

// IsProviderEnabled 检查供应商是否启用
func (s *DefaultProviderConfigService) IsProviderEnabled(ctx context.Context, providerName string) (bool, error) {
	configKey := fmt.Sprintf("provider.%s_enabled", providerName)
	enabled := s.systemConfigService.GetConfigAsBoolWithDefault(configKey, false)

	s.logger.Debug("检查供应商启用状态",
		zap.String("provider", providerName),
		zap.Bool("enabled", enabled))

	return enabled, nil
}

// GetKuaidi100WorkorderConfig 获取快递100工单配置
func (s *DefaultProviderConfigService) GetKuaidi100WorkorderConfig(ctx context.Context) (*adapter.Kuaidi100Config, error) {
	// 检查是否启用
	enabled, err := s.IsWorkorderEnabled(ctx, "kuaidi100")
	if err != nil {
		return nil, fmt.Errorf("检查快递100工单启用状态失败: %w", err)
	}
	if !enabled {
		return nil, fmt.Errorf("快递100工单未启用，请在数据库 system_configs 表中设置 workorder.kuaidi100_enabled = true")
	}

	// 获取必需配置
	apiKey := s.systemConfigService.GetConfigWithDefault("workorder_kuaidi100.api_key", "")
	secret := s.systemConfigService.GetConfigWithDefault("workorder_kuaidi100.secret", "")
	customer := s.systemConfigService.GetConfigWithDefault("workorder_kuaidi100.customer", "")
	baseURL := s.systemConfigService.GetConfigWithDefault("workorder_kuaidi100.base_url", "https://api.kuaidi100.com")

	// 验证必需配置
	if apiKey == "" {
		return nil, fmt.Errorf("快递100工单API密钥未配置，请在数据库 system_configs 表中设置 workorder_kuaidi100.api_key")
	}
	if secret == "" {
		return nil, fmt.Errorf("快递100工单Secret密钥未配置，请在数据库 system_configs 表中设置 workorder_kuaidi100.secret")
	}
	if customer == "" {
		return nil, fmt.Errorf("快递100工单客户编号未配置，请在数据库 system_configs 表中设置 workorder_kuaidi100.customer")
	}

	// 获取可选配置
	timeout := s.systemConfigService.GetConfigAsIntWithDefault("workorder_kuaidi100.timeout", 30)

	config := &adapter.Kuaidi100Config{
		Key:      apiKey,
		Secret:   secret,
		Customer: customer,
		BaseURL:  baseURL,
		Timeout:  timeout,
	}

	s.logger.Info("快递100工单配置获取成功",
		zap.String("base_url", baseURL),
		zap.Int("timeout", timeout))

	return config, nil
}

// GetYidaWorkorderConfig 获取易达工单配置
func (s *DefaultProviderConfigService) GetYidaWorkorderConfig(ctx context.Context) (*adapter.YidaConfig, error) {
	// 检查是否启用
	enabled, err := s.IsWorkorderEnabled(ctx, "yida")
	if err != nil {
		return nil, fmt.Errorf("检查易达工单启用状态失败: %w", err)
	}
	if !enabled {
		return nil, fmt.Errorf("易达工单未启用，请在数据库 system_configs 表中设置 workorder.yida_enabled = true")
	}

	// 获取必需配置
	username := s.systemConfigService.GetConfigWithDefault("workorder_yida.username", "")
	privateKey := s.systemConfigService.GetConfigWithDefault("workorder_yida.private_key", "")
	baseURL := s.systemConfigService.GetConfigWithDefault("workorder_yida.base_url", "https://www.yida178.cn/prod-api/thirdApi/execute")

	// 验证必需配置
	if username == "" {
		return nil, fmt.Errorf("易达工单用户名未配置，请在数据库 system_configs 表中设置 workorder_yida.username")
	}
	if privateKey == "" {
		return nil, fmt.Errorf("易达工单私钥未配置，请在数据库 system_configs 表中设置 workorder_yida.private_key")
	}

	// 获取可选配置
	timeout := s.systemConfigService.GetConfigAsIntWithDefault("workorder_yida.timeout", 30)

	config := &adapter.YidaConfig{
		AppKey:    username,
		AppSecret: privateKey,
		BaseURL:   baseURL,
		Timeout:   timeout,
	}

	s.logger.Info("易达工单配置获取成功",
		zap.String("base_url", baseURL),
		zap.Int("timeout", timeout))

	return config, nil
}

// GetYuntongWorkorderConfig 获取云通工单配置
func (s *DefaultProviderConfigService) GetYuntongWorkorderConfig(ctx context.Context) (*adapter.YuntongConfig, error) {
	// 检查是否启用
	enabled, err := s.IsWorkorderEnabled(ctx, "yuntong")
	if err != nil {
		return nil, fmt.Errorf("检查云通工单启用状态失败: %w", err)
	}
	if !enabled {
		return nil, fmt.Errorf("云通工单未启用，请在数据库 system_configs 表中设置 workorder.yuntong_enabled = true")
	}

	// 获取必需配置
	businessID := s.systemConfigService.GetConfigWithDefault("workorder_yuntong.business_id", "")
	apiKey := s.systemConfigService.GetConfigWithDefault("workorder_yuntong.api_key", "")
	baseURL := s.systemConfigService.GetConfigWithDefault("workorder_yuntong.base_url", "https://open.yuntongzy.com/express/api/OrderService")

	// 验证必需配置
	if businessID == "" {
		return nil, fmt.Errorf("云通工单商户ID未配置，请在数据库 system_configs 表中设置 workorder_yuntong.business_id")
	}
	if apiKey == "" {
		return nil, fmt.Errorf("云通工单API密钥未配置，请在数据库 system_configs 表中设置 workorder_yuntong.api_key")
	}

	// 获取可选配置
	timeout := s.systemConfigService.GetConfigAsIntWithDefault("workorder_yuntong.timeout", 30)

	config := &adapter.YuntongConfig{
		EBusinessID: businessID,
		ApiKey:      apiKey,
		BaseURL:     baseURL,
		Timeout:     timeout,
	}

	s.logger.Info("云通工单配置获取成功",
		zap.String("base_url", baseURL),
		zap.Int("timeout", timeout))

	return config, nil
}

// GetKuaidiniaoWorkorderConfig 获取快递鸟工单配置
func (s *DefaultProviderConfigService) GetKuaidiniaoWorkorderConfig(ctx context.Context) (*adapter.KuaidiNiaoConfig, error) {
	// 检查是否启用
	enabled, err := s.IsWorkorderEnabled(ctx, "kuaidiniao")
	if err != nil {
		return nil, fmt.Errorf("检查快递鸟工单启用状态失败: %w", err)
	}
	if !enabled {
		return nil, fmt.Errorf("快递鸟工单未启用，请在数据库 system_configs 表中设置 workorder.kuaidiniao_enabled = true")
	}

	// 获取必需配置
	eBusinessID := s.systemConfigService.GetConfigWithDefault("workorder.kuaidiniao_e_business_id", "")
	apiKey := s.systemConfigService.GetConfigWithDefault("workorder.kuaidiniao_api_key", "")
	baseURL := s.systemConfigService.GetConfigWithDefault("workorder.kuaidiniao_base_url", "https://api.kdniao.com/Ebusiness/EbusinessOrderHandle.aspx")

	// 验证必需配置
	if eBusinessID == "" {
		return nil, fmt.Errorf("快递鸟工单商户ID未配置，请在数据库 system_configs 表中设置 workorder.kuaidiniao_e_business_id")
	}
	if apiKey == "" {
		return nil, fmt.Errorf("快递鸟工单API密钥未配置，请在数据库 system_configs 表中设置 workorder.kuaidiniao_api_key")
	}

	// 获取可选配置
	environment := s.systemConfigService.GetConfigWithDefault("workorder.kuaidiniao_environment", "production")
	timeout := s.systemConfigService.GetConfigAsIntWithDefault("workorder.kuaidiniao_timeout", 30)

	config := &adapter.KuaidiNiaoConfig{
		EBusinessID: eBusinessID,
		ApiKey:      apiKey,
		BaseURL:     baseURL,
		Environment: environment,
		Timeout:     timeout,
	}

	s.logger.Info("快递鸟工单配置获取成功",
		zap.String("base_url", baseURL),
		zap.String("environment", environment),
		zap.Int("timeout", timeout))

	return config, nil
}

// IsWorkorderEnabled 检查工单是否启用
func (s *DefaultProviderConfigService) IsWorkorderEnabled(ctx context.Context, providerName string) (bool, error) {
	configKey := fmt.Sprintf("workorder.%s_enabled", providerName)
	enabled := s.systemConfigService.GetConfigAsBoolWithDefault(configKey, false)

	s.logger.Debug("检查工单启用状态",
		zap.String("provider", providerName),
		zap.Bool("enabled", enabled))

	return enabled, nil
}

// ValidateProviderConfig 验证供应商配置完整性
func (s *DefaultProviderConfigService) ValidateProviderConfig(ctx context.Context, providerName string) error {
	switch strings.ToLower(providerName) {
	case "kuaidi100":
		_, err := s.GetKuaidi100Config(ctx)
		return err
	case "yida":
		_, err := s.GetYidaConfig(ctx)
		return err
	case "yuntong":
		_, err := s.GetYuntongConfig(ctx)
		return err
	case "cainiao":
		_, err := s.GetCainiaoConfig(ctx)
		return err
	case "kuaidiniao":
		_, err := s.GetKuaidiNiaoConfig(ctx)
		return err
	default:
		return fmt.Errorf("不支持的供应商: %s", providerName)
	}
}
