package service

import (
	"context"
	"fmt"
	"time"

	"github.com/your-org/go-kuaidi/internal/repository"
	"go.uber.org/zap"
)

// EnhancedHealthChecker 增强的健康检查器
// 🔥 包含取消中订单数量检查
type EnhancedHealthChecker struct {
	orderRepository repository.OrderRepository
	timeoutService  *CancellationTimeoutService
	logger          *zap.Logger
}

// NewEnhancedHealthChecker 创建增强的健康检查器
func NewEnhancedHealthChecker(
	orderRepo repository.OrderRepository,
	timeoutService *CancellationTimeoutService,
	logger *zap.Logger,
) *EnhancedHealthChecker {
	return &EnhancedHealthChecker{
		orderRepository: orderRepo,
		timeoutService:  timeoutService,
		logger:          logger,
	}
}

// HealthStatus 健康状态
type HealthStatus struct {
	Status    string                 `json:"status"`    // OK, WARNING, ERROR
	Message   string                 `json:"message"`   // 状态描述
	Details   map[string]interface{} `json:"details"`   // 详细信息
	Timestamp time.Time              `json:"timestamp"` // 检查时间
}

// CheckOverallHealth 检查整体健康状态
func (h *EnhancedHealthChecker) CheckOverallHealth(ctx context.Context) *HealthStatus {
	h.logger.Info("🔍 开始整体健康检查")
	
	details := make(map[string]interface{})
	overallStatus := "OK"
	var messages []string

	// 1. 检查取消中订单数量
	cancellingStatus := h.CheckCancellingOrdersHealth(ctx)
	details["cancelling_orders"] = cancellingStatus
	
	if cancellingStatus.Status == "WARNING" {
		overallStatus = "WARNING"
		messages = append(messages, cancellingStatus.Message)
	} else if cancellingStatus.Status == "ERROR" {
		overallStatus = "ERROR"
		messages = append(messages, cancellingStatus.Message)
	}

	// 2. 检查超时订单
	timeoutStatus := h.CheckTimeoutOrdersHealth(ctx)
	details["timeout_orders"] = timeoutStatus
	
	if timeoutStatus.Status == "ERROR" {
		overallStatus = "ERROR"
		messages = append(messages, timeoutStatus.Message)
	} else if timeoutStatus.Status == "WARNING" && overallStatus == "OK" {
		overallStatus = "WARNING"
		messages = append(messages, timeoutStatus.Message)
	}

	// 3. 检查供应商健康状态
	providerStatus := h.CheckProviderHealth(ctx)
	details["providers"] = providerStatus

	// 构建最终消息
	var finalMessage string
	if len(messages) == 0 {
		finalMessage = "系统健康状态良好"
	} else {
		finalMessage = fmt.Sprintf("发现 %d 个问题: %v", len(messages), messages)
	}

	return &HealthStatus{
		Status:    overallStatus,
		Message:   finalMessage,
		Details:   details,
		Timestamp: time.Now(),
	}
}

// CheckCancellingOrdersHealth 检查取消中订单健康状态
func (h *EnhancedHealthChecker) CheckCancellingOrdersHealth(ctx context.Context) *HealthStatus {
	providers := []string{"kuaidiniao", "kuaidi100", "yida", "yuntong", "cainiao"}
	
	totalCancelling := 0
	providerDetails := make(map[string]interface{})
	
	for _, provider := range providers {
		count, err := h.orderRepository.CountCancellingOrdersByProvider(ctx, provider)
		if err != nil {
			h.logger.Error("检查供应商取消中订单失败",
				zap.String("provider", provider),
				zap.Error(err))
			continue
		}
		
		totalCancelling += count
		providerDetails[provider] = count
	}

	// 🔥 阈值配置
	warningThreshold := 20  // 警告阈值
	errorThreshold := 50    // 错误阈值

	status := "OK"
	message := fmt.Sprintf("取消中订单总数: %d", totalCancelling)

	if totalCancelling >= errorThreshold {
		status = "ERROR"
		message = fmt.Sprintf("取消中订单过多 (%d >= %d)，需要立即处理", totalCancelling, errorThreshold)
	} else if totalCancelling >= warningThreshold {
		status = "WARNING"
		message = fmt.Sprintf("取消中订单较多 (%d >= %d)，建议关注", totalCancelling, warningThreshold)
	}

	return &HealthStatus{
		Status:  status,
		Message: message,
		Details: map[string]interface{}{
			"total_cancelling": totalCancelling,
			"providers":        providerDetails,
			"thresholds": map[string]int{
				"warning": warningThreshold,
				"error":   errorThreshold,
			},
		},
		Timestamp: time.Now(),
	}
}

// CheckTimeoutOrdersHealth 检查超时订单健康状态
func (h *EnhancedHealthChecker) CheckTimeoutOrdersHealth(ctx context.Context) *HealthStatus {
	stats, err := h.timeoutService.GetTimeoutStatistics(ctx)
	if err != nil {
		h.logger.Error("获取超时统计失败", zap.Error(err))
		return &HealthStatus{
			Status:    "ERROR",
			Message:   "无法获取超时订单统计",
			Details:   map[string]interface{}{"error": err.Error()},
			Timestamp: time.Now(),
		}
	}

	// 🔥 超时订单阈值
	timeoutWarningThreshold := 10
	timeoutErrorThreshold := 30

	status := "OK"
	message := fmt.Sprintf("超时订单总数: %d", stats.TotalTimeoutOrders)

	if stats.TotalTimeoutOrders >= timeoutErrorThreshold {
		status = "ERROR"
		message = fmt.Sprintf("超时订单过多 (%d >= %d)，需要立即处理", stats.TotalTimeoutOrders, timeoutErrorThreshold)
	} else if stats.TotalTimeoutOrders >= timeoutWarningThreshold {
		status = "WARNING"
		message = fmt.Sprintf("超时订单较多 (%d >= %d)，建议关注", stats.TotalTimeoutOrders, timeoutWarningThreshold)
	}

	return &HealthStatus{
		Status:  status,
		Message: message,
		Details: map[string]interface{}{
			"timeout_statistics": stats,
			"thresholds": map[string]int{
				"warning": timeoutWarningThreshold,
				"error":   timeoutErrorThreshold,
			},
		},
		Timestamp: time.Now(),
	}
}

// CheckProviderHealth 检查供应商健康状态
func (h *EnhancedHealthChecker) CheckProviderHealth(ctx context.Context) map[string]*HealthStatus {
	providers := []string{"kuaidiniao", "kuaidi100", "yida", "yuntong", "cainiao"}
	providerHealth := make(map[string]*HealthStatus)

	for _, provider := range providers {
		providerHealth[provider] = h.checkSingleProviderHealth(ctx, provider)
	}

	return providerHealth
}

// checkSingleProviderHealth 检查单个供应商健康状态
func (h *EnhancedHealthChecker) checkSingleProviderHealth(ctx context.Context, provider string) *HealthStatus {
	// 获取该供应商的取消中订单数量
	cancellingCount, err := h.orderRepository.CountCancellingOrdersByProvider(ctx, provider)
	if err != nil {
		return &HealthStatus{
			Status:    "ERROR",
			Message:   fmt.Sprintf("无法获取 %s 供应商统计", provider),
			Details:   map[string]interface{}{"error": err.Error()},
			Timestamp: time.Now(),
		}
	}

	// 🔥 不同供应商使用不同阈值
	var warningThreshold, errorThreshold int
	switch provider {
	case "kuaidiniao":
		// 快递鸟由于API特性，阈值设置更低
		warningThreshold = 5
		errorThreshold = 15
	default:
		warningThreshold = 10
		errorThreshold = 25
	}

	status := "OK"
	message := fmt.Sprintf("%s 供应商取消中订单: %d", provider, cancellingCount)

	if cancellingCount >= errorThreshold {
		status = "ERROR"
		message = fmt.Sprintf("%s 供应商取消中订单过多 (%d >= %d)", provider, cancellingCount, errorThreshold)
	} else if cancellingCount >= warningThreshold {
		status = "WARNING"
		message = fmt.Sprintf("%s 供应商取消中订单较多 (%d >= %d)", provider, cancellingCount, warningThreshold)
	}

	return &HealthStatus{
		Status:  status,
		Message: message,
		Details: map[string]interface{}{
			"provider":         provider,
			"cancelling_count": cancellingCount,
			"thresholds": map[string]int{
				"warning": warningThreshold,
				"error":   errorThreshold,
			},
		},
		Timestamp: time.Now(),
	}
}
