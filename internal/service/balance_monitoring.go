package service

import (
	"context"
	"sync"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"

	"github.com/your-org/go-kuaidi/internal/model"
)

// BalanceCheckMetrics 余额检查监控指标
type BalanceCheckMetrics struct {
	// 余额检查总次数
	CheckTotal *prometheus.CounterVec

	// 余额检查耗时
	CheckDuration *prometheus.HistogramVec

	// 余额不足次数
	InsufficientBalance *prometheus.CounterVec

	// 余额检查错误次数
	CheckErrors *prometheus.CounterVec

	// 当前活跃的余额检查数
	ActiveChecks prometheus.Gauge
}

// NewBalanceCheckMetrics 创建余额检查监控指标
func NewBalanceCheckMetrics() *BalanceCheckMetrics {
	// 🔥 修复：使用普通的prometheus指标创建，避免重复注册错误
	return &BalanceCheckMetrics{
		CheckTotal: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "balance_check_total",
				Help: "余额检查总次数",
			},
			[]string{"user_id", "result"},
		),

		CheckDuration: prometheus.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "balance_check_duration_seconds",
				Help:    "余额检查耗时（秒）",
				Buckets: prometheus.DefBuckets,
			},
			[]string{"user_id", "result"},
		),

		InsufficientBalance: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "balance_insufficient_total",
				Help: "余额不足次数",
			},
			[]string{"user_id", "shortage_range"},
		),

		CheckErrors: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "balance_check_errors_total",
				Help: "余额检查错误次数",
			},
			[]string{"user_id", "error_type"},
		),

		ActiveChecks: prometheus.NewGauge(
			prometheus.GaugeOpts{
				Name: "balance_check_active",
				Help: "当前活跃的余额检查数",
			},
		),
	}
}

// BalanceCheckMonitor 余额检查监控器
type BalanceCheckMonitor struct {
	metrics *BalanceCheckMetrics
	logger  *zap.Logger

	// 统计数据
	mu                   sync.RWMutex
	totalChecks          int64
	insufficientCount    int64
	errorCount           int64
	lastInsufficientTime time.Time

	// 告警阈值
	alertThresholds struct {
		insufficientRateThreshold float64       // 余额不足率告警阈值
		errorRateThreshold        float64       // 错误率告警阈值
		checkWindow               time.Duration // 统计窗口
	}
}

// NewBalanceCheckMonitor 创建余额检查监控器
func NewBalanceCheckMonitor(logger *zap.Logger) *BalanceCheckMonitor {
	metrics := NewBalanceCheckMetrics()

	// 🔥 修复：尝试注册指标，如果已存在则忽略错误
	if err := prometheus.Register(metrics.CheckTotal); err != nil {
		if _, ok := err.(prometheus.AlreadyRegisteredError); !ok {
			logger.Warn("注册余额检查总数指标失败", zap.Error(err))
		}
	}
	if err := prometheus.Register(metrics.CheckDuration); err != nil {
		if _, ok := err.(prometheus.AlreadyRegisteredError); !ok {
			logger.Warn("注册余额检查耗时指标失败", zap.Error(err))
		}
	}
	if err := prometheus.Register(metrics.InsufficientBalance); err != nil {
		if _, ok := err.(prometheus.AlreadyRegisteredError); !ok {
			logger.Warn("注册余额不足指标失败", zap.Error(err))
		}
	}
	if err := prometheus.Register(metrics.CheckErrors); err != nil {
		if _, ok := err.(prometheus.AlreadyRegisteredError); !ok {
			logger.Warn("注册余额检查错误指标失败", zap.Error(err))
		}
	}
	if err := prometheus.Register(metrics.ActiveChecks); err != nil {
		if _, ok := err.(prometheus.AlreadyRegisteredError); !ok {
			logger.Warn("注册活跃检查数指标失败", zap.Error(err))
		}
	}

	monitor := &BalanceCheckMonitor{
		metrics: metrics,
		logger:  logger,
	}

	// 设置默认告警阈值
	monitor.alertThresholds.insufficientRateThreshold = 0.3 // 30%
	monitor.alertThresholds.errorRateThreshold = 0.05       // 5%
	monitor.alertThresholds.checkWindow = 5 * time.Minute

	return monitor
}

// RecordBalanceCheck 记录余额检查
func (m *BalanceCheckMonitor) RecordBalanceCheck(ctx context.Context, userID string, amount decimal.Decimal, result *model.BalanceCheckResult, duration time.Duration, err error) {
	// 🔥 修复：添加空指针检查，避免panic
	if m == nil || m.metrics == nil {
		if m != nil && m.logger != nil {
			m.logger.Debug("监控器未初始化，跳过指标记录")
		}
		return
	}

	// 更新活跃检查数
	if m.metrics.ActiveChecks != nil {
		m.metrics.ActiveChecks.Inc()
		defer m.metrics.ActiveChecks.Dec()
	}

	// 记录基础指标
	resultLabel := "success"
	if err != nil {
		resultLabel = "error"
		m.recordError(userID, err)
	} else if !result.IsSufficient {
		resultLabel = "insufficient"
		m.recordInsufficientBalance(userID, result.Shortage)
	}

	// 🔥 修复：安全地更新Prometheus指标
	if m.metrics.CheckTotal != nil {
		m.metrics.CheckTotal.WithLabelValues(userID, resultLabel).Inc()
	}
	if m.metrics.CheckDuration != nil {
		m.metrics.CheckDuration.WithLabelValues(userID, resultLabel).Observe(duration.Seconds())
	}

	// 更新内部统计
	m.mu.Lock()
	m.totalChecks++
	if err != nil {
		m.errorCount++
	} else if !result.IsSufficient {
		m.insufficientCount++
		m.lastInsufficientTime = time.Now()
	}
	m.mu.Unlock()

	// 记录详细日志
	m.logBalanceCheck(userID, amount, result, duration, err)

	// 检查告警条件
	m.checkAlerts()
}

// recordError 记录错误
func (m *BalanceCheckMonitor) recordError(userID string, err error) {
	errorType := "unknown"
	if err != nil {
		errorType = "balance_fetch_error"
		// 可以根据错误类型进行更细分的分类
	}

	m.metrics.CheckErrors.WithLabelValues(userID, errorType).Inc()
}

// recordInsufficientBalance 记录余额不足
func (m *BalanceCheckMonitor) recordInsufficientBalance(userID string, shortage decimal.Decimal) {
	// 根据不足金额范围分类
	shortageRange := m.getShortageRange(shortage)
	m.metrics.InsufficientBalance.WithLabelValues(userID, shortageRange).Inc()
}

// getShortageRange 获取不足金额范围
func (m *BalanceCheckMonitor) getShortageRange(shortage decimal.Decimal) string {
	amount := shortage.InexactFloat64()

	switch {
	case amount <= 10:
		return "0-10"
	case amount <= 50:
		return "10-50"
	case amount <= 100:
		return "50-100"
	case amount <= 500:
		return "100-500"
	default:
		return "500+"
	}
}

// logBalanceCheck 记录详细日志
func (m *BalanceCheckMonitor) logBalanceCheck(userID string, amount decimal.Decimal, result *model.BalanceCheckResult, duration time.Duration, err error) {
	if err != nil {
		m.logger.Error("余额检查失败",
			zap.String("user_id", userID),
			zap.String("requested_amount", amount.String()),
			zap.Duration("duration", duration),
			zap.Error(err))
		return
	}

	if !result.IsSufficient {
		m.logger.Warn("余额不足",
			zap.String("user_id", userID),
			zap.String("requested_amount", amount.String()),
			zap.String("current_balance", result.CurrentBalance.String()),
			zap.String("shortage", result.Shortage.String()),
			zap.Duration("duration", duration),
			zap.Time("check_time", result.CheckTime))
	} else {
		m.logger.Info("余额检查通过",
			zap.String("user_id", userID),
			zap.String("requested_amount", amount.String()),
			zap.String("current_balance", result.CurrentBalance.String()),
			zap.Duration("duration", duration))
	}
}

// checkAlerts 检查告警条件
func (m *BalanceCheckMonitor) checkAlerts() {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if m.totalChecks < 10 {
		return // 样本数量太少，不进行告警
	}

	// 检查余额不足率
	insufficientRate := float64(m.insufficientCount) / float64(m.totalChecks)
	if insufficientRate > m.alertThresholds.insufficientRateThreshold {
		m.logger.Warn("余额不足率过高告警",
			zap.Float64("insufficient_rate", insufficientRate),
			zap.Float64("threshold", m.alertThresholds.insufficientRateThreshold),
			zap.Int64("total_checks", m.totalChecks),
			zap.Int64("insufficient_count", m.insufficientCount))
	}

	// 检查错误率
	errorRate := float64(m.errorCount) / float64(m.totalChecks)
	if errorRate > m.alertThresholds.errorRateThreshold {
		m.logger.Error("余额检查错误率过高告警",
			zap.Float64("error_rate", errorRate),
			zap.Float64("threshold", m.alertThresholds.errorRateThreshold),
			zap.Int64("total_checks", m.totalChecks),
			zap.Int64("error_count", m.errorCount))
	}
}

// GetStats 获取统计信息
func (m *BalanceCheckMonitor) GetStats() map[string]interface{} {
	m.mu.RLock()
	defer m.mu.RUnlock()

	stats := map[string]interface{}{
		"total_checks":           m.totalChecks,
		"insufficient_count":     m.insufficientCount,
		"error_count":            m.errorCount,
		"last_insufficient_time": m.lastInsufficientTime,
	}

	if m.totalChecks > 0 {
		stats["insufficient_rate"] = float64(m.insufficientCount) / float64(m.totalChecks)
		stats["error_rate"] = float64(m.errorCount) / float64(m.totalChecks)
	}

	return stats
}

// ResetStats 重置统计信息
func (m *BalanceCheckMonitor) ResetStats() {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.totalChecks = 0
	m.insufficientCount = 0
	m.errorCount = 0
	m.lastInsufficientTime = time.Time{}

	m.logger.Info("余额检查监控统计信息已重置")
}
