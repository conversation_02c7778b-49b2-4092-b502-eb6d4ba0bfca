package service

import (
	"context"
	"fmt"
	"sort"
	"sync"
	"time"

	"github.com/your-org/go-kuaidi/internal/adapter"
	"github.com/your-org/go-kuaidi/internal/express"
	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/pool"
	"github.com/your-org/go-kuaidi/internal/util"
	"go.uber.org/zap"
)

// ServiceStats 服务统计信息
type ServiceStats struct {
	TotalRequests   int64         `json:"total_requests"`
	AvgResponseTime time.Duration `json:"avg_response_time"`
	ErrorCount      int64         `json:"error_count"`
}

// PriceService 价格查询服务
// 职责: 协调价格查询的核心业务逻辑
// 遵循单一职责原则 (SRP) 和依赖倒置原则 (DIP)
// 注意: 已移除所有缓存功能，专注于实时查询
type PriceService struct {
	validator           *PriceValidator
	providerCoordinator *PriceProviderCoordinator
	workerPool          *pool.WorkerPool
	stats               *ServiceStats
}

// NewPriceService 创建价格查询服务
func NewPriceService(providerManager *adapter.ProviderManager, mappingService express.ExpressMappingService, logger *zap.Logger, expressCompanyRepo express.ExpressCompanyRepository, blacklistService *RegionBlacklistService) *PriceService {
	// 初始化Worker Pool
	workerPool := pool.NewWorkerPool(20, 100) // 20个工作者，100个任务队列
	workerPool.Start()

	return &PriceService{
		validator:           NewPriceValidator(logger, expressCompanyRepo),
		providerCoordinator: NewPriceProviderCoordinator(providerManager, mappingService, blacklistService),
		workerPool:          workerPool,
		stats:               &ServiceStats{},
	}
}

// SetInterfaceAllocator 设置接口分配器
func (s *PriceService) SetInterfaceAllocator(allocator express.PriceInterfaceAllocator) {
	s.providerCoordinator.SetInterfaceAllocator(allocator)
}

// QueryPrice 统一查询价格
// 支持以下功能：
// 1. 查询单个快递公司价格：通过指定express_code参数
// 2. 查询多个快递公司价格：通过指定express_codes数组参数
// 3. 查询所有快递公司价格：通过设置query_all_companies参数为true
// 4. 比较所有供应商价格：通过设置is_compare参数为true
// 5. 指定供应商查询价格：通过指定provider参数
func (s *PriceService) QueryPrice(ctx context.Context, req *model.PriceRequest) (*model.PriceResponse, error) {
	// 验证请求参数
	if err := s.validator.ValidateRequest(req); err != nil {
		return &model.PriceResponse{
			Success: false,
			Code:    model.StatusBadRequest,
			Message: err.Error(),
		}, nil
	}

	// 预处理体积重量（只有在查询单个快递公司时才预处理）
	if s.validator.ShouldPreprocessVolumeWeight(req) {
		s.validator.PreprocessVolumeWeight(req)
	}

	// 根据查询模式选择不同的处理方式
	if req.QueryAllCompanies {
		// 查询所有快递公司价格
		return s.queryAllCompaniesPrice(ctx, req)
	} else if req.IsCompare {
		// 比较所有供应商对特定快递公司的价格
		return s.compareAllProvidersPrice(ctx, req)
	} else if len(req.ExpressCodes) > 0 {
		// 查询多个快递公司价格
		return s.queryMultipleExpressPrice(ctx, req)
	} else {
		// 自动选择最优供应商
		return s.queryBestProviderPrice(ctx, req)
	}
}

// 查询所有快递公司价格
func (s *PriceService) queryAllCompaniesPrice(ctx context.Context, req *model.PriceRequest) (*model.PriceResponse, error) {
	// 使用供应商协调器查询所有快递公司价格
	allPrices, err := s.providerCoordinator.QueryAllCompanies(ctx, req)
	if err != nil {
		return &model.PriceResponse{
			Success: false,
			Code:    model.StatusInternalServerError,
			Message: err.Error(),
		}, nil
	}

	// 🚀 过滤掉实时查价快递：现在有专门的实时查价接口
	filteredPrices := s.filterOutRealtimePriceExpress(allPrices)

	return &model.PriceResponse{
		Success: true,
		Code:    model.StatusSuccess,
		Message: "成功",
		Data:    filteredPrices,
	}, nil
}

// 比较所有供应商对特定快递公司的价格
func (s *PriceService) compareAllProvidersPrice(ctx context.Context, req *model.PriceRequest) (*model.PriceResponse, error) {
	// 使用供应商协调器查询所有供应商价格
	allPrices, err := s.providerCoordinator.QueryAllProviders(ctx, req)
	if err != nil {
		return &model.PriceResponse{
			Success: false,
			Code:    model.StatusInternalServerError,
			Message: err.Error(),
		}, nil
	}

	return &model.PriceResponse{
		Success: true,
		Code:    model.StatusSuccess,
		Message: "成功",
		Data:    allPrices,
	}, nil
}

// 自动选择最优供应商
func (s *PriceService) queryBestProviderPrice(ctx context.Context, req *model.PriceRequest) (*model.PriceResponse, error) {
	var allPrices []model.StandardizedPrice
	var err error

	// 如果指定了供应商，则只查询该供应商
	if req.Provider != "" {
		allPrices, err = s.providerCoordinator.QuerySingleProvider(ctx, req, req.Provider)
		if err != nil {
			return &model.PriceResponse{
				Success: false,
				Code:    model.StatusInternalServerError,
				Message: err.Error(),
			}, nil
		}
	} else {
		// 查询所有供应商，选择最优价格
		allPrices, err = s.providerCoordinator.QueryAllProviders(ctx, req)
		if err != nil {
			return &model.PriceResponse{
				Success: false,
				Code:    model.StatusInternalServerError,
				Message: err.Error(),
			}, nil
		}

		// 取价格最低的结果
		if len(allPrices) > 0 {
			allPrices = []model.StandardizedPrice{allPrices[0]}
		}
	}

	return &model.PriceResponse{
		Success: true,
		Code:    model.StatusSuccess,
		Message: "成功",
		Data:    allPrices,
	}, nil
}

// GetProviderManager 获取供应商管理器（用于灵活价格表服务直接调用供应商API）
func (s *PriceService) GetProviderManager() *adapter.ProviderManager {
	return s.providerCoordinator.GetProviderManager()
}

// 查询多个快递公司价格
func (s *PriceService) queryMultipleExpressPrice(ctx context.Context, req *model.PriceRequest) (*model.PriceResponse, error) {
	// 简化逻辑：直接并行查询每个快递公司的最优价格
	var wg sync.WaitGroup
	var mu sync.Mutex
	var allPrices []model.StandardizedPrice
	var errs []error

	for _, expressCode := range req.ExpressCodes {
		wg.Add(1)
		go func(code string) {
			defer wg.Done()

			// 创建单个查询请求
			singleReq := *req
			singleReq.ExpressType = code
			singleReq.ExpressCodes = nil

			// 查询价格
			resp, err := s.queryBestProviderPrice(ctx, &singleReq)
			if err != nil {
				mu.Lock()
				errs = append(errs, fmt.Errorf("查询快递公司 %s 价格失败: %w", code, err))
				mu.Unlock()
				return
			}

			// 合并结果
			if resp.Success && len(resp.Data) > 0 {
				mu.Lock()
				allPrices = append(allPrices, resp.Data...)
				mu.Unlock()
			}
		}(expressCode)
	}

	// 等待所有查询完成
	wg.Wait()

	// 如果所有查询都失败，返回错误
	if len(allPrices) == 0 && len(errs) > 0 {
		return &model.PriceResponse{
			Success: false,
			Code:    model.StatusInternalServerError,
			Message: fmt.Sprintf("所有查询都失败: %v", errs[0]),
		}, nil
	}

	// 🚀 新增：过滤掉实时查价快递价格
	filteredPrices := s.filterOutRealtimePriceExpress(allPrices)

	// 按价格从低到高排序
	sort.Slice(filteredPrices, func(i, j int) bool {
		return filteredPrices[i].Price < filteredPrices[j].Price
	})

	return &model.PriceResponse{
		Success: true,
		Code:    model.StatusSuccess,
		Message: "成功",
		Data:    filteredPrices,
	}, nil
}

// QueryPriceWithCache 价格查询（保持接口兼容性）
func (s *PriceService) QueryPriceWithCache(ctx context.Context, req *model.PriceRequest) (*model.PriceResponse, error) {
	// 直接调用QueryPrice，已移除缓存功能
	return s.QueryPrice(ctx, req)
}

// ComparePrice 价格比较
func (s *PriceService) ComparePrice(ctx context.Context, req *model.PriceRequest) (*model.PriceComparisonResponse, error) {
	// 设置比较模式
	compareReq := *req
	compareReq.IsCompare = true

	// 查询价格
	resp, err := s.QueryPrice(ctx, &compareReq)
	if err != nil {
		return &model.PriceComparisonResponse{
			Success: false,
			Code:    model.StatusInternalServerError,
			Message: err.Error(),
		}, nil
	}

	if !resp.Success {
		return &model.PriceComparisonResponse{
			Success: resp.Success,
			Code:    resp.Code,
			Message: resp.Message,
		}, nil
	}

	// 转换为比较数据格式
	var comparisonData []*model.PriceComparisonData
	for i, price := range resp.Data {
		savings := float64(0)
		if i > 0 {
			savings = price.Price - resp.Data[0].Price
		}

		comparisonData = append(comparisonData, &model.PriceComparisonData{
			Provider:    price.Provider,
			ExpressType: price.ExpressCode,
			Price:       price.Price,
			TimeLimit:   "", // StandardizedPrice没有TimeLimit字段，使用空字符串
			Ranking:     i + 1,
			Savings:     savings,
		})
	}

	return &model.PriceComparisonResponse{
		Success: true,
		Code:    model.StatusSuccess,
		Message: "成功",
		Data:    comparisonData,
	}, nil
}

// GetBestPrice 获取最优价格
func (s *PriceService) GetBestPrice(ctx context.Context, req *model.PriceRequest) (*model.PriceInfo, error) {
	// 查询价格
	resp, err := s.QueryPrice(ctx, req)
	if err != nil {
		return nil, err
	}

	if !resp.Success || len(resp.Data) == 0 {
		return nil, fmt.Errorf("没有找到可用的价格")
	}

	// 返回第一个（最优）价格
	bestPrice := resp.Data[0]
	return &model.PriceInfo{
		Provider:    bestPrice.Provider,
		ExpressType: bestPrice.ExpressCode,
		Price:       bestPrice.Price,
		TimeLimit:   "", // StandardizedPrice没有TimeLimit字段，使用空字符串
		ChannelID:   bestPrice.ChannelID,
		Available:   true,
	}, nil
}

// WarmupCache 预热缓存（已废弃，保持接口兼容性）
func (s *PriceService) WarmupCache(ctx context.Context, routes []model.RouteInfo) error {
	// 缓存功能已移除，直接返回成功
	return nil
}

// ClearCache 清除缓存（已废弃，保持接口兼容性）
func (s *PriceService) ClearCache(ctx context.Context, cacheKey string) error {
	// 缓存功能已移除，直接返回成功
	return nil
}

// GetPriceStatistics 获取价格统计
func (s *PriceService) GetPriceStatistics(ctx context.Context, req *model.PriceStatisticsRequest) (*model.PriceStatisticsResponse, error) {
	// 这里应该从数据库或统计服务获取数据
	// 目前返回模拟数据
	stats := &model.PriceStatisticsData{
		TotalQueries: 1000,
		AveragePrice: 15.50,
		MinPrice:     8.00,
		MaxPrice:     35.00,
		ProviderStats: map[string]*model.ProviderStat{
			"yida": {
				QueryCount:   400,
				AveragePrice: 14.20,
				SuccessRate:  0.95,
			},
			"yuntong": {
				QueryCount:   350,
				AveragePrice: 16.80,
				SuccessRate:  0.92,
			},
			"kuaidi100": {
				QueryCount:   250,
				AveragePrice: 15.60,
				SuccessRate:  0.88,
			},
		},
		TimeSeriesData: []*model.TimeSeriesPoint{
			{
				Timestamp: util.NowBeijing().Add(-24 * time.Hour),
				Value:     15.20,
				Count:     100,
			},
			{
				Timestamp: util.NowBeijing().Add(-12 * time.Hour),
				Value:     15.80,
				Count:     120,
			},
			{
				Timestamp: util.NowBeijing(),
				Value:     15.50,
				Count:     110,
			},
		},
	}

	return &model.PriceStatisticsResponse{
		Success: true,
		Code:    model.StatusSuccess,
		Message: "成功",
		Data:    stats,
	}, nil
}

// filterOutRealtimePriceExpress 过滤掉实时查价快递价格
// 原因：现在有专门的实时查价接口，避免在统一查价接口中重复返回实时查价快递价格
func (s *PriceService) filterOutRealtimePriceExpress(prices []model.StandardizedPrice) []model.StandardizedPrice {
	var filteredPrices []model.StandardizedPrice

	for _, price := range prices {
		// 排除京东快递（JD）和德邦快递（DBL）
		if price.ExpressCode != "JD" && price.ExpressCode != "DBL" {
			filteredPrices = append(filteredPrices, price)
		}
	}

	return filteredPrices
}
