package service

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/repository"

	"go.uber.org/zap"
)

// OrderStatusHistoryService 订单状态历史服务接口
type OrderStatusHistoryService interface {
	// RecordStatusChange 记录状态变更
	RecordStatusChange(ctx context.Context, req *model.RecordStatusChangeRequest) error

	// GetOrderStatusHistory 获取订单状态历史
	GetOrderStatusHistory(ctx context.Context, req *model.GetStatusHistoryRequest) (*model.GetStatusHistoryResponse, error)

	// GetUserOrdersStatusHistory 获取用户所有订单的状态历史
	GetUserOrdersStatusHistory(ctx context.Context, userID string, page, limit int) (*model.GetStatusHistoryResponse, error)

	// GetLatestStatusByOrderNo 获取订单最新状态记录
	GetLatestStatusByOrderNo(ctx context.Context, orderNo string) (*model.OrderStatusHistory, error)

	// CountStatusChangesByOrderNo 统计订单状态变更次数
	CountStatusChangesByOrderNo(ctx context.Context, orderNo string) (int64, error)
}

// orderStatusHistoryService 订单状态历史服务实现
type orderStatusHistoryService struct {
	db                      *sql.DB
	orderRepository         repository.OrderRepository
	statusHistoryRepository repository.OrderStatusHistoryRepository
	logger                  *zap.Logger
}

// NewOrderStatusHistoryService 创建订单状态历史服务
func NewOrderStatusHistoryService(
	db *sql.DB,
	orderRepository repository.OrderRepository,
	statusHistoryRepository repository.OrderStatusHistoryRepository,
	logger *zap.Logger,
) OrderStatusHistoryService {
	return &orderStatusHistoryService{
		db:                      db,
		orderRepository:         orderRepository,
		statusHistoryRepository: statusHistoryRepository,
		logger:                  logger,
	}
}

// RecordStatusChange 记录状态变更
func (s *orderStatusHistoryService) RecordStatusChange(ctx context.Context, req *model.RecordStatusChangeRequest) error {
	if req == nil {
		return fmt.Errorf("请求参数不能为空")
	}

	// 验证必要参数
	if req.OrderNo == "" {
		return fmt.Errorf("订单号不能为空")
	}
	if req.ToStatus == "" {
		return fmt.Errorf("目标状态不能为空")
	}

	// 构建状态历史记录
	var extraMap map[string]interface{}
	if req.Extra != nil {
		if extra, ok := req.Extra.(map[string]interface{}); ok {
			extraMap = extra
		}
	}

	history := &model.OrderStatusHistory{
		OrderNo:         req.OrderNo,
		FromStatus:      req.FromStatus,
		ToStatus:        req.ToStatus,
		Provider:        req.Provider,
		RawStatus:       req.RawStatus,
		ChangeSource:    req.ChangeSource,
		OperatorID:      req.OperatorID,
		OperatorName:    req.OperatorName,
		ChangeReason:    req.ChangeReason,
		UserID:          req.UserID,
		CustomerOrderNo: req.CustomerOrderNo,
		Extra:           extraMap,
	}

	// 保存状态历史记录
	if err := s.statusHistoryRepository.Create(ctx, history); err != nil {
		s.logger.Error("保存状态历史记录失败",
			zap.String("order_no", req.OrderNo),
			zap.String("from_status", req.FromStatus),
			zap.String("to_status", req.ToStatus),
			zap.Error(err))
		return fmt.Errorf("保存状态历史记录失败: %w", err)
	}

	s.logger.Info("状态变更记录成功",
		zap.String("order_no", req.OrderNo),
		zap.String("from_status", req.FromStatus),
		zap.String("to_status", req.ToStatus),
		zap.String("change_source", req.ChangeSource),
		zap.String("operator", req.OperatorName))

	return nil
}

// GetOrderStatusHistory 获取订单状态历史
func (s *orderStatusHistoryService) GetOrderStatusHistory(ctx context.Context, req *model.GetStatusHistoryRequest) (*model.GetStatusHistoryResponse, error) {
	if req == nil {
		return nil, fmt.Errorf("请求参数不能为空")
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Limit <= 0 {
		req.Limit = 20
	}
	if req.Limit > 100 {
		req.Limit = 100
	}

	// 查询状态历史记录
	histories, total, err := s.statusHistoryRepository.FindByConditions(ctx, req)
	if err != nil {
		s.logger.Error("查询状态历史失败",
			zap.String("order_no", req.OrderNo),
			zap.String("customer_order_no", req.CustomerOrderNo),
			zap.String("user_id", req.UserID),
			zap.Error(err))
		return nil, fmt.Errorf("查询状态历史失败: %w", err)
	}

	// 转换为响应格式
	items := make([]model.StatusHistoryItem, 0, len(histories))
	for i, history := range histories {
		item := model.StatusHistoryItem{
			ID:               history.ID,
			OrderNo:          history.OrderNo,
			CustomerOrderNo:  history.CustomerOrderNo,
			FromStatus:       history.FromStatus,
			FromStatusDesc:   s.getStatusDescription(history.FromStatus),
			ToStatus:         history.ToStatus,
			ToStatusDesc:     s.getStatusDescription(history.ToStatus),
			Provider:         history.Provider,
			ProviderName:     s.getProviderName(history.Provider),
			ChangeSource:     history.ChangeSource,
			ChangeSourceDesc: s.getChangeSourceDescription(history.ChangeSource),
			OperatorName:     history.OperatorName,
			ChangeReason:     history.ChangeReason,
			CreatedAt:        history.CreatedAt,
		}

		// 计算状态持续时间
		if i < len(histories)-1 {
			nextHistory := histories[i+1]
			duration := nextHistory.CreatedAt.Sub(history.CreatedAt)
			item.Duration = s.formatDuration(duration)
		} else {
			// 最后一个状态，计算到当前时间的持续时间
			item.Duration = "至今"
		}

		items = append(items, item)
	}

	return &model.GetStatusHistoryResponse{
		Items: items,
		Total: total,
		Page:  req.Page,
		Limit: req.Limit,
	}, nil
}

// GetUserOrdersStatusHistory 获取用户所有订单的状态历史
func (s *orderStatusHistoryService) GetUserOrdersStatusHistory(ctx context.Context, userID string, page, limit int) (*model.GetStatusHistoryResponse, error) {
	if userID == "" {
		return nil, fmt.Errorf("用户ID不能为空")
	}

	req := &model.GetStatusHistoryRequest{
		UserID: userID,
		Page:   page,
		Limit:  limit,
	}

	return s.GetOrderStatusHistory(ctx, req)
}

// GetLatestStatusByOrderNo 获取订单最新状态记录
func (s *orderStatusHistoryService) GetLatestStatusByOrderNo(ctx context.Context, orderNo string) (*model.OrderStatusHistory, error) {
	if orderNo == "" {
		return nil, fmt.Errorf("订单号不能为空")
	}

	return s.statusHistoryRepository.GetLatestByOrderNo(ctx, orderNo)
}

// CountStatusChangesByOrderNo 统计订单状态变更次数
func (s *orderStatusHistoryService) CountStatusChangesByOrderNo(ctx context.Context, orderNo string) (int64, error) {
	if orderNo == "" {
		return 0, fmt.Errorf("订单号不能为空")
	}

	return s.statusHistoryRepository.CountByOrderNo(ctx, orderNo)
}

// getStatusDescription 获取状态描述
func (s *orderStatusHistoryService) getStatusDescription(status string) string {
	// 使用统一的状态描述映射
	return model.GetOrderStatusDesc(status)
}

// getProviderName 获取供应商名称
func (s *orderStatusHistoryService) getProviderName(provider string) string {
	providerMap := map[string]string{
		"kuaidi100": "快递100",
		"yida":      "易达",
		"yuntong":   "云通",
		"jd":        "京东快递",
		"sf":        "顺丰速运",
		"yt":        "圆通速递",
		"sto":       "申通快递",
		"zto":       "中通快递",
		"yto":       "韵达速递",
	}

	if name, exists := providerMap[provider]; exists {
		return name
	}
	return provider
}

// getChangeSourceDescription 获取变更来源描述
func (s *orderStatusHistoryService) getChangeSourceDescription(source string) string {
	sourceMap := map[string]string{
		"callback": "供应商回调",
		"manual":   "手动操作",
		"system":   "系统自动",
		"api":      "API调用",
	}

	if desc, exists := sourceMap[source]; exists {
		return desc
	}
	return source
}

// formatDuration 格式化持续时间
func (s *orderStatusHistoryService) formatDuration(duration time.Duration) string {
	hours := int(duration.Hours())
	minutes := int(duration.Minutes()) % 60

	if hours > 24 {
		days := hours / 24
		hours = hours % 24
		if hours > 0 {
			return fmt.Sprintf("%d天%d小时", days, hours)
		}
		return fmt.Sprintf("%d天", days)
	}

	if hours > 0 {
		if minutes > 0 {
			return fmt.Sprintf("%d小时%d分钟", hours, minutes)
		}
		return fmt.Sprintf("%d小时", hours)
	}

	if minutes > 0 {
		return fmt.Sprintf("%d分钟", minutes)
	}

	return "不到1分钟"
}
