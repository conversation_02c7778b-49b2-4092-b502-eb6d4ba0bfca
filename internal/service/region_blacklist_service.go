package service

import (
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"
)

// RegionBlacklistService 地区黑名单服务
// 🎯 专门处理云通105错误，智能记录不支持的地区路线
type RegionBlacklistService struct {
	logger    *zap.Logger
	blacklist sync.Map // map[string]*BlacklistEntry
}

// BlacklistEntry 黑名单条目
type BlacklistEntry struct {
	Route         string    `json:"route"`           // 路线，如"上海市->安徽省"
	Provider      string    `json:"provider"`        // 供应商
	ExpressCode   string    `json:"express_code"`    // 快递公司代码
	FailureCount  int       `json:"failure_count"`   // 失败次数
	LastFailureAt time.Time `json:"last_failure_at"` // 最后失败时间
	ErrorMessage  string    `json:"error_message"`   // 错误信息
	IsBlacklisted bool      `json:"is_blacklisted"`  // 是否已加入黑名单
	CreatedAt     time.Time `json:"created_at"`      // 创建时间
}

// NewRegionBlacklistService 创建地区黑名单服务
func NewRegionBlacklistService(logger *zap.Logger) *RegionBlacklistService {
	return &RegionBlacklistService{
		logger: logger.Named("region_blacklist"),
	}
}

// RecordFailure 记录失败的地区路线
func (s *RegionBlacklistService) RecordFailure(provider, route, expressCode, errorMessage string) {
	key := s.buildKey(provider, route, expressCode)

	now := time.Now()

	// 获取或创建黑名单条目
	value, _ := s.blacklist.LoadOrStore(key, &BlacklistEntry{
		Route:       route,
		Provider:    provider,
		ExpressCode: expressCode,
		CreatedAt:   now,
	})

	entry := value.(*BlacklistEntry)

	// 更新失败信息
	entry.FailureCount++
	entry.LastFailureAt = now
	entry.ErrorMessage = errorMessage

	// 🔧 修改阈值：2次失败后自动加入黑名单（原来是3次）
	if entry.FailureCount >= 2 && !entry.IsBlacklisted {
		entry.IsBlacklisted = true
		s.logger.Info("地区路线已加入黑名单",
			zap.String("route", route),
			zap.String("provider", provider),
			zap.String("express_code", expressCode),
			zap.Int("failure_count", entry.FailureCount),
			zap.String("error_message", errorMessage))
	}

	// 存储更新后的条目
	s.blacklist.Store(key, entry)
}

// IsBlacklisted 检查地区路线是否在黑名单中
func (s *RegionBlacklistService) IsBlacklisted(provider, route, expressCode string) bool {
	key := s.buildKey(provider, route, expressCode)

	if value, exists := s.blacklist.Load(key); exists {
		entry := value.(*BlacklistEntry)
		return entry.IsBlacklisted
	}

	return false
}

// GetBlacklistEntry 获取黑名单条目详情
func (s *RegionBlacklistService) GetBlacklistEntry(provider, route, expressCode string) *BlacklistEntry {
	key := s.buildKey(provider, route, expressCode)

	if value, exists := s.blacklist.Load(key); exists {
		entry := value.(*BlacklistEntry)
		return entry
	}

	return nil
}

// GetAllBlacklistEntries 获取所有黑名单条目
func (s *RegionBlacklistService) GetAllBlacklistEntries() []*BlacklistEntry {
	var entries []*BlacklistEntry

	s.blacklist.Range(func(key, value interface{}) bool {
		entry := value.(*BlacklistEntry)
		if entry.IsBlacklisted {
			entries = append(entries, entry)
		}
		return true
	})

	return entries
}

// RemoveFromBlacklist 从黑名单中移除
func (s *RegionBlacklistService) RemoveFromBlacklist(provider, route, expressCode string) {
	key := s.buildKey(provider, route, expressCode)

	if value, exists := s.blacklist.Load(key); exists {
		entry := value.(*BlacklistEntry)
		entry.IsBlacklisted = false
		s.blacklist.Store(key, entry)

		s.logger.Info("地区路线已从黑名单移除",
			zap.String("route", route),
			zap.String("provider", provider),
			zap.String("express_code", expressCode))
	}
}

// ClearExpiredEntries 清理过期条目（30天未失败的条目）
func (s *RegionBlacklistService) ClearExpiredEntries() {
	expiredTime := time.Now().AddDate(0, 0, -30) // 30天前

	var expiredKeys []string

	s.blacklist.Range(func(key, value interface{}) bool {
		entry := value.(*BlacklistEntry)
		if entry.LastFailureAt.Before(expiredTime) {
			expiredKeys = append(expiredKeys, key.(string))
		}
		return true
	})

	// 删除过期条目
	for _, key := range expiredKeys {
		s.blacklist.Delete(key)
	}

	if len(expiredKeys) > 0 {
		s.logger.Info("清理过期黑名单条目",
			zap.Int("expired_count", len(expiredKeys)))
	}
}

// GetStatistics 获取黑名单统计信息
func (s *RegionBlacklistService) GetStatistics() map[string]interface{} {
	totalEntries := 0
	blacklistedEntries := 0
	providerStats := make(map[string]int)
	routeStats := make(map[string]int)

	s.blacklist.Range(func(key, value interface{}) bool {
		entry := value.(*BlacklistEntry)
		totalEntries++

		if entry.IsBlacklisted {
			blacklistedEntries++
		}

		providerStats[entry.Provider]++
		routeStats[entry.Route]++

		return true
	})

	return map[string]interface{}{
		"total_entries":       totalEntries,
		"blacklisted_entries": blacklistedEntries,
		"provider_stats":      providerStats,
		"route_stats":         routeStats,
	}
}

// buildKey 构建黑名单键值
func (s *RegionBlacklistService) buildKey(provider, route, expressCode string) string {
	return fmt.Sprintf("%s:%s:%s", provider, route, expressCode)
}

// ShouldSkipQuery 判断是否应该跳过查询（基于黑名单）
func (s *RegionBlacklistService) ShouldSkipQuery(provider, route, expressCode string) bool {
	// 🎯 只对云通供应商启用预过滤
	if provider != "yuntong" {
		return false
	}

	return s.IsBlacklisted(provider, route, expressCode)
}
