package service

import (
	"context"
	"sync"
	"time"

	"go.uber.org/zap"
)

// PriceQueryMetrics 价格查询性能指标
type PriceQueryMetrics struct {
	TotalQueries          int64         `json:"total_queries"`
	SuccessfulQueries     int64         `json:"successful_queries"`
	FailedQueries         int64         `json:"failed_queries"`
	AverageResponseTime   time.Duration `json:"average_response_time"`
	MaxResponseTime       time.Duration `json:"max_response_time"`
	MinResponseTime       time.Duration `json:"min_response_time"`
	CacheHitRate          float64       `json:"cache_hit_rate"`
	DatabaseQueryCount    int64         `json:"database_query_count"`
	MappingLoadTime       time.Duration `json:"mapping_load_time"`
	PreFilterTime         time.Duration `json:"pre_filter_time"`
	ConcurrentQueries     int64         `json:"concurrent_queries"`
	LastUpdated           time.Time     `json:"last_updated"`
}

// PricePerformanceMonitor 价格查询性能监控器
type PricePerformanceMonitor struct {
	metrics    *PriceQueryMetrics
	mutex      sync.RWMutex
	logger     *zap.Logger
	
	// 性能阈值配置
	responseTimeThreshold time.Duration
	cacheHitRateThreshold float64
	
	// 统计数据
	responseTimes []time.Duration
	maxSamples    int
}

// NewPricePerformanceMonitor 创建性能监控器
func NewPricePerformanceMonitor(logger *zap.Logger) *PricePerformanceMonitor {
	return &PricePerformanceMonitor{
		metrics: &PriceQueryMetrics{
			MinResponseTime: time.Hour, // 初始化为最大值
			LastUpdated:     time.Now(),
		},
		logger:                logger,
		responseTimeThreshold: 200 * time.Millisecond, // 目标响应时间
		cacheHitRateThreshold: 0.95,                   // 目标缓存命中率
		maxSamples:            1000,                    // 最大样本数
		responseTimes:         make([]time.Duration, 0, 1000),
	}
}

// RecordQuery 记录查询性能数据
func (m *PricePerformanceMonitor) RecordQuery(ctx context.Context, responseTime time.Duration, success bool, cacheHit bool, dbQueries int) {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	
	m.metrics.TotalQueries++
	
	if success {
		m.metrics.SuccessfulQueries++
	} else {
		m.metrics.FailedQueries++
	}
	
	// 更新响应时间统计
	m.responseTimes = append(m.responseTimes, responseTime)
	if len(m.responseTimes) > m.maxSamples {
		// 保持样本数量在限制内
		m.responseTimes = m.responseTimes[1:]
	}
	
	// 更新最大最小响应时间
	if responseTime > m.metrics.MaxResponseTime {
		m.metrics.MaxResponseTime = responseTime
	}
	if responseTime < m.metrics.MinResponseTime {
		m.metrics.MinResponseTime = responseTime
	}
	
	// 计算平均响应时间
	var total time.Duration
	for _, rt := range m.responseTimes {
		total += rt
	}
	m.metrics.AverageResponseTime = total / time.Duration(len(m.responseTimes))
	
	// 更新数据库查询计数
	m.metrics.DatabaseQueryCount += int64(dbQueries)
	
	// 计算缓存命中率
	if cacheHit {
		// 简化的缓存命中率计算
		m.metrics.CacheHitRate = float64(m.metrics.SuccessfulQueries-m.metrics.DatabaseQueryCount) / float64(m.metrics.SuccessfulQueries)
		if m.metrics.CacheHitRate < 0 {
			m.metrics.CacheHitRate = 0
		}
	}
	
	m.metrics.LastUpdated = time.Now()
	
	// 性能告警检查
	m.checkPerformanceThresholds(responseTime)
}

// RecordMappingLoadTime 记录映射加载时间
func (m *PricePerformanceMonitor) RecordMappingLoadTime(loadTime time.Duration) {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	
	m.metrics.MappingLoadTime = loadTime
}

// RecordPreFilterTime 记录预过滤时间
func (m *PricePerformanceMonitor) RecordPreFilterTime(filterTime time.Duration) {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	
	m.metrics.PreFilterTime = filterTime
}

// IncrementConcurrentQueries 增加并发查询计数
func (m *PricePerformanceMonitor) IncrementConcurrentQueries() {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	
	m.metrics.ConcurrentQueries++
}

// DecrementConcurrentQueries 减少并发查询计数
func (m *PricePerformanceMonitor) DecrementConcurrentQueries() {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	
	if m.metrics.ConcurrentQueries > 0 {
		m.metrics.ConcurrentQueries--
	}
}

// GetMetrics 获取性能指标
func (m *PricePerformanceMonitor) GetMetrics() *PriceQueryMetrics {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	
	// 返回指标副本
	metrics := *m.metrics
	return &metrics
}

// checkPerformanceThresholds 检查性能阈值并发出告警
func (m *PricePerformanceMonitor) checkPerformanceThresholds(responseTime time.Duration) {
	// 响应时间告警
	if responseTime > m.responseTimeThreshold {
		m.logger.Warn("查价响应时间超过阈值",
			zap.Duration("response_time", responseTime),
			zap.Duration("threshold", m.responseTimeThreshold),
			zap.Duration("average_time", m.metrics.AverageResponseTime))
	}
	
	// 缓存命中率告警
	if m.metrics.CacheHitRate < m.cacheHitRateThreshold && m.metrics.TotalQueries > 10 {
		m.logger.Warn("缓存命中率低于阈值",
			zap.Float64("cache_hit_rate", m.metrics.CacheHitRate),
			zap.Float64("threshold", m.cacheHitRateThreshold),
			zap.Int64("total_queries", m.metrics.TotalQueries))
	}
}

// Reset 重置性能指标
func (m *PricePerformanceMonitor) Reset() {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	
	m.metrics = &PriceQueryMetrics{
		MinResponseTime: time.Hour,
		LastUpdated:     time.Now(),
	}
	m.responseTimes = make([]time.Duration, 0, m.maxSamples)
	
	m.logger.Info("性能监控指标已重置")
}

// GetPerformanceReport 生成性能报告
func (m *PricePerformanceMonitor) GetPerformanceReport() map[string]interface{} {
	metrics := m.GetMetrics()
	
	successRate := float64(0)
	if metrics.TotalQueries > 0 {
		successRate = float64(metrics.SuccessfulQueries) / float64(metrics.TotalQueries) * 100
	}
	
	return map[string]interface{}{
		"performance_summary": map[string]interface{}{
			"total_queries":        metrics.TotalQueries,
			"success_rate":         successRate,
			"average_response_ms":  metrics.AverageResponseTime.Milliseconds(),
			"max_response_ms":      metrics.MaxResponseTime.Milliseconds(),
			"min_response_ms":      metrics.MinResponseTime.Milliseconds(),
			"cache_hit_rate":       metrics.CacheHitRate * 100,
			"concurrent_queries":   metrics.ConcurrentQueries,
		},
		"performance_breakdown": map[string]interface{}{
			"mapping_load_ms":    metrics.MappingLoadTime.Milliseconds(),
			"pre_filter_ms":      metrics.PreFilterTime.Milliseconds(),
			"database_queries":   metrics.DatabaseQueryCount,
		},
		"performance_status": map[string]interface{}{
			"response_time_ok":  metrics.AverageResponseTime <= m.responseTimeThreshold,
			"cache_hit_rate_ok": metrics.CacheHitRate >= m.cacheHitRateThreshold,
			"overall_healthy":   metrics.AverageResponseTime <= m.responseTimeThreshold && metrics.CacheHitRate >= m.cacheHitRateThreshold,
		},
		"last_updated": metrics.LastUpdated,
	}
}
