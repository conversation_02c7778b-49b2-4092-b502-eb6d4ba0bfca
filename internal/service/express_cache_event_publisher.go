package service

import (
	"go.uber.org/zap"
)

// ExpressCacheEventPublisher 快递缓存事件发布器
type ExpressCacheEventPublisher struct {
	eventManager *CacheEventManager
	logger       *zap.Logger
}

// NewExpressCacheEventPublisher 创建快递缓存事件发布器
func NewExpressCacheEventPublisher(eventManager *CacheEventManager, logger *zap.Logger) *ExpressCacheEventPublisher {
	return &ExpressCacheEventPublisher{
		eventManager: eventManager,
		logger:       logger,
	}
}

// PublishCompanyStatusChangedEvent 发布快递公司状态变更事件
func (p *ExpressCacheEventPublisher) PublishCompanyStatusChangedEvent(companyCode string, isActive bool) {
	event := CacheEvent{
		Type:        EventCompanyStatusChanged,
		CompanyCode: companyCode,
		Data: map[string]interface{}{
			"is_active": isActive,
		},
	}
	
	p.eventManager.PublishEvent(event)
	
	p.logger.Debug("发布快递公司状态变更事件",
		zap.String("company_code", companyCode),
		zap.Bool("is_active", isActive))
}

// PublishMappingChangedEvent 发布映射关系变更事件
func (p *ExpressCacheEventPublisher) PublishMappingChangedEvent(companyCode, providerCode string) {
	event := CacheEvent{
		Type:         EventMappingChanged,
		CompanyCode:  companyCode,
		ProviderCode: providerCode,
	}
	
	p.eventManager.PublishEvent(event)
	
	p.logger.Debug("发布映射关系变更事件",
		zap.String("company_code", companyCode),
		zap.String("provider_code", providerCode))
}

// PublishProviderChangedEvent 发布供应商变更事件
func (p *ExpressCacheEventPublisher) PublishProviderChangedEvent(providerCode string) {
	event := CacheEvent{
		Type:         EventProviderChanged,
		ProviderCode: providerCode,
	}
	
	p.eventManager.PublishEvent(event)
	
	p.logger.Debug("发布供应商变更事件",
		zap.String("provider_code", providerCode))
}