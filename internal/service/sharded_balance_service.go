package service

import (
	"context"
	"crypto/md5"
	"fmt"
	"time"

	"github.com/shopspring/decimal"
	"go.uber.org/zap"

	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/repository"
)

// BatchOperations 批量操作类型定义
type BatchOperations struct {
	UserID     string
	Operations []Operation
}

type Operation struct {
	Type   string
	Amount decimal.Decimal
	Memo   string
}

// ShardedStatistics 分片统计信息
type ShardedStatistics struct {
	ShardStatistics map[int]*ShardStatistics `json:"shard_statistics"`
	TotalUsers      int64                    `json:"total_users"`
	TotalBalance    decimal.Decimal          `json:"total_balance"`
}

type ShardStatistics struct {
	ShardID    int             `json:"shard_id"`
	UserCount  int64           `json:"user_count"`
	Balance    decimal.Decimal `json:"balance"`
	Healthy    bool            `json:"healthy"`
}

// ShardedBalanceService 分片余额服务 - 简化版
type ShardedBalanceService struct {
	repositories     map[int]*repository.BalanceRepository
	shardCount       int
	consistencyMode  ConsistencyMode
	logger           *zap.Logger
}

// ConsistencyMode 一致性模式
type ConsistencyMode string

const (
	StrongConsistency   ConsistencyMode = "strong"
	EventualConsistency ConsistencyMode = "eventual"
)

// NewShardedBalanceService 创建分片余额服务
func NewShardedBalanceService(
	repositories map[int]*repository.BalanceRepository,
	shardCount int,
	consistencyMode ConsistencyMode,
	logger *zap.Logger,
) *ShardedBalanceService {
	return &ShardedBalanceService{
		repositories:    repositories,
		shardCount:      shardCount,
		consistencyMode: consistencyMode,
		logger:          logger,
	}
}

// calculateShardID 计算分片ID
func (s *ShardedBalanceService) calculateShardID(userID string) int {
	hash := md5.Sum([]byte(userID))
	return int(hash[0]) % s.shardCount
}

// getRepository 获取分片存储库
func (s *ShardedBalanceService) getRepository(shardID int) *repository.BalanceRepository {
	return s.repositories[shardID]
}

// GetBalance 获取用户余额 - 分片版本
func (s *ShardedBalanceService) GetBalance(ctx context.Context, userID string) (*model.UserBalance, error) {
	shardID := s.calculateShardID(userID)
	repository := s.getRepository(shardID)
	
	if repository == nil {
		return nil, fmt.Errorf("分片 %d 不存在", shardID)
	}

	return (*repository).GetBalance(ctx, userID)
}

// GetUserBalance 获取用户余额 - 兼容方法
func (s *ShardedBalanceService) GetUserBalance(ctx context.Context, userID string) (*model.UserBalance, error) {
	return s.GetBalance(ctx, userID)
}

// PreChargeForOrder 预扣费 - 分片版本
func (s *ShardedBalanceService) PreChargeForOrder(ctx context.Context, userID, orderNo string, amount decimal.Decimal) error {
	if amount.LessThanOrEqual(decimal.Zero) {
		return fmt.Errorf("预扣费金额必须大于0")
	}

	shardID := s.calculateShardID(userID)
	
	s.logger.Info("🔄 分片预扣费开始",
		zap.String("user_id", userID),
		zap.String("order_no", orderNo),
		zap.String("amount", amount.String()),
		zap.Int("shard_id", shardID))

	switch s.consistencyMode {
	case StrongConsistency:
		return s.preChargeWithStrongConsistency(ctx, userID, orderNo, amount, shardID)
	case EventualConsistency:
		return s.preChargeWithEventualConsistency(ctx, userID, orderNo, amount, shardID)
	default:
		return s.preChargeWithEventualConsistency(ctx, userID, orderNo, amount, shardID)
	}
}

// PreChargeForOrderWithDetails 预扣费（优化版）- 分片版本
func (s *ShardedBalanceService) PreChargeForOrderWithDetails(ctx context.Context, userID, customerOrderNo, platformOrderNo string, amount decimal.Decimal) error {
	if amount.LessThanOrEqual(decimal.Zero) {
		return fmt.Errorf("预扣费金额必须大于0")
	}

	shardID := s.calculateShardID(userID)
	
	s.logger.Info("🔄 分片预扣费开始（优化版）",
		zap.String("user_id", userID),
		zap.String("customer_order_no", customerOrderNo),
		zap.String("platform_order_no", platformOrderNo),
		zap.String("amount", amount.String()),
		zap.Int("shard_id", shardID))

	switch s.consistencyMode {
	case StrongConsistency:
		return s.preChargeWithStrongConsistencyWithDetails(ctx, userID, customerOrderNo, platformOrderNo, amount, shardID)
	case EventualConsistency:
		return s.preChargeWithEventualConsistencyWithDetails(ctx, userID, customerOrderNo, platformOrderNo, amount, shardID)
	default:
		return s.preChargeWithEventualConsistencyWithDetails(ctx, userID, customerOrderNo, platformOrderNo, amount, shardID)
	}
}

// preChargeWithStrongConsistency 强一致性预扣费
func (s *ShardedBalanceService) preChargeWithStrongConsistency(ctx context.Context, userID, orderNo string, amount decimal.Decimal, shardID int) error {
	repository := s.getRepository(shardID)
	if repository == nil {
		return fmt.Errorf("分片 %d 不存在", shardID)
	}

	maxRetries := 10
	baseDelay := 10 * time.Millisecond

	for retryCount := 0; retryCount < maxRetries; retryCount++ {
		balance, err := (*repository).GetBalance(ctx, userID)
		if err != nil {
			return fmt.Errorf("获取用户余额失败: %w", err)
		}

		if balance.Balance.LessThan(amount) {
			return fmt.Errorf("余额不足：当前余额 %s，需要 %s", balance.Balance.String(), amount.String())
		}

		newBalance := balance.Balance.Sub(amount)
		
		// 创建交易记录
		transaction := &model.BalanceTransaction{
			ID:              fmt.Sprintf("PRE_%s_%d", orderNo, time.Now().UnixNano()),
			UserID:          userID,
			Type:            model.TransactionTypeOrderPreCharge,
			Amount:          amount.Neg(),
			BalanceBefore:   balance.Balance,
			BalanceAfter:    newBalance,
			Status:          model.TransactionStatusCompleted,
			OrderNo:         orderNo,
			CustomerOrderNo: orderNo,
			CreatedAt:       time.Now(),
		}

		// 首先创建交易记录
		if err := (*repository).CreateTransaction(ctx, transaction); err != nil {
			return fmt.Errorf("创建交易记录失败: %w", err)
		}
		
		// 然后更新余额
		if err := (*repository).UpdateBalanceWithVersion(ctx, userID, newBalance, decimal.Zero, balance.Version); err != nil {
			// 检查是否是版本冲突
			if shardedIsVersionConflictError(err) {
				// 指数退避重试 - 修复位移操作
				exponentialMultiplier := 1 << uint(retryCount)
				delay := time.Duration(float64(baseDelay.Nanoseconds()) * float64(exponentialMultiplier))
				if delay > 500*time.Millisecond {
					delay = 500 * time.Millisecond
				}
				
				s.logger.Debug("🔄 版本冲突，延迟重试",
					zap.String("user_id", userID),
					zap.Int("retry_count", retryCount+1),
					zap.Duration("delay", delay))

				time.Sleep(delay)
				continue
			}
			return fmt.Errorf("更新余额失败: %w", err)
		}

		s.logger.Info("✅ 强一致性预扣费成功",
			zap.String("user_id", userID),
			zap.String("order_no", orderNo),
			zap.String("amount", amount.String()),
			zap.Int("retry_count", retryCount))

		return nil
	}

	return fmt.Errorf("预扣费失败，达到最大重试次数 %d", maxRetries)
}

// preChargeWithEventualConsistency 最终一致性预扣费
func (s *ShardedBalanceService) preChargeWithEventualConsistency(ctx context.Context, userID, orderNo string, amount decimal.Decimal, shardID int) error {
	repository := s.getRepository(shardID)
	if repository == nil {
		return fmt.Errorf("分片 %d 不存在", shardID)
	}

	balance, err := (*repository).GetBalance(ctx, userID)
	if err != nil {
		return fmt.Errorf("获取用户余额失败: %w", err)
	}

	if balance.Balance.LessThan(amount) {
		return fmt.Errorf("余额不足：当前余额 %s，需要 %s", balance.Balance.String(), amount.String())
	}

	newBalance := balance.Balance.Sub(amount)
	
	// 创建交易记录
	transaction := &model.BalanceTransaction{
		ID:              fmt.Sprintf("PRE_%s_%d", orderNo, time.Now().UnixNano()),
		UserID:          userID,
		Type:            model.TransactionTypeOrderPreCharge,
		Amount:          amount.Neg(),
		BalanceBefore:   balance.Balance,
		BalanceAfter:    newBalance,
		Status:          model.TransactionStatusCompleted,
		OrderNo:         orderNo,
		CustomerOrderNo: orderNo,
		CreatedAt:       time.Now(),
	}

	// 首先创建交易记录
	if err := (*repository).CreateTransaction(ctx, transaction); err != nil {
		return fmt.Errorf("创建交易记录失败: %w", err)
	}
	
	// 然后更新余额
	err = (*repository).UpdateBalanceWithVersion(ctx, userID, newBalance, decimal.Zero, balance.Version)
	if err != nil {
		s.logger.Warn("🔄 最终一致性预扣费版本冲突",
			zap.String("user_id", userID),
			zap.Error(err))
		// 最终一致性模式下，暂时接受版本冲突，由后台任务处理
		return nil
	}

	s.logger.Info("✅ 最终一致性预扣费成功",
		zap.String("user_id", userID),
		zap.String("order_no", orderNo),
		zap.String("amount", amount.String()))

	return nil
}

// preChargeWithStrongConsistencyWithDetails 强一致性预扣费（优化版）
func (s *ShardedBalanceService) preChargeWithStrongConsistencyWithDetails(ctx context.Context, userID, customerOrderNo, platformOrderNo string, amount decimal.Decimal, shardID int) error {
	repository := s.getRepository(shardID)
	if repository == nil {
		return fmt.Errorf("分片 %d 不存在", shardID)
	}

	maxRetries := 10
	baseDelay := 10 * time.Millisecond

	for retryCount := 0; retryCount < maxRetries; retryCount++ {
		balance, err := (*repository).GetBalance(ctx, userID)
		if err != nil {
			return fmt.Errorf("获取用户余额失败: %w", err)
		}

		if balance.Balance.LessThan(amount) {
			return fmt.Errorf("余额不足：当前余额 %s，需要 %s", balance.Balance.String(), amount.String())
		}

		newBalance := balance.Balance.Sub(amount)
		
		// 创建交易记录（优化版）
		transaction := &model.BalanceTransaction{
			ID:              fmt.Sprintf("PRE_%s_%d", customerOrderNo, time.Now().UnixNano()),
			UserID:          userID,
			Type:            model.TransactionTypeOrderPreCharge,
			Amount:          amount.Neg(),
			BalanceBefore:   balance.Balance,
			BalanceAfter:    newBalance,
			Status:          model.TransactionStatusCompleted,
			OrderNo:         customerOrderNo,        // 供应商订单号
			PlatformOrderNo: platformOrderNo,        // 平台订单号
			CustomerOrderNo: customerOrderNo,        // 客户订单号
			CreatedAt:       time.Now(),
		}

		// 首先创建交易记录
		if err := (*repository).CreateTransaction(ctx, transaction); err != nil {
			return fmt.Errorf("创建交易记录失败: %w", err)
		}
		
		// 然后更新余额
		if err := (*repository).UpdateBalanceWithVersion(ctx, userID, newBalance, decimal.Zero, balance.Version); err != nil {
			// 检查是否是版本冲突
			if shardedIsVersionConflictError(err) {
				// 指数退避重试
				exponentialMultiplier := 1 << uint(retryCount)
				delay := time.Duration(float64(baseDelay.Nanoseconds()) * float64(exponentialMultiplier))
				if delay > 500*time.Millisecond {
					delay = 500 * time.Millisecond
				}
				
				s.logger.Warn("🔄 分片预扣费版本冲突，正在重试（优化版）",
					zap.String("user_id", userID),
					zap.String("customer_order_no", customerOrderNo),
					zap.Int("retry_count", retryCount+1),
					zap.Duration("delay", delay))
				
				time.Sleep(delay)
				continue
			}
			return fmt.Errorf("更新余额失败: %w", err)
		}

		s.logger.Info("✅ 强一致性预扣费成功（优化版）",
			zap.String("user_id", userID),
			zap.String("customer_order_no", customerOrderNo),
			zap.String("platform_order_no", platformOrderNo),
			zap.String("amount", amount.String()),
			zap.Int("retry_count", retryCount))
		
		return nil
	}

	return fmt.Errorf("预扣费失败，达到最大重试次数 %d", maxRetries)
}

// preChargeWithEventualConsistencyWithDetails 最终一致性预扣费（优化版）
func (s *ShardedBalanceService) preChargeWithEventualConsistencyWithDetails(ctx context.Context, userID, customerOrderNo, platformOrderNo string, amount decimal.Decimal, shardID int) error {
	repository := s.getRepository(shardID)
	if repository == nil {
		return fmt.Errorf("分片 %d 不存在", shardID)
	}

	balance, err := (*repository).GetBalance(ctx, userID)
	if err != nil {
		return fmt.Errorf("获取用户余额失败: %w", err)
	}

	if balance.Balance.LessThan(amount) {
		return fmt.Errorf("余额不足：当前余额 %s，需要 %s", balance.Balance.String(), amount.String())
	}

	newBalance := balance.Balance.Sub(amount)
	
	// 创建交易记录（优化版）
	transaction := &model.BalanceTransaction{
		ID:              fmt.Sprintf("PRE_%s_%d", customerOrderNo, time.Now().UnixNano()),
		UserID:          userID,
		Type:            model.TransactionTypeOrderPreCharge,
		Amount:          amount.Neg(),
		BalanceBefore:   balance.Balance,
		BalanceAfter:    newBalance,
		Status:          model.TransactionStatusCompleted,
		OrderNo:         customerOrderNo,        // 供应商订单号
		PlatformOrderNo: platformOrderNo,        // 平台订单号
		CustomerOrderNo: customerOrderNo,        // 客户订单号
		CreatedAt:       time.Now(),
	}

	// 首先创建交易记录
	if err := (*repository).CreateTransaction(ctx, transaction); err != nil {
		return fmt.Errorf("创建交易记录失败: %w", err)
	}
	
	// 然后更新余额
	err = (*repository).UpdateBalanceWithVersion(ctx, userID, newBalance, decimal.Zero, balance.Version)
	if err != nil {
		s.logger.Warn("🔄 最终一致性预扣费版本冲突（优化版）",
			zap.String("user_id", userID),
			zap.String("customer_order_no", customerOrderNo),
			zap.Error(err))
		// 最终一致性模式下，暂时接受版本冲突，由后台任务处理
		return nil
	}

	s.logger.Info("✅ 最终一致性预扣费成功（优化版）",
		zap.String("user_id", userID),
		zap.String("customer_order_no", customerOrderNo),
		zap.String("platform_order_no", platformOrderNo),
		zap.String("amount", amount.String()))

	return nil
}

// RefundForOrder 退款 - 分片版本
func (s *ShardedBalanceService) RefundForOrder(ctx context.Context, userID, orderNo string, amount decimal.Decimal) error {
	if amount.LessThanOrEqual(decimal.Zero) {
		return fmt.Errorf("退款金额必须大于0")
	}

	shardID := s.calculateShardID(userID)
	repository := s.getRepository(shardID)
	
	if repository == nil {
		return fmt.Errorf("分片 %d 不存在", shardID)
	}

	balance, err := (*repository).GetBalance(ctx, userID)
	if err != nil {
		return fmt.Errorf("获取用户余额失败: %w", err)
	}

	newBalance := balance.Balance.Add(amount)
	
	// 创建交易记录
	transaction := &model.BalanceTransaction{
		ID:              fmt.Sprintf("REF_%s_%d", orderNo, time.Now().UnixNano()),
		UserID:          userID,
		Type:            model.TransactionTypeOrderCancelRefund,
		Amount:          amount,
		BalanceBefore:   balance.Balance,
		BalanceAfter:    newBalance,
		Status:          model.TransactionStatusCompleted,
		OrderNo:         orderNo,
		CustomerOrderNo: orderNo,
		CreatedAt:       time.Now(),
	}

	// 首先创建交易记录
	if err := (*repository).CreateTransaction(ctx, transaction); err != nil {
		return fmt.Errorf("创建交易记录失败: %w", err)
	}
	
	// 然后更新余额
	if err := (*repository).UpdateBalanceWithVersion(ctx, userID, newBalance, decimal.Zero, balance.Version); err != nil {
		return fmt.Errorf("更新余额失败: %w", err)
	}

	s.logger.Info("✅ 分片退款成功",
		zap.String("user_id", userID),
		zap.String("order_no", orderNo),
		zap.String("amount", amount.String()))

	return nil
}

// ExecuteBatchOperations 批量操作 - 简化实现
func (s *ShardedBalanceService) ExecuteBatchOperations(ctx context.Context, operations BatchOperations) error {
	// 简化实现：暂时不支持复杂批量操作
	return fmt.Errorf("批量操作功能暂未在分片服务中实现")
}

// GetShardStats 获取分片统计 - 简化实现  
func (s *ShardedBalanceService) GetShardStats(ctx context.Context) (*ShardedStatistics, error) {
	return &ShardedStatistics{
		ShardStatistics: make(map[int]*ShardStatistics),
		TotalUsers:      0,
		TotalBalance:    decimal.Zero,
	}, nil
}

// HealthCheck 健康检查
func (s *ShardedBalanceService) HealthCheck(ctx context.Context) map[int]bool {
	healthy := make(map[int]bool)
	for shardID := range s.repositories {
		// 简化实现：返回无错误
		healthy[shardID] = true
	}
	return healthy
}

// shardedIsVersionConflictError 检查是否是版本冲突错误
func shardedIsVersionConflictError(err error) bool {
	if err == nil {
		return false
	}
	errStr := err.Error()
	return len(errStr) >= 8 && (shardedContains(errStr, "余额版本冲突") || shardedContains(errStr, "version conflict"))
}

func shardedContains(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}