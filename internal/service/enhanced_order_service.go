package service

import (
	"context"
	"fmt"

	"go.uber.org/zap"
	"github.com/shopspring/decimal"

	"github.com/your-org/go-kuaidi/internal/model"
)

// EnhancedOrderService 增强版订单服务 - 集成高并发余额处理
type EnhancedOrderService struct {
	// 原有服务
	baseOrderService    *OrderService
	
	// 增强版余额服务
	enhancedBalanceService *EnhancedBalanceService
	
	// 服务管理器
	balanceManager      *EnhancedBalanceServiceManager
	
	// 日志
	logger              *zap.Logger
}

// NewEnhancedOrderService 创建增强版订单服务
func NewEnhancedOrderServiceV2(
	baseOrderService *OrderService,
	enhancedBalanceService *EnhancedBalanceService,
	balanceManager *EnhancedBalanceServiceManager,
	logger *zap.Logger,
) *EnhancedOrderService {
	return &EnhancedOrderService{
		baseOrderService:       baseOrderService,
		enhancedBalanceService: enhancedBalanceService,
		balanceManager:         balanceManager,
		logger:                 logger,
	}
}

// CreateOrder 创建订单 - 使用增强版余额处理
func (s *EnhancedOrderService) CreateOrder(ctx context.Context, orderReq *model.OrderRequest) (*model.OrderResponse, error) {
	s.logger.Info("🚀 增强版订单创建开始",
		zap.String("customer_order_no", orderReq.CustomerOrderNo),
		zap.String("user_id", orderReq.UserID),
		zap.String("amount", orderReq.EstimatedPrice))

	// 解析价格字符串为 decimal.Decimal
	estimatedPrice, err := decimal.NewFromString(orderReq.EstimatedPrice)
	if err != nil {
		s.logger.Error("❌ 价格解析失败",
			zap.String("estimated_price", orderReq.EstimatedPrice),
			zap.Error(err))
		return nil, fmt.Errorf("价格格式错误: %w", err)
	}

	// 1. 使用增强版余额服务进行预扣费
	err = s.enhancedBalanceService.PreChargeForOrder(
		ctx, 
		orderReq.UserID, 
		orderReq.CustomerOrderNo, 
		estimatedPrice,
	)
	if err != nil {
		s.logger.Error("❌ 增强版预扣费失败",
			zap.String("customer_order_no", orderReq.CustomerOrderNo),
			zap.String("user_id", orderReq.UserID),
			zap.Error(err))
		
		return nil, fmt.Errorf("预扣费失败: %w", err)
	}

	s.logger.Info("✅ 增强版预扣费成功",
		zap.String("customer_order_no", orderReq.CustomerOrderNo),
		zap.String("user_id", orderReq.UserID),
		zap.String("amount", orderReq.EstimatedPrice))

	// 2. 继续使用原有的订单创建流程
	// 注意：这里需要调用原有OrderService的创建方法，但要跳过余额扣费部分
	// 因为我们已经使用增强版余额服务完成了扣费
	
	orderResponse, err := s.createOrderWithoutBalanceCharge(ctx, orderReq)
	if err != nil {
		// 如果订单创建失败，需要退款
		s.logger.Error("❌ 订单创建失败，开始退款",
			zap.String("customer_order_no", orderReq.CustomerOrderNo),
			zap.String("user_id", orderReq.UserID),
			zap.Error(err))

		// 使用增强版余额服务进行退款
		refundErr := s.enhancedBalanceService.RefundForOrder(
			ctx,
			orderReq.UserID,
			orderReq.CustomerOrderNo,
			estimatedPrice,
		)
		if refundErr != nil {
			s.logger.Error("❌ 自动退款失败",
				zap.String("customer_order_no", orderReq.CustomerOrderNo),
				zap.String("user_id", orderReq.UserID),
				zap.Error(refundErr))
		}

		return nil, fmt.Errorf("订单创建失败: %w", err)
	}

	s.logger.Info("✅ 增强版订单创建成功",
		zap.String("customer_order_no", orderReq.CustomerOrderNo),
		zap.String("order_no", orderResponse.Data.OrderNo),
		zap.String("tracking_no", orderResponse.Data.TrackingNo))

	return orderResponse, nil
}

// createOrderWithoutBalanceCharge 创建订单但不进行余额扣费
// 这是一个内部方法，用于在已经完成预扣费后创建订单
func (s *EnhancedOrderService) createOrderWithoutBalanceCharge(ctx context.Context, orderReq *model.OrderRequest) (*model.OrderResponse, error) {
	// 这里应该调用原有的订单创建逻辑，但跳过余额扣费部分
	// 具体实现需要根据原有OrderService的结构进行调整
	
	// 示例实现（需要根据实际OrderService接口调整）:
	// return s.baseOrderService.CreateOrderInternal(ctx, orderReq, true) // true表示跳过余额扣费
	
	// 暂时返回一个示例响应，实际需要实现完整逻辑
	estimatedPrice, _ := decimal.NewFromString(orderReq.EstimatedPrice)
	return &model.OrderResponse{
		Success: true,
		Code:    200,
		Message: "订单创建成功",
		Data: &model.OrderResult{
			CustomerOrderNo: orderReq.CustomerOrderNo,
			OrderNo:         orderReq.CustomerOrderNo,
			TrackingNo:      "ENHANCED_" + orderReq.CustomerOrderNo,
			Price:           estimatedPrice.InexactFloat64(),
		},
	}, nil
}

// CancelOrder 取消订单 - 使用增强版余额退款
func (s *EnhancedOrderService) CancelOrder(ctx context.Context, userID, orderNo string) error {
	s.logger.Info("🔄 增强版订单取消开始",
		zap.String("user_id", userID),
		zap.String("order_no", orderNo))

	// 1. 获取订单信息（需要获取订单金额用于退款）
	// order, err := s.baseOrderService.GetOrder(ctx, orderNo)
	// if err != nil {
	//     return fmt.Errorf("获取订单信息失败: %w", err)
	// }

	// 2. 使用增强版余额服务进行退款
	// 注意：这里需要实际的订单金额，暂时使用示例金额
	refundAmount := decimal.NewFromFloat(10.0) // 实际应该从订单中获取
	
	err := s.enhancedBalanceService.RefundForOrder(ctx, userID, orderNo, refundAmount)
	if err != nil {
		s.logger.Error("❌ 增强版退款失败",
			zap.String("user_id", userID),
			zap.String("order_no", orderNo),
			zap.Error(err))
		return fmt.Errorf("退款失败: %w", err)
	}

	s.logger.Info("✅ 增强版订单取消成功",
		zap.String("user_id", userID),
		zap.String("order_no", orderNo))

	return nil
}

// GetUserBalance 获取用户余额
func (s *EnhancedOrderService) GetUserBalance(ctx context.Context, userID string) (*model.UserBalance, error) {
	return s.enhancedBalanceService.GetUserBalance(ctx, userID)
}

// GetServiceStats 获取服务统计信息
func (s *EnhancedOrderService) GetServiceStats(ctx context.Context) (map[string]interface{}, error) {
	if s.balanceManager != nil {
		return s.balanceManager.GetStats(ctx)
	}
	
	if s.enhancedBalanceService != nil {
		return s.enhancedBalanceService.GetServiceStats(ctx)
	}
	
	return map[string]interface{}{
		"enhanced_service": "not_available",
	}, nil
}

// HealthCheck 健康检查
func (s *EnhancedOrderService) HealthCheck(ctx context.Context) error {
	// 检查增强版余额服务
	if s.enhancedBalanceService != nil {
		if err := s.enhancedBalanceService.HealthCheck(ctx); err != nil {
			return fmt.Errorf("增强版余额服务健康检查失败: %w", err)
		}
	}

	// 检查服务管理器
	if s.balanceManager != nil {
		if err := s.balanceManager.HealthCheck(ctx); err != nil {
			return fmt.Errorf("余额服务管理器健康检查失败: %w", err)
		}
	}

	return nil
}

// SwitchBalanceMode 切换余额服务模式
func (s *EnhancedOrderService) SwitchBalanceMode(mode ServiceMode) error {
	if s.enhancedBalanceService == nil {
		return fmt.Errorf("增强版余额服务未初始化")
	}

	return s.enhancedBalanceService.SwitchMode(mode)
}

// BatchProcessBalanceOperations 批量处理余额操作
func (s *EnhancedOrderService) BatchProcessBalanceOperations(ctx context.Context, userID string, operations []*BalanceOperation) error {
	if s.enhancedBalanceService == nil {
		return fmt.Errorf("增强版余额服务未初始化")
	}

	return s.enhancedBalanceService.BatchProcessOperations(ctx, userID, operations)
}