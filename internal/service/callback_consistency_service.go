package service

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/your-org/go-kuaidi/internal/repository"
	"go.uber.org/zap"
)

// CallbackConsistencyService 回调一致性服务
// 🔥 用于检测和修复回调处理与数据库状态不一致的问题
type CallbackConsistencyService struct {
	callbackDB   *sql.DB
	mainDB       *sql.DB
	orderRepo    repository.OrderRepository
	callbackRepo repository.CallbackRepository
	logger       *zap.Logger
}

// NewCallbackConsistencyService 创建回调一致性服务
func NewCallbackConsistencyService(
	callbackDB *sql.DB,
	mainDB *sql.DB,
	orderRepo repository.OrderRepository,
	callbackRepo repository.CallbackRepository,
	logger *zap.Logger,
) *CallbackConsistencyService {
	return &CallbackConsistencyService{
		callbackDB:   callbackDB,
		mainDB:       mainDB,
		orderRepo:    orderRepo,
		callbackRepo: callbackRepo,
		logger:       logger,
	}
}

// InconsistentCallback 不一致的回调记录
type InconsistentCallback struct {
	CallbackID      string    `json:"callback_id"`
	Provider        string    `json:"provider"`
	TrackingNo      string    `json:"tracking_no"`
	CallbackStatus  string    `json:"callback_status"`
	OrderStatus     string    `json:"order_status"`
	CallbackTime    time.Time `json:"callback_time"`
	OrderUpdateTime time.Time `json:"order_update_time"`
	TimeDiff        string    `json:"time_diff"`
	Issue           string    `json:"issue"`
}

// CheckCallbackConsistency 检查回调一致性
func (s *CallbackConsistencyService) CheckCallbackConsistency(ctx context.Context, provider string, hours int) ([]*InconsistentCallback, error) {
	s.logger.Info("开始检查回调一致性",
		zap.String("provider", provider),
		zap.Int("hours", hours))

	query := `
	SELECT 
		cr.id as callback_id,
		cr.provider,
		cr.tracking_no,
		cr.internal_status as callback_status,
		cr.received_at as callback_time,
		cr.internal_error
	FROM callback_raw_data cr
	WHERE cr.provider = $1 
	  AND cr.processed = true
	  AND cr.received_at > NOW() - INTERVAL '%d hours'
	ORDER BY cr.received_at DESC
	`

	formattedQuery := fmt.Sprintf(query, hours)
	rows, err := s.callbackDB.QueryContext(ctx, formattedQuery, provider)
	if err != nil {
		return nil, fmt.Errorf("查询回调记录失败: %w", err)
	}
	defer rows.Close()

	var inconsistentCallbacks []*InconsistentCallback

	for rows.Next() {
		var callbackID, provider, trackingNo, callbackStatus, internalError string
		var callbackTime time.Time

		err := rows.Scan(&callbackID, &provider, &trackingNo, &callbackStatus, &callbackTime, &internalError)
		if err != nil {
			s.logger.Error("扫描回调记录失败", zap.Error(err))
			continue
		}

		// 查询对应的订单状态
		orderInfo, err := s.getOrderByTrackingNo(ctx, trackingNo)
		if err != nil {
			s.logger.Warn("查询订单信息失败",
				zap.String("tracking_no", trackingNo),
				zap.Error(err))
			continue
		}

		if orderInfo == nil {
			// 订单不存在
			inconsistentCallbacks = append(inconsistentCallbacks, &InconsistentCallback{
				CallbackID:     callbackID,
				Provider:       provider,
				TrackingNo:     trackingNo,
				CallbackStatus: callbackStatus,
				OrderStatus:    "NOT_FOUND",
				CallbackTime:   callbackTime,
				Issue:          "订单不存在",
			})
			continue
		}

		// 检查时间差异
		timeDiff := orderInfo.UpdatedAt.Sub(callbackTime)

		// 检查是否存在不一致
		if s.isInconsistent(callbackStatus, orderInfo.Status, timeDiff, internalError) {
			inconsistentCallbacks = append(inconsistentCallbacks, &InconsistentCallback{
				CallbackID:      callbackID,
				Provider:        provider,
				TrackingNo:      trackingNo,
				CallbackStatus:  callbackStatus,
				OrderStatus:     orderInfo.Status,
				CallbackTime:    callbackTime,
				OrderUpdateTime: orderInfo.UpdatedAt,
				TimeDiff:        timeDiff.String(),
				Issue:           s.determineIssue(callbackStatus, orderInfo.Status, timeDiff, internalError),
			})
		}
	}

	s.logger.Info("回调一致性检查完成",
		zap.String("provider", provider),
		zap.Int("inconsistent_count", len(inconsistentCallbacks)))

	return inconsistentCallbacks, nil
}

// CallbackOrderInfo 回调相关的订单信息
type CallbackOrderInfo struct {
	ID        int64     `json:"id"`
	OrderNo   string    `json:"order_no"`
	Status    string    `json:"status"`
	UpdatedAt time.Time `json:"updated_at"`
}

// getOrderByTrackingNo 根据运单号查询订单
func (s *CallbackConsistencyService) getOrderByTrackingNo(ctx context.Context, trackingNo string) (*CallbackOrderInfo, error) {
	query := `
	SELECT id, order_no, status, updated_at
	FROM order_records
	WHERE tracking_no = $1
	`

	var orderInfo CallbackOrderInfo
	err := s.mainDB.QueryRowContext(ctx, query, trackingNo).Scan(
		&orderInfo.ID, &orderInfo.OrderNo, &orderInfo.Status, &orderInfo.UpdatedAt)

	if err == sql.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		return nil, err
	}

	return &orderInfo, nil
}

// isInconsistent 判断是否不一致
func (s *CallbackConsistencyService) isInconsistent(callbackStatus, orderStatus string, timeDiff time.Duration, internalError string) bool {
	// 1. 回调处理失败但订单状态没有相应处理
	if callbackStatus == "failed" && internalError != "" {
		return true
	}

	// 2. 回调处理成功但订单状态在回调之后很久没有更新
	if callbackStatus == "success" && timeDiff < -time.Hour {
		return true
	}

	// 3. 回调还在处理中但时间过长
	if callbackStatus == "processing" && timeDiff < -30*time.Minute {
		return true
	}

	return false
}

// determineIssue 确定问题类型
func (s *CallbackConsistencyService) determineIssue(callbackStatus, orderStatus string, timeDiff time.Duration, internalError string) string {
	if callbackStatus == "failed" && internalError != "" {
		return fmt.Sprintf("回调处理失败: %s", internalError)
	}

	if callbackStatus == "success" && timeDiff < -time.Hour {
		return fmt.Sprintf("回调成功但订单状态未同步，时间差: %s", timeDiff.String())
	}

	if callbackStatus == "processing" && timeDiff < -30*time.Minute {
		return fmt.Sprintf("回调处理超时，处理时间: %s", timeDiff.String())
	}

	return "未知问题"
}

// FixInconsistentCallbacks 修复不一致的回调
func (s *CallbackConsistencyService) FixInconsistentCallbacks(ctx context.Context, callbackIDs []string) (*FixResult, error) {
	s.logger.Info("开始修复不一致的回调",
		zap.Int("callback_count", len(callbackIDs)))

	result := &FixResult{
		Total:   len(callbackIDs),
		Success: 0,
		Failed:  0,
		Details: make([]string, 0),
	}

	for _, callbackID := range callbackIDs {
		err := s.fixSingleCallback(ctx, callbackID)
		if err != nil {
			result.Failed++
			result.Details = append(result.Details, fmt.Sprintf("修复回调 %s 失败: %v", callbackID, err))
			s.logger.Error("修复回调失败",
				zap.String("callback_id", callbackID),
				zap.Error(err))
		} else {
			result.Success++
			result.Details = append(result.Details, fmt.Sprintf("修复回调 %s 成功", callbackID))
			s.logger.Info("修复回调成功",
				zap.String("callback_id", callbackID))
		}
	}

	s.logger.Info("回调修复完成",
		zap.Int("total", result.Total),
		zap.Int("success", result.Success),
		zap.Int("failed", result.Failed))

	return result, nil
}

// FixResult 修复结果
type FixResult struct {
	Total   int      `json:"total"`
	Success int      `json:"success"`
	Failed  int      `json:"failed"`
	Details []string `json:"details"`
}

// fixSingleCallback 修复单个回调
func (s *CallbackConsistencyService) fixSingleCallback(ctx context.Context, callbackID string) error {
	// 获取原始回调数据
	query := `SELECT raw_body FROM callback_raw_data WHERE id = $1`
	var rawBody string
	err := s.callbackDB.QueryRowContext(ctx, query, callbackID).Scan(&rawBody)
	if err != nil {
		return fmt.Errorf("获取回调数据失败: %w", err)
	}

	// 重新推送回调（这里需要调用统一回调服务）
	// 由于循环依赖问题，这里使用HTTP调用的方式
	return s.repushCallback(ctx, rawBody)
}

// repushCallback 重新推送回调
func (s *CallbackConsistencyService) repushCallback(ctx context.Context, rawBody string) error {
	// 这里应该调用统一回调服务的重新处理接口
	// 为了避免循环依赖，使用HTTP调用
	s.logger.Info("重新推送回调", zap.String("raw_body", rawBody[:100]+"..."))

	// TODO: 实现HTTP调用重新推送逻辑
	return nil
}
