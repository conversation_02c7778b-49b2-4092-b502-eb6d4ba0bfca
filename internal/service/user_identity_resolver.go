package service

import (
	"fmt"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/your-org/go-kuaidi/internal/user"
	"go.uber.org/zap"
)

// UserIdentityResolver 生产级用户身份解析服务
// 高性能、高并发、高稳定性的用户身份验证
type UserIdentityResolver struct {
	userRepo user.UserRepository
	logger   *zap.Logger

	// 缓存层 - 避免重复数据库查询
	cache    map[string]*CachedUserIdentity
	cacheMu  sync.RWMutex
	cacheExp time.Duration
}

// CachedUserIdentity 缓存的用户身份信息
type CachedUserIdentity struct {
	UserID    string
	ClientID  string
	ExpiresAt time.Time
}

// NewUserIdentityResolver 创建用户身份解析服务
func NewUserIdentityResolver(userRepo user.UserRepository, logger *zap.Logger) *UserIdentityResolver {
	return &UserIdentityResolver{
		userRepo: userRepo,
		logger:   logger,
		cache:    make(map[string]*CachedUserIdentity),
		cacheExp: 5 * time.Minute, // 5分钟缓存
	}
}

// ResolveUserID 从Gin上下文中解析用户ID
// 这是唯一的用户ID获取入口，确保一致性
func (r *UserIdentityResolver) ResolveUserID(c *gin.Context) (string, error) {
	// 1. 优先从userID获取（JWT认证设置）
	if userID := r.getStringFromContext(c, "userID"); userID != "" {
		return userID, nil
	}

	// 2. 从user_id获取（备用JWT字段）
	if userID := r.getStringFromContext(c, "user_id"); userID != "" {
		return userID, nil
	}

	// 3. 通过client_id查询数据库获取用户ID
	if clientID := r.getStringFromContext(c, "client_id"); clientID != "" {
		return r.getUserIDByClientID(clientID)
	}

	// 4. 通过签名验证的client_id获取
	if clientID := r.getStringFromContext(c, "client_id_from_signature"); clientID != "" {
		return r.getUserIDByClientID(clientID)
	}

	return "", fmt.Errorf("无法解析用户身份：所有认证方式都失败")
}

// getStringFromContext 安全地从上下文获取字符串值
func (r *UserIdentityResolver) getStringFromContext(c *gin.Context, key string) string {
	if value, exists := c.Get(key); exists {
		if str, ok := value.(string); ok && str != "" {
			return str
		}
	}
	return ""
}

// getUserIDByClientID 通过客户端ID获取用户ID（带缓存）
func (r *UserIdentityResolver) getUserIDByClientID(clientID string) (string, error) {
	// 检查缓存
	if userID := r.getCachedUserID(clientID); userID != "" {
		return userID, nil
	}

	// 查询数据库
	foundUser, err := r.userRepo.FindByClientID(clientID)
	if err != nil {
		r.logger.Error("通过客户端ID查找用户失败",
			zap.String("client_id", clientID),
			zap.Error(err))
		return "", fmt.Errorf("用户不存在")
	}

	if !foundUser.IsActive {
		return "", fmt.Errorf("用户账户已被禁用")
	}

	// 更新缓存
	r.setCachedUserID(clientID, foundUser.ID)

	return foundUser.ID, nil
}

// getCachedUserID 从缓存获取用户ID
func (r *UserIdentityResolver) getCachedUserID(clientID string) string {
	r.cacheMu.RLock()
	defer r.cacheMu.RUnlock()

	if cached, exists := r.cache[clientID]; exists {
		if time.Now().Before(cached.ExpiresAt) {
			return cached.UserID
		}
		// 缓存过期，删除
		delete(r.cache, clientID)
	}
	return ""
}

// setCachedUserID 设置缓存
func (r *UserIdentityResolver) setCachedUserID(clientID, userID string) {
	r.cacheMu.Lock()
	defer r.cacheMu.Unlock()

	r.cache[clientID] = &CachedUserIdentity{
		UserID:    userID,
		ClientID:  clientID,
		ExpiresAt: time.Now().Add(r.cacheExp),
	}
}

// ValidateUserOwnership 验证用户是否拥有指定资源
func (r *UserIdentityResolver) ValidateUserOwnership(requestUserID, resourceUserID string) error {
	if requestUserID == "" {
		return fmt.Errorf("请求用户ID不能为空")
	}
	if resourceUserID == "" {
		return fmt.Errorf("资源用户ID不能为空")
	}
	if requestUserID != resourceUserID {
		r.logger.Warn("用户权限验证失败",
			zap.String("request_user_id", requestUserID),
			zap.String("resource_user_id", resourceUserID))
		return fmt.Errorf("无权限访问该资源")
	}
	return nil
}

// ClearCache 清理缓存（用于测试或重置）
func (r *UserIdentityResolver) ClearCache() {
	r.cacheMu.Lock()
	defer r.cacheMu.Unlock()
	r.cache = make(map[string]*CachedUserIdentity)
}

// GetCacheStats 获取缓存统计信息
func (r *UserIdentityResolver) GetCacheStats() map[string]interface{} {
	r.cacheMu.RLock()
	defer r.cacheMu.RUnlock()

	validCount := 0
	expiredCount := 0
	now := time.Now()

	for _, cached := range r.cache {
		if now.Before(cached.ExpiresAt) {
			validCount++
		} else {
			expiredCount++
		}
	}

	return map[string]interface{}{
		"total_entries":   len(r.cache),
		"valid_entries":   validCount,
		"expired_entries": expiredCount,
		"cache_duration":  r.cacheExp.String(),
	}
}
