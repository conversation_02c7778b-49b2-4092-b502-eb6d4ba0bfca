package service

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/repository"
	"go.uber.org/zap"
)

// FailedOrderService 失败订单服务
type FailedOrderService struct {
	orderRepository        repository.OrderRepository
	systemConfigService    SystemConfigService
	platformOrderGenerator *PlatformOrderGenerator // 🔥 新增：平台订单号生成器
	logger                 *zap.Logger
}

// NewFailedOrderService 创建失败订单服务
func NewFailedOrderService(
	orderRepository repository.OrderRepository,
	systemConfigService SystemConfigService,
	logger *zap.Logger,
) *FailedOrderService {
	return &FailedOrderService{
		orderRepository:     orderRepository,
		systemConfigService: systemConfigService,
		logger:              logger,
	}
}

// NewFailedOrderServiceWithPlatformGenerator 创建带平台订单号生成器的失败订单服务
func NewFailedOrderServiceWithPlatformGenerator(
	orderRepository repository.OrderRepository,
	systemConfigService SystemConfigService,
	platformOrderGenerator *PlatformOrderGenerator,
	logger *zap.Logger,
) *FailedOrderService {
	return &FailedOrderService{
		orderRepository:        orderRepository,
		systemConfigService:    systemConfigService,
		platformOrderGenerator: platformOrderGenerator,
		logger:                 logger,
	}
}

// CreateFailedOrderRecord 创建失败订单记录
func (s *FailedOrderService) CreateFailedOrderRecord(ctx context.Context, req *model.FailedOrderCreateRequest) error {
	// 检查是否启用失败订单记录
	enabled, err := s.systemConfigService.GetBoolConfig(ctx, "order_failure", "enable_failed_order_record", true)
	if err != nil {
		s.logger.Error("获取失败订单记录配置失败", zap.Error(err))
		return fmt.Errorf("获取失败订单记录配置失败: %w", err)
	}

	if !enabled {
		s.logger.Debug("失败订单记录功能已禁用")
		return nil
	}

	// 🔥 生成平台订单号（失败订单也使用统一的平台订单号）
	var platformOrderNo string
	if s.platformOrderGenerator != nil {
		generatedOrderNo, err := s.platformOrderGenerator.GeneratePlatformOrderNo(ctx)
		if err != nil {
			s.logger.Error("生成失败订单平台订单号失败", zap.Error(err))
			// 如果平台订单号生成失败，使用传统的失败订单号格式
			platformOrderNo = s.generateFailedOrderNo(req.OrderRequest.CustomerOrderNo)
		} else {
			platformOrderNo = generatedOrderNo
			s.logger.Info("失败订单平台订单号生成成功", zap.String("platform_order_no", platformOrderNo))
		}
	} else {
		// 如果没有平台订单号生成器，使用传统的失败订单号格式
		platformOrderNo = s.generateFailedOrderNo(req.OrderRequest.CustomerOrderNo)
		s.logger.Warn("平台订单号生成器未初始化，失败订单使用传统格式")
	}

	// 创建失败订单记录
	failedOrder := &model.OrderRecord{
		PlatformOrderNo: platformOrderNo, // 🔥 设置平台订单号
		CustomerOrderNo: req.OrderRequest.CustomerOrderNo,
		OrderNo:         platformOrderNo, // 🔥 失败订单的OrderNo也使用平台订单号
		ExpressType:     req.OrderRequest.ExpressType,
		Provider:        req.OrderRequest.Provider,
		Status:          model.OrderStatusFailed,
		UserID:          req.OrderRequest.UserID,

		// 🔥 修复：确保失败订单记录重量和体积信息
		Weight:      req.OrderRequest.Package.Weight, // 下单重量
		OrderVolume: req.OrderRequest.Package.Volume, // 订单体积

		// 失败相关信息
		FailureReason:  req.FailureInfo.FailureReason,
		FailureMessage: req.FailureInfo.FailureMessage,
		FailureStage:   req.FailureInfo.FailureStage,
		FailureSource:  req.FailureInfo.FailureSource,
		FailureTime:    req.FailureInfo.FailureTime.Format(time.RFC3339),
		CanRetry:       req.FailureInfo.CanRetry,

		// 🔥 彻底修复：使用安全的JSON序列化，避免PostgreSQL JSON语法错误
		SenderInfo:   s.safeJSONMarshal(req.OrderRequest.Sender, "sender_info"),
		ReceiverInfo: s.safeJSONMarshal(req.OrderRequest.Receiver, "receiver_info"),
		PackageInfo:  s.safeJSONMarshal(req.OrderRequest.Package, "package_info"),
		RequestData:  s.safeJSONMarshal(req.OrderRequest, "request_data"),
		ResponseData: s.safeJSONMarshal(req.FailureInfo, "response_data"),

		// 价格信息
		Price:         req.EstimatedPrice,
		BillingStatus: model.BillingStatusFailed,

		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// 保存到数据库
	if err := s.orderRepository.CreateFailedOrder(ctx, failedOrder); err != nil {
		s.logger.Error("创建失败订单记录失败",
			zap.String("customer_order_no", req.OrderRequest.CustomerOrderNo),
			zap.String("user_id", req.OrderRequest.UserID),
			zap.Error(err))
		return fmt.Errorf("创建失败订单记录失败: %w", err)
	}

	s.logger.Info("成功创建失败订单记录",
		zap.String("customer_order_no", req.OrderRequest.CustomerOrderNo),
		zap.String("platform_order_no", platformOrderNo),
		zap.String("user_id", req.OrderRequest.UserID),
		zap.String("failure_reason", req.FailureInfo.FailureReason),
		zap.String("failure_stage", req.FailureInfo.FailureStage))

	return nil
}

// GetFailedOrdersByUser 获取用户的失败订单
func (s *FailedOrderService) GetFailedOrdersByUser(ctx context.Context, userID string, limit, offset int) ([]*model.OrderRecord, error) {
	// 检查是否显示失败订单
	showFailedOrders, err := s.systemConfigService.GetBoolConfig(ctx, "order_failure", "show_failed_orders_in_list", true)
	if err != nil {
		s.logger.Error("获取失败订单显示配置失败", zap.Error(err))
		return nil, fmt.Errorf("获取失败订单显示配置失败: %w", err)
	}

	if !showFailedOrders {
		return []*model.OrderRecord{}, nil
	}

	return s.orderRepository.GetFailedOrdersByUser(ctx, userID, limit, offset)
}

// GetFailedOrderDetail 获取失败订单详情
func (s *FailedOrderService) GetFailedOrderDetail(ctx context.Context, orderNo string, userID string) (*model.OrderRecord, error) {
	// 检查是否显示失败订单详情
	showFailureDetails, err := s.systemConfigService.GetBoolConfig(ctx, "order_failure", "show_failure_details", true)
	if err != nil {
		s.logger.Error("获取失败订单详情显示配置失败", zap.Error(err))
		return nil, fmt.Errorf("获取失败订单详情显示配置失败: %w", err)
	}

	if !showFailureDetails {
		return nil, fmt.Errorf("失败订单详情查看功能已禁用")
	}

	return s.orderRepository.GetFailedOrderDetail(ctx, orderNo, userID)
}

// CanRetryOrder 检查订单是否可重试
func (s *FailedOrderService) CanRetryOrder(ctx context.Context, orderNo string, userID string) (bool, error) {
	failedOrder, err := s.GetFailedOrderDetail(ctx, orderNo, userID)
	if err != nil {
		return false, err
	}

	if failedOrder == nil {
		return false, fmt.Errorf("未找到失败订单")
	}

	// 检查是否启用自动重试
	autoRetryEnabled, err := s.systemConfigService.GetBoolConfig(ctx, "order_failure", "auto_retry_enabled", true)
	if err != nil {
		s.logger.Error("获取自动重试配置失败", zap.Error(err))
		return false, fmt.Errorf("获取自动重试配置失败: %w", err)
	}

	if !autoRetryEnabled {
		return false, nil
	}

	return failedOrder.CanRetry, nil
}

// GetFailureStatistics 获取失败订单统计
func (s *FailedOrderService) GetFailureStatistics(ctx context.Context, userID string) (*model.FailureStatistics, error) {
	// 获取统计时间窗口
	windowHours, err := s.systemConfigService.GetIntConfig(ctx, "order_failure", "failure_stats_window_hours", 24)
	if err != nil {
		s.logger.Error("获取失败订单统计时间窗口配置失败", zap.Error(err))
		return nil, fmt.Errorf("获取失败订单统计时间窗口配置失败: %w", err)
	}

	startTime := time.Now().Add(-time.Duration(windowHours) * time.Hour)
	return s.orderRepository.GetFailureStatistics(ctx, userID, startTime)
}

// CleanupExpiredFailedOrders 清理过期的失败订单
func (s *FailedOrderService) CleanupExpiredFailedOrders(ctx context.Context) error {
	// 检查是否启用失败订单清理
	cleanupEnabled, err := s.systemConfigService.GetBoolConfig(ctx, "order_failure", "enable_failed_order_cleanup", true)
	if err != nil {
		s.logger.Error("获取失败订单清理配置失败", zap.Error(err))
		return fmt.Errorf("获取失败订单清理配置失败: %w", err)
	}

	if !cleanupEnabled {
		s.logger.Debug("失败订单清理功能已禁用")
		return nil
	}

	// 获取保留天数
	retentionDays, err := s.systemConfigService.GetIntConfig(ctx, "order_failure", "failed_order_retention_days", 30)
	if err != nil {
		s.logger.Error("获取失败订单保留天数配置失败", zap.Error(err))
		return fmt.Errorf("获取失败订单保留天数配置失败: %w", err)
	}

	cutoffTime := time.Now().AddDate(0, 0, -retentionDays)
	deletedCount, err := s.orderRepository.DeleteExpiredFailedOrders(ctx, cutoffTime)
	if err != nil {
		s.logger.Error("清理过期失败订单失败", zap.Error(err))
		return fmt.Errorf("清理过期失败订单失败: %w", err)
	}

	s.logger.Info("成功清理过期失败订单",
		zap.Int("deleted_count", deletedCount),
		zap.Int("retention_days", retentionDays))

	return nil
}

// generateFailedOrderNo 生成失败订单号
func (s *FailedOrderService) generateFailedOrderNo(customerOrderNo string) string {
	timestamp := time.Now().Format("20060102150405")
	return fmt.Sprintf("FAIL-%s-%s", strings.ToUpper(customerOrderNo), timestamp)
}

// safeJSONMarshal 安全地将对象序列化为JSON字符串，专门用于数据库存储
func (s *FailedOrderService) safeJSONMarshal(obj interface{}, fieldName string) string {
	// 🔥 修复：处理nil对象
	if obj == nil {
		s.logger.Debug("对象为nil，返回空JSON对象", zap.String("field", fieldName))
		return "{}"
	}

	// 🔥 修复：先序列化为JSON
	data, err := json.Marshal(obj)
	if err != nil {
		s.logger.Error("JSON序列化失败",
			zap.Error(err),
			zap.String("field", fieldName),
			zap.String("object_type", fmt.Sprintf("%T", obj)))
		return "{}"
	}

	// 🔥 修复：验证生成的JSON是否有效
	if !json.Valid(data) {
		s.logger.Error("生成的JSON无效",
			zap.String("field", fieldName),
			zap.String("json_data", string(data)),
			zap.String("object_type", fmt.Sprintf("%T", obj)))
		return "{}"
	}

	// 🔥 修复：测试JSON是否能被PostgreSQL接受
	// 尝试反序列化以确保JSON格式完全正确
	var testObj interface{}
	if err := json.Unmarshal(data, &testObj); err != nil {
		s.logger.Error("JSON反序列化测试失败",
			zap.Error(err),
			zap.String("field", fieldName),
			zap.String("json_data", string(data)))
		return "{}"
	}

	s.logger.Debug("JSON序列化成功",
		zap.String("field", fieldName),
		zap.String("object_type", fmt.Sprintf("%T", obj)),
		zap.Int("json_length", len(data)))

	return string(data)
}
