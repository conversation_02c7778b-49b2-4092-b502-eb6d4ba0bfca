package service

import (
	"context"
	"database/sql"
	"fmt"
	"sync"
	"time"

	_ "github.com/lib/pq" // PostgreSQL driver
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/repository"
	"go.uber.org/zap"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// BillingCheckMetrics 费用检查监控指标
type BillingCheckMetrics struct {
	ChecksTotal     prometheus.Counter
	CheckDuration   prometheus.Histogram
	OrdersProcessed prometheus.Counter
	ProblemsFound   prometheus.Counter
	FixesApplied    prometheus.Counter
	FixesFailed     prometheus.Counter
}

// NewBillingCheckMetrics 创建监控指标
func NewBillingCheckMetrics() *BillingCheckMetrics {
	return &BillingCheckMetrics{
		ChecksTotal: promauto.NewCounter(prometheus.CounterOpts{
			Name: "billing_difference_checks_total",
			Help: "费用差异检查总次数",
		}),
		CheckDuration: promauto.NewHistogram(prometheus.HistogramOpts{
			Name:    "billing_difference_check_duration_seconds",
			Help:    "费用差异检查耗时",
			Buckets: prometheus.DefBuckets,
		}),
		OrdersProcessed: promauto.NewCounter(prometheus.CounterOpts{
			Name: "billing_difference_orders_processed_total",
			Help: "处理的订单总数",
		}),
		ProblemsFound: promauto.NewCounter(prometheus.CounterOpts{
			Name: "billing_difference_problems_found_total",
			Help: "发现的问题订单总数",
		}),
		FixesApplied: promauto.NewCounter(prometheus.CounterOpts{
			Name: "billing_difference_fixes_applied_total",
			Help: "成功修复的订单总数",
		}),
		FixesFailed: promauto.NewCounter(prometheus.CounterOpts{
			Name: "billing_difference_fixes_failed_total",
			Help: "修复失败的订单总数",
		}),
	}
}

// BillingCheckerConfig 费用差异检查配置
type BillingCheckerConfig struct {
	DryRun       bool   `json:"dry_run"`       // 干运行模式
	Days         int    `json:"days"`          // 检查天数
	BatchSize    int    `json:"batch_size"`    // 批处理大小
	MaxWorkers   int    `json:"max_workers"`   // 最大工作协程数
	ReportFormat string `json:"report_format"` // 报告格式
	OutputFile   string `json:"output_file"`   // 输出文件
}

// BillingCheckStatistics 费用检查统计信息
type BillingCheckStatistics struct {
	TotalOrders           int     `json:"total_orders"`            // 检查订单总数
	ProblemsFound         int     `json:"problems_found"`          // 发现问题订单数
	FixedOrders           int     `json:"fixed_orders"`            // 修复成功订单数
	FailedFixes           int     `json:"failed_fixes"`            // 修复失败订单数
	SkippedOrders         int     `json:"skipped_orders"`          // 跳过订单数
	TotalDifferenceAmount float64 `json:"total_difference_amount"` // 总差异金额
	FixedAmount           float64 `json:"fixed_amount"`            // 已修复金额
	PendingAmount         float64 `json:"pending_amount"`          // 待修复金额
}

// BillingCheckReport 费用检查报告
type BillingCheckReport struct {
	CheckTime  time.Time                `json:"check_time"` // 检查时间
	StartTime  time.Time                `json:"start_time"` // 开始时间
	EndTime    time.Time                `json:"end_time"`   // 结束时间
	Config     *BillingCheckerConfig    `json:"config"`     // 配置信息
	Statistics *BillingCheckStatistics  `json:"statistics"` // 统计信息
	Details    []*OrderDifferenceDetail `json:"details"`    // 详细信息
	Errors     []string                 `json:"errors"`     // 错误信息
}

// OrderDifferenceDetail 订单差异详情
type OrderDifferenceDetail struct {
	PlatformOrderNo  string    `json:"platform_order_no"`   // 平台订单号
	CustomerOrderNo  string    `json:"customer_order_no"`   // 客户订单号
	UserID           string    `json:"user_id"`             // 用户ID
	Provider         string    `json:"provider"`            // 供应商
	Status           string    `json:"status"`              // 订单状态
	CreatedAt        time.Time `json:"created_at"`          // 创建时间
	UserPaidAmount   float64   `json:"user_paid_amount"`    // 用户实际支付金额
	CallbackFee      float64   `json:"callback_fee"`        // 回调费用
	DifferenceAmount float64   `json:"difference_amount"`   // 差异金额
	DifferenceType   string    `json:"difference_type"`     // 差异类型
	FixAttempted     bool      `json:"fix_attempted"`       // 是否尝试修复
	FixSuccessful    bool      `json:"fix_successful"`      // 修复是否成功
	FixError         string    `json:"fix_error,omitempty"` // 修复错误信息
	ProcessedAt      time.Time `json:"processed_at"`        // 处理时间
}

// BillingDifferenceChecker 费用差异检查器
type BillingDifferenceChecker struct {
	config         *BillingCheckerConfig
	logger         *zap.Logger
	db             *sql.DB
	gormDB         *gorm.DB
	orderRepo      repository.OrderRepository
	balanceService BalanceService
	billingService BillingService

	// 监控指标
	metrics *BillingCheckMetrics

	// 工作协程管理
	workerPool chan struct{}
	wg         sync.WaitGroup
	mu         sync.RWMutex

	// 统计信息
	statistics *BillingCheckStatistics
	details    []*OrderDifferenceDetail
	errors     []string
}

// NewBillingDifferenceChecker 创建费用差异检查器
func NewBillingDifferenceChecker(
	config *BillingCheckerConfig,
	logger *zap.Logger,
	configManager ConfigManager,
) (*BillingDifferenceChecker, error) {
	// 初始化数据库连接
	db, err := initDatabaseConnection(configManager)
	if err != nil {
		return nil, fmt.Errorf("初始化数据库连接失败: %w", err)
	}

	gormDB, err := initGormConnection(configManager)
	if err != nil {
		return nil, fmt.Errorf("初始化GORM连接失败: %w", err)
	}

	// 创建仓储层
	orderRepo := repository.NewPostgresOrderRepository(db)
	balanceRepo := repository.NewPostgresBalanceRepository(db)
	billingRepo := repository.NewPostgresBillingRepository(db)

	// 创建服务层
	balanceService := NewBalanceService(balanceRepo, orderRepo, db, logger)
	billingService := NewBillingService(orderRepo, billingRepo, balanceService, logger)

	// 初始化监控指标
	metrics := NewBillingCheckMetrics()

	// 创建工作协程池
	workerPool := make(chan struct{}, config.MaxWorkers)

	checker := &BillingDifferenceChecker{
		config:         config,
		logger:         logger,
		db:             db,
		gormDB:         gormDB,
		orderRepo:      orderRepo,
		balanceService: balanceService,
		billingService: billingService,
		metrics:        metrics,
		workerPool:     workerPool,
		statistics:     &BillingCheckStatistics{},
		details:        make([]*OrderDifferenceDetail, 0),
		errors:         make([]string, 0),
	}

	logger.Info("费用差异检查器初始化完成",
		zap.Bool("dry_run", config.DryRun),
		zap.Int("days", config.Days),
		zap.Int("batch_size", config.BatchSize),
		zap.Int("max_workers", config.MaxWorkers))

	return checker, nil
}

// CheckAndFix 执行费用差异检查和修复
func (c *BillingDifferenceChecker) CheckAndFix(ctx context.Context) (*BillingCheckReport, error) {
	startTime := time.Now()
	c.logger.Info("开始执行费用差异检查",
		zap.Time("start_time", startTime),
		zap.Bool("dry_run", c.config.DryRun))

	// 记录开始指标
	c.metrics.ChecksTotal.Inc()
	c.metrics.CheckDuration.Observe(0) // 开始计时

	defer func() {
		// 记录结束指标
		duration := time.Since(startTime).Seconds()
		c.metrics.CheckDuration.Observe(duration)
	}()

	// 1. 获取需要检查的订单
	orders, err := c.getOrdersToCheck(ctx)
	if err != nil {
		c.recordError(fmt.Sprintf("获取订单数据失败: %v", err))
		return nil, fmt.Errorf("获取订单数据失败: %w", err)
	}

	c.statistics.TotalOrders = len(orders)
	c.logger.Info("获取到待检查订单",
		zap.Int("total_orders", len(orders)))

	// 2. 批量处理订单
	if err := c.processOrdersBatch(ctx, orders); err != nil {
		c.recordError(fmt.Sprintf("批量处理订单失败: %v", err))
		return nil, fmt.Errorf("批量处理订单失败: %w", err)
	}

	// 3. 等待所有工作协程完成
	c.wg.Wait()

	endTime := time.Now()
	c.logger.Info("费用差异检查完成",
		zap.Time("end_time", endTime),
		zap.Duration("duration", endTime.Sub(startTime)),
		zap.Int("total_orders", c.statistics.TotalOrders),
		zap.Int("problems_found", c.statistics.ProblemsFound),
		zap.Int("fixed_orders", c.statistics.FixedOrders))

	// 4. 生成报告
	report := &BillingCheckReport{
		CheckTime:  startTime,
		StartTime:  startTime,
		EndTime:    endTime,
		Config:     c.config,
		Statistics: c.statistics,
		Details:    c.details,
		Errors:     c.errors,
	}

	return report, nil
}

// getOrdersToCheck 获取需要检查的订单
func (c *BillingDifferenceChecker) getOrdersToCheck(ctx context.Context) ([]*model.OrderRecord, error) {
	// 计算时间范围
	endTime := time.Now()
	startTime := endTime.AddDate(0, 0, -c.config.Days)

	c.logger.Info("查询订单数据",
		zap.Time("start_time", startTime),
		zap.Time("end_time", endTime))

	// 查询成功创建的订单，排除已取消或失败的订单
	query := `
		SELECT platform_order_no, customer_order_no, order_no, user_id, provider, 
		       status, price, actual_fee, created_at, updated_at
		FROM orders 
		WHERE created_at >= $1 AND created_at <= $2
		  AND status NOT IN ('failed', 'cancelled', 'voided', 'submit_failed', 'print_failed')
		  AND platform_order_no IS NOT NULL
		ORDER BY created_at DESC
	`

	rows, err := c.db.QueryContext(ctx, query, startTime, endTime)
	if err != nil {
		return nil, fmt.Errorf("查询订单失败: %w", err)
	}
	defer rows.Close()

	var orders []*model.OrderRecord
	for rows.Next() {
		order := &model.OrderRecord{}
		err := rows.Scan(
			&order.PlatformOrderNo,
			&order.CustomerOrderNo,
			&order.OrderNo,
			&order.UserID,
			&order.Provider,
			&order.Status,
			&order.Price,
			&order.ActualFee,
			&order.CreatedAt,
			&order.UpdatedAt,
		)
		if err != nil {
			c.logger.Warn("扫描订单数据失败", zap.Error(err))
			continue
		}
		orders = append(orders, order)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历订单结果失败: %w", err)
	}

	return orders, nil
}

// processOrdersBatch 批量处理订单
func (c *BillingDifferenceChecker) processOrdersBatch(ctx context.Context, orders []*model.OrderRecord) error {
	// 按批次处理订单
	for i := 0; i < len(orders); i += c.config.BatchSize {
		end := i + c.config.BatchSize
		if end > len(orders) {
			end = len(orders)
		}

		batch := orders[i:end]
		c.logger.Debug("处理订单批次",
			zap.Int("batch_start", i),
			zap.Int("batch_end", end),
			zap.Int("batch_size", len(batch)))

		// 并发处理批次中的订单
		for _, order := range batch {
			c.wg.Add(1)
			go c.processOrderConcurrent(ctx, order)
		}
	}

	return nil
}

// processOrderConcurrent 并发处理单个订单
func (c *BillingDifferenceChecker) processOrderConcurrent(ctx context.Context, order *model.OrderRecord) {
	defer c.wg.Done()

	// 获取工作协程槽位
	c.workerPool <- struct{}{}
	defer func() { <-c.workerPool }()

	// 处理订单
	if err := c.processOrder(ctx, order); err != nil {
		c.recordError(fmt.Sprintf("处理订单失败 [%s]: %v", order.PlatformOrderNo, err))
	}
}

// processOrder 处理单个订单的费用差异检查
func (c *BillingDifferenceChecker) processOrder(ctx context.Context, order *model.OrderRecord) error {
	c.logger.Debug("开始处理订单",
		zap.String("platform_order_no", order.PlatformOrderNo),
		zap.String("customer_order_no", order.CustomerOrderNo),
		zap.String("provider", order.Provider),
		zap.String("status", order.Status))

	// 1. 计算用户实际支付金额
	userPaidAmount, err := c.calculateUserPaidAmount(ctx, order)
	if err != nil {
		c.logger.Warn("计算用户实际支付金额失败",
			zap.String("platform_order_no", order.PlatformOrderNo),
			zap.Error(err))
		c.incrementSkippedOrders()
		return fmt.Errorf("计算用户实际支付金额失败: %w", err)
	}

	// 2. 获取回调费用信息
	callbackFee, err := c.getCallbackFee(ctx, order)
	if err != nil {
		c.logger.Debug("获取回调费用失败，可能尚未收到回调",
			zap.String("platform_order_no", order.PlatformOrderNo),
			zap.Error(err))
		// 没有回调费用不算错误，跳过处理
		c.incrementSkippedOrders()
		return nil
	}

	// 3. 计算费用差异
	differenceAmount := callbackFee - userPaidAmount
	differenceType := c.getDifferenceType(differenceAmount)

	c.logger.Debug("费用差异计算结果",
		zap.String("platform_order_no", order.PlatformOrderNo),
		zap.Float64("user_paid_amount", userPaidAmount),
		zap.Float64("callback_fee", callbackFee),
		zap.Float64("difference_amount", differenceAmount),
		zap.String("difference_type", differenceType))

	// 4. 创建订单差异详情
	detail := &OrderDifferenceDetail{
		PlatformOrderNo:  order.PlatformOrderNo,
		CustomerOrderNo:  order.CustomerOrderNo,
		UserID:           order.UserID,
		Provider:         order.Provider,
		Status:           order.Status,
		CreatedAt:        order.CreatedAt,
		UserPaidAmount:   userPaidAmount,
		CallbackFee:      callbackFee,
		DifferenceAmount: differenceAmount,
		DifferenceType:   differenceType,
		ProcessedAt:      time.Now(),
	}

	// 5. 检查是否需要修复
	needsFix := c.needsFixing(differenceAmount)
	if !needsFix {
		c.logger.Debug("费用差异在允许范围内，无需修复",
			zap.String("platform_order_no", order.PlatformOrderNo),
			zap.Float64("difference_amount", differenceAmount))
		c.addOrderDetail(detail)
		return nil
	}

	// 6. 记录发现问题
	c.incrementProblemsFound()
	c.addTotalDifferenceAmount(differenceAmount)

	// 7. 尝试修复（如果不是干运行模式）
	if !c.config.DryRun {
		detail.FixAttempted = true
		if err := c.fixBillingDifference(ctx, order, callbackFee); err != nil {
			detail.FixSuccessful = false
			detail.FixError = err.Error()
			c.incrementFailedFixes()
			c.logger.Error("修复费用差异失败",
				zap.String("platform_order_no", order.PlatformOrderNo),
				zap.Float64("difference_amount", differenceAmount),
				zap.Error(err))
		} else {
			detail.FixSuccessful = true
			c.incrementFixedOrders()
			c.addFixedAmount(differenceAmount)
			c.logger.Info("费用差异修复成功",
				zap.String("platform_order_no", order.PlatformOrderNo),
				zap.Float64("difference_amount", differenceAmount))
		}
	} else {
		c.addPendingAmount(differenceAmount)
		c.logger.Info("发现费用差异（干运行模式，未修复）",
			zap.String("platform_order_no", order.PlatformOrderNo),
			zap.Float64("difference_amount", differenceAmount))
	}

	c.addOrderDetail(detail)
	return nil
}

// calculateUserPaidAmount 计算用户实际支付金额
func (c *BillingDifferenceChecker) calculateUserPaidAmount(ctx context.Context, order *model.OrderRecord) (float64, error) {
	// 使用现有的GetOrderNetPayment方法
	netPayment, err := c.balanceService.GetOrderNetPayment(ctx, order.UserID, order.OrderNo, order.CustomerOrderNo)
	if err != nil {
		return 0, fmt.Errorf("获取订单净支付金额失败: %w", err)
	}

	amount, _ := netPayment.Float64()
	return amount, nil
}

// getCallbackFee 获取回调费用
func (c *BillingDifferenceChecker) getCallbackFee(ctx context.Context, order *model.OrderRecord) (float64, error) {
	// 查询最新的回调费用信息
	query := `
		SELECT
			COALESCE(
				CAST(standardized_data->>'total_fee' AS DECIMAL),
				CAST(standardized_data->>'fee' AS DECIMAL),
				CAST(standardized_data->>'price' AS DECIMAL),
				0
			) as callback_fee
		FROM unified_callback_records
		WHERE (order_no = $1 OR customer_order_no = $1 OR platform_order_no = $1)
		  AND callback_type = 'billing'
		  AND standardized_data IS NOT NULL
		  AND (
			standardized_data->>'total_fee' IS NOT NULL OR
			standardized_data->>'fee' IS NOT NULL OR
			standardized_data->>'price' IS NOT NULL
		  )
		ORDER BY received_at DESC
		LIMIT 1
	`

	var callbackFee float64
	err := c.db.QueryRowContext(ctx, query, order.PlatformOrderNo).Scan(&callbackFee)
	if err != nil {
		if err == sql.ErrNoRows {
			return 0, fmt.Errorf("未找到回调费用信息")
		}
		return 0, fmt.Errorf("查询回调费用失败: %w", err)
	}

	return callbackFee, nil
}

// getDifferenceType 获取差异类型描述
func (c *BillingDifferenceChecker) getDifferenceType(difference float64) string {
	const threshold = 0.01 // 1分钱的精度阈值

	if difference > threshold {
		return "需要补收"
	} else if difference < -threshold {
		return "需要退款"
	}
	return "无需调整"
}

// needsFixing 判断是否需要修复
func (c *BillingDifferenceChecker) needsFixing(difference float64) bool {
	const threshold = 0.01 // 1分钱的精度阈值
	return difference > threshold || difference < -threshold
}

// fixBillingDifference 修复费用差异
func (c *BillingDifferenceChecker) fixBillingDifference(ctx context.Context, order *model.OrderRecord, actualFee float64) error {
	// 调用现有的ProcessBillingDifference方法
	reason := fmt.Sprintf("费用差异检查工具自动修复 - 订单号: %s", order.PlatformOrderNo)
	return c.billingService.ProcessBillingDifference(ctx, order.PlatformOrderNo, actualFee, reason)
}

// 统计信息更新方法（线程安全）
func (c *BillingDifferenceChecker) incrementProblemsFound() {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.statistics.ProblemsFound++
}

func (c *BillingDifferenceChecker) incrementFixedOrders() {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.statistics.FixedOrders++
}

func (c *BillingDifferenceChecker) incrementFailedFixes() {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.statistics.FailedFixes++
}

func (c *BillingDifferenceChecker) incrementSkippedOrders() {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.statistics.SkippedOrders++
}

func (c *BillingDifferenceChecker) addTotalDifferenceAmount(amount float64) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.statistics.TotalDifferenceAmount += amount
}

func (c *BillingDifferenceChecker) addFixedAmount(amount float64) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.statistics.FixedAmount += amount
}

func (c *BillingDifferenceChecker) addPendingAmount(amount float64) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.statistics.PendingAmount += amount
}

func (c *BillingDifferenceChecker) addOrderDetail(detail *OrderDifferenceDetail) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.details = append(c.details, detail)
}

func (c *BillingDifferenceChecker) recordError(errMsg string) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.errors = append(c.errors, errMsg)
	c.logger.Error("记录错误", zap.String("error", errMsg))
}

// 辅助函数

// initDatabaseConnection 初始化数据库连接
func initDatabaseConnection(configManager ConfigManager) (*sql.DB, error) {
	connectionString := configManager.GetString("database.connection_string")
	if connectionString == "" {
		return nil, fmt.Errorf("数据库连接字符串未配置")
	}

	db, err := sql.Open("postgres", connectionString)
	if err != nil {
		return nil, fmt.Errorf("打开数据库连接失败: %w", err)
	}

	// 设置连接池参数
	maxOpenConns := configManager.GetInt("database.max_open_conns")
	if maxOpenConns > 0 {
		db.SetMaxOpenConns(maxOpenConns)
	}

	maxIdleConns := configManager.GetInt("database.max_idle_conns")
	if maxIdleConns > 0 {
		db.SetMaxIdleConns(maxIdleConns)
	}

	connMaxLifetime := configManager.GetDuration("database.conn_max_lifetime")
	if connMaxLifetime > 0 {
		db.SetConnMaxLifetime(connMaxLifetime * time.Second)
	}

	// 测试连接
	if err := db.Ping(); err != nil {
		db.Close()
		return nil, fmt.Errorf("数据库连接测试失败: %w", err)
	}

	return db, nil
}

// initGormConnection 初始化GORM连接
func initGormConnection(configManager ConfigManager) (*gorm.DB, error) {
	connectionString := configManager.GetString("database.connection_string")
	if connectionString == "" {
		return nil, fmt.Errorf("数据库连接字符串未配置")
	}

	gormDB, err := gorm.Open(postgres.Open(connectionString), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	if err != nil {
		return nil, fmt.Errorf("初始化GORM连接失败: %w", err)
	}

	return gormDB, nil
}
