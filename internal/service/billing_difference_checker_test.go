package service

import (
	"context"
	"testing"
	"time"

	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/your-org/go-kuaidi/internal/model"
	"go.uber.org/zap"
)

// MockOrderRepository 模拟订单仓储
type MockOrderRepository struct {
	mock.Mock
}

func (m *MockOrderRepository) Save(ctx context.Context, order *model.OrderRecord) error {
	args := m.Called(ctx, order)
	return args.Error(0)
}

func (m *MockOrderRepository) Update(ctx context.Context, order *model.OrderRecord) error {
	args := m.Called(ctx, order)
	return args.Error(0)
}

func (m *MockOrderRepository) FindByID(ctx context.Context, id int64) (*model.OrderRecord, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*model.OrderRecord), args.Error(1)
}

func (m *MockOrderRepository) FindByOrderNoOrTrackingNo(ctx context.Context, orderNo, trackingNo string) (*model.OrderRecord, error) {
	args := m.Called(ctx, orderNo, trackingNo)
	return args.Get(0).(*model.OrderRecord), args.Error(1)
}

func (m *MockOrderRepository) FindByCustomerOrderNo(ctx context.Context, customerOrderNo string) (*model.OrderRecord, error) {
	args := m.Called(ctx, customerOrderNo)
	return args.Get(0).(*model.OrderRecord), args.Error(1)
}

func (m *MockOrderRepository) List(ctx context.Context, offset, limit int) ([]*model.OrderRecord, error) {
	args := m.Called(ctx, offset, limit)
	return args.Get(0).([]*model.OrderRecord), args.Error(1)
}

// MockBalanceService 模拟余额服务
type MockBalanceService struct {
	mock.Mock
}

func (m *MockBalanceService) GetOrderNetPayment(ctx context.Context, userID, orderNo, customerOrderNo string) (decimal.Decimal, error) {
	args := m.Called(ctx, userID, orderNo, customerOrderNo)
	return args.Get(0).(decimal.Decimal), args.Error(1)
}

// MockBillingService 模拟计费服务
type MockBillingService struct {
	mock.Mock
}

func (m *MockBillingService) ProcessBillingDifference(ctx context.Context, orderNo string, actualFee float64, reason string) error {
	args := m.Called(ctx, orderNo, actualFee, reason)
	return args.Error(0)
}

// TestBillingDifferenceChecker_NewBillingDifferenceChecker 测试创建费用差异检查器
func TestBillingDifferenceChecker_NewBillingDifferenceChecker(t *testing.T) {
	logger := zap.NewNop()
	configManager := NewMockConfigManager()
	configManager.Load()

	config := &BillingCheckerConfig{
		DryRun:       false,
		Days:         7,
		BatchSize:    100,
		MaxWorkers:   5,
		ReportFormat: "text",
		OutputFile:   "",
	}

	checker, err := NewBillingDifferenceChecker(config, logger, configManager)

	assert.NoError(t, err)
	assert.NotNil(t, checker)
	assert.Equal(t, config, checker.config)
	assert.Equal(t, logger, checker.logger)
}

// TestBillingDifferenceChecker_getDifferenceType 测试差异类型判断
func TestBillingDifferenceChecker_getDifferenceType(t *testing.T) {
	logger := zap.NewNop()
	configManager := NewMockConfigManager()
	configManager.Load()

	config := &BillingCheckerConfig{
		DryRun:       false,
		Days:         7,
		BatchSize:    100,
		MaxWorkers:   5,
		ReportFormat: "text",
		OutputFile:   "",
	}

	checker, err := NewBillingDifferenceChecker(config, logger, configManager)
	assert.NoError(t, err)

	tests := []struct {
		name       string
		difference float64
		expected   string
	}{
		{
			name:       "需要补收",
			difference: 1.50,
			expected:   "需要补收",
		},
		{
			name:       "需要退款",
			difference: -1.50,
			expected:   "需要退款",
		},
		{
			name:       "无需调整",
			difference: 0.005,
			expected:   "无需调整",
		},
		{
			name:       "无需调整_负值",
			difference: -0.005,
			expected:   "无需调整",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := checker.getDifferenceType(tt.difference)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestBillingDifferenceChecker_needsFixing 测试是否需要修复判断
func TestBillingDifferenceChecker_needsFixing(t *testing.T) {
	logger := zap.NewNop()
	configManager := NewMockConfigManager()
	configManager.Load()

	config := &BillingCheckerConfig{
		DryRun:       false,
		Days:         7,
		BatchSize:    100,
		MaxWorkers:   5,
		ReportFormat: "text",
		OutputFile:   "",
	}

	checker, err := NewBillingDifferenceChecker(config, logger, configManager)
	assert.NoError(t, err)

	tests := []struct {
		name       string
		difference float64
		expected   bool
	}{
		{
			name:       "需要修复_正值",
			difference: 1.50,
			expected:   true,
		},
		{
			name:       "需要修复_负值",
			difference: -1.50,
			expected:   true,
		},
		{
			name:       "不需要修复_小正值",
			difference: 0.005,
			expected:   false,
		},
		{
			name:       "不需要修复_小负值",
			difference: -0.005,
			expected:   false,
		},
		{
			name:       "不需要修复_零值",
			difference: 0.0,
			expected:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := checker.needsFixing(tt.difference)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestMockConfigManager 测试模拟配置管理器
func TestMockConfigManager(t *testing.T) {
	configManager := NewMockConfigManager()

	// 测试设置和获取字符串配置
	configManager.Set("test.string", "test_value")
	assert.Equal(t, "test_value", configManager.GetString("test.string"))
	assert.Equal(t, "", configManager.GetString("nonexistent.key"))

	// 测试设置和获取整数配置
	configManager.Set("test.int", 42)
	assert.Equal(t, 42, configManager.GetInt("test.int"))
	assert.Equal(t, 0, configManager.GetInt("nonexistent.key"))

	// 测试设置和获取时间间隔配置
	duration := 5 * time.Minute
	configManager.Set("test.duration", duration)
	assert.Equal(t, duration, configManager.GetDuration("test.duration"))
	assert.Equal(t, time.Duration(0), configManager.GetDuration("nonexistent.key"))

	// 测试加载配置
	err := configManager.Load()
	assert.NoError(t, err)
	assert.Equal(t, "*************************************************/go_kuaidi", configManager.GetString("database.connection_string"))
}

// TestBillingCheckReport 测试费用检查报告结构
func TestBillingCheckReport(t *testing.T) {
	now := time.Now()

	config := &BillingCheckerConfig{
		DryRun:       true,
		Days:         7,
		BatchSize:    100,
		MaxWorkers:   5,
		ReportFormat: "text",
		OutputFile:   "",
	}

	statistics := &BillingCheckStatistics{
		TotalOrders:           100,
		ProblemsFound:         5,
		FixedOrders:           3,
		FailedFixes:           2,
		SkippedOrders:         10,
		TotalDifferenceAmount: 150.50,
		FixedAmount:           100.30,
		PendingAmount:         50.20,
	}

	details := []*OrderDifferenceDetail{
		{
			PlatformOrderNo:  "PO123456",
			CustomerOrderNo:  "CO123456",
			UserID:           "user123",
			Provider:         "cainiao",
			Status:           "completed",
			CreatedAt:        now,
			UserPaidAmount:   10.50,
			CallbackFee:      12.00,
			DifferenceAmount: 1.50,
			DifferenceType:   "需要补收",
			FixAttempted:     true,
			FixSuccessful:    true,
			ProcessedAt:      now,
		},
	}

	report := &BillingCheckReport{
		CheckTime:  now,
		StartTime:  now,
		EndTime:    now.Add(5 * time.Minute),
		Config:     config,
		Statistics: statistics,
		Details:    details,
		Errors:     []string{},
	}

	assert.Equal(t, now, report.CheckTime)
	assert.Equal(t, config, report.Config)
	assert.Equal(t, statistics, report.Statistics)
	assert.Equal(t, 1, len(report.Details))
	assert.Equal(t, "PO123456", report.Details[0].PlatformOrderNo)
	assert.Equal(t, 0, len(report.Errors))
}
