package service

import (
	"context"
	"fmt"
	"time"

	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/repository"
	"go.uber.org/zap"
)

// CancellationTimeoutService 取消超时保护服务
// 🔥 解决快递鸟等供应商订单长期卡在cancelling状态的问题
type CancellationTimeoutService struct {
	orderRepository repository.OrderRepository
	statusUpdater   *OrderStatusUpdater
	billingService  BalanceServiceInterface
	logger          *zap.Logger
}

// NewCancellationTimeoutService 创建取消超时保护服务
func NewCancellationTimeoutService(
	orderRepo repository.OrderRepository,
	statusUpdater *OrderStatusUpdater,
	billingService BalanceServiceInterface,
	logger *zap.Logger,
) *CancellationTimeoutService {
	return &CancellationTimeoutService{
		orderRepository: orderRepo,
		statusUpdater:   statusUpdater,
		billingService:  billingService,
		logger:          logger,
	}
}

// ProcessTimeoutCancellations 处理超时的取消订单
// 🔥 核心方法：自动处理长期卡在cancelling状态的订单
func (s *CancellationTimeoutService) ProcessTimeoutCancellations(ctx context.Context) error {
	s.logger.Info("🔄 [超时保护] 开始处理超时取消订单")

	// 1. 查找超时的取消订单
	timeoutOrders, err := s.findTimeoutCancellingOrders(ctx)
	if err != nil {
		s.logger.Error("❌ [超时保护] 查找超时订单失败", zap.Error(err))
		return fmt.Errorf("查找超时订单失败: %w", err)
	}

	if len(timeoutOrders) == 0 {
		s.logger.Info("✅ [超时保护] 没有发现超时的取消订单")
		return nil
	}

	s.logger.Info("📋 [超时保护] 发现超时取消订单",
		zap.Int("count", len(timeoutOrders)))

	// 2. 批量处理超时订单
	successCount := 0
	failedCount := 0

	for _, order := range timeoutOrders {
		s.logger.Info("🔧 [超时保护] 处理超时订单",
			zap.String("order_no", order.OrderNo),
			zap.String("customer_order_no", order.CustomerOrderNo),
			zap.String("provider", order.Provider),
			zap.Time("updated_at", order.UpdatedAt))

		err := s.processTimeoutOrder(ctx, order)
		if err != nil {
			s.logger.Error("❌ [超时保护] 处理超时订单失败",
				zap.String("order_no", order.OrderNo),
				zap.Error(err))
			failedCount++
		} else {
			s.logger.Info("✅ [超时保护] 处理超时订单成功",
				zap.String("order_no", order.OrderNo))
			successCount++
		}

		// 避免处理过快
		time.Sleep(100 * time.Millisecond)
	}

	s.logger.Info("📊 [超时保护] 处理完成",
		zap.Int("total", len(timeoutOrders)),
		zap.Int("success", successCount),
		zap.Int("failed", failedCount))

	return nil
}

// findTimeoutCancellingOrders 查找超时的取消订单
func (s *CancellationTimeoutService) findTimeoutCancellingOrders(ctx context.Context) ([]*model.OrderRecord, error) {
	// 🔥 策略：不同供应商使用不同的超时时间
	// - 快递鸟：30分钟（因为不推送取消状态）
	// - 其他供应商：2小时（给予更多时间处理）

	timeoutThresholds := map[string]time.Duration{
		"kuaidiniao": 30 * time.Minute, // 快递鸟30分钟超时
		"kuaidi100":  2 * time.Hour,    // 快递100 2小时超时
		"yida":       2 * time.Hour,    // 易达 2小时超时
		"yuntong":    2 * time.Hour,    // 云通 2小时超时
		"cainiao":    1 * time.Hour,    // 菜鸟 1小时超时
	}

	var allTimeoutOrders []*model.OrderRecord

	for provider, timeout := range timeoutThresholds {
		orders, err := s.orderRepository.FindTimeoutCancellingOrders(ctx, provider, timeout)
		if err != nil {
			s.logger.Error("查找供应商超时订单失败",
				zap.String("provider", provider),
				zap.Duration("timeout", timeout),
				zap.Error(err))
			continue
		}

		s.logger.Info("发现供应商超时订单",
			zap.String("provider", provider),
			zap.Duration("timeout", timeout),
			zap.Int("count", len(orders)))

		allTimeoutOrders = append(allTimeoutOrders, orders...)
	}

	return allTimeoutOrders, nil
}

// processTimeoutOrder 处理单个超时订单
func (s *CancellationTimeoutService) processTimeoutOrder(ctx context.Context, order *model.OrderRecord) error {
	s.logger.Info("🔧 [超时处理] 开始处理超时订单",
		zap.String("order_no", order.OrderNo),
		zap.String("provider", order.Provider))

	// 1. 更新订单状态为已取消
	err := s.statusUpdater.UpdateOrderStatus(ctx, &UpdateOrderStatusRequest{
		OrderNo:      order.OrderNo,
		NewStatus:    model.OrderStatusCancelled,
		ChangeSource: model.ChangeSourceSystem,
		ChangeReason: fmt.Sprintf("超时保护：%s供应商取消超时，自动确认已取消", order.Provider),
		Extra: map[string]interface{}{
			"timeout_protection": true,
			"original_status":    model.OrderStatusCancelling,
			"timeout_duration":   time.Since(order.UpdatedAt).String(),
		},
	})
	if err != nil {
		return fmt.Errorf("更新订单状态失败: %w", err)
	}

	// 2. 执行退款 - 获取订单的实际支付金额并退款
	netPayment, err := s.billingService.GetOrderNetPayment(ctx, order.UserID, order.OrderNo, order.CustomerOrderNo)
	if err != nil {
		s.logger.Error("❌ [超时处理] 获取订单支付金额失败",
			zap.String("order_no", order.OrderNo),
			zap.Error(err))
	} else if netPayment.IsPositive() {
		// 只有当实际支付金额大于0时才执行退款
		err = s.billingService.RefundForOrder(ctx, order.UserID, order.OrderNo, netPayment)
		if err != nil {
			s.logger.Error("❌ [超时处理] 退款失败",
				zap.String("order_no", order.OrderNo),
				zap.String("amount", netPayment.String()),
				zap.Error(err))
			// 退款失败不阻断状态更新，记录错误继续处理
		} else {
			s.logger.Info("💰 [超时处理] 退款成功",
				zap.String("order_no", order.OrderNo),
				zap.String("amount", netPayment.String()))
		}
	} else {
		s.logger.Info("💰 [超时处理] 无需退款，实际支付金额为0",
			zap.String("order_no", order.OrderNo))
	}

	// 3. 记录超时处理日志
	s.recordTimeoutProcessing(ctx, order)

	return nil
}

// recordTimeoutProcessing 记录超时处理日志
func (s *CancellationTimeoutService) recordTimeoutProcessing(ctx context.Context, order *model.OrderRecord) {
	s.logger.Info("📝 [超时处理] 记录处理日志",
		zap.String("order_no", order.OrderNo),
		zap.String("customer_order_no", order.CustomerOrderNo),
		zap.String("provider", order.Provider),
		zap.String("user_id", order.UserID),
		zap.Time("order_created_at", order.CreatedAt),
		zap.Time("last_updated_at", order.UpdatedAt),
		zap.Duration("stuck_duration", time.Since(order.UpdatedAt)),
		zap.String("action", "timeout_protection_processed"))
}

// GetTimeoutStatistics 获取超时统计信息
func (s *CancellationTimeoutService) GetTimeoutStatistics(ctx context.Context) (*TimeoutStatistics, error) {
	stats := &TimeoutStatistics{
		ProviderStats: make(map[string]*ProviderTimeoutStats),
	}

	// 统计各供应商的超时情况
	providers := []string{"kuaidiniao", "kuaidi100", "yida", "yuntong", "cainiao"}

	for _, provider := range providers {
		providerStats, err := s.getProviderTimeoutStats(ctx, provider)
		if err != nil {
			s.logger.Error("获取供应商超时统计失败",
				zap.String("provider", provider),
				zap.Error(err))
			continue
		}
		stats.ProviderStats[provider] = providerStats
		stats.TotalCancellingOrders += providerStats.CancellingCount
		stats.TotalTimeoutOrders += providerStats.TimeoutCount
	}

	return stats, nil
}

// getProviderTimeoutStats 获取供应商超时统计
func (s *CancellationTimeoutService) getProviderTimeoutStats(ctx context.Context, provider string) (*ProviderTimeoutStats, error) {
	// 获取该供应商所有取消中的订单数量
	cancellingCount, err := s.orderRepository.CountCancellingOrdersByProvider(ctx, provider)
	if err != nil {
		return nil, err
	}

	// 获取超时的订单数量
	timeoutThreshold := 30 * time.Minute
	if provider != "kuaidiniao" {
		timeoutThreshold = 2 * time.Hour
	}

	timeoutCount, err := s.orderRepository.CountTimeoutCancellingOrdersByProvider(ctx, provider, timeoutThreshold)
	if err != nil {
		return nil, err
	}

	return &ProviderTimeoutStats{
		Provider:         provider,
		CancellingCount:  cancellingCount,
		TimeoutCount:     timeoutCount,
		TimeoutThreshold: timeoutThreshold,
	}, nil
}

// TimeoutStatistics 超时统计信息
type TimeoutStatistics struct {
	TotalCancellingOrders int                              `json:"total_cancelling_orders"`
	TotalTimeoutOrders    int                              `json:"total_timeout_orders"`
	ProviderStats         map[string]*ProviderTimeoutStats `json:"provider_stats"`
	GeneratedAt           time.Time                        `json:"generated_at"`
}

// ProviderTimeoutStats 供应商超时统计
type ProviderTimeoutStats struct {
	Provider         string        `json:"provider"`
	CancellingCount  int           `json:"cancelling_count"`
	TimeoutCount     int           `json:"timeout_count"`
	TimeoutThreshold time.Duration `json:"timeout_threshold"`
}
