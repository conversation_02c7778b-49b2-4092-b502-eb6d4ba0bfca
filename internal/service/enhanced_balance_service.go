package service

import (
	"context"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"

	"github.com/your-org/go-kuaidi/internal/infrastructure/database"
	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/repository"
)

// EnhancedBalanceService 增强版余额服务 - 集成所有高并发优化方案
type EnhancedBalanceService struct {
	// 基础服务
	baseService *DefaultBalanceService

	// 异步处理
	asyncService *AsyncBalanceService

	// 分片处理
	shardedService *ShardedBalanceService

	// 分布式锁
	lockService *BalanceLockService

	// 监控服务
	monitoringService *BalanceMonitoringService

	// 配置
	config *EnhancedBalanceConfig

	// 日志
	logger *zap.Logger
}

// EnhancedBalanceConfig 增强版余额服务配置
type EnhancedBalanceConfig struct {
	// 服务模式
	Mode                  ServiceMode `json:"mode"`                    // 服务模式
	EnableAsyncProcessing bool        `json:"enable_async_processing"` // 启用异步处理
	EnableSharding        bool        `json:"enable_sharding"`         // 启用分片
	EnableDistributedLock bool        `json:"enable_distributed_lock"` // 启用分布式锁
	EnableMonitoring      bool        `json:"enable_monitoring"`       // 启用监控

	// 性能参数
	MaxConcurrency int           `json:"max_concurrency"` // 最大并发数
	QueueSize      int           `json:"queue_size"`      // 队列大小
	WorkerCount    int           `json:"worker_count"`    // 工作协程数
	BatchSize      int           `json:"batch_size"`      // 批处理大小
	FlushInterval  time.Duration `json:"flush_interval"`  // 刷新间隔

	// 重试参数
	MaxRetries               int           `json:"max_retries"`                // 最大重试次数
	BaseRetryDelay           time.Duration `json:"base_retry_delay"`           // 基础重试延迟
	MaxRetryDelay            time.Duration `json:"max_retry_delay"`            // 最大重试延迟
	EnableExponentialBackoff bool          `json:"enable_exponential_backoff"` // 启用指数退避

	// 分片参数
	ShardCount      int             `json:"shard_count"`      // 分片数量
	ConsistencyMode ConsistencyMode `json:"consistency_mode"` // 一致性模式

	// 降级参数
	EnableGracefulDegradation bool    `json:"enable_graceful_degradation"` // 启用优雅降级
	FallbackToSync            bool    `json:"fallback_to_sync"`            // 降级到同步模式
	EmergencyModeThreshold    float64 `json:"emergency_mode_threshold"`    // 紧急模式阈值
}

// ServiceMode 服务模式
type ServiceMode string

const (
	// ModeSync 同步模式 - 最高一致性，最低并发
	ModeSync ServiceMode = "sync"

	// ModeAsyncEventual 异步最终一致性 - 最高性能
	ModeAsyncEventual ServiceMode = "async_eventual"

	// ModeAsyncStrong 异步强一致性 - 平衡性能和一致性
	ModeAsyncStrong ServiceMode = "async_strong"

	// ModeShardedEventual 分片最终一致性 - 超高并发
	ModeShardedEventual ServiceMode = "sharded_eventual"

	// ModeShardedStrong 分片强一致性 - 高并发+强一致性
	ModeShardedStrong ServiceMode = "sharded_strong"

	// ModeHybrid 混合模式 - 自适应选择最优策略
	ModeHybrid ServiceMode = "hybrid"
)

// NewEnhancedBalanceService 创建增强版余额服务
func NewEnhancedBalanceService(
	baseService *DefaultBalanceService,
	redisClient *redis.Client,
	repositories map[int]*repository.BalanceRepository,
	config *EnhancedBalanceConfig,
	logger *zap.Logger,
) *EnhancedBalanceService {
	service := &EnhancedBalanceService{
		baseService: baseService,
		config:      config,
		logger:      logger,
	}

	// 初始化子服务
	if config.EnableAsyncProcessing {
		service.asyncService = NewAsyncBalanceService(baseService, redisClient, logger)
	}

	if config.EnableDistributedLock {
		service.lockService = NewBalanceLockService(redisClient, logger)
	}

	if config.EnableSharding {
		// 使用传入的分片存储库映射
		service.shardedService = NewShardedBalanceService(
			repositories,
			config.ShardCount,
			StrongConsistency,
			logger)
	}

	if config.EnableMonitoring {
		service.monitoringService = NewBalanceMonitoringService(logger)
	}

	logger.Info("✅ 增强版余额服务初始化完成",
		zap.String("mode", string(config.Mode)),
		zap.Bool("async_enabled", config.EnableAsyncProcessing),
		zap.Bool("sharding_enabled", config.EnableSharding),
		zap.Bool("lock_enabled", config.EnableDistributedLock),
		zap.Bool("monitoring_enabled", config.EnableMonitoring))

	return service
}

// PreChargeForOrder 订单预扣费 - 统一入口
func (s *EnhancedBalanceService) PreChargeForOrder(ctx context.Context, userID, orderNo string, amount decimal.Decimal) error {
	s.logger.Info("🔄 订单预扣费请求",
		zap.String("user_id", userID),
		zap.String("order_no", orderNo),
		zap.String("amount", amount.String()),
		zap.String("mode", string(s.config.Mode)))

	// 包装监控
	if s.monitoringService != nil {
		return s.monitoringService.WithMonitoring(func() error {
			return s.executePreCharge(ctx, userID, orderNo, amount)
		})
	}

	return s.executePreCharge(ctx, userID, orderNo, amount)
}

// PreChargeForOrderWithDetails 订单预扣费（优化版）- 统一入口
func (s *EnhancedBalanceService) PreChargeForOrderWithDetails(ctx context.Context, userID, customerOrderNo, platformOrderNo string, amount decimal.Decimal) error {
	s.logger.Info("🔄 订单预扣费请求（优化版）",
		zap.String("user_id", userID),
		zap.String("customer_order_no", customerOrderNo),
		zap.String("platform_order_no", platformOrderNo),
		zap.String("amount", amount.String()),
		zap.String("mode", string(s.config.Mode)))

	// 包装监控
	if s.monitoringService != nil {
		return s.monitoringService.WithMonitoring(func() error {
			return s.executePreChargeWithDetails(ctx, userID, customerOrderNo, platformOrderNo, amount)
		})
	}

	return s.executePreChargeWithDetails(ctx, userID, customerOrderNo, platformOrderNo, amount)
}

// executePreCharge 执行预扣费逻辑
func (s *EnhancedBalanceService) executePreCharge(ctx context.Context, userID, orderNo string, amount decimal.Decimal) error {
	switch s.config.Mode {
	case ModeSync:
		return s.preChargeSyncMode(ctx, userID, orderNo, amount)

	case ModeAsyncEventual:
		return s.preChargeAsyncEventualMode(ctx, userID, orderNo, amount)

	case ModeAsyncStrong:
		return s.preChargeAsyncStrongMode(ctx, userID, orderNo, amount)

	case ModeShardedEventual:
		return s.preChargeShardedEventualMode(ctx, userID, orderNo, amount)

	case ModeShardedStrong:
		return s.preChargeShardedStrongMode(ctx, userID, orderNo, amount)

	case ModeHybrid:
		return s.preChargeHybridMode(ctx, userID, orderNo, amount)

	default:
		return s.preChargeSyncMode(ctx, userID, orderNo, amount)
	}
}

// executePreChargeWithDetails 执行预扣费逻辑（优化版）
func (s *EnhancedBalanceService) executePreChargeWithDetails(ctx context.Context, userID, customerOrderNo, platformOrderNo string, amount decimal.Decimal) error {
	// 优化版直接使用基础服务的优化方法
	if s.config.EnableDistributedLock {
		return s.lockService.WithUserLock(ctx, userID, func() error {
			return s.baseService.PreChargeForOrderWithDetails(ctx, userID, customerOrderNo, platformOrderNo, amount)
		})
	}

	return s.baseService.PreChargeForOrderWithDetails(ctx, userID, customerOrderNo, platformOrderNo, amount)
}

// preChargeSyncMode 同步模式预扣费
func (s *EnhancedBalanceService) preChargeSyncMode(ctx context.Context, userID, orderNo string, amount decimal.Decimal) error {
	s.logger.Debug("🔄 使用同步模式处理预扣费",
		zap.String("user_id", userID),
		zap.String("order_no", orderNo))

	if s.config.EnableDistributedLock {
		return s.lockService.WithUserLock(ctx, userID, func() error {
			return s.baseService.PreChargeForOrder(ctx, userID, orderNo, amount)
		})
	}

	return s.baseService.PreChargeForOrder(ctx, userID, orderNo, amount)
}

// preChargeAsyncEventualMode 异步最终一致性模式
func (s *EnhancedBalanceService) preChargeAsyncEventualMode(ctx context.Context, userID, orderNo string, amount decimal.Decimal) error {
	if s.asyncService == nil {
		return fmt.Errorf("异步服务未启用")
	}

	s.logger.Debug("🔄 使用异步最终一致性模式处理预扣费",
		zap.String("user_id", userID),
		zap.String("order_no", orderNo))

	operation := &BalanceOperation{
		UserID:          userID,
		OrderNo:         orderNo,
		CustomerOrderNo: orderNo,
		Amount:          amount,
		Type:            model.TransactionTypeOrderPreCharge,
		Priority:        0, // 正常优先级
		Context:         ctx,
	}

	return s.asyncService.SubmitBalanceOperation(ctx, operation)
}

// preChargeAsyncStrongMode 异步强一致性模式
func (s *EnhancedBalanceService) preChargeAsyncStrongMode(ctx context.Context, userID, orderNo string, amount decimal.Decimal) error {
	if s.asyncService == nil {
		return fmt.Errorf("异步服务未启用")
	}

	s.logger.Debug("🔄 使用异步强一致性模式处理预扣费",
		zap.String("user_id", userID),
		zap.String("order_no", orderNo))

	// 强一致性模式下，需要同步等待结果
	resultChan := make(chan error, 1)

	operation := &BalanceOperation{
		UserID:          userID,
		OrderNo:         orderNo,
		CustomerOrderNo: orderNo,
		Amount:          amount,
		Type:            model.TransactionTypeOrderPreCharge,
		Priority:        1, // 高优先级
		Context:         ctx,
		Callback: func(err error) {
			resultChan <- err
		},
	}

	if err := s.asyncService.SubmitBalanceOperation(ctx, operation); err != nil {
		return err
	}

	// 等待结果
	select {
	case err := <-resultChan:
		return err
	case <-ctx.Done():
		return ctx.Err()
	case <-time.After(30 * time.Second):
		return fmt.Errorf("操作超时")
	}
}

// preChargeShardedEventualMode 分片最终一致性模式
func (s *EnhancedBalanceService) preChargeShardedEventualMode(ctx context.Context, userID, orderNo string, amount decimal.Decimal) error {
	if s.shardedService == nil {
		return fmt.Errorf("分片服务未启用")
	}

	s.logger.Debug("🔄 使用分片最终一致性模式处理预扣费",
		zap.String("user_id", userID),
		zap.String("order_no", orderNo))

	// 设置为最终一致性模式
	// 注意：分片服务的一致性模式在创建时已设定
	return s.shardedService.PreChargeForOrder(ctx, userID, orderNo, amount)
}

// preChargeShardedStrongMode 分片强一致性模式
func (s *EnhancedBalanceService) preChargeShardedStrongMode(ctx context.Context, userID, orderNo string, amount decimal.Decimal) error {
	if s.shardedService == nil {
		return fmt.Errorf("分片服务未启用")
	}

	s.logger.Debug("🔄 使用分片强一致性模式处理预扣费",
		zap.String("user_id", userID),
		zap.String("order_no", orderNo))

	// 设置为强一致性模式
	// 注意：分片服务的一致性模式在创建时已设定
	return s.shardedService.PreChargeForOrder(ctx, userID, orderNo, amount)
}

// preChargeHybridMode 混合模式 - 自适应选择最优策略
func (s *EnhancedBalanceService) preChargeHybridMode(ctx context.Context, userID, orderNo string, amount decimal.Decimal) error {
	s.logger.Debug("🔄 使用混合模式处理预扣费",
		zap.String("user_id", userID),
		zap.String("order_no", orderNo))

	// 🔥 关键修复：检查是否在事务上下文中
	// 如果在事务中，强制使用同步模式确保事务一致性
	if s.isInTransactionContext(ctx) {
		s.logger.Info("🔒 检测到事务上下文，强制使用同步模式确保事务一致性",
			zap.String("user_id", userID),
			zap.String("order_no", orderNo))
		return s.preChargeSyncMode(ctx, userID, orderNo, amount)
	}

	// 获取当前系统状态
	strategy := s.selectOptimalStrategy(ctx, userID)

	s.logger.Debug("🎯 混合模式选择策略",
		zap.String("user_id", userID),
		zap.String("selected_strategy", strategy))

	switch strategy {
	case "sync":
		return s.preChargeSyncMode(ctx, userID, orderNo, amount)
	case "async_eventual":
		return s.preChargeAsyncEventualMode(ctx, userID, orderNo, amount)
	case "async_strong":
		return s.preChargeAsyncStrongMode(ctx, userID, orderNo, amount)
	case "sharded_eventual":
		return s.preChargeShardedEventualMode(ctx, userID, orderNo, amount)
	case "sharded_strong":
		return s.preChargeShardedStrongMode(ctx, userID, orderNo, amount)
	default:
		return s.preChargeSyncMode(ctx, userID, orderNo, amount)
	}
}

// isInTransactionContext 检查是否在事务上下文中
func (s *EnhancedBalanceService) isInTransactionContext(ctx context.Context) bool {
	// 使用database包提供的标准方法检查事务上下文
	// 这与transaction_manager.go中使用的方式一致
	if tx := database.TxFromContext(ctx); tx != nil {
		return true
	}

	return false
}

// selectOptimalStrategy 选择最优策略
func (s *EnhancedBalanceService) selectOptimalStrategy(ctx context.Context, userID string) string {
	// 检查系统负载
	if s.monitoringService != nil {
		metrics := s.monitoringService.GetMetrics()

		// 如果在紧急模式，使用同步模式
		if s.monitoringService.IsEmergencyMode() {
			return "sync"
		}

		// 根据错误率选择策略
		if metrics.ErrorRate > 0.10 { // 错误率超过10%
			return "sync" // 降级到同步模式
		}

		// 根据冲突率选择策略
		if metrics.ConflictRate > 0.20 { // 冲突率超过20%
			if s.shardedService != nil {
				return "sharded_eventual" // 使用分片减少冲突
			}
			return "async_eventual" // 使用异步处理
		}

		// 根据队列大小选择策略
		if metrics.QueueSize > 500 {
			if s.shardedService != nil {
				return "sharded_strong" // 分片+强一致性
			}
			return "async_strong" // 异步+强一致性
		}
	}

	// 检查用户锁状态
	if s.lockService != nil {
		lockInfo, err := s.lockService.GetUserLockStatus(ctx, userID)
		if err == nil && lockInfo.Locked {
			// 用户已被锁定，使用异步处理避免阻塞
			return "async_eventual"
		}
	}

	// 默认策略：根据配置选择
	if s.shardedService != nil {
		return "sharded_eventual" // 优先使用分片
	}

	if s.asyncService != nil {
		return "async_eventual" // 其次使用异步
	}

	return "sync" // 最后使用同步
}

// RefundForOrder 订单退款 - 统一入口
func (s *EnhancedBalanceService) RefundForOrder(ctx context.Context, userID, orderNo string, amount decimal.Decimal) error {
	s.logger.Info("🔄 订单退款请求",
		zap.String("user_id", userID),
		zap.String("order_no", orderNo),
		zap.String("amount", amount.String()),
		zap.String("mode", string(s.config.Mode)))

	// 包装监控
	if s.monitoringService != nil {
		return s.monitoringService.WithMonitoring(func() error {
			return s.executeRefund(ctx, userID, orderNo, amount)
		})
	}

	return s.executeRefund(ctx, userID, orderNo, amount)
}

// executeRefund 执行退款逻辑
func (s *EnhancedBalanceService) executeRefund(ctx context.Context, userID, orderNo string, amount decimal.Decimal) error {
	// 退款操作冲突较少，优先使用高性能模式
	switch s.config.Mode {
	case ModeSync:
		return s.baseService.RefundForOrder(ctx, userID, orderNo, amount)

	case ModeAsyncEventual, ModeAsyncStrong:
		if s.asyncService != nil {
			operation := &BalanceOperation{
				UserID:          userID,
				OrderNo:         orderNo,
				CustomerOrderNo: orderNo,
				Amount:          amount,
				Type:            model.TransactionTypeOrderCancelRefund,
				Priority:        1, // 退款高优先级
				Context:         ctx,
			}
			return s.asyncService.SubmitBalanceOperation(ctx, operation)
		}
		return s.baseService.RefundForOrder(ctx, userID, orderNo, amount)

	case ModeShardedEventual, ModeShardedStrong:
		if s.shardedService != nil {
			return s.shardedService.RefundForOrder(ctx, userID, orderNo, amount)
		}
		return s.baseService.RefundForOrder(ctx, userID, orderNo, amount)

	case ModeHybrid:
		// 混合模式下退款优先使用高性能策略
		if s.shardedService != nil {
			return s.shardedService.RefundForOrder(ctx, userID, orderNo, amount)
		}
		if s.asyncService != nil {
			operation := &BalanceOperation{
				UserID:          userID,
				OrderNo:         orderNo,
				CustomerOrderNo: orderNo,
				Amount:          amount,
				Type:            model.TransactionTypeOrderCancelRefund,
				Priority:        1,
				Context:         ctx,
			}
			return s.asyncService.SubmitBalanceOperation(ctx, operation)
		}
		return s.baseService.RefundForOrder(ctx, userID, orderNo, amount)

	default:
		return s.baseService.RefundForOrder(ctx, userID, orderNo, amount)
	}
}

// GetUserBalance 获取用户余额
func (s *EnhancedBalanceService) GetUserBalance(ctx context.Context, userID string) (*model.UserBalance, error) {
	// 读操作优先使用分片服务
	if s.shardedService != nil {
		return s.shardedService.GetUserBalance(ctx, userID)
	}

	// 从基础服务获取余额并转换类型
	balanceResponse, err := s.baseService.GetBalance(ctx, userID)
	if err != nil {
		return nil, err
	}

	// 转换为UserBalance类型
	userBalance := &model.UserBalance{
		ID:        balanceResponse.UserID,
		UserID:    balanceResponse.UserID,
		Balance:   balanceResponse.Balance,
		Currency:  balanceResponse.Currency,
		Version:   balanceResponse.Version,
		CreatedAt: balanceResponse.CreatedAt,
		UpdatedAt: balanceResponse.UpdatedAt,
	}

	return userBalance, nil
}

// 实现完整的BalanceService接口的其他方法
// Deposit 充值
func (s *EnhancedBalanceService) Deposit(ctx context.Context, req *model.DepositRequest) (*model.TransactionResponse, error) {
	return s.baseService.Deposit(ctx, req)
}

// Payment 支付
func (s *EnhancedBalanceService) Payment(ctx context.Context, req *model.PaymentRequest) (*model.TransactionResponse, error) {
	return s.baseService.Payment(ctx, req)
}

// Refund 退款
func (s *EnhancedBalanceService) Refund(ctx context.Context, req *model.BalanceRequest) (*model.TransactionResponse, error) {
	return s.baseService.Refund(ctx, req)
}

// Transfer 转账
func (s *EnhancedBalanceService) Transfer(ctx context.Context, req *model.TransferRequest) (*model.TransactionResponse, error) {
	return s.baseService.Transfer(ctx, req)
}

// GetTransactionHistory 获取交易历史
func (s *EnhancedBalanceService) GetTransactionHistory(ctx context.Context, userID string, limit, offset int) ([]*model.TransactionResponse, error) {
	return s.baseService.GetTransactionHistory(ctx, userID, limit, offset)
}

// GetTransactionHistoryWithCount 获取交易历史（带总数）
func (s *EnhancedBalanceService) GetTransactionHistoryWithCount(ctx context.Context, userID string, limit, offset int) ([]*model.TransactionResponse, int64, error) {
	return s.baseService.GetTransactionHistoryWithCount(ctx, userID, limit, offset)
}

// GetTransactionHistoryWithFilters 获取交易历史（带过滤器）
func (s *EnhancedBalanceService) GetTransactionHistoryWithFilters(ctx context.Context, userID string, limit, offset int, typeFilter, statusFilter, startTime, endTime, customerOrderNo, orderNo, trackingNo string) ([]*model.TransactionResponse, int64, error) {
	return s.baseService.GetTransactionHistoryWithFilters(ctx, userID, limit, offset, typeFilter, statusFilter, startTime, endTime, customerOrderNo, orderNo, trackingNo)
}

// GetTransactionByID 根据ID获取交易
func (s *EnhancedBalanceService) GetTransactionByID(ctx context.Context, id string) (*model.TransactionResponse, error) {
	return s.baseService.GetTransactionByID(ctx, id)
}

// GetTransactionByOrderNo 根据订单号获取交易
func (s *EnhancedBalanceService) GetTransactionByOrderNo(ctx context.Context, orderNo string) (*model.TransactionResponse, error) {
	return s.baseService.GetTransactionByOrderNo(ctx, orderNo)
}

// GetDepositHistory 获取充值历史
func (s *EnhancedBalanceService) GetDepositHistory(ctx context.Context, userID string, limit, offset int) ([]*model.Deposit, error) {
	return s.baseService.GetDepositHistory(ctx, userID, limit, offset)
}

// GetPaymentHistory 获取支付历史
func (s *EnhancedBalanceService) GetPaymentHistory(ctx context.Context, userID string, limit, offset int) ([]*model.OrderPayment, error) {
	return s.baseService.GetPaymentHistory(ctx, userID, limit, offset)
}

// ChargeForBillingDifference 计费差额扣费
func (s *EnhancedBalanceService) ChargeForBillingDifference(ctx context.Context, userID, orderNo string, amount decimal.Decimal) error {
	return s.baseService.ChargeForBillingDifference(ctx, userID, orderNo, amount)
}

// GetBalance 获取余额（委托给基础服务）
func (s *EnhancedBalanceService) GetBalance(ctx context.Context, userID string) (*model.BalanceResponse, error) {
	// 优先使用分片服务
	if s.shardedService != nil {
		userBalance, err := s.shardedService.GetUserBalance(ctx, userID)
		if err == nil {
			// 转换为BalanceResponse
			return &model.BalanceResponse{
				UserID:    userBalance.UserID,
				Balance:   userBalance.Balance,
				Currency:  userBalance.Currency,
				Version:   userBalance.Version,
				CreatedAt: userBalance.CreatedAt,
				UpdatedAt: userBalance.UpdatedAt,
			}, nil
		}
	}

	// 降级到基础服务
	return s.baseService.GetBalance(ctx, userID)
}

// GetOrderNetPayment 获取订单净支付
func (s *EnhancedBalanceService) GetOrderNetPayment(ctx context.Context, userID, orderNo, customerOrderNo string) (decimal.Decimal, error) {
	return s.baseService.GetOrderNetPayment(ctx, userID, orderNo, customerOrderNo)
}

// CreateBalanceIfNotExists 创建余额账户（如果不存在）
func (s *EnhancedBalanceService) CreateBalanceIfNotExists(ctx context.Context, userID string) error {
	return s.baseService.CreateBalanceIfNotExists(ctx, userID)
}

// ProcessPaymentCallback 处理支付回调
func (s *EnhancedBalanceService) ProcessPaymentCallback(ctx context.Context, depositID string, success bool, transactionID string) error {
	return s.baseService.ProcessPaymentCallback(ctx, depositID, success, transactionID)
}

// RefundForBillingDifference 计费差额退款
func (s *EnhancedBalanceService) RefundForBillingDifference(ctx context.Context, userID, orderNo string, amount decimal.Decimal) error {
	return s.baseService.RefundForBillingDifference(ctx, userID, orderNo, amount)
}

// GetBalanceHistory 获取余额历史 - 实现BalanceServiceInterface接口
func (s *EnhancedBalanceService) GetBalanceHistory(ctx context.Context, userID string, limit, offset int) ([]*model.TransactionResponse, error) {
	return s.baseService.GetTransactionHistory(ctx, userID, limit, offset)
}

// BatchProcessOperations 批量处理操作
func (s *EnhancedBalanceService) BatchProcessOperations(ctx context.Context, userID string, operations []*BalanceOperation) error {
	if len(operations) == 0 {
		return nil
	}

	s.logger.Info("🔄 批量处理余额操作",
		zap.String("user_id", userID),
		zap.Int("operation_count", len(operations)))

	// 优先使用分片服务进行批处理
	if s.shardedService != nil {
		// 简化实现：暂时不支持复杂批量操作
		s.logger.Info("分片服务暂不支持批量操作，降级到逐个处理")
	}

	// 降级到逐个处理
	for _, op := range operations {
		var err error
		switch op.Type {
		case model.TransactionTypeOrderPreCharge:
			err = s.PreChargeForOrder(ctx, userID, op.OrderNo, op.Amount)
		case model.TransactionTypeOrderCancelRefund:
			err = s.RefundForOrder(ctx, userID, op.OrderNo, op.Amount)
		default:
			continue
		}

		if err != nil {
			return fmt.Errorf("批量操作失败: %w", err)
		}
	}

	return nil
}

// GetServiceStats 获取服务统计信息
func (s *EnhancedBalanceService) GetServiceStats(ctx context.Context) (map[string]interface{}, error) {
	stats := map[string]interface{}{
		"mode":      s.config.Mode,
		"config":    s.config,
		"timestamp": time.Now(),
	}

	// 监控指标
	if s.monitoringService != nil {
		stats["metrics"] = s.monitoringService.GetMetrics()
		stats["active_alerts"] = s.monitoringService.GetActiveAlerts()
		stats["emergency_mode"] = s.monitoringService.IsEmergencyMode()
	}

	// 异步服务统计
	if s.asyncService != nil {
		stats["async_queue"] = s.asyncService.GetQueueStats()
	}

	// 分片服务统计
	if s.shardedService != nil {
		shardStats, err := s.shardedService.GetShardStats(ctx)
		if err == nil {
			stats["sharding"] = shardStats
		}
	}

	// 分布式锁统计
	if s.lockService != nil {
		lockStats, err := s.lockService.ListAllLocks(ctx)
		if err == nil {
			stats["locks"] = lockStats
		}
	}

	return stats, nil
}

// SwitchMode 切换服务模式
func (s *EnhancedBalanceService) SwitchMode(newMode ServiceMode) error {
	oldMode := s.config.Mode
	s.config.Mode = newMode

	s.logger.Info("🔄 切换服务模式",
		zap.String("from", string(oldMode)),
		zap.String("to", string(newMode)))

	// 可以在这里添加模式切换的特殊逻辑
	// 比如等待当前操作完成、迁移队列中的操作等

	return nil
}

// HealthCheck 健康检查
func (s *EnhancedBalanceService) HealthCheck(ctx context.Context) error {
	// 检查基础服务 - 简单的可用性检查
	if s.baseService == nil {
		return fmt.Errorf("基础服务未初始化")
	}

	// 检查异步服务
	if s.asyncService != nil {
		// 异步服务没有直接的健康检查方法，检查队列状态
		stats := s.asyncService.GetQueueStats()
		if queueSize, ok := stats["processing_queue_size"].(int); ok && queueSize > 10000 {
			return fmt.Errorf("异步队列积压严重: %d", queueSize)
		}
	}

	// 检查分片服务
	if s.shardedService != nil {
		healthMap := s.shardedService.HealthCheck(ctx)
		for shardID, healthy := range healthMap {
			if !healthy {
				return fmt.Errorf("分片服务健康检查失败: 分片 %d 不健康", shardID)
			}
		}
	}

	// 检查分布式锁服务
	if s.lockService != nil {
		if err := s.lockService.HealthCheck(ctx); err != nil {
			return fmt.Errorf("分布式锁服务健康检查失败: %w", err)
		}
	}

	// 检查监控服务
	if s.monitoringService != nil {
		if err := s.monitoringService.HealthCheck(); err != nil {
			return fmt.Errorf("监控服务健康检查失败: %w", err)
		}
	}

	return nil
}

// Stop 停止服务
func (s *EnhancedBalanceService) Stop() {
	s.logger.Info("⏹️ 停止增强版余额服务")

	if s.asyncService != nil {
		s.asyncService.Stop()
	}

	if s.monitoringService != nil {
		s.monitoringService.Stop()
	}
}

// GetDefaultConfig 获取默认配置
func GetDefaultConfig() *EnhancedBalanceConfig {
	return &EnhancedBalanceConfig{
		Mode:                      ModeHybrid,
		EnableAsyncProcessing:     true,
		EnableSharding:            true,
		EnableDistributedLock:     true,
		EnableMonitoring:          true,
		MaxConcurrency:            1000,
		QueueSize:                 10000,
		WorkerCount:               20,
		BatchSize:                 100,
		FlushInterval:             50 * time.Millisecond,
		MaxRetries:                5,
		BaseRetryDelay:            50 * time.Millisecond,
		MaxRetryDelay:             1 * time.Second,
		EnableExponentialBackoff:  true,
		ShardCount:                8,
		ConsistencyMode:           EventualConsistency,
		EnableGracefulDegradation: true,
		FallbackToSync:            true,
		EmergencyModeThreshold:    0.20, // 20%错误率触发紧急模式
	}
}

// GetHighPerformanceConfig 获取高性能配置
func GetHighPerformanceConfig() *EnhancedBalanceConfig {
	config := GetDefaultConfig()
	config.Mode = ModeShardedEventual
	config.ConsistencyMode = EventualConsistency
	config.WorkerCount = 50
	config.QueueSize = 20000
	config.BatchSize = 200
	config.FlushInterval = 20 * time.Millisecond
	config.ShardCount = 16
	return config
}

// GetHighConsistencyConfig 获取高一致性配置
func GetHighConsistencyConfig() *EnhancedBalanceConfig {
	config := GetDefaultConfig()
	config.Mode = ModeAsyncStrong
	config.ConsistencyMode = StrongConsistency
	config.EnableDistributedLock = true
	config.WorkerCount = 10
	config.QueueSize = 5000
	config.BatchSize = 50
	config.FlushInterval = 100 * time.Millisecond
	return config
}

// CheckBalanceForOrder 检查用户余额是否足够支付订单 - 🔥 新增：修复余额不足却创建订单成功的BUG
func (s *EnhancedBalanceService) CheckBalanceForOrder(ctx context.Context, userID string, amount decimal.Decimal) (*model.BalanceCheckResult, error) {
	s.logger.Info("增强版余额服务：开始余额预检查",
		zap.String("user_id", userID),
		zap.String("amount", amount.String()),
		zap.String("service_mode", string(s.config.Mode)))

	// 委托给基础服务处理
	if s.baseService != nil {
		return s.baseService.CheckBalanceForOrder(ctx, userID, amount)
	}

	// 如果基础服务不可用，返回错误
	s.logger.Error("增强版余额服务：基础服务不可用，无法执行余额检查",
		zap.String("user_id", userID),
		zap.String("amount", amount.String()))

	return nil, fmt.Errorf("增强版余额服务：基础服务不可用")
}

// CheckAndReserveBalance 检查并预留余额 - 🔥 新增：原子化余额检查和预留
func (s *EnhancedBalanceService) CheckAndReserveBalance(ctx context.Context, userID string, amount decimal.Decimal, reservationID string) error {
	s.logger.Info("增强版余额服务：开始余额预留",
		zap.String("user_id", userID),
		zap.String("amount", amount.String()),
		zap.String("reservation_id", reservationID))

	// 委托给基础服务处理
	if s.baseService != nil {
		return s.baseService.CheckAndReserveBalance(ctx, userID, amount, reservationID)
	}

	// 如果基础服务不可用，返回错误
	s.logger.Error("增强版余额服务：基础服务不可用，无法执行余额预留",
		zap.String("user_id", userID),
		zap.String("amount", amount.String()))

	return fmt.Errorf("增强版余额服务：基础服务不可用")
}

// ReleaseReservedBalance 释放预留余额 - 🔥 新增：释放预留的余额
func (s *EnhancedBalanceService) ReleaseReservedBalance(ctx context.Context, userID string, reservationID string) error {
	s.logger.Info("增强版余额服务：释放预留余额",
		zap.String("user_id", userID),
		zap.String("reservation_id", reservationID))

	// 委托给基础服务处理
	if s.baseService != nil {
		return s.baseService.ReleaseReservedBalance(ctx, userID, reservationID)
	}

	// 如果基础服务不可用，返回错误
	s.logger.Error("增强版余额服务：基础服务不可用，无法释放预留余额",
		zap.String("user_id", userID))

	return fmt.Errorf("增强版余额服务：基础服务不可用")
}
