package service

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/your-org/go-kuaidi/internal/model"
	"go.uber.org/zap"
)

// TestCalculateBatchQueryStats 测试批量查询统计计算方法
func TestCalculateBatchQueryStats(t *testing.T) {
	logger, _ := zap.NewDevelopment()
	service := &OrderService{
		logger: logger,
	}

	t.Run("计算批量查询统计信息", func(t *testing.T) {
		trackingNos := []string{"SF1234567890", "YT9876543210", "ZTO5555666677", "STO7777888899"}
		items := []*model.OrderListItem{
			{TrackingNo: "SF1234567890"},
			{TrackingNo: "YT9876543210"},
		}
		queryDuration := 125 * time.Millisecond

		stats := service.calculateBatchQueryStats(trackingNos, items, queryDuration)

		assert.Equal(t, 4, stats.TotalQueried)
		assert.Equal(t, 2, stats.FoundCount)
		assert.Equal(t, 2, len(stats.NotFound))
		assert.Contains(t, stats.NotFound, "ZTO5555666677")
		assert.Contains(t, stats.NotFound, "STO7777888899")
		assert.Equal(t, "125.00ms", stats.QueryTime)
	})

	t.Run("全部找到的情况", func(t *testing.T) {
		trackingNos := []string{"SF1234567890", "YT9876543210"}
		items := []*model.OrderListItem{
			{TrackingNo: "SF1234567890"},
			{TrackingNo: "YT9876543210"},
		}
		queryDuration := 85 * time.Millisecond

		stats := service.calculateBatchQueryStats(trackingNos, items, queryDuration)

		assert.Equal(t, 2, stats.TotalQueried)
		assert.Equal(t, 2, stats.FoundCount)
		assert.Equal(t, 0, len(stats.NotFound))
		assert.Equal(t, "85.00ms", stats.QueryTime)
	})

	t.Run("全部未找到的情况", func(t *testing.T) {
		trackingNos := []string{"SF1111111111", "YT2222222222"}
		items := []*model.OrderListItem{}
		queryDuration := 45 * time.Millisecond

		stats := service.calculateBatchQueryStats(trackingNos, items, queryDuration)

		assert.Equal(t, 2, stats.TotalQueried)
		assert.Equal(t, 0, stats.FoundCount)
		assert.Equal(t, 2, len(stats.NotFound))
		assert.Contains(t, stats.NotFound, "SF1111111111")
		assert.Contains(t, stats.NotFound, "YT2222222222")
		assert.Equal(t, "45.00ms", stats.QueryTime)
	})

	t.Run("处理空运单号的订单项", func(t *testing.T) {
		trackingNos := []string{"SF1234567890", "YT9876543210"}
		items := []*model.OrderListItem{
			{TrackingNo: "SF1234567890"},
			{TrackingNo: ""}, // 空运单号，应该被忽略
		}
		queryDuration := 100 * time.Millisecond

		stats := service.calculateBatchQueryStats(trackingNos, items, queryDuration)

		assert.Equal(t, 2, stats.TotalQueried)
		assert.Equal(t, 2, stats.FoundCount) // 仍然是2个，因为items数量是2
		assert.Equal(t, 1, len(stats.NotFound))
		assert.Contains(t, stats.NotFound, "YT9876543210")
	})

	t.Run("大量数据性能测试", func(t *testing.T) {
		// 创建50个运单号
		trackingNos := make([]string, 50)
		for i := 0; i < 50; i++ {
			trackingNos[i] = "SF12345678" + string(rune('0'+i%10)) + string(rune('0'+(i/10)%10))
		}

		// 创建30个找到的订单
		items := make([]*model.OrderListItem, 30)
		for i := 0; i < 30; i++ {
			items[i] = &model.OrderListItem{
				TrackingNo: trackingNos[i],
			}
		}

		queryDuration := 200 * time.Millisecond

		start := time.Now()
		stats := service.calculateBatchQueryStats(trackingNos, items, queryDuration)
		calcDuration := time.Since(start)

		assert.Equal(t, 50, stats.TotalQueried)
		assert.Equal(t, 30, stats.FoundCount)
		assert.Equal(t, 20, len(stats.NotFound))
		assert.Equal(t, "200.00ms", stats.QueryTime)

		// 验证计算性能（应该在1ms内完成）
		assert.Less(t, calcDuration, time.Millisecond, "批量查询统计计算应该很快")
	})

	t.Run("边界情况 - 空列表", func(t *testing.T) {
		trackingNos := []string{}
		items := []*model.OrderListItem{}
		queryDuration := 10 * time.Millisecond

		stats := service.calculateBatchQueryStats(trackingNos, items, queryDuration)

		assert.Equal(t, 0, stats.TotalQueried)
		assert.Equal(t, 0, stats.FoundCount)
		assert.Equal(t, 0, len(stats.NotFound))
		assert.Equal(t, "10.00ms", stats.QueryTime)
	})

	t.Run("边界情况 - 单个运单号", func(t *testing.T) {
		trackingNos := []string{"SF1234567890"}
		items := []*model.OrderListItem{
			{TrackingNo: "SF1234567890"},
		}
		queryDuration := 25 * time.Millisecond

		stats := service.calculateBatchQueryStats(trackingNos, items, queryDuration)

		assert.Equal(t, 1, stats.TotalQueried)
		assert.Equal(t, 1, stats.FoundCount)
		assert.Equal(t, 0, len(stats.NotFound))
		assert.Equal(t, "25.00ms", stats.QueryTime)
	})
}

// TestBatchQueryStatsModel 测试批量查询统计信息数据模型
func TestBatchQueryStatsModel(t *testing.T) {
	t.Run("批量查询统计信息创建", func(t *testing.T) {
		stats := &model.BatchQueryStats{
			TotalQueried: 5,
			FoundCount:   3,
			NotFound:     []string{"SF1111111111", "YT2222222222"},
			QueryTime:    "125.50ms",
		}

		assert.Equal(t, 5, stats.TotalQueried)
		assert.Equal(t, 3, stats.FoundCount)
		assert.Equal(t, 2, len(stats.NotFound))
		assert.Equal(t, "125.50ms", stats.QueryTime)
	})

	t.Run("空的未找到列表", func(t *testing.T) {
		stats := &model.BatchQueryStats{
			TotalQueried: 3,
			FoundCount:   3,
			NotFound:     []string{},
			QueryTime:    "85.20ms",
		}

		assert.Equal(t, 0, len(stats.NotFound))
		assert.Equal(t, stats.TotalQueried, stats.FoundCount)
	})
}

// TestOrderListDataWithBatchStats 测试订单列表数据中的批量统计信息
func TestOrderListDataWithBatchStats(t *testing.T) {
	t.Run("包含批量统计信息的订单列表数据", func(t *testing.T) {
		data := &model.OrderListData{
			Items:      []*model.OrderListItem{},
			Total:      3,
			Page:       1,
			PageSize:   20,
			TotalPages: 1,
			HasNext:    false,
			HasPrev:    false,
			BatchStats: &model.BatchQueryStats{
				TotalQueried: 5,
				FoundCount:   3,
				NotFound:     []string{"SF1111111111", "YT2222222222"},
				QueryTime:    "95.30ms",
			},
		}

		assert.NotNil(t, data.BatchStats)
		assert.Equal(t, 5, data.BatchStats.TotalQueried)
		assert.Equal(t, 3, data.BatchStats.FoundCount)
		assert.Equal(t, 2, len(data.BatchStats.NotFound))
	})

	t.Run("不包含批量统计信息的普通查询", func(t *testing.T) {
		data := &model.OrderListData{
			Items:      []*model.OrderListItem{},
			Total:      10,
			Page:       1,
			PageSize:   20,
			TotalPages: 1,
			HasNext:    false,
			HasPrev:    false,
			BatchStats: nil, // 普通查询不包含批量统计
		}

		assert.Nil(t, data.BatchStats)
	})
}

// BenchmarkCalculateBatchQueryStats 批量查询统计计算性能基准测试
func BenchmarkCalculateBatchQueryStats(b *testing.B) {
	logger, _ := zap.NewDevelopment()
	service := &OrderService{
		logger: logger,
	}

	// 创建测试数据
	trackingNos := make([]string, 50)
	items := make([]*model.OrderListItem, 30)

	for i := 0; i < 50; i++ {
		trackingNos[i] = "SF12345678" + string(rune('0'+i%10)) + string(rune('0'+(i/10)%10))
	}

	for i := 0; i < 30; i++ {
		items[i] = &model.OrderListItem{
			TrackingNo: trackingNos[i],
		}
	}

	queryDuration := 150 * time.Millisecond

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = service.calculateBatchQueryStats(trackingNos, items, queryDuration)
	}
}
