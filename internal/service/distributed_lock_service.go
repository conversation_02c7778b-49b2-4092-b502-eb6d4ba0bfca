package service

import (
	"context"
	"crypto/rand"
	"fmt"
	"math/big"
	"strconv"
	"strings"
	"time"

	"github.com/go-redis/redis/v8"
	"go.uber.org/zap"
)

// DistributedLockManager 分布式锁管理器
type DistributedLockManager struct {
	redisClient *redis.Client
	logger      *zap.Logger
	lockPrefix  string
	defaultTTL  time.Duration
}

// DistributedLock 分布式锁
type DistributedLock struct {
	manager   *DistributedLockManager
	lockKey   string
	lockValue string
	ttl       time.Duration
	acquired  bool
	ctx       context.Context
	cancel    context.CancelFunc
}

// NewDistributedLockManager 创建分布式锁管理器
func NewDistributedLockManager(redisClient *redis.Client, logger *zap.Logger) *DistributedLockManager {
	return &DistributedLockManager{
		redisClient: redisClient,
		logger:      logger,
		lockPrefix:  "balance_lock:",
		defaultTTL:  10 * time.Second,
	}
}

// NewLock 创建新的分布式锁
func (m *DistributedLockManager) NewLock(resource string, ttl time.Duration) *DistributedLock {
	if ttl <= 0 {
		ttl = m.defaultTTL
	}

	lockValue := m.generateLockValue()
	lockKey := m.lockPrefix + resource
	
	ctx, cancel := context.WithCancel(context.Background())

	return &DistributedLock{
		manager:   m,
		lockKey:   lockKey,
		lockValue: lockValue,
		ttl:       ttl,
		acquired:  false,
		ctx:       ctx,
		cancel:    cancel,
	}
}

// Lock 获取锁
func (l *DistributedLock) Lock(ctx context.Context) error {
	// Lua脚本确保原子性获取锁
	lockScript := `
		if redis.call("GET", KEYS[1]) == false then
			return redis.call("SETEX", KEYS[1], ARGV[1], ARGV[2])
		else
			return false
		end
	`

	ttlSeconds := int64(l.ttl.Seconds())
	
	// 尝试获取锁
	result, err := l.manager.redisClient.Eval(ctx, lockScript, []string{l.lockKey}, ttlSeconds, l.lockValue).Result()
	if err != nil {
		return fmt.Errorf("获取分布式锁失败: %w", err)
	}

	if result == "OK" {
		l.acquired = true
		l.manager.logger.Debug("🔒 获取分布式锁成功",
			zap.String("lock_key", l.lockKey),
			zap.String("lock_value", l.lockValue),
			zap.Duration("ttl", l.ttl))

		// 启动锁续期
		go l.renewLock()
		return nil
	}

	return fmt.Errorf("锁已被其他进程持有")
}

// TryLock 尝试获取锁（非阻塞）
func (l *DistributedLock) TryLock(ctx context.Context) bool {
	err := l.Lock(ctx)
	return err == nil
}

// LockWithRetry 带重试的获取锁
func (l *DistributedLock) LockWithRetry(ctx context.Context, maxRetries int, retryInterval time.Duration) error {
	for i := 0; i < maxRetries; i++ {
		err := l.Lock(ctx)
		if err == nil {
			return nil
		}

		if i < maxRetries-1 {
			select {
			case <-ctx.Done():
				return ctx.Err()
			case <-time.After(retryInterval):
				// 继续重试
			}
		}
	}

	return fmt.Errorf("获取锁失败，已重试 %d 次", maxRetries)
}

// Unlock 释放锁
func (l *DistributedLock) Unlock() error {
	if !l.acquired {
		return nil
	}

	// 停止续期
	l.cancel()

	// Lua脚本确保只释放自己的锁
	unlockScript := `
		if redis.call("GET", KEYS[1]) == ARGV[1] then
			return redis.call("DEL", KEYS[1])
		else
			return 0
		end
	`

	result, err := l.manager.redisClient.Eval(context.Background(), unlockScript, []string{l.lockKey}, l.lockValue).Result()
	if err != nil {
		return fmt.Errorf("释放分布式锁失败: %w", err)
	}

	if result.(int64) == 1 {
		l.acquired = false
		l.manager.logger.Debug("🔓 释放分布式锁成功",
			zap.String("lock_key", l.lockKey),
			zap.String("lock_value", l.lockValue))
		return nil
	}

	l.manager.logger.Warn("⚠️ 锁可能已过期或被其他进程释放",
		zap.String("lock_key", l.lockKey),
		zap.String("lock_value", l.lockValue))
	
	l.acquired = false
	return nil
}

// renewLock 自动续期锁
func (l *DistributedLock) renewLock() {
	renewInterval := l.ttl / 3 // 每1/3 TTL时间续期一次
	ticker := time.NewTicker(renewInterval)
	defer ticker.Stop()

	renewScript := `
		if redis.call("GET", KEYS[1]) == ARGV[1] then
			return redis.call("EXPIRE", KEYS[1], ARGV[2])
		else
			return 0
		end
	`

	for {
		select {
		case <-l.ctx.Done():
			return
		case <-ticker.C:
			if !l.acquired {
				return
			}

			ttlSeconds := int64(l.ttl.Seconds())
			result, err := l.manager.redisClient.Eval(context.Background(), renewScript, 
				[]string{l.lockKey}, l.lockValue, ttlSeconds).Result()
			
			if err != nil {
				l.manager.logger.Error("🔄 锁续期失败",
					zap.String("lock_key", l.lockKey),
					zap.Error(err))
				l.acquired = false
				return
			}

			if result.(int64) == 0 {
				l.manager.logger.Warn("⚠️ 锁已失效，停止续期",
					zap.String("lock_key", l.lockKey))
				l.acquired = false
				return
			}

			l.manager.logger.Debug("🔄 锁续期成功",
				zap.String("lock_key", l.lockKey),
				zap.Duration("ttl", l.ttl))
		}
	}
}

// generateLockValue 生成唯一的锁值
func (m *DistributedLockManager) generateLockValue() string {
	// 使用时间戳 + 随机数生成唯一值
	timestamp := time.Now().UnixNano()
	randomNum, _ := rand.Int(rand.Reader, big.NewInt(1000000))
	return fmt.Sprintf("%d_%s", timestamp, randomNum.String())
}

// IsLocked 检查资源是否被锁定
func (m *DistributedLockManager) IsLocked(ctx context.Context, resource string) bool {
	lockKey := m.lockPrefix + resource
	result, err := m.redisClient.Exists(ctx, lockKey).Result()
	if err != nil {
		return false
	}
	return result > 0
}

// ForceDel 强制删除锁（管理员操作）
func (m *DistributedLockManager) ForceDel(ctx context.Context, resource string) error {
	lockKey := m.lockPrefix + resource
	return m.redisClient.Del(ctx, lockKey).Err()
}

// GetLockInfo 获取锁信息
func (m *DistributedLockManager) GetLockInfo(ctx context.Context, resource string) (*LockInfo, error) {
	lockKey := m.lockPrefix + resource
	
	pipe := m.redisClient.Pipeline()
	getValue := pipe.Get(ctx, lockKey)
	getTTL := pipe.TTL(ctx, lockKey)
	
	_, err := pipe.Exec(ctx)
	if err != nil && err != redis.Nil {
		return nil, err
	}

	if getValue.Err() == redis.Nil {
		return &LockInfo{
			Resource: resource,
			Locked:   false,
		}, nil
	}

	value := getValue.Val()
	ttl := getTTL.Val()

	// 解析锁值获取创建时间
	parts := strings.Split(value, "_")
	var createdAt time.Time
	if len(parts) >= 1 {
		if timestamp, err := strconv.ParseInt(parts[0], 10, 64); err == nil {
			createdAt = time.Unix(0, timestamp)
		}
	}

	return &LockInfo{
		Resource:  resource,
		Locked:    true,
		Value:     value,
		TTL:       ttl,
		CreatedAt: createdAt,
	}, nil
}

// LockInfo 锁信息
type LockInfo struct {
	Resource  string        `json:"resource"`
	Locked    bool          `json:"locked"`
	Value     string        `json:"value,omitempty"`
	TTL       time.Duration `json:"ttl,omitempty"`
	CreatedAt time.Time     `json:"created_at,omitempty"`
}

// BalanceLockService 余额专用锁服务
type BalanceLockService struct {
	lockManager *DistributedLockManager
	logger      *zap.Logger
}

// NewBalanceLockService 创建余额锁服务
func NewBalanceLockService(redisClient *redis.Client, logger *zap.Logger) *BalanceLockService {
	return &BalanceLockService{
		lockManager: NewDistributedLockManager(redisClient, logger),
		logger:      logger,
	}
}

// WithUserLock 在用户余额锁保护下执行操作
func (s *BalanceLockService) WithUserLock(ctx context.Context, userID string, operation func() error) error {
	lock := s.lockManager.NewLock("user:"+userID, 30*time.Second)
	
	s.logger.Debug("🔒 尝试获取用户余额锁",
		zap.String("user_id", userID))

	// 尝试获取锁，最多重试5次
	err := lock.LockWithRetry(ctx, 5, 100*time.Millisecond)
	if err != nil {
		s.logger.Error("❌ 获取用户余额锁失败",
			zap.String("user_id", userID),
			zap.Error(err))
		return fmt.Errorf("获取用户余额锁失败: %w", err)
	}

	defer func() {
		if unlockErr := lock.Unlock(); unlockErr != nil {
			s.logger.Error("❌ 释放用户余额锁失败",
				zap.String("user_id", userID),
				zap.Error(unlockErr))
		}
	}()

	s.logger.Debug("✅ 获取用户余额锁成功，执行操作",
		zap.String("user_id", userID))

	// 执行受保护的操作
	return operation()
}

// WithOrderLock 在订单锁保护下执行操作
func (s *BalanceLockService) WithOrderLock(ctx context.Context, orderNo string, operation func() error) error {
	lock := s.lockManager.NewLock("order:"+orderNo, 60*time.Second)
	
	err := lock.LockWithRetry(ctx, 3, 200*time.Millisecond)
	if err != nil {
		return fmt.Errorf("获取订单锁失败: %w", err)
	}

	defer lock.Unlock()
	
	return operation()
}

// GetUserLockStatus 获取用户锁状态
func (s *BalanceLockService) GetUserLockStatus(ctx context.Context, userID string) (*LockInfo, error) {
	return s.lockManager.GetLockInfo(ctx, "user:"+userID)
}

// Emergency functions for production debugging

// ListAllLocks 列出所有余额相关的锁（管理员功能）
func (s *BalanceLockService) ListAllLocks(ctx context.Context) (map[string]*LockInfo, error) {
	pattern := s.lockManager.lockPrefix + "*"
	keys, err := s.lockManager.redisClient.Keys(ctx, pattern).Result()
	if err != nil {
		return nil, err
	}

	locks := make(map[string]*LockInfo)
	for _, key := range keys {
		resource := strings.TrimPrefix(key, s.lockManager.lockPrefix)
		lockInfo, err := s.lockManager.GetLockInfo(ctx, resource)
		if err != nil {
			continue
		}
		locks[resource] = lockInfo
	}

	return locks, nil
}

// ForceUnlockUser 强制解锁用户（紧急情况使用）
func (s *BalanceLockService) ForceUnlockUser(ctx context.Context, userID string) error {
	s.logger.Warn("🚨 强制解锁用户余额",
		zap.String("user_id", userID))
	
	return s.lockManager.ForceDel(ctx, "user:"+userID)
}

// HealthCheck 健康检查
func (s *BalanceLockService) HealthCheck(ctx context.Context) error {
	// 尝试获取一个测试锁
	testLock := s.lockManager.NewLock("health_check", 5*time.Second)
	
	if err := testLock.Lock(ctx); err != nil {
		return fmt.Errorf("分布式锁健康检查失败: %w", err)
	}
	
	defer testLock.Unlock()
	
	s.logger.Debug("✅ 分布式锁健康检查通过")
	return nil
}