package service

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/repository"
)

// HistoricalOrderMigrationService 历史订单迁移服务
// 🔥 企业级数据迁移：安全、可控、可回滚的历史订单平台订单号生成
type HistoricalOrderMigrationService struct {
	orderRepository        repository.OrderRepository
	platformOrderGenerator *PlatformOrderGenerator
	logger                 *zap.Logger
}

// NewHistoricalOrderMigrationService 创建历史订单迁移服务
func NewHistoricalOrderMigrationService(
	orderRepository repository.OrderRepository,
	platformOrderGenerator *PlatformOrderGenerator,
	logger *zap.Logger,
) *HistoricalOrderMigrationService {
	return &HistoricalOrderMigrationService{
		orderRepository:        orderRepository,
		platformOrderGenerator: platformOrderGenerator,
		logger:                 logger,
	}
}

// MigrationConfig 迁移配置
type MigrationConfig struct {
	BatchSize           int           `json:"batch_size"`            // 批处理大小，默认100
	MaxConcurrency      int           `json:"max_concurrency"`       // 最大并发数，默认5
	DelayBetweenBatches time.Duration `json:"delay_between_batches"` // 批次间延迟，默认1秒
	DryRun              bool          `json:"dry_run"`               // 是否为试运行模式
	StartDate           *time.Time    `json:"start_date"`            // 开始日期过滤
	EndDate             *time.Time    `json:"end_date"`              // 结束日期过滤
	MaxRecords          int           `json:"max_records"`           // 最大处理记录数，0表示无限制
}

// DefaultMigrationConfig 默认迁移配置
func DefaultMigrationConfig() *MigrationConfig {
	return &MigrationConfig{
		BatchSize:           100,
		MaxConcurrency:      5,
		DelayBetweenBatches: 1 * time.Second,
		DryRun:              true, // 默认为试运行模式
		MaxRecords:          0,    // 默认无限制
	}
}

// MigrationResult 迁移结果
type MigrationResult struct {
	TotalRecords     int64         `json:"total_records"`     // 总记录数
	ProcessedRecords int64         `json:"processed_records"` // 已处理记录数
	SuccessRecords   int64         `json:"success_records"`   // 成功记录数
	FailedRecords    int64         `json:"failed_records"`    // 失败记录数
	SkippedRecords   int64         `json:"skipped_records"`   // 跳过记录数
	StartTime        time.Time     `json:"start_time"`        // 开始时间
	EndTime          time.Time     `json:"end_time"`          // 结束时间
	Duration         time.Duration `json:"duration"`          // 耗时
	Errors           []string      `json:"errors"`            // 错误列表
	DryRun           bool          `json:"dry_run"`           // 是否为试运行
}

// MigrationProgress 迁移进度
type MigrationProgress struct {
	TotalRecords     int64   `json:"total_records"`     // 总记录数
	ProcessedRecords int64   `json:"processed_records"` // 已处理记录数
	SuccessRecords   int64   `json:"success_records"`   // 成功记录数
	FailedRecords    int64   `json:"failed_records"`    // 失败记录数
	Progress         float64 `json:"progress"`          // 进度百分比
	CurrentBatch     int     `json:"current_batch"`     // 当前批次
	EstimatedTime    string  `json:"estimated_time"`    // 预计剩余时间
}

// MigrateHistoricalOrders 迁移历史订单
// 🔥 核心功能：为没有平台订单号的历史订单生成平台订单号
func (s *HistoricalOrderMigrationService) MigrateHistoricalOrders(ctx context.Context, config *MigrationConfig) (*MigrationResult, error) {
	if config == nil {
		config = DefaultMigrationConfig()
	}

	s.logger.Info("开始历史订单迁移",
		zap.Int("batch_size", config.BatchSize),
		zap.Int("max_concurrency", config.MaxConcurrency),
		zap.Duration("delay_between_batches", config.DelayBetweenBatches),
		zap.Bool("dry_run", config.DryRun),
		zap.Int("max_records", config.MaxRecords))

	result := &MigrationResult{
		StartTime: time.Now(),
		DryRun:    config.DryRun,
		Errors:    make([]string, 0),
	}

	// 1. 统计需要迁移的订单总数
	totalCount, err := s.countOrdersNeedingMigration(ctx, config)
	if err != nil {
		return nil, fmt.Errorf("统计需要迁移的订单失败: %w", err)
	}

	result.TotalRecords = totalCount
	s.logger.Info("统计需要迁移的订单完成", zap.Int64("total_count", totalCount))

	if totalCount == 0 {
		s.logger.Info("没有需要迁移的订单")
		result.EndTime = time.Now()
		result.Duration = result.EndTime.Sub(result.StartTime)
		return result, nil
	}

	// 2. 分批处理订单
	batchCount := int((totalCount + int64(config.BatchSize) - 1) / int64(config.BatchSize))
	s.logger.Info("开始分批处理", zap.Int("batch_count", batchCount))

	for batchIndex := 0; batchIndex < batchCount; batchIndex++ {
		// 检查上下文是否被取消
		select {
		case <-ctx.Done():
			s.logger.Warn("迁移被取消", zap.Error(ctx.Err()))
			result.EndTime = time.Now()
			result.Duration = result.EndTime.Sub(result.StartTime)
			return result, ctx.Err()
		default:
		}

		// 检查是否达到最大记录数限制
		if config.MaxRecords > 0 && result.ProcessedRecords >= int64(config.MaxRecords) {
			s.logger.Info("达到最大记录数限制，停止迁移", zap.Int("max_records", config.MaxRecords))
			break
		}

		// 处理当前批次
		batchResult, err := s.processBatch(ctx, config, batchIndex, config.BatchSize)
		if err != nil {
			errorMsg := fmt.Sprintf("处理批次 %d 失败: %v", batchIndex, err)
			s.logger.Error(errorMsg)
			result.Errors = append(result.Errors, errorMsg)
			result.FailedRecords += int64(config.BatchSize)
		} else {
			result.ProcessedRecords += batchResult.ProcessedRecords
			result.SuccessRecords += batchResult.SuccessRecords
			result.FailedRecords += batchResult.FailedRecords
			result.SkippedRecords += batchResult.SkippedRecords
			result.Errors = append(result.Errors, batchResult.Errors...)
		}

		// 记录进度
		progress := s.calculateProgress(result, totalCount, batchIndex+1, batchCount)
		s.logger.Info("迁移进度",
			zap.Int("current_batch", batchIndex+1),
			zap.Int("total_batches", batchCount),
			zap.Float64("progress", progress.Progress),
			zap.Int64("processed", result.ProcessedRecords),
			zap.Int64("success", result.SuccessRecords),
			zap.Int64("failed", result.FailedRecords))

		// 批次间延迟
		if batchIndex < batchCount-1 && config.DelayBetweenBatches > 0 {
			time.Sleep(config.DelayBetweenBatches)
		}
	}

	result.EndTime = time.Now()
	result.Duration = result.EndTime.Sub(result.StartTime)

	s.logger.Info("历史订单迁移完成",
		zap.Int64("total_records", result.TotalRecords),
		zap.Int64("processed_records", result.ProcessedRecords),
		zap.Int64("success_records", result.SuccessRecords),
		zap.Int64("failed_records", result.FailedRecords),
		zap.Int64("skipped_records", result.SkippedRecords),
		zap.Duration("duration", result.Duration),
		zap.Bool("dry_run", result.DryRun))

	return result, nil
}

// countOrdersNeedingMigration 统计需要迁移的订单数量
func (s *HistoricalOrderMigrationService) countOrdersNeedingMigration(ctx context.Context, config *MigrationConfig) (int64, error) {
	// 构建查询条件
	whereClause := "WHERE (platform_order_no IS NULL OR platform_order_no = '')"
	args := make([]interface{}, 0)
	argIndex := 1

	// 添加日期过滤
	if config.StartDate != nil {
		whereClause += fmt.Sprintf(" AND created_at >= $%d", argIndex)
		args = append(args, *config.StartDate)
		argIndex++
	}

	if config.EndDate != nil {
		whereClause += fmt.Sprintf(" AND created_at <= $%d", argIndex)
		args = append(args, *config.EndDate)
		argIndex++
	}

	query := fmt.Sprintf("SELECT COUNT(*) FROM order_records %s", whereClause)

	s.logger.Debug("统计需要迁移的订单", zap.String("query", query))

	count, err := s.orderRepository.QueryRawSQLCount(ctx, query, args...)
	if err != nil {
		return 0, fmt.Errorf("查询订单数量失败: %w", err)
	}

	return count, nil
}

// processBatch 处理单个批次
func (s *HistoricalOrderMigrationService) processBatch(ctx context.Context, config *MigrationConfig, batchIndex int, batchSize int) (*MigrationResult, error) {
	batchResult := &MigrationResult{
		StartTime: time.Now(),
		DryRun:    config.DryRun,
		Errors:    make([]string, 0),
	}

	// 获取当前批次的订单
	orders, err := s.getOrdersBatch(ctx, config, batchIndex, batchSize)
	if err != nil {
		return nil, fmt.Errorf("获取批次订单失败: %w", err)
	}

	s.logger.Debug("获取批次订单",
		zap.Int("batch_index", batchIndex),
		zap.Int("batch_size", len(orders)))

	// 处理每个订单
	for _, order := range orders {
		err := s.processOrder(ctx, config, order)
		if err != nil {
			errorMsg := fmt.Sprintf("处理订单 %d 失败: %v", order.ID, err)
			s.logger.Error(errorMsg)
			batchResult.Errors = append(batchResult.Errors, errorMsg)
			batchResult.FailedRecords++
		} else {
			batchResult.SuccessRecords++
		}
		batchResult.ProcessedRecords++
	}

	batchResult.EndTime = time.Now()
	batchResult.Duration = batchResult.EndTime.Sub(batchResult.StartTime)

	return batchResult, nil
}

// getOrdersBatch 获取批次订单
func (s *HistoricalOrderMigrationService) getOrdersBatch(ctx context.Context, config *MigrationConfig, batchIndex int, batchSize int) ([]*model.OrderRecord, error) {
	// 构建查询条件
	whereClause := "WHERE (platform_order_no IS NULL OR platform_order_no = '')"
	args := make([]interface{}, 0)
	argIndex := 1

	// 添加日期过滤
	if config.StartDate != nil {
		whereClause += fmt.Sprintf(" AND created_at >= $%d", argIndex)
		args = append(args, *config.StartDate)
		argIndex++
	}

	if config.EndDate != nil {
		whereClause += fmt.Sprintf(" AND created_at <= $%d", argIndex)
		args = append(args, *config.EndDate)
		argIndex++
	}

	// 添加分页
	offset := batchIndex * batchSize
	whereClause += fmt.Sprintf(" ORDER BY id LIMIT $%d OFFSET $%d", argIndex, argIndex+1)
	args = append(args, batchSize, offset)

	query := fmt.Sprintf(`
		SELECT id, customer_order_no, order_no, tracking_no, express_type, product_type,
		       provider, status, weight, price, user_id, created_at, updated_at
		FROM order_records %s
	`, whereClause)

	s.logger.Debug("查询批次订单", zap.String("query", query))

	orders, err := s.orderRepository.QueryRawSQLOrders(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("查询批次订单失败: %w", err)
	}

	return orders, nil
}

// processOrder 处理单个订单
func (s *HistoricalOrderMigrationService) processOrder(ctx context.Context, config *MigrationConfig, order *model.OrderRecord) error {
	// 检查订单是否已有平台订单号
	if order.PlatformOrderNo != "" {
		s.logger.Debug("订单已有平台订单号，跳过", zap.Int64("order_id", order.ID))
		return nil
	}

	// 生成平台订单号
	platformOrderNo, err := s.platformOrderGenerator.GeneratePlatformOrderNo(ctx)
	if err != nil {
		return fmt.Errorf("生成平台订单号失败: %w", err)
	}

	s.logger.Debug("为订单生成平台订单号",
		zap.Int64("order_id", order.ID),
		zap.String("platform_order_no", platformOrderNo))

	// 试运行模式下不实际更新数据库
	if config.DryRun {
		s.logger.Debug("试运行模式，不更新数据库",
			zap.Int64("order_id", order.ID),
			zap.String("platform_order_no", platformOrderNo))
		return nil
	}

	// 更新订单的平台订单号
	updateQuery := "UPDATE order_records SET platform_order_no = $1, updated_at = $2 WHERE id = $3"
	err = s.orderRepository.ExecRawSQL(ctx, updateQuery, platformOrderNo, time.Now(), order.ID)
	if err != nil {
		return fmt.Errorf("更新订单平台订单号失败: %w", err)
	}

	s.logger.Debug("成功更新订单平台订单号",
		zap.Int64("order_id", order.ID),
		zap.String("platform_order_no", platformOrderNo))

	return nil
}

// calculateProgress 计算迁移进度
func (s *HistoricalOrderMigrationService) calculateProgress(result *MigrationResult, totalRecords int64, currentBatch, totalBatches int) *MigrationProgress {
	progress := float64(result.ProcessedRecords) / float64(totalRecords) * 100
	if progress > 100 {
		progress = 100
	}

	// 估算剩余时间
	elapsed := time.Since(result.StartTime)
	var estimatedTime string
	if result.ProcessedRecords > 0 {
		avgTimePerRecord := elapsed / time.Duration(result.ProcessedRecords)
		remainingRecords := totalRecords - result.ProcessedRecords
		remainingTime := avgTimePerRecord * time.Duration(remainingRecords)
		estimatedTime = remainingTime.Round(time.Second).String()
	} else {
		estimatedTime = "未知"
	}

	return &MigrationProgress{
		TotalRecords:     totalRecords,
		ProcessedRecords: result.ProcessedRecords,
		SuccessRecords:   result.SuccessRecords,
		FailedRecords:    result.FailedRecords,
		Progress:         progress,
		CurrentBatch:     currentBatch,
		EstimatedTime:    estimatedTime,
	}
}

// GetMigrationStatus 获取迁移状态
func (s *HistoricalOrderMigrationService) GetMigrationStatus(ctx context.Context) (*MigrationProgress, error) {
	// 统计总订单数
	totalQuery := "SELECT COUNT(*) FROM order_records"
	totalRecords, err := s.orderRepository.QueryRawSQLCount(ctx, totalQuery)
	if err != nil {
		return nil, fmt.Errorf("统计总订单数失败: %w", err)
	}

	// 统计已迁移订单数
	migratedQuery := "SELECT COUNT(*) FROM order_records WHERE platform_order_no IS NOT NULL AND platform_order_no != ''"
	migratedRecords, err := s.orderRepository.QueryRawSQLCount(ctx, migratedQuery)
	if err != nil {
		return nil, fmt.Errorf("统计已迁移订单数失败: %w", err)
	}

	// 计算进度
	progress := float64(migratedRecords) / float64(totalRecords) * 100
	if totalRecords == 0 {
		progress = 100
	}

	return &MigrationProgress{
		TotalRecords:     totalRecords,
		ProcessedRecords: migratedRecords,
		SuccessRecords:   migratedRecords,
		FailedRecords:    0, // 这里可以通过日志或其他方式统计
		Progress:         progress,
		CurrentBatch:     0,
		EstimatedTime:    "0s",
	}, nil
}
