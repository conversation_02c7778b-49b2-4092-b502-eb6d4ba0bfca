package service

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/your-org/go-kuaidi/internal/model"
	"go.uber.org/zap"
	"github.com/your-org/go-kuaidi/internal/util"

)

// OrderErrorAnalysisService 订单错误分析服务
type OrderErrorAnalysisService struct {
	logger *zap.Logger
}

// NewOrderErrorAnalysisService 创建订单错误分析服务
func NewOrderErrorAnalysisService(logger *zap.Logger) *OrderErrorAnalysisService {
	return &OrderErrorAnalysisService{
		logger: logger,
	}
}

// ErrorAnalysis 错误分析结果
type ErrorAnalysis struct {
	ErrorType        string            `json:"error_type"`         // 错误类型
	ErrorCategory    string            `json:"error_category"`     // 错误分类
	IsRetryable      bool              `json:"is_retryable"`       // 是否可重试
	SuggestedAction  string            `json:"suggested_action"`   // 建议操作
	ErrorKeywords    []string          `json:"error_keywords"`     // 错误关键词
	ProviderSpecific map[string]string `json:"provider_specific"`  // 供应商特定信息
	Severity         string            `json:"severity"`           // 严重程度
	UserFriendlyMsg  string            `json:"user_friendly_msg"`  // 用户友好消息
}

// AnalyzeProviderError 分析供应商错误
func (s *OrderErrorAnalysisService) AnalyzeProviderError(err error, provider string) *ErrorAnalysis {
	if err == nil {
		return nil
	}

	errMsg := err.Error()
	analysis := &ErrorAnalysis{
		ErrorKeywords:    s.extractKeywords(errMsg),
		ProviderSpecific: make(map[string]string),
	}

	// 根据供应商进行特定分析
	switch provider {
	case "yuntong":
		s.analyzeYuntongError(errMsg, analysis)
	case "kuaidi100":
		s.analyzeKuaidi100Error(errMsg, analysis)
	case "yida":
		s.analyzeYidaError(errMsg, analysis)
	default:
		s.analyzeGenericError(errMsg, analysis)
	}

	return analysis
}

// analyzeYuntongError 分析云通错误
func (s *OrderErrorAnalysisService) analyzeYuntongError(errMsg string, analysis *ErrorAnalysis) {
	analysis.ProviderSpecific["provider"] = "yuntong"

	// 云通特定错误模式
	if strings.Contains(errMsg, "当前线路暂未开放商家寄递服务") {
		analysis.ErrorType = "ROUTE_NOT_SUPPORTED"
		analysis.ErrorCategory = "BUSINESS_RULE"
		analysis.IsRetryable = false
		analysis.SuggestedAction = "选择其他快递公司或供应商"
		analysis.Severity = "LOW"
		analysis.UserFriendlyMsg = "该快递公司在当前线路暂不提供服务，请选择其他快递公司"
	} else if strings.Contains(errMsg, "参数验证失败") || strings.Contains(errMsg, "地址格式不正确") {
		analysis.ErrorType = "PARAMETER_VALIDATION"
		analysis.ErrorCategory = "INPUT_ERROR"
		analysis.IsRetryable = true
		analysis.SuggestedAction = "检查并修正地址信息"
		analysis.Severity = "MEDIUM"
		analysis.UserFriendlyMsg = "地址信息格式不正确，请检查收寄件地址"
	} else if strings.Contains(errMsg, "余额不足") || strings.Contains(errMsg, "账户余额") {
		analysis.ErrorType = "INSUFFICIENT_BALANCE"
		analysis.ErrorCategory = "ACCOUNT_ISSUE"
		analysis.IsRetryable = true
		analysis.SuggestedAction = "联系供应商充值"
		analysis.Severity = "HIGH"
		analysis.UserFriendlyMsg = "供应商账户余额不足，请联系客服"
	} else if strings.Contains(errMsg, "网络") || strings.Contains(errMsg, "超时") || strings.Contains(errMsg, "连接") {
		analysis.ErrorType = "NETWORK_ERROR"
		analysis.ErrorCategory = "TECHNICAL_ISSUE"
		analysis.IsRetryable = true
		analysis.SuggestedAction = "稍后重试"
		analysis.Severity = "MEDIUM"
		analysis.UserFriendlyMsg = "网络连接异常，请稍后重试"
	} else {
		analysis.ErrorType = "UNKNOWN_YUNTONG_ERROR"
		analysis.ErrorCategory = "UNKNOWN"
		analysis.IsRetryable = false
		analysis.SuggestedAction = "联系技术支持"
		analysis.Severity = "HIGH"
		analysis.UserFriendlyMsg = "服务暂时不可用，请联系客服"
	}
}

// analyzeKuaidi100Error 分析快递100错误
func (s *OrderErrorAnalysisService) analyzeKuaidi100Error(errMsg string, analysis *ErrorAnalysis) {
	analysis.ProviderSpecific["provider"] = "kuaidi100"

	if strings.Contains(errMsg, "大于2.49公斤") {
		analysis.ErrorType = "WEIGHT_LIMIT_EXCEEDED"
		analysis.ErrorCategory = "BUSINESS_RULE"
		analysis.IsRetryable = false
		analysis.SuggestedAction = "选择支持大件的快递公司"
		analysis.Severity = "LOW"
		analysis.UserFriendlyMsg = "包裹重量超出该快递公司限制，请选择其他快递公司"
	} else if strings.Contains(errMsg, "该区域暂时不开放") {
		analysis.ErrorType = "AREA_NOT_SUPPORTED"
		analysis.ErrorCategory = "BUSINESS_RULE"
		analysis.IsRetryable = false
		analysis.SuggestedAction = "选择其他快递公司"
		analysis.Severity = "LOW"
		analysis.UserFriendlyMsg = "该快递公司在当前区域暂不提供服务"
	} else {
		s.analyzeGenericError(errMsg, analysis)
		analysis.ProviderSpecific["provider"] = "kuaidi100"
	}
}

// analyzeYidaError 分析易达错误
func (s *OrderErrorAnalysisService) analyzeYidaError(errMsg string, analysis *ErrorAnalysis) {
	analysis.ProviderSpecific["provider"] = "yida"
	s.analyzeGenericError(errMsg, analysis)
}

// analyzeGenericError 分析通用错误
func (s *OrderErrorAnalysisService) analyzeGenericError(errMsg string, analysis *ErrorAnalysis) {
	if strings.Contains(errMsg, "不支持") {
		analysis.ErrorType = "NOT_SUPPORTED"
		analysis.ErrorCategory = "BUSINESS_RULE"
		analysis.IsRetryable = false
		analysis.SuggestedAction = "选择其他选项"
		analysis.Severity = "LOW"
		analysis.UserFriendlyMsg = "当前选择不被支持，请选择其他选项"
	} else if strings.Contains(errMsg, "参数") || strings.Contains(errMsg, "格式") {
		analysis.ErrorType = "PARAMETER_ERROR"
		analysis.ErrorCategory = "INPUT_ERROR"
		analysis.IsRetryable = true
		analysis.SuggestedAction = "检查输入参数"
		analysis.Severity = "MEDIUM"
		analysis.UserFriendlyMsg = "输入信息有误，请检查后重试"
	} else if strings.Contains(errMsg, "认证") || strings.Contains(errMsg, "授权") {
		analysis.ErrorType = "AUTHENTICATION_ERROR"
		analysis.ErrorCategory = "AUTH_ISSUE"
		analysis.IsRetryable = false
		analysis.SuggestedAction = "检查认证配置"
		analysis.Severity = "HIGH"
		analysis.UserFriendlyMsg = "认证失败，请联系客服"
	} else if strings.Contains(errMsg, "网络") || strings.Contains(errMsg, "超时") {
		analysis.ErrorType = "NETWORK_ERROR"
		analysis.ErrorCategory = "TECHNICAL_ISSUE"
		analysis.IsRetryable = true
		analysis.SuggestedAction = "稍后重试"
		analysis.Severity = "MEDIUM"
		analysis.UserFriendlyMsg = "网络异常，请稍后重试"
	} else {
		analysis.ErrorType = "UNKNOWN_ERROR"
		analysis.ErrorCategory = "UNKNOWN"
		analysis.IsRetryable = false
		analysis.SuggestedAction = "联系技术支持"
		analysis.Severity = "HIGH"
		analysis.UserFriendlyMsg = "服务异常，请联系客服"
	}
}

// extractKeywords 提取错误关键词
func (s *OrderErrorAnalysisService) extractKeywords(errMsg string) []string {
	keywords := []string{}
	
	// 常见错误关键词
	commonKeywords := []string{
		"不支持", "暂未开放", "参数验证失败", "地址格式", "余额不足",
		"网络", "超时", "连接", "认证", "授权", "大于", "区域",
		"线路", "快递公司", "重量", "体积", "格式不正确",
	}

	for _, keyword := range commonKeywords {
		if strings.Contains(errMsg, keyword) {
			keywords = append(keywords, keyword)
		}
	}

	return keywords
}

// CriticalError 关键错误记录
type CriticalError struct {
	ID               string                 `json:"id"`
	CustomerOrderNo  string                 `json:"customer_order_no"`
	UserID           string                 `json:"user_id"`
	Provider         string                 `json:"provider"`
	ErrorStage       string                 `json:"error_stage"`
	ErrorMessage     string                 `json:"error_message"`
	ErrorAnalysis    *ErrorAnalysis         `json:"error_analysis"`
	RequestData      map[string]interface{} `json:"request_data"`
	ResponseData     map[string]interface{} `json:"response_data"`
	ProcessingTimeMs int64                  `json:"processing_time_ms"`
	CreatedAt        time.Time              `json:"created_at"`
	Resolved         bool                   `json:"resolved"`
	ResolutionNotes  string                 `json:"resolution_notes"`
}

// RecordCriticalError 记录关键错误
func (s *OrderErrorAnalysisService) RecordCriticalError(ctx context.Context, req *model.OrderRequest, provider, stage string, err error, analysis *ErrorAnalysis) {
	// 只记录高严重程度的错误
	if analysis == nil || analysis.Severity != "HIGH" {
		return
	}

	criticalError := &CriticalError{
		ID:              fmt.Sprintf("ce_%d", util.NowBeijing().UnixNano()),
		CustomerOrderNo: req.CustomerOrderNo,
		UserID:          req.UserID,
		Provider:        provider,
		ErrorStage:      stage,
		ErrorMessage:    err.Error(),
		ErrorAnalysis:   analysis,
		CreatedAt:       util.NowBeijing(),
		Resolved:        false,
	}

	// 序列化请求数据
	if reqData, err := json.Marshal(req); err == nil {
		var reqMap map[string]interface{}
		if json.Unmarshal(reqData, &reqMap) == nil {
			criticalError.RequestData = reqMap
		}
	}

	// 记录到日志（实际项目中可以保存到专门的错误追踪表）
	s.logger.Error("记录关键错误",
		zap.String("critical_error_id", criticalError.ID),
		zap.String("customer_order_no", criticalError.CustomerOrderNo),
		zap.String("provider", criticalError.Provider),
		zap.String("error_stage", criticalError.ErrorStage),
		zap.String("error_type", analysis.ErrorType),
		zap.String("error_category", analysis.ErrorCategory),
		zap.String("suggested_action", analysis.SuggestedAction),
		zap.Error(err))
}

// GetErrorStatistics 获取错误统计
func (s *OrderErrorAnalysisService) GetErrorStatistics(ctx context.Context, provider string, timeRange time.Duration) map[string]interface{} {
	// 这里可以实现错误统计逻辑
	// 实际项目中可以从数据库查询统计数据
	return map[string]interface{}{
		"provider":    provider,
		"time_range":  timeRange.String(),
		"total_errors": 0,
		"error_types": map[string]int{},
	}
}
