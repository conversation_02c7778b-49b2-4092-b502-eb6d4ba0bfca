package service

import (
	"context"
	"database/sql"
	"sync"
	"testing"
	"time"

	_ "github.com/lib/pq"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap"

	"github.com/your-org/go-kuaidi/internal/util"
)

// TestPlatformOrderGenerator_GeneratePlatformOrderNo 测试平台订单号生成
func TestPlatformOrderGenerator_GeneratePlatformOrderNo(t *testing.T) {
	// 创建测试数据库连接
	db := setupTestDB(t)
	defer db.Close()

	logger := zap.NewNop()
	config := &PlatformOrderConfig{
		Prefix:           "GK",
		CacheSize:        10,
		RetryAttempts:    3,
		RetryDelay:       50 * time.Millisecond,
		EnableMetrics:    false, // 测试时禁用指标
		EnableLocalCache: true,
	}

	generator := NewPlatformOrderGenerator(db, logger, config)
	ctx := context.Background()

	t.Run("基本生成功能", func(t *testing.T) {
		orderNo, err := generator.GeneratePlatformOrderNo(ctx)
		require.NoError(t, err)
		assert.NotEmpty(t, orderNo)

		// 验证格式：GK + 8位日期 + 9位序列号
		assert.Len(t, orderNo, 19)
		assert.True(t, orderNo[:2] == "GK")

		// 验证日期部分
		dateStr := orderNo[2:10]
		_, err = time.Parse("20060102", dateStr)
		assert.NoError(t, err)

		// 验证序列号部分
		sequenceStr := orderNo[10:]
		assert.Len(t, sequenceStr, 9)
		assert.Regexp(t, `^\d{9}$`, sequenceStr)

		t.Logf("生成的平台订单号: %s", orderNo)
	})

	t.Run("序列号递增", func(t *testing.T) {
		var orderNos []string
		for i := 0; i < 5; i++ {
			orderNo, err := generator.GeneratePlatformOrderNo(ctx)
			require.NoError(t, err)
			orderNos = append(orderNos, orderNo)
		}

		// 验证序列号递增
		for i := 1; i < len(orderNos); i++ {
			prev := orderNos[i-1]
			curr := orderNos[i]

			// 提取序列号
			prevSeq := prev[10:]
			currSeq := curr[10:]

			assert.True(t, currSeq > prevSeq, "序列号应该递增: %s -> %s", prevSeq, currSeq)
		}

		t.Logf("生成的订单号序列: %v", orderNos)
	})

	t.Run("并发生成测试", func(t *testing.T) {
		const goroutines = 10
		const ordersPerGoroutine = 10

		var wg sync.WaitGroup
		var mu sync.Mutex
		orderNos := make([]string, 0, goroutines*ordersPerGoroutine)
		errors := make([]error, 0)

		for i := 0; i < goroutines; i++ {
			wg.Add(1)
			go func() {
				defer wg.Done()
				for j := 0; j < ordersPerGoroutine; j++ {
					orderNo, err := generator.GeneratePlatformOrderNo(ctx)

					mu.Lock()
					if err != nil {
						errors = append(errors, err)
					} else {
						orderNos = append(orderNos, orderNo)
					}
					mu.Unlock()
				}
			}()
		}

		wg.Wait()

		// 验证没有错误
		assert.Empty(t, errors, "并发生成不应该有错误")

		// 验证生成的订单号数量
		assert.Len(t, orderNos, goroutines*ordersPerGoroutine)

		// 验证订单号唯一性
		uniqueOrderNos := make(map[string]bool)
		for _, orderNo := range orderNos {
			assert.False(t, uniqueOrderNos[orderNo], "订单号应该唯一: %s", orderNo)
			uniqueOrderNos[orderNo] = true
		}

		t.Logf("并发生成 %d 个唯一订单号", len(uniqueOrderNos))
	})

	t.Run("性能测试", func(t *testing.T) {
		// 测试生成器性能
		start := time.Now()
		var orderNos []string
		for i := 0; i < 10; i++ {
			orderNo, err := generator.GeneratePlatformOrderNo(ctx)
			require.NoError(t, err)
			orderNos = append(orderNos, orderNo)
		}
		duration := time.Since(start)

		// 验证所有订单号都是唯一的
		uniqueOrderNos := make(map[string]bool)
		for _, orderNo := range orderNos {
			assert.False(t, uniqueOrderNos[orderNo], "订单号应该唯一: %s", orderNo)
			uniqueOrderNos[orderNo] = true
		}

		t.Logf("生成10个订单号耗时: %v, 平均每个: %v", duration, duration/10)
		t.Logf("生成的订单号: %v", orderNos)
	})
}

// TestPlatformOrderGenerator_ValidatePlatformOrderNo 测试订单号验证
func TestPlatformOrderGenerator_ValidatePlatformOrderNo(t *testing.T) {
	logger := zap.NewNop()
	config := &PlatformOrderConfig{Prefix: "GK"}
	generator := NewPlatformOrderGenerator(nil, logger, config)

	testCases := []struct {
		name     string
		orderNo  string
		expected bool
	}{
		{
			name:     "有效订单号",
			orderNo:  "GK20250711000000001",
			expected: true,
		},
		{
			name:     "有效订单号-最大序列号",
			orderNo:  "GK20250711999999999",
			expected: true,
		},
		{
			name:     "无效前缀",
			orderNo:  "XX20250711000000001",
			expected: false,
		},
		{
			name:     "无效日期",
			orderNo:  "GK20251301000000001", // 13月
			expected: false,
		},
		{
			name:     "序列号为0",
			orderNo:  "GK20250711000000000",
			expected: false,
		},
		{
			name:     "长度不正确",
			orderNo:  "GK2025071100000001", // 少一位
			expected: false,
		},
		{
			name:     "包含非数字字符",
			orderNo:  "GK2025071100000000A",
			expected: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := generator.ValidatePlatformOrderNo(tc.orderNo)
			assert.Equal(t, tc.expected, result, "订单号: %s", tc.orderNo)
		})
	}
}

// TestPlatformOrderGenerator_GetStats 测试统计信息
func TestPlatformOrderGenerator_GetStats(t *testing.T) {
	logger := zap.NewNop()
	config := &PlatformOrderConfig{
		Prefix:           "GK",
		CacheSize:        100,
		RetryAttempts:    3,
		RetryDelay:       100 * time.Millisecond,
		EnableMetrics:    false,
		EnableLocalCache: true,
	}

	generator := NewPlatformOrderGenerator(nil, logger, config)
	stats := generator.GetStats()

	// 验证配置信息
	configStats := stats["config"].(map[string]interface{})
	assert.Equal(t, "GK", configStats["prefix"])
	assert.Equal(t, int64(100), configStats["cache_size"])
	assert.Equal(t, 3, configStats["retry_attempts"])
	assert.Equal(t, int64(100), configStats["retry_delay_ms"])
	assert.Equal(t, false, configStats["enable_metrics"])
	assert.Equal(t, true, configStats["enable_local_cache"])

	// 验证缓存信息
	cacheStats := stats["cache"].(map[string]interface{})
	assert.Equal(t, "", cacheStats["date_key"])
	assert.Equal(t, int64(0), cacheStats["sequence"])
	assert.Equal(t, int64(0), cacheStats["max_cache"])
	assert.Equal(t, int64(0), cacheStats["remaining"])

	t.Logf("生成器统计信息: %+v", stats)
}

// BenchmarkPlatformOrderGenerator_GeneratePlatformOrderNo 性能基准测试
func BenchmarkPlatformOrderGenerator_GeneratePlatformOrderNo(b *testing.B) {
	// 创建测试数据库连接
	db := setupTestDB(b)
	defer db.Close()

	logger := zap.NewNop()
	config := &PlatformOrderConfig{
		Prefix:           "GK",
		CacheSize:        1000,
		RetryAttempts:    3,
		RetryDelay:       50 * time.Millisecond,
		EnableMetrics:    false,
		EnableLocalCache: true,
	}

	generator := NewPlatformOrderGenerator(db, logger, config)
	ctx := context.Background()

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			_, err := generator.GeneratePlatformOrderNo(ctx)
			if err != nil {
				b.Fatalf("生成订单号失败: %v", err)
			}
		}
	})
}

// setupTestDB 设置测试数据库
func setupTestDB(t testing.TB) *sql.DB {
	// 使用实际数据库连接进行测试
	connectionString := "*************************************************/go_kuaidi?sslmode=disable"

	db, err := sql.Open("postgres", connectionString)
	if err != nil {
		t.Fatalf("连接测试数据库失败: %v", err)
	}

	// 配置连接池
	db.SetMaxOpenConns(10)
	db.SetMaxIdleConns(5)
	db.SetConnMaxLifetime(5 * time.Minute)

	// 验证数据库连接
	if err := db.Ping(); err != nil {
		t.Fatalf("数据库连接测试失败: %v", err)
	}

	return db
}

// TestPlatformOrderGenerator_EdgeCases 边界情况测试
func TestPlatformOrderGenerator_EdgeCases(t *testing.T) {
	db := setupTestDB(t)
	defer db.Close()

	logger := zap.NewNop()
	config := &PlatformOrderConfig{
		Prefix:           "GK",
		CacheSize:        2,
		RetryAttempts:    3,
		RetryDelay:       10 * time.Millisecond,
		EnableMetrics:    false,
		EnableLocalCache: true,
	}

	generator := NewPlatformOrderGenerator(db, logger, config)
	ctx := context.Background()

	t.Run("上下文取消", func(t *testing.T) {
		cancelCtx, cancel := context.WithCancel(ctx)
		cancel() // 立即取消

		_, err := generator.GeneratePlatformOrderNo(cancelCtx)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "context canceled")
	})

	t.Run("超时上下文", func(t *testing.T) {
		timeoutCtx, cancel := context.WithTimeout(ctx, 1*time.Nanosecond)
		defer cancel()

		time.Sleep(10 * time.Millisecond) // 确保超时

		_, err := generator.GeneratePlatformOrderNo(timeoutCtx)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "context deadline exceeded")
	})

	t.Run("日期变更缓存清理", func(t *testing.T) {
		// 生成一个订单号
		orderNo1, err := generator.GeneratePlatformOrderNo(ctx)
		require.NoError(t, err)

		// 手动修改缓存日期（模拟日期变更）
		generator.cache.mutex.Lock()
		generator.cache.dateKey = "20250710" // 设置为昨天
		generator.cache.mutex.Unlock()

		// 再次生成，应该清理缓存
		orderNo2, err := generator.GeneratePlatformOrderNo(ctx)
		require.NoError(t, err)

		// 验证两个订单号的日期部分
		date1 := orderNo1[2:10]
		date2 := orderNo2[2:10]

		// 第二个订单号应该使用当前日期
		currentDate := util.NowBeijing().Format("20060102")
		assert.Equal(t, currentDate, date2)

		t.Logf("日期变更测试: %s -> %s", date1, date2)
	})
}
