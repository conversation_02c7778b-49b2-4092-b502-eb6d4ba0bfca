package service

import (
	"context"
	"database/sql"
	"fmt"
	"sync"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"go.uber.org/zap"

	"github.com/your-org/go-kuaidi/internal/util"
)

// PlatformOrderGenerator 平台订单号生成器
// 🔥 企业级设计：高性能、高并发、高可靠性
type PlatformOrderGenerator struct {
	db      *sql.DB
	logger  *zap.Logger
	metrics *PlatformOrderMetrics

	// 性能优化：本地缓存
	cache struct {
		dateKey  string
		sequence int64
		maxCache int64 // 本地缓存的最大序列号
		mutex    sync.Mutex
	}

	// 配置参数
	config PlatformOrderConfig
}

// PlatformOrderConfig 平台订单号生成器配置
type PlatformOrderConfig struct {
	Prefix           string        // 订单号前缀，默认"GK"
	CacheSize        int64         // 本地缓存大小，默认100
	RetryAttempts    int           // 重试次数，默认3
	RetryDelay       time.Duration // 重试延迟，默认100ms
	EnableMetrics    bool          // 是否启用监控指标
	EnableLocalCache bool          // 是否启用本地缓存
}

// PlatformOrderMetrics 平台订单号监控指标
type PlatformOrderMetrics struct {
	GenerationTotal    prometheus.Counter   // 总生成次数
	GenerationLatency  prometheus.Histogram // 生成延迟
	GenerationErrors   prometheus.Counter   // 生成错误次数
	CacheHits          prometheus.Counter   // 缓存命中次数
	CacheMisses        prometheus.Counter   // 缓存未命中次数
	SequenceConflicts  prometheus.Counter   // 序列号冲突次数
	DatabaseOperations prometheus.Counter   // 数据库操作次数
}

// NewPlatformOrderGenerator 创建平台订单号生成器
func NewPlatformOrderGenerator(db *sql.DB, logger *zap.Logger, config *PlatformOrderConfig) *PlatformOrderGenerator {
	// 设置默认配置
	if config == nil {
		config = &PlatformOrderConfig{
			Prefix:           "GK",
			CacheSize:        100,
			RetryAttempts:    3,
			RetryDelay:       100 * time.Millisecond,
			EnableMetrics:    true,
			EnableLocalCache: true,
		}
	}

	generator := &PlatformOrderGenerator{
		db:     db,
		logger: logger,
		config: *config,
	}

	// 初始化监控指标
	if config.EnableMetrics {
		generator.initMetrics()
	}

	// 初始化本地缓存
	if config.EnableLocalCache {
		generator.cache.maxCache = 0
	}

	return generator
}

// initMetrics 初始化监控指标
func (g *PlatformOrderGenerator) initMetrics() {
	g.metrics = &PlatformOrderMetrics{
		GenerationTotal: prometheus.NewCounter(prometheus.CounterOpts{
			Name: "platform_order_generation_total",
			Help: "Total number of platform order numbers generated",
		}),
		GenerationLatency: prometheus.NewHistogram(prometheus.HistogramOpts{
			Name:    "platform_order_generation_duration_seconds",
			Help:    "Platform order number generation latency",
			Buckets: prometheus.DefBuckets,
		}),
		GenerationErrors: prometheus.NewCounter(prometheus.CounterOpts{
			Name: "platform_order_generation_errors_total",
			Help: "Total number of platform order generation errors",
		}),
		CacheHits: prometheus.NewCounter(prometheus.CounterOpts{
			Name: "platform_order_cache_hits_total",
			Help: "Total number of cache hits",
		}),
		CacheMisses: prometheus.NewCounter(prometheus.CounterOpts{
			Name: "platform_order_cache_misses_total",
			Help: "Total number of cache misses",
		}),
		SequenceConflicts: prometheus.NewCounter(prometheus.CounterOpts{
			Name: "platform_order_sequence_conflicts_total",
			Help: "Total number of sequence conflicts",
		}),
		DatabaseOperations: prometheus.NewCounter(prometheus.CounterOpts{
			Name: "platform_order_database_operations_total",
			Help: "Total number of database operations",
		}),
	}

	// 注册指标（忽略重复注册错误）
	metrics := []prometheus.Collector{
		g.metrics.GenerationTotal,
		g.metrics.GenerationLatency,
		g.metrics.GenerationErrors,
		g.metrics.CacheHits,
		g.metrics.CacheMisses,
		g.metrics.SequenceConflicts,
		g.metrics.DatabaseOperations,
	}

	for _, metric := range metrics {
		if err := prometheus.Register(metric); err != nil {
			// 如果是重复注册错误，忽略它
			if _, ok := err.(prometheus.AlreadyRegisteredError); !ok {
				// 如果不是重复注册错误，记录警告但不中断程序
				g.logger.Warn("注册Prometheus指标失败", zap.Error(err))
			}
		}
	}
}

// GeneratePlatformOrderNo 生成平台订单号
// 🔥 核心方法：高性能、高并发、高可靠性
func (g *PlatformOrderGenerator) GeneratePlatformOrderNo(ctx context.Context) (string, error) {
	startTime := time.Now()

	// 更新监控指标
	if g.metrics != nil {
		g.metrics.GenerationTotal.Inc()
		defer func() {
			g.metrics.GenerationLatency.Observe(time.Since(startTime).Seconds())
		}()
	}

	// 获取北京时间日期
	beijingTime := util.NowBeijing()
	dateKey := beijingTime.Format("20060102")

	// 🔥 暂时禁用缓存以确保并发安全
	// TODO: 实现更安全的缓存机制
	// if g.config.EnableLocalCache {
	// 	if sequence, ok := g.tryGetFromCache(dateKey); ok {
	// 		platformOrderNo := fmt.Sprintf("%s%s%09d", g.config.Prefix, dateKey, sequence)
	//
	// 		g.logger.Debug("从缓存生成平台订单号",
	// 			zap.String("platform_order_no", platformOrderNo),
	// 			zap.String("date_key", dateKey),
	// 			zap.Int64("sequence", sequence),
	// 			zap.Duration("latency", time.Since(startTime)))
	//
	// 		return platformOrderNo, nil
	// 	}
	// }

	// 从数据库获取序列号（带重试机制）
	sequence, err := g.getSequenceWithRetry(ctx, dateKey)
	if err != nil {
		if g.metrics != nil {
			g.metrics.GenerationErrors.Inc()
		}
		return "", fmt.Errorf("获取序列号失败: %w", err)
	}

	// 生成平台订单号
	platformOrderNo := fmt.Sprintf("%s%s%09d", g.config.Prefix, dateKey, sequence)

	g.logger.Info("生成平台订单号",
		zap.String("platform_order_no", platformOrderNo),
		zap.String("date_key", dateKey),
		zap.Int64("sequence", sequence),
		zap.Duration("latency", time.Since(startTime)))

	return platformOrderNo, nil
}

// getSequenceWithRetry 带重试机制获取序列号
func (g *PlatformOrderGenerator) getSequenceWithRetry(ctx context.Context, dateKey string) (int64, error) {
	var lastErr error

	for attempt := 0; attempt < g.config.RetryAttempts; attempt++ {
		sequence, err := g.getSequenceFromDB(ctx, dateKey)
		if err == nil {
			// 成功获取序列号
			// 🔥 暂时禁用缓存更新
			// if g.config.EnableLocalCache {
			// 	g.updateCache(dateKey, sequence)
			// }
			return sequence, nil
		}

		lastErr = err

		// 记录重试
		g.logger.Warn("获取序列号失败，准备重试",
			zap.String("date_key", dateKey),
			zap.Int("attempt", attempt+1),
			zap.Int("max_attempts", g.config.RetryAttempts),
			zap.Error(err))

		if g.metrics != nil {
			g.metrics.SequenceConflicts.Inc()
		}

		// 等待后重试
		if attempt < g.config.RetryAttempts-1 {
			select {
			case <-ctx.Done():
				return 0, ctx.Err()
			case <-time.After(g.config.RetryDelay):
				// 继续重试
			}
		}
	}

	return 0, fmt.Errorf("重试%d次后仍然失败: %w", g.config.RetryAttempts, lastErr)
}

// getSequenceFromDB 从数据库获取序列号
func (g *PlatformOrderGenerator) getSequenceFromDB(ctx context.Context, dateKey string) (int64, error) {
	if g.metrics != nil {
		g.metrics.DatabaseOperations.Inc()
	}

	// 直接从数据库获取序列号，而不是生成完整订单号
	query := `
		INSERT INTO platform_order_sequences (date_key, current_sequence, updated_at)
		VALUES ($1, 1, CURRENT_TIMESTAMP)
		ON CONFLICT (date_key)
		DO UPDATE SET
			current_sequence = platform_order_sequences.current_sequence + 1,
			updated_at = CURRENT_TIMESTAMP
		WHERE platform_order_sequences.current_sequence < platform_order_sequences.max_sequence
		RETURNING current_sequence
	`

	var sequence int64
	err := g.db.QueryRowContext(ctx, query, dateKey).Scan(&sequence)
	if err != nil {
		return 0, fmt.Errorf("获取序列号失败: %w", err)
	}

	return sequence, nil
}

// GetStats 获取生成器统计信息
func (g *PlatformOrderGenerator) GetStats() map[string]interface{} {
	g.cache.mutex.Lock()
	defer g.cache.mutex.Unlock()

	stats := map[string]interface{}{
		"config": map[string]interface{}{
			"prefix":             g.config.Prefix,
			"cache_size":         g.config.CacheSize,
			"retry_attempts":     g.config.RetryAttempts,
			"retry_delay_ms":     g.config.RetryDelay.Milliseconds(),
			"enable_metrics":     g.config.EnableMetrics,
			"enable_local_cache": g.config.EnableLocalCache,
		},
		"cache": map[string]interface{}{
			"date_key":  g.cache.dateKey,
			"sequence":  g.cache.sequence,
			"max_cache": g.cache.maxCache,
			"remaining": g.cache.maxCache - g.cache.sequence,
		},
	}

	return stats
}

// ValidatePlatformOrderNo 验证平台订单号格式
func (g *PlatformOrderGenerator) ValidatePlatformOrderNo(orderNo string) bool {
	// 检查长度：前缀(2) + 日期(8) + 序列号(9) = 19
	expectedLength := len(g.config.Prefix) + 8 + 9
	if len(orderNo) != expectedLength {
		return false
	}

	// 检查前缀
	if orderNo[:len(g.config.Prefix)] != g.config.Prefix {
		return false
	}

	// 检查日期部分（8位数字）
	dateStr := orderNo[len(g.config.Prefix) : len(g.config.Prefix)+8]
	if _, err := time.Parse("20060102", dateStr); err != nil {
		return false
	}

	// 检查序列号部分（9位数字）
	sequenceStr := orderNo[len(g.config.Prefix)+8:]
	var sequence int64
	if _, err := fmt.Sscanf(sequenceStr, "%09d", &sequence); err != nil {
		return false
	}

	return sequence > 0 && sequence <= 999999999
}
