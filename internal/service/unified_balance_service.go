package service

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"

	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/repository"
)

// UnifiedBalanceService 统一余额服务 - 简化对外接口，隐藏内部复杂性
type UnifiedBalanceService struct {
	// 核心服务层
	coreService      *DefaultBalanceService
	optimizedService *OptimizedBalanceService

	// 高性能引擎（按需启用）
	enhancedEngine *EnhancedBalanceService

	// 配置和状态
	config      *UnifiedBalanceConfig
	logger      *zap.Logger
	initialized bool
}

// UnifiedBalanceConfig 统一配置
type UnifiedBalanceConfig struct {
	// 性能模式选择
	PerformanceMode PerformanceMode `json:"performance_mode"` // simple, optimized, enhanced

	// 高性能功能开关
	EnableAsync      bool `json:"enable_async"`      // 启用异步处理
	EnableSharding   bool `json:"enable_sharding"`   // 启用分片
	EnableLocking    bool `json:"enable_locking"`    // 启用分布式锁
	EnableMonitoring bool `json:"enable_monitoring"` // 启用监控

	// 简化的性能参数
	MaxConcurrency int `json:"max_concurrency"` // 最大并发数
	QueueSize      int `json:"queue_size"`      // 队列大小
	WorkerCount    int `json:"worker_count"`    // 工作协程数

	// 自动降级
	AutoDegradation bool `json:"auto_degradation"` // 自动性能降级
}

// PerformanceMode 性能模式
type PerformanceMode string

const (
	// ModeSimple 简单模式 - 单体服务，低并发（<100 TPS）
	ModeSimple PerformanceMode = "simple"

	// ModeOptimized 优化模式 - 缓存优化，中等并发（100-1000 TPS）
	ModeOptimized PerformanceMode = "optimized"

	// ModeEnhanced 增强模式 - 完整高并发方案（1000+ TPS）
	ModeEnhanced PerformanceMode = "enhanced"
)

// NewUnifiedBalanceService 创建统一余额服务
func NewUnifiedBalanceService(
	balanceRepo repository.BalanceRepository,
	orderRepo repository.OrderRepository,
	db *sql.DB,
	redisClient *redis.Client,
	config *UnifiedBalanceConfig,
	logger *zap.Logger,
) *UnifiedBalanceService {
	service := &UnifiedBalanceService{
		config: config,
		logger: logger,
	}

	// 1. 始终创建核心服务（基础功能）
	service.coreService = NewBalanceService(balanceRepo, orderRepo, db, logger).(*DefaultBalanceService)

	// 2. 根据性能模式选择合适的实现
	switch config.PerformanceMode {
	case ModeSimple:
		service.initializeSimpleMode()

	case ModeOptimized:
		service.initializeOptimizedMode()

	case ModeEnhanced:
		service.initializeEnhancedMode(redisClient, map[int]*repository.BalanceRepository{})

	default:
		// 默认使用简单模式
		config.PerformanceMode = ModeSimple
		service.initializeSimpleMode()
	}

	service.initialized = true

	logger.Info("✅ 统一余额服务初始化完成",
		zap.String("mode", string(config.PerformanceMode)),
		zap.Bool("async", config.EnableAsync),
		zap.Bool("sharding", config.EnableSharding),
		zap.Bool("locking", config.EnableLocking),
		zap.Bool("monitoring", config.EnableMonitoring))

	return service
}

// initializeSimpleMode 初始化简单模式
func (s *UnifiedBalanceService) initializeSimpleMode() {
	s.logger.Info("🎯 启用简单模式 - 适合低并发场景（<100 TPS）")
	// 只使用核心服务，无额外组件
}

// initializeOptimizedMode 初始化优化模式
func (s *UnifiedBalanceService) initializeOptimizedMode() {
	s.logger.Info("🚀 启用优化模式 - 适合中等并发场景（100-1000 TPS）")

	// 创建优化版服务
	s.optimizedService = NewOptimizedBalanceService(s.coreService, s.logger)
}

// initializeEnhancedMode 初始化增强模式
func (s *UnifiedBalanceService) initializeEnhancedMode(redisClient *redis.Client, repositories map[int]*repository.BalanceRepository) {
	s.logger.Info("⚡ 启用增强模式 - 适合高并发场景（1000+ TPS）")

	// 创建增强版配置（禁用分片功能，使用单库高并发方案）
	enhancedConfig := &EnhancedBalanceConfig{
		Mode:                      ModeHybrid,
		EnableAsyncProcessing:     s.config.EnableAsync,
		EnableSharding:            false, // 明确禁用分片功能
		EnableDistributedLock:     s.config.EnableLocking,
		EnableMonitoring:          s.config.EnableMonitoring,
		MaxConcurrency:            s.config.MaxConcurrency,
		QueueSize:                 s.config.QueueSize,
		WorkerCount:               s.config.WorkerCount,
		BatchSize:                 100,
		FlushInterval:             50 * time.Millisecond,
		MaxRetries:                5,
		BaseRetryDelay:            50 * time.Millisecond,
		MaxRetryDelay:             1 * time.Second,
		EnableExponentialBackoff:  true,
		ShardCount:                1, // 单分片
		ConsistencyMode:           EventualConsistency,
		EnableGracefulDegradation: s.config.AutoDegradation,
		FallbackToSync:            true,
		EmergencyModeThreshold:    0.20,
	}

	s.logger.Info("📝 增强模式配置",
		zap.Bool("async_processing", enhancedConfig.EnableAsyncProcessing),
		zap.Bool("sharding", enhancedConfig.EnableSharding),
		zap.Bool("distributed_lock", enhancedConfig.EnableDistributedLock),
		zap.Bool("monitoring", enhancedConfig.EnableMonitoring),
		zap.Int("worker_count", enhancedConfig.WorkerCount),
		zap.Int("max_concurrency", enhancedConfig.MaxConcurrency))

	// 创建增强版服务（传递空的分片映射）
	s.enhancedEngine = NewEnhancedBalanceService(
		s.coreService,
		redisClient,
		map[int]*repository.BalanceRepository{}, // 空分片映射
		enhancedConfig,
		s.logger,
	)
}

// ===========================================
// 统一对外接口 - 隐藏内部复杂性
// ===========================================

// PreChargeForOrder 订单预扣费 - 统一入口
func (s *UnifiedBalanceService) PreChargeForOrder(ctx context.Context, userID, orderNo string, amount decimal.Decimal) error {
	s.logger.Debug("💰 处理订单预扣费",
		zap.String("user_id", userID),
		zap.String("order_no", orderNo),
		zap.String("amount", amount.String()),
		zap.String("mode", string(s.config.PerformanceMode)))

	// 根据性能模式选择合适的实现
	switch s.config.PerformanceMode {
	case ModeEnhanced:
		if s.enhancedEngine != nil {
			return s.enhancedEngine.PreChargeForOrder(ctx, userID, orderNo, amount)
		}
		fallthrough

	case ModeOptimized:
		if s.optimizedService != nil {
			// 优化版服务使用基础接口
			return s.coreService.PreChargeForOrder(ctx, userID, orderNo, amount)
		}
		fallthrough

	case ModeSimple:
		return s.coreService.PreChargeForOrder(ctx, userID, orderNo, amount)

	default:
		return s.coreService.PreChargeForOrder(ctx, userID, orderNo, amount)
	}
}

// PreChargeForOrderWithDetails 订单预扣费（优化版）- 统一入口
func (s *UnifiedBalanceService) PreChargeForOrderWithDetails(ctx context.Context, userID, customerOrderNo, platformOrderNo string, amount decimal.Decimal) error {
	s.logger.Debug("💰 处理订单预扣费（优化版）",
		zap.String("user_id", userID),
		zap.String("customer_order_no", customerOrderNo),
		zap.String("platform_order_no", platformOrderNo),
		zap.String("amount", amount.String()),
		zap.String("mode", string(s.config.PerformanceMode)))

	// 根据性能模式选择合适的实现
	switch s.config.PerformanceMode {
	case ModeEnhanced:
		if s.enhancedEngine != nil {
			return s.enhancedEngine.PreChargeForOrderWithDetails(ctx, userID, customerOrderNo, platformOrderNo, amount)
		}
		fallthrough

	case ModeOptimized:
		if s.optimizedService != nil {
			// 优化版服务使用基础接口
			return s.coreService.PreChargeForOrderWithDetails(ctx, userID, customerOrderNo, platformOrderNo, amount)
		}
		fallthrough

	case ModeSimple:
		return s.coreService.PreChargeForOrderWithDetails(ctx, userID, customerOrderNo, platformOrderNo, amount)

	default:
		return s.coreService.PreChargeForOrderWithDetails(ctx, userID, customerOrderNo, platformOrderNo, amount)
	}
}

// RefundForOrder 订单退款 - 统一入口
func (s *UnifiedBalanceService) RefundForOrder(ctx context.Context, userID, orderNo string, amount decimal.Decimal) error {
	s.logger.Debug("💰 处理订单退款",
		zap.String("user_id", userID),
		zap.String("order_no", orderNo),
		zap.String("amount", amount.String()),
		zap.String("mode", string(s.config.PerformanceMode)))

	// 根据性能模式选择合适的实现
	switch s.config.PerformanceMode {
	case ModeEnhanced:
		if s.enhancedEngine != nil {
			return s.enhancedEngine.RefundForOrder(ctx, userID, orderNo, amount)
		}
		fallthrough

	case ModeOptimized:
		if s.optimizedService != nil {
			// 优化版服务使用基础接口
			return s.coreService.RefundForOrder(ctx, userID, orderNo, amount)
		}
		fallthrough

	case ModeSimple:
		return s.coreService.RefundForOrder(ctx, userID, orderNo, amount)

	default:
		return s.coreService.RefundForOrder(ctx, userID, orderNo, amount)
	}
}

// GetBalance 获取用户余额 - 统一入口
func (s *UnifiedBalanceService) GetBalance(ctx context.Context, userID string) (*model.BalanceResponse, error) {
	// 所有模式都使用相同的查询逻辑
	switch s.config.PerformanceMode {
	case ModeEnhanced:
		if s.enhancedEngine != nil {
			return s.enhancedEngine.GetBalance(ctx, userID)
		}
		fallthrough

	case ModeOptimized:
		if s.optimizedService != nil {
			// 使用优化版的缓存查询
			return s.coreService.GetBalance(ctx, userID)
		}
		fallthrough

	case ModeSimple:
		return s.coreService.GetBalance(ctx, userID)

	default:
		return s.coreService.GetBalance(ctx, userID)
	}
}

// ===========================================
// 委托其他接口方法
// ===========================================

// Deposit 充值
func (s *UnifiedBalanceService) Deposit(ctx context.Context, req *model.DepositRequest) (*model.TransactionResponse, error) {
	return s.coreService.Deposit(ctx, req)
}

// Payment 支付
func (s *UnifiedBalanceService) Payment(ctx context.Context, req *model.PaymentRequest) (*model.TransactionResponse, error) {
	return s.coreService.Payment(ctx, req)
}

// Refund 退款
func (s *UnifiedBalanceService) Refund(ctx context.Context, req *model.BalanceRequest) (*model.TransactionResponse, error) {
	return s.coreService.Refund(ctx, req)
}

// Transfer 转账
func (s *UnifiedBalanceService) Transfer(ctx context.Context, req *model.TransferRequest) (*model.TransactionResponse, error) {
	return s.coreService.Transfer(ctx, req)
}

// GetTransactionHistory 获取交易历史
func (s *UnifiedBalanceService) GetTransactionHistory(ctx context.Context, userID string, limit, offset int) ([]*model.TransactionResponse, error) {
	return s.coreService.GetTransactionHistory(ctx, userID, limit, offset)
}

// GetTransactionHistoryWithCount 获取交易历史（带总数）
func (s *UnifiedBalanceService) GetTransactionHistoryWithCount(ctx context.Context, userID string, limit, offset int) ([]*model.TransactionResponse, int64, error) {
	return s.coreService.GetTransactionHistoryWithCount(ctx, userID, limit, offset)
}

// GetTransactionHistoryWithFilters 获取交易历史（带过滤器）
func (s *UnifiedBalanceService) GetTransactionHistoryWithFilters(ctx context.Context, userID string, limit, offset int, typeFilter, statusFilter, startTime, endTime, customerOrderNo, orderNo, trackingNo string) ([]*model.TransactionResponse, int64, error) {
	return s.coreService.GetTransactionHistoryWithFilters(ctx, userID, limit, offset, typeFilter, statusFilter, startTime, endTime, customerOrderNo, orderNo, trackingNo)
}

// GetTransactionByID 根据ID获取交易
func (s *UnifiedBalanceService) GetTransactionByID(ctx context.Context, id string) (*model.TransactionResponse, error) {
	return s.coreService.GetTransactionByID(ctx, id)
}

// GetTransactionByOrderNo 根据订单号获取交易
func (s *UnifiedBalanceService) GetTransactionByOrderNo(ctx context.Context, orderNo string) (*model.TransactionResponse, error) {
	return s.coreService.GetTransactionByOrderNo(ctx, orderNo)
}

// GetDepositHistory 获取充值历史
func (s *UnifiedBalanceService) GetDepositHistory(ctx context.Context, userID string, limit, offset int) ([]*model.Deposit, error) {
	return s.coreService.GetDepositHistory(ctx, userID, limit, offset)
}

// GetPaymentHistory 获取支付历史
func (s *UnifiedBalanceService) GetPaymentHistory(ctx context.Context, userID string, limit, offset int) ([]*model.OrderPayment, error) {
	return s.coreService.GetPaymentHistory(ctx, userID, limit, offset)
}

// ChargeForBillingDifference 计费差额扣费
func (s *UnifiedBalanceService) ChargeForBillingDifference(ctx context.Context, userID, orderNo string, amount decimal.Decimal) error {
	return s.coreService.ChargeForBillingDifference(ctx, userID, orderNo, amount)
}

// RefundForBillingDifference 计费差额退款
func (s *UnifiedBalanceService) RefundForBillingDifference(ctx context.Context, userID, orderNo string, amount decimal.Decimal) error {
	return s.coreService.RefundForBillingDifference(ctx, userID, orderNo, amount)
}

// GetOrderNetPayment 获取订单净支付
func (s *UnifiedBalanceService) GetOrderNetPayment(ctx context.Context, userID, orderNo, customerOrderNo string) (decimal.Decimal, error) {
	return s.coreService.GetOrderNetPayment(ctx, userID, orderNo, customerOrderNo)
}

// CheckBalanceForOrder 检查用户余额是否足够支付订单 - 🔥 新增：修复余额不足却创建订单成功的BUG
func (s *UnifiedBalanceService) CheckBalanceForOrder(ctx context.Context, userID string, amount decimal.Decimal) (*model.BalanceCheckResult, error) {
	return s.coreService.CheckBalanceForOrder(ctx, userID, amount)
}

// CheckAndReserveBalance 检查并预留余额 - 🔥 新增：原子化余额检查和预留
func (s *UnifiedBalanceService) CheckAndReserveBalance(ctx context.Context, userID string, amount decimal.Decimal, reservationID string) error {
	return s.coreService.CheckAndReserveBalance(ctx, userID, amount, reservationID)
}

// ReleaseReservedBalance 释放预留余额 - 🔥 新增：释放预留的余额
func (s *UnifiedBalanceService) ReleaseReservedBalance(ctx context.Context, userID string, reservationID string) error {
	return s.coreService.ReleaseReservedBalance(ctx, userID, reservationID)
}

// CreateBalanceIfNotExists 创建余额账户（如果不存在）
func (s *UnifiedBalanceService) CreateBalanceIfNotExists(ctx context.Context, userID string) error {
	return s.coreService.CreateBalanceIfNotExists(ctx, userID)
}

// ProcessPaymentCallback 处理支付回调
func (s *UnifiedBalanceService) ProcessPaymentCallback(ctx context.Context, depositID string, success bool, transactionID string) error {
	return s.coreService.ProcessPaymentCallback(ctx, depositID, success, transactionID)
}

// GetBalanceHistory 获取余额历史 - 实现BalanceServiceInterface接口
func (s *UnifiedBalanceService) GetBalanceHistory(ctx context.Context, userID string, limit, offset int) ([]*model.TransactionResponse, error) {
	return s.coreService.GetTransactionHistory(ctx, userID, limit, offset)
}

// ===========================================
// 管理和监控接口
// ===========================================

// GetServiceStats 获取服务统计信息
func (s *UnifiedBalanceService) GetServiceStats(ctx context.Context) (map[string]interface{}, error) {
	stats := map[string]interface{}{
		"mode":        s.config.PerformanceMode,
		"config":      s.config,
		"timestamp":   time.Now(),
		"initialized": s.initialized,
	}

	// 根据模式获取对应的统计信息
	switch s.config.PerformanceMode {
	case ModeEnhanced:
		if s.enhancedEngine != nil {
			enhancedStats, err := s.enhancedEngine.GetServiceStats(ctx)
			if err == nil {
				stats["enhanced"] = enhancedStats
			}
		}
	case ModeOptimized:
		if s.optimizedService != nil {
			stats["optimized"] = map[string]interface{}{
				"cache_enabled": true,
			}
		}
	}

	return stats, nil
}

// HealthCheck 健康检查
func (s *UnifiedBalanceService) HealthCheck(ctx context.Context) error {
	if !s.initialized {
		return fmt.Errorf("服务未初始化")
	}

	// 检查基础服务
	if s.coreService == nil {
		return fmt.Errorf("核心服务未初始化")
	}

	// 根据模式检查对应的服务
	switch s.config.PerformanceMode {
	case ModeEnhanced:
		if s.enhancedEngine != nil {
			return s.enhancedEngine.HealthCheck(ctx)
		}
	case ModeOptimized:
		if s.optimizedService == nil {
			return fmt.Errorf("优化服务未初始化")
		}
	}

	return nil
}

// SwitchPerformanceMode 切换性能模式（运行时）
func (s *UnifiedBalanceService) SwitchPerformanceMode(newMode PerformanceMode) error {
	if newMode == s.config.PerformanceMode {
		return nil // 相同模式，无需切换
	}

	oldMode := s.config.PerformanceMode
	s.config.PerformanceMode = newMode

	s.logger.Info("🔄 切换余额服务性能模式",
		zap.String("from", string(oldMode)),
		zap.String("to", string(newMode)))

	// 注意：这里可以添加模式切换的逻辑
	// 比如停止旧服务、启动新服务等

	return nil
}

// Stop 停止服务
func (s *UnifiedBalanceService) Stop() {
	s.logger.Info("⏹️ 停止统一余额服务")

	// 停止增强引擎
	if s.enhancedEngine != nil {
		s.enhancedEngine.Stop()
	}

	s.initialized = false
}

// ===========================================
// 便捷的配置创建函数
// ===========================================

// GetSimpleConfig 获取简单模式配置
func GetSimpleConfig() *UnifiedBalanceConfig {
	return &UnifiedBalanceConfig{
		PerformanceMode:  ModeSimple,
		EnableAsync:      false,
		EnableSharding:   false,
		EnableLocking:    false,
		EnableMonitoring: false,
		MaxConcurrency:   10,
		QueueSize:        100,
		WorkerCount:      1,
		AutoDegradation:  false,
	}
}

// GetOptimizedConfig 获取优化模式配置
func GetOptimizedConfig() *UnifiedBalanceConfig {
	return &UnifiedBalanceConfig{
		PerformanceMode:  ModeOptimized,
		EnableAsync:      false,
		EnableSharding:   false,
		EnableLocking:    false,
		EnableMonitoring: true,
		MaxConcurrency:   100,
		QueueSize:        1000,
		WorkerCount:      5,
		AutoDegradation:  true,
	}
}

// GetEnhancedConfig 获取增强模式配置
func GetEnhancedConfig() *UnifiedBalanceConfig {
	return &UnifiedBalanceConfig{
		PerformanceMode:  ModeEnhanced,
		EnableAsync:      true,
		EnableSharding:   false, // 禁用分片功能，使用单库高并发方案
		EnableLocking:    true,
		EnableMonitoring: true,
		MaxConcurrency:   1000,
		QueueSize:        10000,
		WorkerCount:      20,
		AutoDegradation:  true,
	}
}
