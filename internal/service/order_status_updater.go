package service

import (
	"context"
	"fmt"

	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/repository"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// OrderStatusUpdater 订单状态更新器
type OrderStatusUpdater struct {
	db                   *gorm.DB
	orderRepo            repository.OrderRepository
	statusHistoryService OrderStatusHistoryService
	logger               *zap.Logger
}

// NewOrderStatusUpdater 创建订单状态更新器
func NewOrderStatusUpdater(
	db *gorm.DB,
	orderRepo repository.OrderRepository,
	statusHistoryService OrderStatusHistoryService,
	logger *zap.Logger,
) *OrderStatusUpdater {
	return &OrderStatusUpdater{
		db:                   db,
		orderRepo:            orderRepo,
		statusHistoryService: statusHistoryService,
		logger:               logger,
	}
}

// UpdateOrderStatusRequest 更新订单状态请求
type UpdateOrderStatusRequest struct {
	OrderNo        string                 `json:"order_no"`
	NewStatus      string                 `json:"new_status"`
	Provider       string                 `json:"provider"`
	RawStatus      string                 `json:"raw_status"`
	ChangeSource   model.ChangeSource     `json:"change_source"`
	OperatorID     string                 `json:"operator_id"`
	OperatorName   string                 `json:"operator_name"`
	ChangeReason   string                 `json:"change_reason"`
	Extra          map[string]interface{} `json:"extra"`
	SkipValidation bool                   `json:"skip_validation"` // 是否跳过状态转换验证
	SkipHistory    bool                   `json:"skip_history"`    // 是否跳过历史记录
}

// UpdateOrderStatus 更新订单状态（带状态历史记录）
func (u *OrderStatusUpdater) UpdateOrderStatus(ctx context.Context, req *UpdateOrderStatusRequest) error {
	u.logger.Info("开始更新订单状态",
		zap.String("order_no", req.OrderNo),
		zap.String("new_status", req.NewStatus),
		zap.String("change_source", string(req.ChangeSource)))

	// 在事务中执行更新
	return u.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 1. 获取当前订单信息
		order, err := u.orderRepo.FindByOrderNo(ctx, req.OrderNo)
		if err != nil {
			return fmt.Errorf("获取订单信息失败: %w", err)
		}

		oldStatus := order.Status

		// 🔥 新增：幂等性检查 - 如果状态已经是目标状态，直接返回成功
		if oldStatus == req.NewStatus {
			u.logger.Info("✅ 订单状态已经是目标状态，跳过更新",
				zap.String("order_no", req.OrderNo),
				zap.String("current_status", oldStatus),
				zap.String("target_status", req.NewStatus),
				zap.String("change_source", string(req.ChangeSource)))
			return nil
		}

		// 2. 验证状态转换（如果需要）
		if !req.SkipValidation {
			validation := model.IsValidStatusTransition(oldStatus, req.NewStatus)
			if !validation.IsValid {
				u.logger.Warn("状态转换验证失败",
					zap.String("order_no", req.OrderNo),
					zap.String("from_status", oldStatus),
					zap.String("to_status", req.NewStatus),
					zap.String("reason", validation.Reason))
				// 注意：这里不返回错误，只记录警告，允许状态更新继续
			}
		}

		// 🔥 新增：特殊处理 cancelling → cancelled 的转换
		if oldStatus == model.OrderStatusCancelling && req.NewStatus == model.OrderStatusCancelled {
			u.logger.Info("✅ 确认取消状态转换：cancelling → cancelled",
				zap.String("order_no", req.OrderNo),
				zap.String("change_source", string(req.ChangeSource)))
		}

		// 3. 更新订单状态
		order.Status = req.NewStatus
		if err := u.orderRepo.Update(ctx, order); err != nil {
			return fmt.Errorf("更新订单状态失败: %w", err)
		}

		// 4. 记录状态变更历史（如果需要）
		if !req.SkipHistory && oldStatus != req.NewStatus {
			historyReq := &model.RecordStatusChangeRequest{
				OrderNo:         req.OrderNo,
				FromStatus:      oldStatus,
				ToStatus:        req.NewStatus,
				Provider:        req.Provider,
				RawStatus:       req.RawStatus,
				ChangeSource:    string(req.ChangeSource),
				OperatorID:      req.OperatorID,
				OperatorName:    req.OperatorName,
				ChangeReason:    req.ChangeReason,
				UserID:          order.UserID,
				CustomerOrderNo: order.CustomerOrderNo,
				Extra:           req.Extra,
			}

			if err := u.statusHistoryService.RecordStatusChange(ctx, historyReq); err != nil {
				u.logger.Error("记录状态变更历史失败",
					zap.String("order_no", req.OrderNo),
					zap.Error(err))
				// 不返回错误，避免影响主要业务流程
			}
		}

		u.logger.Info("订单状态更新成功",
			zap.String("order_no", req.OrderNo),
			zap.String("old_status", oldStatus),
			zap.String("new_status", req.NewStatus),
			zap.String("change_source", string(req.ChangeSource)))

		// 🔥 新增：状态一致性验证
		if err := u.verifyStatusConsistency(ctx, req.OrderNo, req.NewStatus); err != nil {
			u.logger.Error("❌ 状态一致性验证失败",
				zap.String("order_no", req.OrderNo),
				zap.String("expected_status", req.NewStatus),
				zap.Error(err))

			// 🔥 重要：如果状态不一致，尝试修复
			u.logger.Warn("🔧 检测到状态不一致，尝试修复",
				zap.String("order_no", req.OrderNo),
				zap.String("expected_status", req.NewStatus))

			// 重新尝试更新状态
			if retryErr := u.retryStatusUpdate(ctx, req.OrderNo, req.NewStatus); retryErr != nil {
				u.logger.Error("❌ 状态修复失败",
					zap.String("order_no", req.OrderNo),
					zap.Error(retryErr))
			} else {
				u.logger.Info("✅ 状态不一致已修复",
					zap.String("order_no", req.OrderNo),
					zap.String("status", req.NewStatus))
			}
		}

		return nil
	})
}

// verifyStatusConsistency 验证订单状态一致性
func (u *OrderStatusUpdater) verifyStatusConsistency(ctx context.Context, orderNo, expectedStatus string) error {
	// 重新查询订单状态确认更新成功
	actualOrder, err := u.orderRepo.FindByOrderNo(ctx, orderNo)
	if err != nil {
		return fmt.Errorf("查询订单失败: %w", err)
	}

	if actualOrder.Status != expectedStatus {
		return fmt.Errorf("状态不一致：期望 %s，实际 %s", expectedStatus, actualOrder.Status)
	}

	return nil
}

// retryStatusUpdate 重试状态更新（用于修复状态不一致）
func (u *OrderStatusUpdater) retryStatusUpdate(ctx context.Context, orderNo, newStatus string) error {
	u.logger.Info("🔄 开始重试状态更新",
		zap.String("order_no", orderNo),
		zap.String("new_status", newStatus))

	// 直接更新订单状态，跳过验证
	return u.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 获取当前订单信息
		order, err := u.orderRepo.FindByOrderNo(ctx, orderNo)
		if err != nil {
			return fmt.Errorf("获取订单信息失败: %w", err)
		}

		oldStatus := order.Status

		// 如果状态已经正确，直接返回
		if oldStatus == newStatus {
			u.logger.Info("✅ 状态已经正确，无需重试",
				zap.String("order_no", orderNo),
				zap.String("status", newStatus))
			return nil
		}

		// 强制更新订单状态
		order.Status = newStatus
		if err := u.orderRepo.Update(ctx, order); err != nil {
			return fmt.Errorf("更新订单状态失败: %w", err)
		}

		u.logger.Info("✅ 重试状态更新成功",
			zap.String("order_no", orderNo),
			zap.String("old_status", oldStatus),
			zap.String("new_status", newStatus))

		return nil
	})
}

// UpdateOrderStatusWithCallback 通过回调更新订单状态
func (u *OrderStatusUpdater) UpdateOrderStatusWithCallback(ctx context.Context, orderNo, newStatus, provider, rawStatus string, extra map[string]interface{}) error {
	return u.UpdateOrderStatus(ctx, &UpdateOrderStatusRequest{
		OrderNo:      orderNo,
		NewStatus:    newStatus,
		Provider:     provider,
		RawStatus:    rawStatus,
		ChangeSource: model.ChangeSourceCallback,
		Extra:        extra,
	})
}

// UpdateOrderStatusManually 手动更新订单状态
func (u *OrderStatusUpdater) UpdateOrderStatusManually(ctx context.Context, orderNo, newStatus, operatorID, operatorName, reason string) error {
	return u.UpdateOrderStatus(ctx, &UpdateOrderStatusRequest{
		OrderNo:      orderNo,
		NewStatus:    newStatus,
		ChangeSource: model.ChangeSourceManual,
		OperatorID:   operatorID,
		OperatorName: operatorName,
		ChangeReason: reason,
	})
}

// UpdateOrderStatusBySystem 系统自动更新订单状态
func (u *OrderStatusUpdater) UpdateOrderStatusBySystem(ctx context.Context, orderNo, newStatus, reason string) error {
	return u.UpdateOrderStatus(ctx, &UpdateOrderStatusRequest{
		OrderNo:      orderNo,
		NewStatus:    newStatus,
		ChangeSource: model.ChangeSourceSystem,
		ChangeReason: reason,
	})
}

// UpdateOrderStatusByAPI API调用更新订单状态
func (u *OrderStatusUpdater) UpdateOrderStatusByAPI(ctx context.Context, orderNo, newStatus, provider string, extra map[string]interface{}) error {
	return u.UpdateOrderStatus(ctx, &UpdateOrderStatusRequest{
		OrderNo:      orderNo,
		NewStatus:    newStatus,
		Provider:     provider,
		ChangeSource: model.ChangeSourceAPI,
		Extra:        extra,
	})
}

// BatchUpdateOrderStatus 批量更新订单状态
func (u *OrderStatusUpdater) BatchUpdateOrderStatus(ctx context.Context, requests []*UpdateOrderStatusRequest) error {
	u.logger.Info("开始批量更新订单状态", zap.Int("count", len(requests)))

	return u.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for i, req := range requests {
			if err := u.UpdateOrderStatus(ctx, req); err != nil {
				u.logger.Error("批量更新订单状态失败",
					zap.Int("index", i),
					zap.String("order_no", req.OrderNo),
					zap.Error(err))
				return fmt.Errorf("批量更新第%d个订单失败: %w", i+1, err)
			}
		}
		return nil
	})
}
