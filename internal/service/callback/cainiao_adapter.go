package callback

import (
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"net/url"
	"strings"

	"github.com/your-org/go-kuaidi/internal/constants"
	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/util"
)

// CainiaoCallbackAdapter 菜鸟裹裹回调适配器
type CainiaoCallbackAdapter struct {
	accessCode string // 菜鸟裹裹接入编码，用于签名验证
}

// NewCainiaoCallbackAdapter 创建菜鸟裹裹回调适配器
func NewCainiaoCallbackAdapter(accessCode string) ProviderCallbackAdapter {
	return &CainiaoCallbackAdapter{
		accessCode: accessCode,
	}
}

// ValidateSignature 验证菜鸟裹裹签名
func (a *CainiaoCallbackAdapter) ValidateSignature(rawData []byte, headers map[string]string) error {
	// 🔥 菜鸟回调不需要签名验证
	// 菜鸟推送回调是第三方主动发送，不使用我们的签名体系
	// 已在签名中间件层面跳过验证，这里直接返回成功
	return nil
}

// ParseCallback 解析菜鸟裹裹回调数据 - 🔥 数据一致性修复
func (a *CainiaoCallbackAdapter) ParseCallback(rawData []byte) (*model.ParsedCallbackData, error) {
	// 🔥 数据预处理：检查数据是否为空或无效
	if len(rawData) == 0 {
		return nil, fmt.Errorf("菜鸟回调数据为空")
	}

	// 🔥 关键修复：菜鸟回调数据是URL编码的表单数据，不是JSON
	dataStr := strings.TrimSpace(string(rawData))

	var outerData struct {
		LogisticsInterface string `json:"logistics_interface"` // JSON字符串，包含实际事件数据
		DataDigest         string `json:"data_digest"`
		PartnerCode        string `json:"partner_code"`
		FromCode           string `json:"from_code"`
		MsgType            string `json:"msg_type"`
		MsgId              string `json:"msg_id"`
	}

	// 🔥 检查数据格式：JSON还是URL编码表单数据
	if strings.HasPrefix(dataStr, "{") {
		// JSON格式（旧格式或测试数据）
		if err := json.Unmarshal(rawData, &outerData); err != nil {
			return nil, fmt.Errorf("解析菜鸟裹裹JSON回调数据失败: %w", err)
		}
	} else {
		// URL编码表单数据格式（菜鸟官方格式）
		// 解析URL编码的表单数据
		values, err := parseURLEncodedData(dataStr)
		if err != nil {
			return nil, fmt.Errorf("解析菜鸟裹裹URL编码数据失败: %w", err)
		}

		// 提取各个字段
		outerData.LogisticsInterface = values["logistics_interface"]
		outerData.DataDigest = values["data_digest"]
		outerData.PartnerCode = values["partner_code"]
		outerData.FromCode = values["from_code"]
		outerData.MsgType = values["msg_type"]
		outerData.MsgId = values["msg_id"]
	}

	// 🔥 数据一致性：验证必填字段
	if outerData.LogisticsInterface == "" {
		return nil, fmt.Errorf("菜鸟裹裹logistics_interface字段不能为空")
	}
	if outerData.DataDigest == "" {
		return nil, fmt.Errorf("菜鸟裹裹data_digest字段不能为空")
	}
	if outerData.MsgId == "" {
		return nil, fmt.Errorf("菜鸟裹裹msg_id字段不能为空")
	}

	// 🔥 解析logistics_interface中的JSON字符串
	var innerData struct {
		ExternalOrder *struct {
			ExternalBizIdList []string `json:"externalBizIdList"`
			OrderId           string   `json:"orderId"`
			OrderStatusDesc   string   `json:"orderStatusDesc"`
			OrderStatusCode   string   `json:"orderStatusCode"`
		} `json:"externalOrder"`
		OrderEvent *struct {
			EventDesc string                 `json:"eventDesc"`
			EventType string                 `json:"eventType"`
			EventData map[string]interface{} `json:"eventData"`
		} `json:"orderEvent"`
	}

	if err := json.Unmarshal([]byte(outerData.LogisticsInterface), &innerData); err != nil {
		return nil, fmt.Errorf("解析菜鸟裹裹logistics_interface内容失败: %w", err)
	}

	// 🔥 数据一致性：验证内层数据完整性
	if innerData.OrderEvent == nil || innerData.OrderEvent.EventType == "" {
		return nil, fmt.Errorf("菜鸟裹裹事件类型不能为空")
	}
	if innerData.ExternalOrder == nil {
		return nil, fmt.Errorf("菜鸟裹裹外部订单信息不能为空")
	}

	// 🔥 确定回调类型
	var callbackType string
	eventType := innerData.OrderEvent.EventType
	switch eventType {
	// 订单事件
	case "CREATE_ORDER", "CANCEL_ORDER", "GOT_SUCCESS", "UPLOAD_MAIL_NO_SUCCESS",
		"FINISH_ORDER", "COURIER_CHECK_BILL_SUCCESS", "CHANGE_DELIVERY_USER_SUCCESS",
		"MODIFY_EXPECT_GOT_TIME_SUCCESS", "PAY_SUCCESS", "SEEK_DELIVERY_SUCCESS",
		"OUT_ORDER_COURIER_UPDATE":
		callbackType = constants.EventTypeOrderStatusChanged
	// 物流事件
	case "ACCEPT", "TRANSPORT", "DELIVERING", "SIGN", "FAILED", "BACK", "REVERSE_RETURN":
		callbackType = constants.EventTypeOrderStatusChanged
	default:
		return nil, fmt.Errorf("未知的菜鸟裹裹回调事件类型: %s", eventType)
	}

	// 🔥 提取订单号和运单号
	var orderNo, trackingNo string
	if innerData.ExternalOrder != nil {
		orderNo = innerData.ExternalOrder.OrderId
		// 从externalBizIdList中提取我们的订单号
		if len(innerData.ExternalOrder.ExternalBizIdList) > 0 {
			// externalBizIdList[0] 通常是我们的平台订单号
			orderNo = innerData.ExternalOrder.ExternalBizIdList[0]
		}
	}

	// 从事件数据中提取运单号
	if innerData.OrderEvent != nil && innerData.OrderEvent.EventData != nil {
		if mailNo, ok := innerData.OrderEvent.EventData["mailNo"].(string); ok {
			trackingNo = mailNo
		}
	}

	// 🔥 数据一致性：构建解析后的回调数据，包含幂等性标识
	// 将菜鸟特有的数据合并到Data中
	enrichedData := map[string]any{
		"event_type":          eventType,
		"partner_code":        outerData.PartnerCode,
		"from_code":           outerData.FromCode,
		"msg_type":            outerData.MsgType,
		"msg_id":              outerData.MsgId,
		"data_digest":         outerData.DataDigest,
		"logistics_interface": outerData.LogisticsInterface, // 保留原始JSON字符串
		// 🔥 数据一致性：添加幂等性标识
		"idempotency_key": fmt.Sprintf("cainiao_%s_%s", outerData.MsgId, outerData.DataDigest),
		"raw_data_hash":   a.calculateDataHash(rawData),
	}

	// 🔥 修复：正确传递结构体数据，避免指针丢失
	if innerData.ExternalOrder != nil {
		enrichedData["external_order"] = map[string]interface{}{
			"externalBizIdList": innerData.ExternalOrder.ExternalBizIdList,
			"orderId":           innerData.ExternalOrder.OrderId,
			"orderStatusDesc":   innerData.ExternalOrder.OrderStatusDesc,
			"orderStatusCode":   innerData.ExternalOrder.OrderStatusCode,
		}
	}

	if innerData.OrderEvent != nil {
		enrichedData["order_event"] = map[string]interface{}{
			"eventDesc": innerData.OrderEvent.EventDesc,
			"eventType": innerData.OrderEvent.EventType,
			"eventData": innerData.OrderEvent.EventData,
		}
	}

	return &model.ParsedCallbackData{
		Type:            callbackType,
		OrderNo:         "",      // 留空，由统一回调服务查找平台订单号
		CustomerOrderNo: orderNo, // 菜鸟订单号或我们的订单号
		TrackingNo:      trackingNo,
		Data:            enrichedData,
		Timestamp:       util.NowBeijing(),
	}, nil
}

// calculateDataHash 计算数据哈希值用于幂等性检查
func (a *CainiaoCallbackAdapter) calculateDataHash(data []byte) string {
	hash := sha256.Sum256(data)
	return hex.EncodeToString(hash[:])
}

// BuildResponse 构建菜鸟裹裹响应
func (a *CainiaoCallbackAdapter) BuildResponse(data *model.StandardizedCallbackData) *model.CallbackResponse {
	// 根据菜鸟裹裹官方文档，回调响应格式为：
	// {
	//     "result": {
	//         "statusMsg": "成功",
	//         "data": true,
	//         "success": true,
	//         "statusCode": "200"
	//     }
	// }
	return &model.CallbackResponse{
		Success: constants.ResponseValueTrue,
		Code:    constants.HTTPStatusOK,
		Message: constants.MessageReceived,
		Data: map[string]interface{}{
			"result": map[string]interface{}{
				"statusMsg":  constants.MessageSuccess,
				"data":       constants.ResponseValueTrue,
				"success":    constants.ResponseValueTrue,
				"statusCode": constants.HTTPStatusOK,
			},
		},
	}
}

// parseURLEncodedData 解析URL编码的表单数据
func parseURLEncodedData(data string) (map[string]string, error) {
	result := make(map[string]string)

	// 分割键值对
	pairs := strings.Split(data, "&")
	for _, pair := range pairs {
		// 分割键和值
		parts := strings.SplitN(pair, "=", 2)
		if len(parts) != 2 {
			continue
		}

		key := parts[0]
		value := parts[1]

		// 🔥 修复：使用Go标准库的URL解码函数
		decodedKey, err := url.QueryUnescape(key)
		if err != nil {
			return nil, fmt.Errorf("解码键失败 %s: %w", key, err)
		}

		decodedValue, err := url.QueryUnescape(value)
		if err != nil {
			return nil, fmt.Errorf("解码值失败 %s: %w", value, err)
		}

		result[decodedKey] = decodedValue
	}

	return result, nil
}
