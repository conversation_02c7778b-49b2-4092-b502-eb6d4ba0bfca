package callback

import (
	"context"
	"database/sql"
	"testing"
	"time"

	_ "github.com/mattn/go-sqlite3"
	"github.com/stretchr/testify/assert"
	"go.uber.org/zap"

	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/repository"
	"github.com/your-org/go-kuaidi/internal/service"
)

// TestPlatformOrderNoExtraction 测试平台订单号提取功能
func TestPlatformOrderNoExtraction(t *testing.T) {
	// 创建内存数据库进行测试
	db, err := sql.Open("sqlite3", ":memory:")
	if err != nil {
		t.Fatal("创建内存数据库失败:", err)
	}
	defer db.Close()

	// 创建测试表
	createTableSQL := `
	CREATE TABLE order_records (
		id INTEGER PRIMARY KEY,
		platform_order_no TEXT,
		customer_order_no TEXT,
		order_no TEXT,
		tracking_no TEXT,
		user_id TEXT,
		provider TEXT,
		status TEXT,
		express_type TEXT,
		product_type TEXT,
		weight REAL DEFAULT 0,
		price REAL DEFAULT 0,
		sender_info TEXT DEFAULT '{}',
		receiver_info TEXT DEFAULT '{}',
		package_info TEXT DEFAULT '{}',
		request_data TEXT DEFAULT '{}',
		response_data TEXT DEFAULT '{}',
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		task_id TEXT DEFAULT '',
		poll_token TEXT DEFAULT '',
		actual_fee REAL DEFAULT 0,
		insurance_fee REAL DEFAULT 0,
		overweight_fee REAL DEFAULT 0,
		underweight_fee REAL DEFAULT 0,
		weight_adjustment_reason TEXT DEFAULT '',
		billing_status TEXT DEFAULT 'pending',
		order_volume REAL DEFAULT 0,
		actual_weight REAL DEFAULT 0,
		actual_volume REAL DEFAULT 0,
		charged_weight REAL DEFAULT 0,
		courier_name TEXT DEFAULT '',
		courier_phone TEXT DEFAULT '',
		courier_code TEXT DEFAULT '',
		station_name TEXT DEFAULT '',
		pickup_code TEXT DEFAULT ''
	)`

	_, err = db.Exec(createTableSQL)
	if err != nil {
		t.Fatal("创建测试表失败:", err)
	}

	// 插入测试数据
	insertSQL := `
	INSERT INTO order_records (
		platform_order_no, customer_order_no, order_no, tracking_no,
		user_id, provider, status, express_type, product_type
	) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`

	_, err = db.Exec(insertSQL,
		"GK20250714000000002",                  // platform_order_no
		"gk1752424403",                         // customer_order_no
		"JT250714003323965030",                 // order_no
		"JT0017672474283",                      // tracking_no
		"d7e45ff4-cb3d-470c-9fbc-22114639d096", // user_id
		"yida",                                 // provider
		"assigned",                             // status
		"JT",                                   // express_type
		"JT_BK",                                // product_type
	)
	if err != nil {
		t.Fatal("插入测试数据失败:", err)
	}

	// 创建仓库和服务
	logger, _ := zap.NewDevelopment()
	orderRepo := repository.NewPostgresOrderRepository(db)
	smartFinder := service.NewSmartOrderFinder(orderRepo, logger)

	ctx := context.Background()

	t.Run("通过客户订单号查找-应返回平台订单号", func(t *testing.T) {
		// 直接使用仓库方法查询
		order, err := orderRepo.FindByCustomerOrderNo(ctx, "gk1752424403")
		assert.NoError(t, err)
		assert.NotNil(t, order)

		// 🔥 关键验证：确保平台订单号正确返回
		assert.Equal(t, "GK20250714000000002", order.PlatformOrderNo, "平台订单号应该正确返回")
		assert.Equal(t, "gk1752424403", order.CustomerOrderNo, "客户订单号应该正确返回")
		assert.Equal(t, "JT250714003323965030", order.OrderNo, "供应商订单号应该正确返回")
		assert.Equal(t, "JT0017672474283", order.TrackingNo, "运单号应该正确返回")
	})

	t.Run("通过智能订单查找服务-应返回平台订单号", func(t *testing.T) {
		// 使用智能订单查找服务
		order, err := smartFinder.FindOrderByAnyIdentifier(ctx, "gk1752424403", "d7e45ff4-cb3d-470c-9fbc-22114639d096")
		assert.NoError(t, err)
		assert.NotNil(t, order)

		// 🔥 关键验证：智能订单查找应该返回完整的平台订单号
		assert.Equal(t, "GK20250714000000002", order.PlatformOrderNo, "智能订单查找应该返回正确的平台订单号")
		assert.Equal(t, "gk1752424403", order.CustomerOrderNo, "客户订单号应该正确返回")
		assert.Equal(t, "JT250714003323965030", order.OrderNo, "供应商订单号应该正确返回")
		assert.Equal(t, "JT0017672474283", order.TrackingNo, "运单号应该正确返回")
	})

	t.Run("通过供应商订单号查找-应返回平台订单号", func(t *testing.T) {
		order, err := orderRepo.FindByOrderNo(ctx, "JT250714003323965030")
		assert.NoError(t, err)
		assert.NotNil(t, order)

		// 🔥 关键验证：确保平台订单号正确返回
		assert.Equal(t, "GK20250714000000002", order.PlatformOrderNo, "平台订单号应该正确返回")
		assert.Equal(t, "gk1752424403", order.CustomerOrderNo, "客户订单号应该正确返回")
	})

	t.Run("通过运单号查找-应返回平台订单号", func(t *testing.T) {
		order, err := orderRepo.FindByTrackingNo(ctx, "JT0017672474283")
		assert.NoError(t, err)
		assert.NotNil(t, order)

		// 🔥 关键验证：确保平台订单号正确返回
		assert.Equal(t, "GK20250714000000002", order.PlatformOrderNo, "平台订单号应该正确返回")
		assert.Equal(t, "gk1752424403", order.CustomerOrderNo, "客户订单号应该正确返回")
	})

	t.Run("通过平台订单号查找-应返回平台订单号", func(t *testing.T) {
		order, err := orderRepo.FindByPlatformOrderNo(ctx, "GK20250714000000002", "d7e45ff4-cb3d-470c-9fbc-22114639d096")
		assert.NoError(t, err)
		assert.NotNil(t, order)

		// 🔥 关键验证：确保平台订单号正确返回
		assert.Equal(t, "GK20250714000000002", order.PlatformOrderNo, "平台订单号应该正确返回")
		assert.Equal(t, "gk1752424403", order.CustomerOrderNo, "客户订单号应该正确返回")
	})
}

// TestCallbackDataPlatformOrderNoFilling 测试回调数据中平台订单号的填充
func TestCallbackDataPlatformOrderNoFilling(t *testing.T) {
	// 创建内存数据库进行测试
	db, err := sql.Open("sqlite3", ":memory:")
	if err != nil {
		t.Fatal("创建内存数据库失败:", err)
	}
	defer db.Close()

	// 创建测试表（简化版）
	createTableSQL := `
	CREATE TABLE order_records (
		id INTEGER PRIMARY KEY,
		platform_order_no TEXT,
		customer_order_no TEXT,
		order_no TEXT,
		tracking_no TEXT,
		user_id TEXT,
		provider TEXT,
		status TEXT,
		express_type TEXT,
		product_type TEXT,
		weight REAL DEFAULT 0,
		price REAL DEFAULT 0,
		sender_info TEXT DEFAULT '{}',
		receiver_info TEXT DEFAULT '{}',
		package_info TEXT DEFAULT '{}',
		request_data TEXT DEFAULT '{}',
		response_data TEXT DEFAULT '{}',
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		task_id TEXT DEFAULT '',
		poll_token TEXT DEFAULT '',
		actual_fee REAL DEFAULT 0,
		insurance_fee REAL DEFAULT 0,
		overweight_fee REAL DEFAULT 0,
		underweight_fee REAL DEFAULT 0,
		weight_adjustment_reason TEXT DEFAULT '',
		billing_status TEXT DEFAULT 'pending',
		order_volume REAL DEFAULT 0,
		actual_weight REAL DEFAULT 0,
		actual_volume REAL DEFAULT 0,
		charged_weight REAL DEFAULT 0,
		courier_name TEXT DEFAULT '',
		courier_phone TEXT DEFAULT '',
		courier_code TEXT DEFAULT '',
		station_name TEXT DEFAULT '',
		pickup_code TEXT DEFAULT ''
	)`

	_, err = db.Exec(createTableSQL)
	if err != nil {
		t.Fatal("创建测试表失败:", err)
	}

	// 插入测试数据
	insertSQL := `
	INSERT INTO order_records (
		platform_order_no, customer_order_no, order_no, tracking_no, 
		user_id, provider, status, express_type
	) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`

	_, err = db.Exec(insertSQL,
		"GK20250714000000002",                  // platform_order_no
		"gk1752424403",                         // customer_order_no
		"JT250714003323965030",                 // order_no
		"JT0017672474283",                      // tracking_no
		"d7e45ff4-cb3d-470c-9fbc-22114639d096", // user_id
		"yida",                                 // provider
		"assigned",                             // status
		"JT",                                   // express_type
	)
	if err != nil {
		t.Fatal("插入测试数据失败:", err)
	}

	// 创建服务
	logger, _ := zap.NewDevelopment()
	orderRepo := repository.NewPostgresOrderRepository(db)
	smartFinder := service.NewSmartOrderFinder(orderRepo, logger)

	// 模拟回调数据（初始状态）
	callbackData := &model.StandardizedCallbackData{
		EventType:       "order_status_changed",
		OrderNo:         "",             // 初始为空
		PlatformOrderNo: "",             // 初始为空
		CustomerOrderNo: "gk1752424403", // 从回调中获取
		TrackingNo:      "JT0017672474283",
		Provider:        "yida",
		Timestamp:       time.Now(),
		Data: map[string]interface{}{
			"status": "assigned",
			"courier": map[string]interface{}{
				"name":  "苏守太",
				"phone": "15122209431",
			},
		},
	}

	// 模拟智能订单查找过程
	ctx := context.Background()
	foundOrder, err := smartFinder.FindOrderByAnyIdentifier(ctx, callbackData.CustomerOrderNo, "d7e45ff4-cb3d-470c-9fbc-22114639d096")

	// 验证查找结果
	assert.NoError(t, err)
	assert.NotNil(t, foundOrder)

	// 🔥 关键验证：智能订单查找应该返回完整的平台订单号
	assert.Equal(t, "GK20250714000000002", foundOrder.PlatformOrderNo, "智能订单查找应该返回正确的平台订单号")

	// 模拟回调服务填充平台订单号的过程
	callbackData.PlatformOrderNo = foundOrder.PlatformOrderNo
	callbackData.OrderNo = foundOrder.OrderNo

	// 🔥 关键验证：回调数据应该包含正确的平台订单号
	assert.Equal(t, "GK20250714000000002", callbackData.PlatformOrderNo, "回调数据应该包含正确的平台订单号")
	assert.Equal(t, "JT250714003323965030", callbackData.OrderNo, "回调数据应该包含正确的供应商订单号")
	assert.Equal(t, "gk1752424403", callbackData.CustomerOrderNo, "回调数据应该包含正确的客户订单号")

	// 🔥 验证回调转发数据结构
	unifiedCallbackData := &model.UnifiedCallbackData{
		CustomerOrderNo: callbackData.CustomerOrderNo,
		OrderNo:         callbackData.OrderNo,
		PlatformOrderNo: callbackData.PlatformOrderNo,
		TrackingNo:      callbackData.TrackingNo,
		Timestamp:       callbackData.Timestamp.Format(time.RFC3339),
		EventType:       3, // assigned状态
		Data:            callbackData.Data,
	}

	// 🔥 最终验证：统一回调数据应该包含正确的平台订单号
	assert.Equal(t, "GK20250714000000002", unifiedCallbackData.PlatformOrderNo, "统一回调数据应该包含正确的平台订单号")
	assert.Equal(t, "gk1752424403", unifiedCallbackData.CustomerOrderNo, "统一回调数据应该包含正确的客户订单号")
	assert.Equal(t, "JT250714003323965030", unifiedCallbackData.OrderNo, "统一回调数据应该包含正确的供应商订单号")
	assert.Equal(t, "JT0017672474283", unifiedCallbackData.TrackingNo, "统一回调数据应该包含正确的运单号")
	assert.Equal(t, 3, unifiedCallbackData.EventType, "事件类型应该正确")
}
