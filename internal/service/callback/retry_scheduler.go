package callback

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/google/uuid"
	"go.uber.org/zap"

	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/repository"
	"github.com/your-org/go-kuaidi/internal/service"
	"github.com/your-org/go-kuaidi/internal/util"
)

// CallbackRetryScheduler 回调重试调度器
// 🎯 核心功能：智能重试机制，支持固定间隔和指数退避策略
type CallbackRetryScheduler struct {
	callbackRepository repository.CallbackRepository
	configService      service.SystemConfigService
	forwarder          *UnifiedCallbackForwarder
	logger             *zap.Logger

	// 调度控制
	ctx       context.Context
	cancel    context.CancelFunc
	wg        sync.WaitGroup
	isRunning bool
	mu        sync.RWMutex

	// 配置缓存
	configCache      *RetryConfig
	configMu         sync.RWMutex
	lastConfigUpdate time.Time

	// 性能监控
	metrics *model.RetryMetrics
}

// RetryConfig 重试配置
type RetryConfig struct {
	RetryIntervalMinutes int     `json:"retry_interval_minutes"`
	MaxRetryCount        int     `json:"max_retry_count"`
	RetryStrategy        string  `json:"retry_strategy"` // fixed, exponential
	ExponentialBase      float64 `json:"exponential_base"`
	MaxIntervalMinutes   int     `json:"max_interval_minutes"`
	Enabled              bool    `json:"enabled"`
	BatchSize            int     `json:"batch_size"`
	WorkerCount          int     `json:"worker_count"`
}

// 使用model包中的RetryMetrics

// NewCallbackRetryScheduler 创建重试调度器
func NewCallbackRetryScheduler(
	callbackRepository repository.CallbackRepository,
	configService service.SystemConfigService,
	forwarder *UnifiedCallbackForwarder,
	logger *zap.Logger,
) *CallbackRetryScheduler {
	ctx, cancel := context.WithCancel(context.Background())

	return &CallbackRetryScheduler{
		callbackRepository: callbackRepository,
		configService:      configService,
		forwarder:          forwarder,
		logger:             logger,
		ctx:                ctx,
		cancel:             cancel,
		metrics:            &model.RetryMetrics{},
	}
}

// Start 启动重试调度器
func (s *CallbackRetryScheduler) Start() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.isRunning {
		return fmt.Errorf("重试调度器已在运行")
	}

	// 加载配置
	if err := s.loadConfig(); err != nil {
		return fmt.Errorf("加载重试配置失败: %w", err)
	}

	if !s.configCache.Enabled {
		s.logger.Info("重试调度器已禁用")
		return nil
	}

	s.isRunning = true

	// 启动配置监控
	s.wg.Add(1)
	go s.configWatcher()

	// 启动调度器
	s.wg.Add(1)
	go s.scheduler()

	// 启动工作协程
	for i := 0; i < s.configCache.WorkerCount; i++ {
		s.wg.Add(1)
		go s.worker(i)
	}

	s.logger.Info("回调重试调度器已启动",
		zap.Int("worker_count", s.configCache.WorkerCount),
		zap.Int("batch_size", s.configCache.BatchSize),
		zap.Int("retry_interval_minutes", s.configCache.RetryIntervalMinutes))

	return nil
}

// Stop 停止重试调度器
func (s *CallbackRetryScheduler) Stop() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if !s.isRunning {
		return nil
	}

	s.logger.Info("正在停止回调重试调度器...")

	s.cancel()
	s.isRunning = false

	// 等待所有协程结束
	done := make(chan struct{})
	go func() {
		s.wg.Wait()
		close(done)
	}()

	// 设置超时
	select {
	case <-done:
		s.logger.Info("回调重试调度器已停止")
	case <-time.After(30 * time.Second):
		s.logger.Warn("回调重试调度器停止超时")
	}

	return nil
}

// ScheduleRetry 调度重试任务
func (s *CallbackRetryScheduler) ScheduleRetry(ctx context.Context, forwardRecord *model.CallbackForwardRecord, callbackRecord *model.UnifiedCallbackRecord) error {
	config := s.getConfig()
	if !config.Enabled {
		return nil
	}

	// 检查是否已达到最大重试次数
	if forwardRecord.RetryCount >= config.MaxRetryCount {
		s.logger.Info("已达到最大重试次数，停止重试",
			zap.String("forward_record_id", forwardRecord.ID.String()),
			zap.Int("retry_count", forwardRecord.RetryCount),
			zap.Int("max_retry_count", config.MaxRetryCount))

		// 标记重试已停止
		return s.markRetryAsStopped(ctx, forwardRecord, "达到最大重试次数")
	}

	// 计算下次重试时间
	nextRetryAt := s.calculateNextRetryTime(forwardRecord.RetryCount, config)

	// 创建重试记录
	retryRecord := &model.CallbackRetryRecord{
		ID:               uuid.New(),
		ForwardRecordID:  forwardRecord.ID,
		CallbackRecordID: callbackRecord.ID,
		UserID:           forwardRecord.UserID,
		RetryAttempt:     forwardRecord.RetryCount + 1,
		ScheduledAt:      nextRetryAt,
		Status:           model.RetryStatusPending,
		CreatedAt:        util.NowBeijing(),
		UpdatedAt:        util.NowBeijing(),
	}

	// 保存重试记录
	if err := s.callbackRepository.SaveRetryRecord(ctx, retryRecord); err != nil {
		return fmt.Errorf("保存重试记录失败: %w", err)
	}

	// 更新转发记录的下次重试时间
	forwardRecord.NextRetryAt = &nextRetryAt
	if err := s.callbackRepository.UpdateForwardRecord(ctx, forwardRecord); err != nil {
		s.logger.Warn("更新转发记录的重试时间失败", zap.Error(err))
	}

	s.metrics.TotalScheduled++

	s.logger.Info("重试任务已调度",
		zap.String("forward_record_id", forwardRecord.ID.String()),
		zap.String("user_id", forwardRecord.UserID),
		zap.Int("retry_attempt", retryRecord.RetryAttempt),
		zap.Time("scheduled_at", nextRetryAt))

	return nil
}

// calculateNextRetryTime 计算下次重试时间
func (s *CallbackRetryScheduler) calculateNextRetryTime(currentRetryCount int, config *RetryConfig) time.Time {
	var intervalMinutes int

	switch config.RetryStrategy {
	case "exponential":
		// 指数退避策略
		intervalMinutes = int(float64(config.RetryIntervalMinutes) *
			pow(config.ExponentialBase, float64(currentRetryCount)))

		// 限制最大间隔
		if intervalMinutes > config.MaxIntervalMinutes {
			intervalMinutes = config.MaxIntervalMinutes
		}
	default:
		// 固定间隔策略
		intervalMinutes = config.RetryIntervalMinutes
	}

	return util.NowBeijing().Add(time.Duration(intervalMinutes) * time.Minute)
}

// pow 简单的幂运算实现
func pow(base float64, exp float64) float64 {
	if exp == 0 {
		return 1
	}
	result := base
	for i := 1; i < int(exp); i++ {
		result *= base
	}
	return result
}

// markRetryAsStopped 标记重试已停止
func (s *CallbackRetryScheduler) markRetryAsStopped(ctx context.Context, forwardRecord *model.CallbackForwardRecord, reason string) error {
	// 更新转发记录
	forwardRecord.RetryEnabled = false
	forwardRecord.NextRetryAt = nil

	if err := s.callbackRepository.UpdateForwardRecord(ctx, forwardRecord); err != nil {
		return fmt.Errorf("更新转发记录失败: %w", err)
	}

	// 更新回调记录
	callbackRecord, err := s.callbackRepository.GetCallbackRecord(ctx, forwardRecord.CallbackRecordID)
	if err != nil {
		s.logger.Warn("获取回调记录失败", zap.Error(err))
		return nil
	}

	now := util.NowBeijing()
	callbackRecord.RetryStoppedAt = &now
	callbackRecord.RetryStopReason = reason
	callbackRecord.RetryEnabled = false

	if err := s.callbackRepository.UpdateCallbackRecord(ctx, callbackRecord); err != nil {
		s.logger.Warn("更新回调记录失败", zap.Error(err))
	}

	return nil
}

// loadConfig 加载重试配置
func (s *CallbackRetryScheduler) loadConfig() error {
	config := &RetryConfig{
		RetryIntervalMinutes: 10,
		MaxRetryCount:        10,
		RetryStrategy:        "fixed",
		ExponentialBase:      2.0,
		MaxIntervalMinutes:   1440, // 24小时
		Enabled:              true,
		BatchSize:            50,
		WorkerCount:          3,
	}

	// 从数据库加载配置
	if intervalMinutes, err := s.configService.GetIntConfig(s.ctx, "callback_retry", "retry_interval_minutes", config.RetryIntervalMinutes); err == nil {
		config.RetryIntervalMinutes = intervalMinutes
	}

	if maxRetryCount, err := s.configService.GetIntConfig(s.ctx, "callback_retry", "max_retry_count", config.MaxRetryCount); err == nil {
		config.MaxRetryCount = maxRetryCount
	}

	if enabled, err := s.configService.GetBoolConfig(s.ctx, "callback_retry", "enabled", config.Enabled); err == nil {
		config.Enabled = enabled
	}

	if strategy, err := s.configService.GetStringConfig(s.ctx, "callback_retry", "retry_strategy", config.RetryStrategy); err == nil {
		config.RetryStrategy = strategy
	}

	if batchSize, err := s.configService.GetIntConfig(s.ctx, "callback_retry", "batch_size", config.BatchSize); err == nil {
		config.BatchSize = batchSize
	}

	if workerCount, err := s.configService.GetIntConfig(s.ctx, "callback_retry", "worker_count", config.WorkerCount); err == nil {
		config.WorkerCount = workerCount
	}

	s.configMu.Lock()
	s.configCache = config
	s.lastConfigUpdate = time.Now()
	s.configMu.Unlock()

	s.logger.Info("重试配置已加载",
		zap.Int("retry_interval_minutes", config.RetryIntervalMinutes),
		zap.Int("max_retry_count", config.MaxRetryCount),
		zap.String("retry_strategy", config.RetryStrategy),
		zap.Bool("enabled", config.Enabled))

	return nil
}

// getConfig 获取当前配置
func (s *CallbackRetryScheduler) getConfig() *RetryConfig {
	s.configMu.RLock()
	defer s.configMu.RUnlock()
	return s.configCache
}

// GetMetrics 获取重试指标
func (s *CallbackRetryScheduler) GetMetrics() *model.RetryMetrics {
	return s.metrics
}

// IsRunning 检查调度器是否运行中
func (s *CallbackRetryScheduler) IsRunning() bool {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.isRunning
}

// configWatcher 配置监控协程
func (s *CallbackRetryScheduler) configWatcher() {
	defer s.wg.Done()

	ticker := time.NewTicker(30 * time.Second) // 每30秒检查一次配置
	defer ticker.Stop()

	for {
		select {
		case <-s.ctx.Done():
			return
		case <-ticker.C:
			if err := s.loadConfig(); err != nil {
				s.logger.Error("重新加载配置失败", zap.Error(err))
			}
		}
	}
}

// scheduler 主调度协程
func (s *CallbackRetryScheduler) scheduler() {
	defer s.wg.Done()

	ticker := time.NewTicker(1 * time.Minute) // 每分钟扫描一次
	defer ticker.Stop()

	for {
		select {
		case <-s.ctx.Done():
			return
		case <-ticker.C:
			s.scanAndSchedulePendingRetries()
		}
	}
}

// scanAndSchedulePendingRetries 扫描并调度待重试任务
func (s *CallbackRetryScheduler) scanAndSchedulePendingRetries() {
	config := s.getConfig()
	if !config.Enabled {
		return
	}

	ctx, cancel := context.WithTimeout(s.ctx, 30*time.Second)
	defer cancel()

	// 获取到期的重试任务
	retryRecords, err := s.callbackRepository.GetPendingRetryRecords(ctx, config.BatchSize)
	if err != nil {
		s.logger.Error("获取待重试记录失败", zap.Error(err))
		return
	}

	if len(retryRecords) == 0 {
		return
	}

	s.logger.Info("发现待重试任务",
		zap.Int("count", len(retryRecords)))

	// 将任务分发给工作协程
	for _, record := range retryRecords {
		select {
		case <-s.ctx.Done():
			return
		default:
			// 标记为执行中
			record.Status = model.RetryStatusExecuting
			now := util.NowBeijing()
			record.ExecutedAt = &now
			record.UpdatedAt = now

			if err := s.callbackRepository.UpdateRetryRecord(ctx, record); err != nil {
				s.logger.Error("更新重试记录状态失败", zap.Error(err))
				continue
			}

			// 发送到工作队列（这里简化为直接处理）
			go s.executeRetry(record)
		}
	}
}

// worker 工作协程
func (s *CallbackRetryScheduler) worker(workerID int) {
	defer s.wg.Done()

	s.logger.Info("重试工作协程已启动", zap.Int("worker_id", workerID))

	for {
		select {
		case <-s.ctx.Done():
			s.logger.Info("重试工作协程已停止", zap.Int("worker_id", workerID))
			return
		default:
			// 这里可以从队列中获取任务
			// 当前简化实现，实际可以使用channel或其他队列机制
			time.Sleep(1 * time.Second)
		}
	}
}

// executeRetry 执行重试
func (s *CallbackRetryScheduler) executeRetry(retryRecord *model.CallbackRetryRecord) {
	startTime := time.Now()
	s.metrics.ActiveWorkers++
	defer func() {
		s.metrics.ActiveWorkers--
		s.metrics.TotalExecuted++
		s.metrics.LastExecuteTime = time.Now()

		duration := time.Since(startTime)
		s.metrics.AverageLatencyMs = (s.metrics.AverageLatencyMs + duration.Milliseconds()) / 2
	}()

	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	s.logger.Info("开始执行重试",
		zap.String("retry_record_id", retryRecord.ID.String()),
		zap.String("user_id", retryRecord.UserID),
		zap.Int("retry_attempt", retryRecord.RetryAttempt))

	// 获取转发记录
	forwardRecord, err := s.callbackRepository.GetForwardRecord(ctx, retryRecord.ForwardRecordID)
	if err != nil {
		s.updateRetryRecordAsFailed(ctx, retryRecord, fmt.Sprintf("获取转发记录失败: %v", err))
		return
	}

	// 获取回调记录
	callbackRecord, err := s.callbackRepository.GetCallbackRecord(ctx, retryRecord.CallbackRecordID)
	if err != nil {
		s.updateRetryRecordAsFailed(ctx, retryRecord, fmt.Sprintf("获取回调记录失败: %v", err))
		return
	}

	// 解析标准化数据
	var standardizedData *model.StandardizedCallbackData
	if err := json.Unmarshal(callbackRecord.StandardizedData, &standardizedData); err != nil {
		s.updateRetryRecordAsFailed(ctx, retryRecord, fmt.Sprintf("解析标准化数据失败: %v", err))
		return
	}

	// 执行转发
	err = s.forwarder.Forward(ctx, callbackRecord, standardizedData)

	// 更新重试记录
	retryRecord.ExecutionDurationMs = int(time.Since(startTime).Milliseconds())
	retryRecord.UpdatedAt = util.NowBeijing()

	if err != nil {
		// 重试失败
		s.updateRetryRecordAsFailed(ctx, retryRecord, err.Error())

		// 更新转发记录的重试次数
		forwardRecord.RetryCount++
		now := util.NowBeijing()
		forwardRecord.LastRetryAt = &now
		s.callbackRepository.UpdateForwardRecord(ctx, forwardRecord)

		// 调度下次重试
		if err := s.ScheduleRetry(ctx, forwardRecord, callbackRecord); err != nil {
			s.logger.Error("调度下次重试失败", zap.Error(err))
		}

		s.metrics.TotalFailed++
	} else {
		// 重试成功
		retryRecord.Status = model.RetryStatusSuccess
		s.callbackRepository.UpdateRetryRecord(ctx, retryRecord)

		// 更新转发记录状态
		forwardRecord.Status = model.CallbackStatusSuccess
		forwardRecord.RetryEnabled = false
		forwardRecord.NextRetryAt = nil
		s.callbackRepository.UpdateForwardRecord(ctx, forwardRecord)

		// 更新回调记录状态
		callbackRecord.ExternalStatus = model.CallbackStatusSuccess
		s.callbackRepository.UpdateCallbackRecord(ctx, callbackRecord)

		s.metrics.TotalSucceeded++

		s.logger.Info("重试执行成功",
			zap.String("retry_record_id", retryRecord.ID.String()),
			zap.String("user_id", retryRecord.UserID),
			zap.Int("retry_attempt", retryRecord.RetryAttempt))
	}
}

// updateRetryRecordAsFailed 更新重试记录为失败状态
func (s *CallbackRetryScheduler) updateRetryRecordAsFailed(ctx context.Context, retryRecord *model.CallbackRetryRecord, errorMessage string) {
	retryRecord.Status = model.RetryStatusFailed
	retryRecord.ErrorMessage = errorMessage
	retryRecord.UpdatedAt = util.NowBeijing()

	if err := s.callbackRepository.UpdateRetryRecord(ctx, retryRecord); err != nil {
		s.logger.Error("更新重试记录失败", zap.Error(err))
	}

	s.logger.Error("重试执行失败",
		zap.String("retry_record_id", retryRecord.ID.String()),
		zap.String("user_id", retryRecord.UserID),
		zap.Int("retry_attempt", retryRecord.RetryAttempt),
		zap.String("error", errorMessage))
}
