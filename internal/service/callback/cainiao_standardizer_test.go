package callback

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap/zaptest"

	"github.com/your-org/go-kuaidi/internal/model"
)

// TestCallbackStandardizer_StandardizeCainiao 测试菜鸟回调标准化
func TestCallbackStandardizer_StandardizeCainiao(t *testing.T) {
	logger := zaptest.NewLogger(t)
	standardizer := NewCallbackStandardizer(nil, logger)

	tests := []struct {
		name           string
		parsedData     *model.ParsedCallbackData
		expectedEvent  string
		expectedStatus string
		expectError    bool
		errorContains  string
	}{
		{
			name: "CREATE_ORDER事件标准化",
			parsedData: &model.ParsedCallbackData{
				Type:            "order_status_changed",
				CustomerOrderNo: "test-order",
				TrackingNo:      "test-tracking",
				Data: map[string]any{
					"event_type": "CREATE_ORDER",
					"order_event": map[string]any{
						"eventData": map[string]any{
							"mailNo": "test-tracking",
						},
					},
				},
				Timestamp: time.Now(),
			},
			expectedEvent:  model.EventTypeOrderStatusChanged,
			expectedStatus: model.OrderStatusSubmitted,
			expectError:    false,
		},
		{
			name: "SEEK_DELIVERY_SUCCESS事件标准化",
			parsedData: &model.ParsedCallbackData{
				Type:            "order_status_changed",
				CustomerOrderNo: "test-order",
				TrackingNo:      "test-tracking",
				Data: map[string]any{
					"event_type": "SEEK_DELIVERY_SUCCESS",
					"order_event": map[string]any{
						"eventData": map[string]any{
							"courierName":    "测试快递员",
							"courierMobile":  "13800138000",
							"courierCompany": "测试快递",
						},
					},
				},
				Timestamp: time.Now(),
			},
			expectedEvent:  model.EventTypeOrderStatusChanged,
			expectedStatus: model.OrderStatusAssigned,
			expectError:    false,
		},
		{
			name: "COURIER_CHECK_BILL_SUCCESS事件标准化",
			parsedData: &model.ParsedCallbackData{
				Type:            "order_status_changed",
				CustomerOrderNo: "test-order",
				TrackingNo:      "test-tracking",
				Data: map[string]any{
					"event_type": "COURIER_CHECK_BILL_SUCCESS",
					"order_event": map[string]any{
						"eventData": map[string]any{
							"basePrice": "480",  // 4.8元，菜鸟以分为单位
							"weight":    "1000", // 1000克 = 1千克
						},
					},
				},
				Timestamp: time.Now(),
			},
			expectedEvent: model.EventTypeBillingUpdated,
			expectError:   false,
		},
		{
			name: "SIGN事件标准化",
			parsedData: &model.ParsedCallbackData{
				Type:            "order_status_changed",
				CustomerOrderNo: "test-order",
				TrackingNo:      "test-tracking",
				Data: map[string]any{
					"event_type": "SIGN",
					"order_event": map[string]any{
						"eventData": map[string]any{
							"mailNo":                "test-tracking",
							"logisticsAction":       "TMS_SIGN",
							"logisticsStatus":       "SIGN",
							"logisticsStandardDesc": "已签收",
						},
					},
				},
				Timestamp: time.Now(),
			},
			expectedEvent:  model.EventTypeOrderStatusChanged,
			expectedStatus: model.OrderStatusDelivered,
			expectError:    false,
		},
		{
			name: "ACCEPT事件标准化",
			parsedData: &model.ParsedCallbackData{
				Type:            "order_status_changed",
				CustomerOrderNo: "test-order",
				TrackingNo:      "test-tracking",
				Data: map[string]any{
					"event_type": "ACCEPT",
					"order_event": map[string]any{
						"eventData": map[string]any{
							"mailNo": "test-tracking",
						},
					},
				},
				Timestamp: time.Now(),
			},
			expectedEvent:  model.EventTypeOrderStatusChanged,
			expectedStatus: model.OrderStatusPickedUp,
			expectError:    false,
		},
		{
			name: "TRANSPORT事件标准化",
			parsedData: &model.ParsedCallbackData{
				Type:            "order_status_changed",
				CustomerOrderNo: "test-order",
				TrackingNo:      "test-tracking",
				Data: map[string]any{
					"event_type": "TRANSPORT",
					"order_event": map[string]any{
						"eventData": map[string]any{
							"mailNo": "test-tracking",
						},
					},
				},
				Timestamp: time.Now(),
			},
			expectedEvent:  model.EventTypeOrderStatusChanged,
			expectedStatus: model.OrderStatusInTransit,
			expectError:    false,
		},
		{
			name: "DELIVERING事件标准化",
			parsedData: &model.ParsedCallbackData{
				Type:            "order_status_changed",
				CustomerOrderNo: "test-order",
				TrackingNo:      "test-tracking",
				Data: map[string]any{
					"event_type": "DELIVERING",
					"order_event": map[string]any{
						"eventData": map[string]any{
							"mailNo": "test-tracking",
						},
					},
				},
				Timestamp: time.Now(),
			},
			expectedEvent:  model.EventTypeOrderStatusChanged,
			expectedStatus: model.OrderStatusOutForDelivery,
			expectError:    false,
		},
		{
			name: "未知事件类型",
			parsedData: &model.ParsedCallbackData{
				Type:            "order_status_changed",
				CustomerOrderNo: "test-order",
				TrackingNo:      "test-tracking",
				Data: map[string]any{
					"event_type": "UNKNOWN_EVENT",
					"order_event": map[string]any{
						"eventData": map[string]any{},
					},
				},
				Timestamp: time.Now(),
			},
			expectedEvent:  model.EventTypeOrderStatusChanged,
			expectedStatus: model.OrderStatusException,
			expectError:    false,
		},
		{
			name: "数据格式错误",
			parsedData: &model.ParsedCallbackData{
				Type:            "order_status_changed",
				CustomerOrderNo: "test-order",
				TrackingNo:      "test-tracking",
				Data:            "invalid data type",
				Timestamp:       time.Now(),
			},
			expectError:   true,
			errorContains: "菜鸟裹裹回调数据格式错误",
		},
		{
			name: "缺少事件类型",
			parsedData: &model.ParsedCallbackData{
				Type:            "order_status_changed",
				CustomerOrderNo: "test-order",
				TrackingNo:      "test-tracking",
				Data: map[string]any{
					"order_event": map[string]any{},
				},
				Timestamp: time.Now(),
			},
			expectError:   true,
			errorContains: "菜鸟裹裹事件类型不能为空",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := standardizer.standardizeCainiao(tt.parsedData)

			if tt.expectError {
				assert.Error(t, err)
				if tt.errorContains != "" {
					assert.Contains(t, err.Error(), tt.errorContains)
				}
				return
			}

			require.NoError(t, err)
			require.NotNil(t, result)

			// 验证基本字段
			assert.Equal(t, tt.expectedEvent, result.EventType)
			assert.Equal(t, tt.parsedData.CustomerOrderNo, result.CustomerOrderNo)
			assert.Equal(t, tt.parsedData.TrackingNo, result.TrackingNo)
			assert.Equal(t, model.CallbackProviderCainiao, result.Provider)

			// 验证状态相关字段
			if tt.expectedStatus != "" {
				statusData, ok := result.Data.(*model.OrderStatusChangedData)
				require.True(t, ok, "应该是OrderStatusChangedData类型")
				assert.Equal(t, tt.expectedStatus, statusData.NewStatus)
			}

			// 验证计费相关字段
			if tt.expectedEvent == model.EventTypeBillingUpdated {
				billingData, ok := result.Data.(*model.BillingUpdatedData)
				require.True(t, ok, "应该是BillingUpdatedData类型")
				assert.Greater(t, billingData.TotalFee, 0.0)
				assert.Greater(t, billingData.Weight, 0.0)
			}
		})
	}
}

// TestCallbackStandardizer_StandardizeCainiaoBillingCalculation 测试菜鸟计费计算
func TestCallbackStandardizer_StandardizeCainiaoBillingCalculation(t *testing.T) {
	logger := zaptest.NewLogger(t)
	standardizer := NewCallbackStandardizer(nil, logger)

	tests := []struct {
		name         string
		basePrice    string
		weight       string
		expectedFee  float64
		expectedWeight float64
	}{
		{
			name:           "正常计费数据",
			basePrice:      "480",  // 4.8元
			weight:         "1000", // 1千克
			expectedFee:    4.8,
			expectedWeight: 1.0,
		},
		{
			name:           "零费用",
			basePrice:      "0",
			weight:         "500",
			expectedFee:    0.0,
			expectedWeight: 0.5,
		},
		{
			name:           "大重量",
			basePrice:      "1200", // 12元
			weight:         "5000", // 5千克
			expectedFee:    12.0,
			expectedWeight: 5.0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			parsedData := &model.ParsedCallbackData{
				Type:            "order_status_changed",
				CustomerOrderNo: "test-order",
				TrackingNo:      "test-tracking",
				Data: map[string]any{
					"event_type": "COURIER_CHECK_BILL_SUCCESS",
					"order_event": map[string]any{
						"eventData": map[string]any{
							"basePrice": tt.basePrice,
							"weight":    tt.weight,
						},
					},
				},
				Timestamp: time.Now(),
			}

			result, err := standardizer.standardizeCainiao(parsedData)
			require.NoError(t, err)

			billingData, ok := result.Data.(*model.BillingUpdatedData)
			require.True(t, ok)

			assert.Equal(t, tt.expectedFee, billingData.TotalFee)
			assert.Equal(t, tt.expectedWeight, billingData.Weight)
		})
	}
}

// TestCallbackStandardizer_StandardizeCainiaoCourierInfo 测试菜鸟快递员信息提取
func TestCallbackStandardizer_StandardizeCainiaoCourierInfo(t *testing.T) {
	logger := zaptest.NewLogger(t)
	standardizer := NewCallbackStandardizer(nil, logger)

	parsedData := &model.ParsedCallbackData{
		Type:            "order_status_changed",
		CustomerOrderNo: "test-order",
		TrackingNo:      "test-tracking",
		Data: map[string]any{
			"event_type": "SEEK_DELIVERY_SUCCESS",
			"order_event": map[string]any{
				"eventData": map[string]any{
					"courierName":    "张三",
					"courierMobile":  "13800138000",
					"courierCompany": "申通快递",
				},
			},
		},
		Timestamp: time.Now(),
	}

	result, err := standardizer.standardizeCainiao(parsedData)
	require.NoError(t, err)

	statusData, ok := result.Data.(*model.OrderStatusChangedData)
	require.True(t, ok)

	require.NotNil(t, statusData.CourierInfo)
	assert.Equal(t, "张三", statusData.CourierInfo.Name)
	assert.Equal(t, "13800138000", statusData.CourierInfo.Phone)
}
