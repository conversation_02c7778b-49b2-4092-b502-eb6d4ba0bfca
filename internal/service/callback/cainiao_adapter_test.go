package callback

import (
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/your-org/go-kuaidi/internal/constants"
	"github.com/your-org/go-kuaidi/internal/model"
)

// TestCainiaoCallbackAdapter_ParseCallback 测试菜鸟回调数据解析
func TestCainiaoCallbackAdapter_ParseCallback(t *testing.T) {
	adapter := NewCainiaoCallbackAdapter("test-access-code")

	tests := []struct {
		name          string
		rawData       string
		expectedType  string
		expectedOrder string
		expectedTrack string
		expectError   bool
		errorContains string
	}{
		{
			name: "CREATE_ORDER事件解析",
			rawData: `{
				"logistics_interface": "{\"externalOrder\":{\"externalBizIdList\":[\"8867128_157\"],\"orderId\":\"18071048886198104\",\"orderStatusDesc\":\"下单成功\",\"orderStatusCode\":\"0\"},\"orderEvent\":{\"eventDesc\":\"创建订单\",\"eventData\":{\"mailNo\":\"773366734234405\",\"itemId\":\"3000000080\"},\"eventType\":\"CREATE_ORDER\"}}",
				"data_digest": "HbP63x+LvAwKieDcUU5l3w==",
				"partner_code": "20df7df0fa15494c801b249a8b798879",
				"from_code": "tdtradebusiness",
				"msg_type": "GUOGUO_PUSH_ORDER_EVENT",
				"msg_id": "18071048886198104"
			}`,
			expectedType:  constants.EventTypeOrderStatusChanged,
			expectedOrder: "8867128_157",
			expectedTrack: "773366734234405",
			expectError:   false,
		},
		{
			name: "SEEK_DELIVERY_SUCCESS事件解析",
			rawData: `{
				"logistics_interface": "{\"externalOrder\":{\"externalBizIdList\":[\"8867128_157\"],\"orderId\":\"18071048886198104\",\"orderStatusDesc\":\"待取件\",\"orderStatusCode\":\"20\"},\"orderEvent\":{\"eventDesc\":\"寻求运力成功\",\"eventData\":{\"courierName\":\"申通小件员\",\"courierMobile\":\"95543\"},\"eventType\":\"SEEK_DELIVERY_SUCCESS\"}}",
				"data_digest": "EqFI42QcYX5cN93k10ZY0A==",
				"partner_code": "20df7df0fa15494c801b249a8b798879",
				"from_code": "tdtradebusiness",
				"msg_type": "GUOGUO_PUSH_ORDER_EVENT",
				"msg_id": "18071048886198104"
			}`,
			expectedType:  constants.EventTypeOrderStatusChanged,
			expectedOrder: "8867128_157",
			expectedTrack: "",
			expectError:   false,
		},
		{
			name: "SIGN事件解析",
			rawData: `{
				"logistics_interface": "{\"externalOrder\":{\"externalBizIdList\":[\"8867128_157\"],\"orderId\":\"18071048886198104\",\"orderStatusDesc\":\"已完结\",\"orderStatusCode\":\"40\"},\"orderEvent\":{\"eventDesc\":\"已签收\",\"eventData\":{\"mailNo\":\"773366734234405\",\"logisticsAction\":\"TMS_SIGN\",\"logisticsStatus\":\"SIGN\"},\"eventType\":\"SIGN\"}}",
				"data_digest": "+JcocL2qt8BvNpUADLM+Tw==",
				"partner_code": "20df7df0fa15494c801b249a8b798879",
				"from_code": "tdtradebusiness",
				"msg_type": "GUOGUO_PUSH_ORDER_EVENT",
				"msg_id": "18071048886198104"
			}`,
			expectedType:  constants.EventTypeOrderStatusChanged,
			expectedOrder: "8867128_157",
			expectedTrack: "773366734234405",
			expectError:   false,
		},
		{
			name: "空的logistics_interface",
			rawData: `{
				"logistics_interface": "",
				"data_digest": "test",
				"msg_id": "test"
			}`,
			expectError:   true,
			errorContains: "logistics_interface字段不能为空",
		},
		{
			name: "缺少data_digest",
			rawData: `{
				"logistics_interface": "{\"orderEvent\":{\"eventType\":\"TEST\"}}",
				"msg_id": "test"
			}`,
			expectError:   true,
			errorContains: "data_digest字段不能为空",
		},
		{
			name: "缺少msg_id",
			rawData: `{
				"logistics_interface": "{\"orderEvent\":{\"eventType\":\"TEST\"}}",
				"data_digest": "test"
			}`,
			expectError:   true,
			errorContains: "msg_id字段不能为空",
		},
		{
			name:          "无效的JSON格式",
			rawData:       `{invalid json}`,
			expectError:   true,
			errorContains: "解析菜鸟裹裹外层回调数据失败",
		},
		{
			name: "无效的logistics_interface JSON",
			rawData: `{
				"logistics_interface": "{invalid json}",
				"data_digest": "test",
				"msg_id": "test"
			}`,
			expectError:   true,
			errorContains: "解析菜鸟裹裹logistics_interface内容失败",
		},
		{
			name: "缺少orderEvent",
			rawData: `{
				"logistics_interface": "{\"externalOrder\":{}}",
				"data_digest": "test",
				"msg_id": "test"
			}`,
			expectError:   true,
			errorContains: "事件类型不能为空",
		},
		{
			name: "缺少externalOrder",
			rawData: `{
				"logistics_interface": "{\"orderEvent\":{\"eventType\":\"TEST\"}}",
				"data_digest": "test",
				"msg_id": "test"
			}`,
			expectError:   true,
			errorContains: "外部订单信息不能为空",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := adapter.ParseCallback([]byte(tt.rawData))

			if tt.expectError {
				assert.Error(t, err)
				if tt.errorContains != "" {
					assert.Contains(t, err.Error(), tt.errorContains)
				}
				return
			}

			require.NoError(t, err)
			require.NotNil(t, result)

			// 验证基本字段
			assert.Equal(t, tt.expectedType, result.Type)
			assert.Equal(t, tt.expectedOrder, result.CustomerOrderNo)
			assert.Equal(t, tt.expectedTrack, result.TrackingNo)

			// 验证时间戳
			assert.WithinDuration(t, time.Now(), result.Timestamp, time.Minute)

			// 验证数据结构
			assert.NotNil(t, result.Data)
			dataMap, ok := result.Data.(map[string]any)
			require.True(t, ok, "Data应该是map[string]any类型")

			// 验证幂等性字段
			assert.Contains(t, dataMap, "idempotency_key")
			assert.Contains(t, dataMap, "raw_data_hash")
			assert.NotEmpty(t, dataMap["idempotency_key"])
			assert.NotEmpty(t, dataMap["raw_data_hash"])
		})
	}
}

// TestCainiaoCallbackAdapter_BuildResponse 测试菜鸟回调响应构建
func TestCainiaoCallbackAdapter_BuildResponse(t *testing.T) {
	adapter := NewCainiaoCallbackAdapter("test-access-code")

	standardizedData := &model.StandardizedCallbackData{
		EventType:       model.EventTypeOrderStatusChanged,
		OrderNo:         "test-order",
		CustomerOrderNo: "test-customer-order",
		TrackingNo:      "test-tracking",
		Provider:        model.CallbackProviderCainiao,
		Timestamp:       time.Now(),
		Data: &model.OrderStatusChangedData{
			NewStatus:  model.OrderStatusSubmitted,
			StatusDesc: "测试状态",
			UpdateTime: time.Now(),
		},
	}

	response := adapter.BuildResponse(standardizedData)

	require.NotNil(t, response)
	assert.True(t, response.Success)
	assert.Equal(t, constants.HTTPStatusOK, response.Code)
	assert.Equal(t, constants.MessageReceived, response.Message)

	// 验证响应数据结构
	assert.NotNil(t, response.Data)
	dataMap, ok := response.Data.(map[string]any)
	require.True(t, ok)
	assert.Equal(t, true, dataMap["success"])
	assert.Equal(t, constants.MessageReceived, dataMap["message"])
	assert.Equal(t, constants.HTTPStatusOK, dataMap["code"])
}

// TestCainiaoCallbackAdapter_calculateDataHash 测试数据哈希计算
func TestCainiaoCallbackAdapter_calculateDataHash(t *testing.T) {
	adapter := NewCainiaoCallbackAdapter("test-access-code")

	// 需要类型断言来访问私有方法
	cainiaoAdapter, ok := adapter.(*CainiaoCallbackAdapter)
	require.True(t, ok, "适配器应该是CainiaoCallbackAdapter类型")

	testData1 := []byte("test data 1")
	testData2 := []byte("test data 2")
	testData3 := []byte("test data 1") // 与testData1相同

	hash1 := cainiaoAdapter.calculateDataHash(testData1)
	hash2 := cainiaoAdapter.calculateDataHash(testData2)
	hash3 := cainiaoAdapter.calculateDataHash(testData3)

	// 相同数据应该产生相同哈希
	assert.Equal(t, hash1, hash3)
	// 不同数据应该产生不同哈希
	assert.NotEqual(t, hash1, hash2)
	// 哈希应该是64字符的十六进制字符串
	assert.Len(t, hash1, 64)
	assert.Regexp(t, "^[a-f0-9]{64}$", hash1)
}

// TestCainiaoCallbackAdapter_EventTypeMapping 测试事件类型映射
func TestCainiaoCallbackAdapter_EventTypeMapping(t *testing.T) {
	adapter := NewCainiaoCallbackAdapter("test-access-code")

	eventTypeMappings := map[string]string{
		"CREATE_ORDER":                   constants.EventTypeOrderStatusChanged,
		"CANCEL_ORDER":                   constants.EventTypeOrderStatusChanged,
		"GOT_SUCCESS":                    constants.EventTypeOrderStatusChanged,
		"UPLOAD_MAIL_NO_SUCCESS":         constants.EventTypeOrderStatusChanged,
		"FINISH_ORDER":                   constants.EventTypeOrderStatusChanged,
		"COURIER_CHECK_BILL_SUCCESS":     constants.EventTypeOrderStatusChanged,
		"CHANGE_DELIVERY_USER_SUCCESS":   constants.EventTypeOrderStatusChanged,
		"MODIFY_EXPECT_GOT_TIME_SUCCESS": constants.EventTypeOrderStatusChanged,
		"PAY_SUCCESS":                    constants.EventTypeOrderStatusChanged,
		"SEEK_DELIVERY_SUCCESS":          constants.EventTypeOrderStatusChanged,
		"OUT_ORDER_COURIER_UPDATE":       constants.EventTypeOrderStatusChanged,
		"ACCEPT":                         constants.EventTypeOrderStatusChanged,
		"TRANSPORT":                      constants.EventTypeOrderStatusChanged,
		"DELIVERING":                     constants.EventTypeOrderStatusChanged,
		"SIGN":                           constants.EventTypeOrderStatusChanged,
		"FAILED":                         constants.EventTypeOrderStatusChanged,
		"BACK":                           constants.EventTypeOrderStatusChanged,
		"REVERSE_RETURN":                 constants.EventTypeOrderStatusChanged,
	}

	for eventType, expectedCallbackType := range eventTypeMappings {
		t.Run(eventType, func(t *testing.T) {
			rawData := fmt.Sprintf(`{
				"logistics_interface": "{\"externalOrder\":{\"externalBizIdList\":[\"test\"],\"orderId\":\"test\"},\"orderEvent\":{\"eventType\":\"%s\"}}",
				"data_digest": "test",
				"msg_id": "test"
			}`, eventType)

			result, err := adapter.ParseCallback([]byte(rawData))
			require.NoError(t, err)
			assert.Equal(t, expectedCallbackType, result.Type)
		})
	}
}
