package callback

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"github.com/your-org/go-kuaidi/internal/model"
)

// TestSupplierFieldInUnifiedCallbackData 测试UnifiedCallbackData中的供应商字段
func TestSupplierFieldInUnifiedCallbackData(t *testing.T) {
	t.Run("UnifiedCallbackData结构包含供应商字段", func(t *testing.T) {
		// 创建测试数据
		callbackData := &model.UnifiedCallbackData{
			CustomerOrderNo: "CUSTOMER_001",
			OrderNo:         "ORDER_001",
			PlatformOrderNo: "PLATFORM_001",
			TrackingNo:      "TRACK_001",
			Timestamp:       time.Now().Format(time.RFC3339),
			EventType:       model.UnifiedEventTypeStatusUpdate,
			SupplierCode:    "cainiao", // 🔥 测试供应商字段
			Data: &model.UnifiedStatusData{
				Status: model.UnifiedStatus{
					Code: "picked_up",
					Name: "已取件",
				},
				UpdateTime:  time.Now().Format(time.RFC3339),
				Description: "已取件",
			},
		}

		// 验证字段存在且正确
		assert.Equal(t, "cainiao", callbackData.SupplierCode)
		assert.Equal(t, "CUSTOMER_001", callbackData.CustomerOrderNo)
		assert.Equal(t, "ORDER_001", callbackData.OrderNo)
		assert.Equal(t, "PLATFORM_001", callbackData.PlatformOrderNo)
		assert.Equal(t, "TRACK_001", callbackData.TrackingNo)
	})

	t.Run("供应商字段为空时的向后兼容性", func(t *testing.T) {
		// 创建不包含供应商字段的数据（模拟旧版本）
		callbackData := &model.UnifiedCallbackData{
			CustomerOrderNo: "CUSTOMER_002",
			OrderNo:         "ORDER_002",
			PlatformOrderNo: "PLATFORM_002",
			TrackingNo:      "TRACK_002",
			Timestamp:       time.Now().Format(time.RFC3339),
			EventType:       model.UnifiedEventTypeStatusUpdate,
			// SupplierCode 故意不设置，测试向后兼容性
			Data: &model.UnifiedStatusData{
				Status: model.UnifiedStatus{
					Code: "in_transit",
					Name: "运输中",
				},
				UpdateTime:  time.Now().Format(time.RFC3339),
				Description: "运输中",
			},
		}

		// 验证空供应商字段不影响其他功能
		assert.Empty(t, callbackData.SupplierCode)
		assert.Equal(t, "CUSTOMER_002", callbackData.CustomerOrderNo)
		assert.Equal(t, "ORDER_002", callbackData.OrderNo)
		assert.Equal(t, "PLATFORM_002", callbackData.PlatformOrderNo)
		assert.Equal(t, "TRACK_002", callbackData.TrackingNo)
	})
}
