package callback

import (
	"context"
	"fmt"
	"net"
	"net/http"
	"sync"
	"time"

	"github.com/google/uuid"
	"go.uber.org/zap"

	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/repository"
	"github.com/your-org/go-kuaidi/internal/service"
)

// RetryServiceManager 重试服务管理器
// 🎯 核心功能：管理重试调度器的生命周期，提供统一的服务接口
type RetryServiceManager struct {
	scheduler          *CallbackRetryScheduler
	forwarder          *UnifiedCallbackForwarder
	callbackRepository repository.CallbackRepository
	configService      service.SystemConfigService
	logger             *zap.Logger

	// 生命周期管理
	isInitialized bool
	isStarted     bool
	mu            sync.RWMutex
}

// NewRetryServiceManager 创建重试服务管理器
func NewRetryServiceManager(
	callbackRepository repository.CallbackRepository,
	configService service.SystemConfigService,
	logger *zap.Logger,
) *RetryServiceManager {
	return &RetryServiceManager{
		callbackRepository: callbackRepository,
		configService:      configService,
		logger:             logger,
	}
}

// Initialize 初始化重试服务
func (m *RetryServiceManager) Initialize() error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.isInitialized {
		return fmt.Errorf("重试服务已初始化")
	}

	// 1. 创建优化的HTTP客户端 - 专门用于重试场景
	transport := &http.Transport{
		MaxIdleConns:          50,
		MaxIdleConnsPerHost:   20,
		MaxConnsPerHost:       30,
		IdleConnTimeout:       60 * time.Second,
		TLSHandshakeTimeout:   10 * time.Second,
		ResponseHeaderTimeout: 30 * time.Second,
		ExpectContinueTimeout: 1 * time.Second,
		DialContext: (&net.Dialer{
			Timeout:   10 * time.Second,
			KeepAlive: 30 * time.Second,
		}).DialContext,
		DisableCompression: false,
		ForceAttemptHTTP2:  true,
	}

	httpClient := &http.Client{
		Transport: transport,
		Timeout:   60 * time.Second, // 重试场景使用更长的超时时间
	}

	// 2. 创建统一回调转发器
	m.forwarder = NewUnifiedCallbackForwarder(
		m.callbackRepository,
		httpClient,
		m.configService,
		m.logger,
	)

	// 3. 创建重试调度器
	m.scheduler = NewCallbackRetryScheduler(
		m.callbackRepository,
		m.configService,
		m.forwarder,
		m.logger,
	)

	// 4. 设置循环依赖
	m.forwarder.SetRetryScheduler(m.scheduler)

	m.isInitialized = true

	m.logger.Info("重试服务管理器初始化完成")
	return nil
}

// Start 启动重试服务
func (m *RetryServiceManager) Start() error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if !m.isInitialized {
		return fmt.Errorf("重试服务未初始化，请先调用Initialize()")
	}

	if m.isStarted {
		return fmt.Errorf("重试服务已启动")
	}

	// 启动重试调度器
	if err := m.scheduler.Start(); err != nil {
		return fmt.Errorf("启动重试调度器失败: %w", err)
	}

	m.isStarted = true

	m.logger.Info("重试服务已启动")
	return nil
}

// Stop 停止重试服务
func (m *RetryServiceManager) Stop() error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if !m.isStarted {
		return nil
	}

	// 停止重试调度器
	if err := m.scheduler.Stop(); err != nil {
		m.logger.Error("停止重试调度器失败", zap.Error(err))
		return err
	}

	m.isStarted = false

	m.logger.Info("重试服务已停止")
	return nil
}

// GetScheduler 获取重试调度器
func (m *RetryServiceManager) GetScheduler() *CallbackRetryScheduler {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.scheduler
}

// GetForwarder 获取回调转发器
func (m *RetryServiceManager) GetForwarder() *UnifiedCallbackForwarder {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.forwarder
}

// IsRunning 检查服务是否运行中
func (m *RetryServiceManager) IsRunning() bool {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.isStarted && m.scheduler != nil && m.scheduler.IsRunning()
}

// GetStatus 获取服务状态
func (m *RetryServiceManager) GetStatus() *ServiceStatus {
	m.mu.RLock()
	defer m.mu.RUnlock()

	status := &ServiceStatus{
		Initialized: m.isInitialized,
		Started:     m.isStarted,
		Running:     false,
		Metrics:     nil,
	}

	if m.scheduler != nil {
		status.Running = m.scheduler.IsRunning()
		status.Metrics = m.scheduler.GetMetrics()
	}

	return status
}

// ScheduleRetry 手动调度重试（对外接口）
func (m *RetryServiceManager) ScheduleRetry(ctx context.Context, forwardRecordID, callbackRecordID string) error {
	m.mu.RLock()
	scheduler := m.scheduler
	m.mu.RUnlock()

	if scheduler == nil {
		return fmt.Errorf("重试调度器未初始化")
	}

	// 获取转发记录
	forwardUUID, err := parseUUID(forwardRecordID)
	if err != nil {
		return fmt.Errorf("无效的转发记录ID: %w", err)
	}

	forwardRecord, err := m.callbackRepository.GetForwardRecord(ctx, forwardUUID)
	if err != nil {
		return fmt.Errorf("获取转发记录失败: %w", err)
	}

	// 获取回调记录
	callbackUUID, err := parseUUID(callbackRecordID)
	if err != nil {
		return fmt.Errorf("无效的回调记录ID: %w", err)
	}

	callbackRecord, err := m.callbackRepository.GetCallbackRecord(ctx, callbackUUID)
	if err != nil {
		return fmt.Errorf("获取回调记录失败: %w", err)
	}

	// 调度重试
	return scheduler.ScheduleRetry(ctx, forwardRecord, callbackRecord)
}

// CancelRetry 取消重试（对外接口）
func (m *RetryServiceManager) CancelRetry(ctx context.Context, forwardRecordID string) error {
	forwardUUID, err := parseUUID(forwardRecordID)
	if err != nil {
		return fmt.Errorf("无效的转发记录ID: %w", err)
	}

	return m.callbackRepository.CancelPendingRetryRecords(ctx, forwardUUID)
}

// ServiceStatus 服务状态
type ServiceStatus struct {
	Initialized bool                `json:"initialized"`
	Started     bool                `json:"started"`
	Running     bool                `json:"running"`
	Metrics     *model.RetryMetrics `json:"metrics,omitempty"`
}

// parseUUID 解析UUID字符串
func parseUUID(uuidStr string) (uuid.UUID, error) {
	return uuid.Parse(uuidStr)
}

// 🔥 全局重试服务管理器实例
var (
	globalRetryServiceManager *RetryServiceManager
	globalRetryServiceOnce    sync.Once
)

// GetGlobalRetryServiceManager 获取全局重试服务管理器
func GetGlobalRetryServiceManager() *RetryServiceManager {
	return globalRetryServiceManager
}

// InitializeGlobalRetryService 初始化全局重试服务
func InitializeGlobalRetryService(
	callbackRepository repository.CallbackRepository,
	configService service.SystemConfigService,
	logger *zap.Logger,
) error {
	var err error
	globalRetryServiceOnce.Do(func() {
		globalRetryServiceManager = NewRetryServiceManager(
			callbackRepository,
			configService,
			logger,
		)
		err = globalRetryServiceManager.Initialize()
	})
	return err
}

// StartGlobalRetryService 启动全局重试服务
func StartGlobalRetryService() error {
	if globalRetryServiceManager == nil {
		return fmt.Errorf("全局重试服务未初始化")
	}
	return globalRetryServiceManager.Start()
}

// StopGlobalRetryService 停止全局重试服务
func StopGlobalRetryService() error {
	if globalRetryServiceManager == nil {
		return nil
	}
	return globalRetryServiceManager.Stop()
}

// 🔥 优雅关闭处理
type GracefulShutdownHandler struct {
	manager *RetryServiceManager
	logger  *zap.Logger
}

// NewGracefulShutdownHandler 创建优雅关闭处理器
func NewGracefulShutdownHandler(manager *RetryServiceManager, logger *zap.Logger) *GracefulShutdownHandler {
	return &GracefulShutdownHandler{
		manager: manager,
		logger:  logger,
	}
}

// Shutdown 执行优雅关闭
func (h *GracefulShutdownHandler) Shutdown(ctx context.Context) error {
	h.logger.Info("开始优雅关闭重试服务...")

	// 创建一个带超时的上下文
	shutdownCtx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	// 在goroutine中执行关闭操作
	done := make(chan error, 1)
	go func() {
		done <- h.manager.Stop()
	}()

	// 等待关闭完成或超时
	select {
	case err := <-done:
		if err != nil {
			h.logger.Error("重试服务关闭失败", zap.Error(err))
			return err
		}
		h.logger.Info("重试服务已优雅关闭")
		return nil
	case <-shutdownCtx.Done():
		h.logger.Warn("重试服务关闭超时")
		return fmt.Errorf("重试服务关闭超时")
	}
}
