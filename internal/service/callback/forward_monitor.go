package callback

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/your-org/go-kuaidi/internal/repository"
	"github.com/your-org/go-kuaidi/internal/util"
)

// ForwardMonitor 回调转发监控器
type ForwardMonitor struct {
	logger             *zap.Logger
	callbackRepository repository.CallbackRepository
	db                 *sql.DB
	alertThreshold     time.Duration // 告警阈值：回调记录多长时间未转发触发告警
	checkInterval      time.Duration // 检查间隔
}

// NewForwardMonitor 创建回调转发监控器
func NewForwardMonitor(
	logger *zap.Logger,
	callbackRepository repository.CallbackRepository,
	db *sql.DB,
) *ForwardMonitor {
	return &ForwardMonitor{
		logger:             logger,
		callbackRepository: callbackRepository,
		db:                 db,
		alertThreshold:     5 * time.Minute, // 5分钟未转发触发告警
		checkInterval:      1 * time.Minute, // 每分钟检查一次
	}
}

// ForwardIntegrityReport 转发完整性报告
type ForwardIntegrityReport struct {
	CheckTime           time.Time                    `json:"check_time"`
	TotalCallbacks      int                          `json:"total_callbacks"`
	ForwardedCallbacks  int                          `json:"forwarded_callbacks"`
	MissingForwards     int                          `json:"missing_forwards"`
	MissingRecords      []*MissingForwardRecord      `json:"missing_records"`
	DelayedForwards     []*DelayedForwardRecord      `json:"delayed_forwards"`
	FailedForwards      []*FailedForwardRecord       `json:"failed_forwards"`
	Summary             *ForwardIntegritySummary     `json:"summary"`
}

// MissingForwardRecord 遗漏转发记录
type MissingForwardRecord struct {
	CallbackID      string    `json:"callback_id"`
	Provider        string    `json:"provider"`
	EventType       string    `json:"event_type"`
	OrderNo         string    `json:"order_no"`
	CustomerOrderNo string    `json:"customer_order_no"`
	TrackingNo      string    `json:"tracking_no"`
	UserID          string    `json:"user_id"`
	ReceivedAt      time.Time `json:"received_at"`
	DelayMinutes    int       `json:"delay_minutes"`
}

// DelayedForwardRecord 延迟转发记录
type DelayedForwardRecord struct {
	CallbackID      string    `json:"callback_id"`
	ForwardID       string    `json:"forward_id"`
	Provider        string    `json:"provider"`
	EventType       string    `json:"event_type"`
	OrderNo         string    `json:"order_no"`
	UserID          string    `json:"user_id"`
	ReceivedAt      time.Time `json:"received_at"`
	ForwardedAt     time.Time `json:"forwarded_at"`
	DelayMinutes    int       `json:"delay_minutes"`
}

// FailedForwardRecord 失败转发记录
type FailedForwardRecord struct {
	CallbackID      string    `json:"callback_id"`
	ForwardID       string    `json:"forward_id"`
	Provider        string    `json:"provider"`
	EventType       string    `json:"event_type"`
	OrderNo         string    `json:"order_no"`
	UserID          string    `json:"user_id"`
	ReceivedAt      time.Time `json:"received_at"`
	ErrorMessage    string    `json:"error_message"`
	HTTPStatus      int       `json:"http_status"`
	RetryCount      int       `json:"retry_count"`
}

// ForwardIntegritySummary 转发完整性摘要
type ForwardIntegritySummary struct {
	ForwardSuccessRate    float64 `json:"forward_success_rate"`
	AverageForwardDelay   float64 `json:"average_forward_delay_minutes"`
	CriticalIssuesCount   int     `json:"critical_issues_count"`
	RecommendedActions    []string `json:"recommended_actions"`
}

// StartMonitoring 启动监控
func (m *ForwardMonitor) StartMonitoring(ctx context.Context) {
	m.logger.Info("🔍 启动回调转发监控",
		zap.Duration("check_interval", m.checkInterval),
		zap.Duration("alert_threshold", m.alertThreshold))

	ticker := time.NewTicker(m.checkInterval)
	defer ticker.Stop()

	// 立即执行一次检查
	m.performIntegrityCheck(ctx)

	for {
		select {
		case <-ctx.Done():
			m.logger.Info("🔍 回调转发监控已停止")
			return
		case <-ticker.C:
			m.performIntegrityCheck(ctx)
		}
	}
}

// performIntegrityCheck 执行完整性检查
func (m *ForwardMonitor) performIntegrityCheck(ctx context.Context) {
	report, err := m.GenerateIntegrityReport(ctx)
	if err != nil {
		m.logger.Error("生成转发完整性报告失败", zap.Error(err))
		return
	}

	// 记录检查结果
	m.logger.Info("📊 回调转发完整性检查完成",
		zap.Time("check_time", report.CheckTime),
		zap.Int("total_callbacks", report.TotalCallbacks),
		zap.Int("forwarded_callbacks", report.ForwardedCallbacks),
		zap.Int("missing_forwards", report.MissingForwards),
		zap.Int("delayed_forwards", len(report.DelayedForwards)),
		zap.Int("failed_forwards", len(report.FailedForwards)),
		zap.Float64("success_rate", report.Summary.ForwardSuccessRate))

	// 触发告警
	if report.MissingForwards > 0 || len(report.FailedForwards) > 0 {
		m.triggerAlert(report)
	}
}

// GenerateIntegrityReport 生成转发完整性报告
func (m *ForwardMonitor) GenerateIntegrityReport(ctx context.Context) (*ForwardIntegrityReport, error) {
	now := util.NowBeijing()
	
	// 查询最近1小时的回调记录和转发情况
	query := `
		SELECT 
			ucr.id as callback_id,
			ucr.provider,
			ucr.event_type,
			ucr.order_no,
			ucr.customer_order_no,
			ucr.tracking_no,
			ucr.user_id,
			ucr.received_at,
			ucr.external_status,
			ucr.external_processed_at,
			cfr.id as forward_id,
			cfr.status as forward_status,
			cfr.http_status,
			cfr.error_message,
			cfr.retry_count,
			cfr.response_at
		FROM unified_callback_records ucr
		LEFT JOIN callback_forward_records cfr ON ucr.id = cfr.callback_record_id
		WHERE ucr.received_at >= $1 
		  AND ucr.user_id IS NOT NULL 
		  AND ucr.user_id != ''
		ORDER BY ucr.received_at DESC
	`

	oneHourAgo := now.Add(-1 * time.Hour)
	rows, err := m.db.QueryContext(ctx, query, oneHourAgo)
	if err != nil {
		return nil, fmt.Errorf("查询回调转发数据失败: %w", err)
	}
	defer rows.Close()

	var (
		totalCallbacks     = 0
		forwardedCallbacks = 0
		missingRecords     []*MissingForwardRecord
		delayedForwards    []*DelayedForwardRecord
		failedForwards     []*FailedForwardRecord
	)

	for rows.Next() {
		var (
			callbackID         string
			provider           string
			eventType          string
			orderNo            string
			customerOrderNo    string
			trackingNo         string
			userID             string
			receivedAt         time.Time
			externalStatus     sql.NullString
			externalProcessedAt sql.NullTime
			forwardID          sql.NullString
			forwardStatus      sql.NullString
			httpStatus         sql.NullInt32
			errorMessage       sql.NullString
			retryCount         sql.NullInt32
			responseAt         sql.NullTime
		)

		err := rows.Scan(
			&callbackID, &provider, &eventType, &orderNo, &customerOrderNo,
			&trackingNo, &userID, &receivedAt, &externalStatus, &externalProcessedAt,
			&forwardID, &forwardStatus, &httpStatus, &errorMessage, &retryCount, &responseAt,
		)
		if err != nil {
			m.logger.Error("扫描回调转发数据失败", zap.Error(err))
			continue
		}

		totalCallbacks++

		// 检查是否有转发记录
		if !forwardID.Valid {
			// 遗漏转发
			delayMinutes := int(now.Sub(receivedAt).Minutes())
			missingRecords = append(missingRecords, &MissingForwardRecord{
				CallbackID:      callbackID,
				Provider:        provider,
				EventType:       eventType,
				OrderNo:         orderNo,
				CustomerOrderNo: customerOrderNo,
				TrackingNo:      trackingNo,
				UserID:          userID,
				ReceivedAt:      receivedAt,
				DelayMinutes:    delayMinutes,
			})
		} else {
			forwardedCallbacks++

			// 检查转发状态
			if forwardStatus.Valid && forwardStatus.String == "failed" {
				// 转发失败
				failedForwards = append(failedForwards, &FailedForwardRecord{
					CallbackID:   callbackID,
					ForwardID:    forwardID.String,
					Provider:     provider,
					EventType:    eventType,
					OrderNo:      orderNo,
					UserID:       userID,
					ReceivedAt:   receivedAt,
					ErrorMessage: errorMessage.String,
					HTTPStatus:   int(httpStatus.Int32),
					RetryCount:   int(retryCount.Int32),
				})
			} else if responseAt.Valid {
				// 检查转发延迟
				delayMinutes := int(responseAt.Time.Sub(receivedAt).Minutes())
				if delayMinutes > 2 { // 超过2分钟认为是延迟
					delayedForwards = append(delayedForwards, &DelayedForwardRecord{
						CallbackID:   callbackID,
						ForwardID:    forwardID.String,
						Provider:     provider,
						EventType:    eventType,
						OrderNo:      orderNo,
						UserID:       userID,
						ReceivedAt:   receivedAt,
						ForwardedAt:  responseAt.Time,
						DelayMinutes: delayMinutes,
					})
				}
			}
		}
	}

	// 生成摘要
	summary := m.generateSummary(totalCallbacks, forwardedCallbacks, len(missingRecords), delayedForwards, failedForwards)

	return &ForwardIntegrityReport{
		CheckTime:          now,
		TotalCallbacks:     totalCallbacks,
		ForwardedCallbacks: forwardedCallbacks,
		MissingForwards:    len(missingRecords),
		MissingRecords:     missingRecords,
		DelayedForwards:    delayedForwards,
		FailedForwards:     failedForwards,
		Summary:            summary,
	}, nil
}

// generateSummary 生成摘要
func (m *ForwardMonitor) generateSummary(total, forwarded, missing int, delayed []*DelayedForwardRecord, failed []*FailedForwardRecord) *ForwardIntegritySummary {
	var successRate float64
	if total > 0 {
		successRate = float64(forwarded-len(failed)) / float64(total) * 100
	}

	var avgDelay float64
	if len(delayed) > 0 {
		totalDelay := 0
		for _, d := range delayed {
			totalDelay += d.DelayMinutes
		}
		avgDelay = float64(totalDelay) / float64(len(delayed))
	}

	criticalIssues := missing + len(failed)
	
	var actions []string
	if missing > 0 {
		actions = append(actions, fmt.Sprintf("立即处理 %d 条遗漏转发", missing))
	}
	if len(failed) > 0 {
		actions = append(actions, fmt.Sprintf("重试 %d 条失败转发", len(failed)))
	}
	if len(delayed) > 5 {
		actions = append(actions, "检查系统性能，优化转发速度")
	}
	if successRate < 95 {
		actions = append(actions, "检查转发逻辑和网络连接")
	}

	return &ForwardIntegritySummary{
		ForwardSuccessRate:  successRate,
		AverageForwardDelay: avgDelay,
		CriticalIssuesCount: criticalIssues,
		RecommendedActions:  actions,
	}
}

// triggerAlert 触发告警
func (m *ForwardMonitor) triggerAlert(report *ForwardIntegrityReport) {
	m.logger.Error("🚨 回调转发完整性告警",
		zap.Int("missing_forwards", report.MissingForwards),
		zap.Int("failed_forwards", len(report.FailedForwards)),
		zap.Float64("success_rate", report.Summary.ForwardSuccessRate),
		zap.Strings("recommended_actions", report.Summary.RecommendedActions))

	// TODO: 集成告警系统（如钉钉、企业微信、邮件等）
	// 这里可以添加具体的告警通知逻辑
}
