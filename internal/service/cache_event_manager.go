package service

import (
	"context"
	"sync"
	"time"

	"go.uber.org/zap"
)

// CacheEventType 缓存事件类型
type CacheEventType string

const (
	EventCompanyStatusChanged CacheEventType = "company_status_changed"
	EventMappingChanged       CacheEventType = "mapping_changed"
	EventProviderChanged      CacheEventType = "provider_changed"
)

// CacheEvent 缓存事件
type CacheEvent struct {
	Type        CacheEventType    `json:"type"`
	CompanyCode string            `json:"company_code,omitempty"`
	ProviderCode string           `json:"provider_code,omitempty"`
	Data        map[string]interface{} `json:"data,omitempty"`
	Timestamp   time.Time         `json:"timestamp"`
}

// CacheEventHandler 缓存事件处理器接口
type CacheEventHandler interface {
	HandleCacheEvent(ctx context.Context, event CacheEvent) error
	GetHandlerName() string
}

// CacheEventManager 缓存事件管理器
type CacheEventManager struct {
	handlers map[string][]CacheEventHandler
	eventCh  chan CacheEvent
	logger   *zap.Logger
	mu       sync.RWMutex
	stopCh   chan struct{}
	wg       sync.WaitGroup
}

// NewCacheEventManager 创建缓存事件管理器
func NewCacheEventManager(logger *zap.Logger) *CacheEventManager {
	manager := &CacheEventManager{
		handlers: make(map[string][]CacheEventHandler),
		eventCh:  make(chan CacheEvent, 1000), // 缓冲1000个事件
		logger:   logger,
		stopCh:   make(chan struct{}),
	}
	
	// 启动事件处理协程
	manager.start()
	
	return manager
}

// RegisterHandler 注册事件处理器
func (m *CacheEventManager) RegisterHandler(eventType CacheEventType, handler CacheEventHandler) {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	eventTypeStr := string(eventType)
	if m.handlers[eventTypeStr] == nil {
		m.handlers[eventTypeStr] = make([]CacheEventHandler, 0)
	}
	
	m.handlers[eventTypeStr] = append(m.handlers[eventTypeStr], handler)
	
	m.logger.Info("注册缓存事件处理器",
		zap.String("event_type", eventTypeStr),
		zap.String("handler_name", handler.GetHandlerName()))
}

// PublishEvent 发布事件（异步，不阻塞）
func (m *CacheEventManager) PublishEvent(event CacheEvent) {
	event.Timestamp = time.Now()
	
	select {
	case m.eventCh <- event:
		m.logger.Debug("缓存事件已发布",
			zap.String("event_type", string(event.Type)),
			zap.String("company_code", event.CompanyCode),
			zap.String("provider_code", event.ProviderCode))
	default:
		m.logger.Warn("缓存事件队列已满，丢弃事件",
			zap.String("event_type", string(event.Type)),
			zap.String("company_code", event.CompanyCode))
	}
}

// start 启动事件处理
func (m *CacheEventManager) start() {
	m.wg.Add(1)
	go func() {
		defer m.wg.Done()
		
		for {
			select {
			case event := <-m.eventCh:
				m.handleEvent(context.Background(), event)
			case <-m.stopCh:
				// 处理剩余事件
				for {
					select {
					case event := <-m.eventCh:
						m.handleEvent(context.Background(), event)
					default:
						return
					}
				}
			}
		}
	}()
}

// handleEvent 处理事件
func (m *CacheEventManager) handleEvent(ctx context.Context, event CacheEvent) {
	start := time.Now()
	
	m.mu.RLock()
	handlers := m.handlers[string(event.Type)]
	m.mu.RUnlock()
	
	if len(handlers) == 0 {
		m.logger.Debug("无处理器处理该事件",
			zap.String("event_type", string(event.Type)))
		return
	}
	
	// 并发处理所有处理器
	var wg sync.WaitGroup
	for _, handler := range handlers {
		wg.Add(1)
		go func(h CacheEventHandler) {
			defer wg.Done()
			
			handlerStart := time.Now()
			if err := h.HandleCacheEvent(ctx, event); err != nil {
				m.logger.Error("缓存事件处理失败",
					zap.Error(err),
					zap.String("handler_name", h.GetHandlerName()),
					zap.String("event_type", string(event.Type)),
					zap.Duration("duration", time.Since(handlerStart)))
			} else {
				m.logger.Debug("缓存事件处理成功",
					zap.String("handler_name", h.GetHandlerName()),
					zap.String("event_type", string(event.Type)),
					zap.Duration("duration", time.Since(handlerStart)))
			}
		}(handler)
	}
	
	wg.Wait()
	
	m.logger.Debug("缓存事件处理完成",
		zap.String("event_type", string(event.Type)),
		zap.String("company_code", event.CompanyCode),
		zap.String("provider_code", event.ProviderCode),
		zap.Int("handler_count", len(handlers)),
		zap.Duration("total_duration", time.Since(start)))
}

// Stop 停止事件管理器
func (m *CacheEventManager) Stop() {
	close(m.stopCh)
	m.wg.Wait()
	close(m.eventCh)
	
	m.logger.Info("缓存事件管理器已停止")
}

// GetStats 获取统计信息
func (m *CacheEventManager) GetStats() map[string]interface{} {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	stats := make(map[string]interface{})
	stats["pending_events"] = len(m.eventCh)
	stats["handler_count"] = len(m.handlers)
	
	handlerStats := make(map[string]int)
	for eventType, handlers := range m.handlers {
		handlerStats[eventType] = len(handlers)
	}
	stats["handlers_by_event"] = handlerStats
	
	return stats
}