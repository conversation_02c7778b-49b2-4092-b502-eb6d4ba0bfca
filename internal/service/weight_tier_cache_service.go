package service

import (
	"context"
	"errors"
	"fmt"
	"math"
	"strings"
	"time"

	"github.com/shopspring/decimal"
	"go.uber.org/zap"

	"github.com/your-org/go-kuaidi/internal/adapter"
	"github.com/your-org/go-kuaidi/internal/express"
	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/repository"
	"github.com/your-org/go-kuaidi/internal/util"
)

// WeightTierCacheService 重量档位缓存服务接口
type WeightTierCacheService interface {
	// 核心功能
	QueryPriceWithCache(ctx context.Context, req *model.WeightTierCacheRequest) (*model.WeightTierCacheResponse, error)
	ValidateOrderPrice(ctx context.Context, req *model.OrderValidationRequest) (*model.OrderValidationResponse, error)

	// 统计查询
	GetCacheStatistics(ctx context.Context, req *model.CacheStatisticsRequest) (*model.CacheStatisticsResponse, error)
	GetCacheOverview(ctx context.Context) ([]*model.WeightCacheOverview, error)
	GetValidationStats(ctx context.Context, startDate, endDate time.Time, provider string) ([]*model.PriceValidationStats, error)

	// 🚀 优化后的查询方法
	GetCacheOverviewOptimized(ctx context.Context, req *model.CacheOverviewRequest) (*model.CacheOverviewResponse, error)
	GetProviderGroupedOverviewOptimized(ctx context.Context) ([]*model.ProviderGroup, error)
	RefreshCacheViews(ctx context.Context) error
	GetQuickStats(ctx context.Context) (*model.QuickStatsResponse, error)

	// 新增：分组和详情查询
	GetProviderGroupedOverview(ctx context.Context) ([]*model.ProviderGroup, error)
	GetCacheDetails(ctx context.Context, req *model.CacheDetailRequest) (*model.CacheDetailResponse, error)
	BatchInvalidateCache(ctx context.Context, provider, expressCode, route string) error

	// 缓存管理
	InvalidateCacheEntry(ctx context.Context, fromProvince, toProvince, provider, expressCode string, weight float64) error
	InvalidateCacheByWeight(ctx context.Context, provider, expressCode string, weight float64) error // 🔥 新增：按重量批量失效缓存
	WarmupCache(ctx context.Context, routes []*model.RouteDefinition, providers []string, weights []int, expressCodes []string) error
	CleanupInvalidCache(ctx context.Context) error

	// 🚀 新增：获取活跃快递代码
	GetActiveExpressCodes(ctx context.Context) ([]string, error)

	// 🎯 新增：设置地区黑名单服务
	SetBlacklistService(blacklistService *RegionBlacklistService)
}

// ExpressCompanyServiceInterface 快递公司服务接口（简化版）
// 重命名避免与express包中的接口冲突
type ExpressCompanyServiceInterface interface {
	GetActiveCompanies(ctx context.Context) ([]*express.ExpressCompany, error)
}

// weightTierCacheService 重量档位缓存服务实现
type weightTierCacheService struct {
	cacheRepo        repository.WeightTierCacheRepository
	providerManager  *adapter.ProviderManager
	configService    SystemConfigService
	expressService   ExpressCompanyServiceInterface
	mappingService   *express.ExpressMappingCacheService // 🔥 新增：映射服务，用于检查供应商支持状态
	logger           *zap.Logger
	blacklistService *RegionBlacklistService // 🎯 新增：地区黑名单服务
}

// NewWeightTierCacheService 创建重量档位缓存服务
func NewWeightTierCacheService(
	cacheRepo repository.WeightTierCacheRepository,
	providerManager *adapter.ProviderManager,
	configService SystemConfigService,
	expressService ExpressCompanyServiceInterface,
	logger *zap.Logger,
) WeightTierCacheService {
	return &weightTierCacheService{
		cacheRepo:        cacheRepo,
		providerManager:  providerManager,
		configService:    configService,
		expressService:   expressService,
		mappingService:   nil, // 🔥 暂时设为nil，需要通过SetMappingService设置
		logger:           logger,
		blacklistService: nil, // 🎯 暂时设为nil，需要通过SetBlacklistService设置
	}
}

// SetMappingService 设置映射服务（避免循环依赖）
func (s *weightTierCacheService) SetMappingService(mappingService *express.ExpressMappingCacheService) {
	s.logger.Info("🔧 SetMappingService 被调用",
		zap.Bool("mapping_service_nil", mappingService == nil),
		zap.String("mapping_service_type", fmt.Sprintf("%T", mappingService)),
		zap.String("service_instance", fmt.Sprintf("%p", s)),
		zap.String("mapping_service_instance", fmt.Sprintf("%p", mappingService)))
	s.mappingService = mappingService
	s.logger.Info("🔧 SetMappingService 设置完成",
		zap.Bool("s_mapping_service_nil", s.mappingService == nil),
		zap.String("service_instance", fmt.Sprintf("%p", s)),
		zap.String("s_mapping_service_instance", fmt.Sprintf("%p", s.mappingService)))
}

// SetBlacklistService 设置地区黑名单服务（避免循环依赖）
func (s *weightTierCacheService) SetBlacklistService(blacklistService *RegionBlacklistService) {
	s.logger.Info("🔧 SetBlacklistService 被调用",
		zap.Bool("blacklist_service_nil", blacklistService == nil),
		zap.String("blacklist_service_type", fmt.Sprintf("%T", blacklistService)),
		zap.String("service_instance", fmt.Sprintf("%p", s)),
		zap.String("blacklist_service_instance", fmt.Sprintf("%p", blacklistService)))
	s.blacklistService = blacklistService
	s.logger.Info("🔧 SetBlacklistService 设置完成",
		zap.Bool("s_blacklist_service_nil", s.blacklistService == nil),
		zap.String("service_instance", fmt.Sprintf("%p", s)),
		zap.String("s_blacklist_service_instance", fmt.Sprintf("%p", s.blacklistService)))
}

// QueryPriceWithCache 带缓存的价格查询
// 遵循修正版要求：纯缓存策略，100%基于供应商权威价格
func (s *weightTierCacheService) QueryPriceWithCache(ctx context.Context, req *model.WeightTierCacheRequest) (*model.WeightTierCacheResponse, error) {
	start := util.NowBeijing()

	// 输入验证
	if err := s.validateCacheRequest(req); err != nil {
		s.logger.Error("缓存查询请求验证失败", zap.Error(err))
		return &model.WeightTierCacheResponse{
			Success: false,
			Code:    model.StatusBadRequest,
			Message: err.Error(),
		}, nil
	}

	// 重量转档位，从配置获取最大重量值
	weightKg, err := s.WeightToTier(req.Weight)
	if err != nil {
		s.logger.Error("重量转档位失败", zap.Error(err))
		return &model.WeightTierCacheResponse{
			Success: false,
			Code:    model.StatusInternalServerError,
			Message: "重量转档位失败: " + err.Error(),
		}, err
	}

	s.logger.Info("开始缓存价格查询",
		zap.String("route", fmt.Sprintf("%s->%s", req.FromProvince, req.ToProvince)),
		zap.String("provider", req.Provider),
		zap.String("express_code", req.ExpressCode),
		zap.Float64("original_weight", req.Weight),
		zap.Int("weight_tier", weightKg))

	// 1. 查询缓存
	cached, err := s.cacheRepo.GetCachedPrice(ctx, req.FromProvince, req.ToProvince, req.Provider, req.ExpressCode, weightKg)
	if err != nil {
		s.logger.Error("查询缓存失败", zap.Error(err))
		return &model.WeightTierCacheResponse{
			Success: false,
			Code:    model.StatusInternalServerError,
			Message: "缓存查询失败: " + err.Error(),
		}, err
	}

	// 2. 缓存命中
	if cached != nil && cached.IsValid {
		responseTime := time.Since(start)

		// 🚀 异步记录统计信息，避免阻塞响应
		go func() {
			// 记录缓存命中（异步）
			if err := s.cacheRepo.RecordCacheHit(context.Background(), cached.ID.String()); err != nil {
				s.logger.Warn("记录缓存命中失败", zap.Error(err))
			}

			// 记录查询日志（异步）
			s.recordQueryLog(context.Background(), req, cached.Price, "cache", responseTime, true, "")
		}()

		s.logger.Info("缓存命中",
			zap.Duration("response_time", responseTime),
			zap.String("price", cached.Price.String()),
			zap.Int64("cache_hit_count", cached.CacheHitCount+1))

		return &model.WeightTierCacheResponse{
			Success: true,
			Code:    model.StatusSuccess,
			Message: "查询成功",
			Data: &model.CachedPriceInfo{
				Price:                cached.Price,
				ContinuedWeightPerKg: cached.ContinuedWeightPerKg,
				ProductCode:          cached.ProductCode,
				ProductName:          cached.ProductName,
				ChannelID:            cached.ChannelID,
				EstimatedDays:        cached.EstimatedDays,
				WeightKg:             cached.WeightKg,
				ExpressCode:          cached.ExpressCode,
				ExpressName:          cached.ExpressName,
				Provider:             cached.Provider,
				IsFromCache:          true,
				CacheHitTime:         &cached.UpdatedAt,
			},
			Source:       "cache",
			ResponseTime: s.getCacheResponseTime(),
		}, nil
	}

	// 3. 缓存未命中，在调用实时API之前先检查数据库支持状态
	s.logger.Info("缓存未命中，调用实时API",
		zap.String("route", fmt.Sprintf("%s->%s", req.FromProvince, req.ToProvince)),
		zap.String("provider", req.Provider))

	// 🔥 关键修复：在缓存未命中时，调用实时API之前检查数据库中的is_supported字段
	// 这样可以避免对已禁用快递公司的无效API调用，实现100%缓存命中率
	s.logger.Info("🔧 开始执行数据库支持状态检查",
		zap.String("provider", req.Provider),
		zap.String("express_code", req.ExpressCode),
		zap.Bool("mapping_service_available", s.mappingService != nil),
		zap.String("service_instance", fmt.Sprintf("%p", s)),
		zap.String("mapping_service_instance", fmt.Sprintf("%p", s.mappingService)))

	if s.mappingService != nil {
		isSupported, err := s.checkProviderSupportsCompany(ctx, req.Provider, req.ExpressCode)
		s.logger.Info("🔍 数据库支持状态检查结果",
			zap.String("provider", req.Provider),
			zap.String("express_code", req.ExpressCode),
			zap.Bool("is_supported", isSupported),
			zap.Error(err))

		if err != nil {
			s.logger.Warn("检查供应商支持状态失败，继续查询",
				zap.String("provider", req.Provider),
				zap.String("express_code", req.ExpressCode),
				zap.Error(err))
		} else if !isSupported {
			s.logger.Info("🚫 数据库配置显示供应商不支持该快递公司，跳过实时API调用",
				zap.String("provider", req.Provider),
				zap.String("express_code", req.ExpressCode))

			// 记录失败日志
			failureReason := fmt.Sprintf("供应商 %s 不支持快递公司 %s（数据库配置 is_supported=false）", req.Provider, req.ExpressCode)
			s.recordQueryLog(ctx, req, decimal.Zero, "realtime", time.Since(start), false, failureReason)

			return &model.WeightTierCacheResponse{
				Success: false,
				Code:    model.StatusBadRequest,
				Message: failureReason,
				Source:  "database_check",
			}, errors.New(failureReason)
		} else {
			s.logger.Info("✅ 数据库配置显示供应商支持该快递公司，继续调用实时API",
				zap.String("provider", req.Provider),
				zap.String("express_code", req.ExpressCode))
		}
	}

	realtimeResp, err := s.queryRealtimePrice(ctx, req)
	if err != nil {
		s.logger.Error("实时查价失败", zap.Error(err))

		// 记录失败日志
		s.recordQueryLog(ctx, req, decimal.Zero, "realtime", time.Since(start), false, err.Error())

		return &model.WeightTierCacheResponse{
			Success: false,
			Code:    model.StatusInternalServerError,
			Message: "实时查价失败: " + err.Error(),
		}, err
	}

	// 4. 异步缓存结果
	go func() {
		cacheCtx := context.Background()
		s.storeCacheEntryAsync(cacheCtx, req, realtimeResp, weightKg)
	}()

	// 记录查询日志
	s.recordQueryLog(ctx, req, realtimeResp.Price, "realtime", time.Since(start), true, "")

	responseTime := time.Since(start)
	s.logger.Info("实时查价完成",
		zap.Duration("response_time", responseTime),
		zap.String("price", realtimeResp.Price.String()))

	return &model.WeightTierCacheResponse{
		Success: true,
		Code:    model.StatusSuccess,
		Message: "查询成功",
		Data: &model.CachedPriceInfo{
			Price:                realtimeResp.Price,
			ContinuedWeightPerKg: realtimeResp.ContinuedWeightPerKg,
			ProductCode:          realtimeResp.ProductCode,
			ProductName:          realtimeResp.ProductName,
			ChannelID:            realtimeResp.ChannelID,
			EstimatedDays:        realtimeResp.EstimatedDays,
			WeightKg:             weightKg,
			ExpressCode:          req.ExpressCode,
			ExpressName:          realtimeResp.ExpressName,
			Provider:             req.Provider,
			IsFromCache:          false,
		},
		Source:       "realtime",
		ResponseTime: s.getRealtimeResponseTime(),
	}, nil
}

// ValidateOrderPrice 严格无容差的订单价格验证
// 核心修正：使用decimal.Equal()进行严格价格验证，无容差机制
func (s *weightTierCacheService) ValidateOrderPrice(ctx context.Context, req *model.OrderValidationRequest) (*model.OrderValidationResponse, error) {
	s.logger.Info("开始订单价格验证",
		zap.String("order_id", req.OrderID),
		zap.String("price_source", req.PriceSource),
		zap.String("route", fmt.Sprintf("%s->%s", req.FromProvince, req.ToProvince)))

	// 输入验证
	if req.OrderID == "" {
		return &model.OrderValidationResponse{
			Success: false,
			Code:    model.StatusBadRequest,
			Message: "订单ID不能为空",
		}, nil
	}

	// 如果不是缓存价格，跳过验证
	if req.PriceSource != "cache" {
		s.logger.Info("非缓存价格，跳过验证",
			zap.String("order_id", req.OrderID),
			zap.String("price_source", req.PriceSource))

		return &model.OrderValidationResponse{
			Success:          true,
			Code:             model.StatusSuccess,
			Message:          "非缓存价格，无需验证",
			IsValid:          true,
			ValidationResult: "skip",
			ActionTaken:      "order_proceed",
		}, nil
	}

	// 重量转档位
	weightKg, err := s.WeightToTier(req.Weight)
	if err != nil {
		s.logger.Error("重量转档位失败", zap.Error(err))
		return &model.OrderValidationResponse{
			Success: false,
			Code:    model.StatusInternalServerError,
			Message: "重量转档位失败: " + err.Error(),
		}, err
	}

	// 1. 获取缓存价格
	cached, err := s.cacheRepo.GetCachedPrice(ctx, req.FromProvince, req.ToProvince, req.Provider, req.ExpressCode, weightKg)
	if err != nil {
		s.logger.Error("获取缓存价格失败", zap.Error(err))
		return &model.OrderValidationResponse{
			Success: false,
			Code:    model.StatusInternalServerError,
			Message: "缓存查询失败: " + err.Error(),
		}, err
	}

	if cached == nil || !cached.IsValid {
		s.logger.Warn("缓存价格不存在或已失效",
			zap.String("order_id", req.OrderID),
			zap.Bool("cached_exists", cached != nil),
			zap.Bool("is_valid", cached != nil && cached.IsValid))

		validation := &model.OrderPriceValidation{
			OrderID:          req.OrderID,
			FromProvince:     req.FromProvince,
			ToProvince:       req.ToProvince,
			Provider:         req.Provider,
			ExpressCode:      req.ExpressCode,
			WeightKg:         weightKg,
			ValidationResult: "cache_miss",
			ActionTaken:      "order_reject",
			IsPriceMatch:     false,
		}

		s.saveValidationRecord(ctx, validation)

		return &model.OrderValidationResponse{
			Success:          false,
			Code:             model.StatusNotFound,
			Message:          "缓存价格不存在，请重新查价",
			IsValid:          false,
			ValidationResult: "cache_miss",
			ActionTaken:      "order_reject",
		}, nil
	}

	// 2. 调用实时API获取当前价格
	realtimeResp, err := s.queryRealtimePriceForValidation(ctx, req)
	if err != nil {
		s.logger.Error("验证时实时查价失败", zap.Error(err))

		validation := &model.OrderPriceValidation{
			OrderID:          req.OrderID,
			FromProvince:     req.FromProvince,
			ToProvince:       req.ToProvince,
			Provider:         req.Provider,
			ExpressCode:      req.ExpressCode,
			WeightKg:         weightKg,
			CachedPrice:      cached.Price,
			ValidationResult: "error",
			ActionTaken:      "order_reject",
			IsPriceMatch:     false,
		}

		s.saveValidationRecord(ctx, validation)

		return &model.OrderValidationResponse{
			Success: false,
			Code:    model.StatusInternalServerError,
			Message: "价格验证失败: " + err.Error(),
		}, err
	}

	// 3. 严格价格对比 (修正版核心：无容差验证)
	realtimePrice := realtimeResp.Price
	priceDifference := cached.Price.Sub(realtimePrice).Abs()

	// 使用decimal.Equal()进行严格相等验证，无容差
	isPriceMatch := cached.Price.Equal(realtimePrice)

	s.logger.Info("价格验证对比",
		zap.String("order_id", req.OrderID),
		zap.String("cached_price", cached.Price.String()),
		zap.String("realtime_price", realtimePrice.String()),
		zap.String("price_difference", priceDifference.String()),
		zap.Bool("is_price_match", isPriceMatch))

	// 4. 记录验证过程
	validation := &model.OrderPriceValidation{
		OrderID:         req.OrderID,
		FromProvince:    req.FromProvince,
		ToProvince:      req.ToProvince,
		Provider:        req.Provider,
		ExpressCode:     req.ExpressCode,
		WeightKg:        weightKg,
		CachedPrice:     cached.Price,
		RealtimePrice:   realtimePrice,
		PriceDifference: priceDifference,
		IsPriceMatch:    isPriceMatch,
	}

	if isPriceMatch {
		// 5a. 价格严格一致，验证通过
		validation.ValidationResult = "pass"
		validation.ActionTaken = "order_proceed"

		// 记录缓存验证
		if err := s.cacheRepo.RecordCacheValidation(ctx, cached.ID.String()); err != nil {
			s.logger.Warn("记录缓存验证失败", zap.Error(err))
		}

		s.saveValidationRecord(ctx, validation)

		s.logger.Info("价格验证通过",
			zap.String("order_id", req.OrderID),
			zap.String("price", cached.Price.String()))

		return &model.OrderValidationResponse{
			Success:          true,
			Code:             model.StatusSuccess,
			Message:          "价格验证通过",
			IsValid:          true,
			CachedPrice:      cached.Price,
			RealtimePrice:    realtimePrice,
			PriceDifference:  priceDifference,
			ValidationResult: "pass",
			ActionTaken:      "order_proceed",
		}, nil
	} else {
		// 5b. 价格不一致，验证失败
		validation.ValidationResult = "fail"
		validation.ActionTaken = "cache_invalidate"

		// 标记缓存失效
		if err := s.cacheRepo.InvalidateCache(ctx, req.FromProvince, req.ToProvince, req.Provider, req.ExpressCode, weightKg); err != nil {
			s.logger.Error("标记缓存失效失败", zap.Error(err))
		}

		// 异步更新缓存为最新价格
		go func() {
			cacheCtx := context.Background()
			s.updateCacheWithNewPrice(cacheCtx, req, realtimeResp, weightKg)
		}()

		s.saveValidationRecord(ctx, validation)

		s.logger.Warn("价格验证失败，价格已变动",
			zap.String("order_id", req.OrderID),
			zap.String("cached_price", cached.Price.String()),
			zap.String("realtime_price", realtimePrice.String()),
			zap.String("difference", priceDifference.String()))

		return &model.OrderValidationResponse{
			Success:          false,
			Code:             model.StatusConflict,
			Message:          fmt.Sprintf("价格已变动(差异%s元)，请重新查价", priceDifference.String()),
			IsValid:          false,
			CachedPrice:      cached.Price,
			RealtimePrice:    realtimePrice,
			PriceDifference:  priceDifference,
			ValidationResult: "fail",
			ActionTaken:      "cache_invalidate",
		}, nil
	}
}

// GetCacheStatistics 获取缓存统计
func (s *weightTierCacheService) GetCacheStatistics(ctx context.Context, req *model.CacheStatisticsRequest) (*model.CacheStatisticsResponse, error) {
	s.logger.Info("查询缓存统计",
		zap.Time("start_date", req.StartDate),
		zap.Time("end_date", req.EndDate),
		zap.String("provider", req.Provider))

	stats, err := s.cacheRepo.GetCacheStatistics(ctx, req.StartDate, req.EndDate, req.Provider)
	if err != nil {
		s.logger.Error("查询缓存统计失败", zap.Error(err))
		return &model.CacheStatisticsResponse{
			Success: false,
			Code:    model.StatusInternalServerError,
			Message: "查询失败: " + err.Error(),
		}, err
	}

	// 聚合统计数据
	data := s.aggregateStatistics(stats)

	return &model.CacheStatisticsResponse{
		Success: true,
		Code:    model.StatusSuccess,
		Message: "查询成功",
		Data:    data,
	}, nil
}

// GetCacheOverview 获取缓存概览
func (s *weightTierCacheService) GetCacheOverview(ctx context.Context) ([]*model.WeightCacheOverview, error) {
	s.logger.Info("查询缓存概览")

	overview, err := s.cacheRepo.GetCacheOverview(ctx)
	if err != nil {
		s.logger.Error("查询缓存概览失败", zap.Error(err))
		return nil, err
	}

	s.logger.Info("缓存概览查询完成", zap.Int("overview_count", len(overview)))
	return overview, nil
}

// GetValidationStats 获取验证统计
func (s *weightTierCacheService) GetValidationStats(ctx context.Context, startDate, endDate time.Time, provider string) ([]*model.PriceValidationStats, error) {
	s.logger.Info("查询验证统计",
		zap.Time("start_date", startDate),
		zap.Time("end_date", endDate),
		zap.String("provider", provider))

	stats, err := s.cacheRepo.GetValidationStats(ctx, startDate, endDate, provider)
	if err != nil {
		s.logger.Error("查询验证统计失败", zap.Error(err))
		return nil, err
	}

	s.logger.Info("验证统计查询完成", zap.Int("stats_count", len(stats)))
	return stats, nil
}

// InvalidateCacheEntry 使缓存失效（单个线路）
func (s *weightTierCacheService) InvalidateCacheEntry(ctx context.Context, fromProvince, toProvince, provider, expressCode string, weight float64) error {
	weightKg, err := s.WeightToTier(weight)
	if err != nil {
		s.logger.Error("重量转档位失败", zap.Error(err))
		return fmt.Errorf("重量转档位失败: %w", err)
	}

	s.logger.Info("使缓存失效",
		zap.String("route", fmt.Sprintf("%s->%s", fromProvince, toProvince)),
		zap.String("provider", provider),
		zap.String("express_code", expressCode),
		zap.Int("weight_kg", weightKg))

	err = s.cacheRepo.InvalidateCache(ctx, fromProvince, toProvince, provider, expressCode, weightKg)
	if err != nil {
		s.logger.Error("使缓存失效失败", zap.Error(err))
		return err
	}

	s.logger.Info("缓存失效成功")
	return nil
}

// InvalidateCacheByWeight 按重量批量失效缓存（所有地区）
func (s *weightTierCacheService) InvalidateCacheByWeight(ctx context.Context, provider, expressCode string, weight float64) error {
	weightKg, err := s.WeightToTier(weight)
	if err != nil {
		s.logger.Error("重量转档位失败", zap.Error(err))
		return fmt.Errorf("重量转档位失败: %w", err)
	}

	s.logger.Info("开始按重量批量失效缓存",
		zap.String("provider", provider),
		zap.String("express_code", expressCode),
		zap.Int("weight_kg", weightKg))

	err = s.cacheRepo.InvalidateCacheByWeight(ctx, provider, expressCode, weightKg)
	if err != nil {
		s.logger.Error("按重量批量失效缓存失败", zap.Error(err))
		return err
	}

	s.logger.Info("按重量批量失效缓存成功",
		zap.String("provider", provider),
		zap.String("express_code", expressCode),
		zap.Int("weight_kg", weightKg))
	return nil
}

// WarmupCache 缓存预热
func (s *weightTierCacheService) WarmupCache(ctx context.Context, routes []*model.RouteDefinition, providers []string, weights []int, expressCodes []string) error {
	s.logger.Info("开始缓存预热",
		zap.Int("routes_count", len(routes)),
		zap.Int("providers_count", len(providers)),
		zap.Int("weights_count", len(weights)),
		zap.Int("express_codes_count", len(expressCodes)))

	// 如果没有提供快递代码，从数据库获取所有活跃的快递公司代码
	if len(expressCodes) == 0 {
		activeExpressCodes, err := s.getActiveExpressCodes(ctx)
		if err != nil {
			s.logger.Error("获取活跃快递代码失败", zap.Error(err))
			return fmt.Errorf("缓存预热失败: %w", err)
		}
		expressCodes = activeExpressCodes
		s.logger.Info("使用数据库中的活跃快递代码", zap.Strings("express_codes", expressCodes))
	}

	totalTasks := len(routes) * len(providers) * len(weights) * len(expressCodes)
	s.logger.Info("预热任务统计", zap.Int("total_tasks", totalTasks))

	// 实现预热逻辑
	for _, route := range routes {
		for _, provider := range providers {
			for _, weight := range weights {
				for _, expressCode := range expressCodes {
					// 解析路线
					parts := strings.Split(route.RouteKey, "->")
					if len(parts) != 2 {
						s.logger.Warn("路线格式错误", zap.String("route_key", route.RouteKey))
						continue
					}

					req := &model.WeightTierCacheRequest{
						FromProvince: strings.TrimSpace(parts[0]),
						ToProvince:   strings.TrimSpace(parts[1]),
						Provider:     provider,
						ExpressCode:  expressCode,
						Weight:       float64(weight),
					}

					// 异步预热
					go func(r *model.WeightTierCacheRequest) {
						if _, err := s.QueryPriceWithCache(ctx, r); err != nil {
							s.logger.Warn("预热失败",
								zap.String("route", r.FromProvince+"->"+r.ToProvince),
								zap.String("provider", r.Provider),
								zap.String("express_code", r.ExpressCode),
								zap.Error(err))
						}
					}(req)
				}
			}
		}
	}

	s.logger.Info("缓存预热已启动")
	return nil
}

// CleanupInvalidCache 清理无效缓存
func (s *weightTierCacheService) CleanupInvalidCache(ctx context.Context) error {
	s.logger.Info("开始清理无效缓存")

	err := s.cacheRepo.CleanupInvalidCache(ctx)
	if err != nil {
		s.logger.Error("清理无效缓存失败", zap.Error(err))
		return err
	}

	s.logger.Info("清理无效缓存完成")
	return nil
}

// 🚀 新增：获取活跃快递代码
func (s *weightTierCacheService) GetActiveExpressCodes(ctx context.Context) ([]string, error) {
	s.logger.Debug("获取活跃快递代码")

	// 从快递公司服务获取活跃的快递公司
	companies, err := s.expressService.GetActiveCompanies(ctx)
	if err != nil {
		s.logger.Error("获取活跃快递公司失败", zap.Error(err))
		return nil, err
	}

	var expressCodes []string
	for _, company := range companies {
		if company.Code != "" {
			expressCodes = append(expressCodes, company.Code)
		}
	}

	s.logger.Debug("获取活跃快递代码完成",
		zap.Int("count", len(expressCodes)),
		zap.Strings("codes", expressCodes))

	return expressCodes, nil
}

// === 私有辅助方法 ===

// validateCacheRequest 验证缓存请求
func (s *weightTierCacheService) validateCacheRequest(req *model.WeightTierCacheRequest) error {
	if req.FromProvince == "" {
		return fmt.Errorf("起始省份不能为空")
	}
	if req.ToProvince == "" {
		return fmt.Errorf("目标省份不能为空")
	}
	if req.Provider == "" {
		return fmt.Errorf("供应商不能为空")
	}
	if req.ExpressCode == "" {
		return fmt.Errorf("快递代码不能为空")
	}
	if req.Weight <= 0 {
		return fmt.Errorf("重量必须大于0")
	}
	maxWeight, err := s.getMaxCacheWeight()
	if err != nil {
		return fmt.Errorf("获取最大缓存重量配置失败: %w", err)
	}
	if req.Weight > float64(maxWeight) {
		return fmt.Errorf("重量超出缓存范围，最大%dKG", maxWeight)
	}

	return nil
}

// queryRealtimePrice 查询实时价格
func (s *weightTierCacheService) queryRealtimePrice(ctx context.Context, req *model.WeightTierCacheRequest) (*RealtimePriceInfo, error) {
	// 🔥 修复：在标准查价接口中排除菜鸟和快递鸟供应商
	// 这些供应商只应在实时查价接口(QUERY_REALTIME_PRICE)中使用
	if req.Provider == "cainiao" || req.Provider == "kuaidiniao" {
		s.logger.Debug("跳过菜鸟/快递鸟供应商调用",
			zap.String("provider", req.Provider),
			zap.String("reason", "这些供应商仅用于实时查价接口"))
		return nil, &model.ProviderNotSupportedError{
			Provider: req.Provider,
			Message:  fmt.Sprintf("供应商 %s 仅用于实时查价接口，不在标准查价中使用", req.Provider),
		}
	}

	// 🎯 新增：黑名单预检查，避免重复调用已知失败的路线
	if s.blacklistService != nil {
		route := fmt.Sprintf("%s->%s", req.FromProvince, req.ToProvince)
		if s.blacklistService.ShouldSkipQuery(req.Provider, route, req.ExpressCode) {
			s.logger.Debug("跳过黑名单路线查询",
				zap.String("provider", req.Provider),
				zap.String("route", route),
				zap.String("express_code", req.ExpressCode),
				zap.String("reason", "该路线在黑名单中"))
			return nil, &model.ProviderNotSupportedError{
				Provider: req.Provider,
				Message:  "该地区路线暂不支持（基于历史失败记录）",
			}
		}
	}

	adapter, exists := s.providerManager.Get(req.Provider)
	if !exists {
		return nil, &model.ProviderNotSupportedError{
			Provider: req.Provider,
			Message:  fmt.Sprintf("不支持的供应商: %s", req.Provider),
		}
	}

	// 🚀 修复：构造完整的实时API查询请求，使用真实地址信息
	senderCity := s.extractCityFromProvince(req.FromProvince)
	senderDistrict := s.getDefaultDistrict(req.FromProvince, senderCity)
	senderAddress := s.buildRealAddress(senderDistrict)

	receiverCity := s.extractCityFromProvince(req.ToProvince)
	receiverDistrict := s.getDefaultDistrict(req.ToProvince, receiverCity)
	receiverAddress := s.buildRealAddress(receiverDistrict)

	priceReq := &model.PriceRequest{
		Provider:    req.Provider,
		ExpressType: req.ExpressCode,
		Sender: model.SenderInfo{
			Name:     "寄件人",         // 易达API需要
			Mobile:   "13800000000", // 易达API需要
			Province: req.FromProvince,
			City:     senderCity,
			District: senderDistrict,
			Address:  senderAddress,
		},
		Receiver: model.ReceiverInfo{
			Name:     "收件人",         // 易达API需要
			Mobile:   "13900000000", // 易达API需要
			Province: req.ToProvince,
			City:     receiverCity,
			District: receiverDistrict,
			Address:  receiverAddress,
		},
		Package: model.PackageInfo{
			Weight:    req.Weight,
			Quantity:  1,    // 默认包裹数量
			GoodsName: "物品", // 默认物品名称
		},
	}

	// 注意：数据库支持状态检查已在缓存未命中时进行，这里不需要重复检查

	// 调用供应商API
	resp, err := adapter.QueryPrice(ctx, priceReq)
	if err != nil {
		// 🎯 记录查价失败错误到黑名单（所有供应商统一处理）
		if strings.Contains(err.Error(), "不支持") || strings.Contains(err.Error(), "暂未开放") {
			// 记录到黑名单服务
			if s.blacklistService != nil {
				route := fmt.Sprintf("%s->%s", req.FromProvince, req.ToProvince)
				s.blacklistService.RecordFailure(req.Provider, route, req.ExpressCode, err.Error())

				// 🎯 统一处理：所有供应商都使用相同的日志级别和格式
				s.logger.Warn("供应商地区路线不支持（已记录到黑名单）",
					zap.String("provider", req.Provider),
					zap.String("route", route),
					zap.String("express_code", req.ExpressCode),
					zap.String("error_type", "REGION_NOT_SUPPORTED"),
					zap.String("reason", "该地区路线供应商暂不支持，已自动加入黑名单"),
					zap.String("error_message", err.Error()))
			}
			return nil, &model.ProviderNotSupportedError{
				Provider: req.Provider,
				Message:  fmt.Sprintf("供应商不支持该快递公司: %s", err.Error()),
			}
		}
		if strings.Contains(err.Error(), "运力") || strings.Contains(err.Error(), "capacity") {
			return nil, &model.CapacityError{
				Provider: req.Provider,
				Message:  fmt.Sprintf("供应商运力异常: %s", err.Error()),
			}
		}
		if strings.Contains(err.Error(), "网络") || strings.Contains(err.Error(), "timeout") {
			return nil, &model.NetworkError{
				Provider: req.Provider,
				Message:  fmt.Sprintf("网络连接异常: %s", err.Error()),
			}
		}
		return nil, fmt.Errorf("供应商API调用失败: %w", err)
	}

	if len(resp) == 0 {
		// 🔧 修复：改进空数据错误处理，添加更详细的日志记录
		s.logger.Error("供应商API返回空数据",
			zap.String("provider", req.Provider),
			zap.String("express_code", req.ExpressCode),
			zap.Float64("weight", req.Weight),
			zap.String("from", req.FromProvince),
			zap.String("to", req.ToProvince),
			zap.String("reason", "供应商API调用成功但返回空价格列表，可能是该线路不支持或价格配置错误"))
		return nil, fmt.Errorf("供应商API返回空数据")
	}

	// 取第一个结果并提取完整价格信息
	priceData := resp[0]
	price := decimal.NewFromFloat(priceData.Price)
	continuedWeightPerKg := decimal.NewFromFloat(priceData.ContinuedWeightPerKg)

	// 设置默认时效
	estimatedDays := 3 // 默认3天

	// 🚀 优化：确保产品信息和续重价格正确传递
	s.logger.Info("实时查询获取到完整价格信息",
		zap.String("express_code", req.ExpressCode),
		zap.String("provider", req.Provider),
		zap.Float64("price", priceData.Price),
		zap.Float64("continued_weight_per_kg", priceData.ContinuedWeightPerKg),
		zap.String("product_code", priceData.ProductCode),
		zap.String("product_name", priceData.ProductName),
		zap.String("channel_id", priceData.ChannelID))

	return &RealtimePriceInfo{
		Price:                price,
		ContinuedWeightPerKg: continuedWeightPerKg,
		ProductCode:          priceData.ProductCode,
		ProductName:          priceData.ProductName,
		ChannelID:            priceData.ChannelID,
		EstimatedDays:        estimatedDays,
		ExpressName:          priceData.ExpressName,
	}, nil
}

// queryRealtimePriceForValidation 为验证调用实时API
func (s *weightTierCacheService) queryRealtimePriceForValidation(ctx context.Context, req *model.OrderValidationRequest) (*RealtimePriceInfo, error) {
	cacheReq := &model.WeightTierCacheRequest{
		FromProvince: req.FromProvince,
		ToProvince:   req.ToProvince,
		Provider:     req.Provider,
		ExpressCode:  req.ExpressCode,
		Weight:       req.Weight,
	}

	return s.queryRealtimePrice(ctx, cacheReq)
}

// storeCacheEntryAsync 异步存储缓存条目 (优化版 - 包含续重价格和产品信息)
func (s *weightTierCacheService) storeCacheEntryAsync(ctx context.Context, req *model.WeightTierCacheRequest, realtimeResp *RealtimePriceInfo, weightKg int) {
	cache := &model.WeightTierPriceCache{
		FromProvince:         req.FromProvince,
		ToProvince:           req.ToProvince,
		Provider:             req.Provider,
		ExpressCode:          req.ExpressCode,
		ExpressName:          realtimeResp.ExpressName,
		WeightKg:             weightKg,
		Price:                realtimeResp.Price,
		ContinuedWeightPerKg: realtimeResp.ContinuedWeightPerKg,
		ProductCode:          realtimeResp.ProductCode,
		ProductName:          realtimeResp.ProductName,
		ChannelID:            realtimeResp.ChannelID,
		EstimatedDays:        realtimeResp.EstimatedDays,
		Source:               "api",
		IsValid:              true,
	}

	// 使用带超时的上下文，避免异步操作阻塞过久
	storeCtx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	if err := s.cacheRepo.StoreCachedPrice(storeCtx, cache); err != nil {
		// 降低错误日志级别，避免日志洪水
		s.logger.Warn("异步存储缓存失败",
			zap.Error(err),
			zap.String("route", req.FromProvince+"->"+req.ToProvince),
			zap.String("provider", req.Provider),
			zap.Int("weight_kg", weightKg))
	} else {
		s.logger.Info("异步存储缓存成功",
			zap.String("route", req.FromProvince+"->"+req.ToProvince),
			zap.String("provider", req.Provider),
			zap.Int("weight_kg", weightKg))
	}
}

// updateCacheWithNewPrice 使用新价格更新缓存
func (s *weightTierCacheService) updateCacheWithNewPrice(ctx context.Context, req *model.OrderValidationRequest, realtimeResp *RealtimePriceInfo, weightKg int) {
	cache := &model.WeightTierPriceCache{
		FromProvince:  req.FromProvince,
		ToProvince:    req.ToProvince,
		Provider:      req.Provider,
		ExpressCode:   req.ExpressCode,
		ExpressName:   realtimeResp.ExpressName,
		WeightKg:      weightKg,
		Price:         realtimeResp.Price,
		EstimatedDays: realtimeResp.EstimatedDays,
		Source:        "validation_update",
		IsValid:       true,
	}

	if err := s.cacheRepo.StoreCachedPrice(ctx, cache); err != nil {
		s.logger.Error("验证后更新缓存失败", zap.Error(err))
	} else {
		s.logger.Info("验证后更新缓存成功",
			zap.String("order_id", req.OrderID),
			zap.String("new_price", realtimeResp.Price.String()))
	}
}

// saveValidationRecord 保存验证记录
func (s *weightTierCacheService) saveValidationRecord(ctx context.Context, validation *model.OrderPriceValidation) {
	if err := s.cacheRepo.SaveValidationRecord(ctx, validation); err != nil {
		s.logger.Error("保存验证记录失败", zap.Error(err))
	}
}

// recordQueryLog 记录查询日志
func (s *weightTierCacheService) recordQueryLog(ctx context.Context, req *model.WeightTierCacheRequest, price decimal.Decimal, source string, responseTime time.Duration, success bool, errorMsg string) {
	weightKg, err := s.WeightToTier(req.Weight)
	if err != nil {
		s.logger.Error("记录查询日志时重量转档位失败", zap.Error(err))
		weightKg = 1 // 使用最小档位避免日志记录失败
	}

	logReq := &model.WeightTierQueryLogRequest{
		FromProvince:   req.FromProvince,
		ToProvince:     req.ToProvince,
		Provider:       req.Provider,
		ExpressCode:    req.ExpressCode,
		WeightKg:       weightKg,
		Price:          price,
		Source:         source,
		ResponseTimeMs: int(responseTime.Nanoseconds() / 1e6),
		Success:        success,
		ErrorMessage:   errorMsg,
	}

	if err := s.cacheRepo.SavePriceQueryLog(ctx, logReq); err != nil {
		s.logger.Warn("保存查询日志失败", zap.Error(err))
	}
}

// aggregateStatistics 聚合统计数据
func (s *weightTierCacheService) aggregateStatistics(stats []*model.WeightCacheStatistics) *model.CacheStatisticsData {
	data := &model.CacheStatisticsData{
		ProviderStats: make(map[string]*model.ProviderCache),
		DailyStats:    make([]*model.DailyCacheStats, 0),
	}

	totalQueries := int64(0)
	totalHits := int64(0)
	totalValidations := int64(0)
	totalFailures := int64(0)

	providerMap := make(map[string]*model.ProviderCache)

	for _, stat := range stats {
		totalQueries += stat.TotalQueries
		totalHits += stat.CacheHits
		totalValidations += stat.PriceValidations
		totalFailures += stat.ValidationFailures

		// 按供应商统计
		if providerStat, exists := providerMap[stat.Provider]; exists {
			providerStat.TotalQueries += stat.TotalQueries
			providerStat.CacheHits += stat.CacheHits
			providerStat.ValidationFailures += stat.ValidationFailures
		} else {
			providerMap[stat.Provider] = &model.ProviderCache{
				TotalQueries:       stat.TotalQueries,
				CacheHits:          stat.CacheHits,
				ValidationFailures: stat.ValidationFailures,
			}
		}

		// 日期统计
		data.DailyStats = append(data.DailyStats, &model.DailyCacheStats{
			Date:         stat.Date.Format("2006-01-02"),
			TotalQueries: int(stat.TotalQueries),
			CacheHits:    int(stat.CacheHits),
			CacheMisses:  int(stat.CacheMisses),
		})
	}

	// 计算比率
	for provider, providerStat := range providerMap {
		if providerStat.TotalQueries > 0 {
			providerStat.CacheHitRate = decimal.NewFromInt(providerStat.CacheHits).Div(decimal.NewFromInt(providerStat.TotalQueries)).Mul(decimal.NewFromInt(100))
		}
		if totalValidations > 0 {
			providerStat.ValidationFailureRate = decimal.NewFromInt(providerStat.ValidationFailures).Div(decimal.NewFromInt(totalValidations)).Mul(decimal.NewFromInt(100))
		}
		data.ProviderStats[provider] = providerStat
	}

	// 计算日期统计的命中率
	for _, dailyStat := range data.DailyStats {
		if dailyStat.TotalQueries > 0 {
			dailyStat.CacheHitRate = float64(dailyStat.CacheHits) / float64(dailyStat.TotalQueries) * 100
		}
	}

	data.TotalQueries = totalQueries
	if totalQueries > 0 {
		data.CacheHitRate = decimal.NewFromInt(totalHits).Div(decimal.NewFromInt(totalQueries)).Mul(decimal.NewFromInt(100))
	}
	if totalValidations > 0 {
		data.ValidationPassRate = decimal.NewFromInt(totalValidations - totalFailures).Div(decimal.NewFromInt(totalValidations)).Mul(decimal.NewFromInt(100))
	}

	return data
}

// WeightToTier 将重量转换为档位，从配置获取最大重量值
func (s *weightTierCacheService) WeightToTier(weight float64) (int, error) {
	weightKg := int(math.Ceil(weight))

	maxWeight, err := s.getMaxCacheWeight()
	if err != nil {
		return 0, fmt.Errorf("获取最大缓存重量配置失败: %w", err)
	}

	if weightKg > maxWeight {
		return maxWeight, nil // 超过最大重量按最大重量处理
	}
	if weightKg < 1 {
		return 1, nil // 小于1kg按1kg处理
	}
	return weightKg, nil
}

// === 配置获取方法 ===

// getCacheResponseTime 获取缓存响应时间配置
func (s *weightTierCacheService) getCacheResponseTime() string {
	if s.configService == nil {
		s.logger.Error("配置服务未初始化")
		return "N/A"
	}

	value, err := s.configService.GetStringConfig(context.Background(), "weight_tier_cache", "cache_response_time", "<10ms")
	if err != nil {
		s.logger.Warn("获取缓存响应时间配置失败，使用默认值", zap.Error(err))
		return "<10ms"
	}
	return value
}

// getRealtimeResponseTime 获取实时响应时间配置
func (s *weightTierCacheService) getRealtimeResponseTime() string {
	if s.configService == nil {
		s.logger.Error("配置服务未初始化")
		return "N/A"
	}

	value, err := s.configService.GetStringConfig(context.Background(), "weight_tier_cache", "realtime_response_time", "~800ms")
	if err != nil {
		s.logger.Warn("获取实时响应时间配置失败，使用默认值", zap.Error(err))
		return "~800ms"
	}
	return value
}

// getMaxCacheWeight 获取最大缓存重量配置
func (s *weightTierCacheService) getMaxCacheWeight() (int, error) {
	if s.configService == nil {
		return 0, fmt.Errorf("配置服务未初始化，请检查系统配置")
	}

	value, err := s.configService.GetIntConfig(context.Background(), "weight_tier_cache", "max_cache_weight", 20)
	if err != nil {
		return 0, fmt.Errorf("获取最大缓存重量配置失败: %w。请在system_configs表中添加配置项：config_group='weight_tier_cache', config_key='max_cache_weight'", err)
	}
	return value, nil
}

// getActiveExpressCodes 获取所有活跃的快递公司代码
// 严格从数据库获取，不提供任何备用方案
func (s *weightTierCacheService) getActiveExpressCodes(ctx context.Context) ([]string, error) {
	if s.expressService == nil {
		return nil, fmt.Errorf("快递公司服务未初始化，请检查系统配置")
	}

	companies, err := s.expressService.GetActiveCompanies(ctx)
	if err != nil {
		s.logger.Error("从数据库获取活跃快递公司失败", zap.Error(err))
		return nil, fmt.Errorf("获取活跃快递公司失败: %w。请确保数据库连接正常且express_companies表中存在活跃的快递公司记录", err)
	}

	var expressCodes []string
	for _, company := range companies {
		if company.Code != "" {
			expressCodes = append(expressCodes, company.Code)
		}
	}

	if len(expressCodes) == 0 {
		return nil, fmt.Errorf("数据库中没有找到活跃的快递公司。请在express_companies表中添加快递公司记录并设置is_active=true")
	}

	s.logger.Info("从数据库获取活跃快递代码",
		zap.Strings("express_codes", expressCodes),
		zap.Int("count", len(expressCodes)))

	return expressCodes, nil
}

// === 定义缺失的模型类型 ===

// RealtimePriceInfo 实时价格信息 (优化版 - 支持续重价格和产品信息)
type RealtimePriceInfo struct {
	Price                decimal.Decimal `json:"price"`
	ContinuedWeightPerKg decimal.Decimal `json:"continued_weight_per_kg"`
	ProductCode          string          `json:"product_code"`
	ProductName          string          `json:"product_name"`
	ChannelID            string          `json:"channel_id"`
	EstimatedDays        int             `json:"estimated_days"`
	ExpressName          string          `json:"express_name"`
	DeliveryTime         string          `json:"delivery_time"`
}

// extractCityFromProvince 从省份名称智能提取城市名称
// 用于缓存查询时构造合理的城市字段
func (s *weightTierCacheService) extractCityFromProvince(province string) string {
	// 直辖市：省份名就是城市名
	directMunicipalities := map[string]string{
		"北京市": "北京市",
		"上海市": "上海市",
		"天津市": "天津市",
		"重庆市": "重庆市",
	}

	if city, exists := directMunicipalities[province]; exists {
		return city
	}

	// 特别行政区
	if province == "香港特别行政区" {
		return "香港"
	}
	if province == "澳门特别行政区" {
		return "澳门"
	}

	// 其他省份：使用省会城市作为默认值
	provincialCapitals := map[string]string{
		"广东省":      "广州市",
		"浙江省":      "杭州市",
		"江苏省":      "南京市",
		"山东省":      "济南市",
		"河南省":      "郑州市",
		"四川省":      "成都市",
		"湖北省":      "武汉市",
		"湖南省":      "长沙市",
		"河北省":      "石家庄市",
		"福建省":      "福州市",
		"安徽省":      "合肥市",
		"江西省":      "南昌市",
		"辽宁省":      "沈阳市",
		"吉林省":      "长春市",
		"黑龙江省":     "哈尔滨市",
		"山西省":      "太原市",
		"陕西省":      "西安市",
		"甘肃省":      "兰州市",
		"青海省":      "西宁市",
		"云南省":      "昆明市",
		"贵州省":      "贵阳市",
		"海南省":      "海口市",
		"广西壮族自治区":  "南宁市",
		"西藏自治区":    "拉萨市",
		"新疆维吾尔自治区": "乌鲁木齐市",
		"内蒙古自治区":   "呼和浩特市",
		"宁夏回族自治区":  "银川市",
		"台湾省":      "台北市",
	}

	if capital, exists := provincialCapitals[province]; exists {
		return capital
	}

	// 兜底：去掉"省"、"市"、"自治区"等后缀，加上"市"
	city := strings.TrimSuffix(province, "省")
	city = strings.TrimSuffix(city, "市")
	city = strings.TrimSuffix(city, "自治区")
	city = strings.TrimSuffix(city, "特别行政区")

	// 如果处理后为空或太短，使用原省份名
	if len(city) < 2 {
		return province
	}

	return city + "市"
}

// getDefaultDistrict 获取省份对应的默认区县
// 覆盖全国31个省（直辖市、自治区），每个省一个真实区县
func (s *weightTierCacheService) getDefaultDistrict(province, city string) string {
	// 省份到默认区县的映射表
	districtMap := map[string]string{
		// 直辖市
		"北京市": "朝阳区",
		"上海市": "浦东新区",
		"天津市": "和平区",
		"重庆市": "渝中区",

		// 华北地区
		"河北省":    "长安区", // 石家庄市长安区
		"山西省":    "小店区", // 太原市小店区
		"内蒙古自治区": "新城区", // 呼和浩特市新城区

		// 东北地区
		"辽宁省":  "和平区", // 沈阳市和平区
		"吉林省":  "南关区", // 长春市南关区
		"黑龙江省": "南岗区", // 哈尔滨市南岗区

		// 华东地区
		"江苏省": "玄武区", // 南京市玄武区
		"浙江省": "西湖区", // 杭州市西湖区
		"安徽省": "蜀山区", // 合肥市蜀山区
		"福建省": "鼓楼区", // 福州市鼓楼区
		"江西省": "东湖区", // 南昌市东湖区
		"山东省": "历下区", // 济南市历下区

		// 华中地区
		"河南省": "金水区", // 郑州市金水区
		"湖北省": "江汉区", // 武汉市江汉区
		"湖南省": "芙蓉区", // 长沙市芙蓉区

		// 华南地区
		"广东省":     "天河区", // 广州市天河区
		"广西壮族自治区": "青秀区", // 南宁市青秀区
		"海南省":     "龙华区", // 海口市龙华区

		// 西南地区
		"四川省":   "锦江区", // 成都市锦江区
		"贵州省":   "南明区", // 贵阳市南明区
		"云南省":   "五华区", // 昆明市五华区
		"西藏自治区": "城关区", // 拉萨市城关区

		// 西北地区
		"陕西省":      "雁塔区", // 西安市雁塔区
		"甘肃省":      "城关区", // 兰州市城关区
		"青海省":      "城东区", // 西宁市城东区
		"宁夏回族自治区":  "兴庆区", // 银川市兴庆区
		"新疆维吾尔自治区": "天山区", // 乌鲁木齐市天山区

		// 特别行政区和台湾
		"香港特别行政区": "中西区",
		"澳门特别行政区": "花地玛堂区",
		"台湾省":     "中正区", // 台北市中正区
	}

	if district, exists := districtMap[province]; exists {
		return district
	}

	// 兜底：返回通用区县名
	return "中心区"
}

// checkProviderSupportsCompany 检查供应商是否支持指定快递公司（检查数据库is_supported字段）
func (s *weightTierCacheService) checkProviderSupportsCompany(ctx context.Context, providerCode, companyCode string) (bool, error) {
	if s.mappingService == nil {
		return true, nil // 如果映射服务未设置，默认支持（向后兼容）
	}

	// 获取供应商支持的快递公司列表
	supportedCompanies, err := s.mappingService.GetSupportedCompanies(ctx, providerCode)
	if err != nil {
		return false, fmt.Errorf("获取供应商支持的快递公司列表失败: %w", err)
	}

	// 检查指定快递公司是否在支持列表中
	for _, mapping := range supportedCompanies {
		if mapping.CompanyCode == companyCode && mapping.IsSupported {
			return true, nil
		}
	}

	return false, nil
}

// buildRealAddress 构建真实的详细地址
func (s *weightTierCacheService) buildRealAddress(district string) string {
	return fmt.Sprintf("%s建设路123号", district)
}

// GetProviderGroupedOverview 获取按供应商分组的缓存概览
func (s *weightTierCacheService) GetProviderGroupedOverview(ctx context.Context) ([]*model.ProviderGroup, error) {
	s.logger.Info("查询按供应商分组的缓存概览")

	groups, err := s.cacheRepo.GetProviderGroupedOverview(ctx)
	if err != nil {
		s.logger.Error("查询供应商分组概览失败", zap.Error(err))
		return nil, err
	}

	s.logger.Info("供应商分组概览查询完成", zap.Int("group_count", len(groups)))
	return groups, nil
}

// GetCacheDetails 获取缓存详情
func (s *weightTierCacheService) GetCacheDetails(ctx context.Context, req *model.CacheDetailRequest) (*model.CacheDetailResponse, error) {
	s.logger.Info("查询缓存详情",
		zap.String("provider", req.Provider),
		zap.String("express_code", req.ExpressCode),
		zap.String("route", req.Route))

	records, total, err := s.cacheRepo.GetCacheDetails(ctx, req)
	if err != nil {
		s.logger.Error("查询缓存详情失败", zap.Error(err))
		return &model.CacheDetailResponse{
			Success: false,
			Code:    model.StatusInternalServerError,
			Message: "查询失败: " + err.Error(),
		}, nil
	}

	response := &model.CacheDetailResponse{
		Success: true,
		Code:    model.StatusSuccess,
		Message: "查询成功",
	}
	response.Data.Records = records
	response.Data.Total = total
	response.Data.Page = req.Page
	response.Data.PageSize = req.PageSize

	s.logger.Info("缓存详情查询完成",
		zap.Int64("total", total),
		zap.Int("records", len(records)))

	return response, nil
}

// BatchInvalidateCache 批量清理缓存
func (s *weightTierCacheService) BatchInvalidateCache(ctx context.Context, provider, expressCode, route string) error {
	s.logger.Info("批量清理缓存",
		zap.String("provider", provider),
		zap.String("express_code", expressCode),
		zap.String("route", route))

	err := s.cacheRepo.BatchInvalidateCache(ctx, provider, expressCode, route)
	if err != nil {
		s.logger.Error("批量清理缓存失败", zap.Error(err))
		return err
	}

	s.logger.Info("批量清理缓存完成")
	return nil
}

// 🚀 GetCacheOverviewOptimized 获取缓存概览（优化版本）
func (s *weightTierCacheService) GetCacheOverviewOptimized(ctx context.Context, req *model.CacheOverviewRequest) (*model.CacheOverviewResponse, error) {
	s.logger.Info("查询优化缓存概览",
		zap.Int("page", req.Page),
		zap.Int("page_size", req.PageSize),
		zap.String("provider", req.Provider))

	response, err := s.cacheRepo.GetCacheOverviewOptimized(ctx, req)
	if err != nil {
		s.logger.Error("查询优化缓存概览失败", zap.Error(err))
		return nil, err
	}

	s.logger.Info("优化缓存概览查询完成",
		zap.Int64("total_count", response.Data.TotalCount),
		zap.Int("page_count", len(response.Data.Data)))

	return response, nil
}

// 🚀 GetProviderGroupedOverviewOptimized 获取供应商分组概览（优化版本）
func (s *weightTierCacheService) GetProviderGroupedOverviewOptimized(ctx context.Context) ([]*model.ProviderGroup, error) {
	s.logger.Info("查询优化供应商分组概览")

	groups, err := s.cacheRepo.GetProviderGroupedOverviewOptimized(ctx)
	if err != nil {
		s.logger.Error("查询优化供应商分组概览失败", zap.Error(err))
		return nil, err
	}

	s.logger.Info("优化供应商分组概览查询完成", zap.Int("group_count", len(groups)))
	return groups, nil
}

// 🚀 RefreshCacheViews 刷新缓存视图
func (s *weightTierCacheService) RefreshCacheViews(ctx context.Context) error {
	s.logger.Info("开始刷新缓存视图")

	err := s.cacheRepo.RefreshCacheViews(ctx)
	if err != nil {
		s.logger.Error("刷新缓存视图失败", zap.Error(err))
		return err
	}

	s.logger.Info("缓存视图刷新完成")
	return nil
}

// 🚀 GetQuickStats 获取快速统计
func (s *weightTierCacheService) GetQuickStats(ctx context.Context) (*model.QuickStatsResponse, error) {
	s.logger.Info("查询快速统计")

	// 使用优化的供应商分组查询
	groups, err := s.cacheRepo.GetProviderGroupedOverviewOptimized(ctx)
	if err != nil {
		s.logger.Error("查询快速统计失败", zap.Error(err))
		return &model.QuickStatsResponse{
			Success: false,
			Code:    model.StatusInternalServerError,
			Message: "查询失败: " + err.Error(),
		}, err
	}

	// 汇总统计数据
	var totalProviders int
	var totalCacheEntries int64
	var validCacheCount int64
	var totalHitCount int64
	var latestUpdate string

	for _, group := range groups {
		totalProviders++
		totalCacheEntries += int64(group.TotalCacheCount)
		validCacheCount += int64(group.ValidCacheCount)
		totalHitCount += group.TotalHitCount

		// 找到最新的更新时间
		if group.LastUpdateTime > latestUpdate {
			latestUpdate = group.LastUpdateTime
		}
	}

	// 计算整体缓存命中率
	var cacheHitRate float64
	if totalCacheEntries > 0 {
		cacheHitRate = float64(validCacheCount) / float64(totalCacheEntries)
	}

	response := &model.QuickStatsResponse{
		Success: true,
		Code:    model.StatusSuccess,
		Message: "查询成功",
	}

	response.Data.TotalProviders = totalProviders
	response.Data.TotalCacheEntries = totalCacheEntries
	response.Data.ValidCacheCount = validCacheCount
	response.Data.CacheHitRate = cacheHitRate
	response.Data.LastUpdated = latestUpdate

	s.logger.Info("快速统计查询完成",
		zap.Int("total_providers", totalProviders),
		zap.Int64("total_cache_entries", totalCacheEntries),
		zap.Float64("cache_hit_rate", cacheHitRate))

	return response, nil
}
