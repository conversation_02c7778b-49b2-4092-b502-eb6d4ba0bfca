package service

import (
	"context"
	"sync"
	"time"

	"go.uber.org/zap"
)

// ScheduledTaskService 定时任务服务
// 🔥 用于定期检查和处理超时的取消订单以及回调一致性问题
type ScheduledTaskService struct {
	timeoutService     *CancellationTimeoutService
	consistencyService *CallbackConsistencyService
	logger             *zap.Logger
	stopChan           chan struct{}
	wg                 sync.WaitGroup
	running            bool
	mu                 sync.RWMutex
}

// NewScheduledTaskService 创建定时任务服务
func NewScheduledTaskService(
	timeoutService *CancellationTimeoutService,
	consistencyService *CallbackConsistencyService,
	logger *zap.Logger,
) *ScheduledTaskService {
	return &ScheduledTaskService{
		timeoutService:     timeoutService,
		consistencyService: consistencyService,
		logger:             logger,
		stopChan:           make(chan struct{}),
	}
}

// Start 启动定时任务
func (s *ScheduledTaskService) Start(ctx context.Context) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.running {
		s.logger.Warn("定时任务服务已经在运行中")
		return nil
	}

	s.running = true
	s.logger.Info("🚀 启动定时任务服务")

	// 启动超时订单检查任务
	s.wg.Add(1)
	go s.runTimeoutOrderChecker(ctx)

	// 启动回调一致性检查任务
	s.wg.Add(1)
	go s.runCallbackConsistencyChecker(ctx)

	return nil
}

// Stop 停止定时任务
func (s *ScheduledTaskService) Stop() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if !s.running {
		s.logger.Warn("定时任务服务未在运行")
		return nil
	}

	s.logger.Info("🛑 停止定时任务服务")
	close(s.stopChan)
	s.wg.Wait()
	s.running = false

	return nil
}

// runTimeoutOrderChecker 运行超时订单检查器
func (s *ScheduledTaskService) runTimeoutOrderChecker(ctx context.Context) {
	defer s.wg.Done()

	// 🔥 配置：每30分钟检查一次超时订单
	ticker := time.NewTicker(30 * time.Minute)
	defer ticker.Stop()

	s.logger.Info("⏰ 超时订单检查器已启动，每30分钟检查一次")

	// 立即执行一次检查
	s.checkTimeoutOrders(ctx)

	for {
		select {
		case <-ctx.Done():
			s.logger.Info("📴 上下文取消，停止超时订单检查器")
			return
		case <-s.stopChan:
			s.logger.Info("📴 收到停止信号，停止超时订单检查器")
			return
		case <-ticker.C:
			s.checkTimeoutOrders(ctx)
		}
	}
}

// checkTimeoutOrders 检查超时订单
func (s *ScheduledTaskService) checkTimeoutOrders(ctx context.Context) {
	s.logger.Info("🔍 开始检查超时取消订单")
	start := time.Now()

	err := s.timeoutService.ProcessTimeoutCancellations(ctx)
	if err != nil {
		s.logger.Error("❌ 处理超时取消订单失败",
			zap.Error(err),
			zap.Duration("duration", time.Since(start)))
		return
	}

	s.logger.Info("✅ 超时取消订单检查完成",
		zap.Duration("duration", time.Since(start)))
}

// runCallbackConsistencyChecker 运行回调一致性检查器
func (s *ScheduledTaskService) runCallbackConsistencyChecker(ctx context.Context) {
	defer s.wg.Done()

	// 🔥 配置：每2小时检查一次回调一致性
	ticker := time.NewTicker(2 * time.Hour)
	defer ticker.Stop()

	s.logger.Info("⏰ 回调一致性检查器已启动，每2小时检查一次")

	// 立即执行一次检查
	s.checkCallbackConsistency(ctx)

	for {
		select {
		case <-ctx.Done():
			s.logger.Info("📴 上下文取消，停止回调一致性检查器")
			return
		case <-s.stopChan:
			s.logger.Info("📴 收到停止信号，停止回调一致性检查器")
			return
		case <-ticker.C:
			s.checkCallbackConsistency(ctx)
		}
	}
}

// checkCallbackConsistency 检查回调一致性
func (s *ScheduledTaskService) checkCallbackConsistency(ctx context.Context) {
	s.logger.Info("🔍 开始检查回调一致性")
	start := time.Now()

	// 检查主要供应商的回调一致性
	providers := []string{"kuaidi100", "kuaidiniao", "yida", "yuntong", "cainiao"}

	for _, provider := range providers {
		s.logger.Info("🔍 检查供应商回调一致性", zap.String("provider", provider))

		// 检查最近2小时的回调
		inconsistentCallbacks, err := s.consistencyService.CheckCallbackConsistency(ctx, provider, 2)
		if err != nil {
			s.logger.Error("❌ 检查供应商回调一致性失败",
				zap.String("provider", provider),
				zap.Error(err))
			continue
		}

		if len(inconsistentCallbacks) > 0 {
			s.logger.Warn("⚠️ 发现不一致的回调",
				zap.String("provider", provider),
				zap.Int("count", len(inconsistentCallbacks)))

			// 自动修复少量问题回调（最多10个）
			if len(inconsistentCallbacks) <= 10 {
				callbackIDs := make([]string, len(inconsistentCallbacks))
				for i, callback := range inconsistentCallbacks {
					callbackIDs[i] = callback.CallbackID
				}

				s.logger.Info("🔧 自动修复不一致回调",
					zap.String("provider", provider),
					zap.Int("count", len(callbackIDs)))

				result, err := s.consistencyService.FixInconsistentCallbacks(ctx, callbackIDs)
				if err != nil {
					s.logger.Error("❌ 自动修复回调失败",
						zap.String("provider", provider),
						zap.Error(err))
				} else {
					s.logger.Info("✅ 自动修复回调完成",
						zap.String("provider", provider),
						zap.Int("success", result.Success),
						zap.Int("failed", result.Failed))
				}
			} else {
				s.logger.Warn("⚠️ 不一致回调过多，需要手动处理",
					zap.String("provider", provider),
					zap.Int("count", len(inconsistentCallbacks)))
			}
		} else {
			s.logger.Info("✅ 供应商回调一致性正常", zap.String("provider", provider))
		}
	}

	s.logger.Info("✅ 回调一致性检查完成",
		zap.Duration("duration", time.Since(start)))
}

// GetStatus 获取定时任务状态
func (s *ScheduledTaskService) GetStatus() *TaskServiceStatus {
	s.mu.RLock()
	defer s.mu.RUnlock()

	return &TaskServiceStatus{
		Running:   s.running,
		StartTime: time.Now(), // 这里应该记录实际的启动时间
	}
}

// TaskServiceStatus 定时任务服务状态
type TaskServiceStatus struct {
	Running   bool      `json:"running"`
	StartTime time.Time `json:"start_time"`
}
