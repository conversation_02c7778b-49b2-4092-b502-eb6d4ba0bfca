package service

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"

	"github.com/your-org/go-kuaidi/internal/model"
)

// AsyncBalanceService 异步余额处理服务 - 高并发专用
type AsyncBalanceService struct {
	baseService     *DefaultBalanceService
	redisClient     *redis.Client
	logger          *zap.Logger
	workers         int
	batchSize       int
	flushInterval   time.Duration
	stopChan        chan struct{}
	processingQueue chan *BalanceOperation
	userQueues      map[string]*UserBalanceQueue
	queuesMutex     sync.RWMutex
	operationCache  map[string]bool  // 操作缓存，避免重复检查
	cacheMutex      sync.RWMutex     // 缓存锁
}

// BalanceOperation 余额操作结构
type BalanceOperation struct {
	ID              string                  `json:"id"`
	UserID          string                  `json:"user_id"`
	OrderNo         string                  `json:"order_no"`
	CustomerOrderNo string                  `json:"customer_order_no"`
	Amount          decimal.Decimal         `json:"amount"`
	Type            model.TransactionType   `json:"type"`
	Priority        int                     `json:"priority"` // 0=正常, 1=高优先级, 2=紧急
	RetryCount      int                     `json:"retry_count"`  // 重试次数
	CreatedAt       time.Time               `json:"created_at"`
	Timeout         time.Time               `json:"timeout"`
	Callback        func(error)             `json:"-"`
	Context         context.Context         `json:"-"`
	ProcessingLock  sync.Mutex              `json:"-"`  // 处理锁，防止重复处理
	IsProcessing    bool                    `json:"-"`  // 是否正在处理
}

// UserBalanceQueue 用户级余额操作队列 - 确保单用户操作顺序性
type UserBalanceQueue struct {
	UserID      string
	Operations  []*BalanceOperation
	LastUpdate  time.Time
	Processing  bool
	mutex       sync.Mutex
}

// NewAsyncBalanceService 创建异步余额服务
func NewAsyncBalanceService(baseService *DefaultBalanceService, redisClient *redis.Client, logger *zap.Logger) *AsyncBalanceService {
	service := &AsyncBalanceService{
		baseService:     baseService,
		redisClient:     redisClient,
		logger:          logger,
		workers:         20,           // 20个工作协程
		batchSize:       100,          // 批处理大小
		flushInterval:   50 * time.Millisecond, // 50ms刷新间隔
		stopChan:        make(chan struct{}),
		processingQueue: make(chan *BalanceOperation, 10000), // 10K缓冲
		userQueues:      make(map[string]*UserBalanceQueue),
		operationCache:  make(map[string]bool),
	}

	// 启动工作协程池
	service.startWorkers()

	// 启动队列管理器
	go service.queueManager()

	// 启动批量处理器
	go service.batchProcessor()

	return service
}

// SubmitBalanceOperation 提交余额操作 - 主要接口
func (s *AsyncBalanceService) SubmitBalanceOperation(ctx context.Context, op *BalanceOperation) error {
	// 设置操作ID和时间戳
	if op.ID == "" {
		op.ID = generateOperationID()
	}
	op.CreatedAt = time.Now()
	op.Context = ctx
	
	// 设置超时时间
	if op.Timeout.IsZero() {
		op.Timeout = time.Now().Add(30 * time.Second)
	}

	s.logger.Info("🚀 提交余额操作",
		zap.String("operation_id", op.ID),
		zap.String("user_id", op.UserID),
		zap.String("order_no", op.OrderNo),
		zap.String("amount", op.Amount.String()),
		zap.String("type", string(op.Type)),
		zap.Int("priority", op.Priority))

	// 添加到用户队列
	s.addToUserQueue(op)

	// 发送到处理队列
	select {
	case s.processingQueue <- op:
		return nil
	case <-ctx.Done():
		return ctx.Err()
	case <-time.After(5 * time.Second):
		return fmt.Errorf("队列已满，操作超时")
	}
}

// addToUserQueue 添加操作到用户级队列
func (s *AsyncBalanceService) addToUserQueue(op *BalanceOperation) {
	s.queuesMutex.Lock()
	defer s.queuesMutex.Unlock()

	userQueue, exists := s.userQueues[op.UserID]
	if !exists {
		userQueue = &UserBalanceQueue{
			UserID:     op.UserID,
			Operations: make([]*BalanceOperation, 0),
			LastUpdate: time.Now(),
		}
		s.userQueues[op.UserID] = userQueue
	}

	userQueue.mutex.Lock()
	defer userQueue.mutex.Unlock()

	// 按优先级插入
	inserted := false
	for i, existingOp := range userQueue.Operations {
		if op.Priority > existingOp.Priority {
			userQueue.Operations = append(userQueue.Operations[:i], 
				append([]*BalanceOperation{op}, userQueue.Operations[i:]...)...)
			inserted = true
			break
		}
	}
	
	if !inserted {
		userQueue.Operations = append(userQueue.Operations, op)
	}

	userQueue.LastUpdate = time.Now()
}

// startWorkers 启动工作协程池
func (s *AsyncBalanceService) startWorkers() {
	for i := 0; i < s.workers; i++ {
		go s.worker(i)
	}
	s.logger.Info("✅ 异步余额处理工作池启动", zap.Int("workers", s.workers))
}

// worker 工作协程
func (s *AsyncBalanceService) worker(workerID int) {
	s.logger.Info("🔧 启动余额处理工作协程", zap.Int("worker_id", workerID))

	for {
		select {
		case op := <-s.processingQueue:
			s.processOperation(workerID, op)
		case <-s.stopChan:
			s.logger.Info("⏹️ 工作协程停止", zap.Int("worker_id", workerID))
			return
		}
	}
}

// processOperation 处理单个余额操作
func (s *AsyncBalanceService) processOperation(workerID int, op *BalanceOperation) {
	startTime := time.Now()
	operationLogger := s.logger.With(
		zap.Int("worker_id", workerID),
		zap.String("operation_id", op.ID),
		zap.String("user_id", op.UserID),
		zap.String("order_no", op.OrderNo))

	// 尝试获取处理锁，防止重复处理
	op.ProcessingLock.Lock()
	if op.IsProcessing {
		op.ProcessingLock.Unlock()
		operationLogger.Warn("🔄 操作正在被其他worker处理，跳过")
		return
	}
	op.IsProcessing = true
	op.ProcessingLock.Unlock()

	// 确保处理完成后释放锁
	defer func() {
		op.ProcessingLock.Lock()
		op.IsProcessing = false
		op.ProcessingLock.Unlock()
	}()

	operationLogger.Info("🔄 开始处理余额操作")

	// 检查超时
	if time.Now().After(op.Timeout) {
		operationLogger.Warn("⏰ 操作超时，跳过处理")
		if op.Callback != nil {
			op.Callback(fmt.Errorf("操作超时"))
		}
		return
	}
	
	// 检查是否已经处理过相同的订单操作
	if s.isDuplicateOperation(op) {
		operationLogger.Warn("🔄 检测到重复操作，跳过处理")
		if op.Callback != nil {
			op.Callback(nil) // 成功回调，因为已经处理过
		}
		return
	}
	
	// 检查是否已经存在相同订单的交易记录
	if s.isTransactionExists(op) {
		operationLogger.Warn("🔄 检测到订单已有交易记录，跳过处理")
		if op.Callback != nil {
			op.Callback(nil) // 成功回调，因为已经处理过
		}
		return
	}

	// 执行具体的余额操作
	var err error
	
	// 创建带超时的上下文，防止长时间阻塞
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	
	switch op.Type {
	case model.TransactionTypeOrderPreCharge:
		err = s.baseService.PreChargeForOrder(ctx, op.UserID, op.OrderNo, op.Amount.Abs())
	case model.TransactionTypeOrderCancelRefund:
		err = s.baseService.RefundForOrder(ctx, op.UserID, op.OrderNo, op.Amount.Abs())
	case model.TransactionTypeBillingDifference:
		err = s.baseService.ChargeForBillingDifference(ctx, op.UserID, op.OrderNo, op.Amount.Abs())
	case model.TransactionTypeBillingDifferenceRefund:
		err = s.baseService.RefundForBillingDifference(ctx, op.UserID, op.OrderNo, op.Amount.Abs())
	default:
		err = fmt.Errorf("不支持的操作类型: %s", op.Type)
	}

	duration := time.Since(startTime)

	if err != nil {
		operationLogger.Error("❌ 余额操作失败",
			zap.Error(err),
			zap.Duration("duration", duration))
		
		// 如果是版本冲突，重新排队（有限制）
		if isVersionConflictError(err) {
			// 增加重试计数
			if op.RetryCount < 3 {
				op.RetryCount++
				operationLogger.Warn("版本冲突，重新排队",
					zap.Int("retry_count", op.RetryCount))
				s.requeueOperation(op)
				return
			} else {
				operationLogger.Error("达到最大重试次数，放弃操作",
					zap.Int("retry_count", op.RetryCount))
			}
		}
	} else {
		operationLogger.Info("✅ 余额操作成功",
			zap.Duration("duration", duration))
	}

	// 执行回调
	if op.Callback != nil {
		op.Callback(err)
	}

	// 从用户队列中移除
	s.removeFromUserQueue(op)
}

// requeueOperation 重新排队操作
func (s *AsyncBalanceService) requeueOperation(op *BalanceOperation) {
	op.Priority++ // 提高优先级
	op.CreatedAt = time.Now()
	
	if op.Priority > 5 { // 最大重试5次
		s.logger.Error("🚨 操作重试次数超限，放弃处理",
			zap.String("operation_id", op.ID),
			zap.String("user_id", op.UserID))
		if op.Callback != nil {
			op.Callback(fmt.Errorf("重试次数超限"))
		}
		return
	}

	// 指数退避延迟重试
	backoffDuration := time.Duration(op.RetryCount*op.RetryCount) * 100 * time.Millisecond
	if backoffDuration > 2*time.Second {
		backoffDuration = 2 * time.Second
	}
	
	go func() {
		time.Sleep(backoffDuration)
		s.processingQueue <- op
	}()
}

// removeFromUserQueue 从用户队列移除操作
func (s *AsyncBalanceService) removeFromUserQueue(op *BalanceOperation) {
	s.queuesMutex.Lock()
	defer s.queuesMutex.Unlock()

	userQueue, exists := s.userQueues[op.UserID]
	if !exists {
		return
	}

	userQueue.mutex.Lock()
	defer userQueue.mutex.Unlock()

	for i, queueOp := range userQueue.Operations {
		if queueOp.ID == op.ID {
			userQueue.Operations = append(userQueue.Operations[:i], userQueue.Operations[i+1:]...)
			break
		}
	}

	// 如果队列为空且长时间没有更新，清理队列
	if len(userQueue.Operations) == 0 && time.Since(userQueue.LastUpdate) > 5*time.Minute {
		delete(s.userQueues, op.UserID)
	}
}

// queueManager 队列管理器 - 定期清理和优化
func (s *AsyncBalanceService) queueManager() {
	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			s.cleanupQueues()
			s.reportQueueStatus()
		case <-s.stopChan:
			return
		}
	}
}

// cleanupQueues 清理过期队列
func (s *AsyncBalanceService) cleanupQueues() {
	s.queuesMutex.Lock()
	defer s.queuesMutex.Unlock()

	now := time.Now()
	cleanupCount := 0

	for userID, queue := range s.userQueues {
		queue.mutex.Lock()
		
		// 移除超时操作
		validOps := make([]*BalanceOperation, 0, len(queue.Operations))
		for _, op := range queue.Operations {
			if now.Before(op.Timeout) {
				validOps = append(validOps, op)
			} else {
				s.logger.Warn("🗑️ 清理超时操作",
					zap.String("operation_id", op.ID),
					zap.String("user_id", userID))
			}
		}
		queue.Operations = validOps

		// 清理空闲队列
		if len(queue.Operations) == 0 && now.Sub(queue.LastUpdate) > 10*time.Minute {
			delete(s.userQueues, userID)
			cleanupCount++
		}
		
		queue.mutex.Unlock()
	}

	if cleanupCount > 0 {
		s.logger.Info("🧹 队列清理完成", zap.Int("cleaned_queues", cleanupCount))
	}
}

// reportQueueStatus 报告队列状态
func (s *AsyncBalanceService) reportQueueStatus() {
	s.queuesMutex.RLock()
	defer s.queuesMutex.RUnlock()

	totalOperations := 0
	for _, queue := range s.userQueues {
		queue.mutex.Lock()
		totalOperations += len(queue.Operations)
		queue.mutex.Unlock()
	}

	// 添加缓存统计
	s.cacheMutex.RLock()
	cacheSize := len(s.operationCache)
	s.cacheMutex.RUnlock()
	
	s.logger.Info("📊 异步余额队列状态报告",
		zap.Int("active_users", len(s.userQueues)),
		zap.Int("total_operations", totalOperations),
		zap.Int("processing_queue_size", len(s.processingQueue)),
		zap.Int("cache_size", cacheSize))
}

// batchProcessor 批量处理器 - 合并同用户操作
func (s *AsyncBalanceService) batchProcessor() {
	ticker := time.NewTicker(s.flushInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			s.processBatches()
		case <-s.stopChan:
			return
		}
	}
}

// processBatches 处理批量操作
func (s *AsyncBalanceService) processBatches() {
	s.queuesMutex.RLock()
	defer s.queuesMutex.RUnlock()

	for userID, queue := range s.userQueues {
		if queue.Processing || len(queue.Operations) == 0 {
			continue
		}

		queue.mutex.Lock()
		if len(queue.Operations) >= s.batchSize || 
		   time.Since(queue.Operations[0].CreatedAt) > s.flushInterval*2 {
			// 标记为处理中
			queue.Processing = true
			
			// 复制操作列表
			operations := make([]*BalanceOperation, len(queue.Operations))
			copy(operations, queue.Operations)
			
			queue.mutex.Unlock()

			// 异步处理批量操作
			go s.processBatchOperations(userID, operations)
		} else {
			queue.mutex.Unlock()
		}
	}
}

// processBatchOperations 处理批量操作
func (s *AsyncBalanceService) processBatchOperations(userID string, operations []*BalanceOperation) {
	defer func() {
		s.queuesMutex.Lock()
		if queue, exists := s.userQueues[userID]; exists {
			queue.Processing = false
		}
		s.queuesMutex.Unlock()
	}()

	s.logger.Info("🔄 开始批量处理用户余额操作",
		zap.String("user_id", userID),
		zap.Int("operation_count", len(operations)))

	// 按类型分组操作
	chargeOps := make([]*BalanceOperation, 0)
	refundOps := make([]*BalanceOperation, 0)

	for _, op := range operations {
		if op.Amount.IsNegative() || op.Type == model.TransactionTypeOrderPreCharge {
			chargeOps = append(chargeOps, op)
		} else {
			refundOps = append(refundOps, op)
		}
	}

	// 先处理退款，再处理扣费
	s.processBatchByType(userID, refundOps, "refund")
	s.processBatchByType(userID, chargeOps, "charge")
}

// processBatchByType 按类型批量处理
func (s *AsyncBalanceService) processBatchByType(userID string, operations []*BalanceOperation, opType string) {
	if len(operations) == 0 {
		return
	}

	// 计算总金额
	totalAmount := decimal.Zero
	for _, op := range operations {
		totalAmount = totalAmount.Add(op.Amount.Abs())
	}

	s.logger.Info("🔄 批量处理操作",
		zap.String("user_id", userID),
		zap.String("type", opType),
		zap.Int("count", len(operations)),
		zap.String("total_amount", totalAmount.String()))

	// 这里可以实现具体的批量处理逻辑
	// 暂时仍然单个处理，但为未来优化预留接口
	for _, op := range operations {
		s.processOperation(0, op)
	}
}

// Stop 停止异步服务
func (s *AsyncBalanceService) Stop() {
	s.logger.Info("⏹️ 停止异步余额服务")
	close(s.stopChan)
}

// GetQueueStats 获取队列统计信息
func (s *AsyncBalanceService) GetQueueStats() map[string]interface{} {
	s.queuesMutex.RLock()
	defer s.queuesMutex.RUnlock()

	stats := map[string]interface{}{
		"active_users":          len(s.userQueues),
		"processing_queue_size": len(s.processingQueue),
		"workers":              s.workers,
		"batch_size":           s.batchSize,
	}

	// 按用户统计
	userStats := make(map[string]int)
	for userID, queue := range s.userQueues {
		queue.mutex.Lock()
		userStats[userID] = len(queue.Operations)
		queue.mutex.Unlock()
	}
	stats["user_operations"] = userStats

	return stats
}

// 辅助函数
func generateOperationID() string {
	return fmt.Sprintf("op_%d", time.Now().UnixNano())
}

// isDuplicateOperation 检查是否为重复操作
func (s *AsyncBalanceService) isDuplicateOperation(op *BalanceOperation) bool {
	// 检查是否存在相同订单号和类型的处理中操作
	s.queuesMutex.RLock()
	defer s.queuesMutex.RUnlock()
	
	for _, queue := range s.userQueues {
		for _, queuedOp := range queue.Operations {
			if queuedOp.OrderNo == op.OrderNo && 
			   queuedOp.Type == op.Type && 
			   queuedOp.ID != op.ID {
				return true
			}
		}
	}
	
	return false
}

// isTransactionExists 检查订单是否已有对应的交易记录
func (s *AsyncBalanceService) isTransactionExists(op *BalanceOperation) bool {
	// 🔥 修复：构造更准确的缓存key，包含用户ID和订单号
	cacheKey := fmt.Sprintf("%s:%s:%s", op.UserID, op.OrderNo, op.Type)
	
	// 先检查缓存
	s.cacheMutex.RLock()
	if exists, found := s.operationCache[cacheKey]; found {
		s.cacheMutex.RUnlock()
		return exists
	}
	s.cacheMutex.RUnlock()
	
	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()
	
	var exists bool
	// 🔥 修复：使用更精确的查询，包含用户ID和订单号的完整匹配
	switch op.Type {
	case model.TransactionTypeOrderPreCharge:
		// 检查是否已存在预扣费记录（必须同时匹配用户ID、订单号和交易类型）
		exists = s.checkTransactionExistsInDB(ctx, op.UserID, op.OrderNo, "order_pre_charge")
	case model.TransactionTypeOrderCancelRefund:
		// 检查是否已存在取消退款记录（必须同时匹配用户ID、订单号和交易类型）
		exists = s.checkTransactionExistsInDB(ctx, op.UserID, op.OrderNo, "order_cancel_refund")
	case model.TransactionTypeBillingDifference:
		// 检查费用差额补收记录
		exists = s.checkTransactionExistsInDB(ctx, op.UserID, op.OrderNo, "billing_difference")
	case model.TransactionTypeBillingDifferenceRefund:
		// 检查费用差额退款记录
		exists = s.checkTransactionExistsInDB(ctx, op.UserID, op.OrderNo, "billing_difference_refund")
	default:
		exists = false
	}
	
	// 缓存结果（5分钟过期）
	s.cacheMutex.Lock()
	s.operationCache[cacheKey] = exists
	s.cacheMutex.Unlock()
	
	// 异步清理过期缓存
	go func() {
		time.Sleep(5 * time.Minute)
		s.cacheMutex.Lock()
		delete(s.operationCache, cacheKey)
		s.cacheMutex.Unlock()
	}()
	
	return exists
}

// checkTransactionExistsInDB 检查数据库中是否存在指定的交易记录
func (s *AsyncBalanceService) checkTransactionExistsInDB(ctx context.Context, userID, orderNo, transactionType string) bool {
	// 🔥 修复：使用现有的 repository 方法进行更精确的查询
	switch transactionType {
	case "order_pre_charge":
		// 检查预扣费记录
		txn, err := s.baseService.repository.GetPreChargeTransactionByOrderNo(ctx, orderNo)
		if err == nil && txn != nil && txn.UserID == userID {
			s.logger.Warn("🚫 检测到重复预扣费交易", 
				zap.String("user_id", userID), 
				zap.String("order_no", orderNo), 
				zap.String("existing_txn_id", txn.ID))
			return true
		}
	case "order_cancel_refund":
		// 检查取消退款记录 - 需要更精确的检查
		// 使用订单号查询，但需要验证是取消退款类型
		txns, err := s.baseService.repository.GetTransactionsByUserIDWithFilters(
			ctx, userID, 100, 0, "order_cancel_refund", "completed", "", "", "", orderNo, "")
		if err == nil && len(txns) > 0 {
			for _, txn := range txns {
				if (txn.OrderNo == orderNo || txn.CustomerOrderNo == orderNo) && 
				   string(txn.Type) == "order_cancel_refund" {
					s.logger.Warn("🚫 检测到重复取消退款交易", 
						zap.String("user_id", userID), 
						zap.String("order_no", orderNo), 
						zap.String("existing_txn_id", txn.ID))
					return true
				}
			}
		}
	case "billing_difference", "billing_difference_refund":
		// 检查费用差额相关记录
		txns, err := s.baseService.repository.GetTransactionsByUserIDWithFilters(
			ctx, userID, 100, 0, transactionType, "completed", "", "", "", orderNo, "")
		if err == nil && len(txns) > 0 {
			for _, txn := range txns {
				if (txn.OrderNo == orderNo || txn.CustomerOrderNo == orderNo) && 
				   string(txn.Type) == transactionType {
					s.logger.Warn("🚫 检测到重复费用差额交易", 
						zap.String("user_id", userID), 
						zap.String("order_no", orderNo), 
						zap.String("transaction_type", transactionType),
						zap.String("existing_txn_id", txn.ID))
					return true
				}
			}
		}
	}
	
	return false
}

func isVersionConflictError(err error) bool {
	if err == nil {
		return false
	}
	return contains(err.Error(), "余额版本冲突") || contains(err.Error(), "version conflict")
}

// contains 检查字符串是否包含子串
func contains(s, substr string) bool {
	return len(s) >= len(substr) && stringContains(s, substr)
}

func stringContains(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}