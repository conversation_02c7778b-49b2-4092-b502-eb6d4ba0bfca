package service

import (
	"time"

	_ "github.com/lib/pq" // PostgreSQL driver
)

// ConfigManager 配置管理器接口
type ConfigManager interface {
	GetString(key string) string
	GetInt(key string) int
	GetDuration(key string) time.Duration
	Load() error
}

// MockConfigManager 模拟配置管理器（用于测试）
type MockConfigManager struct {
	configs map[string]interface{}
}

// NewMockConfigManager 创建模拟配置管理器
func NewMockConfigManager() *MockConfigManager {
	return &MockConfigManager{
		configs: make(map[string]interface{}),
	}
}

// GetString 获取字符串配置
func (m *MockConfigManager) GetString(key string) string {
	if val, ok := m.configs[key]; ok {
		if str, ok := val.(string); ok {
			return str
		}
	}
	return ""
}

// GetInt 获取整数配置
func (m *MockConfigManager) GetInt(key string) int {
	if val, ok := m.configs[key]; ok {
		if i, ok := val.(int); ok {
			return i
		}
	}
	return 0
}

// GetDuration 获取时间间隔配置
func (m *MockConfigManager) GetDuration(key string) time.Duration {
	if val, ok := m.configs[key]; ok {
		if d, ok := val.(time.Duration); ok {
			return d
		}
	}
	return 0
}

// Set 设置配置值
func (m *MockConfigManager) Set(key string, value interface{}) {
	m.configs[key] = value
}

// Load 加载配置（模拟实现）
func (m *MockConfigManager) Load() error {
	// 设置默认配置
	m.configs["database.connection_string"] = "*************************************************/go_kuaidi"
	m.configs["database.max_open_conns"] = 25
	m.configs["database.max_idle_conns"] = 5
	m.configs["database.conn_max_lifetime"] = 300 // 5分钟
	return nil
}
