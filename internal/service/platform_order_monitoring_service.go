package service

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"

	"github.com/your-org/go-kuaidi/internal/repository"
)

// PlatformOrderMonitoringService 平台订单号监控服务
// 🔥 企业级监控：完善平台订单号相关的监控指标和日志记录，确保系统可观测性
type PlatformOrderMonitoringService struct {
	orderRepository        repository.OrderRepository
	platformOrderGenerator *PlatformOrderGenerator
	logger                 *zap.Logger

	// 监控指标
	metrics *MonitoringMetrics

	// 监控配置
	config *MonitoringConfig

	// 内部状态
	mu      sync.RWMutex
	started bool
}

// MonitoringMetrics 平台订单号监控指标
type MonitoringMetrics struct {
	// 订单号生成指标
	GenerationTotal    int64 `json:"generation_total"`    // 总生成数量
	GenerationSuccess  int64 `json:"generation_success"`  // 成功生成数量
	GenerationFailure  int64 `json:"generation_failure"`  // 失败生成数量
	GenerationDuration int64 `json:"generation_duration"` // 平均生成耗时(ms)

	// 订单查询指标
	QueryTotal        int64 `json:"query_total"`          // 总查询数量
	QueryByPlatformNo int64 `json:"query_by_platform_no"` // 平台订单号查询数量
	QueryByCustomerNo int64 `json:"query_by_customer_no"` // 客户订单号查询数量
	QueryByProviderNo int64 `json:"query_by_provider_no"` // 供应商订单号查询数量
	QueryByTrackingNo int64 `json:"query_by_tracking_no"` // 运单号查询数量
	QuerySuccess      int64 `json:"query_success"`        // 查询成功数量
	QueryFailure      int64 `json:"query_failure"`        // 查询失败数量
	QueryDuration     int64 `json:"query_duration"`       // 平均查询耗时(ms)

	// 数据迁移指标
	MigrationTotal    int64   `json:"migration_total"`    // 总迁移数量
	MigrationSuccess  int64   `json:"migration_success"`  // 成功迁移数量
	MigrationFailure  int64   `json:"migration_failure"`  // 失败迁移数量
	MigrationProgress float64 `json:"migration_progress"` // 迁移进度(%)

	// 系统健康指标
	HealthScore      float64 `json:"health_score"`      // 系统健康分数(0-100)
	ErrorRate        float64 `json:"error_rate"`        // 错误率(%)
	AvailabilityRate float64 `json:"availability_rate"` // 可用性(%)

	// 时间戳
	LastUpdated time.Time `json:"last_updated"` // 最后更新时间
}

// MonitoringConfig 监控配置
type MonitoringConfig struct {
	// 指标收集配置
	MetricsInterval     time.Duration `json:"metrics_interval"`      // 指标收集间隔
	HealthCheckInterval time.Duration `json:"health_check_interval"` // 健康检查间隔

	// 日志配置
	LogLevel            string `json:"log_level"`             // 日志级别
	LogFormat           string `json:"log_format"`            // 日志格式
	EnableStructuredLog bool   `json:"enable_structured_log"` // 启用结构化日志

	// 告警配置
	ErrorRateThreshold    float64 `json:"error_rate_threshold"`    // 错误率告警阈值
	ResponseTimeThreshold int64   `json:"response_time_threshold"` // 响应时间告警阈值(ms)

	// 存储配置
	MetricsRetention time.Duration `json:"metrics_retention"` // 指标保留时间
	LogRetention     time.Duration `json:"log_retention"`     // 日志保留时间
}

// NewPlatformOrderMonitoringService 创建平台订单号监控服务
func NewPlatformOrderMonitoringService(
	orderRepository repository.OrderRepository,
	platformOrderGenerator *PlatformOrderGenerator,
	logger *zap.Logger,
) *PlatformOrderMonitoringService {
	return &PlatformOrderMonitoringService{
		orderRepository:        orderRepository,
		platformOrderGenerator: platformOrderGenerator,
		logger:                 logger,
		metrics:                &MonitoringMetrics{},
		config:                 DefaultMonitoringConfig(),
		started:                false,
	}
}

// DefaultMonitoringConfig 默认监控配置
func DefaultMonitoringConfig() *MonitoringConfig {
	return &MonitoringConfig{
		MetricsInterval:       1 * time.Minute,
		HealthCheckInterval:   30 * time.Second,
		LogLevel:              "info",
		LogFormat:             "json",
		EnableStructuredLog:   true,
		ErrorRateThreshold:    5.0,  // 5%
		ResponseTimeThreshold: 1000, // 1秒
		MetricsRetention:      24 * time.Hour,
		LogRetention:          7 * 24 * time.Hour,
	}
}

// Start 启动监控服务
func (s *PlatformOrderMonitoringService) Start(ctx context.Context) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.started {
		return fmt.Errorf("监控服务已经启动")
	}

	s.logger.Info("🚀 启动平台订单号监控服务",
		zap.Duration("metrics_interval", s.config.MetricsInterval),
		zap.Duration("health_check_interval", s.config.HealthCheckInterval))

	// 启动指标收集
	go s.startMetricsCollection(ctx)

	// 启动健康检查
	go s.startHealthCheck(ctx)

	s.started = true

	s.logger.Info("✅ 平台订单号监控服务启动成功")
	return nil
}

// Stop 停止监控服务
func (s *PlatformOrderMonitoringService) Stop() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if !s.started {
		return fmt.Errorf("监控服务未启动")
	}

	s.started = false
	s.logger.Info("🛑 平台订单号监控服务已停止")
	return nil
}

// RecordOrderGeneration 记录订单号生成指标
func (s *PlatformOrderMonitoringService) RecordOrderGeneration(success bool, duration time.Duration) {
	s.mu.Lock()
	defer s.mu.Unlock()

	s.metrics.GenerationTotal++
	if success {
		s.metrics.GenerationSuccess++
	} else {
		s.metrics.GenerationFailure++
	}

	// 更新平均耗时（加权平均）
	if s.metrics.GenerationTotal == 1 {
		s.metrics.GenerationDuration = duration.Milliseconds()
	} else {
		s.metrics.GenerationDuration = (s.metrics.GenerationDuration*(s.metrics.GenerationTotal-1) + duration.Milliseconds()) / s.metrics.GenerationTotal
	}
	s.metrics.LastUpdated = time.Now()

	// 结构化日志记录
	s.logger.Info("📊 订单号生成指标",
		zap.Bool("success", success),
		zap.Duration("duration", duration),
		zap.Int64("total", s.metrics.GenerationTotal),
		zap.Int64("success_count", s.metrics.GenerationSuccess),
		zap.Int64("failure_count", s.metrics.GenerationFailure))
}

// RecordOrderQuery 记录订单查询指标
func (s *PlatformOrderMonitoringService) RecordOrderQuery(queryType string, success bool, duration time.Duration) {
	s.mu.Lock()
	defer s.mu.Unlock()

	s.metrics.QueryTotal++

	// 按查询类型统计
	switch queryType {
	case "platform_order_no":
		s.metrics.QueryByPlatformNo++
	case "customer_order_no":
		s.metrics.QueryByCustomerNo++
	case "provider_order_no":
		s.metrics.QueryByProviderNo++
	case "tracking_no":
		s.metrics.QueryByTrackingNo++
	}

	if success {
		s.metrics.QuerySuccess++
	} else {
		s.metrics.QueryFailure++
	}

	// 更新平均耗时（加权平均）
	if s.metrics.QueryTotal == 1 {
		s.metrics.QueryDuration = duration.Milliseconds()
	} else {
		s.metrics.QueryDuration = (s.metrics.QueryDuration*(s.metrics.QueryTotal-1) + duration.Milliseconds()) / s.metrics.QueryTotal
	}
	s.metrics.LastUpdated = time.Now()

	// 结构化日志记录
	s.logger.Info("🔍 订单查询指标",
		zap.String("query_type", queryType),
		zap.Bool("success", success),
		zap.Duration("duration", duration),
		zap.Int64("total", s.metrics.QueryTotal),
		zap.Int64("success_count", s.metrics.QuerySuccess),
		zap.Int64("failure_count", s.metrics.QueryFailure))
}

// RecordMigrationProgress 记录迁移进度
func (s *PlatformOrderMonitoringService) RecordMigrationProgress(total, success, failure int64) {
	s.mu.Lock()
	defer s.mu.Unlock()

	s.metrics.MigrationTotal = total
	s.metrics.MigrationSuccess = success
	s.metrics.MigrationFailure = failure

	if total > 0 {
		s.metrics.MigrationProgress = float64(success+failure) / float64(total) * 100
	}

	s.metrics.LastUpdated = time.Now()

	// 结构化日志记录
	s.logger.Info("📈 迁移进度指标",
		zap.Int64("total", total),
		zap.Int64("success", success),
		zap.Int64("failure", failure),
		zap.Float64("progress", s.metrics.MigrationProgress))
}

// GetMetrics 获取监控指标
func (s *PlatformOrderMonitoringService) GetMetrics() *MonitoringMetrics {
	s.mu.RLock()
	defer s.mu.RUnlock()

	// 计算实时指标
	s.calculateHealthMetrics()

	// 返回指标副本
	metrics := *s.metrics
	return &metrics
}

// calculateHealthMetrics 计算健康指标
func (s *PlatformOrderMonitoringService) calculateHealthMetrics() {
	// 计算错误率
	if s.metrics.GenerationTotal > 0 {
		s.metrics.ErrorRate = float64(s.metrics.GenerationFailure+s.metrics.QueryFailure) /
			float64(s.metrics.GenerationTotal+s.metrics.QueryTotal) * 100
	}

	// 计算可用性
	if s.metrics.QueryTotal > 0 {
		s.metrics.AvailabilityRate = float64(s.metrics.QuerySuccess) / float64(s.metrics.QueryTotal) * 100
	}

	// 计算健康分数 (综合指标)
	availabilityScore := s.metrics.AvailabilityRate
	errorScore := 100 - s.metrics.ErrorRate
	performanceScore := 100.0
	if s.metrics.QueryDuration > s.config.ResponseTimeThreshold {
		performanceScore = 50.0
	}

	s.metrics.HealthScore = (availabilityScore + errorScore + performanceScore) / 3
}

// startMetricsCollection 启动指标收集
func (s *PlatformOrderMonitoringService) startMetricsCollection(ctx context.Context) {
	ticker := time.NewTicker(s.config.MetricsInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			s.logger.Info("📊 指标收集服务停止")
			return
		case <-ticker.C:
			s.collectMetrics(ctx)
		}
	}
}

// startHealthCheck 启动健康检查
func (s *PlatformOrderMonitoringService) startHealthCheck(ctx context.Context) {
	ticker := time.NewTicker(s.config.HealthCheckInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			s.logger.Info("🏥 健康检查服务停止")
			return
		case <-ticker.C:
			s.performHealthCheck(ctx)
		}
	}
}

// collectMetrics 收集指标
func (s *PlatformOrderMonitoringService) collectMetrics(ctx context.Context) {
	s.logger.Debug("📊 收集监控指标")

	// 这里可以添加更多的指标收集逻辑
	// 例如：数据库连接数、内存使用率、CPU使用率等

	metrics := s.GetMetrics()

	// 记录关键指标
	s.logger.Info("📈 监控指标汇总",
		zap.Int64("generation_total", metrics.GenerationTotal),
		zap.Int64("query_total", metrics.QueryTotal),
		zap.Float64("error_rate", metrics.ErrorRate),
		zap.Float64("availability_rate", metrics.AvailabilityRate),
		zap.Float64("health_score", metrics.HealthScore))

	// 检查告警条件
	s.checkAlerts(metrics)
}

// performHealthCheck 执行健康检查
func (s *PlatformOrderMonitoringService) performHealthCheck(ctx context.Context) {
	s.logger.Debug("🏥 执行健康检查")

	// 检查平台订单号生成器健康状态
	if s.platformOrderGenerator != nil {
		start := time.Now()
		_, err := s.platformOrderGenerator.GeneratePlatformOrderNo(ctx)
		duration := time.Since(start)

		if err != nil {
			s.logger.Warn("⚠️ 平台订单号生成器健康检查失败",
				zap.Error(err),
				zap.Duration("duration", duration))
		} else {
			s.logger.Debug("✅ 平台订单号生成器健康检查通过",
				zap.Duration("duration", duration))
		}
	}

	// 检查数据库连接健康状态
	// 这里可以添加数据库健康检查逻辑
}

// checkAlerts 检查告警条件
func (s *PlatformOrderMonitoringService) checkAlerts(metrics *MonitoringMetrics) {
	// 检查错误率告警
	if metrics.ErrorRate > s.config.ErrorRateThreshold {
		s.logger.Warn("🚨 错误率告警",
			zap.Float64("current_error_rate", metrics.ErrorRate),
			zap.Float64("threshold", s.config.ErrorRateThreshold))
	}

	// 检查响应时间告警
	if metrics.QueryDuration > s.config.ResponseTimeThreshold {
		s.logger.Warn("🚨 响应时间告警",
			zap.Int64("current_duration", metrics.QueryDuration),
			zap.Int64("threshold", s.config.ResponseTimeThreshold))
	}

	// 检查健康分数告警
	if metrics.HealthScore < 80.0 {
		s.logger.Warn("🚨 系统健康分数告警",
			zap.Float64("health_score", metrics.HealthScore))
	}
}

// LogOrderOperation 记录订单操作日志
func (s *PlatformOrderMonitoringService) LogOrderOperation(operation string, platformOrderNo string, details map[string]interface{}) {
	logFields := []zap.Field{
		zap.String("operation", operation),
		zap.String("platform_order_no", platformOrderNo),
		zap.Time("timestamp", time.Now()),
	}

	// 添加详细信息
	for key, value := range details {
		logFields = append(logFields, zap.Any(key, value))
	}

	s.logger.Info("📝 订单操作日志", logFields...)
}

// LogSystemEvent 记录系统事件日志
func (s *PlatformOrderMonitoringService) LogSystemEvent(event string, level string, details map[string]interface{}) {
	logFields := []zap.Field{
		zap.String("event", event),
		zap.String("level", level),
		zap.Time("timestamp", time.Now()),
	}

	// 添加详细信息
	for key, value := range details {
		logFields = append(logFields, zap.Any(key, value))
	}

	switch level {
	case "error":
		s.logger.Error("🔥 系统事件", logFields...)
	case "warn":
		s.logger.Warn("⚠️ 系统事件", logFields...)
	case "info":
		s.logger.Info("ℹ️ 系统事件", logFields...)
	default:
		s.logger.Debug("🔍 系统事件", logFields...)
	}
}
