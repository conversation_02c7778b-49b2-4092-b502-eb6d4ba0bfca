package logging

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
)

// LogConfig 日志配置
type LogConfig struct {
	Level           string `yaml:"level"`
	Format          string `yaml:"format"`
	Output          string `yaml:"output"`
	FilePath        string `yaml:"file_path"`
	ErrorFilePath   string `yaml:"error_file_path"`
	AccessFilePath  string `yaml:"access_file_path"`
	AuditFilePath   string `yaml:"audit_file_path"`
	MaxSizeMB       int    `yaml:"max_size_mb"`
	MaxBackups      int    `yaml:"max_backups"`
	MaxAgeDays      int    `yaml:"max_age_days"`
	Compress        bool   `yaml:"compress"`
	SamplingEnabled bool   `yaml:"sampling.enabled"`
	SamplingInitial int    `yaml:"sampling.initial"`
	SamplingAfter   int    `yaml:"sampling.thereafter"`
}

// LogManager 企业级日志管理器
type LogManager struct {
	config       LogConfig
	appLogger    *zap.Logger
	errorLogger  *zap.Logger
	accessLogger *zap.Logger
	auditLogger  *zap.Logger
}

// NewLogManager 创建日志管理器
func NewLogManager(config LogConfig) (*LogManager, error) {
	manager := &LogManager{
		config: config,
	}

	// 创建日志目录
	if err := manager.createLogDirectories(); err != nil {
		return nil, fmt.Errorf("创建日志目录失败: %w", err)
	}

	// 初始化各类日志器
	if err := manager.initLoggers(); err != nil {
		return nil, fmt.Errorf("初始化日志器失败: %w", err)
	}

	return manager, nil
}

// createLogDirectories 创建日志目录
func (m *LogManager) createLogDirectories() error {
	dirs := []string{
		filepath.Dir(m.config.FilePath),
		filepath.Dir(m.config.ErrorFilePath),
		filepath.Dir(m.config.AccessFilePath),
		filepath.Dir(m.config.AuditFilePath),
	}

	for _, dir := range dirs {
		if dir != "." && dir != "" {
			if err := os.MkdirAll(dir, 0755); err != nil {
				return fmt.Errorf("创建目录 %s 失败: %w", dir, err)
			}
		}
	}

	return nil
}

// initLoggers 初始化所有日志器
func (m *LogManager) initLoggers() error {
	var err error

	// 应用日志器
	m.appLogger, err = m.createLogger(m.config.FilePath, zapcore.InfoLevel, true)
	if err != nil {
		return fmt.Errorf("创建应用日志器失败: %w", err)
	}

	// 错误日志器
	m.errorLogger, err = m.createLogger(m.config.ErrorFilePath, zapcore.ErrorLevel, false)
	if err != nil {
		return fmt.Errorf("创建错误日志器失败: %w", err)
	}

	// 访问日志器
	m.accessLogger, err = m.createLogger(m.config.AccessFilePath, zapcore.InfoLevel, false)
	if err != nil {
		return fmt.Errorf("创建访问日志器失败: %w", err)
	}

	// 审计日志器
	m.auditLogger, err = m.createLogger(m.config.AuditFilePath, zapcore.InfoLevel, false)
	if err != nil {
		return fmt.Errorf("创建审计日志器失败: %w", err)
	}

	return nil
}

// createLogger 创建单个日志器
func (m *LogManager) createLogger(filePath string, level zapcore.Level, enableSampling bool) (*zap.Logger, error) {
	// 解析日志级别
	logLevel := m.parseLogLevel()

	// 创建编码器配置
	encoderConfig := m.createEncoderConfig()

	// 创建核心组件
	cores := []zapcore.Core{}

	// 文件输出核心
	if m.shouldOutputToFile() {
		fileCore, err := m.createFileCore(filePath, logLevel, encoderConfig)
		if err != nil {
			return nil, err
		}
		cores = append(cores, fileCore)
	}

	// 控制台输出核心
	if m.shouldOutputToConsole() {
		consoleCore := m.createConsoleCore(logLevel, encoderConfig)
		cores = append(cores, consoleCore)
	}

	// 组合核心
	core := zapcore.NewTee(cores...)

	// 应用采样（仅对应用日志器）
	if enableSampling && m.config.SamplingEnabled {
		core = zapcore.NewSamplerWithOptions(
			core,
			time.Second,
			m.config.SamplingInitial,
			m.config.SamplingAfter,
		)
	}

	// 创建日志器
	logger := zap.New(core, zap.AddCaller(), zap.AddStacktrace(zapcore.ErrorLevel))

	return logger, nil
}

// createEncoderConfig 创建编码器配置
func (m *LogManager) createEncoderConfig() zapcore.EncoderConfig {
	config := zap.NewProductionEncoderConfig()

	// 时间格式配置
	config.TimeKey = "timestamp"
	config.EncodeTime = func(t time.Time, enc zapcore.PrimitiveArrayEncoder) {
		// 使用北京时间
		beijingTime := t.In(time.Local)
		enc.AppendString(beijingTime.Format("2006-01-02 15:04:05"))
	}

	// 级别格式配置
	config.LevelKey = "level"
	config.EncodeLevel = zapcore.CapitalLevelEncoder

	// 调用者信息配置
	config.CallerKey = "caller"
	config.EncodeCaller = zapcore.ShortCallerEncoder

	// 消息键配置
	config.MessageKey = "msg"

	// 堆栈跟踪键配置
	config.StacktraceKey = "stacktrace"

	return config
}

// createFileCore 创建文件输出核心
func (m *LogManager) createFileCore(filePath string, level zapcore.Level, encoderConfig zapcore.EncoderConfig) (zapcore.Core, error) {
	// 创建日志轮转器
	writer := &lumberjack.Logger{
		Filename:   filePath,
		MaxSize:    m.config.MaxSizeMB,
		MaxBackups: m.config.MaxBackups,
		MaxAge:     m.config.MaxAgeDays,
		Compress:   m.config.Compress,
		LocalTime:  true, // 使用本地时间
	}

	// 创建编码器
	var encoder zapcore.Encoder
	if m.config.Format == "json" {
		encoder = zapcore.NewJSONEncoder(encoderConfig)
	} else {
		encoder = zapcore.NewConsoleEncoder(encoderConfig)
	}

	return zapcore.NewCore(encoder, zapcore.AddSync(writer), level), nil
}

// createConsoleCore 创建控制台输出核心
func (m *LogManager) createConsoleCore(level zapcore.Level, encoderConfig zapcore.EncoderConfig) zapcore.Core {
	// 控制台使用彩色编码器
	consoleConfig := encoderConfig
	consoleConfig.EncodeLevel = zapcore.CapitalColorLevelEncoder

	encoder := zapcore.NewConsoleEncoder(consoleConfig)
	return zapcore.NewCore(encoder, zapcore.AddSync(os.Stdout), level)
}

// parseLogLevel 解析日志级别
func (m *LogManager) parseLogLevel() zapcore.Level {
	// 优先使用环境变量
	if envLevel := os.Getenv("LOG_LEVEL"); envLevel != "" {
		switch strings.ToUpper(envLevel) {
		case "DEBUG":
			return zapcore.DebugLevel
		case "INFO":
			return zapcore.InfoLevel
		case "WARN":
			return zapcore.WarnLevel
		case "ERROR":
			return zapcore.ErrorLevel
		}
	}

	// 使用配置文件设置
	switch strings.ToLower(m.config.Level) {
	case "debug":
		return zapcore.DebugLevel
	case "info":
		return zapcore.InfoLevel
	case "warn":
		return zapcore.WarnLevel
	case "error":
		return zapcore.ErrorLevel
	default:
		return zapcore.InfoLevel
	}
}

// shouldOutputToFile 是否输出到文件
func (m *LogManager) shouldOutputToFile() bool {
	return m.config.Output == "file" || m.config.Output == "both"
}

// shouldOutputToConsole 是否输出到控制台
func (m *LogManager) shouldOutputToConsole() bool {
	return m.config.Output == "console" || m.config.Output == "both" || m.config.Output == "stdout"
}

// GetAppLogger 获取应用日志器
func (m *LogManager) GetAppLogger() *zap.Logger {
	return m.appLogger
}

// GetErrorLogger 获取错误日志器
func (m *LogManager) GetErrorLogger() *zap.Logger {
	return m.errorLogger
}

// GetAccessLogger 获取访问日志器
func (m *LogManager) GetAccessLogger() *zap.Logger {
	return m.accessLogger
}

// GetAuditLogger 获取审计日志器
func (m *LogManager) GetAuditLogger() *zap.Logger {
	return m.auditLogger
}

// Sync 同步所有日志器
func (m *LogManager) Sync() error {
	var errs []error

	if err := m.appLogger.Sync(); err != nil {
		errs = append(errs, err)
	}
	if err := m.errorLogger.Sync(); err != nil {
		errs = append(errs, err)
	}
	if err := m.accessLogger.Sync(); err != nil {
		errs = append(errs, err)
	}
	if err := m.auditLogger.Sync(); err != nil {
		errs = append(errs, err)
	}

	if len(errs) > 0 {
		return fmt.Errorf("同步日志器失败: %v", errs)
	}

	return nil
}
