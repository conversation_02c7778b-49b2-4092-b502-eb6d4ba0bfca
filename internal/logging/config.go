package logging

import (
	"fmt"

	"github.com/your-org/go-kuaidi/internal/config"
)

// DefaultLogConfig 默认日志配置
func DefaultLogConfig() LogConfig {
	return LogConfig{
		Level:           "info",
		Format:          "json",
		Output:          "both",
		FilePath:        "logs/go-kuaidi.log",
		ErrorFilePath:   "logs/error.log",
		AccessFilePath:  "logs/access.log",
		AuditFilePath:   "logs/audit.log",
		MaxSizeMB:       100,
		MaxBackups:      10,
		MaxAgeDays:      30,
		Compress:        true,
		SamplingEnabled: true,
		SamplingInitial: 100,
		SamplingAfter:   100,
	}
}

// LoadLogConfigFromManager 从配置管理器加载日志配置
func LoadLogConfigFromManager(configManager *config.ConfigManager) LogConfig {
	logConfig := DefaultLogConfig()

	// 从配置管理器读取配置
	if configManager != nil {
		// 基础配置
		if level := configManager.GetString("logging.level"); level != "" {
			logConfig.Level = level
		}
		if format := configManager.GetString("logging.format"); format != "" {
			logConfig.Format = format
		}
		if output := configManager.GetString("logging.output"); output != "" {
			logConfig.Output = output
		}

		// 文件路径配置
		if filePath := configManager.GetString("logging.file_path"); filePath != "" {
			logConfig.FilePath = filePath
		}
		if errorFilePath := configManager.GetString("logging.error_file_path"); errorFilePath != "" {
			logConfig.ErrorFilePath = errorFilePath
		}
		if accessFilePath := configManager.GetString("logging.access_file_path"); accessFilePath != "" {
			logConfig.AccessFilePath = accessFilePath
		}
		if auditFilePath := configManager.GetString("logging.audit_file_path"); auditFilePath != "" {
			logConfig.AuditFilePath = auditFilePath
		}

		// 轮转配置
		if maxSizeMB := configManager.GetInt("logging.max_size_mb"); maxSizeMB > 0 {
			logConfig.MaxSizeMB = maxSizeMB
		}
		if maxBackups := configManager.GetInt("logging.max_backups"); maxBackups > 0 {
			logConfig.MaxBackups = maxBackups
		}
		if maxAgeDays := configManager.GetInt("logging.max_age_days"); maxAgeDays > 0 {
			logConfig.MaxAgeDays = maxAgeDays
		}

		// 压缩配置
		logConfig.Compress = configManager.GetBool("logging.compress")

		// 采样配置
		logConfig.SamplingEnabled = configManager.GetBool("logging.sampling.enabled")
		if samplingInitial := configManager.GetInt("logging.sampling.initial"); samplingInitial > 0 {
			logConfig.SamplingInitial = samplingInitial
		}
		if samplingAfter := configManager.GetInt("logging.sampling.thereafter"); samplingAfter > 0 {
			logConfig.SamplingAfter = samplingAfter
		}
	}

	return logConfig
}

// ValidateLogConfig 验证日志配置
func ValidateLogConfig(config LogConfig) error {
	// 验证日志级别
	validLevels := []string{"debug", "info", "warn", "error"}
	if !contains(validLevels, config.Level) {
		return fmt.Errorf("无效的日志级别: %s, 支持的级别: %v", config.Level, validLevels)
	}

	// 验证日志格式
	validFormats := []string{"json", "console"}
	if !contains(validFormats, config.Format) {
		return fmt.Errorf("无效的日志格式: %s, 支持的格式: %v", config.Format, validFormats)
	}

	// 验证输出方式
	validOutputs := []string{"console", "file", "both", "stdout"}
	if !contains(validOutputs, config.Output) {
		return fmt.Errorf("无效的输出方式: %s, 支持的方式: %v", config.Output, validOutputs)
	}

	// 验证文件路径
	if config.Output == "file" || config.Output == "both" {
		if config.FilePath == "" {
			return fmt.Errorf("文件输出模式下必须指定文件路径")
		}
	}

	// 验证轮转配置
	if config.MaxSizeMB <= 0 {
		return fmt.Errorf("最大文件大小必须大于0")
	}
	if config.MaxBackups < 0 {
		return fmt.Errorf("最大备份数量不能小于0")
	}
	if config.MaxAgeDays < 0 {
		return fmt.Errorf("最大保留天数不能小于0")
	}

	// 验证采样配置
	if config.SamplingEnabled {
		if config.SamplingInitial <= 0 {
			return fmt.Errorf("采样初始值必须大于0")
		}
		if config.SamplingAfter <= 0 {
			return fmt.Errorf("采样后续值必须大于0")
		}
	}

	return nil
}

// contains 检查切片是否包含指定元素
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// PrintLogConfig 打印日志配置（用于调试）
func PrintLogConfig(config LogConfig) {
	fmt.Printf("日志配置:\n")
	fmt.Printf("  级别: %s\n", config.Level)
	fmt.Printf("  格式: %s\n", config.Format)
	fmt.Printf("  输出: %s\n", config.Output)
	fmt.Printf("  应用日志: %s\n", config.FilePath)
	fmt.Printf("  错误日志: %s\n", config.ErrorFilePath)
	fmt.Printf("  访问日志: %s\n", config.AccessFilePath)
	fmt.Printf("  审计日志: %s\n", config.AuditFilePath)
	fmt.Printf("  最大大小: %dMB\n", config.MaxSizeMB)
	fmt.Printf("  最大备份: %d\n", config.MaxBackups)
	fmt.Printf("  保留天数: %d\n", config.MaxAgeDays)
	fmt.Printf("  压缩: %t\n", config.Compress)
	fmt.Printf("  采样: %t\n", config.SamplingEnabled)
	if config.SamplingEnabled {
		fmt.Printf("  采样初始: %d\n", config.SamplingInitial)
		fmt.Printf("  采样后续: %d\n", config.SamplingAfter)
	}
}
