package repository

import (
	"context"
	"database/sql"
	"encoding/base64"
	"fmt"
	"strconv"
	"strings"
	"time"
)

// 🚀 游标分页助手 - 解决深度分页性能问题
// 基于第一性原理：使用唯一ID和时间戳作为游标，避免OFFSET扫描

// CursorPaginationHelper 游标分页助手
type CursorPaginationHelper struct {
	db *sql.DB
}

// NewCursorPaginationHelper 创建游标分页助手
func NewCursorPaginationHelper(db *sql.DB) *CursorPaginationHelper {
	return &CursorPaginationHelper{db: db}
}

// CursorPaginationRequest 游标分页请求
type CursorPaginationRequest struct {
	Limit     int    `json:"limit"`      // 页大小，最大100
	Cursor    string `json:"cursor"`     // 游标（base64编码的"id:timestamp"）
	Direction string `json:"direction"`  // 方向：next/prev
	OrderBy   string `json:"order_by"`   // 排序字段：created_at, updated_at
	OrderDir  string `json:"order_dir"`  // 排序方向：asc, desc
}

// CursorPaginationResponse 游标分页响应
type CursorPaginationResponse struct {
	Items      interface{} `json:"items"`
	HasNext    bool        `json:"has_next"`
	HasPrev    bool        `json:"has_prev"`
	NextCursor string      `json:"next_cursor,omitempty"`
	PrevCursor string      `json:"prev_cursor,omitempty"`
	Count      int         `json:"count"`
}

// CursorInfo 游标信息
type CursorInfo struct {
	ID        int64     `json:"id"`
	Timestamp time.Time `json:"timestamp"`
}

// 🔥 编码游标
func (h *CursorPaginationHelper) EncodeCursor(id int64, timestamp time.Time) string {
	cursor := fmt.Sprintf("%d:%d", id, timestamp.Unix())
	return base64.URLEncoding.EncodeToString([]byte(cursor))
}

// 🔥 解码游标
func (h *CursorPaginationHelper) DecodeCursor(cursor string) (*CursorInfo, error) {
	if cursor == "" {
		return nil, nil
	}

	decoded, err := base64.URLEncoding.DecodeString(cursor)
	if err != nil {
		return nil, fmt.Errorf("游标解码失败: %w", err)
	}

	parts := strings.Split(string(decoded), ":")
	if len(parts) != 2 {
		return nil, fmt.Errorf("游标格式错误")
	}

	id, err := strconv.ParseInt(parts[0], 10, 64)
	if err != nil {
		return nil, fmt.Errorf("游标ID解析失败: %w", err)
	}

	timestamp, err := strconv.ParseInt(parts[1], 10, 64)
	if err != nil {
		return nil, fmt.Errorf("游标时间戳解析失败: %w", err)
	}

	return &CursorInfo{
		ID:        id,
		Timestamp: time.Unix(timestamp, 0),
	}, nil
}

// 🔥 构建游标分页WHERE条件
func (h *CursorPaginationHelper) BuildCursorCondition(
	cursorInfo *CursorInfo, 
	direction, orderBy, orderDir string,
) (string, []interface{}) {
	
	if cursorInfo == nil {
		return "", []interface{}{}
	}

	var condition string
	var args []interface{}

	// 根据排序方向和分页方向确定比较操作符
	var op string
	if direction == "next" {
		if orderDir == "desc" {
			op = "<"
		} else {
			op = ">"
		}
	} else { // prev
		if orderDir == "desc" {
			op = ">"
		} else {
			op = "<"
		}
	}

	// 构建复合条件：先按时间比较，时间相等时按ID比较
	if orderBy == "created_at" || orderBy == "updated_at" {
		condition = fmt.Sprintf(`
			(%s %s $1 OR 
			 (%s = $1 AND id %s $2))
		`, orderBy, op, orderBy, op)
		args = []interface{}{cursorInfo.Timestamp, cursorInfo.ID}
	} else {
		// 默认按ID分页
		condition = fmt.Sprintf("id %s $1", op)
		args = []interface{}{cursorInfo.ID}
	}

	return condition, args
}

// 🔥 优化的交易记录游标分页查询
func (h *CursorPaginationHelper) GetTransactionsByCursor(
	ctx context.Context,
	userID string,
	req *CursorPaginationRequest,
	filters map[string]interface{},
) (*CursorPaginationResponse, error) {

	// 参数验证和默认值
	if req.Limit <= 0 || req.Limit > 100 {
		req.Limit = 20
	}
	if req.OrderBy == "" {
		req.OrderBy = "created_at"
	}
	if req.OrderDir == "" {
		req.OrderDir = "desc"
	}
	if req.Direction == "" {
		req.Direction = "next"
	}

	// 解码游标
	cursorInfo, err := h.DecodeCursor(req.Cursor)
	if err != nil {
		return nil, fmt.Errorf("游标解析失败: %w", err)
	}

	// 构建基础查询
	baseQuery := `
		SELECT 
			bt.id, bt.user_id, bt.transaction_type, bt.amount, bt.currency, 
			bt.balance_before, bt.balance_after, bt.order_no, bt.platform_order_no,
			bt.customer_order_no, bt.tracking_no, bt.transaction_category,
			bt.transaction_sub_type, bt.reference_id, bt.description,
			bt.detail_description, bt.user_friendly_desc, bt.status, bt.created_at
		FROM balance_transactions bt
		WHERE bt.user_id = $1
	`

	args := []interface{}{userID}
	argIndex := 2

	// 添加筛选条件
	for key, value := range filters {
		if value != nil && value != "" {
			switch key {
			case "transaction_type":
				baseQuery += fmt.Sprintf(" AND bt.transaction_type = $%d", argIndex)
				args = append(args, value)
				argIndex++
			case "status":
				baseQuery += fmt.Sprintf(" AND bt.status = $%d", argIndex)
				args = append(args, value)
				argIndex++
			case "start_time":
				baseQuery += fmt.Sprintf(" AND bt.created_at >= $%d", argIndex)
				args = append(args, value)
				argIndex++
			case "end_time":
				baseQuery += fmt.Sprintf(" AND bt.created_at <= $%d", argIndex)
				args = append(args, value)
				argIndex++
			}
		}
	}

	// 添加游标条件
	if cursorCondition, cursorArgs := h.BuildCursorCondition(cursorInfo, req.Direction, req.OrderBy, req.OrderDir); cursorCondition != "" {
		baseQuery += " AND " + cursorCondition
		// 更新参数占位符
		for i := range cursorArgs {
			baseQuery = strings.Replace(baseQuery, fmt.Sprintf("$%d", i+1), fmt.Sprintf("$%d", argIndex+i), -1)
		}
		args = append(args, cursorArgs...)
		argIndex += len(cursorArgs)
	}

	// 添加排序和限制
	baseQuery += fmt.Sprintf(" ORDER BY %s %s, id %s LIMIT $%d", 
		req.OrderBy, strings.ToUpper(req.OrderDir), 
		strings.ToUpper(req.OrderDir), argIndex)
	args = append(args, req.Limit+1) // 多查询一条用于判断是否有下一页

	// 执行查询
	rows, err := h.db.QueryContext(ctx, baseQuery, args...)
	if err != nil {
		return nil, fmt.Errorf("游标分页查询失败: %w", err)
	}
	defer rows.Close()

	// 扫描结果
	var items []map[string]interface{}
	var count int
	
	for rows.Next() && count < req.Limit {
		item := make(map[string]interface{})
		var id int64
		var userID, transactionType, amount, currency string
		var balanceBefore, balanceAfter, orderNo, platformOrderNo string
		var customerOrderNo, trackingNo, category, subType string
		var referenceID, description, detailDescription, userFriendlyDesc string
		var status string
		var createdAt time.Time

		err := rows.Scan(
			&id, &userID, &transactionType, &amount, &currency,
			&balanceBefore, &balanceAfter, &orderNo, &platformOrderNo,
			&customerOrderNo, &trackingNo, &category, &subType,
			&referenceID, &description, &detailDescription, &userFriendlyDesc,
			&status, &createdAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描结果失败: %w", err)
		}

		item["id"] = id
		item["user_id"] = userID
		item["transaction_type"] = transactionType
		item["amount"] = amount
		item["currency"] = currency
		item["balance_before"] = balanceBefore
		item["balance_after"] = balanceAfter
		item["order_no"] = orderNo
		item["platform_order_no"] = platformOrderNo
		item["customer_order_no"] = customerOrderNo
		item["tracking_no"] = trackingNo
		item["transaction_category"] = category
		item["transaction_sub_type"] = subType
		item["reference_id"] = referenceID
		item["description"] = description
		item["detail_description"] = detailDescription
		item["user_friendly_desc"] = userFriendlyDesc
		item["status"] = status
		item["created_at"] = createdAt

		items = append(items, item)
		count++
	}

	// 检查是否还有更多数据
	hasMore := false
	if rows.Next() {
		hasMore = true
	}

	// 构建响应
	response := &CursorPaginationResponse{
		Items:   items,
		Count:   count,
		HasNext: false,
		HasPrev: cursorInfo != nil,
	}

	// 设置游标
	if count > 0 {
		firstItem := items[0]
		lastItem := items[count-1]

		if req.Direction == "next" {
			response.HasNext = hasMore
			if hasMore {
				lastID := lastItem["id"].(int64)
				lastTime := lastItem["created_at"].(time.Time)
				response.NextCursor = h.EncodeCursor(lastID, lastTime)
			}
		} else {
			response.HasPrev = hasMore
			if hasMore {
				firstID := firstItem["id"].(int64)
				firstTime := firstItem["created_at"].(time.Time)
				response.PrevCursor = h.EncodeCursor(firstID, firstTime)
			}
		}
	}

	return response, nil
}

// 🔥 检查是否应该使用游标分页（深度分页优化）
func (h *CursorPaginationHelper) ShouldUseCursorPagination(page int, pageSize int) bool {
	// 当偏移量超过1000时，建议使用游标分页
	offset := (page - 1) * pageSize
	return offset > 1000
}

// 🔥 从传统分页转换为游标分页
func (h *CursorPaginationHelper) ConvertToFirstCursor(
	ctx context.Context,
	tableName string,
	page int,
	pageSize int,
	orderBy string,
	orderDir string,
	whereClause string,
	args []interface{},
) (string, error) {
	
	if page <= 1 {
		return "", nil // 第一页不需要游标
	}

	// 计算目标位置
	offset := (page - 1) * pageSize

	// 查询目标位置的记录
	query := fmt.Sprintf(`
		SELECT id, %s 
		FROM %s 
		%s 
		ORDER BY %s %s, id %s 
		LIMIT 1 OFFSET %d
	`, orderBy, tableName, whereClause, orderBy, orderDir, orderDir, offset)

	var id int64
	var timestamp time.Time
	err := h.db.QueryRowContext(ctx, query, args...).Scan(&id, &timestamp)
	if err != nil {
		if err == sql.ErrNoRows {
			return "", nil // 没有更多数据
		}
		return "", fmt.Errorf("查询游标位置失败: %w", err)
	}

	return h.EncodeCursor(id, timestamp), nil
}