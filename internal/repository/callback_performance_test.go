package repository

import (
	"context"
	"database/sql"
	"testing"
	"time"

	_ "github.com/lib/pq"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestCallbackPerformanceOptimization 测试回调管理系统运单号筛选性能优化
func TestCallbackPerformanceOptimization(t *testing.T) {
	// 连接测试数据库
	db, err := sql.Open("postgres", "*************************************************/go_kuaidi?sslmode=disable")
	require.NoError(t, err)
	defer db.Close()

	// 测试数据库连接
	err = db.Ping()
	require.NoError(t, err)

	repo := NewPostgresCallbackRepository(db)
	ctx := context.Background()

	// 测试用户ID（使用实际存在的用户）
	testUserID := "mywl"

	// 测试参数
	limit := 20
	offset := 0
	filters := map[string]string{
		"tracking_no": "JT", // 使用常见的运单号前缀进行模糊查询
	}

	t.Run("原始UNION ALL查询性能测试", func(t *testing.T) {
		start := time.Now()

		records, err := repo.GetEnhancedForwardRecordsWithJoin(ctx, testUserID, limit, offset, filters)

		duration := time.Since(start)

		require.NoError(t, err)
		t.Logf("原始查询返回记录数: %d", len(records))
		t.Logf("原始查询执行时间: %v", duration)

		// 记录基准性能
		if duration > time.Second {
			t.Logf("⚠️  原始查询性能较差，执行时间超过1秒: %v", duration)
		}
	})

	t.Run("优化后查询性能测试", func(t *testing.T) {
		start := time.Now()

		records, err := repo.GetEnhancedForwardRecordsWithJoinOptimized(ctx, testUserID, limit, offset, filters)

		duration := time.Since(start)

		require.NoError(t, err)
		t.Logf("优化查询返回记录数: %d", len(records))
		t.Logf("优化查询执行时间: %v", duration)

		// 验证性能目标：500毫秒以内
		assert.Less(t, duration, 500*time.Millisecond, "优化后的查询应该在500毫秒以内完成")

		if duration <= 500*time.Millisecond {
			t.Logf("✅ 性能优化成功！查询时间: %v (目标: ≤500ms)", duration)
		} else {
			t.Logf("❌ 性能优化未达到目标，查询时间: %v (目标: ≤500ms)", duration)
		}
	})

	t.Run("游标分页查询性能测试", func(t *testing.T) {
		start := time.Now()

		records, nextCursor, err := repo.GetEnhancedForwardRecordsWithCursor(ctx, testUserID, limit, "", filters)

		duration := time.Since(start)

		require.NoError(t, err)
		t.Logf("游标查询返回记录数: %d", len(records))
		t.Logf("游标查询执行时间: %v", duration)
		t.Logf("下一页游标: %s", nextCursor)

		// 验证性能目标：300毫秒以内（游标分页应该更快）
		assert.Less(t, duration, 300*time.Millisecond, "游标分页查询应该在300毫秒以内完成")

		if duration <= 300*time.Millisecond {
			t.Logf("✅ 游标分页性能优秀！查询时间: %v (目标: ≤300ms)", duration)
		} else {
			t.Logf("❌ 游标分页性能未达到目标，查询时间: %v (目标: ≤300ms)", duration)
		}
	})

	t.Run("精确匹配运单号性能测试", func(t *testing.T) {
		// 使用精确的运单号进行测试（如果数据库中有的话）
		exactFilters := map[string]string{
			"tracking_no": "JT0017672474283", // 使用一个具体的运单号
		}

		start := time.Now()

		records, err := repo.GetEnhancedForwardRecordsWithJoinOptimized(ctx, testUserID, limit, offset, exactFilters)

		duration := time.Since(start)

		require.NoError(t, err)
		t.Logf("精确匹配返回记录数: %d", len(records))
		t.Logf("精确匹配执行时间: %v", duration)

		// 精确匹配应该非常快，目标100毫秒以内
		assert.Less(t, duration, 100*time.Millisecond, "精确匹配查询应该在100毫秒以内完成")

		if duration <= 100*time.Millisecond {
			t.Logf("✅ 精确匹配性能优秀！查询时间: %v (目标: ≤100ms)", duration)
		} else {
			t.Logf("❌ 精确匹配性能未达到目标，查询时间: %v (目标: ≤100ms)", duration)
		}
	})

	t.Run("计数查询性能测试", func(t *testing.T) {
		start := time.Now()

		count, err := repo.GetEnhancedForwardRecordsCountWithFiltersOptimized(ctx, testUserID, filters)

		duration := time.Since(start)

		require.NoError(t, err)
		t.Logf("计数查询结果: %d", count)
		t.Logf("计数查询执行时间: %v", duration)

		// 计数查询应该很快，目标200毫秒以内
		assert.Less(t, duration, 200*time.Millisecond, "计数查询应该在200毫秒以内完成")

		if duration <= 200*time.Millisecond {
			t.Logf("✅ 计数查询性能优秀！查询时间: %v (目标: ≤200ms)", duration)
		} else {
			t.Logf("❌ 计数查询性能未达到目标，查询时间: %v (目标: ≤200ms)", duration)
		}
	})
}

// BenchmarkCallbackQueries 基准测试
func BenchmarkCallbackQueries(b *testing.B) {
	// 连接测试数据库
	db, err := sql.Open("postgres", "*************************************************/go_kuaidi?sslmode=disable")
	require.NoError(b, err)
	defer db.Close()

	repo := NewPostgresCallbackRepository(db)
	ctx := context.Background()
	testUserID := "mywl"
	limit := 20
	offset := 0
	filters := map[string]string{
		"tracking_no": "JT",
	}

	b.Run("原始UNION ALL查询", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, err := repo.GetEnhancedForwardRecordsWithJoin(ctx, testUserID, limit, offset, filters)
			if err != nil {
				b.Fatal(err)
			}
		}
	})

	b.Run("优化后查询", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, err := repo.GetEnhancedForwardRecordsWithJoinOptimized(ctx, testUserID, limit, offset, filters)
			if err != nil {
				b.Fatal(err)
			}
		}
	})

	b.Run("游标分页查询", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, _, err := repo.GetEnhancedForwardRecordsWithCursor(ctx, testUserID, limit, "", filters)
			if err != nil {
				b.Fatal(err)
			}
		}
	})
}

// TestIndexEffectiveness 测试索引有效性
func TestIndexEffectiveness(t *testing.T) {
	// 连接测试数据库
	db, err := sql.Open("postgres", "*************************************************/go_kuaidi?sslmode=disable")
	require.NoError(t, err)
	defer db.Close()

	ctx := context.Background()

	t.Run("检查索引是否被使用", func(t *testing.T) {
		// 检查运单号筛选查询的执行计划
		query := `
			EXPLAIN (ANALYZE, BUFFERS) 
			SELECT cfr.id, cfr.created_at, ucr.tracking_no
			FROM callback_forward_records cfr
			LEFT JOIN unified_callback_records ucr ON cfr.callback_record_id = ucr.id
			WHERE cfr.user_id = $1 AND ucr.tracking_no ILIKE $2
			ORDER BY cfr.created_at DESC
			LIMIT 20
		`

		rows, err := db.QueryContext(ctx, query, "mywl", "%JT%")
		require.NoError(t, err)
		defer rows.Close()

		t.Log("查询执行计划:")
		for rows.Next() {
			var plan string
			err := rows.Scan(&plan)
			require.NoError(t, err)
			t.Log(plan)
		}
	})

	t.Run("检查索引大小和使用情况", func(t *testing.T) {
		query := `
			SELECT
				schemaname,
				tablename,
				indexname,
				pg_size_pretty(pg_relation_size(indexname::regclass)) as index_size
			FROM pg_indexes
			WHERE tablename IN ('unified_callback_records', 'callback_forward_records', 'work_order_forward_records', 'work_orders')
			AND (indexname LIKE '%tracking%' OR indexname LIKE '%user%')
			ORDER BY pg_relation_size(indexname::regclass) DESC
		`

		rows, err := db.QueryContext(ctx, query)
		require.NoError(t, err)
		defer rows.Close()

		t.Log("索引大小统计:")
		for rows.Next() {
			var schema, table, index, size string

			err := rows.Scan(&schema, &table, &index, &size)
			require.NoError(t, err)

			t.Logf("表: %s.%s, 索引: %s, 大小: %s", schema, table, index, size)
		}
	})
}
