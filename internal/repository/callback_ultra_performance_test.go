package repository

import (
	"context"
	"database/sql"
	"testing"
	"time"

	_ "github.com/lib/pq"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestCallbackUltraPerformance 测试超高性能回调查询
func TestCallbackUltraPerformance(t *testing.T) {
	// 连接测试数据库
	db, err := sql.Open("postgres", "*************************************************/go_kuaidi?sslmode=disable")
	require.NoError(t, err)
	defer db.Close()

	// 测试数据库连接
	err = db.Ping()
	require.NoError(t, err)

	repo := NewPostgresCallbackRepository(db)
	ctx := context.Background()

	// 使用实际存在的用户ID
	testUserID := "d7e45ff4-cb3d-470c-9fbc-22114639d096"

	// 测试参数
	limit := 20
	filters := map[string]string{} // 无筛选条件

	t.Run("超高性能查询vs原始UNION ALL查询对比", func(t *testing.T) {
		// 测试原始UNION ALL查询性能
		start1 := time.Now()
		originalRecords, err1 := repo.GetEnhancedForwardRecordsWithJoinOptimized(ctx, testUserID, limit, 0, filters)
		duration1 := time.Since(start1)

		require.NoError(t, err1)
		t.Logf("原始UNION ALL查询: %v, 记录数: %d", duration1, len(originalRecords))

		// 测试超高性能查询
		start2 := time.Now()
		ultraRecords, err2 := repo.GetEnhancedForwardRecordsUltraFast(ctx, testUserID, limit, 0, filters)
		duration2 := time.Since(start2)

		require.NoError(t, err2)
		t.Logf("超高性能查询: %v, 记录数: %d", duration2, len(ultraRecords))

		// 性能验证
		assert.Less(t, duration2, 500*time.Millisecond, "超高性能查询应该在500ms以内")

		// 性能提升验证
		if duration1 > duration2 {
			improvement := float64(duration1-duration2) / float64(duration1) * 100
			t.Logf("✅ 性能提升: %.1f%% (从 %v 到 %v)", improvement, duration1, duration2)
		}

		// 数据一致性验证
		assert.Equal(t, len(originalRecords), len(ultraRecords), "两种查询方法返回的记录数应该相同")
	})

	t.Run("不同分页位置的性能测试", func(t *testing.T) {
		pages := []int{1, 5, 10, 20, 50}

		for _, page := range pages {
			offset := (page - 1) * limit

			start := time.Now()
			records, err := repo.GetEnhancedForwardRecordsUltraFast(ctx, testUserID, limit, offset, filters)
			duration := time.Since(start)

			require.NoError(t, err)
			t.Logf("第%d页查询: %v, 记录数: %d", page, duration, len(records))

			// 性能要求：每页查询应该在500ms以内
			assert.Less(t, duration, 500*time.Millisecond, "第%d页查询应该在500ms以内", page)

			// 深度分页性能要求：即使是第50页也应该在合理时间内
			if page >= 20 {
				assert.Less(t, duration, 1*time.Second, "深度分页查询应该在1秒以内")
			}
		}
	})

	t.Run("并发查询性能测试", func(t *testing.T) {
		concurrency := 5
		results := make(chan time.Duration, concurrency)

		// 启动并发查询
		for i := 0; i < concurrency; i++ {
			go func(offset int) {
				start := time.Now()
				_, err := repo.GetEnhancedForwardRecordsUltraFast(ctx, testUserID, limit, offset*limit, filters)
				duration := time.Since(start)

				if err != nil {
					t.Errorf("并发查询失败: %v", err)
					return
				}

				results <- duration
			}(i)
		}

		// 收集结果
		var totalDuration time.Duration
		var maxDuration time.Duration

		for i := 0; i < concurrency; i++ {
			duration := <-results
			totalDuration += duration
			if duration > maxDuration {
				maxDuration = duration
			}
			t.Logf("并发查询%d: %v", i+1, duration)
		}

		avgDuration := totalDuration / time.Duration(concurrency)
		t.Logf("并发查询平均耗时: %v, 最大耗时: %v", avgDuration, maxDuration)

		// 并发性能要求
		assert.Less(t, avgDuration, 500*time.Millisecond, "并发查询平均耗时应该在500ms以内")
		assert.Less(t, maxDuration, 1*time.Second, "并发查询最大耗时应该在1秒以内")
	})

	t.Run("带筛选条件的性能测试", func(t *testing.T) {
		// 测试运单号筛选
		trackingFilters := map[string]string{
			"tracking_no": "JT",
		}

		start := time.Now()
		records, err := repo.GetEnhancedForwardRecordsUltraFast(ctx, testUserID, limit, 0, trackingFilters)
		duration := time.Since(start)

		require.NoError(t, err)
		t.Logf("运单号筛选查询: %v, 记录数: %d", duration, len(records))

		// 筛选查询性能要求
		assert.Less(t, duration, 500*time.Millisecond, "筛选查询应该在500ms以内")

		// 验证筛选结果
		for _, record := range records {
			if record.TrackingNo != "" {
				assert.Contains(t, record.TrackingNo, "JT", "筛选结果应该包含筛选条件")
			}
		}
	})

	t.Run("内存使用效率测试", func(t *testing.T) {
		// 测试大量数据查询的内存使用
		largeLimit := 1000

		start := time.Now()
		records, err := repo.GetEnhancedForwardRecordsUltraFast(ctx, testUserID, largeLimit, 0, filters)
		duration := time.Since(start)

		require.NoError(t, err)
		t.Logf("大量数据查询: %v, 记录数: %d", duration, len(records))

		// 大量数据查询性能要求
		assert.Less(t, duration, 2*time.Second, "大量数据查询应该在2秒以内")

		// 验证数据完整性
		assert.LessOrEqual(t, len(records), largeLimit, "返回的记录数不应超过限制")
	})

	t.Run("游标分页性能测试", func(t *testing.T) {
		// 测试游标分页的性能
		cursor := ""
		totalRecords := 0

		for page := 1; page <= 10; page++ {
			start := time.Now()
			records, nextCursor, err := repo.GetEnhancedForwardRecordsWithCursorPagination(ctx, testUserID, limit, cursor, filters)
			duration := time.Since(start)

			require.NoError(t, err)
			t.Logf("游标分页第%d页: %v, 记录数: %d, 下一页游标: %s", page, duration, len(records), nextCursor)

			// 游标分页性能要求：每页都应该很快
			assert.Less(t, duration, 200*time.Millisecond, "游标分页第%d页应该在200ms以内", page)

			totalRecords += len(records)
			cursor = nextCursor

			// 如果没有下一页游标，说明数据已经全部获取
			if nextCursor == "" {
				t.Logf("游标分页完成，总共获取 %d 条记录", totalRecords)
				break
			}
		}

		// 验证游标分页获取了足够的数据
		assert.Greater(t, totalRecords, 0, "游标分页应该获取到数据")
	})
}

// BenchmarkCallbackUltraPerformance 基准测试
func BenchmarkCallbackUltraPerformance(b *testing.B) {
	// 连接测试数据库
	db, err := sql.Open("postgres", "*************************************************/go_kuaidi?sslmode=disable")
	require.NoError(b, err)
	defer db.Close()

	repo := NewPostgresCallbackRepository(db)
	ctx := context.Background()
	testUserID := "d7e45ff4-cb3d-470c-9fbc-22114639d096"

	b.Run("原始UNION ALL查询", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, err := repo.GetEnhancedForwardRecordsWithJoinOptimized(ctx, testUserID, 20, 0, map[string]string{})
			if err != nil {
				b.Fatal(err)
			}
		}
	})

	b.Run("超高性能查询", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, err := repo.GetEnhancedForwardRecordsUltraFast(ctx, testUserID, 20, 0, map[string]string{})
			if err != nil {
				b.Fatal(err)
			}
		}
	})

	b.Run("深度分页查询", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			offset := (i % 50) * 20 // 模拟不同的分页位置
			_, err := repo.GetEnhancedForwardRecordsUltraFast(ctx, testUserID, 20, offset, map[string]string{})
			if err != nil {
				b.Fatal(err)
			}
		}
	})
}
