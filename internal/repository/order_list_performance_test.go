package repository

import (
	"context"
	"database/sql"
	"testing"
	"time"

	_ "github.com/lib/pq"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/your-org/go-kuaidi/internal/model"
)

// TestOrderListPerformanceOptimization 测试订单列表筛选功能性能优化
func TestOrderListPerformanceOptimization(t *testing.T) {
	// 连接测试数据库
	db, err := sql.Open("postgres", "*************************************************/go_kuaidi?sslmode=disable")
	require.NoError(t, err)
	defer db.Close()

	// 测试数据库连接
	err = db.Ping()
	require.NoError(t, err)

	repo := NewPostgresOrderRepository(db)
	ctx := context.Background()

	// 测试用户ID（使用实际存在的用户）
	testUserID := "mywl"

	// 基础测试请求
	baseReq := &model.OrderListRequest{
		UserID:   testUserID,
		Page:     1,
		PageSize: 20,
		SortBy:   "created_at",
		SortOrder: "DESC",
	}

	t.Run("原始查询性能测试", func(t *testing.T) {
		start := time.Now()
		
		items, total, err := repo.ListWithFilter(ctx, baseReq)
		
		duration := time.Since(start)
		
		require.NoError(t, err)
		t.Logf("原始查询返回记录数: %d, 总数: %d", len(items), total)
		t.Logf("原始查询执行时间: %v", duration)
		
		// 记录基准性能
		if duration > 500*time.Millisecond {
			t.Logf("⚠️  原始查询性能较差，执行时间: %v", duration)
		}
	})

	t.Run("优化查询性能测试", func(t *testing.T) {
		start := time.Now()
		
		items, total, err := repo.ListWithFilterOptimized(ctx, baseReq)
		
		duration := time.Since(start)
		
		require.NoError(t, err)
		t.Logf("优化查询返回记录数: %d, 总数: %d", len(items), total)
		t.Logf("优化查询执行时间: %v", duration)
		
		// 验证性能目标：300毫秒以内
		assert.Less(t, duration, 300*time.Millisecond, "优化后的查询应该在300毫秒以内完成")
		
		if duration <= 300*time.Millisecond {
			t.Logf("✅ 性能优化成功！查询时间: %v (目标: ≤300ms)", duration)
		} else {
			t.Logf("❌ 性能优化未达到目标，查询时间: %v (目标: ≤300ms)", duration)
		}
	})

	t.Run("游标分页性能测试", func(t *testing.T) {
		start := time.Now()
		
		items, nextCursor, err := repo.ListWithFilterCursorPagination(ctx, baseReq, "")
		
		duration := time.Since(start)
		
		require.NoError(t, err)
		t.Logf("游标分页返回记录数: %d", len(items))
		t.Logf("游标分页执行时间: %v", duration)
		t.Logf("下一页游标: %s", nextCursor)
		
		// 验证性能目标：200毫秒以内（游标分页应该更快）
		assert.Less(t, duration, 200*time.Millisecond, "游标分页查询应该在200毫秒以内完成")
		
		if duration <= 200*time.Millisecond {
			t.Logf("✅ 游标分页性能优秀！查询时间: %v (目标: ≤200ms)", duration)
		} else {
			t.Logf("❌ 游标分页性能未达到目标，查询时间: %v (目标: ≤200ms)", duration)
		}
		
		// 测试第二页
		if nextCursor != "" {
			start2 := time.Now()
			items2, nextCursor2, err2 := repo.ListWithFilterCursorPagination(ctx, baseReq, nextCursor)
			duration2 := time.Since(start2)
			
			require.NoError(t, err2)
			t.Logf("第二页游标分页返回记录数: %d", len(items2))
			t.Logf("第二页游标分页执行时间: %v", duration2)
			t.Logf("第三页游标: %s", nextCursor2)
			
			assert.Less(t, duration2, 200*time.Millisecond, "第二页游标分页查询应该在200毫秒以内完成")
		}
	})

	t.Run("状态筛选性能测试", func(t *testing.T) {
		statusReq := &model.OrderListRequest{
			UserID:    testUserID,
			Status:    "delivered",
			Page:      1,
			PageSize:  20,
			SortBy:    "created_at",
			SortOrder: "DESC",
		}

		start := time.Now()
		items, total, err := repo.ListWithFilterOptimized(ctx, statusReq)
		duration := time.Since(start)
		
		require.NoError(t, err)
		t.Logf("状态筛选返回记录数: %d, 总数: %d", len(items), total)
		t.Logf("状态筛选执行时间: %v", duration)
		
		// 状态筛选应该很快，目标150毫秒以内
		assert.Less(t, duration, 150*time.Millisecond, "状态筛选查询应该在150毫秒以内完成")
	})

	t.Run("运单号精确匹配性能测试", func(t *testing.T) {
		trackingReq := &model.OrderListRequest{
			UserID:     testUserID,
			TrackingNo: "JT0017672474283", // 使用一个具体的运单号
			Page:       1,
			PageSize:   20,
			SortBy:     "created_at",
			SortOrder:  "DESC",
		}

		start := time.Now()
		items, total, err := repo.ListWithFilterOptimized(ctx, trackingReq)
		duration := time.Since(start)
		
		require.NoError(t, err)
		t.Logf("运单号精确匹配返回记录数: %d, 总数: %d", len(items), total)
		t.Logf("运单号精确匹配执行时间: %v", duration)
		
		// 精确匹配应该非常快，目标100毫秒以内
		assert.Less(t, duration, 100*time.Millisecond, "运单号精确匹配查询应该在100毫秒以内完成")
	})

	t.Run("运单号模糊查询性能测试", func(t *testing.T) {
		trackingReq := &model.OrderListRequest{
			UserID:     testUserID,
			TrackingNo: "JT%", // 模糊查询
			Page:       1,
			PageSize:   20,
			SortBy:     "created_at",
			SortOrder:  "DESC",
		}

		start := time.Now()
		items, total, err := repo.ListWithFilterOptimized(ctx, trackingReq)
		duration := time.Since(start)
		
		require.NoError(t, err)
		t.Logf("运单号模糊查询返回记录数: %d, 总数: %d", len(items), total)
		t.Logf("运单号模糊查询执行时间: %v", duration)
		
		// 模糊查询相对较慢，但应该在400毫秒以内
		assert.Less(t, duration, 400*time.Millisecond, "运单号模糊查询应该在400毫秒以内完成")
	})

	t.Run("复合筛选条件性能测试", func(t *testing.T) {
		complexReq := &model.OrderListRequest{
			UserID:      testUserID,
			Status:      "delivered",
			Provider:    "kuaidiniao",
			ExpressType: "JT",
			Page:        1,
			PageSize:    20,
			SortBy:      "created_at",
			SortOrder:   "DESC",
		}

		start := time.Now()
		items, total, err := repo.ListWithFilterOptimized(ctx, complexReq)
		duration := time.Since(start)
		
		require.NoError(t, err)
		t.Logf("复合筛选返回记录数: %d, 总数: %d", len(items), total)
		t.Logf("复合筛选执行时间: %v", duration)
		
		// 复合筛选应该在250毫秒以内
		assert.Less(t, duration, 250*time.Millisecond, "复合筛选查询应该在250毫秒以内完成")
	})

	t.Run("大偏移量分页性能对比", func(t *testing.T) {
		// 测试传统分页在大偏移量时的性能
		largeOffsetReq := &model.OrderListRequest{
			UserID:    testUserID,
			Page:      50, // 第50页，偏移量980
			PageSize:  20,
			SortBy:    "created_at",
			SortOrder: "DESC",
		}

		// 原始查询
		start1 := time.Now()
		items1, total1, err1 := repo.ListWithFilter(ctx, largeOffsetReq)
		duration1 := time.Since(start1)
		
		require.NoError(t, err1)
		t.Logf("原始查询大偏移量返回记录数: %d, 总数: %d", len(items1), total1)
		t.Logf("原始查询大偏移量执行时间: %v", duration1)

		// 优化查询
		start2 := time.Now()
		items2, total2, err2 := repo.ListWithFilterOptimized(ctx, largeOffsetReq)
		duration2 := time.Since(start2)
		
		require.NoError(t, err2)
		t.Logf("优化查询大偏移量返回记录数: %d, 总数: %d", len(items2), total2)
		t.Logf("优化查询大偏移量执行时间: %v", duration2)

		// 优化查询应该比原始查询快
		if duration2 < duration1 {
			improvement := float64(duration1-duration2) / float64(duration1) * 100
			t.Logf("✅ 大偏移量查询性能提升: %.1f%%", improvement)
		}
	})
}

// BenchmarkOrderListQueries 基准测试
func BenchmarkOrderListQueries(b *testing.B) {
	// 连接测试数据库
	db, err := sql.Open("postgres", "*************************************************/go_kuaidi?sslmode=disable")
	require.NoError(b, err)
	defer db.Close()

	repo := NewPostgresOrderRepository(db)
	ctx := context.Background()
	testUserID := "mywl"

	baseReq := &model.OrderListRequest{
		UserID:    testUserID,
		Page:      1,
		PageSize:  20,
		SortBy:    "created_at",
		SortOrder: "DESC",
	}

	b.Run("原始查询", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, _, err := repo.ListWithFilter(ctx, baseReq)
			if err != nil {
				b.Fatal(err)
			}
		}
	})

	b.Run("优化查询", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, _, err := repo.ListWithFilterOptimized(ctx, baseReq)
			if err != nil {
				b.Fatal(err)
			}
		}
	})

	b.Run("游标分页查询", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, _, err := repo.ListWithFilterCursorPagination(ctx, baseReq, "")
			if err != nil {
				b.Fatal(err)
			}
		}
	})
}
