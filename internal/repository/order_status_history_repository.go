package repository

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"

	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/util"
)

// OrderStatusHistoryRepository 订单状态历史仓储接口
type OrderStatusHistoryRepository interface {
	// Create 创建状态历史记录
	Create(ctx context.Context, history *model.OrderStatusHistory) error

	// FindByOrderNo 根据订单号查询状态历史
	FindByOrderNo(ctx context.Context, orderNo string) ([]model.OrderStatusHistory, error)

	// FindByCustomerOrderNo 根据客户订单号查询状态历史
	FindByCustomerOrderNo(ctx context.Context, customerOrderNo string) ([]model.OrderStatusHistory, error)

	// FindByUserID 根据用户ID查询状态历史
	FindByUserID(ctx context.Context, userID string, page, limit int) ([]model.OrderStatusHistory, int64, error)

	// FindByConditions 根据条件查询状态历史
	FindByConditions(ctx context.Context, req *model.GetStatusHistoryRequest) ([]model.OrderStatusHistory, int64, error)

	// GetLatestByOrderNo 获取订单最新的状态记录
	GetLatestByOrderNo(ctx context.Context, orderNo string) (*model.OrderStatusHistory, error)

	// CountByOrderNo 统计订单状态变更次数
	CountByOrderNo(ctx context.Context, orderNo string) (int64, error)
}

// orderStatusHistoryRepository 订单状态历史仓储实现
type orderStatusHistoryRepository struct {
	db *sql.DB
}

// NewOrderStatusHistoryRepository 创建订单状态历史仓储
func NewOrderStatusHistoryRepository(db *sql.DB) OrderStatusHistoryRepository {
	return &orderStatusHistoryRepository{
		db: db,
	}
}

// Create 创建状态历史记录
func (r *orderStatusHistoryRepository) Create(ctx context.Context, history *model.OrderStatusHistory) error {
	query := `
		INSERT INTO order_status_history (
			order_no, from_status, to_status, provider, raw_status,
			change_source, operator_id, operator_name, change_reason,
			user_id, customer_order_no, extra, created_at, updated_at
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
		RETURNING id`

	extraBytes, err := json.Marshal(history.Extra)
	if err != nil {
		extraBytes = []byte("{}")
	}

	now := util.NowBeijing()
	err = r.db.QueryRowContext(ctx, query,
		history.OrderNo,
		history.FromStatus,
		history.ToStatus,
		history.Provider,
		history.RawStatus,
		history.ChangeSource,
		history.OperatorID,
		history.OperatorName,
		history.ChangeReason,
		history.UserID,
		history.CustomerOrderNo,
		extraBytes,
		now,
		now,
	).Scan(&history.ID)

	if err != nil {
		return fmt.Errorf("创建状态历史记录失败: %w", err)
	}

	history.CreatedAt = now
	history.UpdatedAt = now
	return nil
}

// FindByOrderNo 根据订单号查询状态历史
func (r *orderStatusHistoryRepository) FindByOrderNo(ctx context.Context, orderNo string) ([]model.OrderStatusHistory, error) {
	query := `
		SELECT id, order_no, from_status, to_status, provider, raw_status,
			   change_source, operator_id, operator_name, change_reason,
			   user_id, customer_order_no, extra, created_at, updated_at
		FROM order_status_history
		WHERE order_no = $1
		ORDER BY created_at DESC`

	rows, err := r.db.QueryContext(ctx, query, orderNo)
	if err != nil {
		return nil, fmt.Errorf("查询订单状态历史失败: %w", err)
	}
	defer rows.Close()

	return r.scanHistories(rows)
}

// FindByCustomerOrderNo 根据客户订单号查询状态历史
func (r *orderStatusHistoryRepository) FindByCustomerOrderNo(ctx context.Context, customerOrderNo string) ([]model.OrderStatusHistory, error) {
	query := `
		SELECT id, order_no, from_status, to_status, provider, raw_status,
			   change_source, operator_id, operator_name, change_reason,
			   user_id, customer_order_no, extra, created_at, updated_at
		FROM order_status_history
		WHERE customer_order_no = $1
		ORDER BY created_at DESC`

	rows, err := r.db.QueryContext(ctx, query, customerOrderNo)
	if err != nil {
		return nil, fmt.Errorf("查询客户订单状态历史失败: %w", err)
	}
	defer rows.Close()

	return r.scanHistories(rows)
}

// FindByUserID 根据用户ID查询状态历史
func (r *orderStatusHistoryRepository) FindByUserID(ctx context.Context, userID string, page, limit int) ([]model.OrderStatusHistory, int64, error) {
	// 计算偏移量
	offset := (page - 1) * limit

	// 查询总数
	countQuery := `SELECT COUNT(*) FROM order_status_history WHERE user_id = $1`
	var total int64
	err := r.db.QueryRowContext(ctx, countQuery, userID).Scan(&total)
	if err != nil {
		return nil, 0, fmt.Errorf("统计用户状态历史总数失败: %w", err)
	}

	// 查询数据
	query := `
		SELECT id, order_no, from_status, to_status, provider, raw_status,
			   change_source, operator_id, operator_name, change_reason,
			   user_id, customer_order_no, extra, created_at, updated_at
		FROM order_status_history
		WHERE user_id = $1
		ORDER BY created_at DESC
		LIMIT $2 OFFSET $3`

	rows, err := r.db.QueryContext(ctx, query, userID, limit, offset)
	if err != nil {
		return nil, 0, fmt.Errorf("查询用户状态历史失败: %w", err)
	}
	defer rows.Close()

	histories, err := r.scanHistories(rows)
	if err != nil {
		return nil, 0, err
	}

	return histories, total, nil
}

// FindByConditions 根据条件查询状态历史
func (r *orderStatusHistoryRepository) FindByConditions(ctx context.Context, req *model.GetStatusHistoryRequest) ([]model.OrderStatusHistory, int64, error) {
	// 构建查询条件
	var conditions []string
	var args []interface{}
	argIndex := 1

	if req.OrderNo != "" {
		conditions = append(conditions, fmt.Sprintf("order_no = $%d", argIndex))
		args = append(args, req.OrderNo)
		argIndex++
	}
	if req.CustomerOrderNo != "" {
		conditions = append(conditions, fmt.Sprintf("customer_order_no = $%d", argIndex))
		args = append(args, req.CustomerOrderNo)
		argIndex++
	}
	if req.UserID != "" {
		conditions = append(conditions, fmt.Sprintf("user_id = $%d", argIndex))
		args = append(args, req.UserID)
		argIndex++
	}

	whereClause := ""
	if len(conditions) > 0 {
		whereClause = "WHERE " + conditions[0]
		for i := 1; i < len(conditions); i++ {
			whereClause += " AND " + conditions[i]
		}
	}

	// 查询总数
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM order_status_history %s", whereClause)
	var total int64
	err := r.db.QueryRowContext(ctx, countQuery, args...).Scan(&total)
	if err != nil {
		return nil, 0, fmt.Errorf("统计状态历史总数失败: %w", err)
	}

	// 计算偏移量
	offset := (req.Page - 1) * req.Limit

	// 查询数据
	query := fmt.Sprintf(`
		SELECT id, order_no, from_status, to_status, provider, raw_status,
			   change_source, operator_id, operator_name, change_reason,
			   user_id, customer_order_no, extra, created_at, updated_at
		FROM order_status_history
		%s
		ORDER BY created_at DESC
		LIMIT $%d OFFSET $%d`, whereClause, argIndex, argIndex+1)

	args = append(args, req.Limit, offset)
	rows, err := r.db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("查询状态历史失败: %w", err)
	}
	defer rows.Close()

	histories, err := r.scanHistories(rows)
	if err != nil {
		return nil, 0, err
	}

	return histories, total, nil
}

// GetLatestByOrderNo 获取订单最新的状态记录
func (r *orderStatusHistoryRepository) GetLatestByOrderNo(ctx context.Context, orderNo string) (*model.OrderStatusHistory, error) {
	query := `
		SELECT id, order_no, from_status, to_status, provider, raw_status,
			   change_source, operator_id, operator_name, change_reason,
			   user_id, customer_order_no, extra, created_at, updated_at
		FROM order_status_history
		WHERE order_no = $1
		ORDER BY created_at DESC
		LIMIT 1`

	row := r.db.QueryRowContext(ctx, query, orderNo)
	history, err := r.scanHistory(row)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, fmt.Errorf("查询最新状态记录失败: %w", err)
	}
	return history, nil
}

// CountByOrderNo 统计订单状态变更次数
func (r *orderStatusHistoryRepository) CountByOrderNo(ctx context.Context, orderNo string) (int64, error) {
	query := `SELECT COUNT(*) FROM order_status_history WHERE order_no = $1`
	var count int64
	err := r.db.QueryRowContext(ctx, query, orderNo).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("统计订单状态变更次数失败: %w", err)
	}
	return count, nil
}

// scanHistories 扫描多个历史记录
func (r *orderStatusHistoryRepository) scanHistories(rows *sql.Rows) ([]model.OrderStatusHistory, error) {
	var histories []model.OrderStatusHistory
	for rows.Next() {
		history, err := r.scanHistoryFromRows(rows)
		if err != nil {
			return nil, err
		}
		histories = append(histories, *history)
	}
	return histories, rows.Err()
}

// scanHistory 扫描单个历史记录
func (r *orderStatusHistoryRepository) scanHistory(row *sql.Row) (*model.OrderStatusHistory, error) {
	var history model.OrderStatusHistory
	var extraBytes []byte

	err := row.Scan(
		&history.ID,
		&history.OrderNo,
		&history.FromStatus,
		&history.ToStatus,
		&history.Provider,
		&history.RawStatus,
		&history.ChangeSource,
		&history.OperatorID,
		&history.OperatorName,
		&history.ChangeReason,
		&history.UserID,
		&history.CustomerOrderNo,
		&extraBytes,
		&history.CreatedAt,
		&history.UpdatedAt,
	)

	if err != nil {
		return nil, err
	}

	// 解析Extra字段
	if len(extraBytes) > 0 {
		if err := json.Unmarshal(extraBytes, &history.Extra); err != nil {
			history.Extra = make(map[string]interface{})
		}
	} else {
		history.Extra = make(map[string]interface{})
	}

	return &history, nil
}

// scanHistoryFromRows 从Rows扫描历史记录
func (r *orderStatusHistoryRepository) scanHistoryFromRows(rows *sql.Rows) (*model.OrderStatusHistory, error) {
	var history model.OrderStatusHistory
	var extraBytes []byte

	err := rows.Scan(
		&history.ID,
		&history.OrderNo,
		&history.FromStatus,
		&history.ToStatus,
		&history.Provider,
		&history.RawStatus,
		&history.ChangeSource,
		&history.OperatorID,
		&history.OperatorName,
		&history.ChangeReason,
		&history.UserID,
		&history.CustomerOrderNo,
		&extraBytes,
		&history.CreatedAt,
		&history.UpdatedAt,
	)

	if err != nil {
		return nil, err
	}

	// 解析Extra字段
	if len(extraBytes) > 0 {
		if err := json.Unmarshal(extraBytes, &history.Extra); err != nil {
			history.Extra = make(map[string]interface{})
		}
	} else {
		history.Extra = make(map[string]interface{})
	}

	return &history, nil
}
