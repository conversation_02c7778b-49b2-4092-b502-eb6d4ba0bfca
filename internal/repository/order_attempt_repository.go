package repository

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/google/uuid"
	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/util"

)

// OrderAttemptRepository 下单尝试记录仓库接口
type OrderAttemptRepository interface {
	Create(ctx context.Context, attempt *model.OrderAttempt) error
	GetByCustomerOrderNo(ctx context.Context, customerOrderNo string) ([]*model.OrderAttempt, error)
	GetByUserID(ctx context.Context, userID string, limit, offset int) ([]*model.OrderAttempt, error)
	GetFailedAttempts(ctx context.Context, filter *model.OrderAttemptFilter) ([]*model.OrderAttempt, error)
	GetSummary(ctx context.Context, customerOrderNo string) (*model.OrderAttemptSummary, error)
	DeleteOldRecords(ctx context.Context, beforeTime time.Time) error
}

// PostgresOrderAttemptRepository PostgreSQL下单尝试记录仓库实现
type PostgresOrderAttemptRepository struct {
	db     *sql.DB
	logger *zap.Logger
}

// NewOrderAttemptRepository 创建下单尝试记录仓库
func NewOrderAttemptRepository(db *sql.DB, logger *zap.Logger) OrderAttemptRepository {
	return &PostgresOrderAttemptRepository{
		db:     db,
		logger: logger,
	}
}

// Create 创建下单尝试记录
func (r *PostgresOrderAttemptRepository) Create(ctx context.Context, attempt *model.OrderAttempt) error {
	query := `
		INSERT INTO order_attempts (
			id, customer_order_no, user_id, provider, express_type,
			attempt_stage, stage_description, success, error_message, error_code,
			request_data, response_data, processing_time_ms, retry_count, metadata,
			created_at, updated_at
		) VALUES (
			$1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17
		)`

	_, err := r.db.ExecContext(ctx, query,
		attempt.ID,
		attempt.CustomerOrderNo,
		attempt.UserID,
		attempt.Provider,
		attempt.ExpressType,
		attempt.AttemptStage,
		attempt.StageDescription,
		attempt.Success,
		attempt.ErrorMessage,
		attempt.ErrorCode,
		attempt.RequestData,
		attempt.ResponseData,
		attempt.ProcessingTimeMs,
		attempt.RetryCount,
		attempt.Metadata,
		attempt.CreatedAt,
		attempt.UpdatedAt,
	)

	if err != nil {
		r.logger.Error("创建下单尝试记录失败",
			zap.String("customer_order_no", attempt.CustomerOrderNo),
			zap.String("attempt_stage", attempt.AttemptStage),
			zap.Error(err))
		return fmt.Errorf("创建下单尝试记录失败: %w", err)
	}

	return nil
}

// GetByCustomerOrderNo 根据客户订单号获取下单尝试记录
func (r *PostgresOrderAttemptRepository) GetByCustomerOrderNo(ctx context.Context, customerOrderNo string) ([]*model.OrderAttempt, error) {
	query := `
		SELECT id, customer_order_no, user_id, provider, express_type,
			   attempt_stage, stage_description, success, error_message, error_code,
			   request_data, response_data, processing_time_ms, retry_count, metadata,
			   created_at, updated_at
		FROM order_attempts 
		WHERE customer_order_no = $1 
		ORDER BY created_at ASC`

	rows, err := r.db.QueryContext(ctx, query, customerOrderNo)
	if err != nil {
		return nil, fmt.Errorf("查询下单尝试记录失败: %w", err)
	}
	defer rows.Close()

	var attempts []*model.OrderAttempt
	for rows.Next() {
		attempt := &model.OrderAttempt{}
		err := rows.Scan(
			&attempt.ID,
			&attempt.CustomerOrderNo,
			&attempt.UserID,
			&attempt.Provider,
			&attempt.ExpressType,
			&attempt.AttemptStage,
			&attempt.StageDescription,
			&attempt.Success,
			&attempt.ErrorMessage,
			&attempt.ErrorCode,
			&attempt.RequestData,
			&attempt.ResponseData,
			&attempt.ProcessingTimeMs,
			&attempt.RetryCount,
			&attempt.Metadata,
			&attempt.CreatedAt,
			&attempt.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描下单尝试记录失败: %w", err)
		}
		attempts = append(attempts, attempt)
	}

	return attempts, nil
}

// GetByUserID 根据用户ID获取下单尝试记录
func (r *PostgresOrderAttemptRepository) GetByUserID(ctx context.Context, userID string, limit, offset int) ([]*model.OrderAttempt, error) {
	query := `
		SELECT id, customer_order_no, user_id, provider, express_type,
			   attempt_stage, stage_description, success, error_message, error_code,
			   request_data, response_data, processing_time_ms, retry_count, metadata,
			   created_at, updated_at
		FROM order_attempts 
		WHERE user_id = $1 
		ORDER BY created_at DESC
		LIMIT $2 OFFSET $3`

	rows, err := r.db.QueryContext(ctx, query, userID, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("查询用户下单尝试记录失败: %w", err)
	}
	defer rows.Close()

	var attempts []*model.OrderAttempt
	for rows.Next() {
		attempt := &model.OrderAttempt{}
		err := rows.Scan(
			&attempt.ID,
			&attempt.CustomerOrderNo,
			&attempt.UserID,
			&attempt.Provider,
			&attempt.ExpressType,
			&attempt.AttemptStage,
			&attempt.StageDescription,
			&attempt.Success,
			&attempt.ErrorMessage,
			&attempt.ErrorCode,
			&attempt.RequestData,
			&attempt.ResponseData,
			&attempt.ProcessingTimeMs,
			&attempt.RetryCount,
			&attempt.Metadata,
			&attempt.CreatedAt,
			&attempt.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描用户下单尝试记录失败: %w", err)
		}
		attempts = append(attempts, attempt)
	}

	return attempts, nil
}

// GetFailedAttempts 获取失败的下单尝试记录
func (r *PostgresOrderAttemptRepository) GetFailedAttempts(ctx context.Context, filter *model.OrderAttemptFilter) ([]*model.OrderAttempt, error) {
	query := `
		SELECT id, customer_order_no, user_id, provider, express_type,
			   attempt_stage, stage_description, success, error_message, error_code,
			   request_data, response_data, processing_time_ms, retry_count, metadata,
			   created_at, updated_at
		FROM order_attempts 
		WHERE success = false`

	args := []interface{}{}
	argIndex := 1

	// 添加过滤条件
	if filter.CustomerOrderNo != "" {
		query += fmt.Sprintf(" AND customer_order_no = $%d", argIndex)
		args = append(args, filter.CustomerOrderNo)
		argIndex++
	}

	if filter.UserID != "" {
		query += fmt.Sprintf(" AND user_id = $%d", argIndex)
		args = append(args, filter.UserID)
		argIndex++
	}

	if filter.Provider != "" {
		query += fmt.Sprintf(" AND provider = $%d", argIndex)
		args = append(args, filter.Provider)
		argIndex++
	}

	if filter.ExpressType != "" {
		query += fmt.Sprintf(" AND express_type = $%d", argIndex)
		args = append(args, filter.ExpressType)
		argIndex++
	}

	if filter.AttemptStage != "" {
		query += fmt.Sprintf(" AND attempt_stage = $%d", argIndex)
		args = append(args, filter.AttemptStage)
		argIndex++
	}

	if !filter.StartTime.IsZero() {
		query += fmt.Sprintf(" AND created_at >= $%d", argIndex)
		args = append(args, filter.StartTime)
		argIndex++
	}

	if !filter.EndTime.IsZero() {
		query += fmt.Sprintf(" AND created_at <= $%d", argIndex)
		args = append(args, filter.EndTime)
		argIndex++
	}

	query += " ORDER BY created_at DESC"

	if filter.Limit > 0 {
		query += fmt.Sprintf(" LIMIT $%d", argIndex)
		args = append(args, filter.Limit)
		argIndex++
	}

	if filter.Offset > 0 {
		query += fmt.Sprintf(" OFFSET $%d", argIndex)
		args = append(args, filter.Offset)
		argIndex++
	}

	rows, err := r.db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("查询失败的下单尝试记录失败: %w", err)
	}
	defer rows.Close()

	var attempts []*model.OrderAttempt
	for rows.Next() {
		attempt := &model.OrderAttempt{}
		err := rows.Scan(
			&attempt.ID,
			&attempt.CustomerOrderNo,
			&attempt.UserID,
			&attempt.Provider,
			&attempt.ExpressType,
			&attempt.AttemptStage,
			&attempt.StageDescription,
			&attempt.Success,
			&attempt.ErrorMessage,
			&attempt.ErrorCode,
			&attempt.RequestData,
			&attempt.ResponseData,
			&attempt.ProcessingTimeMs,
			&attempt.RetryCount,
			&attempt.Metadata,
			&attempt.CreatedAt,
			&attempt.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描失败的下单尝试记录失败: %w", err)
		}
		attempts = append(attempts, attempt)
	}

	return attempts, nil
}

// GetSummary 获取订单尝试汇总
func (r *PostgresOrderAttemptRepository) GetSummary(ctx context.Context, customerOrderNo string) (*model.OrderAttemptSummary, error) {
	query := `
		SELECT
			COUNT(*) as total_attempts,
			COUNT(CASE WHEN success = true THEN 1 END) as successful_stages,
			COUNT(CASE WHEN success = false THEN 1 END) as failed_stages,
			MAX(created_at) as last_attempt_time,
			MAX(attempt_stage) as last_attempt_stage,
			BOOL_OR(success) as final_success,
			SUM(processing_time_ms) as total_processing_ms
		FROM order_attempts
		WHERE customer_order_no = $1`

	row := r.db.QueryRowContext(ctx, query, customerOrderNo)

	summary := &model.OrderAttemptSummary{
		CustomerOrderNo: customerOrderNo,
	}

	err := row.Scan(
		&summary.TotalAttempts,
		&summary.SuccessfulStages,
		&summary.FailedStages,
		&summary.LastAttemptTime,
		&summary.LastAttemptStage,
		&summary.FinalSuccess,
		&summary.TotalProcessingMs,
	)

	if err != nil {
		return nil, fmt.Errorf("查询订单尝试汇总失败: %w", err)
	}

	// 获取尝试过的供应商列表
	providersQuery := `
		SELECT DISTINCT provider
		FROM order_attempts
		WHERE customer_order_no = $1 AND provider IS NOT NULL AND provider != ''`

	rows, err := r.db.QueryContext(ctx, providersQuery, customerOrderNo)
	if err == nil {
		defer rows.Close()
		var providers []string
		for rows.Next() {
			var provider string
			if err := rows.Scan(&provider); err == nil {
				providers = append(providers, provider)
			}
		}
		summary.ProvidersAttempted = providers
	}

	// 获取错误消息列表
	errorsQuery := `
		SELECT DISTINCT error_message
		FROM order_attempts
		WHERE customer_order_no = $1 AND success = false AND error_message IS NOT NULL AND error_message != ''`

	rows, err = r.db.QueryContext(ctx, errorsQuery, customerOrderNo)
	if err == nil {
		defer rows.Close()
		var errors []string
		for rows.Next() {
			var errorMsg string
			if err := rows.Scan(&errorMsg); err == nil {
				errors = append(errors, errorMsg)
			}
		}
		summary.ErrorMessages = errors
	}

	return summary, nil
}

// DeleteOldRecords 删除旧的记录
func (r *PostgresOrderAttemptRepository) DeleteOldRecords(ctx context.Context, beforeTime time.Time) error {
	query := `DELETE FROM order_attempts WHERE created_at < $1`

	result, err := r.db.ExecContext(ctx, query, beforeTime)
	if err != nil {
		return fmt.Errorf("删除旧的下单尝试记录失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取删除行数失败: %w", err)
	}

	r.logger.Info("删除旧的下单尝试记录成功",
		zap.Time("before_time", beforeTime),
		zap.Int64("rows_affected", rowsAffected))

	return nil
}

// CreateOrderAttemptFromRequest 从请求创建下单尝试记录
func CreateOrderAttemptFromRequest(req *model.OrderAttemptRequest) (*model.OrderAttempt, error) {
	now := util.NowBeijing()

	// 序列化请求数据
	var requestData string
	if req.RequestData != nil {
		if data, err := json.Marshal(req.RequestData); err == nil {
			requestData = string(data)
		}
	}

	// 序列化响应数据
	var responseData string
	if req.ResponseData != nil {
		if data, err := json.Marshal(req.ResponseData); err == nil {
			responseData = string(data)
		}
	}

	// 序列化元数据
	var metadata string
	if req.Metadata != nil {
		if data, err := json.Marshal(req.Metadata); err == nil {
			metadata = string(data)
		}
	}

	attempt := &model.OrderAttempt{
		ID:               uuid.New().String(),
		CustomerOrderNo:  req.CustomerOrderNo,
		UserID:           req.UserID,
		Provider:         req.Provider,
		ExpressType:      req.ExpressType,
		AttemptStage:     req.AttemptStage,
		StageDescription: req.StageDescription,
		Success:          req.Success,
		ErrorMessage:     req.ErrorMessage,
		ErrorCode:        req.ErrorCode,
		RequestData:      requestData,
		ResponseData:     responseData,
		ProcessingTimeMs: req.ProcessingTimeMs,
		RetryCount:       req.RetryCount,
		Metadata:         metadata,
		CreatedAt:        now,
		UpdatedAt:        now,
	}

	return attempt, nil
}
