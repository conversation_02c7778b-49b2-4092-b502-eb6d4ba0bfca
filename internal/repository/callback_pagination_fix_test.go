package repository

import (
	"context"
	"database/sql"
	"testing"
	"time"

	_ "github.com/lib/pq"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestCallbackPaginationFix 测试回调管理分页修复
func TestCallbackPaginationFix(t *testing.T) {
	// 连接测试数据库
	db, err := sql.Open("postgres", "*************************************************/go_kuaidi?sslmode=disable")
	require.NoError(t, err)
	defer db.Close()

	// 测试数据库连接
	err = db.Ping()
	require.NoError(t, err)

	repo := NewPostgresCallbackRepository(db)
	ctx := context.Background()

	// 使用实际存在的用户ID
	testUserID := "d7e45ff4-cb3d-470c-9fbc-22114639d096"

	// 测试参数
	limit := 20
	filters := map[string]string{} // 无筛选条件

	t.Run("测试分页连续性", func(t *testing.T) {
		// 测试前5页的数据
		var allRecords []string // 存储所有记录的ID，用于检查重复

		for page := 1; page <= 5; page++ {
			offset := (page - 1) * limit

			t.Logf("测试第%d页，offset=%d", page, offset)

			// 使用修复后的查询方法
			records, err := repo.GetEnhancedForwardRecordsWithJoinOptimized(ctx, testUserID, limit, offset, filters)
			require.NoError(t, err)

			t.Logf("第%d页返回记录数: %d", page, len(records))

			// 验证记录数量
			if page <= 3 {
				// 前3页应该有数据
				assert.Greater(t, len(records), 0, "第%d页应该有数据", page)
			}

			// 检查记录ID是否重复
			for _, record := range records {
				recordID := record.ID.String()
				for _, existingID := range allRecords {
					assert.NotEqual(t, existingID, recordID, "第%d页出现重复记录ID: %s", page, recordID)
				}
				allRecords = append(allRecords, recordID)
			}

			// 如果这一页没有数据，后续页也不应该有数据
			if len(records) == 0 {
				t.Logf("第%d页无数据，这是正常的", page)
				break
			}
		}

		t.Logf("总共获取到 %d 条不重复记录", len(allRecords))
	})

	t.Run("测试计数查询一致性", func(t *testing.T) {
		// 获取总数
		totalCount, err := repo.GetEnhancedForwardRecordsCountWithFiltersOptimized(ctx, testUserID, filters)
		require.NoError(t, err)

		t.Logf("总记录数: %d", totalCount)

		// 计算应该有多少页
		expectedPages := int(totalCount) / limit
		if int(totalCount)%limit > 0 {
			expectedPages++
		}

		t.Logf("预期页数: %d", expectedPages)

		// 验证最后一页
		if expectedPages > 0 {
			lastPageOffset := (expectedPages - 1) * limit
			lastPageRecords, err := repo.GetEnhancedForwardRecordsWithJoinOptimized(ctx, testUserID, limit, lastPageOffset, filters)
			require.NoError(t, err)

			t.Logf("最后一页(第%d页)记录数: %d", expectedPages, len(lastPageRecords))
			assert.Greater(t, len(lastPageRecords), 0, "最后一页应该有数据")
		}

		// 验证超出范围的页
		if expectedPages > 0 {
			beyondPageOffset := expectedPages * limit
			beyondPageRecords, err := repo.GetEnhancedForwardRecordsWithJoinOptimized(ctx, testUserID, limit, beyondPageOffset, filters)
			require.NoError(t, err)

			t.Logf("超出范围页记录数: %d", len(beyondPageRecords))
			assert.Equal(t, 0, len(beyondPageRecords), "超出范围的页应该没有数据")
		}
	})

	t.Run("测试带筛选条件的分页", func(t *testing.T) {
		// 使用运单号筛选
		trackingFilters := map[string]string{
			"tracking_no": "JT", // 模糊查询
		}

		// 获取筛选后的总数
		filteredCount, err := repo.GetEnhancedForwardRecordsCountWithFiltersOptimized(ctx, testUserID, trackingFilters)
		require.NoError(t, err)

		t.Logf("筛选后总记录数: %d", filteredCount)

		if filteredCount > 0 {
			// 测试筛选后的分页
			for page := 1; page <= 3; page++ {
				offset := (page - 1) * limit

				records, err := repo.GetEnhancedForwardRecordsWithJoinOptimized(ctx, testUserID, limit, offset, trackingFilters)
				require.NoError(t, err)

				t.Logf("筛选后第%d页记录数: %d", page, len(records))

				// 验证筛选条件
				for _, record := range records {
					if record.TrackingNo != "" {
						assert.Contains(t, record.TrackingNo, "JT", "记录应该包含筛选条件")
					}
				}
			}
		}
	})

	t.Run("对比原始查询和优化查询", func(t *testing.T) {
		// 测试第1页
		originalRecords, err := repo.GetEnhancedForwardRecordsWithJoin(ctx, testUserID, limit, 0, filters)
		require.NoError(t, err)

		optimizedRecords, err := repo.GetEnhancedForwardRecordsWithJoinOptimized(ctx, testUserID, limit, 0, filters)
		require.NoError(t, err)

		t.Logf("原始查询第1页记录数: %d", len(originalRecords))
		t.Logf("优化查询第1页记录数: %d", len(optimizedRecords))

		// 记录数应该相同或接近
		assert.InDelta(t, len(originalRecords), len(optimizedRecords), 5, "原始查询和优化查询的记录数应该相近")

		// 测试第5页
		originalRecords5, err := repo.GetEnhancedForwardRecordsWithJoin(ctx, testUserID, limit, 80, filters)
		require.NoError(t, err)

		optimizedRecords5, err := repo.GetEnhancedForwardRecordsWithJoinOptimized(ctx, testUserID, limit, 80, filters)
		require.NoError(t, err)

		t.Logf("原始查询第5页记录数: %d", len(originalRecords5))
		t.Logf("优化查询第5页记录数: %d", len(optimizedRecords5))

		// 第5页的记录数也应该相同或接近
		assert.InDelta(t, len(originalRecords5), len(optimizedRecords5), 5, "第5页的记录数应该相近")
	})
}

// TestCallbackPaginationPerformance 测试分页性能
func TestCallbackPaginationPerformance(t *testing.T) {
	// 连接测试数据库
	db, err := sql.Open("postgres", "*************************************************/go_kuaidi?sslmode=disable")
	require.NoError(t, err)
	defer db.Close()

	repo := NewPostgresCallbackRepository(db)
	ctx := context.Background()
	testUserID := "d7e45ff4-cb3d-470c-9fbc-22114639d096"

	t.Run("分页性能测试", func(t *testing.T) {
		// 测试不同页数的性能
		pages := []int{1, 5, 10, 20}

		for _, page := range pages {
			offset := (page - 1) * 20

			// 测试优化查询性能
			start := time.Now()
			records, err := repo.GetEnhancedForwardRecordsWithJoinOptimized(ctx, testUserID, 20, offset, map[string]string{})
			duration := time.Since(start)

			require.NoError(t, err)
			t.Logf("第%d页查询耗时: %v, 记录数: %d", page, duration, len(records))

			// 性能要求：每页查询应该在500ms以内
			assert.Less(t, duration.Milliseconds(), int64(500), "第%d页查询应该在500ms以内", page)
		}
	})
}
