package repository

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"github.com/your-org/go-kuaidi/internal/model"
)

// TestOrderListOptimization 测试订单列表显示优化
func TestOrderListOptimization(t *testing.T) {
	t.Run("订单列表项字段验证", func(t *testing.T) {
		// 创建测试订单列表项
		item := &model.OrderListItem{
			ID:              1,
			PlatformOrderNo: "GK20250711000000001",
			CustomerOrderNo: "MY_ORDER_001",
			ProviderOrderNo: "YD250711131107037536",
			TrackingNo:      "312816791325357",
			ExpressType:     "YTO",
			Provider:        "易达",
			Status:          "shipped",
			Price:           10.50,
			Weight:          1.5,
			CreatedAt:       time.Now(),
			UpdatedAt:       time.Now(),
		}

		// 验证所有订单标识字段都存在
		assert.Equal(t, "GK20250711000000001", item.PlatformOrderNo, "平台订单号字段应该存在")
		assert.Equal(t, "MY_ORDER_001", item.CustomerOrderNo, "客户订单号字段应该存在")
		assert.Equal(t, "YD250711131107037536", item.ProviderOrderNo, "供应商订单号字段应该存在")
		assert.Equal(t, "312816791325357", item.TrackingNo, "运单号字段应该存在")

		// 验证其他字段
		assert.Equal(t, int64(1), item.ID)
		assert.Equal(t, "YTO", item.ExpressType)
		assert.Equal(t, "易达", item.Provider)
		assert.Equal(t, "shipped", item.Status)
		assert.Equal(t, 10.50, item.Price)
		assert.Equal(t, 1.5, item.Weight)
	})

	t.Run("兼容性字段OrderNo优先显示平台订单号", func(t *testing.T) {
		// 测试有平台订单号的情况
		itemWithPlatform := &model.OrderListItem{
			PlatformOrderNo: "GK20250711000000001",
			ProviderOrderNo: "YD250711131107037536",
		}

		// 模拟repository中的逻辑：设置兼容性字段
		if itemWithPlatform.PlatformOrderNo != "" {
			itemWithPlatform.OrderNo = itemWithPlatform.PlatformOrderNo
		} else {
			itemWithPlatform.OrderNo = itemWithPlatform.ProviderOrderNo
		}

		assert.Equal(t, "GK20250711000000001", itemWithPlatform.OrderNo, "有平台订单号时，OrderNo应该显示平台订单号")

		// 测试没有平台订单号的情况（旧数据）
		itemWithoutPlatform := &model.OrderListItem{
			PlatformOrderNo: "", // 空平台订单号
			ProviderOrderNo: "YD250711131107037537",
		}

		// 模拟repository中的逻辑：设置兼容性字段
		if itemWithoutPlatform.PlatformOrderNo != "" {
			itemWithoutPlatform.OrderNo = itemWithoutPlatform.PlatformOrderNo
		} else {
			itemWithoutPlatform.OrderNo = itemWithoutPlatform.ProviderOrderNo
		}

		assert.Equal(t, "YD250711131107037537", itemWithoutPlatform.OrderNo, "无平台订单号时，OrderNo应该显示供应商订单号")
	})

	t.Run("管理员订单列表项继承验证", func(t *testing.T) {
		// 创建管理员订单列表项
		adminItem := &model.AdminOrderListItem{}

		// 验证继承的字段存在
		adminItem.PlatformOrderNo = "GK20250711000000001"
		adminItem.CustomerOrderNo = "MY_ORDER_001"
		adminItem.ProviderOrderNo = "YD250711131107037536"
		adminItem.TrackingNo = "312816791325357"

		assert.Equal(t, "GK20250711000000001", adminItem.PlatformOrderNo)
		assert.Equal(t, "MY_ORDER_001", adminItem.CustomerOrderNo)
		assert.Equal(t, "YD250711131107037536", adminItem.ProviderOrderNo)
		assert.Equal(t, "312816791325357", adminItem.TrackingNo)

		// 验证管理员特有字段存在
		adminItem.User.ID = "user123"
		adminItem.User.Username = "testuser"
		adminItem.User.Email = "<EMAIL>"
		adminItem.User.IsActive = true

		assert.Equal(t, "user123", adminItem.User.ID)
		assert.Equal(t, "testuser", adminItem.User.Username)
		assert.Equal(t, "<EMAIL>", adminItem.User.Email)
		assert.True(t, adminItem.User.IsActive)
	})

	t.Run("订单列表响应结构验证", func(t *testing.T) {
		// 创建订单列表响应
		response := &model.OrderListResponse{
			Success: true,
			Code:    200,
			Message: "查询成功",
			Data: &model.OrderListData{
				Items: []*model.OrderListItem{
					{
						ID:              1,
						PlatformOrderNo: "GK20250711000000001",
						CustomerOrderNo: "MY_ORDER_001",
						OrderNo:         "GK20250711000000001", // 兼容性字段
						ProviderOrderNo: "YD250711131107037536",
						TrackingNo:      "312816791325357",
						ExpressType:     "YTO",
						Provider:        "易达",
						Status:          "shipped",
						Price:           10.50,
					},
					{
						ID:              2,
						PlatformOrderNo: "", // 旧数据，无平台订单号
						CustomerOrderNo: "OLD_ORDER_001",
						OrderNo:         "YD250711131107037537", // 兼容性字段显示供应商订单号
						ProviderOrderNo: "YD250711131107037537",
						TrackingNo:      "312816791325358",
						ExpressType:     "STO",
						Provider:        "申通",
						Status:          "pending",
						Price:           8.50,
					},
				},
				Total:      2,
				Page:       1,
				PageSize:   20,
				TotalPages: 1,
				HasNext:    false,
				HasPrev:    false,
			},
		}

		// 验证响应结构
		assert.True(t, response.Success)
		assert.Equal(t, 200, response.Code)
		assert.Equal(t, "查询成功", response.Message)
		assert.NotNil(t, response.Data)
		assert.Len(t, response.Data.Items, 2)

		// 验证第一个订单（有平台订单号）
		item1 := response.Data.Items[0]
		assert.Equal(t, "GK20250711000000001", item1.PlatformOrderNo)
		assert.Equal(t, "GK20250711000000001", item1.OrderNo) // 兼容性字段显示平台订单号
		assert.Equal(t, "YD250711131107037536", item1.ProviderOrderNo)

		// 验证第二个订单（无平台订单号）
		item2 := response.Data.Items[1]
		assert.Equal(t, "", item2.PlatformOrderNo)
		assert.Equal(t, "YD250711131107037537", item2.OrderNo) // 兼容性字段显示供应商订单号
		assert.Equal(t, "YD250711131107037537", item2.ProviderOrderNo)

		// 验证分页信息
		assert.Equal(t, int64(2), response.Data.Total)
		assert.Equal(t, 1, response.Data.Page)
		assert.Equal(t, 20, response.Data.PageSize)
		assert.Equal(t, 1, response.Data.TotalPages)
		assert.False(t, response.Data.HasNext)
		assert.False(t, response.Data.HasPrev)
	})

	t.Run("订单标识字段语义验证", func(t *testing.T) {
		// 验证字段语义清晰性
		item := &model.OrderListItem{}

		// 验证字段存在性
		assert.NotNil(t, &item.PlatformOrderNo, "平台订单号字段应该存在")
		assert.NotNil(t, &item.CustomerOrderNo, "客户订单号字段应该存在")
		assert.NotNil(t, &item.OrderNo, "兼容性订单号字段应该存在")
		assert.NotNil(t, &item.ProviderOrderNo, "供应商订单号字段应该存在")
		assert.NotNil(t, &item.TrackingNo, "运单号字段应该存在")

		t.Log("✅ 订单列表显示优化验证通过")
		t.Log("📋 字段语义:")
		t.Log("  - platform_order_no: 平台生成的全局唯一订单号")
		t.Log("  - customer_order_no: 客户传入的订单号")
		t.Log("  - order_no: 兼容性字段，优先显示平台订单号")
		t.Log("  - provider_order_no: 供应商返回的订单号")
		t.Log("  - tracking_no: 快递运单号")
		t.Log("🔄 兼容性保证:")
		t.Log("  - 新订单：order_no = platform_order_no")
		t.Log("  - 旧订单：order_no = provider_order_no")
		t.Log("  - API消费者无需修改代码")
	})
}

// TestOrderListDisplayPriority 测试订单列表显示优先级
func TestOrderListDisplayPriority(t *testing.T) {
	t.Run("平台订单号优先显示", func(t *testing.T) {
		testCases := []struct {
			name            string
			platformOrderNo string
			providerOrderNo string
			expectedOrderNo string
		}{
			{
				name:            "有平台订单号",
				platformOrderNo: "GK20250711000000001",
				providerOrderNo: "YD250711131107037536",
				expectedOrderNo: "GK20250711000000001",
			},
			{
				name:            "无平台订单号",
				platformOrderNo: "",
				providerOrderNo: "YD250711131107037537",
				expectedOrderNo: "YD250711131107037537",
			},
			{
				name:            "平台订单号为空字符串",
				platformOrderNo: "",
				providerOrderNo: "STO123456789",
				expectedOrderNo: "STO123456789",
			},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				item := &model.OrderListItem{
					PlatformOrderNo: tc.platformOrderNo,
					ProviderOrderNo: tc.providerOrderNo,
				}

				// 模拟repository中的逻辑
				if item.PlatformOrderNo != "" {
					item.OrderNo = item.PlatformOrderNo
				} else {
					item.OrderNo = item.ProviderOrderNo
				}

				assert.Equal(t, tc.expectedOrderNo, item.OrderNo, "OrderNo应该按优先级正确设置")
			})
		}
	})

	t.Run("向后兼容性验证", func(t *testing.T) {
		// 模拟旧系统的订单数据
		oldOrderItems := []*model.OrderListItem{
			{
				ID:              1,
				PlatformOrderNo: "", // 旧数据没有平台订单号
				CustomerOrderNo: "OLD_001",
				ProviderOrderNo: "YD123456789",
				TrackingNo:      "312816791325357",
			},
			{
				ID:              2,
				PlatformOrderNo: "", // 旧数据没有平台订单号
				CustomerOrderNo: "OLD_002",
				ProviderOrderNo: "STO987654321",
				TrackingNo:      "312816791325358",
			},
		}

		// 应用兼容性逻辑
		for _, item := range oldOrderItems {
			if item.PlatformOrderNo != "" {
				item.OrderNo = item.PlatformOrderNo
			} else {
				item.OrderNo = item.ProviderOrderNo
			}
		}

		// 验证旧数据的兼容性
		assert.Equal(t, "YD123456789", oldOrderItems[0].OrderNo, "旧数据应该显示供应商订单号")
		assert.Equal(t, "STO987654321", oldOrderItems[1].OrderNo, "旧数据应该显示供应商订单号")

		// 验证字段完整性
		for _, item := range oldOrderItems {
			assert.NotEmpty(t, item.CustomerOrderNo, "客户订单号应该存在")
			assert.NotEmpty(t, item.ProviderOrderNo, "供应商订单号应该存在")
			assert.NotEmpty(t, item.OrderNo, "兼容性订单号应该存在")
			assert.NotEmpty(t, item.TrackingNo, "运单号应该存在")
		}
	})
}
