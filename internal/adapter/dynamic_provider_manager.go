package adapter

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/your-org/go-kuaidi/internal/util"
	"go.uber.org/zap"
)

// SystemConfigServiceInterface 系统配置服务接口（避免循环导入）
type SystemConfigServiceInterface interface {
	GetConfigAsBoolWithDefault(key string, defaultValue bool) bool
}

// DynamicProviderManager 动态供应商管理器
// 支持运行时动态加载、卸载和重新配置供应商适配器
type DynamicProviderManager struct {
	// 基础组件
	adapterFactory      ProviderAdapterFactory
	systemConfigService SystemConfigServiceInterface
	logger              *zap.Logger

	// 适配器管理
	adapters      map[string]ProviderAdapter
	adaptersMutex sync.RWMutex

	// 配置监听
	configWatcher *ConfigWatcher
	stopChan      chan struct{}

	// 状态管理
	isRunning bool
	runMutex  sync.Mutex

	// 指标收集
	metrics      ProviderMetrics
	metricsMutex sync.RWMutex
}

// NewDynamicProviderManager 创建动态供应商管理器
func NewDynamicProviderManager(
	adapterFactory ProviderAdapterFactory,
	systemConfigService SystemConfigServiceInterface,
	logger *zap.Logger,
) *DynamicProviderManager {
	return &DynamicProviderManager{
		adapterFactory:      adapterFactory,
		systemConfigService: systemConfigService,
		logger:              logger,
		adapters:            make(map[string]ProviderAdapter),
		stopChan:            make(chan struct{}),
		metrics: ProviderMetrics{
			ProviderMetrics: make(map[string]ProviderMetric),
		},
	}
}

// Start 启动动态管理器
func (dm *DynamicProviderManager) Start(ctx context.Context) error {
	dm.runMutex.Lock()
	defer dm.runMutex.Unlock()

	if dm.isRunning {
		return fmt.Errorf("动态供应商管理器已在运行")
	}

	dm.logger.Info("启动动态供应商管理器")

	// 1. 初始化配置监听器
	dm.configWatcher = NewConfigWatcher(dm.systemConfigService, dm.logger)

	// 2. 加载初始适配器
	if err := dm.loadAllAdapters(ctx); err != nil {
		return fmt.Errorf("加载初始适配器失败: %w", err)
	}

	// 3. 启动配置监听
	go dm.watchConfigChanges(ctx)

	dm.isRunning = true
	dm.logger.Info("动态供应商管理器启动成功")
	return nil
}

// Stop 停止动态管理器
func (dm *DynamicProviderManager) Stop(ctx context.Context) error {
	dm.runMutex.Lock()
	defer dm.runMutex.Unlock()

	if !dm.isRunning {
		return nil
	}

	dm.logger.Info("停止动态供应商管理器")

	// 1. 停止配置监听
	close(dm.stopChan)

	// 2. 优雅关闭所有适配器
	dm.adaptersMutex.Lock()
	for name, adapter := range dm.adapters {
		dm.logger.Info("关闭适配器", zap.String("provider", name))
		// 如果适配器实现了Closer接口，调用Close方法
		if closer, ok := adapter.(interface{ Close() error }); ok {
			if err := closer.Close(); err != nil {
				dm.logger.Warn("关闭适配器失败", zap.String("provider", name), zap.Error(err))
			}
		}
	}
	dm.adapters = make(map[string]ProviderAdapter)
	dm.adaptersMutex.Unlock()

	dm.isRunning = false
	dm.logger.Info("动态供应商管理器已停止")
	return nil
}

// Get 获取供应商适配器（线程安全）
func (dm *DynamicProviderManager) Get(name string) (ProviderAdapter, bool) {
	dm.adaptersMutex.RLock()
	defer dm.adaptersMutex.RUnlock()

	adapter, exists := dm.adapters[name]
	if !exists {
		return nil, false
	}

	// 检查供应商是否启用
	ctx := context.Background()
	enabled, err := dm.adapterFactory.IsProviderEnabled(ctx, name)
	if err != nil || !enabled {
		dm.logger.Debug("供应商未启用或检查失败",
			zap.String("provider", name),
			zap.Bool("enabled", enabled),
			zap.Error(err))
		return nil, false
	}

	return adapter, true
}

// GetForTracking 获取供应商适配器用于物流跟踪（不检查启用状态）
// 物流跟踪功能不应受供应商启用/禁用开关影响，因为用户有权查询已下订单的物流状态
func (dm *DynamicProviderManager) GetForTracking(name string) (ProviderAdapter, bool) {
	dm.adaptersMutex.RLock()
	adapter, exists := dm.adapters[name]
	dm.adaptersMutex.RUnlock()

	if exists {
		dm.logger.Debug("获取已加载的供应商适配器用于物流跟踪",
			zap.String("provider", name))
		return adapter, true
	}

	// 如果适配器不存在，尝试临时创建一个用于物流跟踪
	dm.logger.Info("供应商适配器未加载，尝试临时创建用于物流跟踪",
		zap.String("provider", name))

	ctx := context.Background()
	tempAdapter, err := dm.createAdapterForTracking(ctx, name)
	if err != nil {
		dm.logger.Error("临时创建供应商适配器失败",
			zap.String("provider", name),
			zap.Error(err))
		return nil, false
	}

	dm.logger.Info("临时创建供应商适配器成功，用于物流跟踪",
		zap.String("provider", name))

	return tempAdapter, true
}

// GetAll 获取所有启用的供应商适配器
func (dm *DynamicProviderManager) GetAll() []ProviderAdapter {
	dm.adaptersMutex.RLock()
	defer dm.adaptersMutex.RUnlock()

	// 🔍 详细调试：记录开始获取所有供应商
	dm.logger.Info("🚀 [DEBUG] 开始获取所有启用的供应商适配器")
	dm.logger.Info("📋 [DEBUG] 当前加载的适配器总数", zap.Int("total_adapters", len(dm.adapters)))

	var enabledAdapters []ProviderAdapter
	ctx := context.Background()

	// 🔍 详细调试：先列出所有已加载的适配器
	dm.logger.Info("🗂️ [DEBUG] 当前已加载的适配器列表:")
	for name := range dm.adapters {
		dm.logger.Info("   - " + name)
	}

	for name, adapter := range dm.adapters {
		dm.logger.Info("🔍 [DEBUG] 检查供应商状态", zap.String("provider", name))

		enabled, err := dm.adapterFactory.IsProviderEnabled(ctx, name)
		if err != nil {
			dm.logger.Warn("❌ [ERROR] 检查供应商状态失败", zap.String("provider", name), zap.Error(err))
			// 🔍 特别关注快递鸟的错误
			if name == "kuaidiniao" {
				dm.logger.Error("🚨 [CRITICAL] 快递鸟状态检查失败！", zap.Error(err))
			}
			continue
		}

		// 🔍 详细调试：记录每个供应商的启用状态
		if enabled {
			enabledAdapters = append(enabledAdapters, adapter)
			if name == "kuaidiniao" {
				dm.logger.Info("✅ [DEBUG] 快递鸟供应商已启用并添加到结果中")
			} else {
				dm.logger.Info("✅ [DEBUG] 供应商已启用", zap.String("provider", name))
			}
		} else {
			if name == "kuaidiniao" {
				dm.logger.Warn("❌ [WARNING] 快递鸟供应商已禁用！")
			} else {
				dm.logger.Info("⏸️ [DEBUG] 供应商已禁用", zap.String("provider", name))
			}
		}
	}

	// 🔍 详细调试：汇总结果
	dm.logger.Info("📊 [DEBUG] 启用的供应商汇总", zap.Int("enabled_count", len(enabledAdapters)))

	kuaidiniaoIncluded := false
	for _, adapter := range enabledAdapters {
		providerName := adapter.Name()
		dm.logger.Info("   ✅ " + providerName)
		if providerName == "kuaidiniao" {
			kuaidiniaoIncluded = true
		}
	}

	if kuaidiniaoIncluded {
		dm.logger.Info("🎯 [DEBUG] 快递鸟已成功包含在启用的供应商列表中")
	} else {
		dm.logger.Warn("🚨 [WARNING] 快递鸟未包含在启用的供应商列表中！")
	}

	return enabledAdapters
}

// GetAllExcluding 获取所有启用的供应商适配器，排除指定的供应商
func (dm *DynamicProviderManager) GetAllExcluding(excludeProviders []string) []ProviderAdapter {
	dm.adaptersMutex.RLock()
	defer dm.adaptersMutex.RUnlock()

	var enabledAdapters []ProviderAdapter
	ctx := context.Background()

	// 创建排除集合
	excludeSet := make(map[string]bool)
	for _, exclude := range excludeProviders {
		excludeSet[exclude] = true
	}

	for name, adapter := range dm.adapters {
		// 跳过被排除的供应商
		if excludeSet[name] {
			continue
		}

		enabled, err := dm.adapterFactory.IsProviderEnabled(ctx, name)
		if err != nil {
			dm.logger.Warn("检查供应商状态失败", zap.String("provider", name), zap.Error(err))
			continue
		}
		if enabled {
			enabledAdapters = append(enabledAdapters, adapter)
		}
	}

	dm.logger.Debug("获取排除指定供应商的启用适配器",
		zap.Strings("excluded_providers", excludeProviders),
		zap.Int("enabled_count", len(enabledAdapters)))

	return enabledAdapters
}

// ReloadProvider 重新加载指定供应商适配器（零停机）
func (dm *DynamicProviderManager) ReloadProvider(ctx context.Context, providerCode string) error {
	dm.logger.Info("开始零停机重新加载供应商适配器", zap.String("provider", providerCode))

	// 1. 检查供应商是否启用
	enabled, err := dm.adapterFactory.IsProviderEnabled(ctx, providerCode)
	if err != nil {
		return fmt.Errorf("检查供应商状态失败: %w", err)
	}

	if enabled {
		// 启用状态：零停机更新适配器
		return dm.reloadEnabledProvider(ctx, providerCode)
	} else {
		// 禁用状态：安全移除适配器
		return dm.removeProvider(ctx, providerCode)
	}
}

// reloadEnabledProvider 零停机重新加载启用的供应商
func (dm *DynamicProviderManager) reloadEnabledProvider(ctx context.Context, providerCode string) error {
	startTime := util.NowBeijing()

	// 1. 创建新适配器（在锁外进行，避免阻塞服务）
	newAdapter, err := dm.createAdapter(ctx, providerCode)
	if err != nil {
		dm.recordReloadMetrics(providerCode, false, time.Since(startTime), err)
		return fmt.Errorf("创建新适配器失败: %w", err)
	}

	// 2. 原子性替换适配器
	dm.adaptersMutex.Lock()
	oldAdapter, exists := dm.adapters[providerCode]
	dm.adapters[providerCode] = newAdapter
	dm.adaptersMutex.Unlock()

	dm.logger.Info("供应商适配器原子性替换成功",
		zap.String("provider", providerCode),
		zap.Duration("reload_time", time.Since(startTime)))

	// 3. 异步关闭旧适配器（避免阻塞）
	if exists && oldAdapter != nil {
		go dm.gracefulCloseAdapter(providerCode, oldAdapter)
	}

	// 4. 记录成功指标
	dm.recordReloadMetrics(providerCode, true, time.Since(startTime), nil)

	return nil
}

// removeProvider 安全移除供应商适配器
func (dm *DynamicProviderManager) removeProvider(ctx context.Context, providerCode string) error {
	dm.adaptersMutex.Lock()
	adapter, exists := dm.adapters[providerCode]
	if exists {
		delete(dm.adapters, providerCode)
	}
	dm.adaptersMutex.Unlock()

	if exists && adapter != nil {
		dm.logger.Info("供应商适配器已从服务中移除", zap.String("provider", providerCode))
		// 异步关闭适配器
		go dm.gracefulCloseAdapter(providerCode, adapter)
	}

	return nil
}

// gracefulCloseAdapter 优雅关闭适配器
func (dm *DynamicProviderManager) gracefulCloseAdapter(providerCode string, adapter ProviderAdapter) {
	defer func() {
		if r := recover(); r != nil {
			dm.logger.Error("关闭适配器时发生panic",
				zap.String("provider", providerCode),
				zap.Any("panic", r))
		}
	}()

	// 等待一小段时间，确保正在进行的请求完成
	time.Sleep(100 * time.Millisecond)

	if closer, ok := adapter.(interface{ Close() error }); ok {
		if err := closer.Close(); err != nil {
			dm.logger.Warn("关闭适配器失败",
				zap.String("provider", providerCode),
				zap.Error(err))
		} else {
			dm.logger.Info("适配器已优雅关闭",
				zap.String("provider", providerCode))
		}
	}
}

// recordReloadMetrics 记录重载指标
func (dm *DynamicProviderManager) recordReloadMetrics(providerCode string, success bool, duration time.Duration, err error) {
	dm.metricsMutex.Lock()
	defer dm.metricsMutex.Unlock()

	// 更新全局指标
	dm.metrics.TotalReloads++
	dm.metrics.LastReloadTime = util.NowBeijing()

	if success {
		dm.metrics.SuccessReloads++
	} else {
		dm.metrics.FailedReloads++
	}

	// 更新供应商指标
	metric, exists := dm.metrics.ProviderMetrics[providerCode]
	if !exists {
		metric = ProviderMetric{
			Code: providerCode,
		}
	}

	metric.ReloadCount++
	metric.LastReload = util.NowBeijing()

	if err != nil {
		metric.LastError = err.Error()
	} else {
		metric.LastError = ""
	}

	// 计算成功率
	if metric.ReloadCount > 0 {
		successCount := metric.ReloadCount
		if err != nil {
			successCount--
		}
		metric.SuccessRate = float64(successCount) / float64(metric.ReloadCount) * 100
	}

	// 计算平均重载时间（毫秒）
	metric.AvgReloadTime = float64(duration.Nanoseconds()) / 1000000

	dm.metrics.ProviderMetrics[providerCode] = metric

	dm.logger.Debug("记录重载指标",
		zap.String("provider", providerCode),
		zap.Bool("success", success),
		zap.Duration("duration", duration),
		zap.Int("total_reloads", dm.metrics.TotalReloads))
}

// GetMetrics 获取指标信息
func (dm *DynamicProviderManager) GetMetrics() ProviderMetrics {
	dm.metricsMutex.RLock()
	defer dm.metricsMutex.RUnlock()

	// 深拷贝指标数据
	metrics := ProviderMetrics{
		TotalReloads:    dm.metrics.TotalReloads,
		SuccessReloads:  dm.metrics.SuccessReloads,
		FailedReloads:   dm.metrics.FailedReloads,
		LastReloadTime:  dm.metrics.LastReloadTime,
		ProviderMetrics: make(map[string]ProviderMetric),
	}

	for k, v := range dm.metrics.ProviderMetrics {
		metrics.ProviderMetrics[k] = v
	}

	return metrics
}

// 🔥 删除缓存：清理指定供应商的适配器缓存（现在是空操作）
func (dm *DynamicProviderManager) InvalidateProviderCache(providerCode string) error {
	dm.logger.Info("无缓存模式：清理供应商适配器缓存操作已跳过",
		zap.String("provider_code", providerCode))
	return nil
}

// ReloadAllProviders 重新加载所有供应商适配器
func (dm *DynamicProviderManager) ReloadAllProviders(ctx context.Context) error {
	dm.logger.Info("重新加载所有供应商适配器")

	knownProviders := []string{"kuaidi100", "yida", "yuntong", "cainiao", "kuaidiniao"}

	for _, providerCode := range knownProviders {
		if err := dm.ReloadProvider(ctx, providerCode); err != nil {
			dm.logger.Error("重新加载供应商失败",
				zap.String("provider", providerCode),
				zap.Error(err))
			// 继续处理其他供应商，不因单个失败而中断
		}
	}

	dm.logger.Info("所有供应商适配器重新加载完成")
	return nil
}

// loadAllAdapters 加载所有启用的适配器
func (dm *DynamicProviderManager) loadAllAdapters(ctx context.Context) error {
	dm.logger.Info("加载所有启用的供应商适配器")

	knownProviders := []string{"kuaidi100", "yida", "yuntong", "cainiao", "kuaidiniao"}

	for _, providerCode := range knownProviders {
		enabled, err := dm.adapterFactory.IsProviderEnabled(ctx, providerCode)
		if err != nil {
			dm.logger.Warn("检查供应商状态失败",
				zap.String("provider", providerCode),
				zap.Error(err))
			continue
		}

		if enabled {
			adapter, err := dm.createAdapter(ctx, providerCode)
			if err != nil {
				dm.logger.Error("创建适配器失败",
					zap.String("provider", providerCode),
					zap.Error(err))
				continue
			}

			dm.adapters[providerCode] = adapter
			dm.logger.Info("适配器加载成功", zap.String("provider", providerCode))
		}
	}

	dm.logger.Info("适配器加载完成", zap.Int("count", len(dm.adapters)))
	return nil
}

// createAdapter 创建指定供应商的适配器
func (dm *DynamicProviderManager) createAdapter(ctx context.Context, providerCode string) (ProviderAdapter, error) {
	// 使用工厂创建适配器
	if factory, ok := dm.adapterFactory.(*DefaultProviderAdapterFactory); ok {
		return factory.CreateAdapter(ctx, providerCode)
	}

	// 如果不是默认工厂，使用接口方法
	switch providerCode {
	case "kuaidi100":
		return dm.adapterFactory.CreateKuaidi100Adapter(ctx)
	case "yida":
		return dm.adapterFactory.CreateYidaAdapter(ctx)
	case "yuntong":
		return dm.adapterFactory.CreateYuntongAdapter(ctx)
	case "cainiao":
		return dm.adapterFactory.CreateCainiaoAdapter(ctx)
	case "kuaidiniao":
		return dm.adapterFactory.CreateKuaidiNiaoAdapter(ctx)
	default:
		return nil, fmt.Errorf("不支持的供应商: %s", providerCode)
	}
}

// watchConfigChanges 监听配置变更
func (dm *DynamicProviderManager) watchConfigChanges(ctx context.Context) {
	dm.logger.Info("开始监听配置变更")

	ticker := time.NewTicker(1 * time.Second) // 🔥 修复：从2秒改为1秒，提高热重载响应速度
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			dm.logger.Info("配置监听器收到上下文取消信号")
			return
		case <-dm.stopChan:
			dm.logger.Info("配置监听器收到停止信号")
			return
		case <-ticker.C:
			// 检查配置变更并重新加载
			if err := dm.checkAndReloadChangedProviders(ctx); err != nil {
				dm.logger.Error("检查配置变更失败", zap.Error(err))
			}
		}
	}
}

// checkAndReloadChangedProviders 检查并重新加载变更的供应商
func (dm *DynamicProviderManager) checkAndReloadChangedProviders(ctx context.Context) error {
	knownProviders := []string{"kuaidi100", "yida", "yuntong", "cainiao", "kuaidiniao"}

	for _, providerCode := range knownProviders {
		enabled, err := dm.adapterFactory.IsProviderEnabled(ctx, providerCode)
		if err != nil {
			continue
		}

		dm.adaptersMutex.RLock()
		_, hasAdapter := dm.adapters[providerCode]
		dm.adaptersMutex.RUnlock()

		// 状态不一致时重新加载
		if (enabled && !hasAdapter) || (!enabled && hasAdapter) {
			dm.logger.Info("检测到供应商状态变更",
				zap.String("provider", providerCode),
				zap.Bool("enabled", enabled),
				zap.Bool("has_adapter", hasAdapter))

			if err := dm.ReloadProvider(ctx, providerCode); err != nil {
				dm.logger.Error("重新加载供应商失败",
					zap.String("provider", providerCode),
					zap.Error(err))
			}
		}
	}

	return nil
}

// GetProviderStatus 获取供应商状态信息
func (dm *DynamicProviderManager) GetProviderStatus(ctx context.Context) map[string]ProviderStatus {
	dm.adaptersMutex.RLock()
	defer dm.adaptersMutex.RUnlock()

	status := make(map[string]ProviderStatus)
	knownProviders := []string{"kuaidi100", "yida", "yuntong", "kuaidiniao"}

	for _, providerCode := range knownProviders {
		enabled, err := dm.adapterFactory.IsProviderEnabled(ctx, providerCode)

		_, hasAdapter := dm.adapters[providerCode]

		status[providerCode] = ProviderStatus{
			Code:       providerCode,
			Enabled:    enabled,
			HasAdapter: hasAdapter,
			Error:      err,
			LastCheck:  util.NowBeijing(),
		}
	}

	return status
}

// createAdapterForTracking 创建指定供应商的适配器用于物流跟踪（不检查启用状态）
func (dm *DynamicProviderManager) createAdapterForTracking(ctx context.Context, providerCode string) (ProviderAdapter, error) {
	// 使用工厂创建适配器，优先使用跟踪专用方法
	if factory, ok := dm.adapterFactory.(*DefaultProviderAdapterFactory); ok {
		switch providerCode {
		case "kuaidi100":
			return factory.CreateKuaidi100AdapterForTracking(ctx)
		case "yida":
			return factory.CreateYidaAdapter(ctx)
		case "yuntong":
			return factory.CreateYuntongAdapter(ctx)
		case "cainiao":
			return factory.CreateCainiaoAdapter(ctx)
		default:
			return nil, fmt.Errorf("不支持的供应商: %s", providerCode)
		}
	}

	// 如果不是默认工厂，使用接口方法
	switch providerCode {
	case "kuaidi100":
		return dm.adapterFactory.CreateKuaidi100Adapter(ctx)
	case "yida":
		return dm.adapterFactory.CreateYidaAdapter(ctx)
	case "yuntong":
		return dm.adapterFactory.CreateYuntongAdapter(ctx)
	case "cainiao":
		return dm.adapterFactory.CreateCainiaoAdapter(ctx)
	case "kuaidiniao":
		return dm.adapterFactory.CreateKuaidiNiaoAdapter(ctx)
	default:
		return nil, fmt.Errorf("不支持的供应商: %s", providerCode)
	}
}

// ProviderStatus 供应商状态信息
type ProviderStatus struct {
	Code         string    `json:"code"`
	Enabled      bool      `json:"enabled"`
	HasAdapter   bool      `json:"has_adapter"`
	Error        error     `json:"error,omitempty"`
	LastCheck    time.Time `json:"last_check"`
	LastReload   time.Time `json:"last_reload,omitempty"`
	ReloadCount  int       `json:"reload_count"`
	HealthStatus string    `json:"health_status"`
}

// ProviderMetrics 供应商指标信息
type ProviderMetrics struct {
	TotalReloads    int                       `json:"total_reloads"`
	SuccessReloads  int                       `json:"success_reloads"`
	FailedReloads   int                       `json:"failed_reloads"`
	LastReloadTime  time.Time                 `json:"last_reload_time"`
	ProviderMetrics map[string]ProviderMetric `json:"provider_metrics"`
}

// ProviderMetric 单个供应商指标
type ProviderMetric struct {
	Code          string    `json:"code"`
	ReloadCount   int       `json:"reload_count"`
	LastReload    time.Time `json:"last_reload"`
	LastError     string    `json:"last_error,omitempty"`
	SuccessRate   float64   `json:"success_rate"`
	AvgReloadTime float64   `json:"avg_reload_time_ms"`
}
