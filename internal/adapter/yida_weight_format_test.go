package adapter

import (
	"math"
	"testing"
)

func TestYidaAdapter_formatWeightForYida(t *testing.T) {
	adapter := &YidaAdapter{}

	tests := []struct {
		name     string
		input    float64
		expected float64
	}{
		{
			name:     "原始问题重量值",
			input:    3.0625,
			expected: 3.06,
		},
		{
			name:     "多位小数",
			input:    1.23456,
			expected: 1.23,
		},
		{
			name:     "小数值",
			input:    0.1234,
			expected: 0.12,
		},
		{
			name:     "接近整数",
			input:    10.999999,
			expected: 11.00,
		},
		{
			name:     "整数",
			input:    5.0,
			expected: 5.00,
		},
		{
			name:     "很小的值",
			input:    0.001,
			expected: 0.00,
		},
		{
			name:     "负数",
			input:    -1.234,
			expected: -1.23,
		},
		{
			name:     "零值",
			input:    0.0,
			expected: 0.00,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := adapter.formatWeightForYida(tt.input)
			if math.Abs(result-tt.expected) > 0.001 {
				t.Errorf("formatWeightForYida(%f) = %f, expected %f", tt.input, result, tt.expected)
			}
		})
	}
}

func TestYidaAdapter_formatWeightForYida_Precision(t *testing.T) {
	adapter := &YidaAdapter{}

	// 测试精度问题
	input := 3.0625
	result := adapter.formatWeightForYida(input)

	// 验证结果确实是2位小数
	rounded := math.Round(result*100) / 100
	if result != rounded {
		t.Errorf("formatWeightForYida(%f) = %f, 不是2位小数精度", input, result)
	}

	// 验证结果与原值不同（修复了精度问题）
	if result == input {
		t.Errorf("formatWeightForYida(%f) = %f, 应该与原值不同", input, result)
	}
}
