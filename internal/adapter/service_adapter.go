package adapter

import (
	"context"
)

// ServiceProviderConfigAdapter 服务层供应商配置适配器
// 用于适配service.ProviderConfigService到adapter包的接口
type ServiceProviderConfigAdapter struct {
	service interface {
		GetKuaidi100Config(ctx context.Context) (*Kuaidi100Config, error)
		GetYidaConfig(ctx context.Context) (*YidaConfig, error)
		GetYuntongConfig(ctx context.Context) (*YuntongConfig, error)
		IsProviderEnabled(ctx context.Context, providerName string) (bool, error)
	}
}

// NewServiceProviderConfigAdapter 创建服务层供应商配置适配器
func NewServiceProviderConfigAdapter(service interface {
	GetKuaidi100Config(ctx context.Context) (*Kuaidi100Config, error)
	GetYidaConfig(ctx context.Context) (*YidaConfig, error)
	GetYuntongConfig(ctx context.Context) (*YuntongConfig, error)
	IsProviderEnabled(ctx context.Context, providerName string) (bool, error)
}) *ServiceProviderConfigAdapter {
	return &ServiceProviderConfigAdapter{
		service: service,
	}
}

// GetKuaidi100Config 获取快递100配置
func (a *ServiceProviderConfigAdapter) GetKuaidi100Config(ctx context.Context) (*Kuaidi100Config, error) {
	return a.service.GetKuaidi100Config(ctx)
}

// GetYidaConfig 获取易达配置
func (a *ServiceProviderConfigAdapter) GetYidaConfig(ctx context.Context) (*YidaConfig, error) {
	return a.service.GetYidaConfig(ctx)
}

// GetYuntongConfig 获取云通配置
func (a *ServiceProviderConfigAdapter) GetYuntongConfig(ctx context.Context) (*YuntongConfig, error) {
	return a.service.GetYuntongConfig(ctx)
}

// IsProviderEnabled 检查供应商是否启用
func (a *ServiceProviderConfigAdapter) IsProviderEnabled(ctx context.Context, providerName string) (bool, error) {
	return a.service.IsProviderEnabled(ctx, providerName)
}
