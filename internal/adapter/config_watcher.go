package adapter

import (
	"context"
	"sync"
	"time"

	"github.com/your-org/go-kuaidi/internal/util"
	"go.uber.org/zap"
)

// ConfigWatcher 配置监听器
// 监听数据库配置变更，支持实时检测供应商配置的启用/禁用状态变化
type ConfigWatcher struct {
	systemConfigService interface {
		GetConfigAsBoolWithDefault(key string, defaultValue bool) bool
	}
	logger *zap.Logger

	// 配置状态缓存
	lastKnownStates map[string]bool
	stateMutex      sync.RWMutex

	// 变更通知
	changeCallbacks []ConfigChangeCallback
	callbackMutex   sync.RWMutex
}

// ConfigChangeCallback 配置变更回调函数
type ConfigChangeCallback func(providerCode string, enabled bool)

// ConfigChangeEvent 配置变更事件
type ConfigChangeEvent struct {
	ProviderCode string    `json:"provider_code"`
	OldValue     bool      `json:"old_value"`
	NewValue     bool      `json:"new_value"`
	Timestamp    time.Time `json:"timestamp"`
}

// NewConfigWatcher 创建配置监听器
func NewConfigWatcher(
	systemConfigService interface {
		GetConfigAsBoolWithDefault(key string, defaultValue bool) bool
	},
	logger *zap.Logger,
) *ConfigWatcher {
	return &ConfigWatcher{
		systemConfigService: systemConfigService,
		logger:              logger,
		lastKnownStates:     make(map[string]bool),
		changeCallbacks:     make([]ConfigChangeCallback, 0),
	}
}

// Start 启动配置监听
func (cw *ConfigWatcher) Start(ctx context.Context) error {
	cw.logger.Info("启动配置监听器")

	// 初始化已知状态
	if err := cw.initializeKnownStates(); err != nil {
		return err
	}

	// 启动监听循环
	go cw.watchLoop(ctx)

	return nil
}

// Stop 停止配置监听
func (cw *ConfigWatcher) Stop() {
	cw.logger.Info("停止配置监听器")
}

// AddChangeCallback 添加配置变更回调
func (cw *ConfigWatcher) AddChangeCallback(callback ConfigChangeCallback) {
	cw.callbackMutex.Lock()
	defer cw.callbackMutex.Unlock()

	cw.changeCallbacks = append(cw.changeCallbacks, callback)
}

// CheckForChanges 检查配置变更
func (cw *ConfigWatcher) CheckForChanges() []ConfigChangeEvent {
	knownProviders := []string{"kuaidi100", "yida", "yuntong", "cainiao", "kuaidiniao"}
	var changes []ConfigChangeEvent

	cw.stateMutex.Lock()
	defer cw.stateMutex.Unlock()

	for _, providerCode := range knownProviders {
		configKey := "provider." + providerCode + "_enabled"
		currentState := cw.systemConfigService.GetConfigAsBoolWithDefault(configKey, false)

		lastState, exists := cw.lastKnownStates[providerCode]

		// 检查是否有变更
		if !exists || lastState != currentState {
			change := ConfigChangeEvent{
				ProviderCode: providerCode,
				OldValue:     lastState,
				NewValue:     currentState,
				Timestamp:    util.NowBeijing(),
			}
			changes = append(changes, change)

			// 更新已知状态
			cw.lastKnownStates[providerCode] = currentState

			// 触发回调
			cw.triggerCallbacks(providerCode, currentState)

			cw.logger.Info("检测到供应商配置变更",
				zap.String("provider", providerCode),
				zap.Bool("old_value", lastState),
				zap.Bool("new_value", currentState))
		}
	}

	return changes
}

// GetCurrentStates 获取当前所有供应商的状态
func (cw *ConfigWatcher) GetCurrentStates() map[string]bool {
	cw.stateMutex.RLock()
	defer cw.stateMutex.RUnlock()

	states := make(map[string]bool)
	for provider, state := range cw.lastKnownStates {
		states[provider] = state
	}

	return states
}

// initializeKnownStates 初始化已知状态
func (cw *ConfigWatcher) initializeKnownStates() error {
	knownProviders := []string{"kuaidi100", "yida", "yuntong", "cainiao", "kuaidiniao"}

	cw.stateMutex.Lock()
	defer cw.stateMutex.Unlock()

	for _, providerCode := range knownProviders {
		configKey := "provider." + providerCode + "_enabled"
		currentState := cw.systemConfigService.GetConfigAsBoolWithDefault(configKey, false)
		cw.lastKnownStates[providerCode] = currentState

		cw.logger.Debug("初始化供应商状态",
			zap.String("provider", providerCode),
			zap.Bool("enabled", currentState))
	}

	cw.logger.Info("配置监听器状态初始化完成", zap.Int("provider_count", len(knownProviders)))
	return nil
}

// watchLoop 监听循环
func (cw *ConfigWatcher) watchLoop(ctx context.Context) {
	ticker := time.NewTicker(1 * time.Second) // 每1秒检查一次，提高热重载响应速度
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			cw.logger.Info("配置监听循环收到上下文取消信号")
			return
		case <-ticker.C:
			changes := cw.CheckForChanges()
			if len(changes) > 0 {
				cw.logger.Info("检测到配置变更", zap.Int("change_count", len(changes)))
			}
		}
	}
}

// triggerCallbacks 触发配置变更回调
func (cw *ConfigWatcher) triggerCallbacks(providerCode string, enabled bool) {
	cw.callbackMutex.RLock()
	defer cw.callbackMutex.RUnlock()

	for _, callback := range cw.changeCallbacks {
		go func(cb ConfigChangeCallback) {
			defer func() {
				if r := recover(); r != nil {
					cw.logger.Error("配置变更回调执行失败",
						zap.String("provider", providerCode),
						zap.Any("panic", r))
				}
			}()
			cb(providerCode, enabled)
		}(callback)
	}
}

// ForceRefresh 强制刷新所有供应商状态
func (cw *ConfigWatcher) ForceRefresh() []ConfigChangeEvent {
	cw.logger.Info("强制刷新供应商配置状态")

	// 清空已知状态，强制检查所有供应商
	cw.stateMutex.Lock()
	cw.lastKnownStates = make(map[string]bool)
	cw.stateMutex.Unlock()

	// 重新初始化并检查变更
	if err := cw.initializeKnownStates(); err != nil {
		cw.logger.Error("强制刷新时初始化状态失败", zap.Error(err))
		return nil
	}

	return cw.CheckForChanges()
}
