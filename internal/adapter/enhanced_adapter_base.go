package adapter

import (
	"context"
	"encoding/json"
	"time"

	"go.uber.org/zap"

	"github.com/your-org/go-kuaidi/internal/model"
)

// EnhancedAdapterBase 增强的适配器基类
// 提供统一的响应记录功能
type EnhancedAdapterBase struct {
	logger *zap.Logger
}

// NewEnhancedAdapterBase 创建增强适配器基类
func NewEnhancedAdapterBase(logger *zap.Logger) *EnhancedAdapterBase {
	return &EnhancedAdapterBase{
		logger: logger,
	}
}

// RecordAPICall 记录API调用（带响应数据）
func (base *EnhancedAdapterBase) RecordAPICall(
	ctx context.Context,
	customerOrderNo string,
	provider string,
	method string,
	requestData interface{},
	responseData interface{},
	err error,
) {
	if err != nil {
		base.logger.Error("供应商API调用失败",
			zap.String("customer_order_no", customerOrderNo),
			zap.String("provider", provider),
			zap.String("method", method),
			zap.Any("request_data", requestData),
			zap.Any("response_data", responseData),
			zap.Error(err))
	} else {
		base.logger.Info("供应商API调用成功",
			zap.String("customer_order_no", customerOrderNo),
			zap.String("provider", provider),
			zap.String("method", method),
			zap.Any("request_data", requestData),
			zap.Any("response_data", responseData))
	}
}

// RecordAPICallWithTiming 记录带时间的API调用
func (base *EnhancedAdapterBase) RecordAPICallWithTiming(
	ctx context.Context,
	customerOrderNo string,
	provider string,
	method string,
	requestData interface{},
	responseData interface{},
	startTime time.Time,
	err error,
) {
	processingTime := time.Since(startTime).Milliseconds()

	if err != nil {
		base.logger.Error("供应商API调用失败",
			zap.String("customer_order_no", customerOrderNo),
			zap.String("provider", provider),
			zap.String("method", method),
			zap.Int64("processing_time_ms", processingTime),
			zap.Any("request_data", requestData),
			zap.Any("response_data", responseData),
			zap.Error(err))
	} else {
		base.logger.Info("供应商API调用成功",
			zap.String("customer_order_no", customerOrderNo),
			zap.String("provider", provider),
			zap.String("method", method),
			zap.Int64("processing_time_ms", processingTime),
			zap.Any("request_data", requestData),
			zap.Any("response_data", responseData))
	}
}

// ParseJSONResponse 解析JSON响应并记录
func (base *EnhancedAdapterBase) ParseJSONResponse(
	customerOrderNo string,
	provider string,
	rawResponse string,
	target interface{},
) error {
	err := json.Unmarshal([]byte(rawResponse), target)
	if err != nil {
		base.logger.Error("供应商响应解析失败",
			zap.String("customer_order_no", customerOrderNo),
			zap.String("provider", provider),
			zap.String("raw_response", rawResponse),
			zap.Error(err))
		return err
	}

	base.logger.Info("供应商响应解析成功",
		zap.String("customer_order_no", customerOrderNo),
		zap.String("provider", provider),
		zap.Any("parsed_response", target))

	return nil
}

// RecordBusinessError 记录业务错误
func (base *EnhancedAdapterBase) RecordBusinessError(
	customerOrderNo string,
	provider string,
	errorCode interface{},
	errorMessage string,
	fullResponse string,
) {
	base.logger.Error("供应商业务失败",
		zap.String("customer_order_no", customerOrderNo),
		zap.String("provider", provider),
		zap.Any("error_code", errorCode),
		zap.String("error_message", errorMessage),
		zap.String("full_response", fullResponse))
}

// CreateOrderResultWithLogging 创建订单结果并记录
func (base *EnhancedAdapterBase) CreateOrderResultWithLogging(
	customerOrderNo string,
	provider string,
	orderNo string,
	trackingNo string,
	price float64,
	rawResponse interface{},
) *model.OrderResult {
	result := &model.OrderResult{
		CustomerOrderNo: customerOrderNo,
		OrderNo:         orderNo,
		TrackingNo:      trackingNo, // 🚀 修复：使用正确的字段名
		Price:           price,
	}

	base.logger.Info("订单创建结果",
		zap.String("customer_order_no", customerOrderNo),
		zap.String("provider", provider),
		zap.String("order_no", orderNo),
		zap.String("tracking_no", trackingNo),
		zap.Float64("price", price),
		zap.Any("raw_response", rawResponse))

	return result
}

// LogAPIStart 记录API调用开始
func (base *EnhancedAdapterBase) LogAPIStart(
	customerOrderNo string,
	provider string,
	method string,
	requestData interface{},
) {
	base.logger.Info("开始调用供应商API",
		zap.String("customer_order_no", customerOrderNo),
		zap.String("provider", provider),
		zap.String("method", method),
		zap.Any("request_data", requestData))
}

// LogAPIEnd 记录API调用结束
func (base *EnhancedAdapterBase) LogAPIEnd(
	customerOrderNo string,
	provider string,
	method string,
	responseData interface{},
	err error,
) {
	if err != nil {
		base.logger.Error("供应商API调用结束（失败）",
			zap.String("customer_order_no", customerOrderNo),
			zap.String("provider", provider),
			zap.String("method", method),
			zap.Any("response_data", responseData),
			zap.Error(err))
	} else {
		base.logger.Info("供应商API调用结束（成功）",
			zap.String("customer_order_no", customerOrderNo),
			zap.String("provider", provider),
			zap.String("method", method),
			zap.Any("response_data", responseData))
	}
}

// ExtractResponseMetadata 提取响应元数据
func (base *EnhancedAdapterBase) ExtractResponseMetadata(response interface{}) map[string]interface{} {
	metadata := make(map[string]interface{})

	// 尝试提取常见的响应元数据
	if responseMap, ok := response.(map[string]interface{}); ok {
		// 提取状态相关信息
		if success, exists := responseMap["success"]; exists {
			metadata["success"] = success
		}
		if code, exists := responseMap["code"]; exists {
			metadata["code"] = code
		}
		if message, exists := responseMap["message"]; exists {
			metadata["message"] = message
		}
		if resultCode, exists := responseMap["ResultCode"]; exists {
			metadata["result_code"] = resultCode
		}
		if reason, exists := responseMap["Reason"]; exists {
			metadata["reason"] = reason
		}
	}

	return metadata
}

// SanitizeResponseForLogging 清理响应数据用于日志记录
// 移除敏感信息，避免日志泄露
func (base *EnhancedAdapterBase) SanitizeResponseForLogging(response interface{}) interface{} {
	// 这里可以实现敏感信息过滤逻辑
	// 例如移除密钥、令牌等敏感字段
	return response
}
