package adapter

import (
	"context"
	"crypto/md5"
	"encoding/base64"
	"encoding/json"
	"encoding/xml"
	"fmt"
	"io"
	"math"
	"net/http"
	"net/url"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/your-org/go-kuaidi/internal/express"
	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/util"
	"go.uber.org/zap"
)

// CainiaoAdapter 菜鸟裹裹适配器
type CainiaoAdapter struct {
	config               CainiaoConfig
	client               *http.Client
	logger               *zap.Logger
	mappingService       express.ExpressMappingService
	expressCompanyRepo   express.ExpressCompanyRepository
	statusMappingService StatusMappingService
}

// StatusMappingService 状态映射服务接口
type StatusMappingService interface {
	MapProviderStatusToSystem(ctx context.Context, provider, providerStatus string) (model.SystemOrderStatus, error)
}

// CainiaoConfig 菜鸟裹裹配置
type CainiaoConfig struct {
	AccessCode         string `json:"access_code"`          // 接入编码
	LogisticProviderID string `json:"logistic_provider_id"` // 资源code
	CPCode             string `json:"cp_code"`              // CP编号
	SecretKey          string `json:"secret_key"`           // 密钥
	MsgTypePrefix      string `json:"msg_type_prefix"`      // 消息类型前缀
	BaseURL            string `json:"base_url"`             // API基础URL
	Environment        string `json:"environment"`          // 环境(sandbox/production)
	Timeout            int    `json:"timeout"`              // 超时时间(秒)
}

// NewCainiaoAdapter 创建菜鸟裹裹适配器
func NewCainiaoAdapter(config CainiaoConfig, expressCompanyRepo express.ExpressCompanyRepository) *CainiaoAdapter {
	// 创建日志记录器
	logger, _ := zap.NewProduction()

	// 优化HTTP客户端配置 - 提升性能
	transport := &http.Transport{
		MaxIdleConns:          200,              // 增加最大空闲连接数
		MaxIdleConnsPerHost:   50,               // 增加每个主机最大空闲连接数
		IdleConnTimeout:       60 * time.Second, // 减少空闲连接超时
		DisableCompression:    false,            // 启用压缩
		ForceAttemptHTTP2:     true,             // 强制尝试HTTP/2
		ResponseHeaderTimeout: 8 * time.Second,  // 响应头超时
		ExpectContinueTimeout: 1 * time.Second,  // Expect: 100-continue超时
	}

	client := &http.Client{
		Transport: transport,
		Timeout:   time.Duration(config.Timeout) * time.Second,
	}

	return &CainiaoAdapter{
		config:             config,
		client:             client,
		logger:             logger,
		expressCompanyRepo: expressCompanyRepo,
	}
}

// SetMappingService 设置快递映射服务
func (a *CainiaoAdapter) SetMappingService(service express.ExpressMappingService) {
	a.mappingService = service
}

// SetStatusMappingService 设置状态映射服务
func (a *CainiaoAdapter) SetStatusMappingService(service StatusMappingService) {
	a.statusMappingService = service
}

// Name 返回适配器名称
func (a *CainiaoAdapter) Name() string {
	return "cainiao"
}

// QueryPrice 查询价格 - 实现ProviderAdapter接口
func (a *CainiaoAdapter) QueryPrice(ctx context.Context, req *model.PriceRequest) ([]model.StandardizedPrice, error) {
	a.logger.Info("[DEBUG] 菜鸟裹裹开始查询价格",
		zap.Any("req", req),
	)
	defer func() {
		a.logger.Info("[DEBUG] 菜鸟裹裹查价结束")
	}()

	// 🚀 简化：如果指定了快递公司，先验证是否支持和启用
	if req.ExpressType != "" {
		// 使用映射服务检查快递公司启用状态（映射服务内部统一管理缓存）
		if a.mappingService != nil {
			_, err := a.mappingService.GetProviderCompanyCode(ctx, req.ExpressType, "cainiao")
			if err != nil {
				a.logger.Info("菜鸟不支持该快递公司或快递公司已禁用，跳过查询",
					zap.String("express_type", req.ExpressType),
					zap.Error(err))
				return []model.StandardizedPrice{}, nil
			}
		}
	}

	// 调用服务预查询接口
	return a.queryServiceDetail(ctx, req)
}

// CreateOrder 创建订单 - 实现ProviderAdapter接口
func (a *CainiaoAdapter) CreateOrder(ctx context.Context, req *model.OrderRequest) (*model.OrderResult, error) {
	a.logger.Info("菜鸟裹裹开始创建订单",
		zap.String("customer_order_no", req.CustomerOrderNo))

	// 🚀 简化：检查快递公司启用状态
	if a.mappingService != nil {
		_, err := a.mappingService.GetProviderCompanyCode(ctx, req.ExpressType, "cainiao")
		if err != nil {
			a.logger.Error("菜鸟不支持该快递公司或快递公司已禁用，无法创建订单",
				zap.String("express_type", req.ExpressType),
				zap.String("customer_order_no", req.CustomerOrderNo),
				zap.Error(err))
			return nil, fmt.Errorf("快递公司 %s 已禁用或不支持，无法创建订单", req.ExpressType)
		}
	}

	// 调用创建订单接口
	return a.createSendOrder(ctx, req)
}

// CancelOrder 取消订单 - 实现ProviderAdapter接口
func (a *CainiaoAdapter) CancelOrder(ctx context.Context, taskId string, orderNo string, reason string) error {
	a.logger.Info("菜鸟裹裹开始取消订单",
		zap.String("task_id", taskId),
		zap.String("order_no", orderNo),
		zap.String("reason", reason))

	// 调用取消订单接口
	return a.cancelSendOrder(ctx, orderNo, reason)
}

// CancelOrderWithUserInfo 取消订单（带用户信息） - 菜鸟专用方法
func (a *CainiaoAdapter) CancelOrderWithUserInfo(ctx context.Context, taskId string, orderNo string, reason string, userMobile string) error {
	a.logger.Info("菜鸟裹裹开始取消订单（带用户信息）",
		zap.String("task_id", taskId),
		zap.String("order_no", orderNo),
		zap.String("reason", reason),
		zap.String("user_mobile", userMobile))

	// 调用带用户信息的取消订单接口
	return a.cancelSendOrderWithUserInfo(ctx, orderNo, reason, userMobile)
}

// QueryOrder 查询订单 - 实现ProviderAdapter接口
func (a *CainiaoAdapter) QueryOrder(ctx context.Context, orderNo string, trackingNo string) (*model.OrderInfo, error) {
	a.logger.Info("菜鸟裹裹开始查询订单",
		zap.String("order_no", orderNo),
		zap.String("tracking_no", trackingNo))

	// 调用查询订单详情接口
	return a.queryOrderDetail(ctx, orderNo)
}

// QueryTrack 查询物流轨迹 - 实现ProviderAdapter接口
func (a *CainiaoAdapter) QueryTrack(ctx context.Context, trackingNo, expressType, phone, pollToken string, from, to string) (*model.TrackInfo, error) {
	a.logger.Info("菜鸟裹裹开始查询物流轨迹",
		zap.String("tracking_no", trackingNo),
		zap.String("express_type", expressType))

	// 🔥 修复：菜鸟物流轨迹查询需要使用菜鸟订单ID
	// 但由于适配器无法直接访问数据库，我们需要在上层处理
	// 这里先返回一个明确的错误信息，指导如何正确使用
	return nil, fmt.Errorf("菜鸟物流轨迹查询需要菜鸟订单ID，请使用QueryTrackByOrderId方法或在统一网关层面处理")
}

// QueryTrackByOrderId 根据菜鸟订单ID查询物流轨迹（新增方法）
func (a *CainiaoAdapter) QueryTrackByOrderId(ctx context.Context, cainiaoOrderId, trackingNo string) (*model.TrackInfo, error) {
	a.logger.Info("菜鸟裹裹开始查询物流轨迹（按订单ID）",
		zap.String("cainiao_order_id", cainiaoOrderId),
		zap.String("tracking_no", trackingNo))

	// 调用查询物流详情接口
	return a.queryLogisticsDetailByOrderId(ctx, cainiaoOrderId, trackingNo)
}

// ==================== 内部方法 ====================

// callAPI 调用菜鸟裹裹API的通用方法
func (a *CainiaoAdapter) callAPI(ctx context.Context, msgType string, data interface{}) ([]byte, error) {
	// 序列化请求数据
	dataJSON, err := json.Marshal(data)
	if err != nil {
		return nil, fmt.Errorf("序列化请求数据失败: %w", err)
	}

	// 构建公共请求参数
	timestamp := strconv.FormatInt(time.Now().UnixMilli(), 10)

	// 按照Python版本：data_digest是对logistics_interface内容的签名
	dataDigest := a.generateDataDigest(string(dataJSON))

	params := map[string]string{
		"logistics_interface":  string(dataJSON),
		"msg_type":             msgType,
		"logistic_provider_id": a.config.LogisticProviderID,
		"data_digest":          dataDigest,
		"timestamp":            timestamp,
		"fromCode":             "916020", // 按照Python版本添加
	}

	// 生成签名
	sign := a.generateSign(params)
	params["sign"] = sign

	// 构建请求体
	formData := url.Values{}
	for key, value := range params {
		formData.Set(key, value)
	}

	// 调试：打印完整的请求体
	a.logger.Info("[DEBUG] 完整请求体", zap.String("form_data", formData.Encode()))

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, "POST", a.config.BaseURL, strings.NewReader(formData.Encode()))
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("User-Agent", "Go-Kuaidi-Cainiao/1.0")

	// 发送请求
	resp, err := a.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("HTTP请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	a.logger.Debug("菜鸟裹裹API调用成功",
		zap.String("msg_type", msgType),
		zap.String("response", string(body)))

	return body, nil
}

// generateDataDigest 生成数据摘要（按照Python版本的doSign函数）
func (a *CainiaoAdapter) generateDataDigest(data string) string {
	// 按照Python版本：content + secret_key，然后MD5+Base64
	content := data + a.config.SecretKey
	hash := md5.Sum([]byte(content))
	return base64.StdEncoding.EncodeToString(hash[:])
}

// generateSign 生成签名（按照Python版本的算法）
func (a *CainiaoAdapter) generateSign(params map[string]string) string {
	// 1. 排除data_digest参数（按照Python版本）
	signParams := make(map[string]string)
	for key, value := range params {
		if key != "data_digest" && key != "sign" {
			signParams[key] = value
		}
	}

	// 2. 按key排序
	var keys []string
	for key := range signParams {
		keys = append(keys, key)
	}
	sort.Strings(keys)

	// 3. 按照Python版本：key + value 拼接（不是key=value&格式）
	var paramStr strings.Builder
	for _, key := range keys {
		value := signParams[key]
		if value != "" {
			paramStr.WriteString(key)
			paramStr.WriteString(value)
		}
	}

	// 4. 使用doSign函数：content + secret_key，然后MD5+Base64
	content := paramStr.String() + a.config.SecretKey
	hash := md5.Sum([]byte(content))
	signature := base64.StdEncoding.EncodeToString(hash[:])

	// 调试信息
	a.logger.Info("[DEBUG] 菜鸟签名参数", zap.Any("sign_params", signParams))
	a.logger.Info("[DEBUG] 菜鸟参数字符串", zap.String("param_str", paramStr.String()))
	a.logger.Info("[DEBUG] 菜鸟签名内容", zap.String("content", content))
	a.logger.Info("[DEBUG] 菜鸟生成签名", zap.String("signature", signature))

	return signature
}

// handleCainiaoError 处理菜鸟裹裹API错误
// 根据官方文档错误码提供用户友好的错误信息
func (a *CainiaoAdapter) handleCainiaoError(statusCode, statusMessage string) error {
	switch statusCode {
	// 参数校验异常
	case "2001":
		return fmt.Errorf("参数验证失败：必填参数为空，请检查externalUserId和externalUserMobile等必填字段")
	case "2304":
		return fmt.Errorf("外部用户ID无效：长度超长，请检查externalUserId字段长度")
	case "2301":
		return fmt.Errorf("寄件人信息无效：请检查寄件人姓名、手机号、地址等信息")
	case "2302":
		return fmt.Errorf("收件人信息无效：请检查收件人姓名、手机号、地址等信息")
	case "AREA_ID_INVALID":
		return fmt.Errorf("收件人区域ID不合法：必须大于0")
	case "DIVSION_IS_NULL":
		return fmt.Errorf("行政区为空：请提供完整的省市区信息")
	case "PARAM_INVALID":
		return fmt.Errorf("参数无效：收件人姓名不能超过32个字符")

	// 账号异常
	case "3208":
		return fmt.Errorf("创建菜鸟账号失败：请确保手机号已实名认证")
	case "3209":
		return fmt.Errorf("访问编码accessCode无效：请检查配置中的accessCode是否正确")
	case "3212":
		return fmt.Errorf("查询菜鸟账号不存在：用户账号未找到")
	case "3301":
		return fmt.Errorf("快递员状态错误：当前快递员无法提供服务")
	case "FAILED_IN_BLACK_LIST":
		return fmt.Errorf("账号行为异常：账号已被冻结，请联系客服")

	// 授权异常
	case "S23":
		return fmt.Errorf("菜鸟裹裹应用授权失败：请检查logistic_provider_id配置或联系菜鸟技术支持进行应用授权")

	// 业务异常
	case "3205":
		return fmt.Errorf("该地区尚未完成服务准备：暂不支持该地区的配送服务")
	case "4017":
		return fmt.Errorf("预约时间不合法：请选择有效的预约时间段")
	case "5002":
		return fmt.Errorf("城市未开放：该城市暂不支持菜鸟裹裹服务")
	case "5003":
		return fmt.Errorf("运营区未开放：该区域暂不支持菜鸟裹裹服务")
	case "5007":
		return fmt.Errorf("超过下单截止时间：当日17点后无法下单，请明日再试")
	case "5009":
		return fmt.Errorf("快递员已约满：当前时段快递员已约满，请选择其他时段")
	case "5010":
		return fmt.Errorf("预约容量已满：当前时段预约已满，请选择其他时段")
	case "5011":
		return fmt.Errorf("当前不支持下单：请稍后重试或联系客服")

	// 默认错误处理
	default:
		// 🚀 智能识别"供应商不支持"错误（参考易达实现）
		if a.isProviderNotSupportedError(statusCode, statusMessage) {
			return &model.ProviderNotSupportedError{
				Provider: "cainiao",
				Message:  fmt.Sprintf("菜鸟裹裹不支持此服务: %s", statusMessage),
			}
		}

		if statusMessage != "" {
			return fmt.Errorf("菜鸟裹裹服务异常 [%s]: %s", statusCode, statusMessage)
		}
		return fmt.Errorf("菜鸟裹裹服务异常，错误码: %s", statusCode)
	}
}

// getServiceType 获取菜鸟裹裹服务类型（固定为当日取件）
func (a *CainiaoAdapter) getServiceType(weight float64, servicePreference string) string {
	// 只支持当日取件服务
	return "**********" // 当日取件
}

// convertExpressCode 转换快递公司代码（标准代码 -> 菜鸟代码）
func (a *CainiaoAdapter) convertExpressCode(standardCode string) string {
	ctx := context.Background()

	// 使用数据库映射服务获取快递公司代码映射
	if a.mappingService != nil {
		if mappedCode, err := a.mappingService.GetProviderCompanyCode(ctx, standardCode, "cainiao"); err == nil {
			a.logger.Debug("菜鸟快递代码映射成功",
				zap.String("provider", "cainiao"),
				zap.String("standard_code", standardCode),
				zap.String("provider_code", mappedCode))
			return mappedCode
		} else {
			// 映射失败时使用静态回退映射
			a.logger.Warn("菜鸟供应商快递代码映射失败，使用静态回退映射",
				zap.String("standard_code", standardCode),
				zap.String("provider", "cainiao"),
				zap.Error(err))

			// 静态回退映射表（菜鸟API标准代码）- 根据官方文档更新
			fallbackMapping := map[string]string{
				"YD":      "YUNDA",      // 韵达快递
				"ZTO":     "ZTO",        // 中通快递
				"STO":     "STO",        // 申通快递
				"YTO":     "YTO",        // 圆通快递
				"JT":      "HTKY",       // 极兔快递（百世快递）
				"JD":      "LE04284890", // 京东快递
				"DBL":     "DBKD",       // 德邦快递
				"SF":      "SF",         // 顺丰快递
				"EMS":     "EMS",        // 邮政快递
				"CAINIAO": "NORMAL",     // 🔧 修复：CAINIAO（菜鸟裹裹）映射为NORMAL
			}

			if mappedCode, exists := fallbackMapping[standardCode]; exists {
				a.logger.Info("使用静态回退映射",
					zap.String("standard_code", standardCode),
					zap.String("mapped_code", mappedCode),
					zap.String("provider", "cainiao"))
				return mappedCode
			}

			// 如果静态映射也没有，返回原始代码
			a.logger.Warn("静态回退映射也未找到，使用原始代码",
				zap.String("express_code", standardCode))
			return standardCode
		}
	}

	// 如果映射服务不可用，使用静态映射
	a.logger.Warn("映射服务不可用，使用静态快递代码映射",
		zap.String("express_code", standardCode))

	// 静态回退映射表 - 根据官方文档更新
	fallbackMapping := map[string]string{
		"YD":      "YUNDA",      // 韵达快递
		"ZTO":     "ZTO",        // 中通快递
		"STO":     "STO",        // 申通快递
		"YTO":     "YTO",        // 圆通快递
		"JT":      "HTKY",       // 极兔快递（百世快递）
		"JD":      "LE04284890", // 京东快递
		"DBL":     "DBKD",       // 德邦快递
		"SF":      "SF",         // 顺丰快递
		"EMS":     "EMS",        // 邮政快递
		"CAINIAO": "NORMAL",     // 🔧 修复：CAINIAO（菜鸟裹裹）映射为NORMAL
	}

	if mappedCode, exists := fallbackMapping[standardCode]; exists {
		return mappedCode
	}

	// 最后的回退：返回原始代码
	return standardCode
}

// convertProviderCodeToStandard 将菜鸟供应商代码转换为标准代码
func (a *CainiaoAdapter) convertProviderCodeToStandard(providerCode string) string {
	ctx := context.Background()

	// 使用数据库映射服务进行反向查询
	if a.mappingService != nil {
		if standardCode, err := a.mappingService.GetStandardCompanyCode(ctx, providerCode, "cainiao"); err == nil {
			a.logger.Debug("菜鸟供应商代码反向映射成功",
				zap.String("provider", "cainiao"),
				zap.String("provider_code", providerCode),
				zap.String("standard_code", standardCode))
			return standardCode
		} else {
			a.logger.Warn("菜鸟供应商代码反向映射失败，使用静态回退映射",
				zap.String("provider_code", providerCode),
				zap.String("provider", "cainiao"),
				zap.Error(err))
		}
	}

	// 静态反向映射表 - 根据官方文档更新
	reverseMapping := map[string]string{
		"YUNDA":      "YD",      // 韵达快递
		"ZTO":        "ZTO",     // 中通快递
		"STO":        "STO",     // 申通快递
		"YTO":        "YTO",     // 圆通快递
		"HTKY":       "JT",      // 极兔快递（百世快递）
		"LE04284890": "JD",      // 京东快递
		"DBKD":       "DBL",     // 德邦快递
		"SF":         "SF",      // 顺丰快递
		"EMS":        "EMS",     // 邮政快递
		"NORMAL":     "CAINIAO", // 🔧 修复：NORMAL映射为CAINIAO（菜鸟裹裹）
	}

	if standardCode, exists := reverseMapping[providerCode]; exists {
		a.logger.Debug("使用静态反向映射",
			zap.String("provider_code", providerCode),
			zap.String("standard_code", standardCode),
			zap.String("provider", "cainiao"))
		return standardCode
	}

	// 最后的回退：返回原始代码
	a.logger.Warn("未找到菜鸟代码的反向映射，使用原始代码",
		zap.String("provider_code", providerCode))
	return providerCode
}

// ==================== API实现方法 ====================

// queryServiceDetail 查询服务详情（价格查询）
func (a *CainiaoAdapter) queryServiceDetail(ctx context.Context, req *model.PriceRequest) ([]model.StandardizedPrice, error) {
	a.logger.Info("[DEBUG] queryServiceDetail 入参", zap.Any("req", req))
	// 参数验证
	if err := a.validatePriceRequest(req); err != nil {
		a.logger.Error("[DEBUG] 参数验证失败", zap.Error(err))
		return nil, fmt.Errorf("菜鸟裹裹价格查询参数验证失败: %w", err)
	}

	// 🚀 智能处理缺失字段，提供默认值（参考易达实现）
	a.enrichPriceRequestWithDefaults(req)
	a.logger.Info("[DEBUG] enrichPriceRequestWithDefaults 后", zap.Any("req", req))

	// 获取服务类型（固定为当日取件）
	serviceType := a.getServiceType(req.Package.Weight, req.ProductType)
	a.logger.Info("[DEBUG] getServiceType", zap.String("serviceType", serviceType))

	// 🚀 智能处理电话号码格式
	senderMobile, _ := a.formatPhoneNumbers(req.Sender.Mobile, req.Sender.Tel)
	receiverMobile, _ := a.formatPhoneNumbers(req.Receiver.Mobile, req.Receiver.Tel)

	// 构建请求参数（按照官方文档格式）
	requestData := map[string]interface{}{
		"accessOption": map[string]interface{}{
			"accessCode": a.config.AccessCode,
		},
		"queryCondition": map[string]interface{}{
			"externalUserId":     "test_user_" + strconv.FormatInt(time.Now().Unix(), 10), // 外部用户ID（必填）
			"externalUserMobile": senderMobile,                                            // 外部用户手机号（必填）
			"itemCodeList":       []string{serviceType},                                   // 寄件服务类型code列表
			"senderInfo": map[string]interface{}{
				"name":              a.getValidName(req.Sender.Name, "寄件人"),
				"mobile":            senderMobile,
				"fullAddressDetail": a.getFullAddress(req.Sender.Province, req.Sender.City, req.Sender.District, req.Sender.Address),
				"provinceName":      req.Sender.Province,
				"cityName":          req.Sender.City,
				"areaName":          req.Sender.District,
				"address":           req.Sender.Address,
			},
			"receiverInfo": map[string]interface{}{
				"name":              a.getValidName(req.Receiver.Name, "收件人"),
				"mobile":            receiverMobile,
				"fullAddressDetail": a.getFullAddress(req.Receiver.Province, req.Receiver.City, req.Receiver.District, req.Receiver.Address),
				"provinceName":      req.Receiver.Province,
				"cityName":          req.Receiver.City,
				"areaName":          req.Receiver.District,
				"address":           req.Receiver.Address,
			},
			"isDesignatedProviderId": false, // 是否指定快递公司
		},
	}
	a.logger.Info("[DEBUG] 菜鸟查价请求参数", zap.Any("requestData", requestData))

	// 调用API
	// 按照Python版本使用正确的消息类型
	msgType := "GUOGUO_QUERY_SEND_SERVICE_DETAIL"
	a.logger.Info("[DEBUG] 调用callAPI", zap.String("msgType", msgType))
	respData, err := a.callAPI(ctx, msgType, requestData)
	if err != nil {
		a.logger.Error("[DEBUG] 菜鸟裹裹服务查询API调用失败",
			zap.Error(err),
			zap.String("msg_type", msgType))
		return nil, fmt.Errorf("调用菜鸟裹裹服务查询API失败: %w", err)
	}
	a.logger.Info("[DEBUG] callAPI返回", zap.ByteString("respData", respData))

	// 解析响应（支持JSON和XML格式）
	var response CainiaoServiceDetailResponse

	// 检查响应格式
	respStr := string(respData)
	if strings.HasPrefix(strings.TrimSpace(respStr), "<") {
		// XML格式响应
		err = xml.Unmarshal(respData, &response)
	} else {
		// JSON格式响应
		err = json.Unmarshal(respData, &response)
	}

	if err != nil {
		a.logger.Error("[DEBUG] 菜鸟裹裹服务查询响应解析失败",
			zap.Error(err),
			zap.String("response", string(respData)))
		return nil, fmt.Errorf("解析菜鸟裹裹服务查询响应失败: %w", err)
	}

	// 检查响应状态（支持不同格式）
	var success bool
	var statusCode, statusMessage string

	// 检查success字段（菜鸟API返回字符串"true"/"false"）
	success = response.Success || response.Result.Success == "true"

	if response.ErrorCode != "" {
		statusCode = response.ErrorCode
		statusMessage = response.ErrorMsg
	} else {
		statusCode = response.Result.StatusCode
		statusMessage = response.Result.StatusMessage
	}

	if !success {
		a.logger.Warn("[DEBUG] 菜鸟裹裹服务查询业务失败",
			zap.String("status_code", statusCode),
			zap.String("status_message", statusMessage))
		return nil, a.handleCainiaoError(statusCode, statusMessage)
	}

	// 转换为标准价格格式
	prices := a.convertToStandardPrices(response.Result.Data, req)

	a.logger.Info("[DEBUG] 菜鸟裹裹价格查询成功",
		zap.Int("price_count", len(prices)),
		zap.String("service_type", serviceType),
		zap.Any("prices", prices))

	return prices, nil
}

// createSendOrder 创建寄件订单
func (a *CainiaoAdapter) createSendOrder(ctx context.Context, req *model.OrderRequest) (*model.OrderResult, error) {
	// 🚀 记录详细的请求信息（参考易达实现）
	a.logger.Info("菜鸟裹裹创建订单开始",
		zap.String("customer_order_no", req.CustomerOrderNo),
		zap.String("express_type", req.ExpressType),
		zap.String("product_type", req.ProductType),
		zap.Float64("weight", req.Package.Weight),
		zap.String("provider", "cainiao"))

	// 🔥 新增：记录原始传参
	a.logger.Info("🔍 菜鸟创建订单 - 原始参数",
		zap.String("customer_order_no", req.CustomerOrderNo),
		zap.String("original_express_type", req.ExpressType),
		zap.String("product_type", req.ProductType),
		zap.String("channel_id", req.ChannelID))

	// 参数验证
	if err := a.validateOrderRequest(req); err != nil {
		a.logger.Error("菜鸟裹裹订单创建参数验证失败",
			zap.String("customer_order_no", req.CustomerOrderNo),
			zap.Error(err))
		return nil, fmt.Errorf("菜鸟裹裹订单创建参数验证失败: %w", err)
	}

	// 🔥 关键修复：转换快递代码为菜鸟API支持的格式
	originalExpressType := req.ExpressType
	mappedExpressType := a.convertExpressCode(req.ExpressType)

	// 🔥 新增：记录快递代码映射过程
	a.logger.Info("🔍 菜鸟创建订单 - 快递代码映射",
		zap.String("customer_order_no", req.CustomerOrderNo),
		zap.String("original_express_type", originalExpressType),
		zap.String("mapped_express_type", mappedExpressType),
		zap.Bool("mapping_changed", mappedExpressType != originalExpressType))

	if mappedExpressType != originalExpressType {
		a.logger.Info("菜鸟订单创建 - 快递代码已映射",
			zap.String("original_code", originalExpressType),
			zap.String("mapped_code", mappedExpressType),
			zap.String("customer_order_no", req.CustomerOrderNo))
		req.ExpressType = mappedExpressType
	}

	// 🚀 智能处理缺失字段，提供默认值（参考易达实现）
	a.enrichOrderRequestWithDefaults(req)

	// 获取服务类型和计算取件时间
	serviceType := a.getServiceType(req.Package.Weight, req.ProductType)

	// 🔥 智能判断下单类型：实时单还是预约单
	timeType, appointGotStartTime, appointGotEndTime := a.determineOrderTimeType(req)

	// 🚀 智能处理电话号码格式（参考易达实现）
	senderMobile, _ := a.formatPhoneNumbers(req.Sender.Mobile, req.Sender.Tel)
	receiverMobile, _ := a.formatPhoneNumbers(req.Receiver.Mobile, req.Receiver.Tel)

	// 构建完整地址信息
	senderFullAddress := a.buildFullAddress(req.Sender.Province, req.Sender.City, req.Sender.District, req.Sender.Address)
	receiverFullAddress := a.buildFullAddress(req.Receiver.Province, req.Receiver.City, req.Receiver.District, req.Receiver.Address)

	// 按照Python版本构建请求数据结构
	orderInfo := map[string]interface{}{
		"accessOption": map[string]interface{}{
			"accessCode": a.config.AccessCode,
		},
		"request": map[string]interface{}{
			"externalUserId":     a.getExternalUserId(req),
			"externalUserMobile": a.getExternalUserMobile(req),
			"cnAccountId":        a.getCnAccountId(req), // 添加cnAccountId字段
			"itemId":             a.parseItemId(serviceType),
			"itemVersion":        1,
			"timeType":           timeType, // 🔥 动态设置：1-实时单，2-预约单
			"userRemark":         a.getUserRemark(req),
			"outOrderInfoList":   a.buildOutOrderInfoList(req),
			"senderInfo": map[string]interface{}{
				"name":              a.getValidName(req.Sender.Name, "寄件人"),
				"mobile":            senderMobile,
				"fullAddressDetail": senderFullAddress,
				"provinceName":      req.Sender.Province,
				"cityName":          req.Sender.City,
				"areaName":          req.Sender.District,
				"address":           req.Sender.Address,
			},
			"receiverInfo": map[string]interface{}{
				"name":              a.getValidName(req.Receiver.Name, "收件人"),
				"mobile":            receiverMobile,
				"fullAddressDetail": receiverFullAddress,
				"provinceName":      req.Receiver.Province,
				"cityName":          req.Receiver.City,
				"areaName":          req.Receiver.District,
				"address":           req.Receiver.Address,
			},
			"extensionMap": map[string]interface{}{
				"canPrint": "false",
			},
		},
	}

	// 🔥 关键修复：如果用户选择了具体的快递公司，指定快递公司
	if req.ExpressType != "" && req.ExpressType != "NORMAL" {
		// �� 关键修复：使用官方文档正确的参数名和cpCode值
		// 根据官方文档附录3，直接使用cpCode表中的值，如：YUNDA、STO、ZTO等
		designatedProviderId := mappedExpressType
		orderInfo["request"].(map[string]interface{})["designatedProviderId"] = designatedProviderId

		// 🔥 新增：详细记录快递公司指定逻辑
		a.logger.Info("🔍 菜鸟创建订单 - 指定快递公司模式",
			zap.String("customer_order_no", req.CustomerOrderNo),
			zap.String("original_express_type", originalExpressType),
			zap.String("mapped_express_type", mappedExpressType),
			zap.String("designated_provider_id", designatedProviderId),
			zap.String("format", "直接使用cpCode表中的值"))
	} else {
		// 🔥 不指定快递公司：让菜鸟自动选择最优路线
		// 注意：不传 designatedProviderId 参数就是自动选择

		// 🔥 新增：详细记录自动选择逻辑
		a.logger.Info("🔍 菜鸟创建订单 - 自动选择快递公司模式",
			zap.String("customer_order_no", req.CustomerOrderNo),
			zap.String("reason", "express_type为空或为NORMAL"),
			zap.String("designated_provider_id", "未设置"))
	}

	// 🔥 根据timeType动态添加预约时间字段
	if timeType == 2 { // 预约单
		orderInfo["request"].(map[string]interface{})["appointGotStartTime"] = appointGotStartTime
		orderInfo["request"].(map[string]interface{})["appointGotEndTime"] = appointGotEndTime
	}

	// 🔥 新增：记录完整的请求体（去除敏感信息）
	orderInfoCopy := make(map[string]interface{})
	for k, v := range orderInfo {
		if k == "accessOption" {
			orderInfoCopy[k] = map[string]interface{}{
				"accessCode": "***", // 脱敏处理
			}
		} else {
			orderInfoCopy[k] = v
		}
	}
	a.logger.Info("🔍 菜鸟创建订单 - 完整请求体",
		zap.String("customer_order_no", req.CustomerOrderNo),
		zap.Any("order_info", orderInfoCopy))

	a.logger.Info("菜鸟裹裹开始创建订单",
		zap.String("customer_order_no", req.CustomerOrderNo),
		zap.String("service_type", serviceType),
		zap.Float64("weight", req.Package.Weight),
		zap.Int("time_type", timeType),
		zap.String("pickup_time", fmt.Sprintf("%s - %s", appointGotStartTime, appointGotEndTime)))

	// 调用API - 按照Python版本使用正确的消息类型
	msgType := "GUOGUO_CREATE_SEND_ORDER"

	// 🔥 新增：记录API调用开始
	a.logger.Info("🔍 菜鸟创建订单 - API调用开始",
		zap.String("customer_order_no", req.CustomerOrderNo),
		zap.String("msg_type", msgType),
		zap.String("api_url", a.config.BaseURL))

	respData, err := a.callAPI(ctx, msgType, orderInfo)
	if err != nil {
		// 🔥 新增：记录API调用失败详情
		a.logger.Error("🔍 菜鸟创建订单 - API调用失败",
			zap.String("customer_order_no", req.CustomerOrderNo),
			zap.String("msg_type", msgType),
			zap.Error(err),
			zap.Any("request_data", orderInfoCopy))

		a.logger.Error("菜鸟裹裹创建订单API调用失败",
			zap.Error(err),
			zap.String("customer_order_no", req.CustomerOrderNo),
			zap.String("msg_type", msgType))
		return nil, fmt.Errorf("调用菜鸟裹裹创建订单API失败: %w", err)
	}

	// 🔥 新增：记录API调用成功和响应
	a.logger.Info("🔍 菜鸟创建订单 - API调用成功",
		zap.String("customer_order_no", req.CustomerOrderNo),
		zap.String("msg_type", msgType),
		zap.String("response_data", string(respData)))

	// 解析响应
	var response CainiaoCreateOrderResponse
	if err := json.Unmarshal(respData, &response); err != nil {
		a.logger.Error("菜鸟裹裹创建订单响应解析失败",
			zap.Error(err),
			zap.String("customer_order_no", req.CustomerOrderNo),
			zap.String("response", string(respData)))
		return nil, fmt.Errorf("解析菜鸟裹裹创建订单响应失败: %w", err)
	}

	// 🔥 新增：记录解析后的响应详情
	a.logger.Info("🔍 菜鸟创建订单 - 响应解析结果",
		zap.String("customer_order_no", req.CustomerOrderNo),
		zap.String("success", response.Result.Success),
		zap.String("status_code", response.Result.StatusCode),
		zap.String("status_message", response.Result.StatusMessage),
		zap.Any("response_data", response.Result.Data))

	// 检查响应状态（菜鸟API返回字符串"true"/"false"）
	if response.Result.Success != "true" {
		a.logger.Error("菜鸟裹裹创建订单业务失败",
			zap.String("customer_order_no", req.CustomerOrderNo),
			zap.String("status_code", response.Result.StatusCode),
			zap.String("status_message", response.Result.StatusMessage))
		return nil, a.handleCainiaoError(response.Result.StatusCode, response.Result.StatusMessage)
	}

	// 转换为标准订单结果
	result := a.convertToOrderResult(response.Result.Data)

	// 设置平台订单号和客户订单号
	result.PlatformOrderNo = req.CustomerOrderNo // 这里应该是系统生成的平台订单号
	result.CustomerOrderNo = req.CustomerOrderNo

	// 🔥 新增：记录最终订单结果
	a.logger.Info("🔍 菜鸟创建订单 - 最终结果",
		zap.String("customer_order_no", req.CustomerOrderNo),
		zap.String("order_no", result.OrderNo),
		zap.String("tracking_no", result.TrackingNo),
		zap.String("pickup_code", result.PickupCode),
		zap.Float64("price", result.Price),
		zap.String("platform_order_no", result.PlatformOrderNo))

	a.logger.Info("菜鸟裹裹订单创建成功",
		zap.String("customer_order_no", req.CustomerOrderNo),
		zap.String("order_no", result.OrderNo),
		zap.String("tracking_no", result.TrackingNo),
		zap.String("pickup_code", result.PickupCode),
		zap.Float64("price", result.Price))

	return result, nil
}

// cancelSendOrder 取消寄件订单
// 根据菜鸟裹裹官方文档：揽收后不可取消
func (a *CainiaoAdapter) cancelSendOrder(ctx context.Context, orderNo string, reason string) error {
	// 参数验证
	if orderNo == "" {
		return fmt.Errorf("订单号不能为空")
	}
	if reason == "" {
		reason = "用户取消" // 默认取消原因
	}

	// 🔥 修复：按照官方文档构建正确的嵌套参数结构
	cancelData := map[string]interface{}{
		"request": map[string]interface{}{
			"orderId":        orderNo,
			"externalUserId": "*************", // 固定的平台用户ID
			"cnAccountId":    "*************", // 用户对应的菜鸟账户ID，使用相同值
			"reasonCode":     "-1",            // 必填：取消原因code，固定填"-1"
			"operatorType":   1,               // 必填：取消人角色，1-用户取消
			"reasonDesc":     reason,          // 必填：取消原因描述
		},
		"accessOption": map[string]interface{}{
			"accessCode": a.config.AccessCode, // 接入编码
		},
	}

	a.logger.Info("菜鸟裹裹开始取消订单",
		zap.String("order_no", orderNo),
		zap.String("reason", reason))

	// 调用API
	// 按照Python版本使用正确的消息类型
	msgType := "GUOGUO_CANCEL_SEND_ORDER"
	respData, err := a.callAPI(ctx, msgType, cancelData)
	if err != nil {
		a.logger.Error("菜鸟裹裹取消订单API调用失败",
			zap.Error(err),
			zap.String("order_no", orderNo),
			zap.String("msg_type", msgType))
		return fmt.Errorf("调用菜鸟裹裹取消订单API失败: %w", err)
	}

	// 解析响应
	var response CainiaoBaseResponse
	if err := json.Unmarshal(respData, &response); err != nil {
		a.logger.Error("菜鸟裹裹取消订单响应解析失败",
			zap.Error(err),
			zap.String("order_no", orderNo),
			zap.String("response", string(respData)))
		return fmt.Errorf("解析菜鸟裹裹取消订单响应失败: %w", err)
	}

	// 检查响应状态（菜鸟API返回字符串"true"/"false"）
	if response.Result.Success != "true" {
		a.logger.Warn("菜鸟裹裹取消订单业务失败",
			zap.String("order_no", orderNo),
			zap.String("status_code", response.Result.StatusCode),
			zap.String("status_message", response.Result.StatusMessage))
		return a.handleCainiaoError(response.Result.StatusCode, response.Result.StatusMessage)
	}

	a.logger.Info("菜鸟裹裹订单取消成功",
		zap.String("order_no", orderNo),
		zap.String("reason", reason))

	return nil
}

// cancelSendOrderWithUserInfo 取消寄件订单（带用户信息）
func (a *CainiaoAdapter) cancelSendOrderWithUserInfo(ctx context.Context, orderNo string, reason string, userMobile string) error {
	// 参数验证
	if orderNo == "" {
		return fmt.Errorf("订单号不能为空")
	}
	if reason == "" {
		reason = "用户取消" // 默认取消原因
	}
	if userMobile == "" {
		return fmt.Errorf("用户手机号不能为空")
	}

	// 🔥 修复：按照官方文档构建正确的嵌套参数结构
	cancelData := map[string]interface{}{
		"request": map[string]interface{}{
			"orderId":        orderNo,
			"externalUserId": "*************", // 固定的平台用户ID
			"cnAccountId":    "*************", // 用户对应的菜鸟账户ID，使用相同值
			"reasonCode":     "-1",            // 必填：取消原因code，固定填"-1"
			"operatorType":   1,               // 必填：取消人角色，1-用户取消
			"reasonDesc":     reason,          // 必填：取消原因描述
		},
		"accessOption": map[string]interface{}{
			"accessCode": a.config.AccessCode, // 接入编码
		},
	}

	a.logger.Info("菜鸟裹裹开始取消订单（带用户信息）",
		zap.String("order_no", orderNo),
		zap.String("reason", reason),
		zap.String("user_mobile", userMobile))

	// 调用API
	msgType := "GUOGUO_CANCEL_SEND_ORDER"
	respData, err := a.callAPI(ctx, msgType, cancelData)
	if err != nil {
		a.logger.Error("菜鸟裹裹取消订单API调用失败",
			zap.Error(err),
			zap.String("order_no", orderNo),
			zap.String("msg_type", msgType))
		return fmt.Errorf("调用菜鸟裹裹取消订单API失败: %w", err)
	}

	// 解析响应
	var response CainiaoBaseResponse
	if err := json.Unmarshal(respData, &response); err != nil {
		a.logger.Error("菜鸟裹裹取消订单响应解析失败",
			zap.Error(err),
			zap.String("order_no", orderNo),
			zap.String("response", string(respData)))
		return fmt.Errorf("解析菜鸟裹裹取消订单响应失败: %w", err)
	}

	// 检查响应状态（菜鸟API返回字符串"true"/"false"）
	if response.Result.Success != "true" {
		a.logger.Warn("菜鸟裹裹取消订单业务失败",
			zap.String("order_no", orderNo),
			zap.String("status_code", response.Result.StatusCode),
			zap.String("status_message", response.Result.StatusMessage))
		return a.handleCainiaoError(response.Result.StatusCode, response.Result.StatusMessage)
	}

	a.logger.Info("菜鸟裹裹订单取消成功（带用户信息）",
		zap.String("order_no", orderNo),
		zap.String("reason", reason),
		zap.String("user_mobile", userMobile))

	return nil
}

// queryOrderDetail 查询订单详情
func (a *CainiaoAdapter) queryOrderDetail(ctx context.Context, orderNo string) (*model.OrderInfo, error) {
	// 参数验证
	if orderNo == "" {
		return nil, fmt.Errorf("订单号不能为空")
	}

	// 🔥 修复：构建查询请求，添加必填的externalUserId和externalUserMobile
	queryData := map[string]interface{}{
		"orderId":             orderNo,
		"accessCode":          a.config.AccessCode,
		"cpCode":              a.config.CPCode,
		"needLogisticsDetail": true,
		"externalUserId":      "*************", // 固定的平台用户ID
		"externalUserMobile":  "13800138000",   // 使用默认手机号，查询订单主要依赖orderId
	}

	a.logger.Debug("菜鸟裹裹开始查询订单详情",
		zap.String("order_no", orderNo))

	// 调用API
	// 按照Python版本使用正确的消息类型
	msgType := "GUOGUO_QUERY_SEND_ORDER_FULL_DETAIL"
	respData, err := a.callAPI(ctx, msgType, queryData)
	if err != nil {
		a.logger.Error("菜鸟裹裹查询订单API调用失败",
			zap.Error(err),
			zap.String("order_no", orderNo),
			zap.String("msg_type", msgType))
		return nil, fmt.Errorf("调用菜鸟裹裹查询订单API失败: %w", err)
	}

	// 解析响应
	var response CainiaoOrderDetailResponse
	if err := json.Unmarshal(respData, &response); err != nil {
		a.logger.Error("菜鸟裹裹查询订单响应解析失败",
			zap.Error(err),
			zap.String("order_no", orderNo),
			zap.String("response", string(respData)))
		return nil, fmt.Errorf("解析菜鸟裹裹查询订单响应失败: %w", err)
	}

	// 检查响应状态（菜鸟API返回字符串"true"/"false"）
	if response.Result.Success != "true" {
		a.logger.Warn("菜鸟裹裹查询订单业务失败",
			zap.String("order_no", orderNo),
			zap.String("status_code", response.Result.StatusCode),
			zap.String("status_message", response.Result.StatusMessage))
		return nil, a.handleCainiaoError(response.Result.StatusCode, response.Result.StatusMessage)
	}

	// 转换为标准订单信息
	orderInfo := a.convertToOrderInfo(response.Result.Data)

	a.logger.Debug("菜鸟裹裹订单查询成功",
		zap.String("order_no", orderNo),
		zap.String("tracking_no", orderInfo.TrackingNo),
		zap.String("status", orderInfo.Status))

	return orderInfo, nil
}

// ==================== 辅助方法 ====================

// validatePriceRequest 验证价格查询请求参数
// 根据菜鸟裹裹官方文档：寄件人和收件人的姓名和手机号可以不传，但地址必须传
func (a *CainiaoAdapter) validatePriceRequest(req *model.PriceRequest) error {
	if req == nil {
		return fmt.Errorf("请求参数不能为空")
	}

	// 验证寄件人信息 - 根据官方文档，姓名和手机号可选，但地址必传
	if req.Sender.Province == "" || req.Sender.City == "" {
		return fmt.Errorf("寄件人省市信息不能为空")
	}
	if req.Sender.Address == "" {
		return fmt.Errorf("寄件人详细地址不能为空")
	}

	// 验证收件人信息 - 根据官方文档，姓名和手机号可选，但地址必传
	if req.Receiver.Province == "" || req.Receiver.City == "" {
		return fmt.Errorf("收件人省市信息不能为空")
	}
	if req.Receiver.Address == "" {
		return fmt.Errorf("收件人详细地址不能为空")
	}

	// 验证包裹信息
	if req.Package.Weight <= 0 {
		return fmt.Errorf("包裹重量必须大于0")
	}
	if req.Package.Weight > 100 {
		return fmt.Errorf("包裹重量不能超过100kg")
	}
	// 物品名称也可以为空，系统会使用默认值

	return nil
}

// validateOrderRequest 验证订单创建请求参数
// 根据菜鸟裹裹官方文档要求进行验证
func (a *CainiaoAdapter) validateOrderRequest(req *model.OrderRequest) error {
	if req == nil {
		return fmt.Errorf("请求参数不能为空")
	}

	// 验证客户订单号
	if req.CustomerOrderNo == "" {
		return fmt.Errorf("客户订单号不能为空")
	}

	// 验证寄件人信息 - 地址必填，姓名和手机号可选
	if req.Sender.Province == "" || req.Sender.City == "" {
		return fmt.Errorf("寄件人省市信息不能为空")
	}
	if req.Sender.Address == "" {
		return fmt.Errorf("寄件人详细地址不能为空")
	}

	// 验证收件人信息 - 地址必填，姓名和手机号可选
	if req.Receiver.Province == "" || req.Receiver.City == "" {
		return fmt.Errorf("收件人省市信息不能为空")
	}
	if req.Receiver.Address == "" {
		return fmt.Errorf("收件人详细地址不能为空")
	}

	// 验证包裹信息
	if req.Package.Weight <= 0 {
		return fmt.Errorf("包裹重量必须大于0")
	}
	if req.Package.Weight > 100 {
		return fmt.Errorf("包裹重量不能超过100kg")
	}

	// 验证支付方式
	if req.PayMethod < 0 || req.PayMethod > 2 {
		return fmt.Errorf("支付方式无效，必须是0(寄付)、1(到付)或2(月结)")
	}

	// 验证菜鸟裹裹必填字段：externalUserId 和 externalUserMobile
	externalUserId := a.getExternalUserId(req)
	externalUserMobile := a.getExternalUserMobile(req)

	if externalUserId == "" {
		return fmt.Errorf("外部用户ID不能为空，请提供UserID或CustomerOrderNo")
	}
	if externalUserMobile == "" {
		return fmt.Errorf("外部用户手机号不能为空，请提供寄件人手机号")
	}

	return nil
}

// getExternalUserId 获取外部用户ID
func (a *CainiaoAdapter) getExternalUserId(req *model.OrderRequest) string {
	// externalUserId代表我们平台在菜鸟系统中的ID，必须固定
	// 这是我们在菜鸟系统中注册的平台用户ID
	return "*************"
}

// getExternalUserMobile 获取外部用户手机号
// 根据菜鸟裹裹官方文档，externalUserMobile是必填字段，必须使用用户提供的真实手机号
func (a *CainiaoAdapter) getExternalUserMobile(req *model.OrderRequest) string {
	// 优先使用寄件人手机号（用户下单时提供的真实手机号）
	if req.Sender.Mobile != "" {
		return req.Sender.Mobile
	}
	// 其次使用收件人手机号
	if req.Receiver.Mobile != "" {
		return req.Receiver.Mobile
	}
	// 如果都没有，返回空字符串，让验证逻辑处理
	return ""
}

// ==================== 数据转换方法 ====================

// convertToStandardPrices 转换为标准价格格式
func (a *CainiaoAdapter) convertToStandardPrices(data CainiaoServiceDetailData, req *model.PriceRequest) []model.StandardizedPrice {
	var prices []model.StandardizedPrice

	for _, item := range data.AvailableServiceItemList {
		// 处理每个快递公司的价格
		for _, billing := range item.BillingTemplateByCpList {
			// 🔥 修复：使用动态映射过滤替代硬编码列表
			standardCode := a.convertProviderCodeToStandard(billing.CpCode)

			// 🚀 简化：使用映射服务检查快递公司启用状态
			if a.mappingService != nil {
				ctx := context.Background()
				_, err := a.mappingService.GetProviderCompanyCode(ctx, standardCode, "cainiao")
				if err != nil {
					a.logger.Debug("菜鸟适配器：快递公司未启用或不支持，跳过",
						zap.String("cp_code", billing.CpCode),
						zap.String("standard_code", standardCode),
						zap.Error(err))
					continue
				}
			} else {
				// 🚀 备用：如果映射服务不可用，使用硬编码列表（保持向后兼容）
				allowedCpCodes := map[string]bool{
					"NORMAL": true, // 标准快递
					"YUNDA":  true, // 韵达快递
					"STO":    true, // 申通快递
				}

				if !allowedCpCodes[billing.CpCode] {
					a.logger.Debug("菜鸟适配器：跳过不支持的快递公司（使用静态列表）",
						zap.String("cp_code", billing.CpCode))
					continue
				}
			}
			// 生成唯一的订单代码，用于下单时验证
			orderCode := fmt.Sprintf("CAINIAO_%s_%s_%d", item.Code, billing.CpCode, time.Now().Unix())

			// 🚀 计算计费重量（考虑体积重量和抛比）
			calcWeight := a.calculateChargedWeight(req, a.convertProviderCodeToStandard(billing.CpCode))

			// 解析价格（菜鸟API返回的是分为单位）
			startPriceYuan := float64(0)
			continuedPriceYuan := float64(0)
			if billing.StartPrice != "" {
				if val, err := strconv.Atoi(billing.StartPrice); err == nil {
					startPriceYuan = float64(val) / 100 // 转换为元
				}
			}
			if billing.ContinuedHeavyPrice != "" {
				if val, err := strconv.Atoi(billing.ContinuedHeavyPrice); err == nil {
					continuedPriceYuan = float64(val) / 100 // 转换为元
				}
			}

			// 🔥 关键修复：根据计费重量计算总价
			// 菜鸟API返回的是单价，需要根据重量计算总价
			// 计算公式：首重价格 + (计费重量 - 首重) * 续重价格
			var totalPrice float64
			startWeight := 1.0 // 菜鸟首重通常是1kg
			if billing.StartWeight != "" {
				if val, err := strconv.Atoi(billing.StartWeight); err == nil {
					startWeight = float64(val) / 1000 // 转换为kg（菜鸟API返回的是克）
				}
			}

			if calcWeight <= startWeight {
				// 如果计费重量小于等于首重，只收首重价格
				totalPrice = startPriceYuan
			} else {
				// 计费重量超过首重，需要加上续重费用
				extraWeight := calcWeight - startWeight
				totalPrice = startPriceYuan + extraWeight*continuedPriceYuan
			}

			// 🔥 修复浮点数精度问题：保留2位小数
			totalPrice = a.formatPrice(totalPrice)

			a.logger.Debug("菜鸟价格计算详情",
				zap.String("cp_code", billing.CpCode),
				zap.Float64("calc_weight", calcWeight),
				zap.Float64("start_weight", startWeight),
				zap.Float64("start_price_yuan", startPriceYuan),
				zap.Float64("continued_price_yuan", continuedPriceYuan),
				zap.Float64("total_price", totalPrice))

			// 🔥 关键修复：将菜鸟API返回的快递代码转换为系统标准代码
			standardExpressCode := a.convertProviderCodeToStandard(billing.CpCode)

			// 创建标准价格结构
			price := model.StandardizedPrice{
				ExpressCode:          standardExpressCode, // 使用转换后的标准代码
				ExpressName:          a.getCpName(billing.CpCode),
				ProductCode:          item.Code,
				ProductName:          item.Title,
				Price:                totalPrice,                        // 🚀 使用计算出的总价（已格式化）
				ContinuedWeightPerKg: a.formatPrice(continuedPriceYuan), // 🚀 使用续重单价（格式化精度）
				CalcWeight:           calcWeight,                        // 🚀 使用计算出的计费重量
				Provider:             a.Name(),
				ChannelID:            fmt.Sprintf("%s_%s", a.Name(), billing.CpCode),
				OrderCode:            orderCode,
			}

			// 设置价格有效期（菜鸟裹裹价格通常有效期较短）
			expiresAt := time.Now().Add(30 * time.Minute) // 30分钟有效期
			price.ExpiresAt = expiresAt.Format("2006-01-02 15:04:05")

			// 只支持当日取件服务
			price.EstimatedDays = 1

			// 添加取件时间信息（从tdTimeSelect解析）
			price.PickupTimeInfo = a.parsePickupTimeInfo(item.TdTimeSelect)

			prices = append(prices, price)
		}
	}

	return prices
}

// getCpName 获取快递公司中文名称
func (a *CainiaoAdapter) getCpName(cpCode string) string {
	cpNames := map[string]string{
		"HTKY":   "百世快递",
		"YUNDA":  "韵达快递",
		"STO":    "申通快递",
		"YTO":    "圆通快递",
		"NORMAL": "菜鸟裹裹", // 🔧 更新：菜鸟裹裹标准快递
	}
	if name, exists := cpNames[cpCode]; exists {
		return name
	}
	return cpCode
}

// convertToOrderResult 转换为标准订单结果
func (a *CainiaoAdapter) convertToOrderResult(data CainiaoCreateOrderData) *model.OrderResult {
	return &model.OrderResult{
		OrderNo:    data.OrderId,
		TrackingNo: data.MailNo,
		PickupCode: data.GotCode,
		Price:      data.PayInfo.TotalPrice,
		TaskId:     data.OrderId, // 🔥 修复：设置菜鸟订单ID为TaskId，用于后续物流轨迹查询
		PrintData:  nil,          // 菜鸟裹裹暂不支持打印数据
	}
}

// convertToOrderInfo 转换为标准订单信息
func (a *CainiaoAdapter) convertToOrderInfo(data CainiaoOrderDetailData) *model.OrderInfo {
	return &model.OrderInfo{
		OrderNo:      data.OrderId,
		TrackingNo:   data.MailNo,
		ExpressType:  data.LogisticsCompanyCode,
		Status:       data.OrderStatus,
		StatusDesc:   data.OrderStatusDesc,
		Weight:       data.PackageInfo.Weight,
		Price:        data.PayInfo.TotalPrice,
		CourierName:  data.CourierInfo.CourierName,
		CourierPhone: data.CourierInfo.CourierMobile,
	}
}

// ==================== 智能默认值处理方法 ====================

// enrichPriceRequestWithDefaults 智能处理缺失字段，提供默认值
// 🚀 参考易达实现，确保菜鸟查价的用户体验一致性
func (a *CainiaoAdapter) enrichPriceRequestWithDefaults(req *model.PriceRequest) {
	// 智能处理寄件人姓名
	if req.Sender.Name == "" {
		req.Sender.Name = "寄件人"
		a.logger.Debug("菜鸟查价使用默认寄件人姓名", zap.String("name", req.Sender.Name))
	}

	// 智能处理收件人姓名
	if req.Receiver.Name == "" {
		req.Receiver.Name = "收件人"
		a.logger.Debug("菜鸟查价使用默认收件人姓名", zap.String("name", req.Receiver.Name))
	}

	// 智能处理寄件人地址
	if req.Sender.Address == "" {
		req.Sender.Address = "寄件地址"
		a.logger.Debug("菜鸟查价使用默认寄件人地址", zap.String("address", req.Sender.Address))
	}

	// 智能处理收件人地址
	if req.Receiver.Address == "" {
		req.Receiver.Address = "收件地址"
		a.logger.Debug("菜鸟查价使用默认收件人地址", zap.String("address", req.Receiver.Address))
	}

	// 智能处理物品名称
	if req.Package.GoodsName == "" {
		req.Package.GoodsName = "普通货物"
		a.logger.Debug("菜鸟查价使用默认物品名称", zap.String("goods_name", req.Package.GoodsName))
	}

	// 智能处理区县信息
	if req.Sender.District == "" {
		req.Sender.District = "市辖区"
		a.logger.Debug("菜鸟查价使用默认寄件人区县", zap.String("district", req.Sender.District))
	}
	if req.Receiver.District == "" {
		req.Receiver.District = "市辖区"
		a.logger.Debug("菜鸟查价使用默认收件人区县", zap.String("district", req.Receiver.District))
	}
}

// formatPhoneNumbers 智能处理电话号码格式
// 🚀 参考易达实现，综合处理Mobile和Tel字段
func (a *CainiaoAdapter) formatPhoneNumbers(mobile, tel string) (string, string) {
	// 优先使用手机号
	if mobile != "" {
		return mobile, tel
	}

	// 如果没有手机号但有固话，将固话作为手机号
	if tel != "" {
		return tel, ""
	}

	// 都没有则使用默认手机号
	return "13800000000", ""
}

// getValidName 获取有效的姓名
func (a *CainiaoAdapter) getValidName(name, defaultName string) string {
	if name != "" {
		return name
	}
	return defaultName
}

// getValidGoodsName 获取有效的物品名称
func (a *CainiaoAdapter) getValidGoodsName(goodsName string) string {
	if goodsName != "" {
		return goodsName
	}
	return "普通货物"
}

// isProviderNotSupportedError 智能识别"供应商不支持"错误
// 🚀 参考易达实现，智能识别服务不支持的情况
func (a *CainiaoAdapter) isProviderNotSupportedError(statusCode, statusMessage string) bool {
	// 根据错误码判断
	notSupportedCodes := []string{
		"5002", // 城市未开放
		"5003", // 运营区未开放
		"3205", // 该地区尚未完成服务准备
	}

	for _, code := range notSupportedCodes {
		if statusCode == code {
			return true
		}
	}

	// 根据错误信息关键词判断
	notSupportedKeywords := []string{
		"不支持",
		"未开放",
		"暂不支持",
		"服务准备",
		"未覆盖",
		"不在服务范围",
	}

	statusMessageLower := strings.ToLower(statusMessage)
	for _, keyword := range notSupportedKeywords {
		if strings.Contains(statusMessageLower, keyword) {
			return true
		}
	}

	return false
}

// enrichOrderRequestWithDefaults 智能处理订单请求缺失字段，提供默认值
// 🚀 参考易达实现，确保菜鸟下单的用户体验一致性
func (a *CainiaoAdapter) enrichOrderRequestWithDefaults(req *model.OrderRequest) {
	// 智能处理寄件人信息
	if req.Sender.Name == "" {
		req.Sender.Name = "寄件人"
		a.logger.Debug("菜鸟下单使用默认寄件人姓名", zap.String("name", req.Sender.Name))
	}
	if req.Sender.Address == "" {
		req.Sender.Address = "寄件地址"
		a.logger.Debug("菜鸟下单使用默认寄件人地址", zap.String("address", req.Sender.Address))
	}
	if req.Sender.District == "" {
		req.Sender.District = "市辖区"
		a.logger.Debug("菜鸟下单使用默认寄件人区县", zap.String("district", req.Sender.District))
	}

	// 智能处理收件人信息
	if req.Receiver.Name == "" {
		req.Receiver.Name = "收件人"
		a.logger.Debug("菜鸟下单使用默认收件人姓名", zap.String("name", req.Receiver.Name))
	}
	if req.Receiver.Address == "" {
		req.Receiver.Address = "收件地址"
		a.logger.Debug("菜鸟下单使用默认收件人地址", zap.String("address", req.Receiver.Address))
	}
	if req.Receiver.District == "" {
		req.Receiver.District = "市辖区"
		a.logger.Debug("菜鸟下单使用默认收件人区县", zap.String("district", req.Receiver.District))
	}

	// 智能处理包裹信息
	if req.Package.GoodsName == "" {
		req.Package.GoodsName = "普通货物"
		a.logger.Debug("菜鸟下单使用默认物品名称", zap.String("goods_name", req.Package.GoodsName))
	}
	if req.Package.Quantity <= 0 {
		req.Package.Quantity = 1
		a.logger.Debug("菜鸟下单使用默认包裹数量", zap.Int("quantity", req.Package.Quantity))
	}

	// 智能处理支付方式
	if req.PayMethod < 0 || req.PayMethod > 2 {
		req.PayMethod = 0 // 寄付
		a.logger.Debug("菜鸟下单使用默认支付方式", zap.Int("pay_method", req.PayMethod))
	}
}

// getFullAddress 获取完整地址
func (a *CainiaoAdapter) getFullAddress(province, city, district, address string) string {
	var fullAddress strings.Builder

	if province != "" {
		fullAddress.WriteString(province)
	}
	if city != "" && city != province {
		fullAddress.WriteString(city)
	}
	if district != "" && district != city {
		fullAddress.WriteString(district)
	}
	if address != "" {
		fullAddress.WriteString(address)
	}

	return fullAddress.String()
}

// buildFullAddress 构建完整地址信息
func (a *CainiaoAdapter) buildFullAddress(province, city, district, address string) string {
	var fullAddress strings.Builder

	if province != "" {
		fullAddress.WriteString(province)
	}
	if city != "" && city != province {
		fullAddress.WriteString(city)
	}
	if district != "" && district != city {
		fullAddress.WriteString(district)
	}
	if address != "" {
		fullAddress.WriteString(address)
	}

	return fullAddress.String()
}

// getCnAccountId 获取菜鸟账号ID
func (a *CainiaoAdapter) getCnAccountId(req *model.OrderRequest) string {
	// 按照Python版本，默认使用固定值
	return "*********"
}

// parseItemId 解析物品ID为整数
func (a *CainiaoAdapter) parseItemId(serviceType string) int {
	// 按照Python版本，将服务类型转换为整数
	if id, err := strconv.Atoi(serviceType); err == nil {
		return id
	}
	return ********** // 默认当日取服务商品ID
}

// getUserRemark 获取用户备注
func (a *CainiaoAdapter) getUserRemark(req *model.OrderRequest) string {
	// 可以使用客户订单号或其他字段作为备注
	if req.CustomerOrderNo != "" {
		return fmt.Sprintf("订单: %s", req.CustomerOrderNo)
	}
	return "测试订单"
}

// buildOutOrderInfoList 构建外部订单信息列表
func (a *CainiaoAdapter) buildOutOrderInfoList(req *model.OrderRequest) []map[string]interface{} {
	// 🔥 修改：优先使用平台订单号，支持用户自定义outOrderId作为备选
	var outOrderId string
	if req.PlatformOrderNo != "" {
		// 🔥 优先使用平台订单号
		outOrderId = req.PlatformOrderNo
	} else if req.OutOrderId != "" {
		// 使用用户自定义的outOrderId
		outOrderId = req.OutOrderId
	} else {
		// 🔥 修复：降级方案使用平台订单号格式，避免使用CustomerOrderNo
		outOrderId = fmt.Sprintf("GK_FALLBACK_%d", time.Now().Unix())
	}

	// 按照Python版本构建订单信息
	orderInfo := map[string]interface{}{
		"itemTitle":  a.getValidGoodsName(req.Package.GoodsName),
		"itemType":   "COMMODITY",
		"outOrderId": outOrderId,
	}

	return []map[string]interface{}{orderInfo}
}

// calculateChargedWeight 计算计费重量（考虑体积重量和抛比）
func (a *CainiaoAdapter) calculateChargedWeight(req *model.PriceRequest, expressCode string) float64 {
	// 如果没有体积信息，直接返回实际重量（向上取整）
	if req.Package.Length <= 0 || req.Package.Width <= 0 || req.Package.Height <= 0 {
		result := math.Ceil(req.Package.Weight)
		a.logger.Debug("菜鸟计费重量计算：无体积信息，使用实际重量",
			zap.String("express_code", expressCode),
			zap.Float64("actual_weight", req.Package.Weight),
			zap.Float64("charged_weight", result))
		return result
	}

	// 计算体积（cm³）
	volumeCm3 := req.Package.Length * req.Package.Width * req.Package.Height

	// 从数据库获取快递公司配置
	if a.expressCompanyRepo == nil {
		a.logger.Error("菜鸟计费重量计算：ExpressCompanyRepository 未初始化",
			zap.String("express_code", expressCode))
		return math.Ceil(req.Package.Weight)
	}

	company, err := a.expressCompanyRepo.GetCompanyByCode(expressCode)
	if err != nil {
		a.logger.Error("菜鸟计费重量计算：获取快递公司配置失败",
			zap.String("express_code", expressCode),
			zap.Error(err))
		// 使用默认抛比 8000（菜鸟统一抛比）
		volumeWeight := volumeCm3 / 8000
		chargedWeight := math.Max(req.Package.Weight, volumeWeight)
		result := math.Ceil(chargedWeight)
		a.logger.Warn("菜鸟计费重量计算：使用默认抛比8000",
			zap.String("express_code", expressCode),
			zap.Float64("volume_weight", volumeWeight),
			zap.Float64("charged_weight", result))
		return result
	}

	// 获取抛比配置
	volumeRatio := company.VolumeWeightRatio
	if volumeRatio <= 0 {
		volumeRatio = 8000 // 菜鸟默认抛比
		a.logger.Warn("菜鸟计费重量计算：快递公司抛比配置无效，使用默认值8000",
			zap.String("express_code", expressCode),
			zap.String("company_name", company.Name))
	}

	// 计算体积重量
	volumeWeight := volumeCm3 / float64(volumeRatio)

	// 取实际重量和体积重量的较大值
	chargedWeight := math.Max(req.Package.Weight, volumeWeight)

	// 向上取整到最近的整数（快递行业标准）
	result := math.Ceil(chargedWeight)

	a.logger.Debug("菜鸟计费重量计算结果",
		zap.String("express_code", expressCode),
		zap.String("company_name", company.Name),
		zap.Int("volume_ratio", volumeRatio),
		zap.Float64("volume_cm3", volumeCm3),
		zap.Float64("volume_weight", volumeWeight),
		zap.Float64("actual_weight", req.Package.Weight),
		zap.Float64("charged_weight", result))

	return result
}

// parsePickupTimeInfo 解析菜鸟的时间选择信息为标准格式
func (a *CainiaoAdapter) parsePickupTimeInfo(tdTimeSelect CainiaoTimeSelect) *model.PickupTimeInfo {
	var availableSlots []model.PickupTimeSlotSimple

	// 检查实时下单是否可用
	realTimeAvailable := tdTimeSelect.RealTime.Selectable == "true"
	a.logger.Debug("菜鸟时间解析：实时下单状态",
		zap.Bool("realtime_available", realTimeAvailable),
		zap.String("realtime_selectable", tdTimeSelect.RealTime.Selectable))

	// 解析预约时间
	for _, appointTime := range tdTimeSelect.AppointTimes {
		a.logger.Debug("菜鸟时间解析：处理预约日期",
			zap.String("date", appointTime.Date),
			zap.String("title", appointTime.Title),
			zap.String("full", appointTime.Full),
			zap.Int("time_slots_count", len(appointTime.TimeList)))

		// 跳过已满的日期
		if appointTime.Full == "true" {
			a.logger.Debug("菜鸟时间解析：跳过已满日期", zap.String("date", appointTime.Date))
			continue
		}

		// 解析该日期的时间段
		for i, timeSlot := range appointTime.TimeList {
			a.logger.Debug("菜鸟时间解析：处理时间段",
				zap.Int("index", i),
				zap.String("date", appointTime.Date),
				zap.String("start_time", timeSlot.StartTime),
				zap.String("end_time", timeSlot.EndTime),
				zap.String("selectable", timeSlot.Selectable),
				zap.String("full", timeSlot.Full))

			// 只添加可选择的时间段
			if timeSlot.Selectable == "true" && timeSlot.Full != "true" {
				slotID := fmt.Sprintf("%s_%d", appointTime.Date, i)
				slotName := fmt.Sprintf("%s %s-%s", appointTime.Title, timeSlot.StartTime, timeSlot.EndTime)
				startTime := fmt.Sprintf("%s %s:00", appointTime.Date, timeSlot.StartTime)
				endTime := fmt.Sprintf("%s %s:00", appointTime.Date, timeSlot.EndTime)

				availableSlots = append(availableSlots, model.PickupTimeSlotSimple{
					SlotID:    slotID,
					SlotName:  slotName,
					StartTime: startTime,
					EndTime:   endTime,
				})

				a.logger.Debug("菜鸟时间解析：添加可用时间段",
					zap.String("slot_id", slotID),
					zap.String("slot_name", slotName))
			}
		}
	}

	// 如果实时下单可用，添加实时时间段
	if realTimeAvailable {
		now := time.Now()
		startTime := now.Add(1 * time.Hour)
		endTime := now.Add(3 * time.Hour)

		availableSlots = append([]model.PickupTimeSlotSimple{{
			SlotID:    "realtime",
			SlotName:  "立即下单",
			StartTime: startTime.Format("2006-01-02 15:04:05"),
			EndTime:   endTime.Format("2006-01-02 15:04:05"),
		}}, availableSlots...)
	}

	// 如果没有可用时间段，返回nil
	if len(availableSlots) == 0 {
		a.logger.Warn("菜鸟查价：没有可用的预约时间段",
			zap.Bool("realtime_available", realTimeAvailable),
			zap.Int("appoint_times_count", len(tdTimeSelect.AppointTimes)))
		return nil
	}

	pickupTimeInfo := &model.PickupTimeInfo{
		PickupRequired:     true,                  // 菜鸟需要预约取件
		SupportsPickupCode: true,                  // 支持取件码
		MinAdvanceHours:    1,                     // 最少提前1小时预约
		TimeFormat:         "2006-01-02 15:04:05", // 时间格式
		AvailableSlots:     availableSlots,        // 可用时间段
	}

	a.logger.Info("菜鸟时间解析：成功解析预约时间信息",
		zap.Int("available_slots_count", len(availableSlots)),
		zap.Bool("realtime_available", realTimeAvailable),
		zap.Any("pickup_time_info", pickupTimeInfo))

	return pickupTimeInfo
}

// determineOrderTimeType 智能判断下单类型和时间
// 根据用户选择的时间来决定是实时单还是预约单
func (a *CainiaoAdapter) determineOrderTimeType(req *model.OrderRequest) (int, string, string) {
	// 🔥 调试：记录接收到的预约时间信息
	a.logger.Info("菜鸟下单：检查预约时间信息",
		zap.String("pickup_start_time", req.Pickup.StartTime),
		zap.String("pickup_end_time", req.Pickup.EndTime),
		zap.Bool("start_time_empty", req.Pickup.StartTime == ""),
		zap.Bool("end_time_empty", req.Pickup.EndTime == ""))

	// 检查用户是否指定了预约时间
	if req.Pickup.StartTime != "" && req.Pickup.EndTime != "" {
		// 🔥 转换ISO 8601格式为菜鸟API格式
		startTime, err := a.convertISO8601ToCainiaoFormat(req.Pickup.StartTime)
		if err != nil {
			a.logger.Error("菜鸟下单：预约开始时间格式转换失败",
				zap.String("original_time", req.Pickup.StartTime),
				zap.Error(err))
			// 转换失败时使用实时单模式
			a.logger.Info("菜鸟下单：时间格式转换失败，回退到实时单模式")
		} else {
			endTime, err := a.convertISO8601ToCainiaoFormat(req.Pickup.EndTime)
			if err != nil {
				a.logger.Error("菜鸟下单：预约结束时间格式转换失败",
					zap.String("original_time", req.Pickup.EndTime),
					zap.Error(err))
				// 转换失败时使用实时单模式
				a.logger.Info("菜鸟下单：时间格式转换失败，回退到实时单模式")
			} else {
				a.logger.Info("菜鸟下单：用户指定了预约时间，使用预约单模式",
					zap.String("original_start_time", req.Pickup.StartTime),
					zap.String("original_end_time", req.Pickup.EndTime),
					zap.String("converted_start_time", startTime),
					zap.String("converted_end_time", endTime))
				return 2, startTime, endTime // 2-预约单
			}
		}
	}

	// 如果没有指定时间，使用实时单模式
	a.logger.Info("菜鸟下单：用户未指定预约时间，使用实时单模式")

	// 计算默认的取件时间（实时单也需要时间范围）
	now := util.NowBeijing()

	// 实时单：1小时后开始，3小时内完成
	startTime := now.Add(1 * time.Hour)
	endTime := now.Add(3 * time.Hour)

	// 如果当前时间太晚（超过18点），安排明天取件
	if now.Hour() >= 18 {
		tomorrow := now.AddDate(0, 0, 1)
		beijingLocation := now.Location() // 使用当前北京时间的时区
		startTime = time.Date(tomorrow.Year(), tomorrow.Month(), tomorrow.Day(), 9, 0, 0, 0, beijingLocation)
		endTime = time.Date(tomorrow.Year(), tomorrow.Month(), tomorrow.Day(), 18, 0, 0, 0, beijingLocation)
	}

	startTimeStr := startTime.Format("2006-01-02 15:04:05")
	endTimeStr := endTime.Format("2006-01-02 15:04:05")

	a.logger.Info("菜鸟下单：计算的实时单时间",
		zap.String("start_time", startTimeStr),
		zap.String("end_time", endTimeStr))

	return 1, startTimeStr, endTimeStr // 1-实时单
}

// convertISO8601ToCainiaoFormat 将ISO 8601格式时间转换为菜鸟API格式
// 输入：2025-07-16T09:00:00+08:00
// 输出：2025-07-16 09:00:00
func (a *CainiaoAdapter) convertISO8601ToCainiaoFormat(iso8601Time string) (string, error) {
	// 解析ISO 8601格式时间
	t, err := time.Parse(time.RFC3339, iso8601Time)
	if err != nil {
		return "", fmt.Errorf("解析ISO 8601时间失败: %v", err)
	}

	// 转换为北京时间
	beijingLocation, _ := time.LoadLocation("Asia/Shanghai")
	beijingTime := t.In(beijingLocation)

	// 格式化为菜鸟API期望的格式
	cainiaoFormat := beijingTime.Format("2006-01-02 15:04:05")

	return cainiaoFormat, nil
}

// queryLogisticsDetailByOrderId 根据菜鸟订单ID查询物流详情
func (a *CainiaoAdapter) queryLogisticsDetailByOrderId(ctx context.Context, cainiaoOrderId, trackingNo string) (*model.TrackInfo, error) {
	// 参数验证
	if cainiaoOrderId == "" {
		return nil, fmt.Errorf("菜鸟订单ID不能为空")
	}

	// 🔥 修复：按照官方文档构建正确的嵌套对象参数结构
	queryData := map[string]interface{}{
		"request": map[string]interface{}{
			"tdOrderId": cainiaoOrderId, // 菜鸟订单ID
		},
		"option": map[string]interface{}{
			"accessMethod": "LINK",              // 固定写死
			"accessCode":   a.config.AccessCode, // 菜鸟分配的接入编码
		},
	}

	a.logger.Info("菜鸟裹裹开始查询物流轨迹（按订单ID）",
		zap.String("cainiao_order_id", cainiaoOrderId),
		zap.String("tracking_no", trackingNo))

	// 🔥 修复：使用正确的消息类型
	msgType := "GUOGUO_QUERY_LOGISTICS_DETAIL"
	respData, err := a.callAPI(ctx, msgType, queryData)
	if err != nil {
		a.logger.Error("菜鸟裹裹查询物流轨迹API调用失败",
			zap.Error(err),
			zap.String("cainiao_order_id", cainiaoOrderId),
			zap.String("tracking_no", trackingNo),
			zap.String("msg_type", msgType))
		return nil, fmt.Errorf("调用菜鸟裹裹查询物流轨迹API失败: %w", err)
	}

	// 解析响应
	var response CainiaoLogisticsDetailResponse
	if err := json.Unmarshal(respData, &response); err != nil {
		a.logger.Error("菜鸟裹裹查询物流轨迹响应解析失败",
			zap.Error(err),
			zap.String("cainiao_order_id", cainiaoOrderId),
			zap.String("tracking_no", trackingNo),
			zap.String("response", string(respData)))
		return nil, fmt.Errorf("解析菜鸟裹裹查询物流轨迹响应失败: %w", err)
	}

	// 检查响应状态（菜鸟API返回字符串"true"/"false"）
	if response.Result.Success != "true" {
		a.logger.Warn("菜鸟裹裹查询物流轨迹业务失败",
			zap.String("cainiao_order_id", cainiaoOrderId),
			zap.String("tracking_no", trackingNo),
			zap.String("error_code", response.Result.ErrorCode),
			zap.String("error_msg", response.Result.ErrorMsg))
		return nil, a.handleCainiaoError(response.Result.ErrorCode, response.Result.ErrorMsg)
	}

	// 转换为标准物流信息
	trackInfo := a.convertLogisticsTraceToTrackInfo(ctx, response.Result.Data)

	a.logger.Info("菜鸟裹裹物流轨迹查询成功",
		zap.String("cainiao_order_id", cainiaoOrderId),
		zap.String("tracking_no", trackingNo),
		zap.String("mail_no", response.Result.Data.MailNo),
		zap.String("company_name", response.Result.Data.CompanyName),
		zap.Int("trace_count", len(response.Result.Data.LogisticsTraceDetailList)))

	return trackInfo, nil
}

// convertLogisticsTraceToTrackInfo 转换菜鸟物流轨迹为标准物流信息
func (a *CainiaoAdapter) convertLogisticsTraceToTrackInfo(ctx context.Context, data CainiaoLogisticsDetailData) *model.TrackInfo {
	var tracks []*model.TrackItem

	for _, detail := range data.LogisticsTraceDetailList {
		// 解析时间字符串为time.Time
		trackTime := a.parseCainiaoTimeString(detail.Time)

		// 映射菜鸟裹裹的状态到标准状态
		_, standardStatus := a.mapCainiaoTraceStatus(detail.Status)
		statusCode := a.getCainiaoStatusCode(detail.Status)

		// 创建轨迹项
		trackItem := &model.TrackItem{
			Context:    detail.Desc, // 使用正确的字段名
			Time:       trackTime,   // 使用time.Time类型
			Status:     standardStatus,
			StatusCode: statusCode,
			Location:   "", // 菜鸟轨迹中位置信息包含在描述中
			AreaName:   "", // 区域名称从描述中提取
		}

		tracks = append(tracks, trackItem)
	}

	// 确定整体状态
	var state string
	var stateDesc string
	if len(tracks) > 0 {
		// 使用最新的轨迹状态作为整体状态
		state = tracks[0].Status
		stateDesc, _ = a.mapCainiaoTraceStatus(data.LogisticsTraceDetailList[0].Status)
	} else {
		state = "unknown"
		stateDesc = "未知状态"
	}

	return &model.TrackInfo{
		TrackingNo:  data.MailNo,
		ExpressType: data.CompanyCode,
		State:       state,
		StateDesc:   stateDesc,
		IsCheck:     a.getIsCheckStatus(state),
		Tracks:      tracks,
	}
}

// mapCainiaoTraceStatus 映射菜鸟轨迹状态到标准状态
func (a *CainiaoAdapter) mapCainiaoTraceStatus(cainiaoStatus string) (string, string) {
	switch cainiaoStatus {
	// === 订单状态（数字编码）===
	case "-1":
		return "已取消", "cancelled"
	case "0":
		return "已提交", "submitted"
	case "20":
		return "已分配", "assigned"
	case "30":
		return "已揽收", "picked_up"
	case "40":
		return "已计费", "billed"

	// === 物流状态（事件编码）===
	case "ACCEPT":
		return "已揽收", "picked_up"
	case "TRANSPORT":
		return "运输中", "in_transit"
	case "DELIVERING":
		return "派送中", "out_for_delivery"
	case "SIGN":
		return "已签收", "delivered"
	case "FAILED":
		return "异常", "exception"
	case "REJECT":
		return "异常", "exception"
	case "AGENT_SIGN":
		return "等待揽收", "awaiting_pickup"
	case "STA_DELIVERING":
		return "派送中", "out_for_delivery"
	case "ORDER_TRANSER":
		return "已转寄", "forwarded"
	case "REVERSE_RETURN":
		return "已退回", "returned"

	// === 兼容状态 ===
	case "CREATE":
		return "已提交", "submitted"
	case "CREATED":
		return "已提交", "submitted"
	case "CANCELLED":
		return "已取消", "cancelled"
	case "COMPLETED":
		return "已计费", "billed"
	case "PROBLEM":
		return "异常", "exception"
	case "RETURN":
		return "已退回", "returned"
	default:
		return cainiaoStatus, "unknown"
	}
}

// getCainiaoStatusCode 获取菜鸟状态对应的状态码
func (a *CainiaoAdapter) getCainiaoStatusCode(cainiaoStatus string) string {
	switch cainiaoStatus {
	case "CREATE":
		return "101" // 已下单
	case "ACCEPT":
		return "103" // 已揽收
	case "TRANSPORT":
		return "0" // 在途
	case "DELIVERING":
		return "5" // 派件
	case "SIGN":
		return "3" // 签收
	case "PROBLEM":
		return "2" // 疑难
	case "RETURN":
		return "6" // 退回
	default:
		return "0" // 默认在途
	}
}

// getIsCheckStatus 根据状态判断是否签收
func (a *CainiaoAdapter) getIsCheckStatus(status string) string {
	if status == "delivered" {
		return "1" // 已签收
	}
	return "0" // 未签收
}

// parseCainiaoTimeString 解析菜鸟时间字符串为time.Time
func (a *CainiaoAdapter) parseCainiaoTimeString(timeStr string) time.Time {
	// 菜鸟API返回的时间格式：2025-07-16 09:13:34
	layout := "2006-01-02 15:04:05"

	// 解析时间，使用北京时区
	loc, _ := time.LoadLocation("Asia/Shanghai")
	parsedTime, err := time.ParseInLocation(layout, timeStr, loc)
	if err != nil {
		a.logger.Warn("解析菜鸟时间字符串失败，使用当前时间",
			zap.String("time_str", timeStr),
			zap.Error(err))
		return time.Now()
	}

	return parsedTime
}

// formatPrice 格式化价格，修复浮点数精度问题
// 保留2位小数，避免出现 9.899999999999999 这样的精度问题
func (a *CainiaoAdapter) formatPrice(price float64) float64 {
	// 使用 math 包的 Round 函数，先乘以100，四舍五入，再除以100
	// 这样可以确保价格精确到分（2位小数）
	return math.Round(price*100) / 100
}
