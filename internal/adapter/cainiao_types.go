package adapter

import "time"

// ==================== 菜鸟裹裹API响应结构体 ====================

// CainiaoBaseResponse 菜鸟裹裹基础响应
type CainiaoBaseResponse struct {
	Result CainiaoResult `json:"result"`
}

// CainiaoResult 菜鸟裹裹响应结果
type CainiaoResult struct {
	Success       string `json:"success"` // 菜鸟API返回字符串"true"/"false"
	StatusCode    string `json:"statusCode"`
	StatusMessage string `json:"statusMessage"`
}

// CainiaoServiceDetailResponse 菜鸟裹裹服务详情查询响应
type CainiaoServiceDetailResponse struct {
	Result    CainiaoServiceDetailResult `json:"result" xml:"result"`
	Success   bool                       `json:"success" xml:"success"`
	ErrorCode string                     `json:"errorCode" xml:"errorCode"`
	ErrorMsg  string                     `json:"errorMsg" xml:"errorMsg"`
}

// CainiaoServiceDetailResult 菜鸟裹裹服务详情查询结果
type CainiaoServiceDetailResult struct {
	Success       string                   `json:"success"` // 菜鸟API返回字符串"true"/"false"
	StatusCode    string                   `json:"statusCode"`
	StatusMessage string                   `json:"statusMessage"`
	Data          CainiaoServiceDetailData `json:"data"`
}

// CainiaoServiceDetailData 菜鸟裹裹服务详情数据
type CainiaoServiceDetailData struct {
	AvailableServiceItemList []CainiaoServiceItem `json:"availableServiceItemList"`
}

// CainiaoServiceItem 菜鸟裹裹服务项目
type CainiaoServiceItem struct {
	Code                    string                   `json:"code"`
	Title                   string                   `json:"title"`
	Version                 string                   `json:"version"`
	BillingTemplateByCpList []CainiaoBillingTemplate `json:"billingTemplateByCpList"`
	TdTimeSelect            CainiaoTimeSelect        `json:"tdTimeSelect"`
	SuppoortCpList          []string                 `json:"suppoortCpList"`

	// 保留旧字段以兼容
	ItemId      string            `json:"itemId"`
	ItemVersion string            `json:"itemVersion"`
	ServiceName string            `json:"serviceName"`
	TimeList    []CainiaoTimeSlot `json:"timeList"`
	DateList    []CainiaoDateSlot `json:"dateList"`
	PriceInfo   CainiaoPriceInfo  `json:"priceInfo"`
}

// CainiaoTimeSlot 菜鸟裹裹时间段
type CainiaoTimeSlot struct {
	StartTime        string `json:"startTime"`
	EndTime          string `json:"endTime"`
	Selectable       string `json:"selectable"` // 菜鸟API返回字符串"true"/"false"
	SelectDisableTip string `json:"selectDisableTip"`
	Full             string `json:"full"` // 菜鸟API返回字符串"true"/"false"
	Description      string `json:"description"`
}

// CainiaoDateSlot 菜鸟裹裹日期段
type CainiaoDateSlot struct {
	Date           string            `json:"date"`
	DateSelectable bool              `json:"dateSelectable"`
	TimeList       []CainiaoTimeSlot `json:"timeList"`
}

// CainiaoPriceInfo 菜鸟裹裹价格信息
type CainiaoPriceInfo struct {
	BasePrice    float64              `json:"basePrice"`
	TotalPrice   float64              `json:"totalPrice"`
	Currency     string               `json:"currency"`
	PriceDetails []CainiaoPriceDetail `json:"priceDetails"`
}

// CainiaoPriceDetail 菜鸟裹裹价格明细
type CainiaoPriceDetail struct {
	FeeType string  `json:"feeType"`
	FeeName string  `json:"feeName"`
	Amount  float64 `json:"amount"`
}

// CainiaoCreateOrderResponse 菜鸟裹裹创建订单响应
type CainiaoCreateOrderResponse struct {
	Result CainiaoCreateOrderResult `json:"result"`
}

// CainiaoCreateOrderResult 菜鸟裹裹创建订单结果
type CainiaoCreateOrderResult struct {
	Success       string                 `json:"success"` // 菜鸟API返回字符串"true"/"false"
	StatusCode    string                 `json:"statusCode"`
	StatusMessage string                 `json:"statusMessage"`
	Data          CainiaoCreateOrderData `json:"data"`
}

// CainiaoCreateOrderData 菜鸟裹裹创建订单数据
type CainiaoCreateOrderData struct {
	OrderId          string             `json:"orderId"`
	MailNo           string             `json:"mailNo"`
	GotCode          string             `json:"gotCode"`
	CheckPackageCode string             `json:"checkPackageCode"`
	CourierInfo      CainiaoCourierInfo `json:"courierInfo"`
	PayInfo          CainiaoPayInfo     `json:"payInfo"`
}

// CainiaoCourierInfo 菜鸟裹裹快递员信息
type CainiaoCourierInfo struct {
	CourierName    string `json:"courierName"`
	CourierMobile  string `json:"courierMobile"`
	CourierCompany string `json:"courierCompany"`
}

// CainiaoPayInfo 菜鸟裹裹支付信息
type CainiaoPayInfo struct {
	PayStatus  string  `json:"payStatus"`
	TotalPrice float64 `json:"totalPrice"`
	AliPayUrl  string  `json:"aliPayUrl"`
}

// CainiaoOrderDetailResponse 菜鸟裹裹订单详情查询响应
type CainiaoOrderDetailResponse struct {
	Result CainiaoOrderDetailResult `json:"result"`
}

// CainiaoOrderDetailResult 菜鸟裹裹订单详情查询结果
type CainiaoOrderDetailResult struct {
	Success       string                 `json:"success"` // 菜鸟API返回字符串"true"/"false"
	StatusCode    string                 `json:"statusCode"`
	StatusMessage string                 `json:"statusMessage"`
	Data          CainiaoOrderDetailData `json:"data"`
}

// CainiaoOrderDetailData 菜鸟裹裹订单详情数据
type CainiaoOrderDetailData struct {
	OrderId              string                   `json:"orderId"`
	OrderStatus          string                   `json:"orderStatus"`
	OrderStatusDesc      string                   `json:"orderStatusDesc"`
	MailNo               string                   `json:"mailNo"`
	LogisticsCompanyCode string                   `json:"logisticsCompanyCode"`
	LogisticsCompanyName string                   `json:"logisticsCompanyName"`
	SenderInfo           CainiaoAddressInfo       `json:"senderInfo"`
	ReceiverInfo         CainiaoAddressInfo       `json:"receiverInfo"`
	PackageInfo          CainiaoPackageInfo       `json:"packageInfo"`
	CourierInfo          CainiaoCourierInfo       `json:"courierInfo"`
	PayInfo              CainiaoPayInfo           `json:"payInfo"`
	LogisticsDetail      []CainiaoLogisticsDetail `json:"logisticsDetail"`
}

// CainiaoAddressInfo 菜鸟裹裹地址信息
type CainiaoAddressInfo struct {
	Name    string `json:"name"`
	Mobile  string `json:"mobile"`
	Phone   string `json:"phone"`
	Address string `json:"address"`
	AreaId  string `json:"areaId"`
}

// CainiaoPackageInfo 菜鸟裹裹包裹信息
type CainiaoPackageInfo struct {
	Weight      float64 `json:"weight"`
	Volume      float64 `json:"volume"`
	GoodsName   string  `json:"goodsName"`
	GoodsValue  float64 `json:"goodsValue"`
	Description string  `json:"description"`
}

// CainiaoLogisticsDetailResponse 菜鸟裹裹物流详情查询响应
type CainiaoLogisticsDetailResponse struct {
	Result CainiaoLogisticsDetailResult `json:"result"`
}

// CainiaoLogisticsDetailResult 菜鸟裹裹物流详情查询结果（官方文档格式）
type CainiaoLogisticsDetailResult struct {
	Data      CainiaoLogisticsDetailData `json:"data"`
	Success   string                     `json:"success"` // 菜鸟API返回字符串"true"/"false"
	ErrorCode string                     `json:"errorCode"`
	ErrorMsg  string                     `json:"errorMsg"`
}

// CainiaoLogisticsDetailData 菜鸟裹裹物流详情数据（官方文档格式）
type CainiaoLogisticsDetailData struct {
	MailNo                   string                        `json:"mailNo"`
	CompanyCode              string                        `json:"companyCode"`
	CompanyName              string                        `json:"companyName"`
	LogisticsTraceDetailList []CainiaoLogisticsTraceDetail `json:"logisticsTraceDetailList"`
	CompanyContact           string                        `json:"companyContact"`
}

// CainiaoLogisticsDetail 菜鸟裹裹物流详情
type CainiaoLogisticsDetail struct {
	Time        string `json:"time"`
	Status      string `json:"status"`
	Description string `json:"description"`
	Location    string `json:"location"`
	Action      string `json:"action"`
}

// CainiaoLogisticsTraceData 菜鸟裹裹物流轨迹数据（官方文档格式）
type CainiaoLogisticsTraceData struct {
	MailNo                   string                        `json:"mailNo"`
	CompanyCode              string                        `json:"companyCode"`
	CompanyName              string                        `json:"companyName"`
	LogisticsTraceDetailList []CainiaoLogisticsTraceDetail `json:"logisticsTraceDetailList"`
	CompanyContact           string                        `json:"companyContact"`
	Class                    string                        `json:"class"`
}

// CainiaoLogisticsTraceDetail 菜鸟裹裹物流轨迹详情（官方文档格式）
type CainiaoLogisticsTraceDetail struct {
	StatusDesc   string `json:"statusDesc"`   // 状态描述
	Action       string `json:"action"`       // 动作
	StandardDesc string `json:"standardDesc"` // 标准描述
	Time         string `json:"time"`         // 时间字符串（格式：2025-07-16 09:13:34）
	Class        string `json:"class"`        // 类名
	Desc         string `json:"desc"`         // 详细描述
	Status       string `json:"status"`       // 状态
}

// ==================== 菜鸟裹裹事件推送结构体 ====================

// CainiaoEventPush 菜鸟裹裹事件推送
type CainiaoEventPush struct {
	EventType  string                 `json:"eventType"`
	EventData  map[string]interface{} `json:"eventData"`
	OrderId    string                 `json:"orderId"`
	MailNo     string                 `json:"mailNo"`
	EventTime  time.Time              `json:"eventTime"`
	AccessCode string                 `json:"accessCode"`
	CPCode     string                 `json:"cpCode"`
}

// CainiaoOrderEvent 菜鸟裹裹订单事件
type CainiaoOrderEvent struct {
	OrderId          string  `json:"orderId"`
	OrderStatus      string  `json:"orderStatus"`
	MailNo           string  `json:"mailNo"`
	GotCode          string  `json:"gotCode"`
	CheckPackageCode string  `json:"checkPackageCode"`
	CourierName      string  `json:"courierName"`
	CourierMobile    string  `json:"courierMobile"`
	CourierCompany   string  `json:"courierCompany"`
	TotalPrice       float64 `json:"totalPrice"`
	Weight           float64 `json:"weight"`
	BasePrice        float64 `json:"basePrice"`
	PayStatus        string  `json:"payStatus"`
	PayTime          string  `json:"payTime"`
}

// CainiaoLogisticsEvent 菜鸟裹裹物流事件
type CainiaoLogisticsEvent struct {
	MailNo                string `json:"mailNo"`
	LogisticsAction       string `json:"logisticsAction"`
	LogisticsStatus       string `json:"logisticsStatus"`
	LogisticsCreate       string `json:"logisticsCreate"`
	LogisticsStandardDesc string `json:"logisticsStandardDesc"`
	LogisticsCompName     string `json:"logisticsCompName"`
	ItemId                string `json:"itemId"`
}

// ==================== 菜鸟裹裹回调数据结构体 ====================

// CainiaoCallbackData 菜鸟裹裹回调数据
// 根据官方文档事件推送格式定义
type CainiaoCallbackData struct {
	EventType      string                 `json:"eventType"`      // 事件类型：CREATE_ORDER、CANCEL_ORDER等
	EventTime      time.Time              `json:"eventTime"`      // 事件时间
	AccessCode     string                 `json:"accessCode"`     // 接入编码
	CPCode         string                 `json:"cpCode"`         // CP编号
	OrderEvent     *CainiaoOrderEvent     `json:"orderEvent"`     // 订单事件（可选）
	LogisticsEvent *CainiaoLogisticsEvent `json:"logisticsEvent"` // 物流事件（可选）
	EventData      map[string]interface{} `json:"eventData"`      // 原始事件数据，包含具体的事件信息
}

// CainiaoCallbackOrderEvent 菜鸟裹裹回调订单事件
type CainiaoCallbackOrderEvent struct {
	OrderID          string  `json:"orderId"`
	OrderStatus      string  `json:"orderStatus"`
	MailNo           string  `json:"mailNo"`
	GotCode          string  `json:"gotCode"`
	CheckPackageCode string  `json:"checkPackageCode"`
	CourierName      string  `json:"courierName"`
	CourierMobile    string  `json:"courierMobile"`
	CourierCompany   string  `json:"courierCompany"`
	TotalPrice       float64 `json:"totalPrice"`
	Weight           float64 `json:"weight"`
	BasePrice        float64 `json:"basePrice"`
	PayStatus        string  `json:"payStatus"`
	PayTime          string  `json:"payTime"`
	EventTime        string  `json:"eventTime"`
	Remark           string  `json:"remark"`
}

// CainiaoCallbackLogisticsEvent 菜鸟裹裹回调物流事件
type CainiaoCallbackLogisticsEvent struct {
	MailNo                string `json:"mailNo"`
	LogisticsAction       string `json:"logisticsAction"`
	LogisticsStatus       string `json:"logisticsStatus"`
	LogisticsCreate       string `json:"logisticsCreate"`
	LogisticsStandardDesc string `json:"logisticsStandardDesc"`
	LogisticsCompName     string `json:"logisticsCompName"`
	LogisticsCompCode     string `json:"logisticsCompCode"`
	ItemId                string `json:"itemId"`
	EventTime             string `json:"eventTime"`
	Location              string `json:"location"`
	Remark                string `json:"remark"`
}

// CainiaoBillingTemplate 菜鸟裹裹计费模板
type CainiaoBillingTemplate struct {
	CpCode              string `json:"cpCode"`
	StartPrice          string `json:"startPrice"`
	ContinuedHeavy      string `json:"continuedHeavy"`
	StartWeight         string `json:"startWeight"`
	ContinuedHeavyPrice string `json:"continuedHeavyPrice"`
}

// CainiaoTimeSelect 菜鸟裹裹时间选择
type CainiaoTimeSelect struct {
	RealTime     CainiaoRealTime      `json:"realTime"`
	AppointTimes []CainiaoAppointTime `json:"appointTimes"`
}

// CainiaoRealTime 菜鸟裹裹实时下单
type CainiaoRealTime struct {
	Selectable       string `json:"selectable"`
	Name             string `json:"name"`
	SelectDisableTip string `json:"selectDisableTip"`
}

// CainiaoAppointTime 菜鸟裹裹预约时间
type CainiaoAppointTime struct {
	Date           string            `json:"date"`
	DateSelectable string            `json:"dateSelectable"`
	TimeList       []CainiaoTimeSlot `json:"timeList"`
	Title          string            `json:"title"`
	Full           string            `json:"full"`
}
