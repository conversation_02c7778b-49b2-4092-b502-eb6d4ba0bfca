package adapter

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/your-org/go-kuaidi/internal/model"
)

// TestKuaidiNiaoAdapter_Name tests the Name method
func TestKuaidiNiaoAdapter_Name(t *testing.T) {
	config := KuaidiNiaoConfig{
		EBusinessID: "test_business_id",
		ApiKey:      "test_api_key",
		BaseURL:     "https://api.test.com",
		Environment: "test",
		Timeout:     10,
	}

	adapter := NewKuaidiNiaoAdapter(config, nil)
	assert.Equal(t, "kuaidiniao", adapter.Name())
}

// TestKuaidiNiaoAdapter_SetMappingService tests the SetMappingService method
func TestKuaidiNiaoAdapter_SetMappingService(t *testing.T) {
	config := KuaidiNiaoConfig{
		EBusinessID: "test_business_id",
		ApiKey:      "test_api_key",
		BaseURL:     "https://api.test.com",
		Environment: "test",
		Timeout:     10,
	}

	adapter := NewKuaidiNiaoAdapter(config, nil)
	
	// Test that SetMappingService doesn't panic
	assert.NotPanics(t, func() {
		adapter.SetMappingService(nil)
	})
}

// TestKuaidiNiaoAdapter_generateSignature tests the signature generation
func TestKuaidiNiaoAdapter_generateSignature(t *testing.T) {
	config := KuaidiNiaoConfig{
		EBusinessID: "test_business_id",
		ApiKey:      "test_api_key",
		BaseURL:     "https://api.test.com",
		Environment: "test",
		Timeout:     10,
	}

	adapter := NewKuaidiNiaoAdapter(config, nil)
	
	// Test signature generation
	testData := `{"test": "data"}`
	signature := adapter.generateSignature(testData)
	
	// Signature should not be empty
	assert.NotEmpty(t, signature)
	
	// Same input should produce same signature
	signature2 := adapter.generateSignature(testData)
	assert.Equal(t, signature, signature2)
	
	// Different input should produce different signature
	signature3 := adapter.generateSignature(`{"test": "other"}`)
	assert.NotEqual(t, signature, signature3)
}

// TestKuaidiNiaoAdapter_standardizeProvince tests province standardization
func TestKuaidiNiaoAdapter_standardizeProvince(t *testing.T) {
	config := KuaidiNiaoConfig{
		EBusinessID: "test_business_id",
		ApiKey:      "test_api_key",
		BaseURL:     "https://api.test.com",
		Environment: "test",
		Timeout:     10,
	}

	adapter := NewKuaidiNiaoAdapter(config, nil)

	tests := []struct {
		input    string
		expected string
	}{
		{"北京", "北京市"},
		{"上海", "上海市"},
		{"天津", "天津市"},
		{"重庆", "重庆市"},
		{"广东", "广东省"},
		{"江苏", "江苏省"},
		{"北京市", "北京市"}, // Already standardized
		{"广东省", "广东省"}, // Already standardized
		{"内蒙古", "内蒙古自治区"},
		{"新疆", "新疆维吾尔自治区"},
	}

	for _, test := range tests {
		result := adapter.standardizeProvince(test.input)
		assert.Equal(t, test.expected, result, "Failed for input: %s", test.input)
	}
}

// TestKuaidiNiaoAdapter_standardizeCity tests city standardization
func TestKuaidiNiaoAdapter_standardizeCity(t *testing.T) {
	config := KuaidiNiaoConfig{
		EBusinessID: "test_business_id",
		ApiKey:      "test_api_key",
		BaseURL:     "https://api.test.com",
		Environment: "test",
		Timeout:     10,
	}

	adapter := NewKuaidiNiaoAdapter(config, nil)

	tests := []struct {
		input    string
		expected string
	}{
		{"深圳", "深圳市"},
		{"广州", "广州市"},
		{"苏州", "苏州市"},
		{"深圳市", "深圳市"}, // Already standardized
		{"延边", "延边州"},   // Special case for 州
		{"阿拉善", "阿拉善盟"}, // Special case for 盟
	}

	for _, test := range tests {
		result := adapter.standardizeCity(test.input)
		assert.Equal(t, test.expected, result, "Failed for input: %s", test.input)
	}
}

// TestKuaidiNiaoAdapter_calculateChargedWeight tests weight calculation
func TestKuaidiNiaoAdapter_calculateChargedWeight(t *testing.T) {
	config := KuaidiNiaoConfig{
		EBusinessID: "test_business_id",
		ApiKey:      "test_api_key",
		BaseURL:     "https://api.test.com",
		Environment: "test",
		Timeout:     10,
	}

	adapter := NewKuaidiNiaoAdapter(config, nil)

	tests := []struct {
		name           string
		weight         float64
		length         float64
		width          float64
		height         float64
		expectedWeight float64
	}{
		{
			name:           "No volume info - use actual weight",
			weight:         2.5,
			length:         0,
			width:          0,
			height:         0,
			expectedWeight: 3, // Ceiling of 2.5
		},
		{
			name:           "Volume weight smaller than actual",
			weight:         5.0,
			length:         20,
			width:          15,
			height:         10, // 3000 cm³ / 6000 = 0.5 kg
			expectedWeight: 5,  // Use actual weight
		},
		{
			name:           "Volume weight larger than actual",
			weight:         1.0,
			length:         50,
			width:          40,
			height:         30, // 60000 cm³ / 6000 = 10 kg
			expectedWeight: 10, // Use volume weight
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			req := &model.PriceRequest{
				Package: model.PackageInfo{
					Weight: test.weight,
					Length: test.length,
					Width:  test.width,
					Height: test.height,
				},
			}

			result := adapter.calculateChargedWeight(req, "SF")
			assert.Equal(t, test.expectedWeight, result)
		})
	}
}

// TestKuaidiNiaoAdapter_parseTime tests time parsing
func TestKuaidiNiaoAdapter_parseTime(t *testing.T) {
	config := KuaidiNiaoConfig{
		EBusinessID: "test_business_id",
		ApiKey:      "test_api_key",
		BaseURL:     "https://api.test.com",
		Environment: "test",
		Timeout:     10,
	}

	adapter := NewKuaidiNiaoAdapter(config, nil)

	tests := []struct {
		name      string
		input     string
		shouldErr bool
	}{
		{
			name:      "Standard format",
			input:     "2023-12-25 15:30:45",
			shouldErr: false,
		},
		{
			name:      "Slash format",
			input:     "2023/12/25 15:30:45",
			shouldErr: false,
		},
		{
			name:      "ISO format",
			input:     "2023-12-25T15:30:45Z",
			shouldErr: false,
		},
		{
			name:      "Invalid format",
			input:     "invalid-time",
			shouldErr: true,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			result, err := adapter.parseTrackTime(test.input)
			
			if test.shouldErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.False(t, result.IsZero())
			}
		})
	}
}

// TestKuaidiNiaoAdapter_mapTrackStatus tests status mapping
func TestKuaidiNiaoAdapter_mapTrackStatus(t *testing.T) {
	config := KuaidiNiaoConfig{
		EBusinessID: "test_business_id",
		ApiKey:      "test_api_key",
		BaseURL:     "https://api.test.com",
		Environment: "test",
		Timeout:     10,
	}

	adapter := NewKuaidiNiaoAdapter(config, nil)

	tests := []struct {
		state    string
		stateEx  string
		expected string
	}{
		{"1", "", "picked_up"},
		{"2", "", "in_transit"},
		{"3", "", "delivered"},
		{"4", "", "exception"},
		{"0", "", "no_info"},
		{"", "302", "delivered"},  // Detailed status takes priority
		{"", "304", "rejected"},   // Detailed status
		{"", "311", "lost"},       // Detailed status
		{"5", "", "unknown"},      // Unknown status
	}

	for _, test := range tests {
		result := adapter.mapTrackStatus(test.state, test.stateEx)
		assert.Equal(t, test.expected, result, 
			"Failed for state: %s, stateEx: %s", test.state, test.stateEx)
	}
}

// TestKuaidiNiaoAdapter_isRetryableError tests error retry logic
func TestKuaidiNiaoAdapter_isRetryableError(t *testing.T) {
	config := KuaidiNiaoConfig{
		EBusinessID: "test_business_id",
		ApiKey:      "test_api_key",
		BaseURL:     "https://api.test.com",
		Environment: "test",
		Timeout:     10,
	}

	adapter := NewKuaidiNiaoAdapter(config, nil)

	tests := []struct {
		errorMsg  string
		retryable bool
	}{
		{"timeout error", true},
		{"connection refused", true},
		{"503 service unavailable", true},
		{"502 bad gateway", true},
		{"500 internal server error", true},
		{"401 unauthorized", false},
		{"404 not found", false},
		{"validation error", false},
	}

	for _, test := range tests {
		err := &testError{message: test.errorMsg}
		result := adapter.isRetryableError(err)
		assert.Equal(t, test.retryable, result, 
			"Failed for error: %s", test.errorMsg)
	}
}

// TestKuaidiNiaoAdapter_validatePriceRequest tests price request validation
func TestKuaidiNiaoAdapter_validatePriceRequest(t *testing.T) {
	config := KuaidiNiaoConfig{
		EBusinessID: "test_business_id",
		ApiKey:      "test_api_key",
		BaseURL:     "https://api.test.com",
		Environment: "test",
		Timeout:     10,
	}

	adapter := NewKuaidiNiaoAdapter(config, nil)

	tests := []struct {
		name      string
		request   *model.PriceRequest
		shouldErr bool
	}{
		{
			name: "Valid request",
			request: &model.PriceRequest{
				Sender: model.SenderInfo{
					Province: "广东省",
					City:     "深圳市",
				},
				Receiver: model.ReceiverInfo{
					Province: "北京市",
					City:     "北京市",
				},
				Package: model.PackageInfo{
					Weight: 2.5,
				},
			},
			shouldErr: false,
		},
		{
			name: "Missing sender province",
			request: &model.PriceRequest{
				Sender: model.SenderInfo{
					City: "深圳市",
				},
				Receiver: model.ReceiverInfo{
					Province: "北京市",
					City:     "北京市",
				},
				Package: model.PackageInfo{
					Weight: 2.5,
				},
			},
			shouldErr: true,
		},
		{
			name: "Invalid weight",
			request: &model.PriceRequest{
				Sender: model.SenderInfo{
					Province: "广东省",
					City:     "深圳市",
				},
				Receiver: model.ReceiverInfo{
					Province: "北京市",
					City:     "北京市",
				},
				Package: model.PackageInfo{
					Weight: 0,
				},
			},
			shouldErr: true,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			err := adapter.validatePriceRequest(test.request)
			
			if test.shouldErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// TestKuaidiNiaoAdapter_validateOrderRequest tests order request validation
func TestKuaidiNiaoAdapter_validateOrderRequest(t *testing.T) {
	config := KuaidiNiaoConfig{
		EBusinessID: "test_business_id",
		ApiKey:      "test_api_key",
		BaseURL:     "https://api.test.com",
		Environment: "test",
		Timeout:     10,
	}

	adapter := NewKuaidiNiaoAdapter(config, nil)

	tests := []struct {
		name      string
		request   *model.OrderRequest
		shouldErr bool
	}{
		{
			name: "Valid request",
			request: &model.OrderRequest{
				CustomerOrderNo: "TEST123",
				Sender: model.SenderInfo{
					Name:    "张三",
					Mobile:  "***********",
					Address: "深圳市南山区",
				},
				Receiver: model.ReceiverInfo{
					Name:    "李四",
					Mobile:  "***********",
					Address: "北京市朝阳区",
				},
				Package: model.PackageInfo{
					GoodsName: "测试商品",
				},
			},
			shouldErr: false,
		},
		{
			name: "Missing customer order no",
			request: &model.OrderRequest{
				Sender: model.SenderInfo{
					Name:    "张三",
					Mobile:  "***********",
					Address: "深圳市南山区",
				},
			},
			shouldErr: true,
		},
		{
			name: "Missing sender info",
			request: &model.OrderRequest{
				CustomerOrderNo: "TEST123",
				Sender: model.SenderInfo{
					Mobile: "***********",
				},
			},
			shouldErr: true,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			err := adapter.validateOrderRequest(test.request)
			
			if test.shouldErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// Helper type for testing errors
type testError struct {
	message string
}

func (e *testError) Error() string {
	return e.message
}

// Benchmark tests
func BenchmarkKuaidiNiaoAdapter_generateSignature(b *testing.B) {
	config := KuaidiNiaoConfig{
		EBusinessID: "test_business_id",
		ApiKey:      "test_api_key",
		BaseURL:     "https://api.test.com",
		Environment: "test",
		Timeout:     10,
	}

	adapter := NewKuaidiNiaoAdapter(config, nil)
	testData := `{"test": "data", "weight": 2.5, "from": "深圳", "to": "北京"}`

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		adapter.generateSignature(testData)
	}
}

func BenchmarkKuaidiNiaoAdapter_calculateChargedWeight(b *testing.B) {
	config := KuaidiNiaoConfig{
		EBusinessID: "test_business_id",
		ApiKey:      "test_api_key",
		BaseURL:     "https://api.test.com",
		Environment: "test",
		Timeout:     10,
	}

	adapter := NewKuaidiNiaoAdapter(config, nil)
	req := &model.PriceRequest{
		Package: model.PackageInfo{
			Weight: 2.5,
			Length: 30,
			Width:  20,
			Height: 15,
		},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		adapter.calculateChargedWeight(req, "SF")
	}
}