package workorder

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/your-org/go-kuaidi/internal/adapter"
	"github.com/your-org/go-kuaidi/internal/model"
	"go.uber.org/zap"
)

// MockWorkOrderRepository 模拟工单仓储
type MockWorkOrderRepository struct {
	mock.Mock
}

func (m *MockWorkOrderRepository) Create(ctx context.Context, workOrder *model.WorkOrder) error {
	args := m.Called(ctx, workOrder)
	return args.Error(0)
}

func (m *MockWorkOrderRepository) GetByID(ctx context.Context, id uuid.UUID) (*model.WorkOrder, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*model.WorkOrder), args.Error(1)
}

func (m *MockWorkOrderRepository) GetByProviderWorkOrderID(ctx context.Context, provider, providerWorkOrderID string) (*model.WorkOrder, error) {
	args := m.Called(ctx, provider, providerWorkOrderID)
	return args.Get(0).(*model.WorkOrder), args.Error(1)
}

func (m *MockWorkOrderRepository) Update(ctx context.Context, workOrder *model.WorkOrder) error {
	args := m.Called(ctx, workOrder)
	return args.Error(0)
}

func (m *MockWorkOrderRepository) List(ctx context.Context, userID string, req *model.WorkOrderListRequest) (*model.WorkOrderListResponse, error) {
	args := m.Called(ctx, userID, req)
	return args.Get(0).(*model.WorkOrderListResponse), args.Error(1)
}

func (m *MockWorkOrderRepository) GetTypeMapping(ctx context.Context, unifiedType int, provider string) (*model.WorkOrderTypeMapping, error) {
	args := m.Called(ctx, unifiedType, provider)
	return args.Get(0).(*model.WorkOrderTypeMapping), args.Error(1)
}

func (m *MockWorkOrderRepository) GetSupportedTypes(ctx context.Context, provider string) ([]model.WorkOrderTypeMapping, error) {
	args := m.Called(ctx, provider)
	return args.Get(0).([]model.WorkOrderTypeMapping), args.Error(1)
}

func (m *MockWorkOrderRepository) CreateReply(ctx context.Context, reply *model.WorkOrderReply) error {
	args := m.Called(ctx, reply)
	return args.Error(0)
}

func (m *MockWorkOrderRepository) GetReplies(ctx context.Context, workOrderID string) ([]model.WorkOrderReply, error) {
	args := m.Called(ctx, workOrderID)
	return args.Get(0).([]model.WorkOrderReply), args.Error(1)
}

func (m *MockWorkOrderRepository) CreateAttachment(ctx context.Context, attachment *model.WorkOrderAttachment) error {
	args := m.Called(ctx, attachment)
	return args.Error(0)
}

func (m *MockWorkOrderRepository) GetAttachments(ctx context.Context, workOrderID string) ([]model.WorkOrderAttachment, error) {
	args := m.Called(ctx, workOrderID)
	return args.Get(0).([]model.WorkOrderAttachment), args.Error(1)
}

func (m *MockWorkOrderRepository) Delete(ctx context.Context, id uuid.UUID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockWorkOrderRepository) ExistsByCustomerWorkOrderID(ctx context.Context, userID, customerWorkOrderID string) (bool, error) {
	args := m.Called(ctx, userID, customerWorkOrderID)
	return args.Bool(0), args.Error(1)
}

func (m *MockWorkOrderRepository) GetAttachmentsByReplyID(ctx context.Context, replyID uuid.UUID) ([]model.WorkOrderAttachment, error) {
	args := m.Called(ctx, replyID)
	return args.Get(0).([]model.WorkOrderAttachment), args.Error(1)
}

func (m *MockWorkOrderRepository) GetAttachmentsByWorkOrderID(ctx context.Context, workOrderID uuid.UUID) ([]model.WorkOrderAttachment, error) {
	args := m.Called(ctx, workOrderID)
	return args.Get(0).([]model.WorkOrderAttachment), args.Error(1)
}

func (m *MockWorkOrderRepository) GetProviderStatusMapping(ctx context.Context, providerStatus int, provider string) (*model.WorkOrderStatusMapping, error) {
	args := m.Called(ctx, providerStatus, provider)
	return args.Get(0).(*model.WorkOrderStatusMapping), args.Error(1)
}

func (m *MockWorkOrderRepository) GetProviderTypeMapping(ctx context.Context, providerType int, provider string) (*model.WorkOrderTypeMapping, error) {
	args := m.Called(ctx, providerType, provider)
	return args.Get(0).(*model.WorkOrderTypeMapping), args.Error(1)
}

func (m *MockWorkOrderRepository) GetRepliesByWorkOrderID(ctx context.Context, workOrderID uuid.UUID) ([]model.WorkOrderReply, error) {
	args := m.Called(ctx, workOrderID)
	return args.Get(0).([]model.WorkOrderReply), args.Error(1)
}

func (m *MockWorkOrderRepository) GetStatusMapping(ctx context.Context, unifiedStatus int, provider string) (*model.WorkOrderStatusMapping, error) {
	args := m.Called(ctx, unifiedStatus, provider)
	return args.Get(0).(*model.WorkOrderStatusMapping), args.Error(1)
}

func (m *MockWorkOrderRepository) ListAllSupportedTypes(ctx context.Context) ([]model.WorkOrderTypeMapping, error) {
	args := m.Called(ctx)
	return args.Get(0).([]model.WorkOrderTypeMapping), args.Error(1)
}

func (m *MockWorkOrderRepository) ListSupportedTypes(ctx context.Context, provider string) ([]model.WorkOrderTypeMapping, error) {
	args := m.Called(ctx, provider)
	return args.Get(0).([]model.WorkOrderTypeMapping), args.Error(1)
}

func (m *MockWorkOrderRepository) UpdateAttachmentWorkOrderID(ctx context.Context, attachmentIDs []uuid.UUID, workOrderID uuid.UUID) error {
	args := m.Called(ctx, attachmentIDs, workOrderID)
	return args.Error(0)
}

// TestKuaidiNiaoWorkOrderAdapter_CreateWorkOrder 测试快递鸟工单创建
func TestKuaidiNiaoWorkOrderAdapter_CreateWorkOrder(t *testing.T) {
	// 创建模拟HTTP服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 验证请求方法
		assert.Equal(t, "POST", r.Method)

		// 验证请求参数
		err := r.ParseForm()
		assert.NoError(t, err)

		assert.Equal(t, "1807", r.FormValue("RequestType"))
		assert.Equal(t, "test_business_id", r.FormValue("EBusinessID"))
		assert.Equal(t, "2", r.FormValue("DataType"))
		assert.NotEmpty(t, r.FormValue("RequestData"))
		assert.NotEmpty(t, r.FormValue("DataSign"))

		// 返回模拟响应
		response := adapter.KuaidiNiaoWorkOrderResponse{
			EBusinessID:     "test_business_id",
			ComplaintNumber: "WO2025012100001",
			Success:         true,
			ResultCode:      "100",
			Reason:          "工单创建成功",
		}

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
	}))
	defer server.Close()

	// 创建模拟仓储
	mockRepo := &MockWorkOrderRepository{}

	// 创建适配器
	logger := zap.NewNop()
	adapter := NewKuaidiNiaoWorkOrderAdapter(
		"test_business_id",
		"test_api_key",
		server.URL,
		mockRepo,
		nil, // 🔥 测试环境使用nil，适配器会回退到默认值
		logger,
	)

	// 准备测试数据
	orderNo := "TEST_ORDER_001"
	trackingNo := "TEST_TRACKING_001"
	req := &model.CreateWorkOrderRequest{
		OrderNo:        &orderNo,
		TrackingNo:     &trackingNo,
		WorkOrderType:  1, // 虚假揽件
		Content:        "测试工单内容",
		AttachmentURLs: []string{"http://example.com/image1.jpg"},
	}

	// 执行测试
	ctx := context.Background()
	result, err := adapter.CreateWorkOrder(ctx, req)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, "WO2025012100001", result.ProviderWorkOrderID)
	assert.Equal(t, 0, result.Status)
	assert.Equal(t, "工单创建成功", result.Message)

	// 验证返回数据
	data := result.Data
	assert.Equal(t, "WO2025012100001", data["complaint_number"])
	assert.Equal(t, "100", data["result_code"])
	assert.Equal(t, "test_business_id", data["e_business_id"])
	assert.Equal(t, 1, data["unified_type"])    // 统一工单类型
	assert.Equal(t, 5, data["kuaidiniao_type"]) // 快递鸟工单类型（催取件->超时揽件）
}

// TestKuaidiNiaoWorkOrderAdapter_CreateWorkOrder_ValidationError 测试工单创建参数验证错误
func TestKuaidiNiaoWorkOrderAdapter_CreateWorkOrder_ValidationError(t *testing.T) {
	mockRepo := &MockWorkOrderRepository{}
	logger := zap.NewNop()
	adapter := NewKuaidiNiaoWorkOrderAdapter(
		"test_business_id",
		"test_api_key",
		"http://example.com",
		mockRepo,
		nil, // 测试环境使用nil
		logger,
	)

	ctx := context.Background()

	// 测试空内容
	req := &model.CreateWorkOrderRequest{
		WorkOrderType: 1,
		Content:       "", // 空内容
	}

	result, err := adapter.CreateWorkOrder(ctx, req)
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "工单内容不能为空")

	// 测试空订单号
	req = &model.CreateWorkOrderRequest{
		WorkOrderType: 1,
		Content:       "测试内容",
		OrderNo:       nil, // 空订单号
	}

	result, err = adapter.CreateWorkOrder(ctx, req)
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "快递鸟工单必须提供订单号")

	// 测试不支持的工单类型
	orderNo := "TEST_ORDER_001"
	req = &model.CreateWorkOrderRequest{
		OrderNo:       &orderNo,
		WorkOrderType: 999, // 不支持的类型
		Content:       "测试内容",
	}

	result, err = adapter.CreateWorkOrder(ctx, req)
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "不支持的统一工单类型")
}

// TestKuaidiNiaoWorkOrderAdapter_CreateWorkOrder_APIError 测试API错误响应
func TestKuaidiNiaoWorkOrderAdapter_CreateWorkOrder_APIError(t *testing.T) {
	// 创建返回错误的模拟服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		response := adapter.KuaidiNiaoWorkOrderResponse{
			EBusinessID:     "test_business_id",
			ComplaintNumber: "",
			Success:         false,
			ResultCode:      "500",
			Reason:          "系统内部错误",
		}

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
	}))
	defer server.Close()

	mockRepo := &MockWorkOrderRepository{}
	logger := zap.NewNop()
	adapter := NewKuaidiNiaoWorkOrderAdapter(
		"test_business_id",
		"test_api_key",
		server.URL,
		mockRepo,
		nil, // 测试环境使用nil
		logger,
	)

	orderNo := "TEST_ORDER_001"
	req := &model.CreateWorkOrderRequest{
		OrderNo:       &orderNo,
		WorkOrderType: 1,
		Content:       "测试工单内容",
	}

	ctx := context.Background()
	result, err := adapter.CreateWorkOrder(ctx, req)

	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "快递鸟工单创建失败")
	assert.Contains(t, err.Error(), "系统内部错误")
	assert.Contains(t, err.Error(), "500")
}

// TestKuaidiNiaoWorkOrderAdapter_UploadAttachment 测试附件上传
func TestKuaidiNiaoWorkOrderAdapter_UploadAttachment(t *testing.T) {
	mockRepo := &MockWorkOrderRepository{}
	logger := zap.NewNop()
	adapter := NewKuaidiNiaoWorkOrderAdapter(
		"test_business_id",
		"test_api_key",
		"http://example.com",
		mockRepo,
		nil, // 测试环境使用nil
		logger,
	)

	ctx := context.Background()
	fileName := "test.jpg"
	fileContent := []byte("test file content")

	result, err := adapter.UploadAttachment(ctx, fileName, fileContent)

	assert.NoError(t, err)
	assert.NotEmpty(t, result)

	// 验证返回的是base64编码
	expected := "dGVzdCBmaWxlIGNvbnRlbnQ=" // "test file content" 的base64编码
	assert.Equal(t, expected, result)
}

// TestKuaidiNiaoWorkOrderAdapter_GenerateSignature 测试签名生成
func TestKuaidiNiaoWorkOrderAdapter_GenerateSignature(t *testing.T) {
	mockRepo := &MockWorkOrderRepository{}
	logger := zap.NewNop()
	adapter := &KuaidiNiaoWorkOrderAdapter{
		eBusinessID: "test_business_id",
		apiKey:      "test_api_key",
		baseURL:     "http://example.com",
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		logger: logger,
		repo:   mockRepo,
	}

	requestData := `{"test":"data"}`
	signature := adapter.generateSignature(requestData)

	assert.NotEmpty(t, signature)

	// 验证签名格式（应该是URL编码的base64字符串）
	assert.Contains(t, signature, "%")
}

// TestKuaidiNiaoWorkOrderAdapter_QueryWorkOrder 测试工单查询
func TestKuaidiNiaoWorkOrderAdapter_QueryWorkOrder(t *testing.T) {
	// 创建模拟HTTP服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 验证请求参数
		err := r.ParseForm()
		assert.NoError(t, err)
		assert.Equal(t, "1818", r.FormValue("RequestType"))

		// 返回模拟响应
		response := adapter.KuaidiNiaoWorkOrderQueryResponse{
			EBusinessID: "test_business_id",
			Success:     true,
			ResultCode:  "100",
			Reason:      "查询成功",
			Data: adapter.KuaidiNiaoWorkOrderQueryData{
				PageIndex:   1,
				SizePerPage: 20,
				TotalCount:  1,
				Rows: []adapter.KuaidiNiaoWorkOrderDetail{
					{
						CustomerID:     "test_business_id",
						KdnOrderCode:   "KDN2025012100000001",
						TicketType:     1,
						TicketTypeName: "虚假揽件",
						TicketNumber:   "WO2025012100001",
						CreateTime:     "2025-01-21 10:00:00",
						Status:         1,
						DealResult:     "处理中",
						Source:         1,
					},
				},
			},
		}

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
	}))
	defer server.Close()

	mockRepo := &MockWorkOrderRepository{}
	logger := zap.NewNop()
	adapter := &KuaidiNiaoWorkOrderAdapter{
		eBusinessID: "test_business_id",
		apiKey:      "test_api_key",
		baseURL:     server.URL,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		logger: logger,
		repo:   mockRepo,
	}

	ctx := context.Background()
	result, err := adapter.QueryWorkOrder(ctx, "WO2025012100001")

	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, "WO2025012100001", result.ProviderWorkOrderID)
	assert.Equal(t, 1, result.Status)
	assert.Equal(t, "查询成功", result.Message)

	// 验证返回数据
	data := result.Data
	assert.Equal(t, "KDN2025012100000001", data["kdn_order_code"])
	assert.Equal(t, 1, data["ticket_type"])
	assert.Equal(t, "虚假揽件", data["ticket_type_name"])
}

// TestKuaidiNiaoWorkOrderAdapter_ReplyWorkOrder 测试工单回复
func TestKuaidiNiaoWorkOrderAdapter_ReplyWorkOrder(t *testing.T) {
	mockRepo := &MockWorkOrderRepository{}
	logger := zap.NewNop()
	adapter := NewKuaidiNiaoWorkOrderAdapter(
		"test_business_id",
		"test_api_key",
		"http://example.com",
		mockRepo,
		nil, // 测试环境使用nil
		logger,
	)

	ctx := context.Background()
	err := adapter.ReplyWorkOrder(ctx, "WO2025012100001", "测试回复", []string{})

	// 快递鸟不支持API工单回复
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "快递鸟不支持API工单回复功能")
}

// TestKuaidiNiaoWorkOrderAdapter_DeleteWorkOrder 测试工单删除
func TestKuaidiNiaoWorkOrderAdapter_DeleteWorkOrder(t *testing.T) {
	mockRepo := &MockWorkOrderRepository{}
	logger := zap.NewNop()
	adapter := NewKuaidiNiaoWorkOrderAdapter(
		"test_business_id",
		"test_api_key",
		"http://example.com",
		mockRepo,
		nil, // 测试环境使用nil
		logger,
	)

	ctx := context.Background()
	err := adapter.DeleteWorkOrder(ctx, "WO2025012100001", "测试取消")

	// 快递鸟不支持API工单取消
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "快递鸟不支持API工单取消功能")
}

// TestKuaidiNiaoWorkOrderAdapter_ParseCallback 测试回调解析（空数据）
func TestKuaidiNiaoWorkOrderAdapter_ParseCallback(t *testing.T) {
	mockRepo := &MockWorkOrderRepository{}
	logger := zap.NewNop()
	adapter := NewKuaidiNiaoWorkOrderAdapter(
		"test_business_id",
		"test_api_key",
		"http://example.com",
		mockRepo,
		nil, // 测试环境使用nil
		logger,
	)

	ctx := context.Background()
	result, err := adapter.ParseCallback(ctx, map[string]interface{}{})

	// 空数据应该返回非工单回调数据错误
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "非工单回调数据")
}

// TestKuaidiNiaoWorkOrderAdapter_SafeStringPtr 测试安全字符串指针方法
func TestKuaidiNiaoWorkOrderAdapter_SafeStringPtr(t *testing.T) {
	mockRepo := &MockWorkOrderRepository{}
	logger := zap.NewNop()
	adapter := &KuaidiNiaoWorkOrderAdapter{
		eBusinessID: "test_business_id",
		apiKey:      "test_api_key",
		baseURL:     "http://example.com",
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		logger: logger,
		repo:   mockRepo,
	}

	// 测试非空指针
	testStr := "test_string"
	result := adapter.safeStringPtr(&testStr)
	assert.Equal(t, "test_string", result)

	// 测试空指针
	result = adapter.safeStringPtr(nil)
	assert.Equal(t, "", result)
}

// TestKuaidiNiaoWorkOrderAdapter_MapUnifiedTypeToKuaidiNiao 测试类型映射
func TestKuaidiNiaoWorkOrderAdapter_MapUnifiedTypeToKuaidiNiao(t *testing.T) {
	mockRepo := &MockWorkOrderRepository{}
	logger := zap.NewNop()
	adapter := &KuaidiNiaoWorkOrderAdapter{
		eBusinessID: "test_business_id",
		apiKey:      "test_api_key",
		baseURL:     "http://example.com",
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		logger: logger,
		repo:   mockRepo,
	}

	// 测试所有支持的类型映射
	testCases := []struct {
		unifiedType   int
		expectedType  int
		expectedError bool
	}{
		{1, 5, false},    // 催取件 -> 超时揽件
		{2, 3, false},    // 重量异常 -> 重量/运费异常
		{12, 10, false},  // 催派送 -> 物流长时间未更新
		{16, 10, false},  // 物流停滞 -> 物流长时间未更新
		{17, 15, false},  // 重新分配快递员 -> 联系不上快递员
		{19, 401, false}, // 取消订单 -> 拦截
		{999, 0, true},   // 不支持的类型
	}

	for _, tc := range testCases {
		result, err := adapter.mapUnifiedTypeToKuaidiNiao(tc.unifiedType)
		if tc.expectedError {
			assert.Error(t, err)
			assert.Equal(t, 0, result)
		} else {
			assert.NoError(t, err)
			assert.Equal(t, tc.expectedType, result)
		}
	}
}

// TestKuaidiNiaoWorkOrderAdapter_MapKuaidiniaoStatusToUnified 测试状态映射
func TestKuaidiNiaoWorkOrderAdapter_MapKuaidiniaoStatusToUnified(t *testing.T) {
	mockRepo := &MockWorkOrderRepository{}
	logger := zap.NewNop()
	adapter := &KuaidiNiaoWorkOrderAdapter{
		eBusinessID: "test_business_id",
		apiKey:      "test_api_key",
		baseURL:     "http://example.com",
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		logger: logger,
		repo:   mockRepo,
	}

	// 测试状态映射
	testCases := []struct {
		kuaidiniaoStatus int
		expectedStatus   int
	}{
		{0, 0},   // 待处理
		{1, 1},   // 处理中
		{2, 2},   // 已处理
		{999, 0}, // 未知状态，默认为待处理
	}

	for _, tc := range testCases {
		result := adapter.mapKuaidiniaoStatusToUnified(tc.kuaidiniaoStatus)
		assert.Equal(t, tc.expectedStatus, result)
	}
}

// TestKuaidiNiaoWorkOrderAdapter_MapStatusToName 测试状态名称映射
func TestKuaidiNiaoWorkOrderAdapter_MapStatusToName(t *testing.T) {
	mockRepo := &MockWorkOrderRepository{}
	logger := zap.NewNop()
	adapter := &KuaidiNiaoWorkOrderAdapter{
		eBusinessID: "test_business_id",
		apiKey:      "test_api_key",
		baseURL:     "http://example.com",
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		logger: logger,
		repo:   mockRepo,
	}

	// 测试状态名称映射
	testCases := []struct {
		status       int
		expectedName string
	}{
		{0, "待处理"},
		{1, "处理中"},
		{2, "已处理"},
		{999, "未知状态"},
	}

	for _, tc := range testCases {
		result := adapter.mapStatusToName(tc.status)
		assert.Equal(t, tc.expectedName, result)
	}
}

// TestKuaidiNiaoWorkOrderAdapter_ParseAttachmentURLs 测试附件URL解析
func TestKuaidiNiaoWorkOrderAdapter_ParseAttachmentURLs(t *testing.T) {
	mockRepo := &MockWorkOrderRepository{}
	logger := zap.NewNop()
	adapter := &KuaidiNiaoWorkOrderAdapter{
		eBusinessID: "test_business_id",
		apiKey:      "test_api_key",
		baseURL:     "http://example.com",
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		logger: logger,
		repo:   mockRepo,
	}

	// 测试空字符串
	result := adapter.parseAttachmentURLs("")
	assert.Equal(t, []string{}, result)

	// 测试单个URL
	result = adapter.parseAttachmentURLs("http://example.com/file1.jpg")
	assert.Equal(t, []string{"http://example.com/file1.jpg"}, result)

	// 测试多个URL
	result = adapter.parseAttachmentURLs("http://example.com/file1.jpg,http://example.com/file2.jpg")
	expected := []string{"http://example.com/file1.jpg", "http://example.com/file2.jpg"}
	assert.Equal(t, expected, result)

	// 测试包含空格的URL
	result = adapter.parseAttachmentURLs(" http://example.com/file1.jpg , http://example.com/file2.jpg ")
	assert.Equal(t, expected, result)

	// 测试包含空值的URL
	result = adapter.parseAttachmentURLs("http://example.com/file1.jpg,,http://example.com/file2.jpg")
	assert.Equal(t, expected, result)
}

// TestKuaidiNiaoWorkOrderAdapter_ExtractMobileFromRequest 测试手机号提取
func TestKuaidiNiaoWorkOrderAdapter_ExtractMobileFromRequest(t *testing.T) {
	mockRepo := &MockWorkOrderRepository{}
	logger := zap.NewNop()
	adapter := &KuaidiNiaoWorkOrderAdapter{
		eBusinessID: "test_business_id",
		apiKey:      "test_api_key",
		baseURL:     "http://example.com",
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		logger: logger,
		repo:   mockRepo,
	}

	// 测试空订单号和运单号
	req := &model.CreateWorkOrderRequest{
		WorkOrderType: 1,
		Content:       "测试内容",
	}
	result := adapter.extractMobileFromRequest(req)
	assert.Equal(t, "***********", result) // 默认值

	// 测试有订单号但查询不到
	orderNo := "TEST_ORDER_001"
	req.OrderNo = &orderNo
	result = adapter.extractMobileFromRequest(req)
	assert.Equal(t, "***********", result) // 默认值

	// 测试有运单号但查询不到
	req.OrderNo = nil
	trackingNo := "TEST_TRACKING_001"
	req.TrackingNo = &trackingNo
	result = adapter.extractMobileFromRequest(req)
	assert.Equal(t, "***********", result) // 默认值
}

// TestKuaidiNiaoWorkOrderAdapter_ExtractNameFromRequest 测试姓名提取
func TestKuaidiNiaoWorkOrderAdapter_ExtractNameFromRequest(t *testing.T) {
	mockRepo := &MockWorkOrderRepository{}
	logger := zap.NewNop()
	adapter := &KuaidiNiaoWorkOrderAdapter{
		eBusinessID: "test_business_id",
		apiKey:      "test_api_key",
		baseURL:     "http://example.com",
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		logger: logger,
		repo:   mockRepo,
	}

	// 测试空订单号和运单号
	req := &model.CreateWorkOrderRequest{
		WorkOrderType: 1,
		Content:       "测试内容",
	}
	result := adapter.extractNameFromRequest(req)
	assert.Equal(t, "客户", result) // 默认值

	// 测试有订单号但查询不到
	orderNo := "TEST_ORDER_001"
	req.OrderNo = &orderNo
	result = adapter.extractNameFromRequest(req)
	assert.Equal(t, "客户", result) // 默认值

	// 测试有运单号但查询不到
	req.OrderNo = nil
	trackingNo := "TEST_TRACKING_001"
	req.TrackingNo = &trackingNo
	result = adapter.extractNameFromRequest(req)
	assert.Equal(t, "客户", result) // 默认值
}

// TestKuaidiNiaoWorkOrderAdapter_ParseCallback_Success 测试回调解析成功
func TestKuaidiNiaoWorkOrderAdapter_ParseCallback_Success(t *testing.T) {
	mockRepo := &MockWorkOrderRepository{}
	logger := zap.NewNop()
	adapter := NewKuaidiNiaoWorkOrderAdapter(
		"test_business_id",
		"test_api_key",
		"http://example.com",
		mockRepo,
		nil, // 测试环境使用nil
		logger,
	)

	// 构建有效的回调数据
	callbackData := map[string]interface{}{
		"RequestType":     "103",
		"StatusCode":      "401",
		"TicketNumber":    "WO2025012100001",
		"KdnOrderCode":    "KDN2025012100000001",
		"DealResult":      "工单已处理完成",
		"Status":          float64(2),
		"DealResultFiles": "http://example.com/file1.jpg,http://example.com/file2.jpg",
		"CreateTime":      "2025-01-21 10:00:00",
	}

	ctx := context.Background()
	result, err := adapter.ParseCallback(ctx, callbackData)

	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, "WO2025012100001", result.ProviderWorkOrderID)
	assert.Equal(t, 2, result.Status)
	assert.Equal(t, "已处理", result.StatusName)
	assert.Equal(t, "工单已处理完成", result.Content)
	assert.Equal(t, "快递鸟客服", result.Committer)
	assert.Len(t, result.AttachmentURLs, 2)
	assert.Equal(t, "http://example.com/file1.jpg", result.AttachmentURLs[0])
	assert.Equal(t, "http://example.com/file2.jpg", result.AttachmentURLs[1])
}

// TestKuaidiNiaoWorkOrderAdapter_ParseCallback_InvalidType 测试回调解析类型错误
func TestKuaidiNiaoWorkOrderAdapter_ParseCallback_InvalidType(t *testing.T) {
	mockRepo := &MockWorkOrderRepository{}
	logger := zap.NewNop()
	adapter := NewKuaidiNiaoWorkOrderAdapter(
		"test_business_id",
		"test_api_key",
		"http://example.com",
		mockRepo,
		nil, // 测试环境使用nil
		logger,
	)

	ctx := context.Background()

	// 测试无效的数据类型
	result, err := adapter.ParseCallback(ctx, "invalid_data")
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "无效的回调数据类型")
}

// TestKuaidiNiaoWorkOrderAdapter_ParseCallback_InvalidRequestType 测试回调解析请求类型错误
func TestKuaidiNiaoWorkOrderAdapter_ParseCallback_InvalidRequestType(t *testing.T) {
	mockRepo := &MockWorkOrderRepository{}
	logger := zap.NewNop()
	adapter := NewKuaidiNiaoWorkOrderAdapter(
		"test_business_id",
		"test_api_key",
		"http://example.com",
		mockRepo,
		nil, // 测试环境使用nil
		logger,
	)

	ctx := context.Background()

	// 测试错误的RequestType
	callbackData := map[string]interface{}{
		"RequestType": "102", // 错误的类型
		"StatusCode":  "401",
	}

	result, err := adapter.ParseCallback(ctx, callbackData)
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "非工单回调数据")
}
