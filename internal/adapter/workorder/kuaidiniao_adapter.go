package workorder

import (
	"context"
	"crypto/md5"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/your-org/go-kuaidi/internal/adapter"
	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/repository"
	"github.com/your-org/go-kuaidi/internal/service"
	"go.uber.org/zap"
)

// KuaidiNiaoWorkOrderAdapter 快递鸟工单适配器
type KuaidiNiaoWorkOrderAdapter struct {
	eBusinessID string
	apiKey      string
	baseURL     string
	httpClient  *http.Client
	logger      *zap.Logger
	repo        repository.WorkOrderRepository
	orderRepo   repository.OrderRepository // 🔥 新增：用于查询订单信息获取真实手机号和姓名
}

// NewKuaidiNiaoWorkOrderAdapter 创建快递鸟工单适配器
func NewKuaidiNiaoWorkOrderAdapter(eBusinessID, apiKey, baseURL string, repo repository.WorkOrderRepository, orderRepo repository.OrderRepository, logger *zap.Logger) service.WorkOrderProviderAdapter {
	return &KuaidiNiaoWorkOrderAdapter{
		eBusinessID: eBusinessID,
		apiKey:      apiKey,
		baseURL:     baseURL,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		logger:    logger,
		repo:      repo,
		orderRepo: orderRepo, // 🔥 新增：注入订单仓储用于查询真实用户信息
	}
}

// CreateWorkOrder 创建工单
func (a *KuaidiNiaoWorkOrderAdapter) CreateWorkOrder(ctx context.Context, req *model.CreateWorkOrderRequest) (*service.ProviderWorkOrderResponse, error) {
	// 🔥 修复：直接使用服务层传递的快递鸟工单类型
	// 服务层已经完成了统一类型到快递鸟类型的转换
	kuaidiniaoWorkOrderType := req.WorkOrderType

	a.logger.Info("快递鸟工单创建开始",
		zap.String("provider", "kuaidiniao"),
		zap.Int("kuaidiniao_work_order_type", kuaidiniaoWorkOrderType),
		zap.String("order_no", a.safeStringPtr(req.OrderNo)),
		zap.String("tracking_no", a.safeStringPtr(req.TrackingNo)))

	// 1. 验证快递鸟特定的参数要求（不验证类型转换）
	if err := a.validateCreateWorkOrderRequest(req); err != nil {
		a.logger.Error("快递鸟工单参数验证失败", zap.Error(err))
		return nil, fmt.Errorf("快递鸟工单参数验证失败: %w", err)
	}

	// 2. 从ProviderSpecificData中获取平台订单号
	var platformOrderNo string
	if kuaidiniaoData, ok := req.ProviderSpecificData["kuaidiniao"].(map[string]interface{}); ok {
		if platformOrderNoValue, exists := kuaidiniaoData["platform_order_no"]; exists {
			if platformOrderNoStr, ok := platformOrderNoValue.(string); ok {
				platformOrderNo = platformOrderNoStr
			}
		}
	}

	// 如果没有平台订单号，回退到使用客户订单号
	if platformOrderNo == "" {
		platformOrderNo = a.safeStringPtr(req.OrderNo)
	}

	// 3. 构建快递鸟工单请求（过滤特殊字符）
	kuaidiniaoReq := &adapter.KuaidiNiaoWorkOrderRequest{
		Mobile:           a.filterSpecialChars(a.extractMobileFromRequest(req)),
		Name:             a.filterSpecialChars(a.extractNameFromRequest(req)),
		OrderCode:        a.filterSpecialChars(platformOrderNo), // 使用平台订单号作为商家订单号（与其他供应商保持一致）
		LogisticCode:     a.filterSpecialChars(a.safeStringPtr(req.TrackingNo)),
		ComplaintType:    kuaidiniaoWorkOrderType, // 使用已转换的快递鸟工单类型
		ComplaintContent: a.filterSpecialChars(req.Content),
		Source:           4, // 4：云工单(C端用户寄件前端提交) - 根据快递鸟官方文档推荐
	}

	// 4. 处理附件
	if len(req.AttachmentURLs) > 0 {
		kuaidiniaoReq.PicList = make([]adapter.KuaidiNiaoWorkOrderPicture, len(req.AttachmentURLs))
		for i, url := range req.AttachmentURLs {
			kuaidiniaoReq.PicList[i] = adapter.KuaidiNiaoWorkOrderPicture{
				PictureItem: url,
			}
		}
	}

	// 5. 调用快递鸟API
	response, err := a.callCreateWorkOrderAPI(ctx, kuaidiniaoReq)
	if err != nil {
		a.logger.Error("调用快递鸟工单创建API失败", zap.Error(err))
		return nil, fmt.Errorf("调用快递鸟工单创建API失败: %w", err)
	}

	// 5. 转换为统一响应格式
	result := &service.ProviderWorkOrderResponse{
		ProviderWorkOrderID: response.ComplaintNumber,
		Status:              0, // 快递鸟工单创建后默认为待处理状态
		Message:             response.Reason,
		Data: map[string]interface{}{
			"complaint_number": response.ComplaintNumber,
			"result_code":      response.ResultCode,
			"e_business_id":    response.EBusinessID,
			"kuaidiniao_type":  kuaidiniaoWorkOrderType, // 快递鸟工单类型
		},
	}

	a.logger.Info("快递鸟工单创建成功",
		zap.String("provider_work_order_id", result.ProviderWorkOrderID),
		zap.String("message", result.Message))

	return result, nil
}

// QueryWorkOrder 查询工单
func (a *KuaidiNiaoWorkOrderAdapter) QueryWorkOrder(ctx context.Context, providerWorkOrderID string) (*service.ProviderWorkOrderResponse, error) {
	a.logger.Info("快递鸟工单查询开始",
		zap.String("provider_work_order_id", providerWorkOrderID))

	// 1. 通过工单号查找对应的快递鸟订单号
	kdnOrderCode, err := a.findKdnOrderCodeByWorkOrderID(ctx, providerWorkOrderID)
	if err != nil {
		return nil, fmt.Errorf("查找快递鸟订单号失败: %w", err)
	}

	// 2. 构建查询请求
	queryReq := &adapter.KuaidiNiaoWorkOrderQueryRequest{
		PageIndex:     1,
		SizePerPage:   20,
		KdnOrderCodes: []string{kdnOrderCode},
		Source:        1, // 1：客户
	}

	// 3. 调用快递鸟查询API
	response, err := a.callQueryWorkOrderAPI(ctx, queryReq)
	if err != nil {
		return nil, fmt.Errorf("调用快递鸟工单查询API失败: %w", err)
	}

	// 4. 查找匹配的工单
	var workOrderDetail *adapter.KuaidiNiaoWorkOrderDetail
	for _, row := range response.Data.Rows {
		if row.TicketNumber == providerWorkOrderID {
			workOrderDetail = &row
			break
		}
	}

	if workOrderDetail == nil {
		return nil, fmt.Errorf("未找到工单: %s", providerWorkOrderID)
	}

	// 5. 转换为统一响应格式
	result := &service.ProviderWorkOrderResponse{
		ProviderWorkOrderID: workOrderDetail.TicketNumber,
		Status:              workOrderDetail.Status,
		Message:             "查询成功",
		Data: map[string]interface{}{
			"detail":           workOrderDetail,
			"kdn_order_code":   workOrderDetail.KdnOrderCode,
			"ticket_type":      workOrderDetail.TicketType,
			"ticket_type_name": workOrderDetail.TicketTypeName,
			"create_time":      workOrderDetail.CreateTime,
			"deal_result":      workOrderDetail.DealResult,
			"tracks":           workOrderDetail.TicketDetailTracks,
		},
	}

	a.logger.Info("快递鸟工单查询成功",
		zap.String("provider_work_order_id", providerWorkOrderID),
		zap.Int("status", result.Status))

	return result, nil
}

// ReplyWorkOrder 回复工单
func (a *KuaidiNiaoWorkOrderAdapter) ReplyWorkOrder(ctx context.Context, providerWorkOrderID string, content string, attachmentURLs []string) error {
	// 快递鸟API不支持工单回复功能
	// 工单回复只能通过快递鸟后台人工处理
	return fmt.Errorf("快递鸟不支持API工单回复功能")
}

// DeleteWorkOrder 删除/取消工单
func (a *KuaidiNiaoWorkOrderAdapter) DeleteWorkOrder(ctx context.Context, providerWorkOrderID string, reason string) error {
	// 快递鸟API不支持工单取消功能
	// 工单取消只能通过快递鸟后台人工处理
	return fmt.Errorf("快递鸟不支持API工单取消功能")
}

// UploadAttachment 上传附件
func (a *KuaidiNiaoWorkOrderAdapter) UploadAttachment(ctx context.Context, fileName string, fileContent []byte) (string, error) {
	// 快递鸟工单附件需要转换为base64编码
	// 或者使用外部图片URL
	base64Content := base64.StdEncoding.EncodeToString(fileContent)

	a.logger.Info("快递鸟工单附件上传",
		zap.String("file_name", fileName),
		zap.Int("file_size", len(fileContent)))

	// 返回base64编码的内容，供工单创建时使用
	return base64Content, nil
}

// ParseCallback 解析回调数据
func (a *KuaidiNiaoWorkOrderAdapter) ParseCallback(ctx context.Context, callbackData interface{}) (*model.WorkOrderCallbackData, error) {
	a.logger.Info("快递鸟工单回调解析开始", zap.Any("callback_data", callbackData))

	// 1. 类型断言，获取回调数据
	rawData, ok := callbackData.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("无效的回调数据类型: %T", callbackData)
	}

	// 2. 验证是否为工单回调（接口指令103，状态码401）
	requestType, _ := rawData["RequestType"].(string)
	statusCode, _ := rawData["StatusCode"].(string)

	if requestType != "103" || statusCode != "401" {
		return nil, fmt.Errorf("非工单回调数据: RequestType=%s, StatusCode=%s", requestType, statusCode)
	}

	// 3. 解析工单处理结果数据
	ticketNumber, _ := rawData["TicketNumber"].(string)
	kdnOrderCode, _ := rawData["KdnOrderCode"].(string)
	dealResult, _ := rawData["DealResult"].(string)
	status, _ := rawData["Status"].(float64)
	dealResultFiles, _ := rawData["DealResultFiles"].(string)

	// 4. 映射快递鸟状态到统一状态
	unifiedStatus := a.mapKuaidiniaoStatusToUnified(int(status))

	// 5. 构建工单回调数据
	callbackResult := &model.WorkOrderCallbackData{
		ProviderWorkOrderID: ticketNumber,
		Status:              unifiedStatus,
		StatusName:          a.mapStatusToName(unifiedStatus),
		Content:             dealResult,
		Committer:           "快递鸟客服",
		AttachmentURLs:      a.parseAttachmentURLs(dealResultFiles),
		UpdatedAt:           time.Now(),
	}

	a.logger.Info("快递鸟工单回调解析成功",
		zap.String("ticket_number", ticketNumber),
		zap.String("kdn_order_code", kdnOrderCode),
		zap.Int("status", int(status)),
		zap.Int("unified_status", unifiedStatus))

	return callbackResult, nil
}

// mapKuaidiniaoStatusToUnified 映射快递鸟工单状态到统一状态
func (a *KuaidiNiaoWorkOrderAdapter) mapKuaidiniaoStatusToUnified(kuaidiniaoStatus int) int {
	switch kuaidiniaoStatus {
	case 0:
		return 0 // 待处理
	case 1:
		return 1 // 处理中
	case 2:
		return 2 // 已处理
	default:
		a.logger.Warn("未知的快递鸟工单状态", zap.Int("status", kuaidiniaoStatus))
		return 0 // 默认为待处理
	}
}

// mapStatusToName 映射状态码到状态名称
func (a *KuaidiNiaoWorkOrderAdapter) mapStatusToName(status int) string {
	switch status {
	case 0:
		return "待处理"
	case 1:
		return "处理中"
	case 2:
		return "已处理"
	default:
		return "未知状态"
	}
}

// parseAttachmentURLs 解析附件URL列表
func (a *KuaidiNiaoWorkOrderAdapter) parseAttachmentURLs(dealResultFiles string) []string {
	if dealResultFiles == "" {
		return []string{}
	}

	// 快递鸟的附件可能是逗号分隔的URL列表
	// 这里简单处理，实际可能需要根据具体格式调整
	urls := strings.Split(dealResultFiles, ",")
	result := make([]string, 0, len(urls))

	for _, url := range urls {
		url = strings.TrimSpace(url)
		if url != "" {
			result = append(result, url)
		}
	}

	return result
}

// QuerySupportedTypes 查询快递鸟支持的工单类型
func (a *KuaidiNiaoWorkOrderAdapter) QuerySupportedTypes(ctx context.Context) ([]adapter.KuaidiNiaoWorkOrderType, error) {
	// 1. 构建请求数据（空JSON对象）
	requestData := "{}"

	// 2. 生成签名
	dataSign := a.generateSignature(requestData)

	// 3. 构建系统级参数
	params := url.Values{}
	params.Set("RequestType", "1817") // 工单类型查询接口
	params.Set("EBusinessID", a.eBusinessID)
	params.Set("RequestData", requestData)
	params.Set("DataSign", dataSign)
	params.Set("DataType", "2")

	// 4. 发送HTTP请求
	resp, err := a.httpClient.PostForm(a.baseURL, params)
	if err != nil {
		return nil, fmt.Errorf("发送HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 5. 解析响应
	var response adapter.KuaidiNiaoWorkOrderTypesResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	// 6. 检查响应结果
	if !response.Success {
		return nil, fmt.Errorf("快递鸟工单类型查询失败: %s (代码: %s)", response.Reason, response.ResultCode)
	}

	a.logger.Info("快递鸟工单类型查询成功",
		zap.Int("type_count", len(response.Data)),
		zap.String("result_code", response.ResultCode))

	return response.Data, nil
}

// validateCreateWorkOrderRequest 验证创建工单请求
func (a *KuaidiNiaoWorkOrderAdapter) validateCreateWorkOrderRequest(req *model.CreateWorkOrderRequest) error {
	if req.Content == "" {
		return fmt.Errorf("工单内容不能为空")
	}

	if req.OrderNo == nil || *req.OrderNo == "" {
		return fmt.Errorf("快递鸟工单必须提供订单号")
	}

	// 🔥 修复：验证快递鸟供应商类型，而不是统一类型
	// req.WorkOrderType 在这里已经是转换后的快递鸟供应商类型
	supportedKuaidiniaoTypes := map[int]string{
		5:   "超时揽件",     // 统一类型1(催取件) -> 快递鸟类型5
		3:   "重量/运费异常",  // 统一类型2(重量异常) -> 快递鸟类型3
		10:  "物流长时间未更新", // 统一类型12(催派送) 和 统一类型16(物流停滞) -> 快递鸟类型10
		15:  "联系不上快递员",  // 统一类型17(重新分配快递员) -> 快递鸟类型15
		401: "拦截",       // 统一类型19(取消订单) -> 快递鸟类型401
	}

	if _, exists := supportedKuaidiniaoTypes[req.WorkOrderType]; !exists {
		return fmt.Errorf("不支持的快递鸟工单类型: %d", req.WorkOrderType)
	}

	a.logger.Info("快递鸟工单参数验证通过",
		zap.String("order_no", a.safeStringPtr(req.OrderNo)),
		zap.Int("kuaidiniao_type", req.WorkOrderType),
		zap.String("type_name", supportedKuaidiniaoTypes[req.WorkOrderType]),
		zap.String("content", req.Content))

	return nil
}

// mapUnifiedTypeToKuaidiNiao 将统一工单类型映射为快递鸟类型
func (a *KuaidiNiaoWorkOrderAdapter) mapUnifiedTypeToKuaidiNiao(unifiedType int) (int, error) {
	switch unifiedType {
	case 1: // 催取件 -> 超时揽件
		return 5, nil
	case 2: // 重量异常 -> 重量/运费异常
		return 3, nil
	case 12: // 催派送 -> 物流长时间未更新
		return 10, nil
	case 16: // 物流停滞 -> 物流长时间未更新
		return 10, nil
	case 17: // 重新分配快递员 -> 联系不上快递员
		return 15, nil
	case 19: // 取消订单 -> 拦截
		return 401, nil
	default:
		return 0, fmt.Errorf("不支持的统一工单类型: %d", unifiedType)
	}
}

// extractMobileFromRequest 从请求中提取手机号
func (a *KuaidiNiaoWorkOrderAdapter) extractMobileFromRequest(req *model.CreateWorkOrderRequest) string {
	// 优先使用订单号查询订单信息
	if req.OrderNo != nil && *req.OrderNo != "" {
		if mobile := a.getMobileFromOrder(*req.OrderNo); mobile != "" {
			return mobile
		}
	}

	// 其次使用运单号查询订单信息
	if req.TrackingNo != nil && *req.TrackingNo != "" {
		if mobile := a.getMobileFromTrackingNo(*req.TrackingNo); mobile != "" {
			return mobile
		}
	}

	// 最后使用默认值（用于测试或紧急情况）
	a.logger.Warn("无法获取真实手机号，使用默认值",
		zap.String("order_no", a.safeStringPtr(req.OrderNo)),
		zap.String("tracking_no", a.safeStringPtr(req.TrackingNo)))
	return "13800138000"
}

// extractNameFromRequest 从请求中提取姓名
func (a *KuaidiNiaoWorkOrderAdapter) extractNameFromRequest(req *model.CreateWorkOrderRequest) string {
	// 优先使用订单号查询订单信息
	if req.OrderNo != nil && *req.OrderNo != "" {
		if name := a.getNameFromOrder(*req.OrderNo); name != "" {
			return name
		}
	}

	// 其次使用运单号查询订单信息
	if req.TrackingNo != nil && *req.TrackingNo != "" {
		if name := a.getNameFromTrackingNo(*req.TrackingNo); name != "" {
			return name
		}
	}

	// 最后使用默认值（用于测试或紧急情况）
	a.logger.Warn("无法获取真实姓名，使用默认值",
		zap.String("order_no", a.safeStringPtr(req.OrderNo)),
		zap.String("tracking_no", a.safeStringPtr(req.TrackingNo)))
	return "客户"
}

// getMobileFromOrder 从订单号获取手机号
func (a *KuaidiNiaoWorkOrderAdapter) getMobileFromOrder(orderNo string) string {
	// 🔥 处理测试环境或未注入OrderRepository的情况
	if a.orderRepo == nil {
		a.logger.Debug("OrderRepository未注入，跳过数据库查询", zap.String("order_no", orderNo))
		return ""
	}

	ctx := context.Background()

	// 查询订单信息
	order, err := a.orderRepo.FindByOrderNo(ctx, orderNo)
	if err != nil {
		a.logger.Warn("根据订单号查询订单失败",
			zap.String("order_no", orderNo),
			zap.Error(err))
		return ""
	}

	// 优先使用发件人手机号
	if senderMobile := a.extractMobileFromSenderInfo(order.SenderInfo); senderMobile != "" {
		a.logger.Debug("从发件人信息获取手机号成功",
			zap.String("order_no", orderNo),
			zap.String("mobile", senderMobile))
		return senderMobile
	}

	// 备选：使用收件人手机号
	if receiverMobile := a.extractMobileFromReceiverInfo(order.ReceiverInfo); receiverMobile != "" {
		a.logger.Debug("从收件人信息获取手机号成功",
			zap.String("order_no", orderNo),
			zap.String("mobile", receiverMobile))
		return receiverMobile
	}

	a.logger.Warn("无法从订单信息中提取手机号", zap.String("order_no", orderNo))
	return ""
}

// getMobileFromTrackingNo 从运单号获取手机号
func (a *KuaidiNiaoWorkOrderAdapter) getMobileFromTrackingNo(trackingNo string) string {
	// 🔥 处理测试环境或未注入OrderRepository的情况
	if a.orderRepo == nil {
		a.logger.Debug("OrderRepository未注入，跳过数据库查询", zap.String("tracking_no", trackingNo))
		return ""
	}

	ctx := context.Background()

	// 查询订单信息
	order, err := a.orderRepo.FindByTrackingNo(ctx, trackingNo)
	if err != nil {
		a.logger.Warn("根据运单号查询订单失败",
			zap.String("tracking_no", trackingNo),
			zap.Error(err))
		return ""
	}

	// 优先使用发件人手机号
	if senderMobile := a.extractMobileFromSenderInfo(order.SenderInfo); senderMobile != "" {
		a.logger.Debug("从发件人信息获取手机号成功",
			zap.String("tracking_no", trackingNo),
			zap.String("mobile", senderMobile))
		return senderMobile
	}

	// 备选：使用收件人手机号
	if receiverMobile := a.extractMobileFromReceiverInfo(order.ReceiverInfo); receiverMobile != "" {
		a.logger.Debug("从收件人信息获取手机号成功",
			zap.String("tracking_no", trackingNo),
			zap.String("mobile", receiverMobile))
		return receiverMobile
	}

	a.logger.Warn("无法从订单信息中提取手机号", zap.String("tracking_no", trackingNo))
	return ""
}

// getNameFromOrder 从订单号获取姓名
func (a *KuaidiNiaoWorkOrderAdapter) getNameFromOrder(orderNo string) string {
	// 🔥 处理测试环境或未注入OrderRepository的情况
	if a.orderRepo == nil {
		a.logger.Debug("OrderRepository未注入，跳过数据库查询", zap.String("order_no", orderNo))
		return ""
	}

	ctx := context.Background()

	// 查询订单信息
	order, err := a.orderRepo.FindByOrderNo(ctx, orderNo)
	if err != nil {
		a.logger.Warn("根据订单号查询订单失败",
			zap.String("order_no", orderNo),
			zap.Error(err))
		return ""
	}

	// 优先使用发件人姓名
	if senderName := a.extractNameFromSenderInfo(order.SenderInfo); senderName != "" {
		a.logger.Debug("从发件人信息获取姓名成功",
			zap.String("order_no", orderNo),
			zap.String("name", senderName))
		return senderName
	}

	// 备选：使用收件人姓名
	if receiverName := a.extractNameFromReceiverInfo(order.ReceiverInfo); receiverName != "" {
		a.logger.Debug("从收件人信息获取姓名成功",
			zap.String("order_no", orderNo),
			zap.String("name", receiverName))
		return receiverName
	}

	a.logger.Warn("无法从订单信息中提取姓名", zap.String("order_no", orderNo))
	return ""
}

// getNameFromTrackingNo 从运单号获取姓名
func (a *KuaidiNiaoWorkOrderAdapter) getNameFromTrackingNo(trackingNo string) string {
	// 🔥 处理测试环境或未注入OrderRepository的情况
	if a.orderRepo == nil {
		a.logger.Debug("OrderRepository未注入，跳过数据库查询", zap.String("tracking_no", trackingNo))
		return ""
	}

	ctx := context.Background()

	// 查询订单信息
	order, err := a.orderRepo.FindByTrackingNo(ctx, trackingNo)
	if err != nil {
		a.logger.Warn("根据运单号查询订单失败",
			zap.String("tracking_no", trackingNo),
			zap.Error(err))
		return ""
	}

	// 优先使用发件人姓名
	if senderName := a.extractNameFromSenderInfo(order.SenderInfo); senderName != "" {
		a.logger.Debug("从发件人信息获取姓名成功",
			zap.String("tracking_no", trackingNo),
			zap.String("name", senderName))
		return senderName
	}

	// 备选：使用收件人姓名
	if receiverName := a.extractNameFromReceiverInfo(order.ReceiverInfo); receiverName != "" {
		a.logger.Debug("从收件人信息获取姓名成功",
			zap.String("tracking_no", trackingNo),
			zap.String("name", receiverName))
		return receiverName
	}

	a.logger.Warn("无法从订单信息中提取姓名", zap.String("tracking_no", trackingNo))
	return ""
}

// callCreateWorkOrderAPI 调用快递鸟工单创建API
func (a *KuaidiNiaoWorkOrderAdapter) callCreateWorkOrderAPI(ctx context.Context, req *adapter.KuaidiNiaoWorkOrderRequest) (*adapter.KuaidiNiaoWorkOrderResponse, error) {
	// 1. 序列化请求数据
	requestData, err := json.Marshal(req)
	if err != nil {
		a.logger.Error("序列化请求数据失败", zap.Error(err))
		return nil, fmt.Errorf("序列化请求数据失败: %w", err)
	}

	a.logger.Info("🔍 快递鸟工单API请求数据",
		zap.String("request_data", string(requestData)),
		zap.Any("request_struct", req))

	// 2. 生成签名
	dataSign := a.generateSignature(string(requestData))

	a.logger.Info("🔐 快递鸟工单API签名信息",
		zap.String("e_business_id", a.eBusinessID),
		zap.String("api_key_prefix", a.apiKey[:8]+"..."),
		zap.String("data_sign", dataSign))

	// 3. 构建系统级参数
	params := url.Values{}
	params.Set("RequestType", "1807") // 工单提交接口
	params.Set("EBusinessID", a.eBusinessID)
	params.Set("RequestData", string(requestData))
	params.Set("DataSign", dataSign)
	params.Set("DataType", "2")

	a.logger.Info("🌐 快递鸟工单API请求参数",
		zap.String("base_url", a.baseURL),
		zap.String("request_type", "1807"),
		zap.String("e_business_id", a.eBusinessID),
		zap.String("data_type", "2"),
		zap.Int("request_data_length", len(string(requestData))))

	// 4. 发送HTTP请求
	resp, err := a.httpClient.PostForm(a.baseURL, params)
	if err != nil {
		a.logger.Error("发送HTTP请求失败",
			zap.Error(err),
			zap.String("base_url", a.baseURL))
		return nil, fmt.Errorf("发送HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	a.logger.Debug("📡 快递鸟工单API响应状态",
		zap.Int("status_code", resp.StatusCode),
		zap.String("status", resp.Status))

	// 5. 读取响应体
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		a.logger.Error("读取响应体失败", zap.Error(err))
		return nil, fmt.Errorf("读取响应体失败: %w", err)
	}

	a.logger.Info("📥 快递鸟工单API原始响应",
		zap.String("response_body", string(responseBody)))

	// 6. 解析响应
	var response adapter.KuaidiNiaoWorkOrderResponse
	if err := json.Unmarshal(responseBody, &response); err != nil {
		a.logger.Error("解析响应失败",
			zap.Error(err),
			zap.String("response_body", string(responseBody)))
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	a.logger.Debug("📋 快递鸟工单API解析结果",
		zap.Bool("success", response.Success),
		zap.String("result_code", response.ResultCode),
		zap.String("reason", response.Reason),
		zap.String("complaint_number", response.ComplaintNumber))

	// 7. 检查响应结果
	if !response.Success {
		a.logger.Error("快递鸟工单创建失败",
			zap.String("result_code", response.ResultCode),
			zap.String("reason", response.Reason),
			zap.String("complaint_number", response.ComplaintNumber))
		return nil, fmt.Errorf("快递鸟工单创建失败: %s (代码: %s)", response.Reason, response.ResultCode)
	}

	a.logger.Info("✅ 快递鸟工单创建成功",
		zap.String("complaint_number", response.ComplaintNumber),
		zap.String("result_code", response.ResultCode))

	return &response, nil
}

// generateSignature 生成签名 - 与快递鸟下单API保持一致的签名算法
func (a *KuaidiNiaoWorkOrderAdapter) generateSignature(requestData string) string {
	// 拼接字符串: RequestData + ApiKey
	signData := requestData + a.apiKey

	// MD5加密(32位小写)
	h := md5.New()
	h.Write([]byte(signData))
	md5str := fmt.Sprintf("%x", h.Sum(nil))

	// Base64编码（与下单API保持一致）
	return base64.StdEncoding.EncodeToString([]byte(md5str))
}

// safeStringPtr 安全地获取字符串指针的值
func (a *KuaidiNiaoWorkOrderAdapter) safeStringPtr(ptr *string) string {
	if ptr == nil {
		return ""
	}
	return *ptr
}

// findKdnOrderCodeByWorkOrderID 通过工单号查找快递鸟订单号
func (a *KuaidiNiaoWorkOrderAdapter) findKdnOrderCodeByWorkOrderID(ctx context.Context, workOrderID string) (string, error) {
	// TODO: 实现通过工单号查找快递鸟订单号的逻辑
	// 这里需要查询数据库中的工单记录，获取对应的快递鸟订单号
	// 暂时返回模拟数据
	return "KDN2025012100000001", nil
}

// callQueryWorkOrderAPI 调用快递鸟工单查询API
func (a *KuaidiNiaoWorkOrderAdapter) callQueryWorkOrderAPI(ctx context.Context, req *adapter.KuaidiNiaoWorkOrderQueryRequest) (*adapter.KuaidiNiaoWorkOrderQueryResponse, error) {
	// 1. 序列化请求数据
	requestData, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("序列化请求数据失败: %w", err)
	}

	// 2. 生成签名
	dataSign := a.generateSignature(string(requestData))

	// 3. 构建系统级参数
	params := url.Values{}
	params.Set("RequestType", "1818") // 工单详情查询接口
	params.Set("EBusinessID", a.eBusinessID)
	params.Set("RequestData", string(requestData))
	params.Set("DataSign", dataSign)
	params.Set("DataType", "2")

	// 4. 发送HTTP请求
	resp, err := a.httpClient.PostForm(a.baseURL, params)
	if err != nil {
		return nil, fmt.Errorf("发送HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 5. 解析响应
	var response adapter.KuaidiNiaoWorkOrderQueryResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	// 6. 检查响应结果
	if !response.Success {
		return nil, fmt.Errorf("快递鸟工单查询失败: %s (代码: %s)", response.Reason, response.ResultCode)
	}

	return &response, nil
}

// filterSpecialChars 过滤快递鸟不支持的特殊字符
// 根据快递鸟官方文档，不允许出现以下特殊字符： '   "   #    &    +    <   >   %   \  以及表情包、生僻字等
func (a *KuaidiNiaoWorkOrderAdapter) filterSpecialChars(input string) string {
	if input == "" {
		return input
	}

	// 定义需要过滤的特殊字符
	specialChars := []string{"'", "\"", "#", "&", "+", "<", ">", "%", "\\"}

	result := input
	for _, char := range specialChars {
		result = strings.ReplaceAll(result, char, "")
	}

	// 记录过滤日志（仅在有变化时）
	if result != input {
		a.logger.Debug("过滤特殊字符",
			zap.String("original", input),
			zap.String("filtered", result))
	}

	return result
}

// extractMobileFromSenderInfo 从发件人信息JSON中提取手机号
func (a *KuaidiNiaoWorkOrderAdapter) extractMobileFromSenderInfo(senderInfoJSON string) string {
	if senderInfoJSON == "" {
		return ""
	}

	var senderInfo model.SenderInfo
	if err := json.Unmarshal([]byte(senderInfoJSON), &senderInfo); err != nil {
		a.logger.Warn("解析发件人信息JSON失败",
			zap.String("sender_info", senderInfoJSON),
			zap.Error(err))
		return ""
	}

	return senderInfo.Mobile
}

// extractMobileFromReceiverInfo 从收件人信息JSON中提取手机号
func (a *KuaidiNiaoWorkOrderAdapter) extractMobileFromReceiverInfo(receiverInfoJSON string) string {
	if receiverInfoJSON == "" {
		return ""
	}

	var receiverInfo model.ReceiverInfo
	if err := json.Unmarshal([]byte(receiverInfoJSON), &receiverInfo); err != nil {
		a.logger.Warn("解析收件人信息JSON失败",
			zap.String("receiver_info", receiverInfoJSON),
			zap.Error(err))
		return ""
	}

	return receiverInfo.Mobile
}

// extractNameFromSenderInfo 从发件人信息JSON中提取姓名
func (a *KuaidiNiaoWorkOrderAdapter) extractNameFromSenderInfo(senderInfoJSON string) string {
	if senderInfoJSON == "" {
		return ""
	}

	var senderInfo model.SenderInfo
	if err := json.Unmarshal([]byte(senderInfoJSON), &senderInfo); err != nil {
		a.logger.Warn("解析发件人信息JSON失败",
			zap.String("sender_info", senderInfoJSON),
			zap.Error(err))
		return ""
	}

	return senderInfo.Name
}

// extractNameFromReceiverInfo 从收件人信息JSON中提取姓名
func (a *KuaidiNiaoWorkOrderAdapter) extractNameFromReceiverInfo(receiverInfoJSON string) string {
	if receiverInfoJSON == "" {
		return ""
	}

	var receiverInfo model.ReceiverInfo
	if err := json.Unmarshal([]byte(receiverInfoJSON), &receiverInfo); err != nil {
		a.logger.Warn("解析收件人信息JSON失败",
			zap.String("receiver_info", receiverInfoJSON),
			zap.Error(err))
		return ""
	}

	return receiverInfo.Name
}
