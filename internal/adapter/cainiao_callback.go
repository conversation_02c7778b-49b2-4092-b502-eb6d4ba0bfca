package adapter

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/your-org/go-kuaidi/internal/model"
	"go.uber.org/zap"
)

// CainiaoCallbackHandler 菜鸟裹裹回调处理器
type CainiaoCallbackHandler struct {
	config               CainiaoConfig
	logger               *zap.Logger
	orderService         OrderServiceInterface
	trackService         TrackServiceInterface
	billingService       BillingServiceInterface
	statusMappingService StatusMappingService
}

// OrderServiceInterface 订单服务接口
type OrderServiceInterface interface {
	UpdateOrderStatus(ctx context.Context, orderNo string, status string, statusDesc string) error
	UpdateOrderCourierInfo(ctx context.Context, orderNo string, courierName, courierPhone string) error
	UpdateOrderPrice(ctx context.Context, orderNo string, actualPrice float64) error
}

// TrackServiceInterface 物流跟踪服务接口
type TrackServiceInterface interface {
	AddTrackRecord(ctx context.Context, trackingNo string, trackItem *model.TrackItem) error
	UpdateTrackStatus(ctx context.Context, trackingNo string, status string, statusDesc string) error
}

// BillingServiceInterface 计费服务接口
type BillingServiceInterface interface {
	ProcessBillingDifference(ctx context.Context, orderNo string, actualPrice float64) error
}

// NewCainiaoCallbackHandler 创建菜鸟裹裹回调处理器
func NewCainiaoCallbackHandler(
	config CainiaoConfig,
	logger *zap.Logger,
	orderService OrderServiceInterface,
	trackService TrackServiceInterface,
	billingService BillingServiceInterface,
	statusMappingService StatusMappingService,
) *CainiaoCallbackHandler {
	return &CainiaoCallbackHandler{
		config:               config,
		logger:               logger,
		orderService:         orderService,
		trackService:         trackService,
		billingService:       billingService,
		statusMappingService: statusMappingService,
	}
}

// HandleCallback 处理菜鸟裹裹回调
func (h *CainiaoCallbackHandler) HandleCallback(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	h.logger.Info("收到菜鸟裹裹回调请求",
		zap.String("method", r.Method),
		zap.String("url", r.URL.String()),
		zap.String("remote_addr", r.RemoteAddr))

	// 只接受POST请求
	if r.Method != http.MethodPost {
		h.respondError(w, http.StatusMethodNotAllowed, "只支持POST请求")
		return
	}

	// 读取请求体
	body, err := io.ReadAll(r.Body)
	if err != nil {
		h.logger.Error("读取回调请求体失败", zap.Error(err))
		h.respondError(w, http.StatusBadRequest, "读取请求体失败")
		return
	}
	defer r.Body.Close()

	h.logger.Debug("菜鸟裹裹回调请求体", zap.String("body", string(body)))

	// 验证签名
	if !h.verifySignature(r, body) {
		h.logger.Warn("菜鸟裹裹回调签名验证失败")
		h.respondError(w, http.StatusUnauthorized, "签名验证失败")
		return
	}

	// 解析回调数据
	var callbackData CainiaoCallbackData
	if err := json.Unmarshal(body, &callbackData); err != nil {
		h.logger.Error("解析菜鸟裹裹回调数据失败", zap.Error(err))
		h.respondError(w, http.StatusBadRequest, "解析回调数据失败")
		return
	}

	// 处理回调事件
	if err := h.processCallback(ctx, &callbackData); err != nil {
		h.logger.Error("处理菜鸟裹裹回调失败", zap.Error(err))
		h.respondError(w, http.StatusInternalServerError, "处理回调失败")
		return
	}

	// 返回成功响应
	h.respondSuccess(w, "回调处理成功")
}

// verifySignature 验证回调签名
func (h *CainiaoCallbackHandler) verifySignature(r *http.Request, body []byte) bool {
	// 获取签名参数
	signature := r.Header.Get("X-Cainiao-Signature")
	timestamp := r.Header.Get("X-Cainiao-Timestamp")

	if signature == "" || timestamp == "" {
		h.logger.Warn("菜鸟裹裹回调缺少签名参数")
		return false
	}

	// 构建签名字符串
	signStr := fmt.Sprintf("%s%s%s", timestamp, string(body), h.config.AccessCode)

	// 计算MD5签名
	hash := md5.Sum([]byte(signStr))
	expectedSignature := strings.ToUpper(hex.EncodeToString(hash[:]))

	// 验证签名
	return signature == expectedSignature
}

// processCallback 处理回调事件
// 根据菜鸟裹裹官方文档附录5事件类型进行处理
func (h *CainiaoCallbackHandler) processCallback(ctx context.Context, data *CainiaoCallbackData) error {
	switch data.EventType {
	// 订单事件
	case "CREATE_ORDER":
		return h.handleCreateOrder(ctx, data)
	case "CANCEL_ORDER":
		return h.handleCancelOrder(ctx, data)
	case "GOT_SUCCESS":
		return h.handleGotSuccess(ctx, data)
	case "UPLOAD_MAIL_NO_SUCCESS":
		return h.handleUploadMailNoSuccess(ctx, data)
	case "FINISH_ORDER":
		return h.handleFinishOrder(ctx, data)
	case "COURIER_CHECK_BILL_SUCCESS":
		return h.handleCourierCheckBillSuccess(ctx, data)
	case "CHANGE_DELIVERY_USER_SUCCESS":
		return h.handleChangeDeliveryUserSuccess(ctx, data)
	case "MODIFY_EXPECT_GOT_TIME_SUCCESS":
		return h.handleModifyExpectGotTimeSuccess(ctx, data)
	case "PAY_SUCCESS":
		return h.handlePaySuccess(ctx, data)

	// 物流事件
	case "ACCEPT":
		return h.handleLogisticsEvent(ctx, data, "已揽件")
	case "TRANSPORT":
		return h.handleLogisticsEvent(ctx, data, "运输中")
	case "DELIVERING":
		return h.handleLogisticsEvent(ctx, data, "派件中")
	case "SIGN":
		return h.handleLogisticsEvent(ctx, data, "已签收")
	case "FAILED":
		return h.handleLogisticsEvent(ctx, data, "派件失败")
	case "BACK":
		return h.handleLogisticsEvent(ctx, data, "退回")
	case "REVERSE_RETURN":
		return h.handleLogisticsEvent(ctx, data, "退货返回")

	default:
		h.logger.Warn("未知的菜鸟裹裹回调事件类型",
			zap.String("event_type", data.EventType))
		return nil // 忽略未知事件类型
	}
}

// handleCreateOrder 处理创建订单事件
func (h *CainiaoCallbackHandler) handleCreateOrder(ctx context.Context, data *CainiaoCallbackData) error {
	h.logger.Info("处理菜鸟裹裹创建订单事件",
		zap.String("event_type", data.EventType),
		zap.Time("event_time", data.EventTime))

	// 从eventData中提取订单信息
	if accountId, ok := data.EventData["accountId"].(string); ok {
		h.logger.Info("订单创建成功", zap.String("account_id", accountId))
	}

	// 可以在这里添加订单创建后的业务逻辑
	return nil
}

// handleCancelOrder 处理取消订单事件
func (h *CainiaoCallbackHandler) handleCancelOrder(ctx context.Context, data *CainiaoCallbackData) error {
	h.logger.Info("处理菜鸟裹裹取消订单事件",
		zap.String("event_type", data.EventType),
		zap.Time("event_time", data.EventTime))

	// 从eventData中提取取消原因
	if reason, ok := data.EventData["orderCancelReasonDesc"].(string); ok {
		h.logger.Info("订单取消", zap.String("cancel_reason", reason))
	}

	return nil
}

// handleGotSuccess 处理取件成功事件
func (h *CainiaoCallbackHandler) handleGotSuccess(ctx context.Context, data *CainiaoCallbackData) error {
	h.logger.Info("处理菜鸟裹裹取件成功事件",
		zap.String("event_type", data.EventType),
		zap.Time("event_time", data.EventTime))

	// 从eventData中提取快递员信息
	if courierName, ok := data.EventData["courierName"].(string); ok {
		if courierMobile, ok := data.EventData["courierMobile"].(string); ok {
			h.logger.Info("快递员信息",
				zap.String("courier_name", courierName),
				zap.String("courier_mobile", courierMobile))
		}
	}

	return nil
}

// handleUploadMailNoSuccess 处理回单成功事件
func (h *CainiaoCallbackHandler) handleUploadMailNoSuccess(ctx context.Context, data *CainiaoCallbackData) error {
	h.logger.Info("处理菜鸟裹裹回单成功事件",
		zap.String("event_type", data.EventType),
		zap.Time("event_time", data.EventTime))

	// 从eventData中提取运单号信息
	if mailNo, ok := data.EventData["mailNo"].(string); ok {
		h.logger.Info("回单成功", zap.String("mail_no", mailNo))
	}

	return nil
}

// handleFinishOrder 处理订单完结事件
func (h *CainiaoCallbackHandler) handleFinishOrder(ctx context.Context, data *CainiaoCallbackData) error {
	h.logger.Info("处理菜鸟裹裹订单完结事件",
		zap.String("event_type", data.EventType),
		zap.Time("event_time", data.EventTime))

	// 从eventData中提取订单总价和重量
	if totalPrice, ok := data.EventData["totalPrice"].(float64); ok {
		if weight, ok := data.EventData["weight"].(float64); ok {
			h.logger.Info("订单完结",
				zap.Float64("total_price", totalPrice),
				zap.Float64("weight", weight))
		}
	}

	return nil
}

// handleCourierCheckBillSuccess 处理核价成功事件
func (h *CainiaoCallbackHandler) handleCourierCheckBillSuccess(ctx context.Context, data *CainiaoCallbackData) error {
	h.logger.Info("处理菜鸟裹裹核价成功事件",
		zap.String("event_type", data.EventType),
		zap.Time("event_time", data.EventTime))

	// 从eventData中提取价格信息
	if basePrice, ok := data.EventData["basePrice"].(float64); ok {
		if totalPrice, ok := data.EventData["totalPrice"].(float64); ok {
			h.logger.Info("核价成功",
				zap.Float64("base_price", basePrice),
				zap.Float64("total_price", totalPrice))
		}
	}

	return nil
}

// handleChangeDeliveryUserSuccess 处理改派成功事件
func (h *CainiaoCallbackHandler) handleChangeDeliveryUserSuccess(ctx context.Context, data *CainiaoCallbackData) error {
	h.logger.Info("处理菜鸟裹裹改派成功事件",
		zap.String("event_type", data.EventType),
		zap.Time("event_time", data.EventTime))

	return nil
}

// handleModifyExpectGotTimeSuccess 处理修改上门时间成功事件
func (h *CainiaoCallbackHandler) handleModifyExpectGotTimeSuccess(ctx context.Context, data *CainiaoCallbackData) error {
	h.logger.Info("处理菜鸟裹裹修改上门时间成功事件",
		zap.String("event_type", data.EventType),
		zap.Time("event_time", data.EventTime))

	return nil
}

// handlePaySuccess 处理支付成功事件
func (h *CainiaoCallbackHandler) handlePaySuccess(ctx context.Context, data *CainiaoCallbackData) error {
	h.logger.Info("处理菜鸟裹裹支付成功事件",
		zap.String("event_type", data.EventType),
		zap.Time("event_time", data.EventTime))

	return nil
}

// handleLogisticsEvent 处理物流事件
func (h *CainiaoCallbackHandler) handleLogisticsEvent(ctx context.Context, data *CainiaoCallbackData, statusDesc string) error {
	logisticsEvent := data.LogisticsEvent
	if logisticsEvent == nil {
		return fmt.Errorf("物流事件数据为空")
	}

	h.logger.Info("处理菜鸟裹裹物流状态变更",
		zap.String("mail_no", logisticsEvent.MailNo),
		zap.String("status", logisticsEvent.LogisticsStatus),
		zap.String("action", logisticsEvent.LogisticsAction))

	// 创建轨迹记录
	trackItem := &model.TrackItem{
		Context:    logisticsEvent.LogisticsStandardDesc,
		Time:       time.Now(), // 使用当前时间，实际应该从事件中获取
		Status:     logisticsEvent.LogisticsStatus,
		StatusCode: h.getLogisticsStatusCode(logisticsEvent.LogisticsStatus),
		Location:   "", // 菜鸟裹裹可能不提供具体位置信息
	}

	// 添加轨迹记录
	if err := h.trackService.AddTrackRecord(ctx, logisticsEvent.MailNo, trackItem); err != nil {
		return fmt.Errorf("添加轨迹记录失败: %w", err)
	}

	// 更新整体物流状态
	logisticsStatusDesc := h.getLogisticsStatusDesc(ctx, logisticsEvent.LogisticsStatus)
	if err := h.trackService.UpdateTrackStatus(ctx, logisticsEvent.MailNo, logisticsEvent.LogisticsStatus, logisticsStatusDesc); err != nil {
		return fmt.Errorf("更新物流状态失败: %w", err)
	}

	return nil
}

// getLogisticsStatusCode 获取物流状态码
func (h *CainiaoCallbackHandler) getLogisticsStatusCode(status string) string {
	codeMap := map[string]string{
		"ACCEPT":     "103", // 已揽收
		"TRANSPORT":  "0",   // 在途
		"DELIVERING": "5",   // 派件
		"SIGN":       "3",   // 已签收
		"FAILED":     "2",   // 派件失败
		"BACK":       "6",   // 退回
	}

	if code, exists := codeMap[status]; exists {
		return code
	}
	return "0" // 默认状态码
}

// getLogisticsStatusDesc 获取物流状态描述
// 使用统一的状态映射服务进行映射
func (h *CainiaoCallbackHandler) getLogisticsStatusDesc(ctx context.Context, status string) string {
	if h.statusMappingService != nil {
		// 使用统一状态映射服务
		systemStatus, err := h.statusMappingService.MapProviderStatusToSystem(ctx, "cainiao", status)
		if err != nil {
			h.logger.Warn("状态映射失败，使用默认映射",
				zap.String("cainiao_status", status),
				zap.Error(err))
		} else {
			return systemStatus.GetLabel()
		}
	}

	// 降级到本地映射（兼容性）- 与统一映射服务保持一致
	statusMap := map[string]string{
		// === 订单状态映射（官方数字编码）===
		"-1": "订单已取消",
		"0":  "订单已创建",
		"20": "已分配运力",
		"30": "已取件",
		"40": "订单已完结",

		// === 物流状态映射（官方事件编码）===
		"ACCEPT":         "快件已揽收",
		"TRANSPORT":      "快件在途中",
		"DELIVERING":     "正在派件",
		"SIGN":           "快件已签收",
		"FAILED":         "异常提醒",
		"REJECT":         "拒签",
		"AGENT_SIGN":     "待取件",
		"STA_DELIVERING": "驿站派送中",
		"ORDER_TRANSER":  "已转单",
		"REVERSE_RETURN": "退货返回",

		// === 兼容字符串状态 ===
		"CREATED":   "订单已创建",
		"CANCELLED": "订单已取消",
		"COMPLETED": "订单已完成",

		// === 其他兼容状态 ===
		"TRANSITING": "快件在途中",
		"DELIVERED":  "快件已签收",
		"EXCEPTION":  "快件异常",
		"RETURNED":   "快件已退回",
		"BACK":       "快件已退回",
	}

	if desc, exists := statusMap[status]; exists {
		return desc
	}
	return status
}

// respondSuccess 返回成功响应
func (h *CainiaoCallbackHandler) respondSuccess(w http.ResponseWriter, message string) {
	response := map[string]interface{}{
		"success": true,
		"message": message,
		"code":    "200",
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(response)
}

// respondError 返回错误响应
func (h *CainiaoCallbackHandler) respondError(w http.ResponseWriter, statusCode int, message string) {
	response := map[string]interface{}{
		"success": false,
		"message": message,
		"code":    fmt.Sprintf("%d", statusCode),
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)
	json.NewEncoder(w).Encode(response)
}
