#!/bin/bash

# =====================================================
# Go-Kuaidi 系统性能评估 - 订单承载能力测试脚本
# 基于现有测试脚本扩展，评估系统可承载的订单量
# 测试内容：
# 1. 并发查价性能测试
# 2. 并发订单创建性能测试  
# 3. 系统资源监控
# 4. 数据库连接池监控
# 5. Redis缓存性能监控
# =====================================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 配置
BASE_URL="http://localhost:8081"
LOGIN_USERNAME="admin"
LOGIN_PASSWORD="1104030777+.aA..@"

# 性能测试配置
CONCURRENT_USERS=(1 5 10 20 50 100)  # 并发用户数
TEST_DURATION=60                      # 每轮测试持续时间（秒）
WARMUP_TIME=10                       # 预热时间（秒）

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_test() {
    echo -e "${PURPLE}[TEST]${NC} $1"
}

log_perf() {
    echo -e "${CYAN}[PERF]${NC} $1"
}

# 分割线
print_separator() {
    echo -e "${CYAN}=================================================${NC}"
}

print_section() {
    echo -e "${CYAN}=============== $1 ===============${NC}"
}

# 获取JWT Token
get_jwt_token() {
    log_info "正在获取JWT Token..."
    
    TOKEN_RESPONSE=$(curl -s -X POST "${BASE_URL}/api/v1/auth/login" \
        -H "Content-Type: application/json" \
        -d "{
            \"username\": \"${LOGIN_USERNAME}\",
            \"password\": \"${LOGIN_PASSWORD}\"
        }")

    TOKEN=$(echo $TOKEN_RESPONSE | jq -r '.access_token')

    if [ "$TOKEN" = "null" ] || [ -z "$TOKEN" ]; then
        log_error "获取Token失败: $TOKEN_RESPONSE"
        exit 1
    fi

    log_success "Token获取成功"
}

# 系统健康检查
check_system_health() {
    log_info "🔍 系统健康检查..."
    
    # 检查服务是否运行
    health_response=$(curl -s "${BASE_URL}/health")
    if [ $? -eq 0 ]; then
        log_success "✅ 服务运行正常"
    else
        log_error "❌ 服务无法访问"
        exit 1
    fi
    
    # 检查数据库连接
    db_check=$(curl -s -H "Authorization: Bearer $TOKEN" "${BASE_URL}/api/v1/system/config")
    if [ $? -eq 0 ]; then
        log_success "✅ 数据库连接正常"
    else
        log_warning "⚠️ 数据库连接可能有问题"
    fi
}

# 获取系统性能基线
get_performance_baseline() {
    log_info "📊 获取系统性能基线..."
    
    # 记录开始时间
    baseline_start=$(date +%s)
    
    # 单次查价测试
    single_price_start=$(date +%s%3N)
    price_response=$(curl -s -X POST "${BASE_URL}/api/gateway/execute" \
        -H "Content-Type: application/json" \
        -d "{
            \"apiMethod\": \"QUERY_PRICE\",
            \"clientType\": \"web\",
            \"accessToken\": \"$TOKEN\",
            \"businessParams\": {
                \"from_province\": \"北京市\",
                \"from_city\": \"北京市\",
                \"to_province\": \"上海市\",
                \"to_city\": \"上海市\",
                \"weight\": 1.0,
                \"goods_name\": \"测试物品\"
            }
        }")
    single_price_end=$(date +%s%3N)
    single_price_time=$((single_price_end - single_price_start))
    
    log_perf "单次查价响应时间: ${single_price_time}ms"
    
    # 检查响应是否成功
    price_success=$(echo "$price_response" | jq -r '.success')
    if [ "$price_success" = "true" ]; then
        log_success "✅ 查价接口基线测试通过"
    else
        log_warning "⚠️ 查价接口基线测试失败"
    fi
}

# 并发查价性能测试
concurrent_price_test() {
    local concurrent_users=$1
    log_test "🚀 并发查价测试 - ${concurrent_users} 并发用户"
    
    # 创建临时目录存储结果
    local test_dir="/tmp/price_test_${concurrent_users}"
    mkdir -p "$test_dir"
    
    # 启动并发测试
    local pids=()
    local start_time=$(date +%s)
    
    for ((i=1; i<=concurrent_users; i++)); do
        {
            local user_requests=0
            local user_success=0
            local user_total_time=0
            
            while [ $(($(date +%s) - start_time)) -lt $TEST_DURATION ]; do
                local req_start=$(date +%s%3N)
                
                local response=$(curl -s -X POST "${BASE_URL}/api/gateway/execute" \
                    -H "Content-Type: application/json" \
                    -d "{
                        \"apiMethod\": \"QUERY_PRICE\",
                        \"clientType\": \"web\",
                        \"accessToken\": \"$TOKEN\",
                        \"businessParams\": {
                            \"from_province\": \"北京市\",
                            \"from_city\": \"北京市\",
                            \"to_province\": \"上海市\",
                            \"to_city\": \"上海市\",
                            \"weight\": $((RANDOM % 10 + 1)).0,
                            \"goods_name\": \"测试物品${i}\"
                        }
                    }")
                
                local req_end=$(date +%s%3N)
                local req_time=$((req_end - req_start))
                
                user_requests=$((user_requests + 1))
                user_total_time=$((user_total_time + req_time))
                
                local success=$(echo "$response" | jq -r '.success')
                if [ "$success" = "true" ]; then
                    user_success=$((user_success + 1))
                fi
                
                # 短暂休息避免过度压力
                sleep 0.1
            done
            
            # 保存用户结果
            echo "${user_requests},${user_success},${user_total_time}" > "${test_dir}/user_${i}.result"
        } &
        pids+=($!)
    done
    
    # 等待所有进程完成
    for pid in "${pids[@]}"; do
        wait $pid
    done
    
    # 汇总结果
    local total_requests=0
    local total_success=0
    local total_time=0
    
    for ((i=1; i<=concurrent_users; i++)); do
        if [ -f "${test_dir}/user_${i}.result" ]; then
            local result=$(cat "${test_dir}/user_${i}.result")
            IFS=',' read -r requests success time <<< "$result"
            total_requests=$((total_requests + requests))
            total_success=$((total_success + success))
            total_time=$((total_time + time))
        fi
    done
    
    # 计算性能指标
    local success_rate=0
    local avg_response_time=0
    local qps=0
    
    if [ $total_requests -gt 0 ]; then
        success_rate=$(echo "scale=2; $total_success * 100 / $total_requests" | bc)
        avg_response_time=$(echo "scale=2; $total_time / $total_requests" | bc)
        qps=$(echo "scale=2; $total_requests / $TEST_DURATION" | bc)
    fi
    
    log_perf "并发用户数: ${concurrent_users}"
    log_perf "总请求数: ${total_requests}"
    log_perf "成功请求数: ${total_success}"
    log_perf "成功率: ${success_rate}%"
    log_perf "平均响应时间: ${avg_response_time}ms"
    log_perf "QPS: ${qps}"
    
    # 清理临时文件
    rm -rf "$test_dir"
    
    echo ""
}

# 系统资源监控
monitor_system_resources() {
    log_info "📈 系统资源监控..."
    
    # CPU使用率
    cpu_usage=$(top -l 1 | grep "CPU usage" | awk '{print $3}' | sed 's/%//')
    log_perf "CPU使用率: ${cpu_usage}%"
    
    # 内存使用率
    memory_info=$(vm_stat | grep -E "(free|active|inactive|wired)" | awk '{print $3}' | sed 's/\.//')
    free_pages=$(echo "$memory_info" | sed -n '1p')
    active_pages=$(echo "$memory_info" | sed -n '2p')
    inactive_pages=$(echo "$memory_info" | sed -n '3p')
    wired_pages=$(echo "$memory_info" | sed -n '4p')
    
    total_pages=$((free_pages + active_pages + inactive_pages + wired_pages))
    used_pages=$((active_pages + inactive_pages + wired_pages))
    memory_usage=$(echo "scale=2; $used_pages * 100 / $total_pages" | bc)
    
    log_perf "内存使用率: ${memory_usage}%"
    
    # 网络连接数
    network_connections=$(netstat -an | grep ESTABLISHED | wc -l)
    log_perf "网络连接数: ${network_connections}"
}

# 并发订单创建性能测试
concurrent_order_test() {
    local concurrent_users=$1
    log_test "📦 并发订单创建测试 - ${concurrent_users} 并发用户"

    # 创建临时目录存储结果
    local test_dir="/tmp/order_test_${concurrent_users}"
    mkdir -p "$test_dir"

    # 启动并发测试
    local pids=()
    local start_time=$(date +%s)

    for ((i=1; i<=concurrent_users; i++)); do
        {
            local user_requests=0
            local user_success=0
            local user_total_time=0

            while [ $(($(date +%s) - start_time)) -lt $TEST_DURATION ]; do
                local req_start=$(date +%s%3N)

                # 生成唯一订单号
                local order_no="PERF_TEST_$(date +%s%3N)_${i}_${user_requests}"

                local response=$(curl -s -X POST "${BASE_URL}/api/gateway/execute" \
                    -H "Content-Type: application/json" \
                    -d "{
                        \"apiMethod\": \"CREATE_ORDER\",
                        \"clientType\": \"web\",
                        \"accessToken\": \"$TOKEN\",
                        \"businessParams\": {
                            \"customer_order_no\": \"${order_no}\",
                            \"express_company_code\": \"YTO\",
                            \"sender\": {
                                \"name\": \"测试寄件人${i}\",
                                \"mobile\": \"1380013800${i}\",
                                \"province\": \"北京市\",
                                \"city\": \"北京市\",
                                \"district\": \"朝阳区\",
                                \"address\": \"测试地址${i}号\"
                            },
                            \"receiver\": {
                                \"name\": \"测试收件人${i}\",
                                \"mobile\": \"1390013900${i}\",
                                \"province\": \"上海市\",
                                \"city\": \"上海市\",
                                \"district\": \"浦东新区\",
                                \"address\": \"测试收货地址${i}号\"
                            },
                            \"weight\": $((RANDOM % 5 + 1)).0,
                            \"goods_name\": \"性能测试商品${i}\",
                            \"estimated_price\": \"10.00\",
                            \"pay_method\": 0
                        }
                    }")

                local req_end=$(date +%s%3N)
                local req_time=$((req_end - req_start))

                user_requests=$((user_requests + 1))
                user_total_time=$((user_total_time + req_time))

                local success=$(echo "$response" | jq -r '.success')
                if [ "$success" = "true" ]; then
                    user_success=$((user_success + 1))
                fi

                # 订单创建间隔更长，避免过度压力
                sleep 0.5
            done

            # 保存用户结果
            echo "${user_requests},${user_success},${user_total_time}" > "${test_dir}/user_${i}.result"
        } &
        pids+=($!)
    done

    # 等待所有进程完成
    for pid in "${pids[@]}"; do
        wait $pid
    done

    # 汇总结果
    local total_requests=0
    local total_success=0
    local total_time=0

    for ((i=1; i<=concurrent_users; i++)); do
        if [ -f "${test_dir}/user_${i}.result" ]; then
            local result=$(cat "${test_dir}/user_${i}.result")
            IFS=',' read -r requests success time <<< "$result"
            total_requests=$((total_requests + requests))
            total_success=$((total_success + success))
            total_time=$((total_time + time))
        fi
    done

    # 计算性能指标
    local success_rate=0
    local avg_response_time=0
    local qps=0

    if [ $total_requests -gt 0 ]; then
        success_rate=$(echo "scale=2; $total_success * 100 / $total_requests" | bc)
        avg_response_time=$(echo "scale=2; $total_time / $total_requests" | bc)
        qps=$(echo "scale=2; $total_requests / $TEST_DURATION" | bc)
    fi

    log_perf "📦 订单创建 - 并发用户数: ${concurrent_users}"
    log_perf "📦 订单创建 - 总请求数: ${total_requests}"
    log_perf "📦 订单创建 - 成功请求数: ${total_success}"
    log_perf "📦 订单创建 - 成功率: ${success_rate}%"
    log_perf "📦 订单创建 - 平均响应时间: ${avg_response_time}ms"
    log_perf "📦 订单创建 - QPS: ${qps}"

    # 清理临时文件
    rm -rf "$test_dir"

    echo ""
}

# 数据库性能监控
monitor_database_performance() {
    log_info "🗄️ 数据库性能监控..."

    # 这里需要实际的数据库监控API，暂时用模拟数据
    log_perf "数据库连接池状态:"
    log_perf "  - 最大连接数: 200"
    log_perf "  - 当前活跃连接: 模拟监控中..."
    log_perf "  - 空闲连接数: 模拟监控中..."
    log_perf "  - 等待连接数: 模拟监控中..."
}

# Redis性能监控
monitor_redis_performance() {
    log_info "🔴 Redis性能监控..."

    log_perf "Redis连接池状态:"
    log_perf "  - 连接池大小: 100"
    log_perf "  - 最小空闲连接: 20"
    log_perf "  - 缓存命中率: 模拟监控中..."
    log_perf "  - 内存使用情况: 模拟监控中..."
}

# 生成性能报告
generate_performance_report() {
    local report_file="performance_report_$(date +%Y%m%d_%H%M%S).txt"

    log_info "📋 生成性能报告: ${report_file}"

    cat > "$report_file" << EOF
Go-Kuaidi 系统性能评估报告
生成时间: $(date)
测试配置: 并发用户 ${CONCURRENT_USERS[@]}, 测试时长 ${TEST_DURATION}秒

=== 系统配置 ===
- 数据库连接池: 200个连接
- Redis连接池: 100个连接
- 查价缓存TTL: 5分钟
- 服务器配置: 8核32GB

=== 性能目标 ===
- API响应时间: <200ms
- 系统可用性: >99.9%
- 并发支持: >1000用户
- 错误率: <1%

=== 测试结果 ===
详细测试数据请查看测试日志

=== 订单承载能力评估 ===
基于测试结果计算:
- 查价QPS: [根据测试结果]
- 订单创建QPS: [根据测试结果]
- 每日订单承载量: QPS × 86400 × 0.7
- 峰值处理能力: 最大QPS × 安全系数

=== 优化建议 ===
1. 数据库优化
2. 缓存策略优化
3. 连接池调优
4. 监控告警设置

EOF

    log_success "✅ 性能报告已生成: ${report_file}"
}

# 主函数
main() {
    print_separator
    log_info "🚀 Go-Kuaidi 系统性能评估 - 订单承载能力测试"
    print_separator

    # 检查依赖
    if ! command -v curl &> /dev/null; then
        log_error "curl 命令未找到，请安装 curl"
        exit 1
    fi

    if ! command -v jq &> /dev/null; then
        log_error "jq 命令未找到，请安装 jq"
        exit 1
    fi

    if ! command -v bc &> /dev/null; then
        log_error "bc 命令未找到，请安装 bc"
        exit 1
    fi

    # 获取认证Token
    get_jwt_token

    # 系统健康检查
    check_system_health

    # 获取性能基线
    get_performance_baseline

    print_section "并发查价性能测试"

    # 预热系统
    log_info "🔥 系统预热中..."
    sleep $WARMUP_TIME

    # 执行不同并发级别的查价测试
    for users in "${CONCURRENT_USERS[@]}"; do
        concurrent_price_test $users

        # 监控系统资源
        monitor_system_resources
        monitor_database_performance
        monitor_redis_performance

        # 测试间隔，让系统恢复
        log_info "⏳ 等待系统恢复..."
        sleep 10
    done

    print_section "并发订单创建性能测试"

    # 执行不同并发级别的订单创建测试（较小并发数）
    ORDER_CONCURRENT_USERS=(1 2 5 10)  # 订单创建测试用较小并发数
    for users in "${ORDER_CONCURRENT_USERS[@]}"; do
        concurrent_order_test $users

        # 监控系统资源
        monitor_system_resources
        monitor_database_performance
        monitor_redis_performance

        # 测试间隔，让系统恢复
        log_info "⏳ 等待系统恢复..."
        sleep 15
    done

    print_section "性能评估总结"

    echo -e "${CYAN}📊 系统性能评估结果：${NC}"
    echo "1. 🔍 基础性能："
    echo "   - 单次查价响应时间: <200ms (目标)"
    echo "   - 系统健康状态: 正常"
    echo ""
    echo "2. 🚀 并发性能："
    echo "   - 查价支持并发用户数: 根据测试结果确定"
    echo "   - 订单创建支持并发数: 根据测试结果确定"
    echo "   - 推荐查价QPS: 根据测试结果确定"
    echo "   - 推荐订单QPS: 根据测试结果确定"
    echo "   - 成功率要求: >99%"
    echo ""
    echo "3. 💡 订单承载能力估算："
    echo "   - 每日查价量 = 查价QPS × 86400 × 0.7"
    echo "   - 每日订单量 = 订单QPS × 86400 × 0.7"
    echo "   - 利用率建议: 70% (保留30%缓冲)"
    echo "   - 峰值处理能力: 根据最高QPS计算"
    echo ""
    echo "4. 🔧 系统配置："
    echo "   - 数据库连接池: 200个连接"
    echo "   - Redis连接池: 100个连接"
    echo "   - 查价缓存策略: 5分钟TTL"
    echo "   - 服务器配置: 8核32GB"
    echo ""
    echo "5. 📈 监控告警建议："
    echo "   - 响应时间告警: >1s"
    echo "   - 错误率告警: >5%"
    echo "   - 连接池使用率: >80%"
    echo "   - 内存使用率: >85%"
    echo "   - CPU使用率: >80%"

    # 生成详细报告
    generate_performance_report

    print_separator
    log_success "✅ 性能评估测试完成！"
    log_info "💡 建议根据测试结果调整系统配置和容量规划"
    print_separator
}

# 执行主函数
main
