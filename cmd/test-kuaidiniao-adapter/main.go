package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/your-org/go-kuaidi/internal/adapter"
	"github.com/your-org/go-kuaidi/internal/model"
)

func main() {
	fmt.Println("🚀 开始测试快递鸟适配器...")

	// 配置快递鸟适配器（使用您提供的正式环境秘钥）
	config := adapter.KuaidiNiaoConfig{
		EBusinessID: "1778716",                              // 您的商户ID
		ApiKey:      "e7ea996e-f395-4208-b099-6513eb00eead", // 您的API密钥
		BaseURL:     "https://api.kdniao.com/api/dist",      // 正式环境API地址
		Environment: "production",                           // 正式环境
		Timeout:     10,                                     // 10秒超时
	}

	// 创建快递鸟适配器
	kuaidiNiaoAdapter := adapter.NewKuaidiNiaoAdapter(config, nil)

	fmt.Printf("✅ 快递鸟适配器创建成功\n")
	fmt.Printf("   商户ID: %s\n", config.EBusinessID)
	fmt.Printf("   环境: %s\n", config.Environment)
	fmt.Printf("   API地址: %s\n", config.BaseURL)

	// 构建测试查价请求
	priceRequest := &model.PriceRequest{
		Sender: model.SenderInfo{
			Province: "广东省",
			City:     "深圳市",
			District: "南山区",
		},
		Receiver: model.ReceiverInfo{
			Province: "北京市",
			City:     "北京市",
			District: "朝阳区",
		},
		Package: model.PackageInfo{
			Weight:    2.5,  // 2.5公斤
			Length:    30.0, // 30厘米
			Width:     20.0, // 20厘米
			Height:    15.0, // 15厘米
			GoodsName: "测试商品",
			// 去掉保价参数，避免API错误
		},
	}

	fmt.Println("\n📦 查价请求参数:")
	fmt.Printf("   寄件地址: %s %s %s\n", priceRequest.Sender.Province, priceRequest.Sender.City, priceRequest.Sender.District)
	fmt.Printf("   收件地址: %s %s %s\n", priceRequest.Receiver.Province, priceRequest.Receiver.City, priceRequest.Receiver.District)
	fmt.Printf("   重量: %.1f kg\n", priceRequest.Package.Weight)
	fmt.Printf("   尺寸: %.0f×%.0f×%.0f cm\n", priceRequest.Package.Length, priceRequest.Package.Width, priceRequest.Package.Height)

	// 创建上下文（10秒超时）
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	fmt.Println("\n🔍 正在调用快递鸟查价API...")

	// 调用查价接口
	startTime := time.Now()
	prices, err := kuaidiNiaoAdapter.QueryPrice(ctx, priceRequest)
	duration := time.Since(startTime)

	if err != nil {
		log.Fatalf("❌ 查价失败: %v", err)
	}

	fmt.Printf("✅ 查价成功! 耗时: %v\n\n", duration)

	// 输出查价结果
	fmt.Printf("📊 查价结果 (共 %d 条):\n", len(prices))
	fmt.Println("─────────────────────────────────────────────────────────")

	if len(prices) == 0 {
		fmt.Println("⚠️  没有查询到任何价格信息")
	} else {
		for i, price := range prices {
			fmt.Printf("%d. 快递公司: %s (%s)\n", i+1, price.ExpressName, price.ExpressCode)
			fmt.Printf("   价格: ¥%.2f\n", price.Price)
			fmt.Printf("   计费重量: %.1f kg\n", price.CalcWeight)
			fmt.Printf("   供应商: %s\n", price.Provider)
			if price.OrderCode != "" {
				fmt.Printf("   订单代码: %s\n", price.OrderCode)
			}
			if price.PickupTimeInfo != nil {
				fmt.Printf("   上门取件: %v\n", price.PickupTimeInfo.PickupRequired)
			}
			fmt.Println()
		}
	}

	fmt.Println("🎉 测试完成!")
}
