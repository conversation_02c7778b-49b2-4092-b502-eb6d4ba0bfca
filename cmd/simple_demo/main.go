package main

import (
	"context"
	"fmt"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"

	"github.com/your-org/go-kuaidi/internal/adapter"
	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/service"
)

// SimplifiedEnhancedBalanceDemo 简化版演示程序
func main() {
	// 1. 初始化日志
	logger, _ := zap.NewProduction()
	defer logger.Sync()

	logger.Info("🚀 启动简化版高并发余额服务演示")

	// 2. 初始化Redis客户端
	redisClient := redis.NewClient(&redis.Options{
		Addr:     "localhost:6379",
		Password: "",
		DB:       0,
	})

	// 测试Redis连接
	ctx := context.Background()
	if err := redisClient.Ping(ctx).Err(); err != nil {
		logger.Error("Redis连接失败", zap.Error(err))
		logger.Info("💡 请确保Redis服务运行: redis-server")
		os.Exit(1)
	}
	logger.Info("✅ Redis连接成功")

	// 3. 创建简化版余额服务
	demo := NewSimplifiedBalanceDemo(redisClient, logger)

	// 4. 启动演示
	demo.RunDemo(ctx)

	// 5. 等待退出信号
	waitForShutdown(logger)

	// ========== 菜鸟查价底层直测 ===========
	testCainiaoQueryPrice()
}

// SimplifiedBalanceDemo 简化版余额演示
type SimplifiedBalanceDemo struct {
	redisClient *redis.Client
	logger      *zap.Logger
	lockService *service.BalanceLockService
	monitoring  *service.BalanceMonitoringService
}

// NewSimplifiedBalanceDemo 创建简化演示
func NewSimplifiedBalanceDemo(redisClient *redis.Client, logger *zap.Logger) *SimplifiedBalanceDemo {
	return &SimplifiedBalanceDemo{
		redisClient: redisClient,
		logger:      logger,
		lockService: service.NewBalanceLockService(redisClient, logger),
		monitoring:  service.NewBalanceMonitoringService(logger),
	}
}

// RunDemo 运行演示
func (d *SimplifiedBalanceDemo) RunDemo(ctx context.Context) {
	d.logger.Info("🔄 开始运行高并发余额处理演示")

	// 1. 演示分布式锁功能
	d.demonstrateDistributedLock(ctx)

	// 2. 演示并发安全
	d.demonstrateConcurrencySafety(ctx)

	// 3. 演示监控功能
	d.demonstrateMonitoring(ctx)

	d.logger.Info("✅ 演示完成")
}

// demonstrateDistributedLock 演示分布式锁
func (d *SimplifiedBalanceDemo) demonstrateDistributedLock(ctx context.Context) {
	d.logger.Info("🔐 演示分布式锁功能")

	userID := "demo_user_001"

	// 测试获取锁
	err := d.lockService.WithUserLock(ctx, userID, func() error {
		d.logger.Info("🔒 获取用户锁成功，执行业务逻辑",
			zap.String("user_id", userID))

		// 模拟业务处理
		time.Sleep(100 * time.Millisecond)

		d.logger.Info("✅ 业务逻辑执行完成")
		return nil
	})

	if err != nil {
		d.logger.Error("❌ 分布式锁测试失败", zap.Error(err))
	} else {
		d.logger.Info("✅ 分布式锁测试成功")
	}
}

// demonstrateConcurrencySafety 演示并发安全
func (d *SimplifiedBalanceDemo) demonstrateConcurrencySafety(ctx context.Context) {
	d.logger.Info("🔄 演示并发安全处理")

	userID := "demo_user_002"

	// 模拟10个并发操作
	concurrency := 10
	done := make(chan bool, concurrency)

	for i := 0; i < concurrency; i++ {
		go func(operationID int) {
			defer func() { done <- true }()

			orderNo := fmt.Sprintf("ORDER_%d_%d", operationID, time.Now().UnixNano())
			amount := decimal.NewFromFloat(10.50)

			// 使用分布式锁保护操作
			err := d.lockService.WithUserLock(ctx, userID, func() error {
				d.logger.Info("💰 模拟余额操作",
					zap.String("user_id", userID),
					zap.String("order_no", orderNo),
					zap.String("amount", amount.String()),
					zap.Int("operation_id", operationID))

				// 模拟处理时间
				time.Sleep(50 * time.Millisecond)

				return nil
			})

			if err != nil {
				d.logger.Error("❌ 并发操作失败",
					zap.Int("operation_id", operationID),
					zap.Error(err))
			} else {
				d.logger.Info("✅ 并发操作成功",
					zap.Int("operation_id", operationID))
			}
		}(i)
	}

	// 等待所有操作完成
	for i := 0; i < concurrency; i++ {
		<-done
	}

	d.logger.Info("✅ 并发安全测试完成")
}

// demonstrateMonitoring 演示监控功能
func (d *SimplifiedBalanceDemo) demonstrateMonitoring(ctx context.Context) {
	d.logger.Info("📊 演示监控功能")

	// 模拟一些操作和指标
	for i := 0; i < 20; i++ {
		success := i%4 != 0 // 75%成功率
		responseTime := time.Duration(50+i*10) * time.Millisecond
		isConflict := i%10 == 0 // 10%冲突率

		// 记录指标
		d.monitoring.GetMetrics().RecordOperation(success, responseTime, isConflict)

		time.Sleep(10 * time.Millisecond)
	}

	// 获取监控指标
	metrics := d.monitoring.GetMetrics()
	d.logger.Info("📈 当前监控指标",
		zap.Int64("total_operations", metrics.TotalOperations),
		zap.Int64("successful_operations", metrics.SuccessfulOperations),
		zap.Int64("failed_operations", metrics.FailedOperations),
		zap.Float64("error_rate", metrics.ErrorRate),
		zap.Float64("conflict_rate", metrics.ConflictRate))

	d.logger.Info("✅ 监控功能演示完成")
}

// waitForShutdown 等待关闭信号
func waitForShutdown(logger *zap.Logger) {
	logger.Info("🔄 服务运行中，按 Ctrl+C 退出...")

	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)

	<-quit
	logger.Info("⏹️ 收到退出信号，正在关闭服务...")

	// 优雅关闭
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 这里可以添加清理逻辑
	select {
	case <-ctx.Done():
		logger.Info("⏰ 关闭超时")
	default:
		logger.Info("✅ 服务已优雅关闭")
	}
}

// ========== 菜鸟查价底层直测 ===========
func testCainiaoQueryPrice() {
	fmt.Println("========== 菜鸟查价底层直测 ==========")
	// 构造配置（使用实际的菜鸟配置）
	config := adapter.CainiaoConfig{
		AccessCode:         "4fe6b622-b431-4693-952e-4d01e0ae5c77",
		LogisticProviderID: "20df7df0fa15494c801b249a8b798879",
		CPCode:             "CP_CAINIAO_001",
		MsgTypePrefix:      "GUOGUO_",
		BaseURL:            "https://link.cainiao.com/gateway/link.do",
		Environment:        "production",
		Timeout:            30,
	}
	// 依赖仓库可mock或传nil
	cainiao := adapter.NewCainiaoAdapter(config, nil)

	// 构造查价请求
	req := &model.PriceRequest{
		Sender: model.SenderInfo{
			Name:     "张三",
			Mobile:   "13800138000",
			Province: "北京市",
			City:     "北京市",
			District: "朝阳区",
			Address:  "三里屯SOHO",
		},
		Receiver: model.ReceiverInfo{
			Name:     "李四",
			Mobile:   "13900139000",
			Province: "上海市",
			City:     "上海市",
			District: "浦东新区",
			Address:  "陆家嘴金融中心",
		},
		Package: model.PackageInfo{
			Weight:    2.5,
			Length:    30,
			Width:     20,
			Height:    15,
			Quantity:  1,
			GoodsName: "测试物品",
		},
		PayMethod: 0,
	}

	ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
	defer cancel()
	result, err := cainiao.QueryPrice(ctx, req)
	if err != nil {
		fmt.Printf("菜鸟查价失败: %v\n", err)
	} else {
		fmt.Printf("菜鸟查价结果: %+v\n", result)
	}
}
