package main

import (
	"crypto/rand"
	"crypto/sha256"
	"encoding/hex"
	"flag"
	"fmt"
	"log"
	"time"
)

// NonceGenerator 企业级nonce生成器
type NonceGenerator struct {
	clientID string
}

// NewNonceGenerator 创建nonce生成器
func NewNonceGenerator(clientID string) *NonceGenerator {
	return &NonceGenerator{
		clientID: clientID,
	}
}

// GenerateNonce 生成高强度nonce
func (ng *NonceGenerator) GenerateNonce() (string, error) {
	// 生成时间戳部分（毫秒级）
	timestamp := time.Now().UnixMilli()
	
	// 生成随机部分（16字节）
	randomBytes := make([]byte, 16)
	if _, err := rand.Read(randomBytes); err != nil {
		return "", fmt.Errorf("生成随机字节失败: %w", err)
	}
	
	// 生成客户端指纹（防止跨客户端nonce重用）
	clientHash := sha256.Sum256([]byte(ng.clientID))
	clientFingerprint := hex.EncodeToString(clientHash[:4]) // 8字符
	
	// 组合nonce: timestamp(13) + random(32) + client(8) = 53字符
	nonce := fmt.Sprintf("%d%s%s", 
		timestamp,
		hex.EncodeToString(randomBytes),
		clientFingerprint,
	)
	
	return nonce, nil
}

// GenerateMultipleNonces 生成多个nonce
func (ng *NonceGenerator) GenerateMultipleNonces(count int) ([]string, error) {
	nonces := make([]string, count)
	
	for i := 0; i < count; i++ {
		nonce, err := ng.GenerateNonce()
		if err != nil {
			return nil, fmt.Errorf("生成第%d个nonce失败: %w", i+1, err)
		}
		nonces[i] = nonce
		
		// 确保时间戳不同（毫秒级精度）
		time.Sleep(1 * time.Millisecond)
	}
	
	return nonces, nil
}

// ValidateNonceFormat 验证nonce格式
func ValidateNonceFormat(nonce, clientID string) error {
	// 检查长度
	if len(nonce) < 53 {
		return fmt.Errorf("nonce长度不足，期望至少53字符，实际%d字符", len(nonce))
	}
	
	// 检查是否只包含十六进制字符和数字
	for _, char := range nonce {
		if !((char >= '0' && char <= '9') || (char >= 'a' && char <= 'f') || (char >= 'A' && char <= 'F')) {
			return fmt.Errorf("nonce包含非法字符: %c", char)
		}
	}
	
	// 验证客户端指纹
	if len(nonce) >= 8 {
		nonceFingerprint := nonce[len(nonce)-8:]
		clientHash := sha256.Sum256([]byte(clientID))
		expectedFingerprint := hex.EncodeToString(clientHash[:4])
		
		if nonceFingerprint != expectedFingerprint {
			return fmt.Errorf("nonce不属于客户端%s", clientID)
		}
	}
	
	return nil
}

// ExtractTimestamp 从nonce中提取时间戳
func ExtractTimestamp(nonce string) (time.Time, error) {
	if len(nonce) < 13 {
		return time.Time{}, fmt.Errorf("nonce格式无效")
	}
	
	// 提取时间戳部分（前13字符）
	timestampStr := nonce[:13]
	
	// 解析时间戳
	var timestamp int64
	if _, err := fmt.Sscanf(timestampStr, "%d", &timestamp); err != nil {
		return time.Time{}, fmt.Errorf("解析时间戳失败: %w", err)
	}
	
	return time.UnixMilli(timestamp), nil
}

func main() {
	var (
		clientID = flag.String("client", "", "客户端ID (必需)")
		count    = flag.Int("count", 1, "生成nonce数量")
		validate = flag.String("validate", "", "验证指定nonce的格式")
		extract  = flag.String("extract", "", "从nonce中提取时间戳")
		help     = flag.Bool("help", false, "显示帮助信息")
	)
	flag.Parse()

	if *help {
		showHelp()
		return
	}

	// 验证nonce格式
	if *validate != "" {
		if *clientID == "" {
			log.Fatal("验证nonce时必须指定客户端ID")
		}
		
		err := ValidateNonceFormat(*validate, *clientID)
		if err != nil {
			fmt.Printf("❌ nonce格式验证失败: %v\n", err)
		} else {
			fmt.Printf("✅ nonce格式验证通过\n")
			
			// 提取时间戳
			if timestamp, err := ExtractTimestamp(*validate); err == nil {
				fmt.Printf("📅 生成时间: %s\n", timestamp.Format("2006-01-02 15:04:05.000"))
				fmt.Printf("⏰ 距现在: %v\n", time.Since(timestamp))
			}
		}
		return
	}

	// 提取时间戳
	if *extract != "" {
		timestamp, err := ExtractTimestamp(*extract)
		if err != nil {
			fmt.Printf("❌ 提取时间戳失败: %v\n", err)
		} else {
			fmt.Printf("📅 生成时间: %s\n", timestamp.Format("2006-01-02 15:04:05.000"))
			fmt.Printf("⏰ 距现在: %v\n", time.Since(timestamp))
			fmt.Printf("🕐 Unix毫秒: %d\n", timestamp.UnixMilli())
		}
		return
	}

	// 生成nonce
	if *clientID == "" {
		log.Fatal("必须指定客户端ID，使用 -client 参数")
	}

	if *count < 1 || *count > 100 {
		log.Fatal("nonce数量必须在1-100之间")
	}

	generator := NewNonceGenerator(*clientID)

	fmt.Printf("🚀 为客户端 '%s' 生成 %d 个nonce\n", *clientID, *count)
	fmt.Println("=" + fmt.Sprintf("%*s", 60, "="))

	if *count == 1 {
		nonce, err := generator.GenerateNonce()
		if err != nil {
			log.Fatalf("生成nonce失败: %v", err)
		}

		fmt.Printf("✅ 生成成功:\n")
		fmt.Printf("   Nonce: %s\n", nonce)
		fmt.Printf("   长度: %d 字符\n", len(nonce))
		
		if timestamp, err := ExtractTimestamp(nonce); err == nil {
			fmt.Printf("   时间: %s\n", timestamp.Format("2006-01-02 15:04:05.000"))
		}
	} else {
		nonces, err := generator.GenerateMultipleNonces(*count)
		if err != nil {
			log.Fatalf("生成nonce失败: %v", err)
		}

		fmt.Printf("✅ 生成 %d 个nonce成功:\n", len(nonces))
		for i, nonce := range nonces {
			fmt.Printf("%3d. %s\n", i+1, nonce)
		}
	}

	fmt.Println("=" + fmt.Sprintf("%*s", 60, "="))
	fmt.Printf("💡 使用说明:\n")
	fmt.Printf("   - 每个nonce有效期5分钟\n")
	fmt.Printf("   - nonce只能使用一次\n")
	fmt.Printf("   - 包含客户端指纹，防止跨客户端重用\n")
	fmt.Printf("   - 验证格式: %s -validate=<nonce> -client=%s\n", flag.CommandLine.Name(), *clientID)
}

func showHelp() {
	fmt.Printf(`
企业级Nonce生成器 v1.0.0
用于生成符合企业级安全标准的nonce值

用法:
  %s [选项]

选项:
  -client string    客户端ID (必需)
  -count int        生成nonce数量 (默认: 1, 最大: 100)
  -validate string  验证指定nonce的格式
  -extract string   从nonce中提取时间戳信息
  -help            显示此帮助信息

示例:
  # 生成单个nonce
  %s -client=test-client

  # 生成多个nonce
  %s -client=test-client -count=5

  # 验证nonce格式
  %s -validate=1704672000000abcd1234... -client=test-client

  # 提取时间戳
  %s -extract=1704672000000abcd1234...

Nonce格式:
  - 总长度: 53字符
  - 结构: timestamp(13) + random(32) + client_fingerprint(8)
  - 字符集: 十六进制 (0-9, a-f, A-F)
  - 有效期: 5分钟
  - 唯一性: 毫秒级时间戳 + 加密随机数 + 客户端指纹

安全特性:
  ✅ 加密安全的随机数生成
  ✅ 毫秒级时间戳防重放
  ✅ 客户端指纹防跨客户端重用
  ✅ 高熵值确保唯一性
  ✅ 格式验证防篡改

`, flag.CommandLine.Name(), flag.CommandLine.Name(), flag.CommandLine.Name(), flag.CommandLine.Name(), flag.CommandLine.Name())
}
