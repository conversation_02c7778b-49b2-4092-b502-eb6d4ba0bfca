<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>原始回调数据管理 - 演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            text-align: center;
        }

        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 2.5em;
        }

        .header p {
            color: #7f8c8d;
            font-size: 1.1em;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #7f8c8d;
            font-size: 1.1em;
        }

        .controls {
            background: white;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .controls h3 {
            margin-bottom: 20px;
            color: #2c3e50;
        }

        .button-group {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background: #229954;
        }

        .btn-warning {
            background: #f39c12;
            color: white;
        }

        .btn-warning:hover {
            background: #e67e22;
        }

        .btn-info {
            background: #17a2b8;
            color: white;
        }

        .btn-info:hover {
            background: #138496;
        }

        .data-table {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .table-header {
            background: #34495e;
            color: white;
            padding: 20px;
        }

        .table-header h3 {
            margin: 0;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #ecf0f1;
        }

        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }

        tr:hover {
            background: #f8f9fa;
        }

        .provider-tag {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            color: white;
        }

        .provider-cainiao { background: #3498db; }
        .provider-kuaidi100 { background: #27ae60; }
        .provider-yida { background: #f39c12; }
        .provider-yuntong { background: #9b59b6; }
        .provider-kuaidiniao { background: #e74c3c; }

        .raw-data-preview {
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            color: #7f8c8d;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }

        .error {
            background: #e74c3c;
            color: white;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
        }

        .success {
            background: #27ae60;
            color: white;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
        }

        .api-info {
            background: white;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-top: 30px;
        }

        .api-info h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .api-endpoint {
            background: #2c3e50;
            color: white;
            padding: 10px 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .button-group {
                flex-direction: column;
            }
            
            .btn {
                width: 100%;
                text-align: center;
            }
            
            table {
                font-size: 14px;
            }
            
            th, td {
                padding: 10px 8px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面标题 -->
        <div class="header">
            <h1>🔄 原始回调数据管理</h1>
            <p>管理员专用的回调数据查看、分析和重推工具</p>
        </div>

        <!-- 统计卡片 -->
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="totalRecords">-</div>
                <div class="stat-label">总记录数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="successRecords">-</div>
                <div class="stat-label">成功处理</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="failedRecords">-</div>
                <div class="stat-label">处理失败</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="successRate">-</div>
                <div class="stat-label">成功率</div>
            </div>
        </div>

        <!-- 操作控制 -->
        <div class="controls">
            <h3>🛠️ 操作控制</h3>
            <div class="button-group">
                <button class="btn btn-primary" onclick="loadData()">🔄 刷新数据</button>
                <button class="btn btn-success" onclick="testSingleRetry()">🔁 测试单个重推</button>
                <button class="btn btn-warning" onclick="testBatchRetry()">📦 测试批量重推</button>
                <button class="btn btn-info" onclick="exportData()">📥 导出数据</button>
            </div>
        </div>

        <!-- 数据表格 -->
        <div class="data-table">
            <div class="table-header">
                <h3>📋 原始回调记录</h3>
            </div>
            <div id="tableContainer">
                <div class="loading">正在加载数据...</div>
            </div>
        </div>

        <!-- API信息 -->
        <div class="api-info">
            <h3>🔗 API接口信息</h3>
            <p><strong>演示服务地址：</strong></p>
            <div class="api-endpoint">http://localhost:8082/api/v1/admin/raw-callbacks</div>
            
            <p><strong>主要接口：</strong></p>
            <div class="api-endpoint">GET /records - 获取回调记录列表</div>
            <div class="api-endpoint">GET /statistics - 获取统计信息</div>
            <div class="api-endpoint">POST /retry/:id - 重推单个回调</div>
            <div class="api-endpoint">POST /batch-retry - 批量重推回调</div>
            <div class="api-endpoint">GET /export - 导出数据</div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8082/api/v1/admin/raw-callbacks';
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadData();
            loadStatistics();
        });

        // 加载数据
        async function loadData() {
            try {
                showMessage('正在加载数据...', 'info');
                const response = await fetch(`${API_BASE}/records`);
                const data = await response.json();
                
                if (data.success) {
                    renderTable(data.data.records);
                    showMessage('数据加载成功', 'success');
                } else {
                    throw new Error('加载数据失败');
                }
            } catch (error) {
                showMessage('加载数据失败: ' + error.message, 'error');
                document.getElementById('tableContainer').innerHTML = '<div class="error">加载数据失败</div>';
            }
        }

        // 加载统计信息
        async function loadStatistics() {
            try {
                const response = await fetch(`${API_BASE}/statistics`);
                const data = await response.json();
                
                if (data.success) {
                    const stats = data.data;
                    document.getElementById('totalRecords').textContent = stats.total_records;
                    document.getElementById('successRecords').textContent = stats.success_records;
                    document.getElementById('failedRecords').textContent = stats.failed_records;
                    document.getElementById('successRate').textContent = stats.success_rate.toFixed(1) + '%';
                }
            } catch (error) {
                console.error('加载统计信息失败:', error);
            }
        }

        // 渲染表格
        function renderTable(records) {
            const html = `
                <table>
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>供应商</th>
                            <th>事件类型</th>
                            <th>订单号</th>
                            <th>运单号</th>
                            <th>接收时间</th>
                            <th>原始数据预览</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${records.map(record => `
                            <tr>
                                <td><code>${record.id.substring(0, 8)}...</code></td>
                                <td><span class="provider-tag provider-${record.provider}">${getProviderLabel(record.provider)}</span></td>
                                <td>${record.event_type || '-'}</td>
                                <td>${record.order_no || '-'}</td>
                                <td>${record.tracking_no || '-'}</td>
                                <td>${formatDateTime(record.received_at)}</td>
                                <td><div class="raw-data-preview" title="${record.raw_body}">${record.raw_body.substring(0, 100)}...</div></td>
                                <td>
                                    <button class="btn btn-primary" onclick="retryCallback('${record.id}')" style="padding: 6px 12px; font-size: 12px;">重推</button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;
            document.getElementById('tableContainer').innerHTML = html;
        }

        // 获取供应商标签
        function getProviderLabel(provider) {
            const labels = {
                'cainiao': '菜鸟',
                'kuaidi100': '快递100',
                'yida': '易达',
                'yuntong': '云通',
                'kuaidiniao': '快递鸟'
            };
            return labels[provider] || provider;
        }

        // 格式化时间
        function formatDateTime(dateTime) {
            return new Date(dateTime).toLocaleString('zh-CN');
        }

        // 重推单个回调
        async function retryCallback(id) {
            if (!confirm(`确定要重推记录 ${id.substring(0, 8)}... 吗？`)) {
                return;
            }

            try {
                showMessage('正在重推...', 'info');
                const response = await fetch(`${API_BASE}/retry/${id}`, {
                    method: 'POST'
                });
                const data = await response.json();
                
                if (data.success) {
                    showMessage('重推成功', 'success');
                } else {
                    throw new Error(data.message || '重推失败');
                }
            } catch (error) {
                showMessage('重推失败: ' + error.message, 'error');
            }
        }

        // 测试单个重推
        async function testSingleRetry() {
            await retryCallback('demo-001');
        }

        // 测试批量重推
        async function testBatchRetry() {
            if (!confirm('确定要批量重推演示记录吗？')) {
                return;
            }

            try {
                showMessage('正在批量重推...', 'info');
                const response = await fetch(`${API_BASE}/batch-retry`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        record_ids: ['demo-002', 'demo-003', 'demo-004']
                    })
                });
                const data = await response.json();
                
                if (data.success) {
                    const result = data.data;
                    showMessage(`批量重推完成：成功 ${result.success_count} 条，失败 ${result.failed_count} 条`, 'success');
                } else {
                    throw new Error(data.message || '批量重推失败');
                }
            } catch (error) {
                showMessage('批量重推失败: ' + error.message, 'error');
            }
        }

        // 导出数据
        async function exportData() {
            try {
                showMessage('正在导出数据...', 'info');
                const response = await fetch(`${API_BASE}/export`);
                
                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `raw_callbacks_${new Date().toISOString().slice(0, 10)}.csv`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);
                    showMessage('导出成功', 'success');
                } else {
                    throw new Error('导出失败');
                }
            } catch (error) {
                showMessage('导出失败: ' + error.message, 'error');
            }
        }

        // 显示消息
        function showMessage(message, type) {
            // 移除现有消息
            const existingMessage = document.querySelector('.message');
            if (existingMessage) {
                existingMessage.remove();
            }

            // 创建新消息
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.textContent = message;
            
            // 插入到容器顶部
            const container = document.querySelector('.container');
            container.insertBefore(messageDiv, container.firstChild);

            // 3秒后自动移除
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.remove();
                }
            }, 3000);
        }
    </script>
</body>
</html>
