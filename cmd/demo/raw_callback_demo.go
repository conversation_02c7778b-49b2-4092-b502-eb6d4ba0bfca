package main

import (
	"fmt"
	"log"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

// 演示用的原始回调记录
type DemoRawCallbackRecord struct {
	ID          string                 `json:"id"`
	Provider    string                 `json:"provider"`
	RawBody     string                 `json:"raw_body"`
	ParsedData  map[string]interface{} `json:"parsed_data,omitempty"`
	EventType   string                 `json:"event_type,omitempty"`
	OrderNo     string                 `json:"order_no,omitempty"`
	TrackingNo  string                 `json:"tracking_no,omitempty"`
	ProcessedAt *time.Time             `json:"processed_at,omitempty"`
	ReceivedAt  time.Time              `json:"received_at"`
	CreatedAt   time.Time              `json:"created_at"`
}

// 演示数据
var demoRecords = []DemoRawCallbackRecord{
	{
		ID:         "demo-001",
		Provider:   "cainiao",
		EventType:  "FINISH_ORDER",
		OrderNo:    "18290278952609400",
		TrackingNo: "773370051341852",
		RawBody:    `data_digest=cnxaYsGM1SvRtz0dJEWWcQ%3D%3D&from_code=tdtradebusiness&msg_type=GUOGUO_PUSH_ORDER_EVENT&partner_code=20df7df0fa15494c801b249a8b798879&msg_id=18290278952609400&logistics_interface=%7B%22externalOrder%22%3A%7B%22externalBizIdList%22%3A%5B%22GK20250803000002912%22%5D%2C%22orderId%22%3A%2218290278952609400%22%2C%22orderStatusDesc%22%3A%22%E5%B7%B2%E5%AE%8C%E7%BB%93%22%2C%22orderStatusCode%22%3A%2240%22%7D%2C%22orderEvent%22%3A%7B%22eventDesc%22%3A%22%E8%AE%A2%E5%8D%95%E5%AE%8C%E7%BB%93%22%2C%22eventData%22%3A%7B%22mailNo%22%3A%22773370051341852%22%2C%22insuranceValue%22%3A%220%22%2C%22itemId%22%3A%223000000080%22%2C%22courierAdjustFee%22%3A%220%22%2C%22lpCode%22%3A%22LP00752904805734%22%2C%22logisticsCompanyName%22%3A%22%E7%94%B3%E9%80%9A%E5%BF%AB%E9%80%92%22%2C%22totalPrice%22%3A%22720%22%2C%22insurancePrice%22%3A%220%22%2C%22weight%22%3A%221060%22%2C%22logisticsCompanyCode%22%3A%22STO%22%2C%22basePrice%22%3A%22720%22%2C%22packageFee%22%3A%220%22%7D%2C%22eventType%22%3A%22FINISH_ORDER%22%7D%7D`,
		ReceivedAt: time.Now().Add(-2 * time.Hour),
		CreatedAt:  time.Now().Add(-2 * time.Hour),
	},
	{
		ID:         "demo-002",
		Provider:   "cainiao",
		EventType:  "SIGN",
		OrderNo:    "18291974709127629",
		TrackingNo: "773370021132165",
		RawBody:    `data_digest=test&from_code=tdtradebusiness&msg_type=GUOGUO_PUSH_ORDER_EVENT&partner_code=20df7df0fa15494c801b249a8b798879&msg_id=18291974709127629&logistics_interface=%7B%22externalOrder%22%3A%7B%22externalBizIdList%22%3A%5B%22GK20250803000001960%22%5D%2C%22orderId%22%3A%2218291974709127629%22%2C%22orderStatusDesc%22%3A%22%E5%B7%B2%E7%AD%BE%E6%94%B6%22%2C%22orderStatusCode%22%3A%2250%22%7D%2C%22orderEvent%22%3A%7B%22eventDesc%22%3A%22%E5%B7%B2%E7%AD%BE%E6%94%B6%22%2C%22eventData%22%3A%7B%22mailNo%22%3A%22773370021132165%22%7D%2C%22eventType%22%3A%22SIGN%22%7D%7D`,
		ReceivedAt: time.Now().Add(-1 * time.Hour),
		CreatedAt:  time.Now().Add(-1 * time.Hour),
	},
	{
		ID:         "demo-003",
		Provider:   "kuaidi100",
		EventType:  "status_update",
		OrderNo:    "KD100001",
		TrackingNo: "SF1234567890",
		RawBody:    `{"taskId":"KD100001","status":"polling","message":"监控中","autoCheck":"1","comOld":"","com":"shunfeng","nu":"SF1234567890","data":[{"time":"2025-08-03 10:30:00","ftime":"2025-08-03 10:30:00","context":"快件已签收","location":"北京市"}]}`,
		ReceivedAt: time.Now().Add(-30 * time.Minute),
		CreatedAt:  time.Now().Add(-30 * time.Minute),
	},
	{
		ID:         "demo-004",
		Provider:   "yida",
		EventType:  "push_type_3",
		OrderNo:    "YD001",
		TrackingNo: "YD1234567890",
		RawBody:    `{"pushType":3,"ydOrderId":"YD001","ydOrderNo":"YD1234567890","status":"已签收","statusCode":"3","updateTime":"2025-08-03 09:15:00"}`,
		ReceivedAt: time.Now().Add(-45 * time.Minute),
		CreatedAt:  time.Now().Add(-45 * time.Minute),
	},
	{
		ID:         "demo-005",
		Provider:   "yuntong",
		EventType:  "state_2",
		OrderNo:    "YT001",
		TrackingNo: "YT1234567890",
		RawBody:    `{"State":2,"TaskId":"YT001","BillCode":"YT1234567890","Message":"运输中","UpdateTime":"2025-08-03 08:45:00"}`,
		ReceivedAt: time.Now().Add(-1 * time.Hour),
		CreatedAt:  time.Now().Add(-1 * time.Hour),
	},
}

func main() {
	// 创建Gin引擎
	r := gin.Default()

	// 配置CORS
	r.Use(cors.New(cors.Config{
		AllowOrigins:     []string{"http://localhost:3000", "http://localhost:8080", "http://localhost:5173"},
		AllowMethods:     []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowHeaders:     []string{"Origin", "Content-Type", "Accept", "Authorization", "X-Requested-With"},
		ExposeHeaders:    []string{"Content-Length"},
		AllowCredentials: true,
	}))

	// API路由组
	api := r.Group("/api/v1/admin/raw-callbacks")
	{
		// 获取原始回调记录列表
		api.GET("/records", getRawCallbackRecords)

		// 获取原始回调记录详情
		api.GET("/records/:id", getRawCallbackRecordById)

		// 重推单个原始回调
		api.POST("/retry/:id", retryRawCallback)

		// 批量重推原始回调
		api.POST("/batch-retry", batchRetryRawCallbacks)

		// 按条件批量重推
		api.POST("/batch-retry-by-condition", batchRetryByCondition)

		// 获取原始回调统计
		api.GET("/statistics", getRawCallbackStatistics)

		// 导出原始回调数据
		api.GET("/export", exportRawCallbacks)
	}

	fmt.Println("🚀 原始回调管理演示服务启动成功!")
	fmt.Println("📊 管理界面: http://localhost:3000")
	fmt.Println("🔗 API地址: http://localhost:8082/api/v1/admin/raw-callbacks")
	fmt.Println("📝 演示数据: 5条不同供应商的回调记录")

	// 启动服务
	log.Fatal(r.Run(":8082"))
}

// 获取原始回调记录列表
func getRawCallbackRecords(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "50"))
	provider := c.Query("provider")
	eventType := c.Query("event_type")
	orderNo := c.Query("order_no")

	// 简单的筛选逻辑
	filteredRecords := make([]DemoRawCallbackRecord, 0)
	for _, record := range demoRecords {
		if provider != "" && record.Provider != provider {
			continue
		}
		if eventType != "" && record.EventType != eventType {
			continue
		}
		if orderNo != "" && record.OrderNo != orderNo {
			continue
		}
		filteredRecords = append(filteredRecords, record)
	}

	// 简单的分页
	total := len(filteredRecords)
	start := (page - 1) * pageSize
	end := start + pageSize
	if start > total {
		start = total
	}
	if end > total {
		end = total
	}

	records := filteredRecords[start:end]

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"records":   records,
			"total":     total,
			"page":      page,
			"page_size": pageSize,
		},
	})
}

// 获取原始回调记录详情
func getRawCallbackRecordById(c *gin.Context) {
	id := c.Param("id")

	for _, record := range demoRecords {
		if record.ID == id {
			c.JSON(http.StatusOK, gin.H{
				"success": true,
				"data":    record,
			})
			return
		}
	}

	c.JSON(http.StatusNotFound, gin.H{
		"success": false,
		"message": "记录不存在",
	})
}

// 重推单个原始回调
func retryRawCallback(c *gin.Context) {
	id := c.Param("id")

	for _, record := range demoRecords {
		if record.ID == id {
			// 模拟重推处理
			time.Sleep(500 * time.Millisecond)

			c.JSON(http.StatusOK, gin.H{
				"success": true,
				"message": fmt.Sprintf("记录 %s 重推成功", id),
			})
			return
		}
	}

	c.JSON(http.StatusNotFound, gin.H{
		"success": false,
		"message": "记录不存在",
	})
}

// 批量重推原始回调
func batchRetryRawCallbacks(c *gin.Context) {
	var req struct {
		RecordIDs []string `json:"record_ids"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "参数错误: " + err.Error(),
		})
		return
	}

	// 模拟批量重推
	time.Sleep(1 * time.Second)

	successCount := len(req.RecordIDs)
	failedCount := 0

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"total":         len(req.RecordIDs),
			"success_count": successCount,
			"failed_count":  failedCount,
			"errors":        []string{},
		},
		"message": fmt.Sprintf("批量重推完成，成功%d条，失败%d条", successCount, failedCount),
	})
}

// 按条件批量重推
func batchRetryByCondition(c *gin.Context) {
	var req struct {
		Provider  string `json:"provider"`
		EventType string `json:"event_type"`
		StartTime string `json:"start_time"`
		EndTime   string `json:"end_time"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "参数错误: " + err.Error(),
		})
		return
	}

	// 模拟按条件重推
	time.Sleep(2 * time.Second)

	total := 3
	successCount := 3
	failedCount := 0

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"total":         total,
			"success_count": successCount,
			"failed_count":  failedCount,
			"errors":        []string{},
		},
		"message": fmt.Sprintf("按条件批量重推完成，成功%d条，失败%d条", successCount, failedCount),
	})
}

// 获取原始回调统计
func getRawCallbackStatistics(c *gin.Context) {
	stats := gin.H{
		"total_records":   len(demoRecords),
		"success_records": len(demoRecords),
		"failed_records":  0,
		"pending_records": 0,
		"success_rate":    100.0,
		"provider_stats": gin.H{
			"cainiao":   gin.H{"total": 2, "success": 2, "failed": 0, "pending": 0},
			"kuaidi100": gin.H{"total": 1, "success": 1, "failed": 0, "pending": 0},
			"yida":      gin.H{"total": 1, "success": 1, "failed": 0, "pending": 0},
			"yuntong":   gin.H{"total": 1, "success": 1, "failed": 0, "pending": 0},
		},
		"event_type_stats": gin.H{
			"FINISH_ORDER":  gin.H{"total": 1, "success": 1, "failed": 0, "pending": 0},
			"SIGN":          gin.H{"total": 1, "success": 1, "failed": 0, "pending": 0},
			"status_update": gin.H{"total": 1, "success": 1, "failed": 0, "pending": 0},
			"push_type_3":   gin.H{"total": 1, "success": 1, "failed": 0, "pending": 0},
			"state_2":       gin.H{"total": 1, "success": 1, "failed": 0, "pending": 0},
		},
		"daily_stats": []gin.H{
			{"date": time.Now().Format("2006-01-02"), "total": 5, "success": 5, "failed": 0, "pending": 0},
		},
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    stats,
	})
}

// 导出原始回调数据
func exportRawCallbacks(c *gin.Context) {
	// 生成CSV数据
	csvData := "ID,供应商,事件类型,订单号,运单号,接收时间,原始数据预览\n"

	for _, record := range demoRecords {
		rawDataPreview := record.RawBody
		if len(rawDataPreview) > 100 {
			rawDataPreview = rawDataPreview[:100] + "..."
		}

		csvData += fmt.Sprintf("%s,%s,%s,%s,%s,%s,\"%s\"\n",
			record.ID,
			record.Provider,
			record.EventType,
			record.OrderNo,
			record.TrackingNo,
			record.ReceivedAt.Format("2006-01-02 15:04:05"),
			rawDataPreview,
		)
	}

	// 设置响应头
	filename := fmt.Sprintf("raw_callbacks_%s.csv", time.Now().Format("20060102_150405"))
	c.Header("Content-Type", "text/csv; charset=utf-8")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filename))

	// 写入BOM以支持Excel正确显示中文
	c.Writer.Write([]byte{0xEF, 0xBB, 0xBF})
	c.String(http.StatusOK, csvData)
}
