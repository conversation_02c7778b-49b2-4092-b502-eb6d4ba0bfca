package main

import (
	"context"
	"fmt"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/shopspring/decimal"
)

func main() {
	fmt.Println("🚀 高并发余额服务核心功能演示")

	// 1. 初始化Redis客户端
	redisClient := redis.NewClient(&redis.Options{
		Addr:     "localhost:6379",
		Password: "",
		DB:       0,
	})

	// 测试Redis连接
	ctx := context.Background()
	if err := redisClient.Ping(ctx).Err(); err != nil {
		fmt.Printf("❌ Redis连接失败: %v\n", err)
		fmt.Println("💡 请确保Redis服务运行: redis-server")
		os.Exit(1)
	}
	fmt.Println("✅ Redis连接成功")

	// 2. 创建演示服务
	demo := NewBalanceDemo(redisClient)

	// 3. 运行演示
	demo.RunDemo(ctx)

	// 4. 等待退出信号
	waitForShutdown()
}

// BalanceDemo 余额演示服务
type BalanceDemo struct {
	redisClient *redis.Client
	lockManager *SimpleLockManager
	metrics     *SimpleMetrics
}

// SimpleLockManager 简化版锁管理器
type SimpleLockManager struct {
	redisClient *redis.Client
}

// SimpleMetrics 简化版指标
type SimpleMetrics struct {
	mu                   sync.RWMutex
	TotalOperations      int64
	SuccessfulOperations int64
	FailedOperations     int64
	ConflictCount        int64
}

// NewBalanceDemo 创建余额演示
func NewBalanceDemo(redisClient *redis.Client) *BalanceDemo {
	return &BalanceDemo{
		redisClient: redisClient,
		lockManager: &SimpleLockManager{redisClient: redisClient},
		metrics:     &SimpleMetrics{},
	}
}

// RunDemo 运行演示
func (d *BalanceDemo) RunDemo(ctx context.Context) {
	fmt.Println("🔄 开始高并发余额处理演示")

	// 1. 演示分布式锁
	d.demonstrateDistributedLock(ctx)

	// 2. 演示高并发处理
	d.demonstrateHighConcurrency(ctx)

	// 3. 显示最终指标
	d.showMetrics()

	fmt.Println("✅ 演示完成")
}

// demonstrateDistributedLock 演示分布式锁
func (d *BalanceDemo) demonstrateDistributedLock(ctx context.Context) {
	fmt.Println("🔐 演示分布式锁功能")

	userID := "demo_user_001"
	
	// 测试获取锁
	success := d.lockManager.WithLock(ctx, userID, func() error {
		fmt.Printf("🔒 用户 %s 锁定成功，执行业务逻辑\n", userID)
		time.Sleep(100 * time.Millisecond)
		fmt.Println("✅ 业务逻辑执行完成")
		return nil
	})

	if success {
		fmt.Println("✅ 分布式锁测试成功")
	} else {
		fmt.Println("❌ 分布式锁测试失败")
	}
}

// demonstrateHighConcurrency 演示高并发处理
func (d *BalanceDemo) demonstrateHighConcurrency(ctx context.Context) {
	fmt.Println("🔄 演示高并发余额处理 (模拟100个并发操作)")

	userID := "demo_user_002"
	concurrency := 100
	var wg sync.WaitGroup

	startTime := time.Now()

	// 启动100个并发操作
	for i := 0; i < concurrency; i++ {
		wg.Add(1)
		go func(operationID int) {
			defer wg.Done()

			orderNo := fmt.Sprintf("ORDER_%d_%d", operationID, time.Now().UnixNano())
			amount := decimal.NewFromFloat(10.50)

			// 模拟余额操作（使用分布式锁保证一致性）
			success := d.lockManager.WithLock(ctx, userID, func() error {
				// 模拟业务处理时间
				processingTime := time.Duration(5+operationID%10) * time.Millisecond
				time.Sleep(processingTime)

				// 随机模拟成功/失败
				if operationID%10 == 0 {
					return fmt.Errorf("模拟版本冲突")
				}
				return nil
			})

			// 记录指标
			d.recordOperation(success, operationID%10 == 0)

			if success {
				fmt.Printf("✅ 操作 %d 成功 (订单: %s, 金额: %s)\n", 
					operationID, orderNo, amount.String())
			} else {
				fmt.Printf("❌ 操作 %d 失败 (版本冲突)\n", operationID)
			}
		}(i)
	}

	// 等待所有操作完成
	wg.Wait()

	duration := time.Since(startTime)
	fmt.Printf("✅ 高并发测试完成，耗时: %v\n", duration)
	fmt.Printf("📊 TPS: %.2f\n", float64(concurrency)/duration.Seconds())
}

// WithLock 在锁保护下执行操作
func (lm *SimpleLockManager) WithLock(ctx context.Context, userID string, operation func() error) bool {
	lockKey := fmt.Sprintf("balance_lock:%s", userID)
	lockValue := fmt.Sprintf("%d", time.Now().UnixNano())
	
	// 尝试获取锁（5秒过期）
	success, err := lm.redisClient.SetNX(ctx, lockKey, lockValue, 5*time.Second).Result()
	if err != nil || !success {
		return false
	}

	// 确保释放锁
	defer func() {
		// 使用Lua脚本确保只释放自己的锁
		script := `
			if redis.call("GET", KEYS[1]) == ARGV[1] then
				return redis.call("DEL", KEYS[1])
			else
				return 0
			end
		`
		lm.redisClient.Eval(ctx, script, []string{lockKey}, lockValue)
	}()

	// 执行业务操作
	err = operation()
	return err == nil
}

// recordOperation 记录操作指标
func (d *BalanceDemo) recordOperation(success bool, isConflict bool) {
	d.metrics.mu.Lock()
	defer d.metrics.mu.Unlock()

	d.metrics.TotalOperations++
	if success {
		d.metrics.SuccessfulOperations++
	} else {
		d.metrics.FailedOperations++
		if isConflict {
			d.metrics.ConflictCount++
		}
	}
}

// showMetrics 显示指标
func (d *BalanceDemo) showMetrics() {
	d.metrics.mu.RLock()
	defer d.metrics.mu.RUnlock()

	fmt.Println("📊 最终性能指标:")
	fmt.Printf("  • 总操作数: %d\n", d.metrics.TotalOperations)
	fmt.Printf("  • 成功操作: %d\n", d.metrics.SuccessfulOperations)
	fmt.Printf("  • 失败操作: %d\n", d.metrics.FailedOperations)
	fmt.Printf("  • 版本冲突: %d\n", d.metrics.ConflictCount)
	
	if d.metrics.TotalOperations > 0 {
		successRate := float64(d.metrics.SuccessfulOperations) / float64(d.metrics.TotalOperations) * 100
		conflictRate := float64(d.metrics.ConflictCount) / float64(d.metrics.TotalOperations) * 100
		fmt.Printf("  • 成功率: %.2f%%\n", successRate)
		fmt.Printf("  • 冲突率: %.2f%%\n", conflictRate)
	}
}

// waitForShutdown 等待关闭信号
func waitForShutdown() {
	fmt.Println("🔄 演示运行完成，按 Ctrl+C 退出...")

	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)

	<-quit
	fmt.Println("⏹️ 正在退出...")
}