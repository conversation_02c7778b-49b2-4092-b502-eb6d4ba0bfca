package main

import (
	"context"
	"fmt"
	"math/rand"
	"os"
	"os/signal"
	"strings"
	"sync"
	"sync/atomic"
	"syscall"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"

	"github.com/your-org/go-kuaidi/internal/service"
)

// 测试配置
const (
	// 基础配置
	CONCURRENT_USERS     = 1000  // 1000个并发用户
	OPERATIONS_PER_USER  = 100   // 每用户100次操作
	TEST_DURATION        = 5     // 测试持续5分钟
	
	// 压力配置
	MAX_GOROUTINES      = 5000   // 最大协程数
	BATCH_SIZE          = 50     // 批处理大小
	
	// 业务配置
	MIN_AMOUNT          = 1.0    // 最小金额
	MAX_AMOUNT          = 1000.0 // 最大金额
)

// 性能统计
type PerformanceStats struct {
	// 基础统计
	TotalOperations     int64 `json:"total_operations"`
	SuccessOperations   int64 `json:"success_operations"`
	FailedOperations    int64 `json:"failed_operations"`
	
	// 错误统计
	LockFailures        int64 `json:"lock_failures"`
	VersionConflicts    int64 `json:"version_conflicts"`
	TimeoutErrors       int64 `json:"timeout_errors"`
	UnknownErrors       int64 `json:"unknown_errors"`
	
	// 性能统计
	MinResponseTime     time.Duration `json:"min_response_time"`
	MaxResponseTime     time.Duration `json:"max_response_time"`
	TotalResponseTime   time.Duration `json:"total_response_time"`
	
	// 时间统计
	StartTime           time.Time `json:"start_time"`
	EndTime             time.Time `json:"end_time"`
	
	mutex sync.RWMutex
}

// 高强度压力测试主程序
func main() {
	fmt.Println("🔥🔥🔥 高强度压力测试开始 🔥🔥🔥")
	
	// 1. 初始化
	logger, _ := zap.NewProduction()
	defer logger.Sync()
	
	redisClient := redis.NewClient(&redis.Options{
		Addr:     "localhost:6379",
		Password: "",
		DB:       0,
		PoolSize: 100, // 增大连接池
	})
	
	ctx := context.Background()
	if err := redisClient.Ping(ctx).Err(); err != nil {
		fmt.Printf("❌ Redis连接失败: %v\n", err)
		fmt.Println("💡 请确保Redis服务运行: redis-server")
		os.Exit(1)
	}
	
	fmt.Println("✅ Redis连接成功")
	
	// 2. 创建测试服务
	tester := NewStressTester(redisClient, logger)
	
	// 3. 运行压力测试套件
	fmt.Println("🚀 开始高强度压力测试套件...")
	
	// 测试1: 基础并发测试
	fmt.Println("\n🎯 测试1: 基础高并发测试 (1000并发)")
	tester.RunBasicConcurrencyTest(ctx)
	
	// 测试2: 极限并发测试  
	fmt.Println("\n🎯 测试2: 极限并发测试 (5000并发)")
	tester.RunExtremeConcurrencyTest(ctx)
	
	// 测试3: 持续压力测试
	fmt.Println("\n🎯 测试3: 持续压力测试 (5分钟)")
	tester.RunSustainedPressureTest(ctx)
	
	// 测试4: 混合场景测试
	fmt.Println("\n🎯 测试4: 混合业务场景测试")
	tester.RunMixedScenarioTest(ctx)
	
	// 测试5: 故障恢复测试
	fmt.Println("\n🎯 测试5: 故障恢复能力测试")
	tester.RunFailureRecoveryTest(ctx)
	
	fmt.Println("\n🏆 所有压力测试完成！")
	
	// 4. 等待用户退出
	waitForShutdown()
}

// StressTester 压力测试器
type StressTester struct {
	redisClient     *redis.Client
	lockService     *service.BalanceLockService
	monitorService  *service.BalanceMonitoringService
	logger          *zap.Logger
	stats           *PerformanceStats
}

// NewStressTester 创建压力测试器
func NewStressTester(redisClient *redis.Client, logger *zap.Logger) *StressTester {
	return &StressTester{
		redisClient:    redisClient,
		lockService:    service.NewBalanceLockService(redisClient, logger),
		monitorService: service.NewBalanceMonitoringService(logger),
		logger:         logger,
		stats:          &PerformanceStats{
			MinResponseTime: time.Hour, // 初始化为最大值
		},
	}
}

// RunBasicConcurrencyTest 基础并发测试
func (t *StressTester) RunBasicConcurrencyTest(ctx context.Context) {
	fmt.Println("📊 启动1000个并发用户，每用户10次操作...")
	
	stats := &PerformanceStats{StartTime: time.Now(), MinResponseTime: time.Hour}
	var wg sync.WaitGroup
	
	// 启动1000个并发用户
	for userID := 0; userID < CONCURRENT_USERS; userID++ {
		wg.Add(1)
		go func(uid int) {
			defer wg.Done()
			t.simulateUserOperations(ctx, fmt.Sprintf("user_%d", uid), 10, stats)
		}(userID)
	}
	
	wg.Wait()
	stats.EndTime = time.Now()
	t.printTestResults("基础并发测试", stats)
}

// RunExtremeConcurrencyTest 极限并发测试
func (t *StressTester) RunExtremeConcurrencyTest(ctx context.Context) {
	fmt.Println("🔥 启动5000个极限并发，测试系统承载能力...")
	
	stats := &PerformanceStats{StartTime: time.Now(), MinResponseTime: time.Hour}
	
	// 使用协程池控制并发数
	semaphore := make(chan struct{}, MAX_GOROUTINES)
	var wg sync.WaitGroup
	
	for userID := 0; userID < MAX_GOROUTINES; userID++ {
		wg.Add(1)
		semaphore <- struct{}{} // 获取信号量
		
		go func(uid int) {
			defer func() {
				<-semaphore // 释放信号量
				wg.Done()
			}()
			t.simulateUserOperations(ctx, fmt.Sprintf("extreme_user_%d", uid), 5, stats)
		}(userID)
	}
	
	wg.Wait()
	stats.EndTime = time.Now()
	t.printTestResults("极限并发测试", stats)
}

// RunSustainedPressureTest 持续压力测试
func (t *StressTester) RunSustainedPressureTest(ctx context.Context) {
	fmt.Println("⏰ 启动5分钟持续压力测试...")
	
	stats := &PerformanceStats{StartTime: time.Now(), MinResponseTime: time.Hour}
	
	// 设置测试持续时间
	testCtx, cancel := context.WithTimeout(ctx, TEST_DURATION*time.Minute)
	defer cancel()
	
	var wg sync.WaitGroup
	
	// 持续启动工作者
	for workerID := 0; workerID < 100; workerID++ {
		wg.Add(1)
		go func(wid int) {
			defer wg.Done()
			t.sustainedWorker(testCtx, fmt.Sprintf("sustained_worker_%d", wid), stats)
		}(workerID)
	}
	
	wg.Wait()
	stats.EndTime = time.Now()
	t.printTestResults("持续压力测试", stats)
}

// RunMixedScenarioTest 混合场景测试
func (t *StressTester) RunMixedScenarioTest(ctx context.Context) {
	fmt.Println("🎭 模拟真实业务场景：充值、消费、退款混合测试...")
	
	stats := &PerformanceStats{StartTime: time.Now(), MinResponseTime: time.Hour}
	var wg sync.WaitGroup
	
	// 场景1: 大量小额充值
	for i := 0; i < 500; i++ {
		wg.Add(1)
		go func(uid int) {
			defer wg.Done()
			t.simulateDepositScenario(ctx, fmt.Sprintf("deposit_user_%d", uid), stats)
		}(i)
	}
	
	// 场景2: 高频消费操作
	for i := 0; i < 300; i++ {
		wg.Add(1)
		go func(uid int) {
			defer wg.Done()
			t.simulateConsumptionScenario(ctx, fmt.Sprintf("consume_user_%d", uid), stats)
		}(i)
	}
	
	// 场景3: 退款处理
	for i := 0; i < 200; i++ {
		wg.Add(1)
		go func(uid int) {
			defer wg.Done()
			t.simulateRefundScenario(ctx, fmt.Sprintf("refund_user_%d", uid), stats)
		}(i)
	}
	
	wg.Wait()
	stats.EndTime = time.Now()
	t.printTestResults("混合场景测试", stats)
}

// RunFailureRecoveryTest 故障恢复测试
func (t *StressTester) RunFailureRecoveryTest(ctx context.Context) {
	fmt.Println("🛠️ 测试系统故障恢复能力...")
	
	stats := &PerformanceStats{StartTime: time.Now(), MinResponseTime: time.Hour}
	var wg sync.WaitGroup
	
	// 模拟网络延迟和超时
	for i := 0; i < 100; i++ {
		wg.Add(1)
		go func(uid int) {
			defer wg.Done()
			t.simulateNetworkLatency(ctx, fmt.Sprintf("latency_user_%d", uid), stats)
		}(i)
	}
	
	wg.Wait()
	stats.EndTime = time.Now()
	t.printTestResults("故障恢复测试", stats)
}

// simulateUserOperations 模拟用户操作
func (t *StressTester) simulateUserOperations(ctx context.Context, userID string, operationCount int, stats *PerformanceStats) {
	for i := 0; i < operationCount; i++ {
		startTime := time.Now()
		
		// 随机选择操作类型
		operationType := rand.Intn(3)
		var err error
		
		switch operationType {
		case 0: // 预扣费操作
			amount := t.randomAmount()
			orderNo := fmt.Sprintf("ORDER_%s_%d_%d", userID, i, time.Now().UnixNano())
			err = t.simulatePreCharge(ctx, userID, orderNo, amount)
		case 1: // 退款操作  
			amount := t.randomAmount()
			orderNo := fmt.Sprintf("REFUND_%s_%d_%d", userID, i, time.Now().UnixNano())
			err = t.simulateRefund(ctx, userID, orderNo, amount)
		case 2: // 锁竞争操作
			err = t.simulateLockContention(ctx, userID)
		}
		
		responseTime := time.Since(startTime)
		t.recordOperation(stats, err, responseTime)
		
		// 随机间隔，模拟真实用户行为
		time.Sleep(time.Duration(rand.Intn(10)) * time.Millisecond)
	}
}

// simulatePreCharge 模拟预扣费
func (t *StressTester) simulatePreCharge(ctx context.Context, userID, orderNo string, amount decimal.Decimal) error {
	return t.lockService.WithUserLock(ctx, userID, func() error {
		// 模拟复杂的业务逻辑
		time.Sleep(time.Duration(rand.Intn(10)+5) * time.Millisecond)
		
		// 模拟10%的业务失败率
		if rand.Intn(10) == 0 {
			return fmt.Errorf("业务逻辑失败")
		}
		
		return nil
	})
}

// simulateRefund 模拟退款
func (t *StressTester) simulateRefund(ctx context.Context, userID, orderNo string, amount decimal.Decimal) error {
	return t.lockService.WithUserLock(ctx, userID, func() error {
		// 模拟退款验证逻辑
		time.Sleep(time.Duration(rand.Intn(15)+10) * time.Millisecond)
		return nil
	})
}

// simulateLockContention 模拟锁竞争
func (t *StressTester) simulateLockContention(ctx context.Context, userID string) error {
	// 多个操作竞争同一个用户的锁
	hotUserID := fmt.Sprintf("hot_user_%d", rand.Intn(10)) // 10个热点用户
	
	return t.lockService.WithUserLock(ctx, hotUserID, func() error {
		// 模拟较长的业务处理时间，增加锁竞争
		time.Sleep(time.Duration(rand.Intn(50)+20) * time.Millisecond)
		return nil
	})
}

// sustainedWorker 持续工作者
func (t *StressTester) sustainedWorker(ctx context.Context, workerID string, stats *PerformanceStats) {
	operationCount := 0
	for {
		select {
		case <-ctx.Done():
			t.logger.Info("持续测试工作者退出", 
				zap.String("worker_id", workerID),
				zap.Int("total_operations", operationCount))
			return
		default:
			startTime := time.Now()
			
			// 执行随机操作
			userID := fmt.Sprintf("user_%d", rand.Intn(100))
			orderNo := fmt.Sprintf("ORDER_%s_%d", workerID, operationCount)
			amount := t.randomAmount()
			
			err := t.simulatePreCharge(ctx, userID, orderNo, amount)
			
			responseTime := time.Since(startTime)
			t.recordOperation(stats, err, responseTime)
			operationCount++
			
			// 控制操作频率
			time.Sleep(time.Duration(rand.Intn(20)+10) * time.Millisecond)
		}
	}
}

// simulateDepositScenario 模拟充值场景
func (t *StressTester) simulateDepositScenario(ctx context.Context, userID string, stats *PerformanceStats) {
	// 模拟多次小额充值
	for i := 0; i < 5; i++ {
		startTime := time.Now()
		amount := decimal.NewFromFloat(float64(rand.Intn(100) + 10)) // 10-110元
		
		// 模拟充值操作
		err := t.lockService.WithUserLock(ctx, userID, func() error {
			// 使用amount进行充值验证
			if amount.LessThan(decimal.NewFromFloat(10)) {
				return fmt.Errorf("充值金额过小")
			}
			time.Sleep(time.Duration(rand.Intn(20)+10) * time.Millisecond)
			return nil
		})
		
		responseTime := time.Since(startTime)
		t.recordOperation(stats, err, responseTime)
	}
}

// simulateConsumptionScenario 模拟消费场景
func (t *StressTester) simulateConsumptionScenario(ctx context.Context, userID string, stats *PerformanceStats) {
	// 模拟连续消费
	for i := 0; i < 10; i++ {
		startTime := time.Now()
		orderNo := fmt.Sprintf("CONSUME_%s_%d", userID, i)
		amount := t.randomAmount()
		
		err := t.simulatePreCharge(ctx, userID, orderNo, amount)
		
		responseTime := time.Since(startTime)
		t.recordOperation(stats, err, responseTime)
		
		time.Sleep(time.Duration(rand.Intn(5)) * time.Millisecond)
	}
}

// simulateRefundScenario 模拟退款场景
func (t *StressTester) simulateRefundScenario(ctx context.Context, userID string, stats *PerformanceStats) {
	// 模拟批量退款
	for i := 0; i < 3; i++ {
		startTime := time.Now()
		orderNo := fmt.Sprintf("REFUND_%s_%d", userID, i)
		amount := t.randomAmount()
		
		err := t.simulateRefund(ctx, userID, orderNo, amount)
		
		responseTime := time.Since(startTime)
		t.recordOperation(stats, err, responseTime)
		
		time.Sleep(time.Duration(rand.Intn(30)+10) * time.Millisecond)
	}
}

// simulateNetworkLatency 模拟网络延迟
func (t *StressTester) simulateNetworkLatency(ctx context.Context, userID string, stats *PerformanceStats) {
	for i := 0; i < 5; i++ {
		startTime := time.Now()
		
		// 模拟网络延迟
		time.Sleep(time.Duration(rand.Intn(100)+50) * time.Millisecond)
		
		err := t.lockService.WithUserLock(ctx, userID, func() error {
			// 模拟偶尔的超时
			if rand.Intn(20) == 0 {
				return fmt.Errorf("模拟超时")
			}
			return nil
		})
		
		responseTime := time.Since(startTime)
		t.recordOperation(stats, err, responseTime)
	}
}

// randomAmount 生成随机金额
func (t *StressTester) randomAmount() decimal.Decimal {
	amount := MIN_AMOUNT + rand.Float64()*(MAX_AMOUNT-MIN_AMOUNT)
	return decimal.NewFromFloat(amount)
}

// recordOperation 记录操作结果
func (t *StressTester) recordOperation(stats *PerformanceStats, err error, responseTime time.Duration) {
	stats.mutex.Lock()
	defer stats.mutex.Unlock()
	
	atomic.AddInt64(&stats.TotalOperations, 1)
	
	if err != nil {
		atomic.AddInt64(&stats.FailedOperations, 1)
		
		// 错误分类统计
		errStr := err.Error()
		switch {
		case contains(errStr, "获取锁失败") || contains(errStr, "lock"):
			atomic.AddInt64(&stats.LockFailures, 1)
		case contains(errStr, "版本冲突") || contains(errStr, "version conflict"):
			atomic.AddInt64(&stats.VersionConflicts, 1)
		case contains(errStr, "超时") || contains(errStr, "timeout"):
			atomic.AddInt64(&stats.TimeoutErrors, 1)
		default:
			atomic.AddInt64(&stats.UnknownErrors, 1)
		}
	} else {
		atomic.AddInt64(&stats.SuccessOperations, 1)
	}
	
	// 更新响应时间统计
	if responseTime < stats.MinResponseTime {
		stats.MinResponseTime = responseTime
	}
	if responseTime > stats.MaxResponseTime {
		stats.MaxResponseTime = responseTime
	}
	stats.TotalResponseTime += responseTime
}

// printTestResults 打印测试结果
func (t *StressTester) printTestResults(testName string, stats *PerformanceStats) {
	duration := stats.EndTime.Sub(stats.StartTime)
	
	fmt.Printf("\n" + strings.Repeat("=", 60) + "\n")
	fmt.Printf("🏆 %s 结果报告\n", testName)
	fmt.Printf(strings.Repeat("=", 60) + "\n")
	
	// 基础统计
	fmt.Printf("📊 基础统计:\n")
	fmt.Printf("  • 测试时长: %v\n", duration)
	fmt.Printf("  • 总操作数: %d\n", stats.TotalOperations)
	fmt.Printf("  • 成功操作: %d\n", stats.SuccessOperations)
	fmt.Printf("  • 失败操作: %d\n", stats.FailedOperations)
	
	if stats.TotalOperations > 0 {
		successRate := float64(stats.SuccessOperations) / float64(stats.TotalOperations) * 100
		fmt.Printf("  • 成功率: %.2f%%\n", successRate)
		
		// TPS计算
		tps := float64(stats.TotalOperations) / duration.Seconds()
		fmt.Printf("  • TPS: %.2f 操作/秒\n", tps)
		
		// 平均响应时间
		avgResponseTime := stats.TotalResponseTime / time.Duration(stats.TotalOperations)
		fmt.Printf("  • 平均响应时间: %v\n", avgResponseTime)
		fmt.Printf("  • 最小响应时间: %v\n", stats.MinResponseTime)
		fmt.Printf("  • 最大响应时间: %v\n", stats.MaxResponseTime)
	}
	
	// 错误分析
	if stats.FailedOperations > 0 {
		fmt.Printf("\n🔍 错误分析:\n")
		fmt.Printf("  • 锁获取失败: %d\n", stats.LockFailures)
		fmt.Printf("  • 版本冲突: %d\n", stats.VersionConflicts)
		fmt.Printf("  • 超时错误: %d\n", stats.TimeoutErrors)
		fmt.Printf("  • 其他错误: %d\n", stats.UnknownErrors)
	}
	
	// 性能评级
	fmt.Printf("\n🎯 性能评级: ")
	if stats.TotalOperations > 0 {
		successRate := float64(stats.SuccessOperations) / float64(stats.TotalOperations) * 100
		tps := float64(stats.TotalOperations) / duration.Seconds()
		
		switch {
		case successRate >= 95 && tps >= 1000:
			fmt.Printf("🏆 优秀 (成功率: %.1f%%, TPS: %.0f)\n", successRate, tps)
		case successRate >= 90 && tps >= 500:
			fmt.Printf("✅ 良好 (成功率: %.1f%%, TPS: %.0f)\n", successRate, tps)
		case successRate >= 80 && tps >= 200:
			fmt.Printf("⚠️ 一般 (成功率: %.1f%%, TPS: %.0f)\n", successRate, tps)
		default:
			fmt.Printf("❌ 需要优化 (成功率: %.1f%%, TPS: %.0f)\n", successRate, tps)
		}
	}
	
	fmt.Printf(strings.Repeat("=", 60) + "\n")
}

// contains 字符串包含检查
func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr || (len(s) > len(substr) && 
		(s[:len(substr)] == substr || s[len(s)-len(substr):] == substr || containsInMiddle(s, substr))))
}

func containsInMiddle(s, substr string) bool {
	for i := 1; i <= len(s)-len(substr)-1; i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

// waitForShutdown 等待关闭信号
func waitForShutdown() {
	fmt.Println("\n🔄 所有测试完成，按 Ctrl+C 退出...")
	
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	
	<-quit
	fmt.Println("⏹️ 正在退出...")
}