package main

import (
	"context"
	"database/sql"
	"flag"
	"fmt"
	"os"
	"time"

	_ "github.com/lib/pq"
	"go.uber.org/zap"

	"github.com/your-org/go-kuaidi/internal/repository"
	"github.com/your-org/go-kuaidi/internal/service"
)

// MigrateCommand 迁移命令
type MigrateCommand struct {
	// 数据库连接参数
	dbURL string

	// 迁移参数
	batchSize           int
	maxConcurrency      int
	delayBetweenBatches time.Duration
	dryRun              bool
	maxRecords          int
	startDate           string
	endDate             string

	// 其他参数
	verbose bool
	help    bool
}

func main() {
	cmd := &MigrateCommand{}

	// 解析命令行参数
	flag.StringVar(&cmd.dbURL, "db-url", "", "数据库连接URL (必填)")
	flag.IntVar(&cmd.batchSize, "batch-size", 100, "批处理大小")
	flag.IntVar(&cmd.maxConcurrency, "max-concurrency", 5, "最大并发数")
	flag.DurationVar(&cmd.delayBetweenBatches, "delay", 1*time.Second, "批次间延迟")
	flag.BoolVar(&cmd.dryRun, "dry-run", true, "试运行模式（默认开启）")
	flag.IntVar(&cmd.maxRecords, "max-records", 0, "最大处理记录数（0表示无限制）")
	flag.StringVar(&cmd.startDate, "start-date", "", "开始日期 (YYYY-MM-DD)")
	flag.StringVar(&cmd.endDate, "end-date", "", "结束日期 (YYYY-MM-DD)")
	flag.BoolVar(&cmd.verbose, "verbose", false, "详细输出")
	flag.BoolVar(&cmd.help, "help", false, "显示帮助信息")

	flag.Parse()

	// 显示帮助信息
	if cmd.help {
		cmd.showHelp()
		return
	}

	// 验证必填参数
	if cmd.dbURL == "" {
		fmt.Fprintf(os.Stderr, "错误: 数据库连接URL是必填参数\n")
		cmd.showUsage()
		os.Exit(1)
	}

	// 执行迁移
	if err := cmd.run(); err != nil {
		fmt.Fprintf(os.Stderr, "迁移失败: %v\n", err)
		os.Exit(1)
	}
}

// showHelp 显示帮助信息
func (cmd *MigrateCommand) showHelp() {
	fmt.Println("历史订单平台订单号迁移工具")
	fmt.Println()
	fmt.Println("用途:")
	fmt.Println("  为没有平台订单号的历史订单生成平台订单号")
	fmt.Println()
	fmt.Println("用法:")
	fmt.Println("  migrate [选项]")
	fmt.Println()
	fmt.Println("选项:")
	flag.PrintDefaults()
	fmt.Println()
	fmt.Println("示例:")
	fmt.Println("  # 试运行模式（默认）")
	fmt.Println("  migrate -db-url=\"********************************/dbname\"")
	fmt.Println()
	fmt.Println("  # 实际执行迁移")
	fmt.Println("  migrate -db-url=\"********************************/dbname\" -dry-run=false")
	fmt.Println()
	fmt.Println("  # 限制处理记录数")
	fmt.Println("  migrate -db-url=\"********************************/dbname\" -max-records=1000")
	fmt.Println()
	fmt.Println("  # 指定日期范围")
	fmt.Println("  migrate -db-url=\"********************************/dbname\" -start-date=2025-01-01 -end-date=2025-07-11")
	fmt.Println()
	fmt.Println("安全提示:")
	fmt.Println("  - 默认为试运行模式，不会修改数据")
	fmt.Println("  - 建议先在试运行模式下验证")
	fmt.Println("  - 生产环境请在低峰期执行")
	fmt.Println("  - 建议先备份数据库")
}

// showUsage 显示用法
func (cmd *MigrateCommand) showUsage() {
	fmt.Fprintf(os.Stderr, "用法: migrate [选项]\n")
	fmt.Fprintf(os.Stderr, "使用 -help 查看详细帮助信息\n")
}

// run 执行迁移
func (cmd *MigrateCommand) run() error {
	// 创建日志记录器
	var logger *zap.Logger
	var err error
	if cmd.verbose {
		logger, err = zap.NewDevelopment()
	} else {
		logger, err = zap.NewProduction()
	}
	if err != nil {
		return fmt.Errorf("创建日志记录器失败: %w", err)
	}
	defer logger.Sync()

	// 连接数据库
	db, err := sql.Open("postgres", cmd.dbURL)
	if err != nil {
		return fmt.Errorf("连接数据库失败: %w", err)
	}
	defer db.Close()

	// 测试数据库连接
	if err := db.Ping(); err != nil {
		return fmt.Errorf("数据库连接测试失败: %w", err)
	}

	logger.Info("数据库连接成功")

	// 创建服务
	orderRepository := repository.NewPostgresOrderRepository(db)
	platformOrderGenerator := service.NewPlatformOrderGenerator(db, logger, &service.PlatformOrderConfig{
		Prefix:           "GK",
		CacheSize:        100,
		RetryAttempts:    3,
		RetryDelay:       100 * time.Millisecond,
		EnableMetrics:    false,
		EnableLocalCache: false,
	})
	migrationService := service.NewHistoricalOrderMigrationService(orderRepository, platformOrderGenerator, logger)

	// 构建迁移配置
	config, err := cmd.buildMigrationConfig()
	if err != nil {
		return fmt.Errorf("构建迁移配置失败: %w", err)
	}

	// 显示迁移配置
	cmd.showMigrationConfig(config, logger)

	// 获取迁移状态
	status, err := migrationService.GetMigrationStatus(context.Background())
	if err != nil {
		logger.Warn("获取迁移状态失败", zap.Error(err))
	} else {
		cmd.showMigrationStatus(status, logger)
	}

	// 确认执行
	if !cmd.dryRun {
		fmt.Print("⚠️  这将修改数据库中的订单记录，确认继续吗？(y/N): ")
		var confirm string
		fmt.Scanln(&confirm)
		if confirm != "y" && confirm != "Y" {
			fmt.Println("迁移已取消")
			return nil
		}
	}

	// 执行迁移
	logger.Info("开始执行历史订单迁移")
	result, err := migrationService.MigrateHistoricalOrders(context.Background(), config)
	if err != nil {
		return fmt.Errorf("执行迁移失败: %w", err)
	}

	// 显示迁移结果
	cmd.showMigrationResult(result, logger)

	return nil
}

// buildMigrationConfig 构建迁移配置
func (cmd *MigrateCommand) buildMigrationConfig() (*service.MigrationConfig, error) {
	config := &service.MigrationConfig{
		BatchSize:           cmd.batchSize,
		MaxConcurrency:      cmd.maxConcurrency,
		DelayBetweenBatches: cmd.delayBetweenBatches,
		DryRun:              cmd.dryRun,
		MaxRecords:          cmd.maxRecords,
	}

	// 解析开始日期
	if cmd.startDate != "" {
		startDate, err := time.Parse("2006-01-02", cmd.startDate)
		if err != nil {
			return nil, fmt.Errorf("开始日期格式错误: %w", err)
		}
		config.StartDate = &startDate
	}

	// 解析结束日期
	if cmd.endDate != "" {
		endDate, err := time.Parse("2006-01-02", cmd.endDate)
		if err != nil {
			return nil, fmt.Errorf("结束日期格式错误: %w", err)
		}
		// 设置为当天的23:59:59
		endDate = endDate.Add(23*time.Hour + 59*time.Minute + 59*time.Second)
		config.EndDate = &endDate
	}

	return config, nil
}

// showMigrationConfig 显示迁移配置
func (cmd *MigrateCommand) showMigrationConfig(config *service.MigrationConfig, _ *zap.Logger) {
	fmt.Println("📋 迁移配置:")
	fmt.Printf("  批处理大小: %d\n", config.BatchSize)
	fmt.Printf("  最大并发数: %d\n", config.MaxConcurrency)
	fmt.Printf("  批次间延迟: %v\n", config.DelayBetweenBatches)
	fmt.Printf("  试运行模式: %v\n", config.DryRun)
	fmt.Printf("  最大记录数: %d\n", config.MaxRecords)

	if config.StartDate != nil {
		fmt.Printf("  开始日期: %s\n", config.StartDate.Format("2006-01-02"))
	}
	if config.EndDate != nil {
		fmt.Printf("  结束日期: %s\n", config.EndDate.Format("2006-01-02"))
	}

	fmt.Println()
}

// showMigrationStatus 显示迁移状态
func (cmd *MigrateCommand) showMigrationStatus(status *service.MigrationProgress, _ *zap.Logger) {
	fmt.Println("📊 当前迁移状态:")
	fmt.Printf("  总订单数: %d\n", status.TotalRecords)
	fmt.Printf("  已迁移数: %d\n", status.ProcessedRecords)
	fmt.Printf("  迁移进度: %.2f%%\n", status.Progress)
	fmt.Println()
}

// showMigrationResult 显示迁移结果
func (cmd *MigrateCommand) showMigrationResult(result *service.MigrationResult, _ *zap.Logger) {
	fmt.Println("🎉 迁移完成!")
	fmt.Println("📈 迁移结果:")
	fmt.Printf("  总记录数: %d\n", result.TotalRecords)
	fmt.Printf("  已处理数: %d\n", result.ProcessedRecords)
	fmt.Printf("  成功数量: %d\n", result.SuccessRecords)
	fmt.Printf("  失败数量: %d\n", result.FailedRecords)
	fmt.Printf("  跳过数量: %d\n", result.SkippedRecords)
	fmt.Printf("  执行时间: %v\n", result.Duration)
	fmt.Printf("  试运行模式: %v\n", result.DryRun)

	if len(result.Errors) > 0 {
		fmt.Println("❌ 错误信息:")
		for i, err := range result.Errors {
			fmt.Printf("  %d. %s\n", i+1, err)
		}
	}

	if result.DryRun {
		fmt.Println()
		fmt.Println("💡 提示: 这是试运行模式，没有实际修改数据")
		fmt.Println("   如需实际执行，请使用 -dry-run=false 参数")
	} else {
		fmt.Println()
		fmt.Println("✅ 数据库已更新，迁移完成")
	}
}
