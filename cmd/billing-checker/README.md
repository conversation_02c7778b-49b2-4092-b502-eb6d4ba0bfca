# 订单费用差异检查和修复工具

## 概述

订单费用差异检查和修复工具是一个企业级的定时任务脚本，用于检查和修复快递物流系统中的订单费用差异，确保系统计费准确性。

## 功能特性

### 🔍 核心功能
- **订单数据获取**: 从数据库获取指定时间范围内的订单数据
- **交易记录分析**: 分析balance_transactions表中的交易记录
- **回调费用统计**: 支持多供应商回调费用格式（菜鸟、快递100、易达、云通）
- **费用差异检测**: 自动识别多扣、少扣、未扣费等情况
- **自动修复**: 调用ProcessBillingDifference方法进行费用调整

### 🚀 技术特性
- **高性能**: 支持并发处理，可配置工作协程数
- **高可靠**: 完整的错误处理和事务一致性
- **高可观测**: 集成Prometheus监控指标和结构化日志
- **灵活配置**: 支持命令行参数和配置文件
- **多种输出**: 支持文本、JSON、CSV格式报告

## 快速开始

### 环境要求

- Go 1.23.0+
- PostgreSQL 数据库
- Redis（可选，用于缓存）

### 安装

```bash
# 克隆项目
git clone https://github.com/your-org/go-kuaidi.git
cd go-kuaidi

# 构建工具
go build -o billing-checker ./cmd/billing-checker
```

### 基本使用

```bash
# 干运行模式 - 只检查不修复
./billing-checker -dry-run -days=3

# 检查并修复最近7天的订单
./billing-checker -days=7

# 详细模式并输出到文件
./billing-checker -verbose -output=report.txt

# 高并发模式
./billing-checker -workers=10 -batch-size=200
```

## 命令行参数

### 基本选项
- `-config string`: 配置文件路径 (默认: config/config.yaml)
- `-dry-run`: 干运行模式，只检查不修复
- `-days int`: 检查最近N天的订单 (默认: 7)
- `-verbose`: 详细输出
- `-help`: 显示帮助信息

### 高级选项
- `-batch-size int`: 批处理大小 (默认: 100)
- `-workers int`: 并发工作协程数 (默认: 5)
- `-timeout duration`: 总超时时间 (默认: 30m)
- `-report-format string`: 报告格式: text, json, csv (默认: text)
- `-output string`: 输出文件路径（为空则输出到控制台）

## 使用示例

### 1. 日常检查
```bash
# 检查最近3天的订单（干运行）
./billing-checker -dry-run -days=3 -verbose

# 检查并修复最近7天的订单
./billing-checker -days=7 -output=daily-check.txt
```

### 2. 批量处理
```bash
# 高并发处理大量订单
./billing-checker -days=30 -workers=20 -batch-size=500 -timeout=2h

# 生成详细报告
./billing-checker -days=7 -report-format=json -output=billing-report.json
```

### 3. 定时任务
```bash
# 添加到crontab，每天凌晨2点执行
0 2 * * * /path/to/billing-checker -days=1 -output=/var/log/billing-check.log
```

## Docker 部署

### 构建镜像
```bash
docker build -t billing-checker -f cmd/billing-checker/Dockerfile .
```

### 运行容器
```bash
# 干运行模式
docker run --rm -v $(pwd)/reports:/app/reports billing-checker -dry-run -days=7

# 完整环境（包含数据库和监控）
cd cmd/billing-checker
docker-compose up -d
```

### Docker Compose 服务
- `billing-checker`: 主应用程序
- `postgres`: PostgreSQL 数据库
- `redis`: Redis 缓存
- `prometheus`: 监控指标收集
- `grafana`: 监控仪表板

## 监控和日志

### Prometheus 指标
- `billing_difference_checks_total`: 检查总次数
- `billing_difference_check_duration_seconds`: 检查耗时
- `billing_difference_orders_processed_total`: 处理订单总数
- `billing_difference_problems_found_total`: 发现问题订单数
- `billing_difference_fixes_applied_total`: 修复成功订单数
- `billing_difference_fixes_failed_total`: 修复失败订单数

### 日志格式
使用结构化日志（JSON格式），包含以下字段：
- `timestamp`: 时间戳
- `level`: 日志级别
- `message`: 日志消息
- `platform_order_no`: 平台订单号
- `user_id`: 用户ID
- `difference_amount`: 差异金额
- `error`: 错误信息（如有）

## 配置说明

### 数据库配置
```yaml
database:
  connection_string: "postgresql://postgres:password@localhost:5432/go_kuaidi"
  max_open_conns: 25
  max_idle_conns: 5
  conn_max_lifetime: 300
```

### 应用配置
```yaml
billing_checker:
  default_batch_size: 100
  default_workers: 5
  default_timeout: "30m"
  threshold_amount: 0.01  # 1分钱精度阈值
```

## 故障排除

### 常见问题

1. **数据库连接失败**
   ```
   错误: 数据库连接测试失败
   解决: 检查数据库连接字符串和网络连接
   ```

2. **内存不足**
   ```
   错误: 处理大量订单时内存不足
   解决: 减少batch-size或增加系统内存
   ```

3. **超时错误**
   ```
   错误: 处理超时
   解决: 增加timeout参数或减少处理范围
   ```

### 调试模式
```bash
# 启用详细日志
./billing-checker -verbose -days=1

# 使用小批次测试
./billing-checker -dry-run -days=1 -batch-size=10 -workers=1
```

## 开发指南

### 项目结构
```
cmd/billing-checker/
├── main.go                    # 主程序入口
├── Dockerfile                 # Docker镜像构建文件
├── docker-compose.yml         # Docker Compose配置
└── README.md                  # 使用说明

internal/service/
├── billing_difference_checker.go      # 核心检查逻辑
├── billing_difference_checker_test.go # 单元测试
└── billing_checker_helpers.go         # 辅助函数
```

### 运行测试
```bash
# 运行单元测试
go test ./internal/service -v

# 运行集成测试
go test ./internal/service -tags=integration -v

# 生成测试覆盖率报告
go test ./internal/service -coverprofile=coverage.out
go tool cover -html=coverage.out
```

### 贡献指南
1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License - 详见 [LICENSE](../../LICENSE) 文件

## 支持

如有问题或建议，请提交 [Issue](https://github.com/your-org/go-kuaidi/issues) 或联系开发团队。
