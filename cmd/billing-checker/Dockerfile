# 订单费用差异检查工具 Docker 镜像
# 基于 Go 1.23.0 构建

# 构建阶段
FROM golang:1.23.0-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装必要的系统依赖
RUN apk add --no-cache git ca-certificates tzdata

# 复制 go mod 文件
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download

# 复制源代码
COPY . .

# 构建应用程序
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o billing-checker ./cmd/billing-checker

# 运行阶段
FROM alpine:latest

# 安装必要的运行时依赖
RUN apk --no-cache add ca-certificates tzdata

# 设置时区为北京时间
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
RUN echo 'Asia/Shanghai' > /etc/timezone

# 创建非root用户
RUN addgroup -g 1001 appgroup && \
    adduser -D -u 1001 -G appgroup appuser

# 设置工作目录
WORKDIR /app

# 从构建阶段复制二进制文件
COPY --from=builder /app/billing-checker .

# 创建日志目录
RUN mkdir -p /app/logs && chown -R appuser:appgroup /app

# 切换到非root用户
USER appuser

# 设置环境变量
ENV TZ=Asia/Shanghai
ENV LANG=zh_CN.UTF-8

# 暴露健康检查端口（如果需要）
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD ./billing-checker -help || exit 1

# 默认命令
ENTRYPOINT ["./billing-checker"]

# 默认参数（可以被覆盖）
CMD ["-days=7", "-batch-size=100", "-workers=5"]

# 标签信息
LABEL maintainer="Go Kuaidi Team"
LABEL version="1.0.0"
LABEL description="订单费用差异检查和修复工具"
LABEL org.opencontainers.image.title="Billing Difference Checker"
LABEL org.opencontainers.image.description="企业级订单费用差异检查和自动修复工具"
LABEL org.opencontainers.image.version="1.0.0"
LABEL org.opencontainers.image.created="2025-01-29"
LABEL org.opencontainers.image.source="https://github.com/your-org/go-kuaidi"
LABEL org.opencontainers.image.licenses="MIT"
