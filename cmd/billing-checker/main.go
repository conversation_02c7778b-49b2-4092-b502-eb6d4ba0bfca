package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"os"
	"time"

	"github.com/your-org/go-kuaidi/internal/service"
	"go.uber.org/zap"
)

var (
	// 命令行参数
	configFile = flag.String("config", "config/config.yaml", "配置文件路径")
	dryRun     = flag.Bool("dry-run", false, "干运行模式，只检查不修复")
	days       = flag.Int("days", 7, "检查最近N天的订单")
	verbose    = flag.Bool("verbose", false, "详细输出")
	help       = flag.Bool("help", false, "显示帮助信息")

	// 高级选项
	batchSize    = flag.Int("batch-size", 100, "批处理大小")
	maxWorkers   = flag.Int("workers", 5, "并发工作协程数")
	timeout      = flag.Duration("timeout", 30*time.Minute, "总超时时间")
	reportFormat = flag.String("report-format", "text", "报告格式: text, json, csv")
	outputFile   = flag.String("output", "", "输出文件路径（为空则输出到控制台）")
)

func main() {
	flag.Parse()

	if *help {
		printUsage()
		os.Exit(0)
	}

	// 🕐 设置应用程序时区为北京时间
	location, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		log.Fatalf("设置时区失败: %v", err)
	}
	time.Local = location

	// 初始化日志
	logger := initLogger(*verbose)
	defer logger.Sync()

	logger.Info("启动订单费用差异检查工具",
		zap.String("config_file", *configFile),
		zap.Bool("dry_run", *dryRun),
		zap.Int("days", *days),
		zap.Int("batch_size", *batchSize),
		zap.Int("workers", *maxWorkers),
		zap.Duration("timeout", *timeout))

	// 初始化配置系统
	configManager := service.NewMockConfigManager()
	if err := configManager.Load(); err != nil {
		logger.Fatal("加载配置失败", zap.Error(err))
	}

	// 创建应用上下文
	ctx, cancel := context.WithTimeout(context.Background(), *timeout)
	defer cancel()

	// 运行费用差异检查
	if err := runBillingCheck(ctx, logger, configManager); err != nil {
		logger.Fatal("费用差异检查失败", zap.Error(err))
	}

	logger.Info("费用差异检查完成")
}

// runBillingCheck 运行费用差异检查
func runBillingCheck(ctx context.Context, logger *zap.Logger, configManager service.ConfigManager) error {
	// 创建费用差异检查服务
	checkerService, err := service.NewBillingDifferenceChecker(&service.BillingCheckerConfig{
		DryRun:       *dryRun,
		Days:         *days,
		BatchSize:    *batchSize,
		MaxWorkers:   *maxWorkers,
		ReportFormat: *reportFormat,
		OutputFile:   *outputFile,
	}, logger, configManager)
	if err != nil {
		return fmt.Errorf("创建费用差异检查服务失败: %w", err)
	}

	// 执行检查
	report, err := checkerService.CheckAndFix(ctx)
	if err != nil {
		return fmt.Errorf("执行费用差异检查失败: %w", err)
	}

	// 输出报告
	if err := outputReport(report, logger); err != nil {
		return fmt.Errorf("输出报告失败: %w", err)
	}

	return nil
}

// initLogger 初始化日志器
func initLogger(verbose bool) *zap.Logger {
	var logger *zap.Logger
	var err error

	if verbose {
		config := zap.NewDevelopmentConfig()
		config.Level = zap.NewAtomicLevelAt(zap.DebugLevel)
		logger, err = config.Build()
	} else {
		config := zap.NewProductionConfig()
		config.Level = zap.NewAtomicLevelAt(zap.InfoLevel)
		logger, err = config.Build()
	}

	if err != nil {
		log.Fatalf("初始化日志器失败: %v", err)
	}

	return logger
}

// outputReport 输出报告
func outputReport(report *service.BillingCheckReport, logger *zap.Logger) error {
	switch *reportFormat {
	case "json":
		return outputJSONReport(report, logger)
	case "csv":
		return outputCSVReport(report, logger)
	default:
		return outputTextReport(report, logger)
	}
}

// outputTextReport 输出文本格式报告
func outputTextReport(report *service.BillingCheckReport, logger *zap.Logger) error {
	output := fmt.Sprintf(`
订单费用差异检查报告
===========================================
检查时间: %s
检查范围: 最近 %d 天
运行模式: %s

统计信息:
-----------------------------------------
检查订单总数: %d
发现问题订单: %d
修复成功订单: %d
修复失败订单: %d
跳过订单数量: %d

费用统计:
-----------------------------------------
总差异金额: %.2f 元
已修复金额: %.2f 元
待修复金额: %.2f 元

执行时间:
-----------------------------------------
开始时间: %s
结束时间: %s
总耗时: %s

`,
		report.CheckTime.Format("2006-01-02 15:04:05"),
		report.Config.Days,
		func() string {
			if report.Config.DryRun {
				return "干运行（仅检查）"
			}
			return "正常运行（检查并修复）"
		}(),
		report.Statistics.TotalOrders,
		report.Statistics.ProblemsFound,
		report.Statistics.FixedOrders,
		report.Statistics.FailedFixes,
		report.Statistics.SkippedOrders,
		report.Statistics.TotalDifferenceAmount,
		report.Statistics.FixedAmount,
		report.Statistics.PendingAmount,
		report.StartTime.Format("2006-01-02 15:04:05"),
		report.EndTime.Format("2006-01-02 15:04:05"),
		report.EndTime.Sub(report.StartTime).String(),
	)

	if *outputFile != "" {
		return writeToFile(*outputFile, output)
	}

	fmt.Print(output)
	return nil
}

// outputJSONReport 输出JSON格式报告
func outputJSONReport(report *service.BillingCheckReport, logger *zap.Logger) error {
	// TODO: 实现JSON格式输出
	return fmt.Errorf("JSON格式报告暂未实现")
}

// outputCSVReport 输出CSV格式报告
func outputCSVReport(report *service.BillingCheckReport, logger *zap.Logger) error {
	// TODO: 实现CSV格式输出
	return fmt.Errorf("CSV格式报告暂未实现")
}

// writeToFile 写入文件
func writeToFile(filename, content string) error {
	file, err := os.Create(filename)
	if err != nil {
		return err
	}
	defer file.Close()

	_, err = file.WriteString(content)
	return err
}

// printUsage 打印使用说明
func printUsage() {
	fmt.Println("订单费用差异检查和修复工具")
	fmt.Println("=====================================")
	fmt.Println()
	fmt.Println("用法:")
	fmt.Println("  go run cmd/billing-checker/main.go [选项]")
	fmt.Println()
	fmt.Println("基本选项:")
	fmt.Println("  -config string     配置文件路径 (默认: config/config.yaml)")
	fmt.Println("  -dry-run          干运行模式，只检查不修复")
	fmt.Println("  -days int         检查最近N天的订单 (默认: 7)")
	fmt.Println("  -verbose          详细输出")
	fmt.Println("  -help             显示此帮助信息")
	fmt.Println()
	fmt.Println("高级选项:")
	fmt.Println("  -batch-size int   批处理大小 (默认: 100)")
	fmt.Println("  -workers int      并发工作协程数 (默认: 5)")
	fmt.Println("  -timeout duration 总超时时间 (默认: 30m)")
	fmt.Println("  -report-format string 报告格式: text, json, csv (默认: text)")
	fmt.Println("  -output string    输出文件路径（为空则输出到控制台）")
	fmt.Println()
	fmt.Println("示例:")
	fmt.Println("  # 检查最近3天的订单（干运行）")
	fmt.Println("  go run cmd/billing-checker/main.go -dry-run -days=3")
	fmt.Println()
	fmt.Println("  # 检查并修复最近7天的订单")
	fmt.Println("  go run cmd/billing-checker/main.go -days=7")
	fmt.Println()
	fmt.Println("  # 详细模式检查并输出到文件")
	fmt.Println("  go run cmd/billing-checker/main.go -verbose -output=report.txt")
	fmt.Println()
	fmt.Println("  # 高并发模式检查")
	fmt.Println("  go run cmd/billing-checker/main.go -workers=10 -batch-size=200")
}
