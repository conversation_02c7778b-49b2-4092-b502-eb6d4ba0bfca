version: '3.8'

services:
  # 订单费用差异检查工具
  billing-checker:
    build:
      context: ../../
      dockerfile: cmd/billing-checker/Dockerfile
    container_name: billing-checker
    environment:
      - TZ=Asia/Shanghai
      - LANG=zh_CN.UTF-8
    volumes:
      # 挂载配置文件
      - ./config:/app/config:ro
      # 挂载日志目录
      - ./logs:/app/logs
      # 挂载报告输出目录
      - ./reports:/app/reports
    networks:
      - kuaidi-network
    depends_on:
      - postgres
    # 默认运行干运行模式
    command: ["-dry-run", "-days=7", "-verbose", "-output=/app/reports/billing-check-report.txt"]
    restart: "no"  # 一次性任务，不自动重启

  # PostgreSQL 数据库（用于测试）
  postgres:
    image: postgres:15-alpine
    container_name: billing-checker-postgres
    environment:
      POSTGRES_DB: go_kuaidi
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: gjx6ngf4
      TZ: Asia/Shanghai
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - kuaidi-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d go_kuaidi"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis（如果需要缓存）
  redis:
    image: redis:7-alpine
    container_name: billing-checker-redis
    environment:
      - TZ=Asia/Shanghai
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - kuaidi-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Prometheus（监控指标收集）
  prometheus:
    image: prom/prometheus:latest
    container_name: billing-checker-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    networks:
      - kuaidi-network
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'

  # Grafana（监控仪表板）
  grafana:
    image: grafana/grafana:latest
    container_name: billing-checker-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - TZ=Asia/Shanghai
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - kuaidi-network
    depends_on:
      - prometheus

networks:
  kuaidi-network:
    driver: bridge

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
