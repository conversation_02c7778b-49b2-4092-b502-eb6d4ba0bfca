# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Commands

### Build and Deployment
- **Build production package**: `./build.sh` - Creates optimized production deployment package with auto-configured environment settings
- **Build for local testing**: `./build.sh local` - Creates local binary for development/testing
- **Start service**: `./start.sh` (in deployment package)
- **Stop service**: `./stop.sh` (in deployment package)
- **View logs**: `./logs.sh app|error|access|audit` (in deployment package)

### Development
- **Run locally**: `go run cmd/main.go` - Starts server on port 8081
- **Test**: `go test ./...` - Run all tests
- **Build binary**: `go build -o kuaidi-server cmd/main.go`
- **Download dependencies**: `go mod download && go mod verify`

### Frontend Development
- **Admin frontend**: `cd admin-frontend && npm run dev`
- **User frontend**: `cd user-frontend && npm run dev`
- **Callback receiver**: `cd callback-receiver-service && go run cmd/main.go`

## Architecture

### High-Level Structure
This is a microservices-based express delivery management system with clean architecture:

- **Main Go Backend** (`cmd/main.go`): Core logistics API server (port 8081)
- **Callback Receiver Service** (`callback-receiver-service/`): Separate microservice for provider callbacks
- **Admin Frontend** (`admin-frontend/`): Vue.js management dashboard
- **User Frontend** (`user-frontend/`): Vue.js customer interface

### Core Layers
1. **Handlers** (`api/handler/`): HTTP request/response handling, input validation
2. **Services** (`internal/service/`): Business logic, transaction management, orchestration
3. **Repositories** (`internal/repository/`): Data access, database operations
4. **Adapters** (`internal/adapter/`): External provider integration (Kuaidi100, Cainiao, Yida, etc.)
5. **Models** (`internal/model/`): Data structures and domain entities

### Key Patterns
- **Clean Architecture**: Strict dependency inversion with interfaces
- **Adapter Pattern**: Unified interface for multiple express delivery providers
- **Repository Pattern**: Database abstraction with transaction support
- **Factory Pattern**: Dynamic provider selection and configuration
- **Observer Pattern**: Event-driven callback processing

## Provider Integration System

### Supported Providers
- **Kuaidi100** (`internal/adapter/kuaidi100.go`): Industry standard API
- **Cainiao** (`internal/adapter/cainiao.go`): Alibaba logistics platform
- **Yida** (`internal/adapter/yida.go`): Regional express provider
- **Yuntong** (`internal/adapter/yuntong.go`): Cloud-based logistics
- **Kuaidiniao** (`internal/adapter/kuaidiniao.go`): Multi-carrier platform

### Integration Architecture
- **Unified Gateway**: Single API endpoint handling all providers
- **Dynamic Provider Manager**: Runtime provider switching and failover
- **Standardized Callbacks**: Common format for all provider status updates
- **Price Comparison**: Real-time multi-provider pricing

## Configuration

### Main Config (`config/config.yaml`)
- Database: PostgreSQL with connection pooling (200 connections)
- Redis: Caching and session management
- Providers: Database-driven configuration
- Security: JWT with RSA keys, signature validation
- Logging: Multi-level with file rotation

### Environment Auto-Configuration
Build script automatically replaces connection strings:
- Dev: `*************:5432` → Prod: `1Panel-postgresql-HioR:5432`
- Dev: `*************:6379` → Prod: `1Panel-redis-dryE:6379`

## Key Business Features

### Order Management
- **Multi-step Lifecycle**: Creation → Provider Selection → Submission → Tracking → Completion
- **Smart Provider Selection**: Algorithm-based optimal provider choice
- **Real-time Status Updates**: Callback-driven status synchronization
- **Platform Order Numbers**: Unified order numbering across providers

### Callback System
The callback system processes delivery status updates from providers:
- **Callback Router** (`api/router/callback_router.go`): Routes provider callbacks
- **Standardizer** (`internal/service/callback/standardizer.go`): Converts provider formats
- **Status Mapping** (`config/status_mapping.yaml`): Maps provider statuses to internal codes
- **Unified Service** (`internal/service/callback/unified_callback_service.go`): Central processing

### Price Management
- **Enhanced Price Service** (`internal/service/enhanced_price_service.go`): Multi-provider comparison
- **Price Validation** (`internal/service/order_price_validation_service.go`): Real-time validation
- **Dynamic Pricing**: Provider-specific rate calculations

## Development Guidelines

### Code Organization
- Follow clean architecture principles with clear layer separation
- Use dependency injection through constructors and interfaces
- Implement comprehensive error handling with custom error types
- Maintain transaction boundaries at service layer

### Database Patterns
- Use GORM with proper transaction management
- Implement soft deletes for audit trails
- Follow naming conventions: snake_case for database, camelCase for Go
- Use repository pattern for all database operations

### API Design
- RESTful endpoints with proper HTTP methods and status codes
- Consistent JSON response format with error handling
- JWT authentication with role-based access control
- Request validation at handler layer

### Testing Approach
- Unit tests for individual components with mocks
- Integration tests for service interactions
- Use testify for assertions and test helpers
- Mock external dependencies and database calls

## Security Features

### Authentication & Authorization
- JWT tokens with RSA signature verification
- Request signature validation with nonce management
- Role-based access control (admin/user)
- Comprehensive audit logging

### Data Protection
- Input validation at all entry points
- SQL injection prevention through parameterized queries
- Rate limiting and request throttling
- Sensitive data encryption in transit and at rest

## Performance Optimizations

### Database
- Connection pooling with 200 max connections
- Query optimization with proper indexes
- Prepared statements for repeated queries
- Database operation monitoring

### Caching Strategy
- Redis for session management and API responses
- Application-level caching for static data
- Cache invalidation strategies for data consistency

### Monitoring
- Structured logging with Zap (JSON format)
- Prometheus metrics integration
- Performance monitoring for critical paths
- Error tracking and alerting

## Troubleshooting

### Common Issues
- **Port conflicts**: Ensure 8081 is available
- **Database connectivity**: Check PostgreSQL connection and credentials
- **Redis issues**: Verify Redis server status and connection
- **Provider API failures**: Check provider adapter configurations

### Debugging Tools
- **Debug script**: `./debug.sh` (in deployment package) - Comprehensive startup diagnostics
- **Log viewer**: `./logs.sh help` - Multi-category log management
- **Status checker**: `./status.sh` - Process and system status
- **Optimizer**: `./optimize.sh` - Performance analysis and recommendations

### Log Categories
- **Application logs**: `logs/go-kuaidi.log` - Main business logic
- **Error logs**: `logs/error.log` - Error tracking and debugging
- **Access logs**: `logs/access.log` - HTTP request/response logging  
- **Audit logs**: `logs/audit.log` - Security and compliance tracking