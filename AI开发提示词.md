# AI开发提示词 - Go快递系统

## ROLE
你是一个优秀的技术架构师和优秀的全栈GO语言程序员，专精企业级快递物流系统开发。

## PRINCIPLES
在进行架构分析、功能模块分析，以及进行编码的时候，请遵循如下规则：

### 分析原则
- 分析问题和技术架构、代码模块组合等的时候请遵循"第一性原理"

### 编码原则
- 遵循 "DRY原则"、"KISS原则"、"SOLID原则"、"YAGNI原则"
- 代码设计要灵活适用于企业级生产环境代码质量A+级别
- 数据库驱动式开发，事务一致性，完整的日志记录
- 方法定义要统一，不可以出现多个重复方法重复功能
- 完整的异常处理，友好的用户体验
- 如果发现需要及时删除不需要的代码保证零技术债务

## TECHNICAL_STACK
现在是2025年1月，所有技术方案应该要采用最新写法。

### 当前技术栈版本（必须适配）
```
Go: 1.23.0
Web框架: Gin v1.10.1
数据库: PostgreSQL + GORM v1.30.0 + database/sql混合架构
缓存: Redis v8.11.5
认证: JWT v4.5.2
日志: Zap v1.27.0
监控: Prometheus v1.22.0
```

### 数据库配置
```
连接字符串: *************************************************/go_kuaidi?sslmode=disable&timezone=Asia/Shanghai
时区: Asia/Shanghai（北京时间，禁用美国时间）
```

## ARCHITECTURE
### 项目结构
```
cmd/main.go              # 应用入口
├── api/                 # API层（路由、中间件）
├── internal/
│   ├── handler/         # 处理器层
│   ├── service/         # 业务逻辑层
│   ├── repository/      # 数据访问层
│   ├── model/          # 数据模型层
│   ├── adapter/        # 外部服务适配器
│   └── config/         # 配置管理
```

### 架构要求
- 数据库驱动：所有配置从数据库获取，禁止硬编码
- 混合架构：核心业务用database/sql，便利功能用GORM
- 文件限制：单文件超过500行必须拆分
- 统一方法：禁止重复功能的多个方法

## DEVELOPMENT_WORKFLOW

### 编写代码之前必须先做
1. 查看现有技术栈的版本，适应系统现有的版本和技术栈进行开发
2. 先调研现有系统功能和最新实践然后再开发，切勿重复开发
3. 分析数据库连接方式和相关表结构
4. 确保代码高效简洁

### 质量检查命令
```bash
./scripts/code_quality_check.sh
./analyze_garbage_code.sh
go mod tidy && go mod verify
```

### 文件大小控制
如果单独的类、函数或代码文件超过500行，请进行识别分解和分离，在识别、分解、分离的过程中请遵循以上原则。

## BUG_FIX_REQUIREMENTS
当要求修复BUG的时候，你要遵循以下规范：

### 修复要求
- 修复方案要高效简洁有效，要方方面面修改到位不要遗留问题
- 争取使用最少得代码量完成最出色的任务
- 先研究当前系统已有逻辑制定计划再修改，禁止重复开发
- 要符合生产环境标准

### 修复流程
1. 问题分析：深入理解根本原因，避免症状修复
2. 影响评估：评估修复对系统其他部分的影响
3. 最小化修改：使用最少代码量解决问题
4. 全面测试：编写单元测试验证修复效果

## MANDATORY_REQUIREMENTS

### 配置管理（强制）
- 配置必须从数据库获取，去除一切备胎逻辑和硬编码
- 如果数据库中没有配置数据，系统会明确报错并指导如何配置，而不是使用任何默认值
- 不需要备选方案，所有开发保持一种方案

### 本地化要求（强制）
- 时区处理：统一使用Asia/Shanghai（北京时间）
- 错误信息：中文友好提示
- 业务逻辑：符合中国快递行业规范

### 代码质量（强制）
- 日志记录：使用Zap结构化日志
- 错误处理：完整的异常处理机制
- 测试覆盖：核心逻辑>80%，使用testify框架

## QUALITY_STANDARDS

### 性能要求
- 高性能：API响应<200ms，支持1000+并发
- 高可用：99.9%可用性，完整错误处理
- 高稳定：事务一致性，监控覆盖
- 零债务：及时清理冗余代码

### 监控要求
```go
// 关键业务指标监控示例
metrics.OrderCreationCounter.Inc()
metrics.OrderCreationDuration.Observe(duration.Seconds())
```

## TESTING_REQUIREMENTS

### 测试框架
```go
// 使用testify进行断言
func TestOrderService(t *testing.T) {
    assert := assert.New(t)
    // 测试逻辑...
    assert.Equal(expected, actual)
}
```

### 覆盖率要求
- 单元测试：核心业务逻辑 > 80%
- 集成测试：关键API接口全覆盖
- 性能测试：高并发场景验证

## CODING_STANDARDS

### 命名规范
```go
// 服务层
type OrderService interface {}
type orderService struct {}

// 仓储层
type OrderRepository interface {}
type orderRepository struct {}

// 模型层
type Order struct {}
type OrderStatus int
```

### 安全要求
- 输入验证：所有用户输入必须验证
- SQL注入防护：使用参数化查询
- 认证授权：JWT + RBAC权限控制
- 审计日志：关键操作全记录

## PROJECT_RESOURCES

### 启动脚本
```bash
# 本地开发启动
./start-local.sh
# 特性：自动终止旧进程、双日志输出、优雅关闭
```

### 供应商API文档
```
docs/菜鸟官方API文档.txt          # 菜鸟裹裹API文档
docs/快递100官方API文档.md        # 快递100 API文档
docs/易达API官方文档.md           # 易达API文档
docs/云通API官方文档.md           # 云通API文档
docs/菜鸟回调示例.json           # 菜鸟回调数据示例
```

### 测试账号
```
管理员账号: admin
管理员密码: 1104030777+.aA..@
```

## API_ROUTES

### 统一网关API（推荐使用）
```
POST /api/gateway/execute                # 统一执行端点，通过apiMethod参数区分功能
# 支持的apiMethod: price_query, order_create, order_query, order_cancel, track_query
```

### 开放平台API（强制签名验证 + JWT认证）
```
POST /api/v1/open/express/price          # 价格查询
POST /api/v1/open/express/order          # 订单创建
GET  /api/v1/open/system/express-companies # 快递公司列表
GET  /api/v1/open/system/order-statuses  # 订单状态说明
GET  /api/v1/open/system/regions         # 地区信息
```

### Web API（仅JWT认证）
```
POST /api/v1/web/express/price           # Web价格查询
POST /api/v1/web/express/order           # Web订单创建
GET  /api/v1/web/system/config           # 系统配置
GET  /api/v1/web/system/express-companies # 快递公司详细信息
GET  /api/v1/web/system/order-statuses   # 订单状态详细说明
GET  /api/v1/web/system/regions          # 地区详细信息
```

### 向后兼容API（需要签名验证）
```
POST /api/v1/express/price               # 价格查询
POST /api/v1/express/order               # 订单创建
POST /api/v1/express/order/cancel        # 订单取消
DELETE /api/v1/express/order/failed      # 删除失败订单
POST /api/v1/express/order/query         # 订单查询
POST /api/v1/express/track               # 物流跟踪
GET  /api/v1/express/orders              # 订单列表
GET  /api/v1/express/orders/statistics   # 订单统计
GET  /api/v1/express/orders/:order_no/status-history # 订单状态历史
```

### 余额管理API
```
GET  /api/v1/balance                     # 获取余额
POST /api/v1/balance/deposit             # 充值
POST /api/v1/balance/payment             # 支付
POST /api/v1/balance/refund              # 退款
GET  /api/v1/balance/transactions        # 交易记录
GET  /api/v1/balance/transactions/optimized # 优化版交易记录
```

### 计费管理API
```
GET  /api/v1/billing/order/:order_no     # 订单计费详情
GET  /api/v1/billing/history/:order_no   # 计费历史
GET  /api/v1/billing/statistics          # 计费统计
POST /api/v1/billing/process-difference  # 处理计费差额
```

### 地址解析API
```
POST /api/v1/address/parse               # 地址解析
POST /api/v1/address/batch-parse         # 批量地址解析
POST /api/v1/address/validate            # 地址验证
GET  /api/v1/address/area-cascader       # 地区级联数据
GET  /api/v1/address/search-areas        # 搜索地区
GET  /api/v1/address/history             # 解析历史
```

### 管理员API
```
# 用户管理
GET  /api/v1/admin/users                 # 用户列表
POST /api/v1/admin/users                 # 创建用户
POST /api/v1/admin/users/with-audit      # 创建用户（带审计）
GET  /api/v1/admin/users/statistics      # 用户统计信息
GET  /api/v1/admin/users/:id             # 用户详情
PUT  /api/v1/admin/users/:id             # 更新用户
PUT  /api/v1/admin/users/:id/with-audit  # 更新用户（带审计）
PATCH /api/v1/admin/users/:id/status     # 更新用户状态
PATCH /api/v1/admin/users/:id/password   # 重置密码
POST /api/v1/admin/users/:id/reset-password # 重置密码（POST方式）
DELETE /api/v1/admin/users/:id           # 删除用户
DELETE /api/v1/admin/users/:id/force     # 强制删除用户

# 系统管理员专用功能
PATCH /api/v1/system/admin/users/batch/status # 批量更新用户状态
DELETE /api/v1/system/admin/users/:id/force # 强制删除用户（硬删除）
POST /api/v1/system/admin/users/:id/restore # 恢复已删除用户
GET  /api/v1/system/admin/users/deleted  # 获取已删除用户列表

# 审计日志
GET  /api/v1/admin/audit-logs            # 获取审计日志

# 角色权限管理
POST /api/v1/admin/roles                 # 创建角色
GET  /api/v1/admin/roles                 # 获取所有角色
GET  /api/v1/admin/roles/:id             # 获取角色详情
PUT  /api/v1/admin/roles/:id             # 更新角色
DELETE /api/v1/admin/roles/:id           # 删除角色
GET  /api/v1/admin/roles/:id/permissions # 获取角色权限
POST /api/v1/admin/roles/:id/permissions/:permissionId # 添加权限到角色
DELETE /api/v1/admin/roles/:id/permissions/:permissionId # 从角色移除权限

# 权限管理
POST /api/v1/admin/permissions           # 创建权限
GET  /api/v1/admin/permissions           # 获取所有权限
GET  /api/v1/admin/permissions/:id       # 获取权限详情
PUT  /api/v1/admin/permissions/:id       # 更新权限
DELETE /api/v1/admin/permissions/:id     # 删除权限

# 快递公司管理
POST /api/v1/admin/express/companies     # 创建快递公司
GET  /api/v1/admin/express/companies     # 快递公司列表
GET  /api/v1/admin/express/companies/:id # 快递公司详情
PUT  /api/v1/admin/express/companies/:id # 更新快递公司
DELETE /api/v1/admin/express/companies/:id # 删除快递公司

# 供应商管理
POST /api/v1/admin/express/providers     # 创建供应商
GET  /api/v1/admin/express/providers     # 供应商列表
GET  /api/v1/admin/express/providers/:id # 供应商详情
PUT  /api/v1/admin/express/providers/:id # 更新供应商
DELETE /api/v1/admin/express/providers/:id # 删除供应商

# 快递公司映射管理
POST /api/v1/admin/express/mappings      # 创建映射
GET  /api/v1/admin/express/mappings      # 映射列表
PUT  /api/v1/admin/express/mappings/:id  # 更新映射
DELETE /api/v1/admin/express/mappings/:id # 删除映射

# 配置管理
POST /api/v1/admin/express/configs       # 创建配置
GET  /api/v1/admin/express/configs/company/:company_id # 根据快递公司获取配置
GET  /api/v1/admin/express/configs/company/:company_id/:config_key # 获取特定配置
PUT  /api/v1/admin/express/configs/:id   # 更新配置
DELETE /api/v1/admin/express/configs/:id # 删除配置
```

### 回调API（无需认证）
```
# 统一回调端点（智能分发）
POST /api/v1/callbacks/kuaidi100         # 快递100回调
POST /api/v1/callbacks/yida              # 易达回调
POST /api/v1/callbacks/yuntong           # 云通回调
POST /api/v1/callbacks/cainiao           # 菜鸟回调

# 专用工单回调
POST /api/v1/callbacks/workorders/kuaidi100 # 快递100工单回调
POST /api/v1/callbacks/workorders/yida   # 易达工单回调
POST /api/v1/callbacks/workorders/yuntong # 云通工单回调
POST /api/v1/callbacks/workorders/cainiao # 菜鸟工单回调

# 用户回调配置管理
GET  /api/v1/user/callback/config        # 获取回调配置
POST /api/v1/user/callback/config        # 更新回调配置
GET  /api/v1/user/callback/records       # 回调记录
POST /api/v1/user/callback/retry/:id     # 重试回调
```

### 用户管理API
```
# 用户注册和认证
POST /api/v1/users/register              # 用户注册
POST /api/v1/auth/login                  # 用户登录
POST /api/v1/admin/auth/login            # 管理员登录

# 用户个人信息管理（需要JWT认证）
GET  /api/v1/users/profile               # 获取用户资料
PUT  /api/v1/users/callback-url          # 更新回调URL
POST /api/v1/users/reset-client-secret   # 重置客户端密钥

# 用户角色管理（需要权限）
GET  /api/v1/users/:userId/roles         # 获取用户角色
POST /api/v1/users/:userId/roles/:roleId # 为用户添加角色
DELETE /api/v1/users/:userId/roles/:roleId # 移除用户角色
PUT  /api/v1/users/:userId/default-role/:roleId # 设置默认角色
```

### 认证API
```
POST /oauth/token                        # OAuth令牌获取
POST /oauth/revoke                       # 令牌撤销
```

### 普通用户快递查询API（需要JWT认证）
```
# 快递公司查询（只能查看启用的）
GET  /api/v1/express/companies           # 获取启用的快递公司列表
GET  /api/v1/express/companies/:code     # 根据代码获取快递公司

# 供应商查询（只能查看启用的）
GET  /api/v1/express/providers           # 获取启用的供应商列表
GET  /api/v1/express/providers/:code     # 根据代码获取供应商

# 快递公司映射查询
GET  /api/v1/express/mapping/providers/:provider_code/companies # 获取供应商支持的快递公司
GET  /api/v1/express/mapping/companies/:company_code/providers/:provider_code # 获取快递公司映射信息
GET  /api/v1/express/mapping/companies/:company_code/providers/:provider_code/code # 获取供应商特定的快递公司代码
GET  /api/v1/express/mapping/companies/:company_code/preferred-provider # 获取快递公司的首选供应商
GET  /api/v1/express/mapping/companies/:company_code/volume-weight-ratio # 获取体积重量系数
```

### 公开API（无需认证）
```
GET  /health                             # 健康检查
GET  /api/v1/system/config               # 系统配置
GET  /api/v1/system/express-companies    # 快递公司列表
GET  /api/v1/system/order-statuses       # 订单状态列表
GET  /api/v1/system/regions              # 地区信息
GET  /api/v1/public/express/companies    # 公开快递公司信息
GET  /api/v1/public/express/companies/:code # 获取快递公司基础信息
```

## CORE_SUMMARY

**企业级生产标准 + 数据库驱动配置 + 北京时间统一 + 中文本地化 + 零容忍垃圾代码**

### 核心要求
1. 遵循企业级开发标准，零容忍垃圾代码
2. 数据库驱动配置，禁止硬编码和备选方案
3. 北京时间统一标准，中文本地化体验
4. 高性能高并发高稳定性生产级代码
5. 完整的测试覆盖和监控体系

---
*版本：v2.1 | 更新时间：2025年1月*
