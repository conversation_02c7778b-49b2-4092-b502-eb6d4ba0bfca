#!/bin/bash
# 性能监控脚本
echo "======== 快递系统性能监控 ========"
echo "时间: $(date)"
echo ""

# 检查服务状态
if pgrep -f "go-kuaidi" > /dev/null; then
    PID=$(pgrep -f "go-kuaidi")
    echo "✅ 服务运行中 (PID: $PID)"
    
    # 显示CPU和内存使用
    ps -p $PID -o pid,ppid,pgid,pcpu,pmem,vsz,rss,comm
    echo ""
    
    # 显示连接数
    echo "📊 网络连接统计:"
    netstat -an | grep :8081 | wc -l | xargs echo "端口8081连接数:"
    
    # 显示日志最新输出
    echo ""
    echo "📋 最新日志 (最后5行):"
    tail -5 logs/go-kuaidi.log 2>/dev/null || echo "日志文件不存在"
    
else
    echo "❌ 服务未运行"
fi
