# 快递鸟回调集成文档

## 概述

本文档描述了快递鸟回调系统的集成架构和实现方式。快递鸟回调通过独立的回调接收服务处理，然后通过Redis队列异步传递给主服务进行业务处理。

## 架构图

```
快递鸟 → 回调接收服务 → Redis队列 → 主服务消费者 → 业务处理
  ↓           ↓            ↓           ↓            ↓
回调推送   数据存储      消息队列    回调消费     状态更新
```

## 系统组件

### 1. 回调接收服务 (callback-receiver-service)

**职责:**
- 接收快递鸟的HTTP回调请求
- 验证和解析回调数据
- 存储原始回调数据到数据库
- 发送通知消息到Redis队列
- 返回符合快递鸟期望的响应格式

**端点:** `POST /webhook/kuaidiniao`

**响应格式:**
```json
{
  "Result": true,
  "ResultCode": "100", 
  "Reason": "成功"
}
```

### 2. Redis消息队列

**队列名:** `callback_notifications`

**消息格式:**
```json
{
  "id": "uuid",
  "provider": "kuaidiniao"
}
```

### 3. 主服务消费者

**职责:**
- 从Redis队列消费回调通知
- 从回调接收服务数据库获取原始回调数据
- 调用主服务的回调处理逻辑
- 标记回调为已处理

## 快递鸟回调数据格式

### 状态推送回调 (PushType: 1)

```json
{
  "PushType": 1,
  "EBusinessID": "商户ID",
  "OrderCode": "商户订单号",
  "LogisticCode": "快递运单号",
  "ShipperCode": "快递公司编码",
  "State": "物流状态",
  "StateEx": "详细状态",
  "Location": "所在城市",
  "PushTime": "推送时间",
  "Traces": [
    {
      "AcceptTime": "轨迹时间",
      "AcceptStation": "轨迹描述",
      "Location": "轨迹城市"
    }
  ]
}
```

### 计费推送回调 (PushType: 2)

```json
{
  "PushType": 2,
  "EBusinessID": "商户ID", 
  "OrderCode": "商户订单号",
  "LogisticCode": "快递运单号",
  "Weight": 1.5,
  "Cost": 12.50,
  "PushTime": "推送时间"
}
```

## 配置说明

### 回调接收服务配置 (config.json)

```json
{
  "providers": {
    "kuaidiniao": {
      "enabled": true,
      "endpoint": "/webhook/kuaidiniao",
      "api_endpoint": "/api/v1/callbacks/kuaidiniao",
      "status_field": "State",
      "content_field": "Content",
      "committer_field": "Operator",
      "work_order_id_field": "OrderCode",
      "response_format": {
        "Result": true,
        "ResultCode": "100",
        "Reason": "成功"
      }
    }
  }
}
```

### 主服务回调URL配置 (config.yaml)

```yaml
callback:
  base_url: "http://47.123.6.144:8082"
  endpoints:
    kuaidiniao: "/webhook/kuaidiniao"
```

## 部署和运行

### 1. 启动回调接收服务

```bash
cd callback-receiver-service
go build -o bin/callback-receiver cmd/main.go
./bin/callback-receiver
```

服务将在端口8082上启动。

### 2. 启动主服务

```bash
./start-local.sh
```

主服务将在端口8081上启动，并自动启动回调消费者。

### 3. 配置快递鸟回调URL

在快递鸟商户后台配置回调URL：
```
http://your-domain:8082/webhook/kuaidiniao
```

## 测试

### 1. 单独测试回调接收

```bash
chmod +x callback-receiver-service/test_kuaidiniao_callback.sh
./callback-receiver-service/test_kuaidiniao_callback.sh
```

### 2. 集成测试

```bash
chmod +x test_kuaidiniao_integration.sh
./test_kuaidiniao_integration.sh
```

## 监控和调试

### 查看回调数据

```sql
-- 查看最近的快递鸟回调
SELECT * FROM callback_raw_data 
WHERE provider = 'kuaidiniao' 
ORDER BY created_at DESC 
LIMIT 10;
```

### 查看Redis队列状态

```bash
redis-cli -h 8.138.252.193 -a a63006320 LLEN callback_notifications
```

### 查看处理日志

```bash
# 回调接收服务日志
tail -f callback-receiver-service/logs/*.log

# 主服务日志  
tail -f logs/go-kuaidi-local-*.log
```

## 故障排除

### 常见问题

1. **回调接收失败**
   - 检查回调接收服务是否运行
   - 检查端口8082是否可访问
   - 查看回调接收服务日志

2. **消息队列堆积**
   - 检查主服务消费者是否运行
   - 检查Redis连接
   - 查看主服务日志

3. **回调处理失败**
   - 检查回调数据格式是否正确
   - 查看主服务回调处理逻辑日志
   - 检查数据库连接

### 日志级别

开发环境建议设置为DEBUG级别以获取详细日志：

```json
{
  "logging": {
    "level": "debug"
  }
}
```

## 安全考虑

1. **IP白名单**: 建议配置快递鸟回调IP白名单
2. **签名验证**: 可启用回调签名验证（需要快递鸟支持）
3. **HTTPS**: 生产环境建议使用HTTPS
4. **限流**: 配置适当的回调接收限流

## 性能优化

1. **数据库连接池**: 合理配置数据库连接池大小
2. **Redis连接池**: 优化Redis连接池配置
3. **消费者并发**: 根据业务量调整消费者工作协程数量
4. **批量处理**: 考虑实现批量回调处理机制
