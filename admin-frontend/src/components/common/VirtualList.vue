<template>
  <div 
    ref="containerRef" 
    class="virtual-list" 
    :style="{ height: containerHeight }"
    @scroll="handleScroll"
  >
    <!-- 虚拟滚动容器 -->
    <div 
      class="virtual-list-phantom" 
      :style="{ height: totalHeight + 'px' }"
    ></div>
    
    <!-- 可见项容器 -->
    <div 
      class="virtual-list-content" 
      :style="{ transform: `translateY(${offsetY}px)` }"
    >
      <div
        v-for="(item, index) in visibleItems"
        :key="getItemKey(item, startIndex + index)"
        class="virtual-list-item"
        :style="{ height: itemHeight + 'px' }"
      >
        <slot 
          :item="item" 
          :index="startIndex + index"
          :isVisible="true"
        >
          <!-- 默认渲染 -->
          <div class="default-item">
            {{ item }}
          </div>
        </slot>
      </div>
    </div>
    
    <!-- 加载更多指示器 -->
    <div 
      v-if="loading" 
      class="virtual-list-loading"
    >
      <el-icon class="is-loading">
        <Loading />
      </el-icon>
      <span>加载中...</span>
    </div>
    
    <!-- 空状态 -->
    <div 
      v-if="!loading && items.length === 0" 
      class="virtual-list-empty"
    >
      <slot name="empty">
        <div class="empty-content">
          <el-icon size="48" color="#c0c4cc">
            <DocumentDelete />
          </el-icon>
          <p>暂无数据</p>
        </div>
      </slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { Loading, DocumentDelete } from '@element-plus/icons-vue'
import { debounce } from '@/utils/debounce'

// Props定义
interface Props {
  items: any[] // 数据列表
  itemHeight: number // 每项高度
  containerHeight?: string // 容器高度
  bufferSize?: number // 缓冲区大小
  keyField?: string // 用作key的字段名
  loading?: boolean // 是否正在加载
  hasMore?: boolean // 是否还有更多数据
  threshold?: number // 触发加载更多的阈值
}

const props = withDefaults(defineProps<Props>(), {
  containerHeight: '400px',
  bufferSize: 5,
  keyField: 'id',
  loading: false,
  hasMore: false,
  threshold: 100
})

// Emits定义
const emit = defineEmits<{
  loadMore: []
  scroll: [scrollTop: number]
}>()

// 响应式数据
const containerRef = ref<HTMLElement>()
const scrollTop = ref(0)
const containerClientHeight = ref(0)

// 计算属性
const totalHeight = computed(() => props.items.length * props.itemHeight)

const visibleCount = computed(() => {
  if (containerClientHeight.value === 0) return 10
  return Math.ceil(containerClientHeight.value / props.itemHeight) + props.bufferSize * 2
})

const startIndex = computed(() => {
  const index = Math.floor(scrollTop.value / props.itemHeight) - props.bufferSize
  return Math.max(0, index)
})

const endIndex = computed(() => {
  const index = startIndex.value + visibleCount.value
  return Math.min(props.items.length, index)
})

const visibleItems = computed(() => {
  return props.items.slice(startIndex.value, endIndex.value)
})

const offsetY = computed(() => {
  return startIndex.value * props.itemHeight
})

// 防抖的滚动处理
const debouncedScroll = debounce((scrollTop: number) => {
  emit('scroll', scrollTop)
}, 100)

// 滚动处理
const handleScroll = (event: Event) => {
  const target = event.target as HTMLElement
  scrollTop.value = target.scrollTop
  
  // 发送滚动事件
  debouncedScroll(scrollTop.value)
  
  // 检查是否需要加载更多
  if (props.hasMore && !props.loading) {
    const scrollBottom = target.scrollHeight - target.scrollTop - target.clientHeight
    if (scrollBottom <= props.threshold) {
      console.log('🔄 触发加载更多')
      emit('loadMore')
    }
  }
}

// 获取项目的key
const getItemKey = (item: any, index: number): string | number => {
  if (props.keyField && item[props.keyField] !== undefined) {
    return item[props.keyField]
  }
  return index
}

// 滚动到指定位置
const scrollTo = (index: number) => {
  if (!containerRef.value) return
  
  const targetScrollTop = index * props.itemHeight
  containerRef.value.scrollTop = targetScrollTop
  scrollTop.value = targetScrollTop
}

// 滚动到顶部
const scrollToTop = () => {
  scrollTo(0)
}

// 滚动到底部
const scrollToBottom = () => {
  if (!containerRef.value) return
  
  containerRef.value.scrollTop = containerRef.value.scrollHeight
}

// 更新容器高度
const updateContainerHeight = () => {
  if (containerRef.value) {
    containerClientHeight.value = containerRef.value.clientHeight
  }
}

// 监听容器大小变化
let resizeObserver: ResizeObserver | null = null

onMounted(async () => {
  await nextTick()
  updateContainerHeight()
  
  // 监听容器大小变化
  if (containerRef.value && window.ResizeObserver) {
    resizeObserver = new ResizeObserver(() => {
      updateContainerHeight()
    })
    resizeObserver.observe(containerRef.value)
  }
})

onUnmounted(() => {
  if (resizeObserver) {
    resizeObserver.disconnect()
  }
})

// 监听数据变化，重置滚动位置
watch(() => props.items.length, (newLength, oldLength) => {
  // 如果是新数据（长度变为0或大幅减少），滚动到顶部
  if (newLength === 0 || (oldLength > 0 && newLength < oldLength / 2)) {
    scrollToTop()
  }
})

// 暴露方法给父组件
defineExpose({
  scrollTo,
  scrollToTop,
  scrollToBottom,
  getScrollTop: () => scrollTop.value,
  getVisibleRange: () => ({ start: startIndex.value, end: endIndex.value })
})
</script>

<style scoped lang="scss">
.virtual-list {
  position: relative;
  overflow-y: auto;
  overflow-x: hidden;
  
  // 优化滚动性能
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
  
  // 启用硬件加速
  transform: translateZ(0);
  will-change: scroll-position;
}

.virtual-list-phantom {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: -1;
}

.virtual-list-content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  
  // 启用硬件加速
  transform: translateZ(0);
  will-change: transform;
}

.virtual-list-item {
  box-sizing: border-box;
  
  // 优化渲染性能
  contain: layout style paint;
}

.virtual-list-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #909399;
  font-size: 14px;
  
  .el-icon {
    margin-right: 8px;
  }
}

.virtual-list-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 200px;
  
  .empty-content {
    text-align: center;
    color: #909399;
    
    p {
      margin: 12px 0 0 0;
      font-size: 14px;
    }
  }
}

.default-item {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
}

// 自定义滚动条样式
.virtual-list::-webkit-scrollbar {
  width: 6px;
}

.virtual-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.virtual-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
  
  &:hover {
    background: #a8a8a8;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .virtual-list-item {
    // 移动端优化
    -webkit-tap-highlight-color: transparent;
  }

  .virtual-list-loading {
    padding: 16px;
    font-size: 13px;
  }
}
</style>
