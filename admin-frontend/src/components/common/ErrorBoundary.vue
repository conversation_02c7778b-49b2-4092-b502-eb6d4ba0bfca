<template>
  <div class="error-boundary">
    <!-- 正常渲染子组件 -->
    <slot v-if="!hasError" />
    
    <!-- 错误状态显示 -->
    <div v-else class="error-container">
      <div class="error-content">
        <!-- 错误图标 -->
        <div class="error-icon">
          <el-icon size="64" color="#f56565">
            <WarningFilled />
          </el-icon>
        </div>
        
        <!-- 错误信息 -->
        <div class="error-info">
          <h3 class="error-title">{{ errorTitle }}</h3>
          <p class="error-message">{{ errorMessage }}</p>
          
          <!-- 错误详情（开发环境显示） -->
          <details v-if="isDevelopment && errorDetails" class="error-details">
            <summary>错误详情</summary>
            <pre class="error-stack">{{ errorDetails }}</pre>
          </details>
        </div>
        
        <!-- 操作按钮 -->
        <div class="error-actions">
          <el-button type="primary" @click="handleRetry">
            <el-icon><Refresh /></el-icon>
            重试
          </el-button>
          
          <el-button @click="handleReload">
            <el-icon><RefreshRight /></el-icon>
            刷新页面
          </el-button>
          
          <el-button v-if="showReportButton" type="info" @click="handleReport">
            <el-icon><Warning /></el-icon>
            报告问题
          </el-button>
        </div>
        
        <!-- 建议操作 -->
        <div class="error-suggestions">
          <h4>您可以尝试：</h4>
          <ul>
            <li>检查网络连接是否正常</li>
            <li>刷新页面重新加载</li>
            <li>清除浏览器缓存</li>
            <li v-if="isDevelopment">检查控制台错误信息</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onErrorCaptured, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { WarningFilled, Refresh, RefreshRight, Warning } from '@element-plus/icons-vue'

// Props定义
interface Props {
  title?: string
  message?: string
  showReportButton?: boolean
  onRetry?: () => void | Promise<void>
  onReport?: (error: Error) => void
}

const props = withDefaults(defineProps<Props>(), {
  title: '页面加载出错',
  message: '抱歉，页面遇到了一些问题。请尝试刷新页面或联系技术支持。',
  showReportButton: true
})

// Emits定义
const emit = defineEmits<{
  error: [error: Error]
  retry: []
  reload: []
  report: [error: Error]
}>()

// 响应式数据
const hasError = ref(false)
const currentError = ref<Error | null>(null)
const retryCount = ref(0)
const maxRetries = 3

// 计算属性
const isDevelopment = computed(() => import.meta.env.DEV)

const errorTitle = computed(() => {
  if (currentError.value) {
    // 根据错误类型返回不同标题
    if (currentError.value.name === 'ChunkLoadError') {
      return '资源加载失败'
    }
    if (currentError.value.message?.includes('Network')) {
      return '网络连接异常'
    }
    if (currentError.value.message?.includes('timeout')) {
      return '请求超时'
    }
  }
  return props.title
})

const errorMessage = computed(() => {
  if (currentError.value) {
    // 根据错误类型返回友好的错误信息
    if (currentError.value.name === 'ChunkLoadError') {
      return '页面资源加载失败，可能是网络问题或版本更新导致。请刷新页面重试。'
    }
    if (currentError.value.message?.includes('Network')) {
      return '网络连接异常，请检查网络设置后重试。'
    }
    if (currentError.value.message?.includes('timeout')) {
      return '请求超时，请检查网络连接或稍后重试。'
    }
    
    // 开发环境显示详细错误信息
    if (isDevelopment.value) {
      return currentError.value.message
    }
  }
  return props.message
})

const errorDetails = computed(() => {
  if (currentError.value && isDevelopment.value) {
    return currentError.value.stack || currentError.value.toString()
  }
  return null
})

// 错误捕获
onErrorCaptured((error: Error, instance: any, info: any) => {
  console.error('🚨 ErrorBoundary捕获到错误:', error)
  console.error('错误实例:', instance)
  console.error('错误信息:', info)
  
  hasError.value = true
  currentError.value = error
  
  // 发送错误事件
  emit('error', error)
  
  // 自动报告错误（如果配置了）
  if (props.onReport) {
    props.onReport(error)
  }
  
  // 阻止错误继续向上传播
  return false
})

// 处理重试
const handleRetry = async () => {
  if (retryCount.value >= maxRetries) {
    ElMessage.warning(`已达到最大重试次数 (${maxRetries})，请刷新页面`)
    return
  }
  
  try {
    retryCount.value++
    console.log(`🔄 错误边界重试 (${retryCount.value}/${maxRetries})`)
    
    // 重置错误状态
    hasError.value = false
    currentError.value = null
    
    // 等待下一个tick确保DOM更新
    await nextTick()
    
    // 调用自定义重试逻辑
    if (props.onRetry) {
      await props.onRetry()
    }
    
    emit('retry')
    ElMessage.success('重试成功')
    
  } catch (error: any) {
    console.error('🚨 重试失败:', error)
    hasError.value = true
    currentError.value = error
    ElMessage.error('重试失败，请刷新页面')
  }
}

// 处理页面刷新
const handleReload = () => {
  emit('reload')
  
  // 显示确认对话框
  ElMessageBox.confirm(
    '刷新页面将丢失当前未保存的数据，确定要继续吗？',
    '确认刷新',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    window.location.reload()
  }).catch(() => {
    // 用户取消刷新
  })
}

// 处理错误报告
const handleReport = () => {
  if (currentError.value) {
    emit('report', currentError.value)
    
    // 这里可以集成错误报告服务
    const errorReport = {
      error: currentError.value.message,
      stack: currentError.value.stack,
      url: window.location.href,
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString()
    }
    
    console.log('📊 错误报告:', errorReport)
    ElMessage.success('错误报告已发送，感谢您的反馈')
  }
}

// 重置错误状态的方法（供外部调用）
const reset = () => {
  hasError.value = false
  currentError.value = null
  retryCount.value = 0
}

// 暴露方法给父组件
defineExpose({
  reset,
  hasError: computed(() => hasError.value),
  currentError: computed(() => currentError.value)
})
</script>

<style scoped lang="scss">
.error-boundary {
  width: 100%;
  height: 100%;
  min-height: 200px;
}

.error-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 40px 20px;
  background: #fafafa;
}

.error-content {
  max-width: 600px;
  text-align: center;
  
  .error-icon {
    margin-bottom: 24px;
  }
  
  .error-info {
    margin-bottom: 32px;
    
    .error-title {
      font-size: 24px;
      font-weight: 600;
      color: #2d3748;
      margin: 0 0 12px 0;
    }
    
    .error-message {
      font-size: 16px;
      color: #718096;
      line-height: 1.6;
      margin: 0 0 16px 0;
    }
    
    .error-details {
      text-align: left;
      margin-top: 16px;
      
      summary {
        cursor: pointer;
        font-weight: 600;
        color: #4a5568;
        margin-bottom: 8px;
      }
      
      .error-stack {
        background: #f7fafc;
        border: 1px solid #e2e8f0;
        border-radius: 4px;
        padding: 12px;
        font-size: 12px;
        color: #2d3748;
        overflow-x: auto;
        white-space: pre-wrap;
        word-break: break-all;
      }
    }
  }
  
  .error-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 32px;
  }
  
  .error-suggestions {
    text-align: left;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 20px;
    
    h4 {
      font-size: 16px;
      font-weight: 600;
      color: #2d3748;
      margin: 0 0 12px 0;
    }
    
    ul {
      margin: 0;
      padding-left: 20px;
      
      li {
        color: #4a5568;
        line-height: 1.6;
        margin-bottom: 4px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .error-container {
    padding: 20px 16px;
    min-height: 300px;
  }
  
  .error-content {
    .error-title {
      font-size: 20px;
    }
    
    .error-message {
      font-size: 14px;
    }
    
    .error-actions {
      flex-direction: column;
      align-items: center;
      
      .el-button {
        width: 100%;
        max-width: 200px;
      }
    }
  }
}
</style>
