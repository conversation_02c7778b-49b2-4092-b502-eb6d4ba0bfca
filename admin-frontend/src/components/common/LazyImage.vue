<template>
  <div 
    ref="containerRef"
    class="lazy-image-container"
    :class="{ 'is-loading': isLoading, 'has-error': hasError }"
    :style="containerStyle"
  >
    <!-- 占位符 -->
    <div v-if="isLoading && !hasError" class="lazy-image-placeholder">
      <slot name="placeholder">
        <div class="default-placeholder">
          <el-icon class="placeholder-icon">
            <Picture />
          </el-icon>
          <span v-if="showLoadingText" class="placeholder-text">加载中...</span>
        </div>
      </slot>
    </div>
    
    <!-- 错误状态 -->
    <div v-else-if="hasError" class="lazy-image-error">
      <slot name="error">
        <div class="default-error" @click="handleRetry">
          <el-icon class="error-icon">
            <PictureFilled />
          </el-icon>
          <span class="error-text">加载失败</span>
          <span class="error-retry">点击重试</span>
        </div>
      </slot>
    </div>
    
    <!-- 实际图片 -->
    <img
      v-else
      ref="imageRef"
      :src="currentSrc"
      :alt="alt"
      :class="imageClass"
      :style="imageStyle"
      class="lazy-image"
      @load="handleLoad"
      @error="handleError"
    />
    
    <!-- 加载进度条 -->
    <div v-if="showProgress && isLoading" class="lazy-image-progress">
      <div 
        class="progress-bar" 
        :style="{ width: loadProgress + '%' }"
      ></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { Picture, PictureFilled } from '@element-plus/icons-vue'

// Props定义
interface Props {
  src: string // 图片源地址
  alt?: string // 图片描述
  placeholder?: string // 占位图片
  errorImage?: string // 错误时显示的图片
  width?: string | number // 宽度
  height?: string | number // 高度
  fit?: 'fill' | 'contain' | 'cover' | 'none' | 'scale-down' // 图片适应方式
  lazy?: boolean // 是否懒加载
  threshold?: number // 懒加载阈值
  showLoadingText?: boolean // 是否显示加载文本
  showProgress?: boolean // 是否显示加载进度
  retryCount?: number // 重试次数
  imageClass?: string // 图片额外类名
  preload?: boolean // 是否预加载
}

const props = withDefaults(defineProps<Props>(), {
  alt: '',
  placeholder: '',
  errorImage: '',
  width: 'auto',
  height: 'auto',
  fit: 'cover',
  lazy: true,
  threshold: 100,
  showLoadingText: false,
  showProgress: false,
  retryCount: 3,
  imageClass: '',
  preload: false
})

// Emits定义
const emit = defineEmits<{
  load: [event: Event]
  error: [event: Event]
  intersect: [isIntersecting: boolean]
}>()

// 响应式数据
const containerRef = ref<HTMLElement>()
const imageRef = ref<HTMLImageElement>()
const isLoading = ref(true)
const hasError = ref(false)
const isIntersecting = ref(false)
const currentRetryCount = ref(0)
const loadProgress = ref(0)

// 计算属性
const currentSrc = computed(() => {
  if (hasError.value && props.errorImage) {
    return props.errorImage
  }
  
  if (isLoading.value && props.placeholder) {
    return props.placeholder
  }
  
  return props.src
})

const containerStyle = computed(() => {
  const style: Record<string, string> = {}
  
  if (props.width !== 'auto') {
    style.width = typeof props.width === 'number' ? `${props.width}px` : props.width
  }
  
  if (props.height !== 'auto') {
    style.height = typeof props.height === 'number' ? `${props.height}px` : props.height
  }
  
  return style
})

const imageStyle = computed(() => {
  return {
    objectFit: props.fit,
    width: '100%',
    height: '100%'
  }
})

// 交叉观察器
let intersectionObserver: IntersectionObserver | null = null

// 初始化交叉观察器
const initIntersectionObserver = () => {
  if (!props.lazy || !containerRef.value) return
  
  intersectionObserver = new IntersectionObserver(
    (entries) => {
      const entry = entries[0]
      isIntersecting.value = entry.isIntersecting
      
      emit('intersect', entry.isIntersecting)
      
      if (entry.isIntersecting) {
        loadImage()
        // 一旦开始加载，就停止观察
        intersectionObserver?.disconnect()
      }
    },
    {
      rootMargin: `${props.threshold}px`
    }
  )
  
  intersectionObserver.observe(containerRef.value)
}

// 加载图片
const loadImage = async () => {
  if (!props.src || hasError.value) return
  
  isLoading.value = true
  hasError.value = false
  loadProgress.value = 0
  
  try {
    // 创建新的图片对象进行预加载
    const img = new Image()
    
    // 模拟加载进度（实际情况下可能需要更复杂的进度计算）
    if (props.showProgress) {
      const progressInterval = setInterval(() => {
        if (loadProgress.value < 90) {
          loadProgress.value += Math.random() * 20
        }
      }, 100)
      
      img.onload = () => {
        clearInterval(progressInterval)
        loadProgress.value = 100
        setTimeout(() => {
          isLoading.value = false
        }, 200)
      }
      
      img.onerror = () => {
        clearInterval(progressInterval)
        handleImageError()
      }
    } else {
      img.onload = () => {
        isLoading.value = false
      }
      
      img.onerror = () => {
        handleImageError()
      }
    }
    
    img.src = props.src
    
  } catch (error) {
    console.error('🖼️ 图片加载异常:', error)
    handleImageError()
  }
}

// 处理图片加载成功
const handleLoad = (event: Event) => {
  isLoading.value = false
  hasError.value = false
  currentRetryCount.value = 0
  emit('load', event)
  console.log('✅ 图片加载成功:', props.src)
}

// 处理图片加载错误
const handleError = (event: Event) => {
  handleImageError()
  emit('error', event)
}

// 处理图片错误
const handleImageError = () => {
  console.error('❌ 图片加载失败:', props.src)
  
  if (currentRetryCount.value < props.retryCount) {
    // 自动重试
    currentRetryCount.value++
    console.log(`🔄 图片加载重试 (${currentRetryCount.value}/${props.retryCount})`)
    
    setTimeout(() => {
      loadImage()
    }, 1000 * currentRetryCount.value) // 递增延迟重试
  } else {
    // 达到最大重试次数，显示错误状态
    isLoading.value = false
    hasError.value = true
  }
}

// 手动重试
const handleRetry = () => {
  currentRetryCount.value = 0
  hasError.value = false
  loadImage()
}

// 预加载图片
const preloadImage = () => {
  if (!props.preload || !props.src) return
  
  const link = document.createElement('link')
  link.rel = 'preload'
  link.as = 'image'
  link.href = props.src
  document.head.appendChild(link)
}

// 监听src变化
watch(() => props.src, (newSrc, oldSrc) => {
  if (newSrc !== oldSrc) {
    currentRetryCount.value = 0
    hasError.value = false
    
    if (props.lazy && !isIntersecting.value) {
      // 如果是懒加载且不在视口内，重新开始观察
      initIntersectionObserver()
    } else {
      // 直接加载新图片
      loadImage()
    }
  }
})

// 生命周期
onMounted(async () => {
  await nextTick()
  
  // 预加载
  preloadImage()
  
  if (props.lazy) {
    // 懒加载模式
    initIntersectionObserver()
  } else {
    // 直接加载
    loadImage()
  }
})

onUnmounted(() => {
  if (intersectionObserver) {
    intersectionObserver.disconnect()
  }
})

// 暴露方法给父组件
defineExpose({
  retry: handleRetry,
  reload: loadImage,
  isLoading: computed(() => isLoading.value),
  hasError: computed(() => hasError.value)
})
</script>

<style scoped lang="scss">
.lazy-image-container {
  position: relative;
  display: inline-block;
  overflow: hidden;
  background-color: #f5f7fa;
  
  // 启用硬件加速
  transform: translateZ(0);
  
  &.is-loading {
    .lazy-image-placeholder {
      display: flex;
    }
  }
  
  &.has-error {
    .lazy-image-error {
      display: flex;
    }
  }
}

.lazy-image {
  display: block;
  transition: opacity 0.3s ease;
  
  // 优化渲染性能
  contain: layout style paint;
}

.lazy-image-placeholder,
.lazy-image-error {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
}

.default-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #c0c4cc;
  
  .placeholder-icon {
    font-size: 32px;
    margin-bottom: 8px;
  }
  
  .placeholder-text {
    font-size: 12px;
  }
}

.default-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #f56c6c;
  cursor: pointer;
  transition: color 0.3s ease;
  
  &:hover {
    color: #f78989;
  }
  
  .error-icon {
    font-size: 32px;
    margin-bottom: 8px;
  }
  
  .error-text {
    font-size: 12px;
    margin-bottom: 4px;
  }
  
  .error-retry {
    font-size: 10px;
    opacity: 0.8;
  }
}

.lazy-image-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background-color: rgba(0, 0, 0, 0.1);
  
  .progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #409eff, #67c23a);
    transition: width 0.3s ease;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .default-placeholder,
  .default-error {
    .placeholder-icon,
    .error-icon {
      font-size: 24px;
    }
    
    .placeholder-text,
    .error-text {
      font-size: 11px;
    }
    
    .error-retry {
      font-size: 9px;
    }
  }
}
</style>
