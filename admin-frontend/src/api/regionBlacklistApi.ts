import { http } from '@/utils/http'

// 黑名单条目接口定义
export interface BlacklistEntry {
  route: string           // 路线，如"上海市->安徽省"
  provider: string        // 供应商
  express_code: string    // 快递公司代码
  failure_count: number   // 失败次数
  last_failure_at: string // 最后失败时间
  error_message: string   // 错误信息
  is_blacklisted: boolean // 是否已加入黑名单
  created_at: string      // 创建时间
}

// 黑名单统计信息接口
export interface BlacklistStatistics {
  total_entries: number       // 总条目数
  blacklisted_entries: number // 黑名单条目数
  provider_stats: Record<string, number> // 按供应商统计
  route_stats: Record<string, number>    // 按路线统计
}

// 测试路线结果接口
export interface RouteTestResult {
  provider: string
  route: string
  express_code: string
  is_blacklisted: boolean
  should_skip: boolean
  entry?: BlacklistEntry
}

// API 响应接口
interface ApiResponse<T = any> {
  success: boolean
  message: string
  data: T
  total?: number
}

/**
 * 地区黑名单API
 */
export const regionBlacklistApi = {
  /**
   * 获取黑名单统计信息
   */
  getStatistics(): Promise<ApiResponse<BlacklistStatistics>> {
    return http.get({
      url: '/api/v1/admin/region-blacklist/statistics'
    })
  },

  /**
   * 获取所有黑名单条目
   */
  getAllEntries(): Promise<ApiResponse<BlacklistEntry[]>> {
    return http.get({
      url: '/api/v1/admin/region-blacklist/entries'
    })
  },

  /**
   * 获取特定黑名单条目
   */
  getEntry(params: {
    provider: string
    route: string
    express_code: string
  }): Promise<ApiResponse<BlacklistEntry>> {
    return http.get({
      url: '/api/v1/admin/region-blacklist/entry',
      params
    })
  },

  /**
   * 从黑名单中移除条目
   */
  removeFromBlacklist(data: {
    provider: string
    route: string
    express_code: string
  }): Promise<ApiResponse> {
    return http.post({
      url: '/api/v1/admin/region-blacklist/remove',
      data
    })
  },

  /**
   * 清理过期的黑名单条目
   */
  clearExpiredEntries(): Promise<ApiResponse<{
    before_total: number
    after_total: number
    cleared: number
  }>> {
    return http.post({
      url: '/api/v1/admin/region-blacklist/clear-expired'
    })
  },

  /**
   * 测试路线是否在黑名单中
   */
  testRoute(params: {
    provider: string
    route: string
    express_code: string
  }): Promise<ApiResponse<RouteTestResult>> {
    return http.get({
      url: '/api/v1/admin/region-blacklist/test',
      params
    })
  }
} 