import { http } from '@/utils/http'

/**
 * 供应商状态信息
 */
export interface ProviderStatus {
  code: string
  enabled: boolean
  has_adapter: boolean
  error?: string
  last_check: string
  last_reload?: string
  reload_count: number
  health_status: string
}

/**
 * 供应商指标信息
 */
export interface ProviderMetric {
  code: string
  reload_count: number
  last_reload: string
  last_error?: string
  success_rate: number
  avg_reload_time: number
}

/**
 * 供应商重载指标
 */
export interface ProviderMetrics {
  total_reloads: number
  success_reloads: number
  failed_reloads: number
  last_reload_time: string
  provider_metrics: Record<string, ProviderMetric>
}

/**
 * 供应商重载响应
 */
export interface ProviderReloadResponse {
  provider_code: string
  success: boolean
  message: string
  status: ProviderStatus
}

/**
 * 所有供应商重载响应
 */
export interface AllProvidersReloadResponse {
  success: boolean
  message: string
  providers: Record<string, ProviderStatus>
}

/**
 * API响应基础类型
 */
export interface ApiResponse<T = any> {
  success: boolean
  code: number
  message: string
  data: T
}

/**
 * 供应商重载管理 API 类
 */
export class ProviderReloadApi {
  /**
   * 重新加载指定供应商
   */
  static async reloadProvider(providerCode: string): Promise<ApiResponse<ProviderReloadResponse>> {
    return http.post({
      url: `/api/v1/admin/providers/${providerCode}/reload`,
      data: {}
    })
  }

  /**
   * 重新加载所有供应商
   */
  static async reloadAllProviders(): Promise<ApiResponse<AllProvidersReloadResponse>> {
    return http.post({
      url: '/api/v1/admin/providers/reload-all',
      data: {}
    })
  }

  /**
   * 获取供应商状态
   */
  static async getProviderStatus(): Promise<ApiResponse<Record<string, ProviderStatus>>> {
    return http.get({
      url: '/api/v1/admin/providers/status',
      params: {
        _t: Date.now() // 防止缓存
      }
    })
  }

  /**
   * 获取供应商重载指标
   */
  static async getProviderMetrics(): Promise<ApiResponse<ProviderMetrics>> {
    return http.get({
      url: '/api/v1/admin/providers/metrics',
      params: {
        _t: Date.now() // 防止缓存
      }
    })
  }

  /**
   * 获取指定供应商的状态
   */
  static async getProviderStatusByCode(providerCode: string): Promise<ApiResponse<ProviderStatus>> {
    const response = await this.getProviderStatus()
    if (response.success && response.data[providerCode]) {
      return {
        success: true,
        code: 200,
        message: '获取成功',
        data: response.data[providerCode]
      }
    }
    return {
      success: false,
      code: 404,
      message: '供应商不存在',
      data: {} as ProviderStatus
    }
  }

  /**
   * 批量重载指定供应商
   */
  static async batchReloadProviders(providerCodes: string[]): Promise<ApiResponse<Record<string, ProviderReloadResponse>>> {
    const results: Record<string, ProviderReloadResponse> = {}
    
    // 并发重载所有指定的供应商
    const promises = providerCodes.map(async (code) => {
      try {
        const response = await this.reloadProvider(code)
        results[code] = response.data
      } catch (error) {
        results[code] = {
          provider_code: code,
          success: false,
          message: error instanceof Error ? error.message : '重载失败',
          status: {} as ProviderStatus
        }
      }
    })

    await Promise.all(promises)

    return {
      success: true,
      code: 200,
      message: '批量重载完成',
      data: results
    }
  }

  /**
   * 检查供应商健康状态
   */
  static async checkProviderHealth(providerCode: string): Promise<ApiResponse<{
    healthy: boolean
    message: string
    last_check: string
  }>> {
    try {
      const statusResponse = await this.getProviderStatusByCode(providerCode)
      if (statusResponse.success) {
        const status = statusResponse.data
        return {
          success: true,
          code: 200,
          message: '健康检查完成',
          data: {
            healthy: status.enabled && status.has_adapter && status.health_status === 'healthy',
            message: status.health_status,
            last_check: status.last_check
          }
        }
      }
      return {
        success: false,
        code: 404,
        message: '供应商不存在',
        data: {
          healthy: false,
          message: '供应商不存在',
          last_check: new Date().toISOString()
        }
      }
    } catch (error) {
      return {
        success: false,
        code: 500,
        message: error instanceof Error ? error.message : '健康检查失败',
        data: {
          healthy: false,
          message: '健康检查失败',
          last_check: new Date().toISOString()
        }
      }
    }
  }

  /**
   * 获取重载历史统计
   */
  static async getReloadHistory(providerCode?: string, limit: number = 50): Promise<ApiResponse<{
    total: number
    history: Array<{
      provider_code: string
      timestamp: string
      success: boolean
      duration_ms: number
      error?: string
    }>
  }>> {
    // 注意：这个API需要后端支持，目前返回模拟数据
    return {
      success: true,
      code: 200,
      message: '获取重载历史成功',
      data: {
        total: 0,
        history: []
      }
    }
  }
}
