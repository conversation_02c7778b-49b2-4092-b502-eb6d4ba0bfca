import { http } from '@/utils/http'

// ==================== 数据类型定义 ====================

// 基础响应类型
export interface BaseResponse<T = any> {
  success: boolean
  code: string
  message: string
  data: T
  timestamp?: string
  request_id?: string
}

// 分页请求参数
export interface CacheOverviewRequest {
  page: number
  page_size: number
  provider?: string
  route?: string
  sort_field?: string
  sort_order?: string
}

// 失败原因信息
export interface FailureReasonInfo {
  error_message: string
  count: number
  last_occurred: string
  source: string
}

// 分页响应数据（匹配后端实际返回的字段名）
export interface CacheOverviewData {
  route: string
  from_province: string
  to_province: string
  weight_kg: number
  price: string
  continued_weight_per_kg: string
  product_code: string
  product_name: string
  channel_id: string
  estimated_days: number
  cache_hit_count: number
  validation_count: number
  validation_failure_count: number
  is_valid: boolean
  last_hit_time?: string
  last_validated_time?: string
  created_at: string
  updated_at: string

  // 🚀 新增：失败统计和原因（匹配后端字段名）
  failed_queries?: number
  total_queries?: number
  failure_reasons?: FailureReasonInfo[]

  // 兼容旧字段（用于向后兼容）
  provider?: string
  expressCode?: string
  weightKg?: number
  cacheCount?: number
  validCacheCount?: number
  cacheHitCount?: number
  avgPrice?: number
  lastUpdateTime?: string
  invalidCacheCount?: number
  minPrice?: number
  maxPrice?: number
  refreshed_at?: string
}

export interface CacheOverviewResponse extends BaseResponse {
  data: {
    data: CacheOverviewData[]
    total_count: number
    page: number
    page_size: number
    total_pages: number
  }
}

// 快速统计响应
export interface QuickStatsResponse {
  total_providers: number
  total_cache_entries: number
  valid_cache_count: number
  cache_hit_rate: number
  last_updated: string
}

// 供应商分组数据（匹配后端实际返回的字段名）
export interface ProviderGroup {
  provider: string
  provider_name: string
  total_cache_count: number
  valid_cache_count: number
  invalid_cache_count?: number
  total_hit_count: number  // 后端返回的是total_hit_count，不是cache_hit_count
  total_validation_count?: number
  total_validation_failure_count?: number
  avg_price: number
  min_price?: number
  max_price?: number
  last_update_time: string
  refreshed_at?: string
  express_companies?: CompanyInfo[] | null  // 后端返回null

  // 计算属性
  cache_hit_rate?: number
  companies?: CompanyInfo[]
}

export interface CompanyInfo {
  express_code: string
  express_name: string
  cache_count: number
  valid_cache_count: number
  cache_hit_count: number
  avg_price: number
  weight_ranges: number[]
}

// 缓存统计请求参数
export interface CacheStatisticsRequest {
  startDate: string
  endDate: string
  provider?: string
}

// 缓存统计响应（匹配后端实际返回的数据结构）
export interface CacheStatisticsResponse extends BaseResponse {
  data: {
    total_queries: number
    cache_hit_rate: string  // 后端返回字符串
    validation_pass_rate: string  // 后端返回字符串
    avg_response_time: string  // 后端返回字符串
    provider_stats: Record<string, any> | ProviderStats[]  // 后端返回对象或数组
    daily_stats: any[]

    // 兼容字段
    cache_hits?: number
    cache_misses?: number
    hit_rate?: number
  }
}

export interface ProviderStats {
  provider: string
  queries: number
  hits: number
  misses: number
  hit_rate: number
}

// 验证统计响应
export interface ValidationStatsResponse extends BaseResponse {
  data: PriceValidationStats[]
}

export interface PriceValidationStats {
  provider: string
  express_code: string
  total_validations: number
  exact_matches: number
  price_increases: number
  price_decreases: number
  success_rate: number
  avg_price_difference: number
}

// 缓存预热请求
export interface CacheWarmupRequest {
  routes: string[]
  providers: string[]
  weights: number[]
  express_codes: string[]
}

// 预热进度响应
export interface WarmupProgressResponse extends BaseResponse {
  data: {
    status: string
    progress: number
    total_tasks: number
    completed_tasks: number
    failed_tasks: number
    estimated_remaining_time: number
  }
}

// 价格查询请求
export interface WeightCacheQueryRequest {
  from_province: string
  to_province: string
  provider: string
  express_code: string
  weight: number
}

// 价格查询响应
export interface WeightCacheQueryResponse extends BaseResponse {
  data: {
    price: number
    continued_weight_per_kg: number
    estimated_days: number
    product_name: string
    cache_hit: boolean
    source: string
  }
}

// 订单验证请求
export interface OrderValidationRequest {
  order_no: string
  expected_price: number
  actual_price: number
  provider: string
  express_code: string
}

// 订单验证响应
export interface OrderValidationResponse extends BaseResponse {
  data: {
    is_valid: boolean
    price_difference: number
    validation_result: string
    recommendations: string[]
  }
}

// ==================== API 接口定义 ====================

export const weightCacheApi = {
  // 🚀 核心功能API
  // 带缓存的价格查询
  queryPriceWithCache: (data: WeightCacheQueryRequest): Promise<WeightCacheQueryResponse> => {
    return http.post({ url: '/api/v1/weight-cache/query', data })
  },

  // 订单价格验证
  validateOrderPrice: (data: OrderValidationRequest): Promise<OrderValidationResponse> => {
    return http.post({ url: '/api/v1/weight-cache/validate', data })
  },

  // 🚀 缓存管理API
  // 使缓存失效
  invalidateCache: (params: {
    fromProvince: string
    toProvince: string
    provider: string
    expressCode: string
    weight: number
  }) => {
    return http.del({ 
      url: '/api/v1/weight-cache/invalidate', 
      params: {
        from_province: params.fromProvince,
        to_province: params.toProvince,
        provider: params.provider,
        express_code: params.expressCode,
        weight: params.weight
      }
    })
  },

  // 缓存预热
  warmupCache: (data: CacheWarmupRequest) => {
    return http.post({ url: '/api/v1/weight-cache/warmup', data })
  },

  // 获取预热进度
  getWarmupProgress: (): Promise<WarmupProgressResponse> => {
    return http.post({ url: '/api/v1/weight-cache/warmup/progress' })
  },

  // 清理无效缓存
  cleanupInvalidCache: () => {
    return http.del({ url: '/api/v1/weight-cache/cleanup' })
  },

  // 🚀 清除供应商+快递公司的所有缓存
  clearProviderCompanyCache: (params: {
    provider: string
    expressCode: string
  }) => {
    return http.del({
      url: '/api/v1/weight-cache/clear-provider-company',
      params: {
        provider: params.provider,
        express_code: params.expressCode
      }
    })
  },

  // 🚀 统计查询API
  // 获取缓存统计
  getCacheStatistics: (params: CacheStatisticsRequest): Promise<CacheStatisticsResponse> => {
    const queryParams = {
      start_date: params.startDate,
      end_date: params.endDate,
      ...(params.provider && { provider: params.provider })
    }
    return http.get({ url: '/api/v1/weight-cache/statistics', params: queryParams })
  },

  // 获取验证统计
  getValidationStats: (params: {
    startDate: string
    endDate: string
    provider?: string
  }): Promise<ValidationStatsResponse> => {
    const queryParams = {
      start_date: params.startDate,
      end_date: params.endDate,
      ...(params.provider && { provider: params.provider })
    }
    return http.get({ url: '/api/v1/weight-cache/validation-stats', params: queryParams })
  },

  // 🚀 优化后的API接口（推荐使用）
  // 获取缓存概览（优化版本 - 支持分页）
  getCacheOverviewOptimized: (params: CacheOverviewRequest): Promise<CacheOverviewResponse> => {
    return http.get({ url: '/api/v1/weight-cache/overview-optimized', params })
  },

  // 获取供应商分组概览（优化版本）
  getProviderGroupedOverviewOptimized: (): Promise<BaseResponse<ProviderGroup[]>> => {
    return http.get({ url: '/api/v1/weight-cache/overview/grouped-optimized' })
  },

  // 🚀 获取缓存详情（包含失败原因）
  getCacheDetails: (params: {
    provider: string
    express_code: string
    from_province?: string
    to_province?: string
    weight_kg?: number
    page: number
    page_size: number
  }): Promise<BaseResponse<{
    records: CacheOverviewData[]  // 🔧 修复：使用正确的字段名 records
    total: number
    page: number
    page_size: number
  }>> => {
    return http.get({ url: '/api/v1/weight-cache/details', params })
  },

  // 获取快速统计
  getQuickStats: (): Promise<BaseResponse<QuickStatsResponse>> => {
    return http.get({ url: '/api/v1/weight-cache/quick-stats' })
  },

  // 刷新缓存视图
  refreshCacheViews: () => {
    return http.post({ url: '/api/v1/weight-cache/refresh-views' })
  }
}
