import http from '@/utils/http'

// ==================== 基础类型定义 ====================

/**
 * API响应基础类型
 */
export interface ApiResponse<T = any> {
  success: boolean
  code: number
  message: string
  data: T
}

/**
 * 状态更新请求
 */
export interface StatusUpdateRequest {
  enabled: boolean
  reason?: string
}

/**
 * 状态更新响应
 */
export interface StatusUpdateResponse {
  company_code?: string
  provider_code?: string
  enabled: boolean
  updated_at: string
  updated_by: string
}

/**
 * 分页参数
 */
export interface PaginationParams {
  page: number
  page_size: number
}

/**
 * 分页响应
 */
export interface PaginationResponse {
  total: number
  page: number
  page_size: number
}

// ==================== 快递公司相关类型 ====================

/**
 * 快递公司模型
 */
export interface ExpressCompany {
  id: string
  code: string
  name: string
  english_name?: string
  official_website?: string
  description?: string
  is_active: boolean
  sort_order: number
  volume_weight_ratio: number
  max_weight_kg?: number
  created_at: string
  updated_at: string
  created_by?: string
  updated_by?: string
  
  // 关联数据
  providers?: ExpressProvider[]
  services?: ExpressCompanyService[]
  configs?: Record<string, ExpressCompanyConfig>
  mappings?: ExpressCompanyProviderMapping[]
}

/**
 * 快递公司列表响应
 */
export interface ExpressCompanyListResponse extends ApiResponse<{
  companies: ExpressCompany[]
  total: number
  page: number
  page_size: number
}> {}

/**
 * 快递公司筛选参数
 */
export interface ExpressCompanyFilter {
  keyword?: string
  is_active?: boolean
  sort_by?: string
  sort_order?: 'ASC' | 'DESC'
}

/**
 * 创建快递公司请求
 */
export interface CreateExpressCompanyRequest {
  code: string
  name: string
  english_name?: string
  official_website?: string
  description?: string
  is_active?: boolean
  sort_order?: number
  volume_weight_ratio?: number
  max_weight_kg?: number
}

/**
 * 更新快递公司请求
 */
export interface UpdateExpressCompanyRequest {
  name?: string
  english_name?: string
  official_website?: string
  description?: string
  is_active?: boolean
  sort_order?: number
  volume_weight_ratio?: number
  max_weight_kg?: number
}

// ==================== 供应商相关类型 ====================

/**
 * 快递供应商模型
 */
export interface ExpressProvider {
  id: string
  code: string
  name: string
  api_endpoint?: string
  api_version?: string
  description?: string
  is_active: boolean
  priority: number
  created_at: string
  updated_at: string
  created_by?: string
  updated_by?: string
}

/**
 * 供应商列表响应
 */
export interface ExpressProviderListResponse extends ApiResponse<{
  providers: ExpressProvider[]
  total: number
  page: number
  page_size: number
}> {}

/**
 * 供应商筛选参数
 */
export interface ExpressProviderFilter {
  keyword?: string
  is_active?: boolean
  sort_by?: string
  sort_order?: 'ASC' | 'DESC'
}

/**
 * 创建供应商请求
 */
export interface CreateExpressProviderRequest {
  code: string
  name: string
  api_endpoint?: string
  api_version?: string
  description?: string
  is_active?: boolean
  priority?: number
}

/**
 * 更新供应商请求
 */
export interface UpdateExpressProviderRequest {
  name?: string
  api_endpoint?: string
  api_version?: string
  description?: string
  is_active?: boolean
  priority?: number
}

// ==================== 映射关系相关类型 ====================

/**
 * 快递公司供应商映射关系
 */
export interface ExpressCompanyProviderMapping {
  id: string
  company_id: string
  provider_id: string
  provider_company_code: string
  is_supported: boolean
  is_preferred: boolean
  weight_limit_kg?: number
  size_limit_cm_length?: number
  size_limit_cm_width?: number
  size_limit_cm_height?: number
  supported_services?: string[]
  pricing_config?: Record<string, any>
  created_at: string
  updated_at: string
  created_by?: string
  updated_by?: string
  
  // 关联数据
  provider_code?: string
  provider_name?: string

  // 🔥 新增：前端状态管理
  _switching?: boolean // 切换状态时的加载状态
  _reloading?: boolean // 重载状态时的加载状态
}

/**
 * 映射关系列表响应
 */
export interface ExpressMappingListResponse extends ApiResponse<{
  mappings: ExpressCompanyProviderMapping[]
  total: number
  page: number
  page_size: number
}> {}

/**
 * 映射关系筛选参数
 */
export interface ExpressMappingFilter {
  company_id?: string
  provider_id?: string
  is_supported?: boolean
  is_preferred?: boolean
  sort_by?: string
  sort_order?: 'ASC' | 'DESC'
}

/**
 * 创建映射关系请求
 */
export interface CreateExpressMappingRequest {
  company_id: string
  provider_id: string
  provider_company_code: string
  is_supported?: boolean
  is_preferred?: boolean
  weight_limit_kg?: number
  size_limit_cm_length?: number
  size_limit_cm_width?: number
  size_limit_cm_height?: number
  supported_services?: string[]
  pricing_config?: Record<string, any>
}

/**
 * 更新映射关系请求
 */
export interface UpdateExpressMappingRequest {
  provider_company_code?: string
  is_supported?: boolean
  is_preferred?: boolean
  weight_limit_kg?: number
  size_limit_cm_length?: number
  size_limit_cm_width?: number
  size_limit_cm_height?: number
  supported_services?: string[]
  pricing_config?: Record<string, any>
}

// ==================== 服务相关类型 ====================

/**
 * 快递公司服务
 */
export interface ExpressCompanyService {
  id: string
  company_id: string
  service_code: string
  service_name: string
  description?: string
  estimated_days?: number
  is_active: boolean
  sort_order: number
  created_at: string
  updated_at: string
  created_by?: string
  updated_by?: string
}

/**
 * 创建服务请求
 */
export interface CreateExpressServiceRequest {
  company_id: string
  service_code: string
  service_name: string
  description?: string
  estimated_days?: number
  is_active?: boolean
  sort_order?: number
}

/**
 * 更新服务请求
 */
export interface UpdateExpressServiceRequest {
  service_name?: string
  description?: string
  estimated_days?: number
  is_active?: boolean
  sort_order?: number
}

// ==================== 配置相关类型 ====================

/**
 * 快递公司配置
 */
export interface ExpressCompanyConfig {
  id: string
  company_id: string
  config_key: string
  config_value: string
  config_type: string
  description?: string
  is_active: boolean
  created_at: string
  updated_at: string
  deleted_at?: string
  is_deleted: boolean
  created_by?: string
  updated_by?: string
}

/**
 * 创建配置请求
 */
export interface CreateExpressConfigRequest {
  company_id: string
  config_key: string
  config_value: string
  config_type: string
  description?: string
  is_active?: boolean
}

/**
 * 更新配置请求
 */
export interface UpdateExpressConfigRequest {
  config_value?: string
  config_type?: string
  description?: string
  is_active?: boolean
}

// ==================== 审计日志相关类型 ====================

/**
 * 快递公司审计日志
 */
export interface ExpressCompanyAuditLog {
  id: string
  company_id?: string
  provider_id?: string
  mapping_id?: string
  service_id?: string
  config_id?: string
  action: string
  resource_type: string
  resource_id: string
  old_values?: Record<string, any>
  new_values?: Record<string, any>
  operator_id: string
  operator_name?: string
  ip_address?: string
  user_agent?: string
  created_at: string
}

/**
 * 审计日志列表响应
 */
export interface ExpressAuditLogListResponse extends ApiResponse<{
  logs: ExpressCompanyAuditLog[]
  total: number
  page: number
  page_size: number
}> {}

/**
 * 审计日志筛选参数
 */
export interface ExpressAuditLogFilter {
  company_id?: string
  provider_id?: string
  action?: string
  resource_type?: string
  operator_id?: string
  start_time?: string
  end_time?: string
  sort_by?: string
  sort_order?: 'ASC' | 'DESC'
}

// ==================== API调用类 ====================

/**
 * 快递公司管理API
 */
export class ExpressCompanyApi {

  // ==================== 快递公司管理 ====================

  /**
   * 获取快递公司列表
   */
  static async getCompanies(params: PaginationParams & ExpressCompanyFilter): Promise<ExpressCompanyListResponse> {
    return http.get({
      url: '/api/v1/admin/express/companies',
      params: {
        ...params,
        _t: Date.now() // 防止缓存
      }
    })
  }

  /**
   * 获取快递公司详情
   */
  static async getCompany(id: string): Promise<ApiResponse<ExpressCompany>> {
    return http.get({
      url: `/api/v1/admin/express/companies/${id}`,
      params: {
        _t: Date.now() // 防止缓存
      }
    })
  }

  /**
   * 创建快递公司
   */
  static async createCompany(data: CreateExpressCompanyRequest): Promise<ApiResponse<ExpressCompany>> {
    return http.post({
      url: '/api/v1/admin/express/companies',
      data
    })
  }

  /**
   * 更新快递公司
   */
  static async updateCompany(id: string, data: UpdateExpressCompanyRequest): Promise<ApiResponse<ExpressCompany>> {
    return http.put({
      url: `/api/v1/admin/express/companies/${id}`,
      data
    })
  }

  /**
   * 删除快递公司
   */
  static async deleteCompany(id: string): Promise<ApiResponse<void>> {
    return http.del({
      url: `/api/v1/admin/express/companies/${id}`
    })
  }

  /**
   * 更新快递公司状态（新的状态管理API）
   */
  static async updateCompanyStatus(companyCode: string, data: StatusUpdateRequest): Promise<ApiResponse<StatusUpdateResponse>> {
    return http.patch({
      url: `/api/v1/admin/express/companies/${companyCode}/status`,
      data
    })
  }

  // ==================== 供应商管理 ====================

  /**
   * 获取供应商列表
   */
  static async getProviders(params: PaginationParams & ExpressProviderFilter): Promise<ExpressProviderListResponse> {
    return http.get({
      url: '/api/v1/admin/express/providers',
      params: {
        ...params,
        _t: Date.now() // 防止缓存
      }
    })
  }

  /**
   * 获取供应商详情
   */
  static async getProvider(id: string): Promise<ApiResponse<ExpressProvider>> {
    return http.get({
      url: `/api/v1/admin/express/providers/${id}`,
      params: {
        _t: Date.now() // 防止缓存
      }
    })
  }

  /**
   * 创建供应商
   */
  static async createProvider(data: CreateExpressProviderRequest): Promise<ApiResponse<ExpressProvider>> {
    return http.post({
      url: '/api/v1/admin/express/providers',
      data
    })
  }

  /**
   * 更新供应商
   */
  static async updateProvider(id: string, data: UpdateExpressProviderRequest): Promise<ApiResponse<ExpressProvider>> {
    return http.put({
      url: `/api/v1/admin/express/providers/${id}`,
      data
    })
  }

  /**
   * 删除供应商
   */
  static async deleteProvider(id: string): Promise<ApiResponse<void>> {
    return http.del({
      url: `/api/v1/admin/express/providers/${id}`
    })
  }

  /**
   * 更新供应商状态（新的状态管理API）
   */
  static async updateProviderStatus(providerCode: string, data: StatusUpdateRequest): Promise<ApiResponse<StatusUpdateResponse>> {
    return http.patch({
      url: `/api/v1/admin/express/providers/${providerCode}/status`,
      data
    })
  }

  // ==================== 映射关系管理 ====================

  /**
   * 获取映射关系列表
   */
  static async getMappings(params: PaginationParams & ExpressMappingFilter): Promise<ExpressMappingListResponse> {
    return http.get({
      url: '/api/v1/admin/express/mappings',
      params: {
        ...params,
        _t: Date.now() // 防止缓存
      }
    })
  }

  /**
   * 获取映射关系详情
   */
  static async getMapping(id: string): Promise<ApiResponse<ExpressCompanyProviderMapping>> {
    return http.get({
      url: `/api/v1/admin/express/mappings/${id}`,
      params: {
        _t: Date.now() // 防止缓存
      }
    })
  }

  /**
   * 创建映射关系
   */
  static async createMapping(data: CreateExpressMappingRequest): Promise<ApiResponse<ExpressCompanyProviderMapping>> {
    return http.post({
      url: '/api/v1/admin/express/mappings',
      data
    })
  }

  /**
   * 更新映射关系
   */
  static async updateMapping(id: string, data: UpdateExpressMappingRequest): Promise<ApiResponse<ExpressCompanyProviderMapping>> {
    return http.put({
      url: `/api/v1/admin/express/mappings/${id}`,
      data
    })
  }

  /**
   * 手动触发供应商重载
   */
  static async reloadProvider(providerCode: string): Promise<ApiResponse<any>> {
    return http.post({
      url: `/api/v1/admin/providers/${providerCode}/reload`
    })
  }

  /**
   * 重载所有供应商
   */
  static async reloadAllProviders(): Promise<ApiResponse<any>> {
    return http.post({
      url: `/api/v1/admin/providers/reload-all`
    })
  }

  /**
   * 删除映射关系
   */
  static async deleteMapping(id: string): Promise<ApiResponse<void>> {
    return http.del({
      url: `/api/v1/admin/express/mappings/${id}`
    })
  }

  /**
   * 更新映射关系状态（新的状态管理API）
   */
  static async updateMappingStatus(companyCode: string, providerCode: string, data: StatusUpdateRequest): Promise<ApiResponse<StatusUpdateResponse>> {
    return http.patch({
      url: `/api/v1/admin/express/mappings/${companyCode}/${providerCode}/status`,
      data
    })
  }

  /**
   * 根据快递公司获取映射关系
   */
  static async getMappingsByCompany(companyId: string): Promise<ApiResponse<ExpressCompanyProviderMapping[]>> {
    return http.get({
      url: `/api/v1/admin/express/mappings/company/${companyId}`,
      params: {
        _t: Date.now() // 防止缓存
      }
    })
  }

  /**
   * 根据供应商获取映射关系
   */
  static async getMappingsByProvider(providerId: string): Promise<ApiResponse<ExpressCompanyProviderMapping[]>> {
    return http.get({
      url: `/api/v1/admin/express/mappings/provider/${providerId}`,
      params: {
        _t: Date.now() // 防止缓存
      }
    })
  }

  // ==================== 查价接口分配管理 ====================

  /**
   * 设置供应商+快递公司查价接口类型
   */
  static async setPriceInterface(data: {
    provider_code: string
    company_code: string
    interface_type: 'QUERY_PRICE' | 'QUERY_REALTIME_PRICE'
  }): Promise<ApiResponse<any>> {
    return http.post({
      url: '/api/v1/admin/express/price-interface',
      data
    })
  }

  /**
   * 获取供应商+快递公司查价接口类型
   */
  static async getPriceInterface(providerCode: string, companyCode: string): Promise<ApiResponse<{
    provider_code: string
    company_code: string
    interface_type: string
  }>> {
    return http.get({
      url: `/api/v1/admin/express/price-interface/${providerCode}/${companyCode}`,
      params: {
        _t: Date.now() // 防止缓存
      }
    })
  }

  /**
   * 获取所有接口分配信息
   */
  static async getAllAllocations(params?: {
    page?: number
    page_size?: number
  }): Promise<ApiResponse<{
    allocations: Array<{
      id: number
      provider_code: string
      company_code: string
      interface_type: string
      is_active: boolean
      created_at: string
      updated_at: string
    }>
    total: number
    page: number
    page_size: number
  }>> {
    return http.get({
      url: '/api/v1/admin/express/price-interface/allocations',
      params: {
        ...params,
        _t: Date.now() // 防止缓存
      }
    })
  }

  /**
   * 获取接口分配统计信息
   */
  static async getAllocationStats(): Promise<ApiResponse<{
    total_allocations: number
    standard_price_count: number
    realtime_price_count: number
    provider_stats: Array<{
      provider_code: string
      total_count: number
      standard_count: number
      realtime_count: number
    }>
    company_stats: Array<{
      company_code: string
      total_count: number
      standard_count: number
      realtime_count: number
    }>
  }>> {
    return http.get({
      url: '/api/v1/admin/express/price-interface/stats',
      params: {
        _t: Date.now() // 防止缓存
      }
    })
  }

  // ==================== 服务管理 ====================

  /**
   * 创建快递公司服务
   */
  static async createService(data: CreateExpressServiceRequest): Promise<ApiResponse<ExpressCompanyService>> {
    return http.post({
      url: '/api/v1/admin/express/services',
      data
    })
  }

  /**
   * 根据快递公司获取服务列表
   */
  static async getServicesByCompany(companyId: string): Promise<ApiResponse<ExpressCompanyService[]>> {
    return http.get({
      url: `/api/v1/admin/express/services/company/${companyId}`,
      params: {
        _t: Date.now() // 防止缓存
      }
    })
  }

  /**
   * 更新快递公司服务
   */
  static async updateService(id: string, data: UpdateExpressServiceRequest): Promise<ApiResponse<ExpressCompanyService>> {
    return http.put({
      url: `/api/v1/admin/express/services/${id}`,
      data
    })
  }

  /**
   * 删除快递公司服务
   */
  static async deleteService(id: string): Promise<ApiResponse<void>> {
    return http.del({
      url: `/api/v1/admin/express/services/${id}`
    })
  }

  // ==================== 配置管理 ====================

  /**
   * 创建快递公司配置
   */
  static async createConfig(data: CreateExpressConfigRequest): Promise<ApiResponse<ExpressCompanyConfig>> {
    return http.post({
      url: '/api/v1/admin/express/configs',
      data
    })
  }

  /**
   * 根据快递公司获取配置列表
   */
  static async getConfigsByCompany(companyId: string): Promise<ApiResponse<ExpressCompanyConfig[]>> {
    return http.get({
      url: `/api/v1/admin/express/configs/company/${companyId}`,
      params: {
        _t: Date.now() // 防止缓存
      }
    })
  }

  /**
   * 获取特定配置
   */
  static async getConfig(companyId: string, configKey: string): Promise<ApiResponse<ExpressCompanyConfig>> {
    return http.get({
      url: `/api/v1/admin/express/configs/company/${companyId}/${configKey}`,
      params: {
        _t: Date.now() // 防止缓存
      }
    })
  }

  /**
   * 更新快递公司配置
   */
  static async updateConfig(id: string, data: UpdateExpressConfigRequest): Promise<ApiResponse<ExpressCompanyConfig>> {
    return http.put({
      url: `/api/v1/admin/express/configs/${id}`,
      data
    })
  }

  /**
   * 删除快递公司配置
   */
  static async deleteConfig(id: string): Promise<ApiResponse<void>> {
    return http.del({
      url: `/api/v1/admin/express/configs/${id}`
    })
  }

  // ==================== 审计日志 ====================

  /**
   * 获取审计日志列表
   */
  static async getAuditLogs(params: PaginationParams & ExpressAuditLogFilter): Promise<ExpressAuditLogListResponse> {
    return http.get({
      url: '/api/v1/admin/express/audit-logs',
      params: {
        ...params,
        _t: Date.now() // 防止缓存
      }
    })
  }

  // ==================== 工具方法 ====================

  /**
   * 获取启用的快递公司列表（用于下拉选择）
   */
  static async getActiveCompanies(): Promise<ApiResponse<ExpressCompany[]>> {
    return http.get({
      url: '/api/v1/express/companies',
      params: {
        _t: Date.now() // 防止缓存
      }
    })
  }

  /**
   * 获取启用的供应商列表（用于下拉选择）
   */
  static async getActiveProviders(): Promise<ApiResponse<ExpressProvider[]>> {
    return http.get({
      url: '/api/v1/express/providers',
      params: {
        _t: Date.now() // 防止缓存
      }
    })
  }

  /**
   * 根据代码获取快递公司
   */
  static async getCompanyByCode(code: string): Promise<ApiResponse<ExpressCompany>> {
    return http.get({
      url: `/api/v1/express/companies/${code}`,
      params: {
        _t: Date.now() // 防止缓存
      }
    })
  }

  /**
   * 根据代码获取供应商
   */
  static async getProviderByCode(code: string): Promise<ApiResponse<ExpressProvider>> {
    return http.get({
      url: `/api/v1/express/providers/${code}`,
      params: {
        _t: Date.now() // 防止缓存
      }
    })
  }

  /**
   * 获取供应商特定的快递公司代码
   */
  static async getProviderCompanyCode(companyCode: string, providerCode: string): Promise<ApiResponse<{ provider_company_code: string }>> {
    return http.get({
      url: `/api/v1/express/mappings/provider-code/${companyCode}/${providerCode}`,
      params: {
        _t: Date.now() // 防止缓存
      }
    })
  }
}

// ==================== 常量定义 ====================

/**
 * 快递公司状态选项
 */
export const EXPRESS_COMPANY_STATUS_OPTIONS = [
  { label: '全部', value: undefined },
  { label: '启用', value: true },
  { label: '禁用', value: false }
]

/**
 * 排序字段选项
 */
export const EXPRESS_COMPANY_SORT_OPTIONS = [
  { label: '代码', value: 'code' },
  { label: '名称', value: 'name' },
  { label: '排序', value: 'sort_order' },
  { label: '创建时间', value: 'created_at' },
  { label: '更新时间', value: 'updated_at' }
]

/**
 * 排序方向选项
 */
export const SORT_ORDER_OPTIONS = [
  { label: '升序', value: 'ASC' },
  { label: '降序', value: 'DESC' }
]

/**
 * 配置类型选项
 */
export const CONFIG_TYPE_OPTIONS = [
  { label: '字符串', value: 'string' },
  { label: '数字', value: 'number' },
  { label: '布尔值', value: 'boolean' },
  { label: 'JSON', value: 'json' }
]

export default ExpressCompanyApi
