import { RoutesAlias } from './routesAlias'
import { MenuListType } from '@/types/menu'

/**
 * 菜单列表、异步路由
 *
 * 支持两种模式:
 * 1. 前端静态配置 - 直接使用本文件中定义的路由配置
 * 2. 后端动态配置 - 后端返回菜单数据，前端解析生成路由
 *
 * 菜单标题（title）:
 * 可以是 i18n 的 key，也可以是字符串，比如：'用户列表'
 */
export const asyncRoutes: MenuListType[] = [
  {
    id: 1,
    name: 'Dashboard',
    path: '/dashboard',
    component: RoutesAlias.Home,
    meta: {
      title: 'menus.dashboard.title',
      icon: '&#xe721;',
      keepAlive: false
    },
    children: [
      {
        id: 101,
        path: 'console',
        name: 'Console',
        component: RoutesAlias.Dashboard,
        meta: {
          title: 'menus.dashboard.console',
          keepAlive: true
        }
      }
    ]
  },
  {
    id: 2,
    name: 'UserManagement',
    path: '/users',
    component: '/users/index',
    meta: {
      title: '用户管理',
      icon: '&#xe608;',
      keepAlive: true,
      isInMainContainer: true
    }
  },
  {
    id: 3,
    name: 'OrderManagement',
    path: '/orders',
    component: '/orders/index',
    meta: {
      title: '订单管理',
      icon: '&#xe76c;',
      keepAlive: true,
      isInMainContainer: true
    }
  },
  {
    id: 4,
    name: 'BalanceManagement',
    path: '/balance',
    component: '/balance/index',
    meta: {
      title: '余额管理',
      icon: '&#xe6ff;',
      keepAlive: true,
      isInMainContainer: true
    }
  },
  {
    id: 5,
    name: 'SystemConfig',
    path: '/system-config',
    component: '/system-config/index',
    meta: {
      title: '系统配置',
      icon: '&#xe614;',
      keepAlive: true,
      isInMainContainer: true
    }
  },
  {
    id: 6,
    name: 'ExpressCompany',
    path: '/express-company',
    component: '/express-company/index',
    meta: {
      title: '快递公司管理',
      icon: '&#xe6e8;',
      keepAlive: true,
      isInMainContainer: true
    }
  },
  {
    id: 14,
    name: 'ProviderManagement',
    path: '/provider-management',
    component: '/provider-management/index',
    meta: {
      title: '供应商管理',
      icon: '&#xe7ae;',
      keepAlive: true,
      isInMainContainer: true
    }
  },
  {
    id: 15,
    name: 'ProviderTest',
    path: '/provider-test',
    component: '/provider-test/index',
    meta: {
      title: 'API测试',
      icon: '&#xe6e8;',
      keepAlive: true,
      isInMainContainer: true
    }
  },
  {
    id: 16,
    name: 'WeightCacheManagement',
    path: '/weight-cache',
    component: '/weight-cache/index',
    meta: {
      title: '重量缓存管理',
      icon: '&#xe7e8;',
      keepAlive: true,
      isInMainContainer: true
    }
  },

  {
    id: 17,
    name: 'RegionBlacklistManagement',
    path: '/region-blacklist',
    component: '/region-blacklist/index',
    meta: {
      title: '地区黑名单管理',
      icon: '&#xe76c;',
      keepAlive: true,
      isInMainContainer: true
    }
  },
  {
    id: 18,
    name: 'InterfaceConfig',
    path: '/interface-config',
    component: RoutesAlias.Home,
    meta: {
      title: '接口配置管理',
      icon: '&#xe6e8;',
      keepAlive: false
    },
    children: [
      {
        id: 1801,
        path: 'allocation',
        name: 'InterfaceAllocation',
        component: '/interface-config/InterfaceAllocationManagement',
        meta: {
          title: '查价接口分配',
          keepAlive: true
        }
      }
    ]
  },
  {
    id: 7,
    name: 'RolePermission',
    path: '/role-permission',
    component: RoutesAlias.Home,
    meta: {
      title: '角色权限管理',
      icon: '&#xe7ae;',
      keepAlive: false
    },
    children: [
      {
        id: 701,
        path: 'role',
        name: 'RoleManagement',
        component: '/role-permission/role/index',
        meta: {
          title: '角色管理',
          keepAlive: true
        }
      },
      {
        id: 702,
        path: 'permission',
        name: 'PermissionManagement',
        component: '/role-permission/permission/index',
        meta: {
          title: '权限管理',
          keepAlive: true
        }
      },
      {
        id: 703,
        path: 'user-role',
        name: 'UserRoleManagement',
        component: '/role-permission/user-role/index',
        meta: {
          title: '用户角色管理',
          keepAlive: true
        }
      }
    ]
  },


  {
    id: 9,
    name: 'CallbackManagement',
    path: '/callback-management',
    component: RoutesAlias.Home,
    meta: {
      title: '回调管理',
      icon: '&#xe614;', // 刷新图标，适合回调管理
      keepAlive: false
    },
    children: [
      {
        id: 901,
        path: 'records',
        name: 'CallbackRecords',
        component: '/callback-management/records/index',
        meta: {
          title: '回调记录',
          keepAlive: true
        }
      },
      {
        id: 902,
        path: 'statistics',
        name: 'CallbackStatistics',
        component: '/callback-management/statistics/index',
        meta: {
          title: '统计监控',
          keepAlive: true
        }
      },
      {
        id: 903,
        path: 'raw-data',
        name: 'RawCallbackManagement',
        component: '/admin/RawCallbackManagement',
        meta: {
          title: '原始回调管理',
          keepAlive: true
        }
      }
    ]
  },
  {
    id: 10,
    path: '/result',
    name: 'Result',
    component: RoutesAlias.Home,
    meta: {
      title: 'menus.result.title',
      icon: '&#xe679;',
      keepAlive: false,
      isHide: true
    },
    children: [
      {
        id: 1001,
        path: 'success',
        name: 'Success',
        component: RoutesAlias.Success,
        meta: {
          title: 'menus.result.success',
          keepAlive: true
        }
      },
      {
        id: 1002,
        path: 'fail',
        name: 'Fail',
        component: RoutesAlias.Fail,
        meta: {
          title: 'menus.result.fail',
          keepAlive: true
        }
      }
    ]
  },
  {
    id: 11,
    name: 'Exception',
    path: '/exception',
    component: RoutesAlias.Home,
    meta: {
      title: 'menus.exception.title',
      icon: '&#xe649;',
      keepAlive: false,
      isHide: true
    },
    children: [
      {
        id: 1101,
        path: '404',
        name: 'Exception404',
        component: RoutesAlias.Exception404,
        meta: {
          title: 'menus.exception.notFound',
          keepAlive: true
        }
      },
      {
        id: 1102,
        path: '500',
        name: 'Exception500',
        component: RoutesAlias.Exception500,
        meta: {
          title: 'menus.exception.serverError',
          keepAlive: true
        }
      }
    ]
  },
  {
    id: 12,
    path: '/safeguard',
    name: 'Safeguard',
    component: RoutesAlias.Home,
    meta: {
      title: 'menus.safeguard.title',
      icon: '&#xe816;',
      keepAlive: false,
      isHide: true
    },
    children: [
      {
        id: 1201,
        path: 'server',
        name: 'Server',
        component: RoutesAlias.Server,
        meta: {
          title: 'menus.safeguard.server',
          keepAlive: true
        }
      }
    ]
  }
]
