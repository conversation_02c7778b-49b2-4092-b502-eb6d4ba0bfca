<template>
  <div class="error-monitor">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>错误监控</span>
          <el-button type="danger" @click="clearErrors">清除错误</el-button>
        </div>
      </template>
      
      <div class="error-stats">
        <el-row :gutter="16">
          <el-col :span="6">
            <el-statistic title="总错误数" :value="totalErrors" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="今日错误" :value="todayErrors" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="严重错误" :value="criticalErrors" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="错误率" :value="errorRate" suffix="%" />
          </el-col>
        </el-row>
      </div>
      
      <div class="error-list">
        <h3>错误列表</h3>
        <el-table :data="errorList" style="width: 100%" max-height="400">
          <el-table-column prop="timestamp" label="时间" width="180" />
          <el-table-column prop="level" label="级别" width="100">
            <template #default="scope">
              <el-tag :type="getLevelType(scope.row.level)">
                {{ scope.row.level }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="message" label="错误信息" />
          <el-table-column prop="source" label="来源" width="150" />
          <el-table-column label="操作" width="100">
            <template #default="scope">
              <el-button size="small" @click="viewError(scope.row)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

// 响应式数据
const totalErrors = ref(0)
const todayErrors = ref(0)
const criticalErrors = ref(0)
const errorRate = ref(0)

const errorList = ref([
  {
    timestamp: '2025-01-29 10:30:00',
    level: 'error',
    message: 'Network request failed',
    source: 'API调用'
  },
  {
    timestamp: '2025-01-29 10:25:00',
    level: 'warning',
    message: 'Component render warning',
    source: '组件渲染'
  }
])

// 获取级别类型
const getLevelType = (level: string) => {
  switch (level) {
    case 'error': return 'danger'
    case 'warning': return 'warning'
    case 'info': return 'info'
    default: return 'info'
  }
}

// 清除错误
const clearErrors = () => {
  errorList.value = []
  totalErrors.value = 0
  todayErrors.value = 0
  criticalErrors.value = 0
  errorRate.value = 0
  ElMessage.success('错误列表已清除')
}

// 查看错误详情
const viewError = (error: any) => {
  ElMessage.info(`查看错误: ${error.message}`)
}

onMounted(() => {
  // 初始化错误统计
  totalErrors.value = errorList.value.length
  todayErrors.value = errorList.value.length
  criticalErrors.value = errorList.value.filter(e => e.level === 'error').length
  errorRate.value = Math.round((criticalErrors.value / totalErrors.value) * 100) || 0
})
</script>

<style scoped>
.error-monitor {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.error-stats {
  margin-bottom: 24px;
}

.error-list {
  margin-top: 24px;
}
</style>
