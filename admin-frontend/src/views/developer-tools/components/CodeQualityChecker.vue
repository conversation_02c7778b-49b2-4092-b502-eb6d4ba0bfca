<template>
  <div class="code-quality-checker">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>代码质量检查</span>
          <el-button type="primary" @click="runCheck">运行检查</el-button>
        </div>
      </template>
      
      <div class="quality-score">
        <el-progress 
          :percentage="qualityScore" 
          :color="getScoreColor(qualityScore)"
          :stroke-width="20"
          text-inside
        />
        <div class="score-text">代码质量评分: {{ qualityScore }}/100</div>
      </div>
      
      <div class="check-results">
        <h3>检查结果</h3>
        <el-table :data="checkResults" style="width: 100%">
          <el-table-column prop="category" label="检查项" width="200" />
          <el-table-column prop="score" label="得分" width="100" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)">
                {{ scope.row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="issues" label="问题数" width="100" />
          <el-table-column prop="suggestion" label="建议" />
        </el-table>
      </div>
      
      <div class="detailed-issues">
        <h3>详细问题</h3>
        <el-collapse>
          <el-collapse-item title="TypeScript 类型检查" name="typescript">
            <div>检查 TypeScript 类型定义和类型安全性</div>
          </el-collapse-item>
          <el-collapse-item title="ESLint 代码规范" name="eslint">
            <div>检查代码风格和潜在问题</div>
          </el-collapse-item>
          <el-collapse-item title="性能优化建议" name="performance">
            <div>分析组件性能和渲染优化建议</div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

// 响应式数据
const qualityScore = ref(85)

const checkResults = ref([
  {
    category: 'TypeScript 类型',
    score: 90,
    status: '良好',
    issues: 2,
    suggestion: '修复少量类型定义问题'
  },
  {
    category: 'ESLint 规范',
    score: 85,
    status: '良好',
    issues: 5,
    suggestion: '修复代码风格问题'
  },
  {
    category: '性能优化',
    score: 80,
    status: '一般',
    issues: 8,
    suggestion: '优化组件渲染性能'
  },
  {
    category: '安全检查',
    score: 95,
    status: '优秀',
    issues: 1,
    suggestion: '安全性良好'
  }
])

// 获取分数颜色
const getScoreColor = (score: number) => {
  if (score >= 90) return '#67c23a'
  if (score >= 70) return '#e6a23c'
  return '#f56c6c'
}

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case '优秀': return 'success'
    case '良好': return 'success'
    case '一般': return 'warning'
    case '差': return 'danger'
    default: return 'info'
  }
}

// 运行检查
const runCheck = () => {
  ElMessage.success('开始代码质量检查...')
  
  // 模拟检查过程
  setTimeout(() => {
    // 随机生成一些结果
    qualityScore.value = Math.round(Math.random() * 30 + 70)
    
    checkResults.value = checkResults.value.map(item => ({
      ...item,
      score: Math.round(Math.random() * 30 + 70),
      issues: Math.round(Math.random() * 10)
    }))
    
    ElMessage.success('代码质量检查完成')
  }, 2000)
}

onMounted(() => {
  // 初始化时运行一次检查
})
</script>

<style scoped>
.code-quality-checker {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.quality-score {
  text-align: center;
  margin-bottom: 24px;
}

.score-text {
  margin-top: 16px;
  font-size: 18px;
  font-weight: bold;
}

.check-results {
  margin-bottom: 24px;
}

.detailed-issues {
  margin-top: 24px;
}
</style>
