<template>
  <div class="performance-analyzer">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>性能分析器</span>
          <el-button type="primary" @click="startAnalysis">开始分析</el-button>
        </div>
      </template>
      
      <div class="metrics-grid">
        <el-card class="metric-card">
          <div class="metric-title">内存使用</div>
          <div class="metric-value">{{ memoryUsage }}MB</div>
        </el-card>
        
        <el-card class="metric-card">
          <div class="metric-title">CPU使用率</div>
          <div class="metric-value">{{ cpuUsage }}%</div>
        </el-card>
        
        <el-card class="metric-card">
          <div class="metric-title">网络延迟</div>
          <div class="metric-value">{{ networkLatency }}ms</div>
        </el-card>
        
        <el-card class="metric-card">
          <div class="metric-title">渲染时间</div>
          <div class="metric-value">{{ renderTime }}ms</div>
        </el-card>
      </div>
      
      <div class="analysis-results">
        <h3>分析结果</h3>
        <el-table :data="analysisData" style="width: 100%">
          <el-table-column prop="metric" label="指标" width="200" />
          <el-table-column prop="value" label="当前值" width="150" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)">
                {{ scope.row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="suggestion" label="建议" />
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

// 响应式数据
const memoryUsage = ref(0)
const cpuUsage = ref(0)
const networkLatency = ref(0)
const renderTime = ref(0)

const analysisData = ref([
  {
    metric: '内存使用',
    value: '0MB',
    status: '正常',
    suggestion: '内存使用在正常范围内'
  },
  {
    metric: 'CPU使用率',
    value: '0%',
    status: '正常',
    suggestion: 'CPU使用率正常'
  }
])

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case '正常': return 'success'
    case '警告': return 'warning'
    case '异常': return 'danger'
    default: return 'info'
  }
}

// 开始性能分析
const startAnalysis = () => {
  ElMessage.success('开始性能分析...')
  
  // 获取内存信息
  if (performance.memory) {
    memoryUsage.value = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024)
  }
  
  // 模拟其他指标
  cpuUsage.value = Math.round(Math.random() * 50)
  networkLatency.value = Math.round(Math.random() * 100 + 50)
  renderTime.value = Math.round(Math.random() * 20 + 10)
  
  // 更新分析数据
  analysisData.value = [
    {
      metric: '内存使用',
      value: `${memoryUsage.value}MB`,
      status: memoryUsage.value > 100 ? '警告' : '正常',
      suggestion: memoryUsage.value > 100 ? '内存使用较高，建议优化' : '内存使用正常'
    },
    {
      metric: 'CPU使用率',
      value: `${cpuUsage.value}%`,
      status: cpuUsage.value > 80 ? '异常' : cpuUsage.value > 60 ? '警告' : '正常',
      suggestion: cpuUsage.value > 80 ? 'CPU使用率过高' : 'CPU使用率正常'
    },
    {
      metric: '网络延迟',
      value: `${networkLatency.value}ms`,
      status: networkLatency.value > 200 ? '异常' : networkLatency.value > 100 ? '警告' : '正常',
      suggestion: networkLatency.value > 200 ? '网络延迟过高' : '网络延迟正常'
    },
    {
      metric: '渲染时间',
      value: `${renderTime.value}ms`,
      status: renderTime.value > 50 ? '警告' : '正常',
      suggestion: renderTime.value > 50 ? '渲染时间较长，建议优化' : '渲染性能良好'
    }
  ]
}

onMounted(() => {
  startAnalysis()
})
</script>

<style scoped>
.performance-analyzer {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.metric-card {
  text-align: center;
  padding: 16px;
}

.metric-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.metric-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
}

.analysis-results {
  margin-top: 24px;
}
</style>
