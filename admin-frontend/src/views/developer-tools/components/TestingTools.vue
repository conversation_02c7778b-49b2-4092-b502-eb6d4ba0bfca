<template>
  <div class="testing-tools">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>测试工具</span>
          <el-button type="primary" @click="runAllTests">运行所有测试</el-button>
        </div>
      </template>
      
      <div class="test-stats">
        <el-row :gutter="16">
          <el-col :span="6">
            <el-statistic title="总测试数" :value="totalTests" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="通过测试" :value="passedTests" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="失败测试" :value="failedTests" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="覆盖率" :value="coverage" suffix="%" />
          </el-col>
        </el-row>
      </div>
      
      <div class="test-suites">
        <h3>测试套件</h3>
        <el-table :data="testSuites" style="width: 100%">
          <el-table-column prop="name" label="测试套件" width="200" />
          <el-table-column prop="tests" label="测试数" width="100" />
          <el-table-column prop="passed" label="通过" width="100" />
          <el-table-column prop="failed" label="失败" width="100" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)">
                {{ scope.row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="duration" label="耗时" width="100" />
          <el-table-column label="操作" width="150">
            <template #default="scope">
              <el-button size="small" @click="runSuite(scope.row)">运行</el-button>
              <el-button size="small" type="info" @click="viewDetails(scope.row)">详情</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      
      <div class="test-output">
        <h3>测试输出</h3>
        <el-input
          v-model="testOutput"
          type="textarea"
          :rows="10"
          readonly
          placeholder="测试输出将显示在这里..."
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

// 响应式数据
const totalTests = ref(0)
const passedTests = ref(0)
const failedTests = ref(0)
const coverage = ref(0)
const testOutput = ref('')

const testSuites = ref([
  {
    name: 'Utils Tests',
    tests: 15,
    passed: 14,
    failed: 1,
    status: '部分通过',
    duration: '2.3s'
  },
  {
    name: 'Component Tests',
    tests: 8,
    passed: 8,
    failed: 0,
    status: '全部通过',
    duration: '1.8s'
  },
  {
    name: 'API Tests',
    tests: 12,
    passed: 10,
    failed: 2,
    status: '部分通过',
    duration: '3.1s'
  }
])

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case '全部通过': return 'success'
    case '部分通过': return 'warning'
    case '全部失败': return 'danger'
    default: return 'info'
  }
}

// 运行所有测试
const runAllTests = () => {
  ElMessage.success('开始运行所有测试...')
  testOutput.value = '正在运行测试...\n'
  
  setTimeout(() => {
    testOutput.value += '✓ Utils Tests: 14/15 passed\n'
    testOutput.value += '✓ Component Tests: 8/8 passed\n'
    testOutput.value += '✗ API Tests: 10/12 passed\n'
    testOutput.value += '\n测试完成！总计: 32/35 passed\n'
    
    // 更新统计
    updateStats()
    ElMessage.success('测试运行完成')
  }, 3000)
}

// 运行单个测试套件
const runSuite = (suite: any) => {
  ElMessage.success(`运行测试套件: ${suite.name}`)
  testOutput.value += `\n运行 ${suite.name}...\n`
  
  setTimeout(() => {
    testOutput.value += `✓ ${suite.name}: ${suite.passed}/${suite.tests} passed\n`
  }, 1000)
}

// 查看测试详情
const viewDetails = (suite: any) => {
  ElMessage.info(`查看测试详情: ${suite.name}`)
}

// 更新统计数据
const updateStats = () => {
  totalTests.value = testSuites.value.reduce((sum, suite) => sum + suite.tests, 0)
  passedTests.value = testSuites.value.reduce((sum, suite) => sum + suite.passed, 0)
  failedTests.value = testSuites.value.reduce((sum, suite) => sum + suite.failed, 0)
  coverage.value = Math.round((passedTests.value / totalTests.value) * 100)
}

onMounted(() => {
  updateStats()
})
</script>

<style scoped>
.testing-tools {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.test-stats {
  margin-bottom: 24px;
}

.test-suites {
  margin-bottom: 24px;
}

.test-output {
  margin-top: 24px;
}
</style>
