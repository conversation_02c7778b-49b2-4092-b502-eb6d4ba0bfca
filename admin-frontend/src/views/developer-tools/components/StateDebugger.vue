<template>
  <div class="state-debugger">
    <!-- 控制面板 -->
    <div class="control-panel">
      <div class="panel-section">
        <h4>状态控制</h4>
        <div class="control-buttons">
          <el-button size="small" @click="createSnapshot">
            <el-icon><Camera /></el-icon>
            创建快照
          </el-button>
          <el-button size="small" @click="toggleRecording">
            <el-icon><VideoPlay v-if="!isRecording" /><VideoPause v-else /></el-icon>
            {{ isRecording ? '暂停记录' : '开始记录' }}
          </el-button>
          <el-button size="small" @click="clearHistory" type="danger">
            <el-icon><Delete /></el-icon>
            清理历史
          </el-button>
          <el-button size="small" @click="exportState">
            <el-icon><Download /></el-icon>
            导出状态
          </el-button>
        </div>
      </div>
      
      <div class="panel-section">
        <h4>性能指标</h4>
        <div class="metrics-grid">
          <div class="metric-item">
            <span class="metric-label">总变更</span>
            <span class="metric-value">{{ metrics.totalChanges }}</span>
          </div>
          <div class="metric-item">
            <span class="metric-label">平均耗时</span>
            <span class="metric-value">{{ metrics.averageChangeTime.toFixed(2) }}ms</span>
          </div>
          <div class="metric-item">
            <span class="metric-label">Store数量</span>
            <span class="metric-value">{{ metrics.storeCount }}</span>
          </div>
          <div class="metric-item">
            <span class="metric-label">内存使用</span>
            <span class="metric-value">{{ metrics.memoryUsage }}MB</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <el-tabs v-model="activeTab">
        <!-- 变更历史 -->
        <el-tab-pane label="变更历史" name="history">
          <div class="history-section">
            <div class="section-header">
              <h4>状态变更历史</h4>
              <div class="filter-controls">
                <el-select v-model="historyFilter.store" placeholder="筛选Store" clearable size="small">
                  <el-option
                    v-for="store in availableStores"
                    :key="store"
                    :label="store"
                    :value="store"
                  />
                </el-select>
                <el-input
                  v-model="historyFilter.action"
                  placeholder="筛选Action"
                  clearable
                  size="small"
                  style="width: 150px; margin-left: 8px;"
                />
              </div>
            </div>
            
            <div class="history-list">
              <div
                v-for="change in filteredHistory"
                :key="change.id"
                class="history-item"
                :class="{ 'selected': selectedChange?.id === change.id }"
                @click="selectChange(change)"
              >
                <div class="change-header">
                  <span class="store-name">{{ change.storeName }}</span>
                  <span class="action-name">{{ change.action }}</span>
                  <span class="timestamp">{{ formatTime(change.timestamp) }}</span>
                  <span class="duration">{{ change.duration.toFixed(2) }}ms</span>
                </div>
                <div class="change-preview">
                  {{ getChangePreview(change) }}
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 快照管理 -->
        <el-tab-pane label="快照管理" name="snapshots">
          <div class="snapshots-section">
            <div class="section-header">
              <h4>状态快照</h4>
              <el-button size="small" @click="createSnapshot">
                <el-icon><Plus /></el-icon>
                新建快照
              </el-button>
            </div>
            
            <div class="snapshots-list">
              <div
                v-for="snapshot in snapshots"
                :key="snapshot.id"
                class="snapshot-item"
              >
                <div class="snapshot-header">
                  <span class="snapshot-id">{{ snapshot.id }}</span>
                  <span class="snapshot-time">{{ formatTime(snapshot.timestamp) }}</span>
                  <div class="snapshot-actions">
                    <el-button size="small" @click="restoreSnapshot(snapshot.id)">
                      <el-icon><RefreshLeft /></el-icon>
                      恢复
                    </el-button>
                    <el-button size="small" @click="compareSnapshot(snapshot)">
                      <el-icon><View /></el-icon>
                      对比
                    </el-button>
                  </div>
                </div>
                <div class="snapshot-description">
                  {{ snapshot.description }}
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 状态详情 -->
        <el-tab-pane label="状态详情" name="details">
          <div class="details-section">
            <div v-if="selectedChange" class="change-details">
              <h4>变更详情</h4>
              <div class="detail-grid">
                <div class="detail-item">
                  <label>Store:</label>
                  <span>{{ selectedChange.storeName }}</span>
                </div>
                <div class="detail-item">
                  <label>Action:</label>
                  <span>{{ selectedChange.action }}</span>
                </div>
                <div class="detail-item">
                  <label>时间:</label>
                  <span>{{ formatTime(selectedChange.timestamp) }}</span>
                </div>
                <div class="detail-item">
                  <label>耗时:</label>
                  <span>{{ selectedChange.duration.toFixed(2) }}ms</span>
                </div>
              </div>
              
              <div class="state-diff">
                <div class="diff-section">
                  <h5>变更前状态</h5>
                  <pre class="state-json">{{ JSON.stringify(selectedChange.previousState, null, 2) }}</pre>
                </div>
                <div class="diff-section">
                  <h5>变更后状态</h5>
                  <pre class="state-json">{{ JSON.stringify(selectedChange.newState, null, 2) }}</pre>
                </div>
              </div>
              
              <div class="time-travel-section">
                <h5>时间旅行</h5>
                <el-button @click="timeTravel(selectedChange.id)">
                  <el-icon><Back /></el-icon>
                  回滚到此状态
                </el-button>
              </div>
            </div>
            
            <div v-else class="no-selection">
              <el-empty description="请选择一个变更记录查看详情" />
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Camera, VideoPlay, VideoPause, Delete, Download, Plus,
  RefreshLeft, View, Back
} from '@element-plus/icons-vue'
import { useStateManager } from '@/store/enhancers/stateManager'

// 使用状态管理器
const {
  createSnapshot,
  restoreSnapshot,
  timeTravel,
  getChangeHistory,
  getSnapshots,
  getMetrics,
  clearHistory,
  exportState,
  toggleRecording
} = useStateManager()

// 响应式数据
const activeTab = ref('history')
const selectedChange = ref<any>(null)
const isRecording = ref(true)

// 筛选条件
const historyFilter = reactive({
  store: '',
  action: ''
})

// 获取数据
const history = ref<any[]>([])
const snapshots = ref<any[]>([])
const metrics = ref<any>({
  totalChanges: 0,
  averageChangeTime: 0,
  storeCount: 0,
  memoryUsage: 0
})

// 计算属性
const availableStores = computed(() => {
  const stores = new Set(history.value.map(h => h.storeName))
  return Array.from(stores)
})

const filteredHistory = computed(() => {
  return history.value.filter(change => {
    if (historyFilter.store && change.storeName !== historyFilter.store) {
      return false
    }
    if (historyFilter.action && !change.action.includes(historyFilter.action)) {
      return false
    }
    return true
  })
})

// 方法
const refreshData = () => {
  history.value = getChangeHistory(100)
  snapshots.value = getSnapshots()
  metrics.value = getMetrics()
}

const selectChange = (change: any) => {
  selectedChange.value = change
  activeTab.value = 'details'
}

const formatTime = (timestamp: number) => {
  return new Date(timestamp).toLocaleTimeString()
}

const getChangePreview = (change: any) => {
  const payload = change.payload
  if (payload && typeof payload === 'object') {
    return JSON.stringify(payload).substring(0, 100) + '...'
  }
  return change.action
}

const handleCreateSnapshot = async () => {
  try {
    const { value: description } = await ElMessageBox.prompt(
      '请输入快照描述',
      '创建快照',
      {
        confirmButtonText: '创建',
        cancelButtonText: '取消',
        inputPlaceholder: '快照描述'
      }
    )
    
    const snapshotId = createSnapshot(description || '手动快照')
    ElMessage.success(`快照已创建: ${snapshotId}`)
    refreshData()
  } catch {
    // 用户取消
  }
}

const handleRestoreSnapshot = async (snapshotId: string) => {
  try {
    await ElMessageBox.confirm(
      '恢复快照将覆盖当前状态，确定要继续吗？',
      '确认恢复',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const success = restoreSnapshot(snapshotId)
    if (success) {
      ElMessage.success('快照恢复成功')
      refreshData()
    } else {
      ElMessage.error('快照恢复失败')
    }
  } catch {
    // 用户取消
  }
}

const handleTimeTravel = async (changeId: string) => {
  try {
    await ElMessageBox.confirm(
      '时间旅行将回滚到指定状态，确定要继续吗？',
      '确认回滚',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const success = timeTravel(changeId)
    if (success) {
      ElMessage.success('时间旅行成功')
      refreshData()
    } else {
      ElMessage.error('时间旅行失败')
    }
  } catch {
    // 用户取消
  }
}

const handleToggleRecording = () => {
  isRecording.value = toggleRecording()
  ElMessage.info(`状态记录已${isRecording.value ? '开启' : '关闭'}`)
}

const handleClearHistory = async () => {
  try {
    await ElMessageBox.confirm(
      '清理历史将删除所有变更记录和快照，确定要继续吗？',
      '确认清理',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    clearHistory()
    ElMessage.success('历史记录已清理')
    refreshData()
    selectedChange.value = null
  } catch {
    // 用户取消
  }
}

const handleExportState = () => {
  try {
    const stateData = exportState()
    const blob = new Blob([stateData], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `state-export-${Date.now()}.json`
    a.click()
    URL.revokeObjectURL(url)
    
    ElMessage.success('状态数据已导出')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

const compareSnapshot = (snapshot: any) => {
  // TODO: 实现快照对比功能
  ElMessage.info('快照对比功能开发中')
}

// 生命周期
onMounted(() => {
  refreshData()
  
  // 定期刷新数据
  setInterval(refreshData, 2000)
})
</script>

<style scoped lang="scss">
.state-debugger {
  .control-panel {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    
    .panel-section {
      margin-bottom: 20px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      h4 {
        margin: 0 0 12px 0;
        color: #374151;
        font-size: 16px;
      }
    }
    
    .control-buttons {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }
    
    .metrics-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 16px;
      
      .metric-item {
        text-align: center;
        
        .metric-label {
          display: block;
          font-size: 12px;
          color: #6b7280;
          margin-bottom: 4px;
        }
        
        .metric-value {
          display: block;
          font-size: 18px;
          font-weight: 600;
          color: #1f2937;
        }
      }
    }
  }
  
  .main-content {
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    
    :deep(.el-tabs__content) {
      padding: 20px;
    }
  }
  
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    h4 {
      margin: 0;
      color: #374151;
    }
    
    .filter-controls {
      display: flex;
      align-items: center;
    }
  }
  
  .history-list {
    max-height: 400px;
    overflow-y: auto;
    
    .history-item {
      padding: 12px;
      border: 1px solid #e5e7eb;
      border-radius: 6px;
      margin-bottom: 8px;
      cursor: pointer;
      transition: all 0.2s;
      
      &:hover {
        background: #f9fafb;
        border-color: #d1d5db;
      }
      
      &.selected {
        background: #eff6ff;
        border-color: #3b82f6;
      }
      
      .change-header {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 4px;
        
        .store-name {
          font-weight: 600;
          color: #1f2937;
        }
        
        .action-name {
          color: #3b82f6;
          font-family: monospace;
        }
        
        .timestamp {
          color: #6b7280;
          font-size: 12px;
        }
        
        .duration {
          color: #059669;
          font-size: 12px;
          margin-left: auto;
        }
      }
      
      .change-preview {
        font-size: 12px;
        color: #6b7280;
        font-family: monospace;
      }
    }
  }
  
  .snapshots-list {
    .snapshot-item {
      padding: 16px;
      border: 1px solid #e5e7eb;
      border-radius: 6px;
      margin-bottom: 12px;
      
      .snapshot-header {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 8px;
        
        .snapshot-id {
          font-weight: 600;
          color: #1f2937;
          font-family: monospace;
        }
        
        .snapshot-time {
          color: #6b7280;
          font-size: 12px;
        }
        
        .snapshot-actions {
          margin-left: auto;
          display: flex;
          gap: 8px;
        }
      }
      
      .snapshot-description {
        color: #6b7280;
        font-size: 14px;
      }
    }
  }
  
  .change-details {
    .detail-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 12px;
      margin-bottom: 20px;
      
      .detail-item {
        display: flex;
        align-items: center;
        
        label {
          font-weight: 600;
          color: #374151;
          margin-right: 8px;
          min-width: 60px;
        }
        
        span {
          color: #6b7280;
          font-family: monospace;
        }
      }
    }
    
    .state-diff {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
      margin-bottom: 20px;
      
      .diff-section {
        h5 {
          margin: 0 0 8px 0;
          color: #374151;
        }
        
        .state-json {
          background: #f8fafc;
          border: 1px solid #e2e8f0;
          border-radius: 4px;
          padding: 12px;
          font-size: 12px;
          max-height: 300px;
          overflow-y: auto;
          margin: 0;
        }
      }
    }
    
    .time-travel-section {
      h5 {
        margin: 0 0 12px 0;
        color: #374151;
      }
    }
  }
  
  .no-selection {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .state-debugger {
    .metrics-grid {
      grid-template-columns: repeat(2, 1fr);
    }
    
    .state-diff {
      grid-template-columns: 1fr;
    }
    
    .detail-grid {
      grid-template-columns: 1fr;
    }
  }
}
</style>
