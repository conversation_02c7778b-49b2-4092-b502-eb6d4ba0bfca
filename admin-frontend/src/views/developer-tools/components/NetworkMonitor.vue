<template>
  <div class="network-monitor">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>网络监控</span>
          <el-button type="primary" @click="refreshData">刷新数据</el-button>
        </div>
      </template>
      
      <div class="network-stats">
        <el-row :gutter="16">
          <el-col :span="6">
            <el-statistic title="总请求数" :value="totalRequests" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="成功请求" :value="successRequests" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="失败请求" :value="failedRequests" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="平均延迟" :value="averageLatency" suffix="ms" />
          </el-col>
        </el-row>
      </div>
      
      <div class="request-list">
        <h3>请求列表</h3>
        <el-table :data="requestList" style="width: 100%" max-height="400">
          <el-table-column prop="timestamp" label="时间" width="180" />
          <el-table-column prop="method" label="方法" width="80" />
          <el-table-column prop="url" label="URL" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)">
                {{ scope.row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="duration" label="耗时" width="100" />
          <el-table-column label="操作" width="100">
            <template #default="scope">
              <el-button size="small" @click="viewRequest(scope.row)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

// 响应式数据
const totalRequests = ref(0)
const successRequests = ref(0)
const failedRequests = ref(0)
const averageLatency = ref(0)

const requestList = ref([
  {
    timestamp: '2025-01-29 10:30:00',
    method: 'GET',
    url: '/api/v1/users',
    status: 200,
    duration: '120ms'
  },
  {
    timestamp: '2025-01-29 10:29:30',
    method: 'POST',
    url: '/api/v1/orders',
    status: 201,
    duration: '250ms'
  },
  {
    timestamp: '2025-01-29 10:29:00',
    method: 'GET',
    url: '/api/v1/balance',
    status: 500,
    duration: '5000ms'
  }
])

// 获取状态类型
const getStatusType = (status: number) => {
  if (status >= 200 && status < 300) return 'success'
  if (status >= 400 && status < 500) return 'warning'
  if (status >= 500) return 'danger'
  return 'info'
}

// 刷新数据
const refreshData = () => {
  // 计算统计数据
  totalRequests.value = requestList.value.length
  successRequests.value = requestList.value.filter(r => r.status >= 200 && r.status < 300).length
  failedRequests.value = requestList.value.filter(r => r.status >= 400).length
  
  // 计算平均延迟
  const totalLatency = requestList.value.reduce((sum, req) => {
    const latency = parseInt(req.duration.replace('ms', ''))
    return sum + latency
  }, 0)
  averageLatency.value = Math.round(totalLatency / requestList.value.length)
  
  ElMessage.success('数据已刷新')
}

// 查看请求详情
const viewRequest = (request: any) => {
  ElMessage.info(`查看请求: ${request.method} ${request.url}`)
}

onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.network-monitor {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.network-stats {
  margin-bottom: 24px;
}

.request-list {
  margin-top: 24px;
}
</style>
