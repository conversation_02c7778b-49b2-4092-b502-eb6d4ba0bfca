<template>
  <div class="developer-tools">
    <div class="page-header">
      <h2>开发者工具</h2>
      <p class="page-description">代码质量检查、性能分析和调试工具</p>
    </div>

    <!-- 工具导航 -->
    <el-tabs v-model="activeTab" class="tools-tabs">
      <!-- 状态管理调试 -->
      <el-tab-pane label="状态管理" name="state">
        <StateDebugger />
      </el-tab-pane>

      <!-- 性能分析 -->
      <el-tab-pane label="性能分析" name="performance">
        <PerformanceAnalyzer />
      </el-tab-pane>

      <!-- 错误监控 -->
      <el-tab-pane label="错误监控" name="errors">
        <ErrorMonitor />
      </el-tab-pane>

      <!-- 网络监控 -->
      <el-tab-pane label="网络监控" name="network">
        <NetworkMonitor />
      </el-tab-pane>

      <!-- 代码质量 -->
      <el-tab-pane label="代码质量" name="quality">
        <CodeQualityChecker />
      </el-tab-pane>

      <!-- 测试工具 -->
      <el-tab-pane label="测试工具" name="testing">
        <TestingTools />
      </el-tab-pane>
    </el-tabs>

    <!-- 浮动工具栏 -->
    <div class="floating-toolbar" :class="{ 'collapsed': toolbarCollapsed }">
      <div class="toolbar-header" @click="toolbarCollapsed = !toolbarCollapsed">
        <span>开发工具</span>
        <el-icon>
          <ArrowUp v-if="!toolbarCollapsed" />
          <ArrowDown v-else />
        </el-icon>
      </div>
      
      <div v-if="!toolbarCollapsed" class="toolbar-content">
        <div class="quick-stats">
          <div class="stat-item">
            <span class="stat-label">内存</span>
            <span class="stat-value">{{ memoryUsage }}MB</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">错误</span>
            <span class="stat-value error">{{ errorCount }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">请求</span>
            <span class="stat-value">{{ activeRequests }}</span>
          </div>
        </div>
        
        <div class="quick-actions">
          <el-button size="small" @click="clearCache">
            <el-icon><Delete /></el-icon>
            清理缓存
          </el-button>
          <el-button size="small" @click="exportLogs">
            <el-icon><Download /></el-icon>
            导出日志
          </el-button>
          <el-button size="small" @click="runHealthCheck">
            <el-icon><CircleCheck /></el-icon>
            健康检查
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { ArrowUp, ArrowDown, Delete, Download, CircleCheck } from '@element-plus/icons-vue'
import StateDebugger from './components/StateDebugger.vue'
import PerformanceAnalyzer from './components/PerformanceAnalyzer.vue'
import ErrorMonitor from './components/ErrorMonitor.vue'
import NetworkMonitor from './components/NetworkMonitor.vue'
import CodeQualityChecker from './components/CodeQualityChecker.vue'
import TestingTools from './components/TestingTools.vue'
import { useTimerManager } from '@/utils/timerManager'
import { useHttpClient } from '@/utils/httpClient'
import { useErrorTracker } from '@/utils/monitoring/errorTracker'
import { usePerformanceMonitor } from '@/utils/monitoring/performanceMonitor'

// 响应式数据
const activeTab = ref('state')
const toolbarCollapsed = ref(false)

// 快速统计数据
const quickStats = reactive({
  memoryUsage: 0,
  errorCount: 0,
  activeRequests: 0,
  cacheSize: 0
})

// 使用工具
const { createTimer } = useTimerManager('developer-tools')
const { getStats: getHttpStats, clearCache } = useHttpClient()
const { getStats: getErrorStats } = useErrorTracker()
const { getMetrics } = usePerformanceMonitor()

// 计算属性
const memoryUsage = computed(() => quickStats.memoryUsage)
const errorCount = computed(() => quickStats.errorCount)
const activeRequests = computed(() => quickStats.activeRequests)

// 更新快速统计
const updateQuickStats = () => {
  try {
    // 更新内存使用
    if (performance.memory) {
      quickStats.memoryUsage = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024)
    }

    // 更新错误统计
    const errorStats = getErrorStats()
    quickStats.errorCount = errorStats.total

    // 更新请求统计
    const httpStats = getHttpStats()
    quickStats.activeRequests = httpStats.requests.activeRequests

    // 更新缓存大小
    quickStats.cacheSize = httpStats.cache.totalSize
  } catch (error) {
    console.error('更新快速统计失败:', error)
  }
}

// 清理缓存
const handleClearCache = async () => {
  try {
    clearCache()
    ElMessage.success('缓存已清理')
    updateQuickStats()
  } catch (error) {
    ElMessage.error('清理缓存失败')
  }
}

// 导出日志
const exportLogs = () => {
  try {
    const logs = {
      timestamp: new Date().toISOString(),
      performance: getMetrics(),
      errors: getErrorStats(),
      http: getHttpStats(),
      memory: performance.memory ? {
        used: performance.memory.usedJSHeapSize,
        total: performance.memory.totalJSHeapSize,
        limit: performance.memory.jsHeapSizeLimit
      } : null
    }

    const blob = new Blob([JSON.stringify(logs, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `dev-logs-${Date.now()}.json`
    a.click()
    URL.revokeObjectURL(url)

    ElMessage.success('日志已导出')
  } catch (error) {
    ElMessage.error('导出日志失败')
  }
}

// 运行健康检查
const runHealthCheck = async () => {
  try {
    const checks = [
      { name: '内存使用', check: () => quickStats.memoryUsage < 200 },
      { name: '错误数量', check: () => quickStats.errorCount < 10 },
      { name: '活跃请求', check: () => quickStats.activeRequests < 20 },
      { name: '缓存大小', check: () => quickStats.cacheSize < 500 }
    ]

    const results = checks.map(({ name, check }) => ({
      name,
      passed: check(),
      status: check() ? '✅' : '❌'
    }))

    const allPassed = results.every(r => r.passed)
    const message = results.map(r => `${r.status} ${r.name}`).join('\n')

    ElMessage({
      type: allPassed ? 'success' : 'warning',
      message: `健康检查${allPassed ? '通过' : '发现问题'}:\n${message}`,
      duration: 5000,
      showClose: true
    })
  } catch (error) {
    ElMessage.error('健康检查失败')
  }
}

// 生命周期
onMounted(() => {
  updateQuickStats()
  
  // 定期更新统计数据
  createTimer({
    id: 'quick-stats-update',
    type: 'interval',
    delay: 2000,
    pauseWhenHidden: true,
    callback: updateQuickStats
  })

  console.log('🛠️ 开发者工具已加载')
})

// 键盘快捷键
const handleKeydown = (event: KeyboardEvent) => {
  // Ctrl/Cmd + Shift + D 切换工具栏
  if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'D') {
    event.preventDefault()
    toolbarCollapsed.value = !toolbarCollapsed.value
  }
  
  // Ctrl/Cmd + Shift + C 清理缓存
  if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'C') {
    event.preventDefault()
    handleClearCache()
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped lang="scss">
.developer-tools {
  padding: 20px;
  min-height: 100vh;
  background: #f8fafc;
  
  .page-header {
    margin-bottom: 24px;
    
    h2 {
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 600;
      color: #1f2937;
    }
    
    .page-description {
      margin: 0;
      color: #6b7280;
      font-size: 14px;
    }
  }
  
  .tools-tabs {
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    
    :deep(.el-tabs__header) {
      margin: 0;
      padding: 0 20px;
      background: #f8fafc;
      border-radius: 8px 8px 0 0;
    }
    
    :deep(.el-tabs__content) {
      padding: 20px;
    }
  }
}

.floating-toolbar {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  min-width: 200px;
  transition: all 0.3s ease;
  
  &.collapsed {
    .toolbar-content {
      display: none;
    }
  }
  
  .toolbar-header {
    padding: 12px 16px;
    background: #4f46e5;
    color: white;
    border-radius: 8px 8px 0 0;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
    
    &:hover {
      background: #4338ca;
    }
  }
  
  .toolbar-content {
    padding: 16px;
  }
  
  .quick-stats {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 12px;
    margin-bottom: 16px;
    
    .stat-item {
      text-align: center;
      
      .stat-label {
        display: block;
        font-size: 12px;
        color: #6b7280;
        margin-bottom: 4px;
      }
      
      .stat-value {
        display: block;
        font-size: 16px;
        font-weight: 600;
        color: #1f2937;
        
        &.error {
          color: #ef4444;
        }
      }
    }
  }
  
  .quick-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
    
    .el-button {
      width: 100%;
      justify-content: flex-start;
      
      .el-icon {
        margin-right: 8px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .developer-tools {
    padding: 16px;
  }
  
  .floating-toolbar {
    bottom: 10px;
    right: 10px;
    min-width: 180px;
    
    .quick-stats {
      grid-template-columns: 1fr 1fr;
      gap: 8px;
    }
  }
}

// 暗色主题支持
@media (prefers-color-scheme: dark) {
  .developer-tools {
    background: #111827;
    color: #f9fafb;
    
    .page-header h2 {
      color: #f9fafb;
    }
    
    .tools-tabs {
      background: #1f2937;
      
      :deep(.el-tabs__header) {
        background: #111827;
      }
    }
  }
  
  .floating-toolbar {
    background: #1f2937;
    color: #f9fafb;
    
    .stat-item .stat-value {
      color: #f9fafb;
    }
  }
}
</style>
