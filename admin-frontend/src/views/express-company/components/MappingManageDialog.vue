<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`${company?.name} - 供应商映射管理`"
    width="1000px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <!-- 操作栏 -->
    <div class="action-bar">
      <el-button type="primary" :icon="Plus" @click="handleCreate">
        新增映射
      </el-button>
      <el-button :icon="Refresh" @click="loadMappings">
        刷新
      </el-button>
      <el-button 
        type="warning" 
        :icon="Operation" 
        :loading="reloadingProviders"
        @click="handleReloadAllProviders"
      >
        重载所有供应商
      </el-button>
    </div>

    <!-- 映射列表 -->
    <el-table
      v-loading="loading"
      :data="mappingList"
      stripe
      border
      style="width: 100%"
    >
      <el-table-column label="供应商" width="120">
        <template #default="{ row }">
          {{ getProviderName(row.provider_id) }}
        </template>
      </el-table-column>
      <el-table-column prop="provider_company_code" label="供应商快递代码" width="150" />
      <el-table-column label="是否支持" width="120">
        <template #default="{ row }">
          <el-switch
            v-model="row.is_supported"
            :loading="row._switching"
            active-text="支持"
            inactive-text="不支持"
            @change="handleToggleSupport(row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="是否首选" width="100">
        <template #default="{ row }">
          <el-tag v-if="row.is_preferred" type="warning">首选</el-tag>
          <span v-else class="text-gray-400">-</span>
        </template>
      </el-table-column>
      <el-table-column prop="weight_limit_kg" label="重量限制(kg)" width="120">
        <template #default="{ row }">
          {{ row.weight_limit_kg || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="尺寸限制(cm)" width="150">
        <template #default="{ row }">
          <span v-if="row.size_limit_cm_length">
            {{ row.size_limit_cm_length }}×{{ row.size_limit_cm_width }}×{{ row.size_limit_cm_height }}
          </span>
          <span v-else class="text-gray-400">-</span>
        </template>
      </el-table-column>
      <el-table-column label="支持服务" min-width="150">
        <template #default="{ row }">
          <el-tag
            v-for="service in row.supported_services"
            :key="service"
            size="small"
            style="margin-right: 4px; margin-bottom: 4px"
          >
            {{ service }}
          </el-tag>
          <span v-if="!row.supported_services?.length" class="text-gray-400">-</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" link size="small" @click="handleEdit(row)">
            编辑
          </el-button>
          <el-button 
            type="warning" 
            link 
            size="small" 
            :loading="row._reloading"
            @click="handleReloadProvider(row)"
          >
            重载
          </el-button>
          <el-button type="danger" link size="small" @click="handleDelete(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 映射编辑对话框 -->
    <el-dialog
      v-model="showMappingEditDialog"
      :title="isEditMapping ? '编辑映射关系' : '新增映射关系'"
      width="600px"
      :close-on-click-modal="false"
      append-to-body
    >
      <el-form
        ref="mappingFormRef"
        :model="mappingFormData"
        :rules="mappingFormRules"
        label-width="140px"
      >
        <el-form-item label="供应商" prop="provider_id">
          <el-select
            v-model="mappingFormData.provider_id"
            placeholder="请选择供应商"
            style="width: 100%"
            :disabled="isEditMapping"
          >
            <el-option
              v-for="provider in providerList"
              :key="provider.id"
              :label="provider.name"
              :value="provider.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="供应商快递代码" prop="provider_company_code">
          <el-input
            v-model="mappingFormData.provider_company_code"
            placeholder="请输入供应商的快递公司代码"
            maxlength="50"
          />
          <div class="form-tip">供应商系统中对应的快递公司代码</div>
        </el-form-item>

        <el-form-item label="是否支持" prop="is_supported">
          <el-switch
            v-model="mappingFormData.is_supported"
            active-text="支持"
            inactive-text="不支持"
          />
        </el-form-item>

        <el-form-item label="是否首选" prop="is_preferred">
          <el-switch
            v-model="mappingFormData.is_preferred"
            active-text="首选"
            inactive-text="普通"
          />
          <div class="form-tip">首选供应商会优先使用</div>
        </el-form-item>

        <el-form-item label="重量限制(kg)" prop="weight_limit_kg">
          <el-input-number
            v-model="mappingFormData.weight_limit_kg"
            :min="0"
            :max="1000"
            :precision="2"
            placeholder="重量限制"
            style="width: 200px"
          />
        </el-form-item>

        <el-form-item label="长度限制(cm)" prop="size_limit_cm_length">
          <el-input-number
            v-model="mappingFormData.size_limit_cm_length"
            :min="0"
            :max="1000"
            placeholder="长度"
            style="width: 150px"
          />
        </el-form-item>

        <el-form-item label="宽度限制(cm)" prop="size_limit_cm_width">
          <el-input-number
            v-model="mappingFormData.size_limit_cm_width"
            :min="0"
            :max="1000"
            placeholder="宽度"
            style="width: 150px"
          />
        </el-form-item>

        <el-form-item label="高度限制(cm)" prop="size_limit_cm_height">
          <el-input-number
            v-model="mappingFormData.size_limit_cm_height"
            :min="0"
            :max="1000"
            placeholder="高度"
            style="width: 150px"
          />
        </el-form-item>

        <el-form-item label="支持服务" prop="supported_services">
          <el-select
            v-model="mappingFormData.supported_services"
            multiple
            placeholder="请选择支持的服务"
            style="width: 100%"
          >
            <el-option label="标准快递" value="standard" />
            <el-option label="特快专递" value="express" />
            <el-option label="次日达" value="next_day" />
            <el-option label="当日达" value="same_day" />
            <el-option label="代收货款" value="cod" />
            <el-option label="签收回单" value="receipt" />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showMappingEditDialog = false">取消</el-button>
          <el-button type="primary" :loading="submittingMapping" @click="handleSubmitMapping">
            {{ isEditMapping ? '更新' : '创建' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { Plus, Refresh, Operation } from '@element-plus/icons-vue'
import {
  ExpressCompanyApi,
  type ExpressCompany,
  type ExpressProvider,
  type ExpressCompanyProviderMapping,
  type CreateExpressMappingRequest,
  type UpdateExpressMappingRequest,
  type StatusUpdateRequest
} from '@/api/expressCompanyApi'

// ==================== Props & Emits ====================

interface Props {
  visible: boolean
  company?: ExpressCompany | null
}

interface Emits {
  (e: 'update:visible', visible: boolean): void
  (e: 'success'): void
}

const props = withDefaults(defineProps<Props>(), {
  company: null
})

const emit = defineEmits<Emits>()

// ==================== 响应式数据 ====================

const loading = ref(false)
const reloadingProviders = ref(false)
const mappingList = ref<ExpressCompanyProviderMapping[]>([])
const providerList = ref<ExpressProvider[]>([])
const company = ref<ExpressCompany | null>(null)

// 映射编辑对话框
const showMappingEditDialog = ref(false)
const submittingMapping = ref(false)
const mappingFormRef = ref<FormInstance>()
const currentMapping = ref<ExpressCompanyProviderMapping | null>(null)

// 映射表单数据
const mappingFormData = reactive<CreateExpressMappingRequest>({
  company_id: '',
  provider_id: '',
  provider_company_code: '',
  is_supported: true,
  is_preferred: false,
  weight_limit_kg: undefined,
  size_limit_cm_length: undefined,
  size_limit_cm_width: undefined,
  size_limit_cm_height: undefined,
  supported_services: []
})

// 映射表单验证规则
const mappingFormRules: FormRules = {
  provider_id: [
    { required: true, message: '请选择供应商', trigger: 'change' }
  ],
  provider_company_code: [
    { required: true, message: '请输入供应商快递代码', trigger: 'blur' },
    { max: 50, message: '代码长度不能超过50个字符', trigger: 'blur' }
  ]
}

// ==================== 计算属性 ====================

// 弹窗显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value: boolean) => {
    if (!value) {
      emit('update:visible', false)
    }
  }
})

const isEditMapping = computed(() => !!currentMapping.value?.id)

// ==================== 监听器 ====================

watch(
  () => props.visible,
  (visible) => {
    if (visible && props.company) {
      loadMappings()
      loadProviders()
    }
  }
)

// ==================== 生命周期 ====================

onMounted(() => {
  loadProviders()
})

// ==================== 方法 ====================

/**
 * 加载映射关系列表
 */
const loadMappings = async () => {
  if (!props.company?.id) return

  try {
    loading.value = true
    const response = await ExpressCompanyApi.getMappingsByCompany(props.company.id)

    if (response.success) {
      mappingList.value = response.data
    } else {
      ElMessage.error(response.message || '获取映射关系失败')
    }
  } catch (error) {
    console.error('Load mappings error:', error)
    ElMessage.error('获取映射关系失败')
  } finally {
    loading.value = false
  }
}

/**
 * 加载供应商列表
 */
const loadProviders = async () => {
  try {
    const response = await ExpressCompanyApi.getActiveProviders()
    if (response.success) {
      providerList.value = response.data
    }
  } catch (error) {
    console.error('Load providers error:', error)
  }
}

/**
 * 关闭对话框
 */
const handleClose = () => {
  dialogVisible.value = false
}

/**
 * 创建映射关系
 */
const handleCreate = () => {
  currentMapping.value = null
  resetMappingForm()
  showMappingEditDialog.value = true
}

/**
 * 编辑映射关系
 */
const handleEdit = (mapping: ExpressCompanyProviderMapping) => {
  currentMapping.value = mapping
  Object.assign(mappingFormData, {
    company_id: mapping.company_id,
    provider_id: mapping.provider_id,
    provider_company_code: mapping.provider_company_code,
    is_supported: mapping.is_supported,
    is_preferred: mapping.is_preferred,
    weight_limit_kg: mapping.weight_limit_kg,
    size_limit_cm_length: mapping.size_limit_cm_length,
    size_limit_cm_width: mapping.size_limit_cm_width,
    size_limit_cm_height: mapping.size_limit_cm_height,
    supported_services: mapping.supported_services || []
  })
  showMappingEditDialog.value = true
}

/**
 * 切换映射支持状态（使用新的状态管理API）
 */
const handleToggleSupport = async (mapping: ExpressCompanyProviderMapping) => {
  try {
    // 弹出确认对话框
    await ElMessageBox.confirm(
      `确认${mapping.is_supported ? '启用' : '禁用'}映射关系吗？`,
      '确认操作',
      {
        type: 'warning',
        confirmButtonText: '确认',
        cancelButtonText: '取消'
      }
    )

    // 设置切换状态
    mapping._switching = true

    // 获取快递公司和供应商代码
    const company = props.company
    const provider = providerList.value.find(p => p.id === mapping.provider_id)

    if (!company || !provider) {
      ElMessage.error('无法获取快递公司或供应商信息')
      return
    }

    const response = await ExpressCompanyApi.updateMappingStatus(company.code, provider.code, {
      enabled: mapping.is_supported,
      reason: `管理员${mapping.is_supported ? '启用' : '禁用'}映射关系`
    })

    if (response.success) {
      ElMessage.success(`映射关系 ${company.name} -> ${provider.name} ${mapping.is_supported ? '启用' : '禁用'}成功`)
      emit('success')
      // 重新加载映射列表
      loadMappings()
    } else {
      // 如果更新失败，恢复原状态
      mapping.is_supported = !mapping.is_supported
      ElMessage.error(response.message || '更新失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      // 如果出错，恢复原状态
      mapping.is_supported = !mapping.is_supported
      console.error('Toggle support error:', error)
      ElMessage.error('操作失败')
    } else {
      // 用户取消，恢复状态
      mapping.is_supported = !mapping.is_supported
    }
  } finally {
    mapping._switching = false
  }
}

/**
 * 重载指定供应商
 */
const handleReloadProvider = async (mapping: ExpressCompanyProviderMapping) => {
  try {
    // 获取供应商代码
    const providerCode = getProviderCode(mapping.provider_id)
    if (!providerCode) {
      ElMessage.error('无法获取供应商代码')
      return
    }

    mapping._reloading = true
    const response = await ExpressCompanyApi.reloadProvider(providerCode)
    
    if (response.success) {
      ElMessage.success(`供应商 ${providerCode} 重载成功`)
    } else {
      ElMessage.error(response.message || '重载失败')
    }
  } catch (error) {
    console.error('Reload provider error:', error)
    ElMessage.error('重载失败')
  } finally {
    mapping._reloading = false
  }
}

/**
 * 重载所有供应商
 */
const handleReloadAllProviders = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要重载所有供应商吗？这将应用最新的配置变更。',
      '确认重载',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    reloadingProviders.value = true
    const response = await ExpressCompanyApi.reloadAllProviders()
    
    if (response.success) {
      ElMessage.success('所有供应商重载成功')
    } else {
      ElMessage.error(response.message || '重载失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Reload all providers error:', error)
      ElMessage.error('重载失败')
    }
  } finally {
    reloadingProviders.value = false
  }
}

/**
 * 获取供应商代码
 */
const getProviderCode = (providerId: string): string | null => {
  const provider = providerList.value.find(p => p.id === providerId)
  return provider?.code || null
}

/**
 * 删除映射关系
 */
const handleDelete = async (mapping: ExpressCompanyProviderMapping) => {
  try {
    await ElMessageBox.confirm(
      `确认删除与供应商 "${mapping.provider_name}" 的映射关系吗？`,
      '确认删除',
      {
        type: 'warning',
        confirmButtonText: '确认',
        cancelButtonText: '取消'
      }
    )

    const response = await ExpressCompanyApi.deleteMapping(mapping.id)
    if (response.success) {
      ElMessage.success('删除成功')
      loadMappings()
      emit('success')
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('Delete mapping error:', error)
      ElMessage.error('删除失败')
    }
  }
}

/**
 * 重置映射表单
 */
const resetMappingForm = () => {
  Object.assign(mappingFormData, {
    company_id: props.company?.id || '',
    provider_id: '',
    provider_company_code: '',
    is_supported: true,
    is_preferred: false,
    weight_limit_kg: undefined,
    size_limit_cm_length: undefined,
    size_limit_cm_width: undefined,
    size_limit_cm_height: undefined,
    supported_services: []
  })
  mappingFormRef.value?.clearValidate()
}

/**
 * 获取供应商名称
 */
const getProviderName = (providerId: string) => {
  const provider = providerList.value.find(p => p.id === providerId)
  return provider ? provider.name : providerId
}

/**
 * 提交映射表单
 */
const handleSubmitMapping = async () => {
  if (!mappingFormRef.value) return

  try {
    const valid = await mappingFormRef.value.validate()
    if (!valid) return

    submittingMapping.value = true

    if (isEditMapping.value) {
      // 编辑模式
      const updateData: UpdateExpressMappingRequest = {
        provider_company_code: mappingFormData.provider_company_code,
        is_supported: mappingFormData.is_supported,
        is_preferred: mappingFormData.is_preferred,
        weight_limit_kg: mappingFormData.weight_limit_kg,
        size_limit_cm_length: mappingFormData.size_limit_cm_length,
        size_limit_cm_width: mappingFormData.size_limit_cm_width,
        size_limit_cm_height: mappingFormData.size_limit_cm_height,
        supported_services: mappingFormData.supported_services
      }

      const response = await ExpressCompanyApi.updateMapping(currentMapping.value!.id, updateData)
      if (response.success) {
        ElMessage.success('映射关系更新成功')
        showMappingEditDialog.value = false
        loadMappings()
        emit('success')
      } else {
        ElMessage.error(response.message || '更新失败')
      }
    } else {
      // 创建模式
      const response = await ExpressCompanyApi.createMapping(mappingFormData)
      if (response.success) {
        ElMessage.success('映射关系创建成功')
        showMappingEditDialog.value = false
        loadMappings()
        emit('success')
      } else {
        ElMessage.error(response.message || '创建失败')
      }
    }
  } catch (error) {
    console.error('Submit mapping error:', error)
    ElMessage.error('操作失败')
  } finally {
    submittingMapping.value = false
  }
}
</script>

<style scoped>
.action-bar {
  margin-bottom: 16px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.dialog-footer {
  text-align: right;
}

.text-gray-400 {
  color: #9ca3af;
}
</style>
