<template>
  <div class="express-company-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>快递公司管理</h2>
      <p>管理系统中的快递公司信息，包括基本信息、供应商映射、服务配置等</p>
    </div>

    <!-- 操作栏 -->
    <div class="action-bar">
      <div class="action-left">
        <el-button type="primary" :icon="Plus" @click="handleCreate">
          新增快递公司
        </el-button>
        <el-button :icon="Refresh" @click="handleRefresh">
          刷新
        </el-button>
      </div>
      <div class="action-right">
        <el-button :icon="Download" @click="handleExport">
          导出数据
        </el-button>
      </div>
    </div>

    <!-- 筛选区域 -->
    <div class="filter-section">
      <el-form :model="searchForm" inline>
        <el-form-item label="搜索关键词">
          <el-input
            v-model="searchForm.keyword"
            placeholder="请输入快递公司代码或名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.is_active" placeholder="请选择状态" clearable style="width: 120px">
            <el-option
              v-for="option in EXPRESS_COMPANY_STATUS_OPTIONS"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="排序字段">
          <el-select v-model="searchForm.sort_by" placeholder="请选择排序字段" style="width: 120px">
            <el-option
              v-for="option in EXPRESS_COMPANY_SORT_OPTIONS"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="排序方向">
          <el-select v-model="searchForm.sort_order" placeholder="请选择排序方向" style="width: 100px">
            <el-option
              v-for="option in SORT_ORDER_OPTIONS"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="handleSearch">
            搜索
          </el-button>
          <el-button @click="handleReset">
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-table
        v-loading="loading"
        :data="companyList"
        stripe
        border
        style="width: 100%"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="code" label="快递公司代码" width="140" sortable="custom" />
        <el-table-column prop="name" label="快递公司名称" width="160" sortable="custom" show-overflow-tooltip />
        <el-table-column prop="volume_weight_ratio" label="抛比" width="100" align="center" sortable="custom">
          <template #default="{ row }">
            <span>{{ row.volume_weight_ratio }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="max_weight_kg" label="限重(KG)" width="120" align="center" sortable="custom">
          <template #default="{ row }">
            <span>{{ row.max_weight_kg || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-switch
              v-model="row.is_active"
              :loading="row._switching"
              active-text="启用"
              inactive-text="禁用"
              @change="handleToggleStatus(row)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="sort_order" label="排序" width="100" align="center" sortable="custom" />
        <el-table-column prop="updated_at" label="更新时间" width="180" sortable="custom">
          <template #default="{ row }">
            {{ formatDateTime(row.updated_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="220" fixed="right" align="center">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button type="primary" size="small" @click="handleEdit(row)">
                编辑
              </el-button>
              <el-button type="info" size="small" @click="handleViewMappings(row)">
                映射
              </el-button>
              <el-button type="warning" size="small" @click="handleViewServices(row)">
                服务
              </el-button>
              <el-button type="danger" size="small" @click="handleDelete(row)">
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.page_size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handlePageSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </div>

    <!-- 快递公司编辑对话框 -->
    <CompanyEditDialog
      v-model:visible="showEditDialog"
      :company="currentCompany"
      @success="handleEditSuccess"
    />

    <!-- 映射关系管理对话框 -->
    <MappingManageDialog
      v-model:visible="showMappingDialog"
      :company="currentCompany"
      @success="handleMappingSuccess"
    />

    <!-- 服务管理对话框 -->
    <ServiceManageDialog
      v-model:visible="showServiceDialog"
      :company="currentCompany"
      @success="handleServiceSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Refresh,
  Download,
  Search
} from '@element-plus/icons-vue'

import {
  ExpressCompanyApi,
  type ExpressCompany,
  type ExpressCompanyFilter,
  type StatusUpdateRequest,
  EXPRESS_COMPANY_STATUS_OPTIONS,
  EXPRESS_COMPANY_SORT_OPTIONS,
  SORT_ORDER_OPTIONS
} from '@/api/expressCompanyApi'

// 导入对话框组件
import CompanyEditDialog from './components/CompanyEditDialog.vue'
import MappingManageDialog from './components/MappingManageDialog.vue'
import ServiceManageDialog from './components/ServiceManageDialog.vue'

// ==================== 响应式数据 ====================

const loading = ref(false)
const companyList = ref<ExpressCompany[]>([])

// 搜索表单
const searchForm = reactive<ExpressCompanyFilter & { keyword?: string }>({
  keyword: '',
  is_active: undefined,
  sort_by: 'sort_order',
  sort_order: 'ASC'
})

// 分页信息
const pagination = reactive({
  page: 1,
  page_size: 20,
  total: 0
})

// 对话框状态
const showEditDialog = ref(false)
const showMappingDialog = ref(false)
const showServiceDialog = ref(false)

// 当前操作的数据
const currentCompany = ref<ExpressCompany | null>(null)

// ==================== 生命周期 ====================

onMounted(() => {
  loadCompanyList()
})

// ==================== 方法 ====================

/**
 * 加载快递公司列表
 */
const loadCompanyList = async () => {
  try {
    loading.value = true
    const response = await ExpressCompanyApi.getCompanies({
      page: pagination.page,
      page_size: pagination.page_size,
      ...searchForm
    })

    if (response.success) {
      companyList.value = response.data.companies
      pagination.total = response.data.total
    } else {
      ElMessage.error(response.message || '获取快递公司列表失败')
    }
  } catch (error) {
    console.error('Load company list error:', error)
    ElMessage.error('获取快递公司列表失败')
  } finally {
    loading.value = false
  }
}

/**
 * 搜索
 */
const handleSearch = () => {
  pagination.page = 1
  loadCompanyList()
}

/**
 * 重置搜索
 */
const handleReset = () => {
  Object.assign(searchForm, {
    keyword: '',
    is_active: undefined,
    sort_by: 'sort_order',
    sort_order: 'ASC'
  })
  pagination.page = 1
  loadCompanyList()
}

/**
 * 刷新
 */
const handleRefresh = () => {
  loadCompanyList()
}

/**
 * 分页变化
 */
const handlePageChange = (page: number) => {
  pagination.page = page
  loadCompanyList()
}

/**
 * 页大小变化
 */
const handlePageSizeChange = (pageSize: number) => {
  pagination.page_size = pageSize
  pagination.page = 1
  loadCompanyList()
}

/**
 * 排序变化
 */
const handleSortChange = ({ prop, order }: { prop: string; order: string }) => {
  searchForm.sort_by = prop
  searchForm.sort_order = order === 'ascending' ? 'ASC' : 'DESC'
  loadCompanyList()
}

/**
 * 创建快递公司
 */
const handleCreate = () => {
  currentCompany.value = null
  showEditDialog.value = true
}

/**
 * 编辑快递公司
 */
const handleEdit = (company: ExpressCompany) => {
  currentCompany.value = { ...company }
  showEditDialog.value = true
}

/**
 * 编辑成功
 */
const handleEditSuccess = () => {
  loadCompanyList()
}

/**
 * 切换状态（使用新的状态管理API）
 */
const handleToggleStatus = async (company: ExpressCompany & { _switching?: boolean }) => {
  try {
    // 弹出确认对话框
    await ElMessageBox.confirm(
      `确认${company.is_active ? '启用' : '禁用'}快递公司 "${company.name}" 吗？`,
      '确认操作',
      {
        type: 'warning',
        confirmButtonText: '确认',
        cancelButtonText: '取消'
      }
    )

    // 设置loading状态
    company._switching = true

    const response = await ExpressCompanyApi.updateCompanyStatus(company.code, {
      enabled: company.is_active,
      reason: `管理员${company.is_active ? '启用' : '禁用'}快递公司`
    })

    if (response.success) {
      ElMessage.success(`快递公司 ${company.name} ${company.is_active ? '启用' : '禁用'}成功`)
      // 刷新列表以获取最新状态
      loadCompanyList()
    } else {
      // 恢复状态
      company.is_active = !company.is_active
      ElMessage.error(response.message || '操作失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      // 恢复状态
      company.is_active = !company.is_active
      console.error('Toggle status error:', error)
      ElMessage.error('操作失败')
    } else {
      // 用户取消，恢复状态
      company.is_active = !company.is_active
    }
  } finally {
    // 清除loading状态
    company._switching = false
  }
}

/**
 * 删除快递公司
 */
const handleDelete = async (company: ExpressCompany) => {
  try {
    await ElMessageBox.confirm(
      `确认删除快递公司 "${company.name}" 吗？此操作不可撤销。`,
      '确认删除',
      {
        type: 'warning',
        confirmButtonText: '确认',
        cancelButtonText: '取消'
      }
    )

    const response = await ExpressCompanyApi.deleteCompany(company.id)
    if (response.success) {
      ElMessage.success('删除成功')
      loadCompanyList()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('Delete company error:', error)
      ElMessage.error('删除失败')
    }
  }
}

/**
 * 查看映射关系
 */
const handleViewMappings = (company: ExpressCompany) => {
  currentCompany.value = company
  showMappingDialog.value = true
}

/**
 * 映射关系管理成功
 */
const handleMappingSuccess = () => {
  // 可以选择是否刷新列表
}

/**
 * 查看服务
 */
const handleViewServices = (company: ExpressCompany) => {
  currentCompany.value = company
  showServiceDialog.value = true
}

/**
 * 服务管理成功
 */
const handleServiceSuccess = () => {
  // 可以选择是否刷新列表
}

/**
 * 导出数据
 */
const handleExport = () => {
  const data = companyList.value.map(company => ({
    快递公司代码: company.code,
    快递公司名称: company.name,
    抛比: company.volume_weight_ratio,
    限重KG: company.max_weight_kg || '',
    描述: company.description || '',
    状态: company.is_active ? '启用' : '禁用',
    排序: company.sort_order,
    更新时间: formatDateTime(company.updated_at)
  }))

  // 简单的CSV导出
  const csvContent = [
    Object.keys(data[0]).join(','),
    ...data.map(row => Object.values(row).join(','))
  ].join('\n')

  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = `express_companies_${new Date().toISOString().slice(0, 10)}.csv`
  link.click()
}

// ==================== 工具方法 ====================

/**
 * 格式化日期时间
 */
const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString('zh-CN')
}
</script>

<style scoped>
.express-company-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.filter-section {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.table-section {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.pagination-wrapper {
  padding: 20px;
  text-align: right;
  border-top: 1px solid #ebeef5;
}

.text-gray-400 {
  color: #9ca3af;
}

.action-buttons {
  display: flex;
  gap: 4px;
  justify-content: center;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  margin: 0;
  padding: 4px 8px;
  font-size: 12px;
}
</style>
