<template>
  <div class="interface-allocation-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>查价接口分配管理</h2>
      <p class="page-description">
        管理供应商和快递公司的查价接口类型分配，控制使用标准查价还是实时查价接口
      </p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-number">{{ stats.total_allocations || 0 }}</div>
              <div class="stats-label">总分配数</div>
            </div>
            <el-icon class="stats-icon"><Document /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-number">{{ stats.standard_price_count || 0 }}</div>
              <div class="stats-label">标准查价</div>
            </div>
            <el-icon class="stats-icon standard"><List /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-number">{{ stats.realtime_price_count || 0 }}</div>
              <div class="stats-label">实时查价</div>
            </div>
            <el-icon class="stats-icon realtime"><Timer /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-number">{{ providerCount }}</div>
              <div class="stats-label">供应商数量</div>
            </div>
            <el-icon class="stats-icon"><Shop /></el-icon>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 操作工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button type="primary" @click="showAddDialog = true">
          <el-icon><Plus /></el-icon>
          新增分配
        </el-button>
        <el-button @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
      <div class="toolbar-right">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索供应商或快递公司..."
          style="width: 300px"
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>

    <!-- 分配列表表格 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="filteredAllocations"
        stripe
        border
        style="width: 100%"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="id" label="ID" width="80" sortable />
        <el-table-column prop="provider_code" label="供应商代码" width="120" sortable />
        <el-table-column prop="company_code" label="快递公司代码" width="140" sortable />
        <el-table-column prop="interface_type" label="接口类型" width="150" sortable>
          <template #default="{ row }">
            <el-tag
              :type="row.interface_type === 'QUERY_PRICE' ? 'success' : 'warning'"
              size="small"
            >
              {{ row.interface_type === 'QUERY_PRICE' ? '标准查价' : '实时查价' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="is_active" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.is_active ? 'success' : 'danger'" size="small">
              {{ row.is_active ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180" sortable>
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column prop="updated_at" label="更新时间" width="180" sortable>
          <template #default="{ row }">
            {{ formatDateTime(row.updated_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="editAllocation(row)"
            >
              编辑
            </el-button>
            <el-button
              :type="row.is_active ? 'warning' : 'success'"
              size="small"
              @click="toggleStatus(row)"
            >
              {{ row.is_active ? '禁用' : '启用' }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="editingAllocation ? '编辑接口分配' : '新增接口分配'"
      width="600px"
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        label-width="120px"
      >
        <el-form-item label="供应商代码" prop="provider_code">
          <el-select
            v-model="form.provider_code"
            placeholder="请选择供应商"
            style="width: 100%"
            :disabled="!!editingAllocation"
          >
            <el-option
              v-for="provider in providerOptions"
              :key="provider.value"
              :label="provider.label"
              :value="provider.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="快递公司代码" prop="company_code">
          <el-select
            v-model="form.company_code"
            placeholder="请选择快递公司"
            style="width: 100%"
            :disabled="!!editingAllocation"
          >
            <el-option
              v-for="company in companyOptions"
              :key="company.value"
              :label="company.label"
              :value="company.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="接口类型" prop="interface_type">
          <el-radio-group v-model="form.interface_type">
            <el-radio value="QUERY_PRICE">标准查价</el-radio>
            <el-radio value="QUERY_REALTIME_PRICE">实时查价</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitting">
          {{ editingAllocation ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Document, List, Timer, Shop, Plus, Refresh, Search } from '@element-plus/icons-vue'
import { ExpressCompanyApi } from '@/api/expressCompanyApi'
import { formatDateTime } from '@/utils/utils'

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const showAddDialog = ref(false)
const searchKeyword = ref('')
const editingAllocation = ref<any>(null)

// 统计数据
const stats = ref<any>({})

// 分配列表数据
const allocations = ref<any[]>([])

// 分页数据
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 表单数据
const form = reactive({
  provider_code: '',
  company_code: '',
  interface_type: 'QUERY_PRICE'
})

// 表单验证规则
const formRules = {
  provider_code: [
    { required: true, message: '请选择供应商', trigger: 'change' }
  ],
  company_code: [
    { required: true, message: '请选择快递公司', trigger: 'change' }
  ],
  interface_type: [
    { required: true, message: '请选择接口类型', trigger: 'change' }
  ]
}

// 选项数据
const providerOptions = ref([
  { label: '快递100', value: 'kuaidi100' },
  { label: '云通', value: 'yuntong' },
  { label: '菜鸟', value: 'cainiao' },
  { label: '快递鸟', value: 'kuaidiniao' },
  { label: '易达', value: 'yida' }
])

const companyOptions = ref([
  { label: '中通快递 (ZTO)', value: 'ZTO' },
  { label: '圆通快递 (YTO)', value: 'YTO' },
  { label: '申通快递 (STO)', value: 'STO' },
  { label: '韵达快递 (YD)', value: 'YD' },
  { label: '极兔快递 (JT)', value: 'JT' },
  { label: '京东快递 (JD)', value: 'JD' },
  { label: '德邦快递 (DBL)', value: 'DBL' },
  { label: '菜鸟 (CAINIAO)', value: 'CAINIAO' }
])

// 计算属性
const filteredAllocations = computed(() => {
  if (!searchKeyword.value) {
    return allocations.value
  }
  const keyword = searchKeyword.value.toLowerCase()
  return allocations.value.filter(item =>
    item.provider_code.toLowerCase().includes(keyword) ||
    item.company_code.toLowerCase().includes(keyword)
  )
})

const providerCount = computed(() => {
  const providers = new Set(allocations.value.map(item => item.provider_code))
  return providers.size
})

// 表单引用
const formRef = ref()

// 方法
const loadStats = async () => {
  try {
    const response = await ExpressCompanyApi.getAllocationStats()
    if (response.success) {
      stats.value = response.data
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

const loadAllocations = async () => {
  loading.value = true
  try {
    const response = await ExpressCompanyApi.getAllAllocations({
      page: pagination.page,
      page_size: pagination.pageSize
    })
    if (response.success) {
      allocations.value = response.data.allocations || []
      pagination.total = response.data.total || 0
    }
  } catch (error) {
    console.error('加载分配列表失败:', error)
    ElMessage.error('加载分配列表失败')
  } finally {
    loading.value = false
  }
}

const refreshData = async () => {
  await Promise.all([loadStats(), loadAllocations()])
  ElMessage.success('数据刷新成功')
}

const handleSearch = () => {
  // 搜索逻辑在计算属性中处理
}

const handleSortChange = (sort: any) => {
  // 这里可以实现排序逻辑
  console.log('排序变化:', sort)
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.page = 1
  loadAllocations()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadAllocations()
}

const editAllocation = (row: any) => {
  editingAllocation.value = row
  form.provider_code = row.provider_code
  form.company_code = row.company_code
  form.interface_type = row.interface_type
  showAddDialog.value = true
}

const toggleStatus = async (row: any) => {
  const action = row.is_active ? '禁用' : '启用'
  try {
    await ElMessageBox.confirm(
      `确定要${action}该接口分配吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 这里需要调用切换状态的API
    // 暂时模拟操作
    row.is_active = !row.is_active
    ElMessage.success(`${action}成功`)
  } catch (error) {
    console.log('用户取消操作')
  }
}

const submitForm = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    submitting.value = true
    
    if (editingAllocation.value) {
      // 编辑模式 - 调用更新接口
      await ExpressCompanyApi.setPriceInterface({
        provider_code: form.provider_code,
        company_code: form.company_code,
        interface_type: form.interface_type as 'QUERY_PRICE' | 'QUERY_REALTIME_PRICE'
      })
      ElMessage.success('接口分配更新成功')
    } else {
      // 新增模式 - 调用创建接口
      await ExpressCompanyApi.setPriceInterface({
        provider_code: form.provider_code,
        company_code: form.company_code,
        interface_type: form.interface_type as 'QUERY_PRICE' | 'QUERY_REALTIME_PRICE'
      })
      ElMessage.success('接口分配创建成功')
    }
    
    showAddDialog.value = false
    await refreshData()
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败，请重试')
  } finally {
    submitting.value = false
  }
}

const resetForm = () => {
  editingAllocation.value = null
  form.provider_code = ''
  form.company_code = ''
  form.interface_type = 'QUERY_PRICE'
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 生命周期
onMounted(() => {
  refreshData()
})
</script>

<style scoped lang="scss">
.interface-allocation-management {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;

  .page-header {
    margin-bottom: 24px;

    h2 {
      margin: 0 0 8px 0;
      color: #303133;
      font-size: 24px;
      font-weight: 600;
    }

    .page-description {
      margin: 0;
      color: #606266;
      font-size: 14px;
      line-height: 1.5;
    }
  }

  .stats-cards {
    margin-bottom: 24px;

    .stats-card {
      position: relative;
      overflow: hidden;

      :deep(.el-card__body) {
        padding: 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      .stats-content {
        .stats-number {
          font-size: 28px;
          font-weight: 600;
          color: #303133;
          line-height: 1;
          margin-bottom: 8px;
        }

        .stats-label {
          font-size: 14px;
          color: #909399;
        }
      }

      .stats-icon {
        font-size: 32px;
        color: #409eff;
        opacity: 0.8;

        &.standard {
          color: #67c23a;
        }

        &.realtime {
          color: #e6a23c;
        }
      }
    }
  }

  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .toolbar-left {
      display: flex;
      gap: 12px;
    }

    .toolbar-right {
      display: flex;
      gap: 12px;
      align-items: center;
    }
  }

  .table-card {
    :deep(.el-card__body) {
      padding: 0;
    }

    .el-table {
      :deep(.el-table__header) {
        background-color: #fafafa;

        th {
          background-color: #fafafa !important;
          color: #606266;
          font-weight: 600;
        }
      }

      :deep(.el-table__row) {
        &:hover {
          background-color: #f5f7fa;
        }
      }
    }

    .pagination-wrapper {
      padding: 20px;
      display: flex;
      justify-content: flex-end;
      border-top: 1px solid #ebeef5;
      background-color: #fafafa;
    }
  }

  // 对话框样式
  :deep(.el-dialog) {
    .el-dialog__header {
      padding: 20px 20px 10px;
      border-bottom: 1px solid #ebeef5;

      .el-dialog__title {
        font-size: 18px;
        font-weight: 600;
        color: #303133;
      }
    }

    .el-dialog__body {
      padding: 20px;
    }

    .el-dialog__footer {
      padding: 10px 20px 20px;
      border-top: 1px solid #ebeef5;
    }
  }

  // 表单样式
  .el-form {
    .el-form-item {
      margin-bottom: 22px;

      :deep(.el-form-item__label) {
        color: #606266;
        font-weight: 500;
      }
    }
  }

  // 标签样式
  .el-tag {
    font-weight: 500;
  }

  // 按钮样式
  .el-button {
    &.el-button--small {
      padding: 5px 12px;
      font-size: 12px;
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    padding: 16px;

    .stats-cards {
      .el-col {
        margin-bottom: 16px;
      }
    }

    .toolbar {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;

      .toolbar-left,
      .toolbar-right {
        justify-content: center;
      }
    }

    .table-card {
      :deep(.el-table) {
        font-size: 12px;
      }
    }
  }
}
</style>
