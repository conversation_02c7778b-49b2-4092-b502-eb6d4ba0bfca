<template>
  <div class="region-blacklist-container">
    <!-- 页面标题和描述 -->
    <div class="page-header">
      <div class="page-title">
        <h1>地区黑名单管理</h1>
        <p class="page-description">
          管理云通供应商不支持的地区路线，减少无效API调用，优化系统性能
        </p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="handleRefresh" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
        <el-button type="warning" @click="handleClearExpired" :loading="clearingExpired">
          <el-icon><Delete /></el-icon>
          清理过期条目
        </el-button>
      </div>
    </div>

    <!-- 统计信息卡片 -->
    <div class="statistics-grid">
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon total">
            <el-icon><Document /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ statistics?.total_entries || 0 }}</div>
            <div class="stat-label">总条目数</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon blacklisted">
            <el-icon><WarningFilled /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ statistics?.blacklisted_entries || 0 }}</div>
            <div class="stat-label">黑名单条目</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon rate">
            <el-icon><TrendCharts /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ blacklistRate }}%</div>
            <div class="stat-label">黑名单比例</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon providers">
            <el-icon><Connection /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ Object.keys(statistics?.provider_stats || {}).length }}</div>
            <div class="stat-label">涉及供应商</div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 主要内容区域 -->
    <div class="content-grid">
      <!-- 左侧：黑名单条目列表 -->
      <el-card class="blacklist-table-card">
        <template #header>
          <div class="card-header">
            <span class="card-title">
              <el-icon><List /></el-icon>
              黑名单条目列表
            </span>
            <div class="header-actions">
              <el-input
                v-model="searchKeyword"
                placeholder="搜索路线或供应商..."
                style="width: 300px"
                clearable
                @input="handleSearch"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </div>
          </div>
        </template>

        <el-table
          :data="filteredEntries"
          v-loading="loading"
          empty-text="暂无黑名单条目"
          height="600"
        >
          <el-table-column prop="provider" label="供应商" width="100">
            <template #default="{ row }">
              <el-tag :type="getProviderTagType(row.provider)">
                {{ getProviderName(row.provider) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="route" label="路线" min-width="180">
            <template #default="{ row }">
              <div class="route-info">
                <span class="route-text">{{ row.route }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="express_code" label="快递公司" width="100">
            <template #default="{ row }">
              <el-tag size="small">{{ row.express_code }}</el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="failure_count" label="失败次数" width="100" align="center">
            <template #default="{ row }">
              <el-badge :value="row.failure_count" :type="getFailureCountType(row.failure_count)">
                <span></span>
              </el-badge>
            </template>
          </el-table-column>

          <el-table-column prop="last_failure_at" label="最后失败时间" width="180">
            <template #default="{ row }">
              <div class="time-info">
                <el-icon><Clock /></el-icon>
                {{ formatTime(row.last_failure_at) }}
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="is_blacklisted" label="状态" width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="row.is_blacklisted ? 'danger' : 'warning'">
                {{ row.is_blacklisted ? '已拉黑' : '监控中' }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="120" align="center" fixed="right">
            <template #default="{ row }">
              <el-button
                v-if="row.is_blacklisted"
                type="success"
                size="small"
                @click="handleRemoveFromBlacklist(row)"
              >
                移除
              </el-button>
              <el-button
                type="primary"
                size="small"
                @click="handleViewDetails(row)"
              >
                详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <!-- 右侧：供应商统计和路线测试 -->
      <div class="side-panels">
        <!-- 供应商统计 -->
        <el-card class="provider-stats-card">
          <template #header>
            <span class="card-title">
              <el-icon><PieChart /></el-icon>
              供应商统计
            </span>
          </template>
          <div class="provider-stats">
            <div
              v-for="(count, provider) in statistics?.provider_stats"
              :key="provider"
              class="provider-stat-item"
            >
              <div class="provider-info">
                <el-tag :type="getProviderTagType(provider)" size="small">
                  {{ getProviderName(provider) }}
                </el-tag>
                <span class="count">{{ count }} 条</span>
              </div>
              <el-progress
                :percentage="getProviderPercentage(count)"
                :stroke-width="8"
                :show-text="false"
              />
            </div>
          </div>
        </el-card>

        <!-- 路线测试工具 -->
        <el-card class="route-test-card">
          <template #header>
            <span class="card-title">
              <el-icon><Search /></el-icon>
              路线测试工具
            </span>
          </template>
          <div class="route-test">
            <el-form :model="testForm" label-width="80px" size="small">
              <el-form-item label="供应商">
                <el-select v-model="testForm.provider" placeholder="选择供应商" style="width: 100%">
                  <el-option label="云通" value="yuntong" />
                  <el-option label="易达" value="yida" />
                  <el-option label="快递100" value="kuaidi100" />
                </el-select>
              </el-form-item>

              <el-form-item label="路线">
                <el-input
                  v-model="testForm.route"
                  placeholder="如：上海市->安徽省"
                  style="width: 100%"
                />
              </el-form-item>

              <el-form-item label="快递公司">
                <el-select v-model="testForm.express_code" placeholder="选择快递公司" style="width: 100%">
                  <el-option label="极兔快递" value="JT" />
                  <el-option label="申通快递" value="STO" />
                  <el-option label="圆通速递" value="YTO" />
                  <el-option label="中通快递" value="ZTO" />
                  <el-option label="韵达速递" value="YD" />
                </el-select>
              </el-form-item>

              <el-form-item>
                <el-button
                  type="primary"
                  @click="handleTestRoute"
                  :loading="testing"
                  style="width: 100%"
                >
                  测试路线
                </el-button>
              </el-form-item>
            </el-form>

            <!-- 测试结果 -->
            <div v-if="testResult" class="test-result">
              <el-divider content-position="left">测试结果</el-divider>
              <div class="result-item">
                <span class="label">黑名单状态:</span>
                <el-tag :type="testResult.is_blacklisted ? 'danger' : 'success'">
                  {{ testResult.is_blacklisted ? '在黑名单中' : '不在黑名单中' }}
                </el-tag>
              </div>
              <div class="result-item">
                <span class="label">是否跳过:</span>
                <el-tag :type="testResult.should_skip ? 'warning' : 'info'">
                  {{ testResult.should_skip ? '会跳过查询' : '正常查询' }}
                </el-tag>
              </div>
              <div v-if="testResult.entry" class="result-item">
                <span class="label">失败次数:</span>
                <span>{{ testResult.entry.failure_count }} 次</span>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 条目详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="黑名单条目详情"
      width="600px"
    >
      <div v-if="selectedEntry" class="entry-details">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="供应商">
            <el-tag :type="getProviderTagType(selectedEntry.provider)">
              {{ getProviderName(selectedEntry.provider) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="快递公司">
            <el-tag>{{ selectedEntry.express_code }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="路线" span="2">
            {{ selectedEntry.route }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="selectedEntry.is_blacklisted ? 'danger' : 'warning'">
              {{ selectedEntry.is_blacklisted ? '已拉黑' : '监控中' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="失败次数">
            {{ selectedEntry.failure_count }} 次
          </el-descriptions-item>
          <el-descriptions-item label="创建时间" span="2">
            {{ formatTime(selectedEntry.created_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="最后失败时间" span="2">
            {{ formatTime(selectedEntry.last_failure_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="错误信息" span="2">
            <div class="error-message">
              {{ selectedEntry.error_message }}
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
          <el-button
            v-if="selectedEntry?.is_blacklisted"
            type="success"
            @click="handleRemoveFromBlacklist(selectedEntry)"
          >
            从黑名单移除
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh,
  Delete,
  Document,
  WarningFilled,
  TrendCharts,
  Connection,
  List,
  Search,
  Clock,
  PieChart
} from '@element-plus/icons-vue'
import { regionBlacklistApi } from '@/api/regionBlacklistApi'
import type { BlacklistEntry, BlacklistStatistics, RouteTestResult } from '@/api/regionBlacklistApi'

// 响应式数据
const loading = ref(false)
const clearingExpired = ref(false)
const testing = ref(false)
const statistics = ref<BlacklistStatistics>()
const entries = ref<BlacklistEntry[]>([])
const searchKeyword = ref('')
const detailDialogVisible = ref(false)
const selectedEntry = ref<BlacklistEntry>()
const testResult = ref<RouteTestResult>()

// 测试表单
const testForm = ref({
  provider: '',
  route: '',
  express_code: ''
})

// 计算属性
const blacklistRate = computed(() => {
  if (!statistics.value?.total_entries) return 0
  return Math.round((statistics.value.blacklisted_entries / statistics.value.total_entries) * 100)
})

const filteredEntries = computed(() => {
  if (!searchKeyword.value) return entries.value
  const keyword = searchKeyword.value.toLowerCase()
  return entries.value.filter(entry =>
    entry.route.toLowerCase().includes(keyword) ||
    entry.provider.toLowerCase().includes(keyword) ||
    entry.express_code.toLowerCase().includes(keyword)
  )
})

// 方法
const getProviderName = (provider: string) => {
  const names: Record<string, string> = {
    yuntong: '云通',
    yida: '易达',
    kuaidi100: '快递100'
  }
  return names[provider] || provider
}

const getProviderTagType = (provider: string) => {
  const types: Record<string, string> = {
    yuntong: 'danger',
    yida: 'warning',
    kuaidi100: 'success'
  }
  return types[provider] || 'info'
}

const getFailureCountType = (count: number) => {
  if (count >= 10) return 'danger'
  if (count >= 5) return 'warning'
  return 'primary'
}

const getProviderPercentage = (count: number) => {
  if (!statistics.value?.total_entries) return 0
  return Math.round((count / statistics.value.total_entries) * 100)
}

const formatTime = (timeStr: string) => {
  return new Date(timeStr).toLocaleString('zh-CN')
}

const loadData = async () => {
  loading.value = true
  try {
    const [statsRes, entriesRes] = await Promise.all([
      regionBlacklistApi.getStatistics(),
      regionBlacklistApi.getAllEntries()
    ])

    if (statsRes.success) {
      statistics.value = statsRes.data
    }

    if (entriesRes.success) {
      entries.value = entriesRes.data
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const handleRefresh = () => {
  loadData()
}

const handleSearch = () => {
  // 搜索功能已通过计算属性实现
}

const handleClearExpired = async () => {
  try {
    await ElMessageBox.confirm(
      '此操作将清理30天未失败的黑名单条目，是否继续？',
      '确认清理',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    clearingExpired.value = true
    const res = await regionBlacklistApi.clearExpiredEntries()

    if (res.success) {
      ElMessage.success(`清理完成，共清理了 ${res.data.cleared} 个过期条目`)
      await loadData()
    } else {
      ElMessage.error(res.message || '清理失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('清理过期条目失败:', error)
      ElMessage.error('清理失败')
    }
  } finally {
    clearingExpired.value = false
  }
}

const handleRemoveFromBlacklist = async (entry: BlacklistEntry) => {
  try {
    await ElMessageBox.confirm(
      `确定要将路线"${entry.route}"从黑名单中移除吗？`,
      '确认移除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const res = await regionBlacklistApi.removeFromBlacklist({
      provider: entry.provider,
      route: entry.route,
      express_code: entry.express_code
    })

    if (res.success) {
      ElMessage.success('已从黑名单移除')
      detailDialogVisible.value = false
      await loadData()
    } else {
      ElMessage.error(res.message || '移除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('移除失败:', error)
      ElMessage.error('移除失败')
    }
  }
}

const handleViewDetails = (entry: BlacklistEntry) => {
  selectedEntry.value = entry
  detailDialogVisible.value = true
}

const handleTestRoute = async () => {
  if (!testForm.value.provider || !testForm.value.route || !testForm.value.express_code) {
    ElMessage.warning('请填写完整的测试信息')
    return
  }

  testing.value = true
  try {
    const res = await regionBlacklistApi.testRoute({
      provider: testForm.value.provider,
      route: testForm.value.route,
      express_code: testForm.value.express_code
    })

    if (res.success) {
      testResult.value = res.data
    } else {
      ElMessage.error(res.message || '测试失败')
    }
  } catch (error) {
    console.error('测试路线失败:', error)
    ElMessage.error('测试失败')
  } finally {
    testing.value = false
  }
}

// 生命周期
loadData()
</script>

<style scoped lang="scss">
.region-blacklist-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;

  .page-title {
    h1 {
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 600;
      color: #303133;
    }

    .page-description {
      margin: 0;
      color: #606266;
      font-size: 14px;
      line-height: 1.4;
    }
  }

  .header-actions {
    display: flex;
    gap: 12px;
  }
}

.statistics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;

  .stat-card {
    border: none;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .stat-content {
      display: flex;
      align-items: center;
      gap: 16px;

      .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;

        &.total {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
        }

        &.blacklisted {
          background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
          color: white;
        }

        &.rate {
          background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
          color: white;
        }

        &.providers {
          background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
          color: white;
        }
      }

      .stat-info {
        .stat-number {
          font-size: 28px;
          font-weight: bold;
          color: #303133;
          line-height: 1;
        }

        .stat-label {
          font-size: 14px;
          color: #909399;
          margin-top: 4px;
        }
      }
    }
  }
}

.content-grid {
  display: grid;
  grid-template-columns: 1fr 350px;
  gap: 20px;
}

.blacklist-table-card {
  border: none;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .card-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 600;
      color: #303133;
    }
  }

  .route-info {
    .route-text {
      font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
      background: #f1f2f6;
      padding: 2px 6px;
      border-radius: 4px;
      font-size: 12px;
    }
  }

  .time-info {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    color: #909399;
  }
}

.side-panels {
  display: flex;
  flex-direction: column;
  gap: 20px;

  .provider-stats-card,
  .route-test-card {
    border: none;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .card-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 600;
      color: #303133;
    }
  }
}

.provider-stats {
  .provider-stat-item {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }

    .provider-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;

      .count {
        font-size: 12px;
        color: #909399;
      }
    }
  }
}

.route-test {
  .test-result {
    .result-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;

      .label {
        font-size: 14px;
        color: #606266;
      }
    }
  }
}

.entry-details {
  .error-message {
    background: #fef0f0;
    border: 1px solid #fbc4c4;
    border-radius: 4px;
    padding: 8px;
    font-size: 12px;
    color: #f56c6c;
    word-break: break-all;
  }
}

@media (max-width: 1200px) {
  .content-grid {
    grid-template-columns: 1fr;
  }

  .side-panels {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .statistics-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .side-panels {
    grid-template-columns: 1fr;
  }
}
</style> 