<template>
  <div class="system-diagnostics">
    <div class="page-header">
      <h2>系统诊断</h2>
      <p class="page-description">监控系统性能和定时器状态，诊断潜在问题</p>
    </div>

    <!-- 定时器统计 -->
    <el-card class="stats-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>定时器统计</span>
          <el-button size="small" @click="refreshStats">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </template>
      
      <div class="stats-grid">
        <div class="stat-item">
          <div class="stat-value">{{ timerStats.total }}</div>
          <div class="stat-label">总定时器数</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ timerStats.active }}</div>
          <div class="stat-label">活跃定时器</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ timerStats.paused }}</div>
          <div class="stat-label">暂停定时器</div>
        </div>
      </div>

      <div class="timer-details">
        <h4>按类型分布</h4>
        <div class="type-stats">
          <span>Interval: {{ timerStats.byType.interval }}</span>
          <span>Timeout: {{ timerStats.byType.timeout }}</span>
        </div>

        <h4>按组件分布</h4>
        <div class="component-stats">
          <div v-for="(count, component) in timerStats.byComponent" :key="component" class="component-item">
            <span class="component-name">{{ component }}</span>
            <span class="component-count">{{ count }}</span>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 性能监控 -->
    <el-card class="performance-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>性能监控</span>
          <el-button size="small" @click="refreshPerformance">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </template>

      <div class="performance-metrics">
        <div class="metric-item">
          <div class="metric-label">页面可见性</div>
          <div class="metric-value" :class="{ 'visible': isPageVisible, 'hidden': !isPageVisible }">
            {{ isPageVisible ? '可见' : '隐藏' }}
          </div>
        </div>

        <div class="metric-item">
          <div class="metric-label">内存使用</div>
          <div class="metric-value">{{ memoryUsage }} MB</div>
        </div>

        <div class="metric-item">
          <div class="metric-label">缓存命中率</div>
          <div class="metric-value">{{ cacheStats.hitRate }}</div>
        </div>

        <div class="metric-item">
          <div class="metric-label">请求成功率</div>
          <div class="metric-value">{{ requestStats.successRate }}</div>
        </div>

        <div class="metric-item">
          <div class="metric-label">活跃请求</div>
          <div class="metric-value">{{ requestStats.activeRequests }}</div>
        </div>

        <div class="metric-item">
          <div class="metric-label">去重率</div>
          <div class="metric-value">{{ requestStats.deduplicationRate }}</div>
        </div>
      </div>

      <!-- HTTP客户端统计 -->
      <div class="http-stats">
        <h4>HTTP客户端统计</h4>
        <div class="stats-row">
          <span>缓存项数: {{ cacheStats.totalSize }}</span>
          <span>待处理请求: {{ requestStats.pending }}</span>
          <span>已完成请求: {{ requestStats.completed }}</span>
          <span>失败请求: {{ requestStats.failed }}</span>
        </div>
      </div>
    </el-card>

    <!-- 操作测试 -->
    <el-card class="test-card" shadow="hover">
      <template #header>
        <span>功能测试</span>
      </template>
      
      <div class="test-actions">
        <el-button @click="testRefresh" :loading="testingRefresh">
          测试页面刷新
        </el-button>
        
        <el-button @click="testDebounce" :loading="testingDebounce">
          测试防抖功能
        </el-button>
        
        <el-button @click="testTimer">
          创建测试定时器
        </el-button>
        
        <el-button @click="clearTestTimers" type="danger">
          清理测试定时器
        </el-button>
      </div>
      
      <div class="test-logs">
        <h4>测试日志</h4>
        <div class="log-container">
          <div v-for="(log, index) in testLogs" :key="index" class="log-item">
            <span class="log-time">{{ log.time }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'
import { useTimerManager, timerManager } from '@/utils/timerManager'
import { debounce } from '@/utils/debounce'
import { useSettingStore } from '@/store/modules/setting'
import { useHttpClient } from '@/utils/httpClient'

// 响应式数据
const timerStats = reactive({
  total: 0,
  active: 0,
  paused: 0,
  byType: { interval: 0, timeout: 0 },
  byComponent: {} as Record<string, number>
})

const isPageVisible = ref(true)
const memoryUsage = ref(0)
const testingRefresh = ref(false)
const testingDebounce = ref(false)
const testLogs = ref<Array<{ time: string; message: string }>>([])

// HTTP客户端统计
const cacheStats = reactive({
  hits: 0,
  misses: 0,
  totalSize: 0,
  hitRate: '0%'
})

const requestStats = reactive({
  total: 0,
  pending: 0,
  completed: 0,
  failed: 0,
  activeRequests: 0,
  successRate: '0%',
  deduplicationRate: '0%'
})

// 使用定时器管理器和HTTP客户端
const { createTimer, clearComponentTimers } = useTimerManager('system-diagnostics')
const { getStats } = useHttpClient()
const settingStore = useSettingStore()

// 添加测试日志
const addLog = (message: string) => {
  const time = new Date().toLocaleTimeString()
  testLogs.value.unshift({ time, message })
  if (testLogs.value.length > 50) {
    testLogs.value = testLogs.value.slice(0, 50)
  }
}

// 刷新统计信息
const refreshStats = () => {
  const stats = timerManager.getStats()
  Object.assign(timerStats, stats)

  // 更新内存使用情况
  if (window.performance && (performance as any).memory) {
    memoryUsage.value = Math.round((performance as any).memory.usedJSHeapSize / 1024 / 1024)
  }

  addLog('统计信息已刷新')
}

// 刷新性能指标
const refreshPerformance = () => {
  try {
    const httpStats = getStats()

    // 更新缓存统计
    Object.assign(cacheStats, httpStats.cache)

    // 更新请求统计
    Object.assign(requestStats, httpStats.requests)

    addLog('性能指标已刷新')
  } catch (error) {
    console.error('刷新性能指标失败:', error)
    addLog('性能指标刷新失败')
  }
}

// 测试页面刷新
const testRefresh = async () => {
  testingRefresh.value = true
  addLog('开始测试页面刷新')
  
  try {
    settingStore.reload()
    addLog('页面刷新测试完成')
  } catch (error) {
    addLog(`页面刷新测试失败: ${error}`)
  } finally {
    testingRefresh.value = false
  }
}

// 测试防抖功能
const testDebounceFunction = debounce(() => {
  addLog('防抖函数执行完成')
  testingDebounce.value = false
}, 1000)

const testDebounce = () => {
  testingDebounce.value = true
  addLog('开始测试防抖功能（1秒延迟）')
  testDebounceFunction()
}

// 创建测试定时器
const testTimer = () => {
  const timerId = `test-timer-${Date.now()}`
  
  createTimer({
    id: timerId,
    type: 'interval',
    delay: 2000,
    callback: () => {
      addLog(`测试定时器 ${timerId} 执行`)
    }
  })
  
  addLog(`已创建测试定时器: ${timerId}`)
  refreshStats()
}

// 清理测试定时器
const clearTestTimers = () => {
  clearComponentTimers()
  addLog('已清理所有测试定时器')
  refreshStats()
}

// 监听页面可见性变化
const handleVisibilityChange = () => {
  isPageVisible.value = !document.hidden
  addLog(`页面可见性变更: ${isPageVisible.value ? '可见' : '隐藏'}`)
}

// 生命周期
onMounted(() => {
  refreshStats()
  refreshPerformance()

  // 监听页面可见性
  document.addEventListener('visibilitychange', handleVisibilityChange)

  // 定期刷新统计信息
  createTimer({
    id: 'stats-refresh',
    type: 'interval',
    delay: 5000,
    pauseWhenHidden: true,
    callback: () => {
      refreshStats()
      refreshPerformance()
    }
  })

  addLog('系统诊断页面初始化完成')
})

onUnmounted(() => {
  document.removeEventListener('visibilitychange', handleVisibilityChange)
})
</script>

<style scoped lang="scss">
.system-diagnostics {
  padding: 20px;
  
  .page-header {
    margin-bottom: 24px;
    
    h2 {
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 600;
      color: #1f2937;
    }
    
    .page-description {
      margin: 0;
      color: #6b7280;
      font-size: 14px;
    }
  }
  
  .stats-card, .performance-card, .test-card {
    margin-bottom: 20px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
  
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin-bottom: 20px;
    
    .stat-item {
      text-align: center;
      padding: 20px;
      background: #f8fafc;
      border-radius: 8px;
      
      .stat-value {
        font-size: 32px;
        font-weight: bold;
        color: #3b82f6;
        margin-bottom: 8px;
      }
      
      .stat-label {
        color: #6b7280;
        font-size: 14px;
      }
    }
  }
  
  .timer-details {
    h4 {
      margin: 16px 0 8px 0;
      color: #374151;
    }
    
    .type-stats {
      display: flex;
      gap: 20px;
      margin-bottom: 16px;
      
      span {
        padding: 4px 12px;
        background: #e5e7eb;
        border-radius: 4px;
        font-size: 14px;
      }
    }
    
    .component-stats {
      .component-item {
        display: flex;
        justify-content: space-between;
        padding: 8px 0;
        border-bottom: 1px solid #e5e7eb;
        
        .component-name {
          color: #374151;
        }
        
        .component-count {
          color: #3b82f6;
          font-weight: bold;
        }
      }
    }
  }
  
  .performance-metrics {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin-bottom: 20px;

    .metric-item {
      text-align: center;

      .metric-label {
        color: #6b7280;
        font-size: 14px;
        margin-bottom: 8px;
      }

      .metric-value {
        font-size: 18px;
        font-weight: bold;

        &.visible {
          color: #10b981;
        }

        &.hidden {
          color: #ef4444;
        }
      }
    }
  }

  .http-stats {
    h4 {
      margin: 0 0 12px 0;
      color: #374151;
      font-size: 16px;
    }

    .stats-row {
      display: flex;
      gap: 20px;
      flex-wrap: wrap;

      span {
        padding: 4px 12px;
        background: #f3f4f6;
        border-radius: 4px;
        font-size: 14px;
        color: #374151;
      }
    }
  }
  
  .test-actions {
    display: flex;
    gap: 12px;
    margin-bottom: 20px;
    flex-wrap: wrap;
  }
  
  .test-logs {
    h4 {
      margin: 0 0 12px 0;
      color: #374151;
    }
    
    .log-container {
      max-height: 300px;
      overflow-y: auto;
      border: 1px solid #e5e7eb;
      border-radius: 4px;
      padding: 12px;
      background: #f8fafc;
      
      .log-item {
        display: flex;
        gap: 12px;
        padding: 4px 0;
        font-size: 14px;
        
        .log-time {
          color: #6b7280;
          min-width: 80px;
        }
        
        .log-message {
          color: #374151;
        }
      }
    }
  }
}
</style>
