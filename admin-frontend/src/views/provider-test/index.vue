<template>
  <div class="provider-test">
    <h2>供应商API测试页面</h2>
    
    <div class="test-buttons">
      <el-button type="primary" @click="testProvidersAPI">测试供应商API</el-button>
      <el-button type="success" @click="testConfigsAPI">测试配置API</el-button>
    </div>
    
    <div class="test-results">
      <h3>测试结果:</h3>
      <pre>{{ testResults }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { SystemConfigApi } from '@/api/systemConfigApi'

const testResults = ref('')

const testProvidersAPI = async () => {
  try {
    testResults.value = '正在测试供应商API...\n'
    
    // 直接调用后端API
    const response = await fetch('/api/v1/admin/express/providers?page=1&page_size=100')
    const data = await response.json()
    
    testResults.value += `供应商API响应:\n${JSON.stringify(data, null, 2)}\n\n`
    
    if (data.success) {
      ElMessage.success('供应商API测试成功')
    } else {
      ElMessage.error('供应商API测试失败')
    }
  } catch (error) {
    testResults.value += `供应商API错误: ${error}\n\n`
    ElMessage.error('供应商API测试失败')
  }
}

const testConfigsAPI = async () => {
  try {
    testResults.value += '正在测试配置API...\n'
    
    // 直接调用后端API
    const response = await fetch('/api/v1/admin/system-configs/groups/provider')
    const data = await response.json()
    
    testResults.value += `配置API响应:\n${JSON.stringify(data, null, 2)}\n\n`
    
    if (data.success) {
      ElMessage.success('配置API测试成功')
    } else {
      ElMessage.error('配置API测试失败')
    }
  } catch (error) {
    testResults.value += `配置API错误: ${error}\n\n`
    ElMessage.error('配置API测试失败')
  }
}
</script>

<style scoped>
.provider-test {
  padding: 20px;
}

.test-buttons {
  margin: 20px 0;
}

.test-results {
  margin-top: 20px;
}

.test-results pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  max-height: 400px;
  overflow-y: auto;
}
</style>
