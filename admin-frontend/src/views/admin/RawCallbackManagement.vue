<template>
  <div class="raw-callback-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>原始回调数据管理</h1>
      <p>管理和重推供应商的原始回调数据，用于排查问题和修复状态映射错误</p>
    </div>

    <!-- 统计卡片 -->
    <div class="statistics-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.total_records }}</div>
              <div class="stat-label">总记录数</div>
            </div>
            <el-icon class="stat-icon"><Document /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.success_records }}</div>
              <div class="stat-label">成功处理</div>
            </div>
            <el-icon class="stat-icon success"><Check /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.failed_records }}</div>
              <div class="stat-label">处理失败</div>
            </div>
            <el-icon class="stat-icon error"><Close /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.success_rate.toFixed(1) }}%</div>
              <div class="stat-label">成功率</div>
            </div>
            <el-icon class="stat-icon"><TrendCharts /></el-icon>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 筛选器 -->
    <el-card class="filter-card">
      <div class="filter-form">
        <el-form :model="filterForm" inline>
          <el-form-item label="供应商">
            <el-select v-model="filterForm.provider" placeholder="选择供应商" clearable style="width: 120px">
              <el-option
                v-for="option in providerOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="事件类型">
            <el-select v-model="filterForm.event_type" placeholder="选择事件类型" clearable style="width: 150px">
              <el-option
                v-for="option in eventTypeOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="订单号">
            <el-input
              v-model="filterForm.order_no"
              placeholder="输入订单号搜索"
              style="width: 200px"
              clearable
            />
          </el-form-item>

          <el-form-item label="运单号">
            <el-input
              v-model="filterForm.tracking_no"
              placeholder="输入运单号搜索"
              style="width: 200px"
              clearable
            />
          </el-form-item>

          <el-form-item label="时间范围">
            <el-date-picker
              v-model="dateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 350px"
            />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="handleSearch" :loading="loading">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <el-button
        type="success"
        :disabled="selectedRecords.length === 0"
        @click="handleBatchRetry"
        :loading="batchRetryLoading"
      >
        <el-icon><Refresh /></el-icon>
        批量重推 ({{ selectedRecords.length }})
      </el-button>
      
      <el-button
        type="warning"
        @click="showBatchRetryByConditionDialog = true"
      >
        <el-icon><Operation /></el-icon>
        按条件批量重推
      </el-button>

      <el-button
        type="info"
        @click="handleExport"
        :loading="exportLoading"
      >
        <el-icon><Download /></el-icon>
        导出数据
      </el-button>

      <el-button @click="loadData">
        <el-icon><Refresh /></el-icon>
        刷新
      </el-button>
    </div>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        stripe
        border
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column prop="id" label="ID" width="100" show-overflow-tooltip>
          <template #default="{ row }">
            <el-text class="record-id">{{ row.id.substring(0, 8) }}...</el-text>
          </template>
        </el-table-column>

        <el-table-column prop="provider" label="供应商" width="100">
          <template #default="{ row }">
            <el-tag :type="getProviderTagType(row.provider)">
              {{ getProviderLabel(row.provider) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="event_type" label="事件类型" width="120">
          <template #default="{ row }">
            <el-tag v-if="row.event_type" size="small" :type="getEventTypeTagType(row.event_type)">
              {{ getEventTypeLabel(row.event_type) }}
            </el-tag>
            <el-text v-else type="info">-</el-text>
          </template>
        </el-table-column>

        <el-table-column prop="order_no" label="订单号" width="150" show-overflow-tooltip>
          <template #default="{ row }">
            <el-text v-if="row.order_no">{{ row.order_no }}</el-text>
            <el-text v-else type="info">-</el-text>
          </template>
        </el-table-column>

        <el-table-column prop="tracking_no" label="运单号" width="150" show-overflow-tooltip>
          <template #default="{ row }">
            <el-text v-if="row.tracking_no">{{ row.tracking_no }}</el-text>
            <el-text v-else type="info">-</el-text>
          </template>
        </el-table-column>

        <el-table-column prop="received_at" label="接收时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.received_at) }}
          </template>
        </el-table-column>

        <el-table-column label="原始数据" width="200">
          <template #default="{ row }">
            <el-text class="raw-data-preview" @click="showRawDataDialog(row)">
              {{ getRawDataPreview(row.raw_body) }}
            </el-text>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="handleSingleRetry(row)"
              :loading="retryingRecords.has(row.id)"
            >
              重推
            </el-button>
            <el-button
              type="info"
              size="small"
              @click="showDetailDialog(row)"
            >
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[20, 50, 100, 200]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 原始数据查看对话框 -->
    <el-dialog
      v-model="showRawDataDialogVisible"
      title="原始回调数据"
      width="85%"
      top="5vh"
      :before-close="handleCloseRawDataDialog"
      class="raw-data-dialog"
      center
      destroy-on-close
    >
      <div class="raw-data-content">
        <div class="data-tabs">
          <el-tabs v-model="activeDataTab" type="card">
            <el-tab-pane label="原始数据" name="raw">
              <el-input
                v-model="currentRawData"
                type="textarea"
                :rows="20"
                readonly
                placeholder="原始回调数据"
              />
            </el-tab-pane>
            <el-tab-pane label="解码数据" name="decoded">
              <el-input
                v-model="decodedRawData"
                type="textarea"
                :rows="20"
                readonly
                placeholder="解码后的数据"
              />
            </el-tab-pane>
            <el-tab-pane label="格式化JSON" name="formatted" v-if="formattedJsonData">
              <pre class="json-formatted">{{ formattedJsonData }}</pre>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
      <template #footer>
        <el-button @click="showRawDataDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="copyCurrentTabData">复制当前数据</el-button>
      </template>
    </el-dialog>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="showDetailDialogVisible"
      title="回调记录详情"
      width="70%"
    >
      <div v-if="currentRecord" class="detail-content">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="记录ID">{{ currentRecord.id }}</el-descriptions-item>
          <el-descriptions-item label="供应商">{{ getProviderLabel(currentRecord.provider) }}</el-descriptions-item>
          <el-descriptions-item label="事件类型">{{ currentRecord.event_type ? getEventTypeLabel(currentRecord.event_type) : '-' }}</el-descriptions-item>
          <el-descriptions-item label="订单号">{{ currentRecord.order_no || '-' }}</el-descriptions-item>
          <el-descriptions-item label="运单号">{{ currentRecord.tracking_no || '-' }}</el-descriptions-item>
          <el-descriptions-item label="接收时间">{{ formatDateTime(currentRecord.received_at) }}</el-descriptions-item>
          <el-descriptions-item label="处理时间">{{ currentRecord.processed_at ? formatDateTime(currentRecord.processed_at) : '-' }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDateTime(currentRecord.created_at) }}</el-descriptions-item>
        </el-descriptions>

        <div class="parsed-data-section" v-if="currentRecord.parsed_data">
          <h4>解析后数据</h4>
          <pre class="json-content">{{ JSON.stringify(currentRecord.parsed_data, null, 2) }}</pre>
        </div>

        <div class="error-section" v-if="currentRecord.process_error">
          <h4>处理错误</h4>
          <el-alert type="error" :description="currentRecord.process_error" show-icon />
        </div>
      </div>
      <template #footer>
        <el-button @click="showDetailDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="handleSingleRetry(currentRecord)" :loading="retryingRecords.has(currentRecord?.id)">
          重推此记录
        </el-button>
      </template>
    </el-dialog>

    <!-- 按条件批量重推对话框 -->
    <el-dialog
      v-model="showBatchRetryByConditionDialog"
      title="按条件批量重推"
      width="50%"
    >
      <el-form :model="batchRetryConditions" label-width="100px">
        <el-form-item label="供应商">
          <el-select v-model="batchRetryConditions.provider" placeholder="选择供应商" clearable>
            <el-option
              v-for="option in providerOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="事件类型">
          <el-select v-model="batchRetryConditions.event_type" placeholder="选择事件类型" clearable>
            <el-option
              v-for="option in eventTypeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="时间范围">
          <el-date-picker
            v-model="batchRetryDateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>

        <el-alert
          type="warning"
          title="注意"
          description="批量重推将重新处理符合条件的所有回调记录，请确认条件无误后再执行。"
          show-icon
          :closable="false"
        />
      </el-form>
      
      <template #footer>
        <el-button @click="showBatchRetryByConditionDialog = false">取消</el-button>
        <el-button type="primary" @click="handleBatchRetryByCondition" :loading="batchRetryByConditionLoading">
          确认重推
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import {
  Document,
  Check,
  Close,
  TrendCharts,
  Search,
  Refresh,
  Operation,
  Download
} from '@element-plus/icons-vue'
import RawCallbackManagementService, {
  type RawCallbackRecord,
  type RawCallbackStatistics,
  type RawCallbackListParams,
  type BatchRetryByConditionRequest
} from '@/api/rawCallbackApi'

// 响应式数据
const loading = ref(false)
const exportLoading = ref(false)
const batchRetryLoading = ref(false)
const batchRetryByConditionLoading = ref(false)
const retryingRecords = ref(new Set<string>())

// 表格数据
const tableData = ref<RawCallbackRecord[]>([])
const selectedRecords = ref<RawCallbackRecord[]>([])

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 50,
  total: 0
})

// 筛选表单
const filterForm = reactive<RawCallbackListParams>({
  provider: '',
  event_type: '',
  order_no: '',
  tracking_no: '',
  start_time: '',
  end_time: ''
})

// 日期范围
const dateRange = ref<[string, string] | null>(null)

// 统计数据
const statistics = ref<RawCallbackStatistics>({
  total_records: 0,
  success_records: 0,
  failed_records: 0,
  pending_records: 0,
  success_rate: 0,
  provider_stats: {},
  event_type_stats: {},
  daily_stats: []
})

// 对话框状态
const showRawDataDialogVisible = ref(false)
const showDetailDialogVisible = ref(false)
const showBatchRetryByConditionDialog = ref(false)

// 当前数据
const currentRawData = ref('')
const decodedRawData = ref('')
const formattedJsonData = ref('')
const activeDataTab = ref('raw')
const currentRecord = ref<RawCallbackRecord | null>(null)

// 批量重推条件
const batchRetryConditions = reactive<BatchRetryByConditionRequest>({
  provider: '',
  event_type: '',
  start_time: '',
  end_time: ''
})
const batchRetryDateRange = ref<[string, string] | null>(null)

// 选项数据
const providerOptions = computed(() => RawCallbackManagementService.getProviderOptions())
const eventTypeOptions = computed(() => RawCallbackManagementService.getEventTypeOptions(filterForm.provider))

// 监听日期范围变化
watch(dateRange, (newVal) => {
  if (newVal) {
    filterForm.start_time = newVal[0]
    filterForm.end_time = newVal[1]
  } else {
    filterForm.start_time = ''
    filterForm.end_time = ''
  }
})

watch(batchRetryDateRange, (newVal) => {
  if (newVal) {
    batchRetryConditions.start_time = newVal[0]
    batchRetryConditions.end_time = newVal[1]
  } else {
    batchRetryConditions.start_time = ''
    batchRetryConditions.end_time = ''
  }
})

// 监听供应商变化，重置事件类型
watch(() => filterForm.provider, () => {
  filterForm.event_type = ''
})

// 页面加载
onMounted(() => {
  // 设置默认时间范围为最近7天
  const endTime = new Date()
  const startTime = new Date()
  startTime.setDate(startTime.getDate() - 7)

  dateRange.value = [
    startTime.toISOString().slice(0, 19).replace('T', ' '),
    endTime.toISOString().slice(0, 19).replace('T', ' ')
  ]

  loadData()
  loadStatistics()
})

// 加载数据
const loadData = async () => {
  try {
    loading.value = true
    const params: RawCallbackListParams = {
      page: pagination.page,
      page_size: pagination.pageSize,
      ...filterForm
    }

    const result = await RawCallbackManagementService.getRawCallbackRecords(params)
    tableData.value = result.records
    pagination.total = result.total
  } catch (error: any) {
    ElMessage.error(error.message || '加载数据失败')
  } finally {
    loading.value = false
  }
}

// 加载统计数据
const loadStatistics = async () => {
  try {
    const params = {
      start_time: filterForm.start_time,
      end_time: filterForm.end_time,
      provider: filterForm.provider
    }

    const result = await RawCallbackManagementService.getRawCallbackStatistics(params)
    statistics.value = result
  } catch (error: any) {
    console.error('加载统计数据失败:', error)
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  loadData()
  loadStatistics()
}

// 重置
const handleReset = () => {
  Object.assign(filterForm, {
    provider: '',
    event_type: '',
    order_no: '',
    tracking_no: '',
    start_time: '',
    end_time: ''
  })
  dateRange.value = null
  pagination.page = 1
  loadData()
  loadStatistics()
}

// 分页处理
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.page = 1
  loadData()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadData()
}

// 选择处理
const handleSelectionChange = (selection: RawCallbackRecord[]) => {
  selectedRecords.value = selection
}

// 单个重推
const handleSingleRetry = async (record: RawCallbackRecord) => {
  try {
    await ElMessageBox.confirm(
      `确定要重推记录 ${record.id.substring(0, 8)}... 吗？`,
      '确认重推',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    retryingRecords.value.add(record.id)
    await RawCallbackManagementService.retryRawCallback(record.id)

    ElMessage.success('重推成功')
    loadData() // 重新加载数据
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '重推失败')
    }
  } finally {
    retryingRecords.value.delete(record.id)
  }
}

// 批量重推
const handleBatchRetry = async () => {
  if (selectedRecords.value.length === 0) {
    ElMessage.warning('请选择要重推的记录')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要重推选中的 ${selectedRecords.value.length} 条记录吗？`,
      '确认批量重推',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    batchRetryLoading.value = true
    const recordIds = selectedRecords.value.map(record => record.id)
    const result = await RawCallbackManagementService.batchRetryRawCallbacks(recordIds)

    ElNotification({
      title: '批量重推完成',
      message: `总计 ${result.total} 条，成功 ${result.success_count} 条，失败 ${result.failed_count} 条`,
      type: result.failed_count > 0 ? 'warning' : 'success',
      duration: 5000
    })

    if (result.errors.length > 0) {
      console.error('批量重推错误:', result.errors)
    }

    loadData() // 重新加载数据
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '批量重推失败')
    }
  } finally {
    batchRetryLoading.value = false
  }
}

// 按条件批量重推
const handleBatchRetryByCondition = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要按指定条件批量重推回调记录吗？这可能会影响大量数据。',
      '确认批量重推',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    batchRetryByConditionLoading.value = true
    const result = await RawCallbackManagementService.batchRetryByCondition(batchRetryConditions)

    ElNotification({
      title: '按条件批量重推完成',
      message: `总计 ${result.total} 条，成功 ${result.success_count} 条，失败 ${result.failed_count} 条`,
      type: result.failed_count > 0 ? 'warning' : 'success',
      duration: 5000
    })

    if (result.errors.length > 0) {
      console.error('按条件批量重推错误:', result.errors)
    }

    showBatchRetryByConditionDialog.value = false
    loadData() // 重新加载数据
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '按条件批量重推失败')
    }
  } finally {
    batchRetryByConditionLoading.value = false
  }
}

// 导出数据
const handleExport = async () => {
  try {
    exportLoading.value = true
    const blob = await RawCallbackManagementService.exportRawCallbackRecords(filterForm)

    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `raw_callbacks_${new Date().toISOString().slice(0, 10)}.csv`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('导出成功')
  } catch (error: any) {
    ElMessage.error(error.message || '导出失败')
  } finally {
    exportLoading.value = false
  }
}

// 显示原始数据对话框
const showRawDataDialog = (record: RawCallbackRecord) => {
  currentRawData.value = record.raw_body

  // 解码原始数据
  decodedRawData.value = decodeRawData(record.raw_body)

  // 尝试格式化JSON
  formattedJsonData.value = formatJsonData(decodedRawData.value)

  // 重置到原始数据标签页
  activeDataTab.value = 'raw'

  showRawDataDialogVisible.value = true
}

// 关闭原始数据对话框
const handleCloseRawDataDialog = () => {
  showRawDataDialogVisible.value = false
  currentRawData.value = ''
  decodedRawData.value = ''
  formattedJsonData.value = ''
  activeDataTab.value = 'raw'
}

// 复制当前标签页数据
const copyCurrentTabData = async () => {
  try {
    let dataToCopy = ''
    switch (activeDataTab.value) {
      case 'raw':
        dataToCopy = currentRawData.value
        break
      case 'decoded':
        dataToCopy = decodedRawData.value
        break
      case 'formatted':
        dataToCopy = formattedJsonData.value
        break
      default:
        dataToCopy = currentRawData.value
    }

    await navigator.clipboard.writeText(dataToCopy)
    ElMessage.success('已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

// 解码原始数据
const decodeRawData = (rawData: string): string => {
  if (!rawData) return ''

  try {
    // 1. 尝试URL解码
    let decoded = decodeURIComponent(rawData)

    // 2. 如果是表单数据格式，进一步解析
    if (decoded.includes('=') && decoded.includes('&')) {
      // 解析表单数据
      const params = new URLSearchParams(decoded)
      const result: Record<string, string> = {}

      for (const [key, value] of params.entries()) {
        // 对每个参数值再次尝试URL解码
        try {
          result[key] = decodeURIComponent(value)
        } catch {
          result[key] = value
        }
      }

      // 格式化为可读的键值对
      return Object.entries(result)
        .map(([key, value]) => `${key}:\n${value}\n`)
        .join('\n---\n\n')
    }

    return decoded
  } catch (error) {
    // 如果解码失败，返回原始数据
    return rawData
  }
}

// 格式化JSON数据
const formatJsonData = (data: string): string => {
  if (!data) return ''

  try {
    // 尝试解析为JSON
    const parsed = JSON.parse(data)
    return JSON.stringify(parsed, null, 2)
  } catch {
    // 如果不是JSON，尝试从表单数据中提取JSON
    if (data.includes('param:') || data.includes('RequestData:')) {
      const lines = data.split('\n')
      for (const line of lines) {
        if (line.includes(':')) {
          const [, value] = line.split(':', 2)
          if (value && value.trim().startsWith('{')) {
            try {
              const parsed = JSON.parse(value.trim())
              return JSON.stringify(parsed, null, 2)
            } catch {
              continue
            }
          }
        }
      }
    }

    return '' // 不是JSON数据
  }
}

// 显示详情对话框
const showDetailDialog = async (record: RawCallbackRecord) => {
  try {
    currentRecord.value = await RawCallbackManagementService.getRawCallbackRecordById(record.id)
    showDetailDialogVisible.value = true
  } catch (error: any) {
    ElMessage.error(error.message || '获取详情失败')
  }
}

// 工具方法
const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

const getRawDataPreview = (rawData: string) => {
  if (!rawData) return '-'
  return rawData.length > 100 ? rawData.substring(0, 100) + '...' : rawData
}

const getProviderLabel = (provider: string) => {
  const option = providerOptions.value.find(opt => opt.value === provider)
  return option ? option.label : provider
}

const getProviderTagType = (provider: string) => {
  const types: { [key: string]: string } = {
    cainiao: 'primary',
    kuaidi100: 'success',
    yida: 'warning',
    yuntong: 'info',
    kuaidiniao: 'danger'
  }
  return types[provider] || 'default'
}

// 获取事件类型中文标签
const getEventTypeLabel = (eventType: string) => {
  const labelMap: { [key: string]: string } = {
    // 菜鸟事件类型
    'TRANSPORT': '运输中',
    'FINISH_ORDER': '订单完结',
    'PICKUP': '已揽收',
    'DELIVERY': '派送中',
    'SIGNED': '已签收',
    'EXCEPTION': '异常',

    // 快递100事件类型
    'status_200': '成功',
    'status_101': '已下单',
    'status_102': '已揽收',
    'status_103': '运输中',
    'status_104': '派送中',
    'status_105': '已签收',
    'status_106': '异常',

    // 快递鸟事件类型
    'kdn_state_1': '已下单',
    'kdn_state_2': '在途中',
    'kdn_state_3': '已签收',
    'kdn_state_4': '问题件',
    'kdn_state_101': '已下单',
    'kdn_state_102': '已揽收',
    'kdn_state_103': '运输中',
    'kdn_state_104': '派送中',
    'kdn_state_105': '已签收',
    'kdn_state_106': '异常',

    // 易达事件类型
    'yida_push_type_1': '状态推送',
    'yida_push_type_2': '费用推送',
    'yida_push_type_3': '异常推送',

    // 云通事件类型
    'yt_state_100': '下单成功',
    'yt_state_101': '已下单',
    'yt_state_102': '已揽收',
    'yt_state_103': '运输中',
    'yt_state_104': '派送中',
    'yt_state_105': '已签收',
    'yt_state_106': '异常',
    'yt_state_201': '计费成功',
    'yt_state_202': '计费中',
    'yt_state_203': '计费完成',
    'yt_state_301': '已计费',

    // 通用状态（保持原有键名以兼容现有数据）
    'state_1': '已下单',
    'state_2': '在途中',
    'state_3': '已签收',
    'state_4': '问题件',
    'state_100': '下单成功',
    'state_101': '已下单',
    'state_102': '已揽收',
    'state_103': '运输中',
    'state_104': '派送中',
    'state_105': '已签收',
    'state_106': '异常',
    'state_201': '计费成功',
    'state_202': '计费中',
    'state_203': '计费完成',
    'state_301': '已计费',
    'push_type_1': '状态推送',
    'push_type_2': '费用推送',
    'push_type_3': '异常推送'
  }

  return labelMap[eventType] || eventType
}

// 获取事件类型标签颜色
const getEventTypeTagType = (eventType: string) => {
  // 成功状态 - 绿色
  if (eventType.includes('200') || eventType.includes('FINISH') || eventType.includes('SIGNED') ||
      eventType.includes('105') || eventType === 'state_3') {
    return 'success'
  }

  // 进行中状态 - 蓝色
  if (eventType.includes('TRANSPORT') || eventType.includes('PICKUP') || eventType.includes('DELIVERY') ||
      eventType.includes('102') || eventType.includes('103') || eventType.includes('104') ||
      eventType === 'state_2' || eventType.includes('push_type_1')) {
    return 'primary'
  }

  // 异常状态 - 红色
  if (eventType.includes('EXCEPTION') || eventType.includes('106') || eventType === 'state_4' ||
      eventType.includes('push_type_3')) {
    return 'danger'
  }

  // 计费相关 - 橙色
  if (eventType.includes('20') || eventType.includes('30') || eventType.includes('push_type_2')) {
    return 'warning'
  }

  // 默认 - 灰色
  return 'info'
}
</script>

<style scoped>
.raw-callback-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.statistics-cards {
  margin-bottom: 20px;
}

.stat-card {
  position: relative;
  overflow: hidden;
}

.stat-card .el-card__body {
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.stat-icon {
  font-size: 40px;
  color: #409eff;
  opacity: 0.8;
}

.stat-icon.success {
  color: #67c23a;
}

.stat-icon.error {
  color: #f56c6c;
}

.filter-card {
  margin-bottom: 20px;
}

.filter-form .el-form-item {
  margin-bottom: 0;
}

.action-buttons {
  margin-bottom: 20px;
  display: flex;
  gap: 12px;
}

.table-card .el-card__body {
  padding: 0;
}

.pagination-wrapper {
  padding: 20px;
  display: flex;
  justify-content: center;
}

.record-id {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.raw-data-preview {
  cursor: pointer;
  color: #409eff;
  font-size: 12px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.raw-data-preview:hover {
  text-decoration: underline;
}

.raw-data-content {
  margin-bottom: 20px;
}

.detail-content {
  max-height: 60vh;
  overflow-y: auto;
}

.parsed-data-section,
.error-section {
  margin-top: 20px;
}

.parsed-data-section h4,
.error-section h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.json-content {
  background: #f5f7fa;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 12px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  color: #606266;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 300px;
  overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .raw-callback-management {
    padding: 10px;
  }

  .statistics-cards .el-col {
    margin-bottom: 10px;
  }

  .filter-form .el-form-item {
    margin-bottom: 12px;
  }

  .action-buttons {
    flex-wrap: wrap;
  }
}

/* 原始数据对话框样式 */
.raw-data-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.data-tabs {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.data-tabs .el-tabs {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.data-tabs .el-tabs__content {
  flex: 1;
  padding: 0;
  overflow: hidden;
}

.data-tabs .el-tab-pane {
  height: 100%;
}

/* 修复 textarea 滚动条问题 */
.raw-data-content .el-textarea {
  height: 60vh;
}

.raw-data-content .el-textarea__inner {
  height: 60vh !important;
  max-height: 60vh;
  overflow-y: auto !important;
  overflow-x: auto !important;
  resize: none;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.4;
}

/* 强制显示滚动条 */
.raw-data-content .el-textarea__inner::-webkit-scrollbar {
  width: 8px !important;
  height: 8px !important;
}

.raw-data-content .el-textarea__inner::-webkit-scrollbar-track {
  background-color: #f1f1f1;
}

.raw-data-content .el-textarea__inner::-webkit-scrollbar-thumb {
  background-color: #c1c1c1;
  border-radius: 4px;
}

.raw-data-content .el-textarea__inner::-webkit-scrollbar-thumb:hover {
  background-color: #a8a8a8;
}

.json-formatted {
  background-color: #f5f7fa;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 12px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
  color: #2c3e50;
  white-space: pre-wrap;
  word-wrap: break-word;
  height: 60vh;
  overflow-y: auto;
  overflow-x: auto;
  margin: 0;
}

/* JSON格式化区域的滚动条样式 */
.json-formatted::-webkit-scrollbar {
  width: 8px !important;
  height: 8px !important;
}

.json-formatted::-webkit-scrollbar-track {
  background-color: #f1f1f1;
}

.json-formatted::-webkit-scrollbar-thumb {
  background-color: #c1c1c1;
  border-radius: 4px;
}

.json-formatted::-webkit-scrollbar-thumb:hover {
  background-color: #a8a8a8;
}

.raw-data-preview {
  cursor: pointer;
  color: #409eff;
  text-decoration: underline;
  font-family: monospace;
  font-size: 12px;
}

.raw-data-preview:hover {
  color: #66b1ff;
}

/* 原始数据对话框整体样式 */
.raw-data-dialog {
  margin: 0 auto;
}

.raw-data-dialog .el-dialog {
  margin: 5vh auto 5vh;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

.raw-data-dialog .el-dialog__header {
  flex-shrink: 0;
  padding: 20px 20px 10px;
  border-bottom: 1px solid #ebeef5;
}

.raw-data-dialog .el-dialog__body {
  flex: 1;
  max-height: calc(90vh - 120px);
  overflow-y: auto;
  padding: 20px;
}

.raw-data-dialog .el-dialog__footer {
  flex-shrink: 0;
  padding: 10px 20px 20px;
  border-top: 1px solid #ebeef5;
}

/* 确保对话框在可视区域内 */
.raw-data-dialog .el-dialog__wrapper {
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding: 5vh 0;
  overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .raw-data-dialog .el-dialog {
    margin: 2vh auto;
    max-height: 96vh;
    width: 95% !important;
  }

  .raw-data-dialog .el-dialog__body {
    max-height: calc(96vh - 100px);
    padding: 15px;
  }

  .raw-data-dialog .el-dialog__wrapper {
    padding: 2vh 0;
  }
}
</style>
