<template>
  <div class="weight-cache-container">
    <!-- 页面标题和操作按钮 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">
            <el-icon><DataAnalysis /></el-icon>
            重量缓存管理
          </h1>
          <p class="page-description">管理重量档位缓存，监控缓存性能，优化查价效率</p>
        </div>
        <div class="action-buttons">
          <el-button type="primary" @click="showWarmupDialog = true" :icon="Upload">
            缓存预热
          </el-button>
          <el-button type="warning" @click="cleanupCache" :icon="Delete">
            清理无效缓存
          </el-button>
          <el-button @click="refreshData" :icon="Refresh" :loading="refreshing">
            刷新数据
          </el-button>
        </div>
      </div>
    </div>

    <!-- 🚀 正确布局：按供应商分组，每个供应商下列出快递公司 -->
    <div class="provider-overview" v-loading="loading">
      <div class="overview-header">
        <h3>缓存概览 - 按供应商分组</h3>
        <div class="header-stats" v-if="quickStats">
          <span class="stat-item">
            <el-icon><DataBoard /></el-icon>
            {{ quickStats.total_providers }} 个供应商
          </span>
          <span class="stat-item">
            <el-icon><SuccessFilled /></el-icon>
            {{ formatNumber(quickStats.total_cache_entries) }} 条缓存
          </span>
          <span class="stat-item">
            <el-icon><Trophy /></el-icon>
            命中率 {{ formatPercent(quickStats.cache_hit_rate) }}
          </span>
        </div>
      </div>

      <!-- 供应商分组列表 -->
      <div class="provider-groups">
        <div
          v-for="provider in providerCompanyGroups"
          :key="provider.provider"
          class="provider-group"
        >
          <!-- 供应商标题 -->
          <div class="provider-header">
            <el-tag :type="getProviderTagType(provider.provider)" size="large">
              {{ getProviderName(provider.provider) }}
            </el-tag>
            <span class="provider-stats">
              {{ provider.companies.length }} 个快递公司，
              总缓存 {{ formatNumber(provider.totalCache) }} 条，
              成功 {{ formatNumber(provider.totalValid) }} 条
            </span>
          </div>

          <!-- 快递公司列表 -->
          <div class="company-list">
            <div
              v-for="company in provider.companies"
              :key="company.expressCode"
              class="company-item"
            >
              <!-- 快递公司信息 -->
              <div class="company-info">
                <div class="company-name">
                  <span class="express-code">{{ company.expressCode }}</span>
                  <span class="express-name">{{ getExpressName(company.expressCode) }}</span>
                </div>
              </div>

              <!-- 缓存统计 -->
              <div class="cache-stats">
                <div class="stat-item">
                  <span class="stat-label">总缓存</span>
                  <span class="stat-value">{{ formatNumber(company.totalCache) }}</span>
                </div>
                <div class="stat-item success">
                  <span class="stat-label">成功</span>
                  <span class="stat-value">{{ formatNumber(company.validCache) }}</span>
                </div>
                <div class="stat-item warning">
                  <span class="stat-label">失败</span>
                  <span class="stat-value">{{ formatNumber(company.invalidCache) }}</span>
                </div>
                <div class="stat-item info">
                  <span class="stat-label">命中</span>
                  <span class="stat-value">{{ formatNumber(company.hitCount) }}</span>
                </div>
              </div>

              <!-- 操作按钮 -->
              <div class="action-section">
                <el-button
                  type="primary"
                  size="small"
                  @click="viewCompanyDetails(provider.provider, company.expressCode)"
                  :icon="View"
                >
                  {{ getExpressName(company.expressCode) }}缓存明细
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="!loading && providerCompanyGroups.length === 0" class="empty-state">
        <el-empty description="暂无供应商缓存数据" />
      </div>
    </div>

    <!-- 🚀 缓存详情对话框 -->
    <CacheDetailDialog
      v-model="showDetailDialog"
      :provider="selectedProvider"
      :express-code="selectedExpressCode"
      :company-stats="selectedCompanyStats"
      @refresh="refreshData"
    />

    <!-- 缓存预热对话框 -->
    <CacheWarmupDialog
      v-model="showWarmupDialog"
      @confirm="handleWarmup"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  DataAnalysis, Upload, Delete, Refresh, DataBoard,
  SuccessFilled, TrendCharts, Trophy, View, Search
} from '@element-plus/icons-vue'
import {
  weightCacheApi,
  type QuickStatsResponse,
  type CacheWarmupRequest,
  type CacheOverviewData,
  type ProviderGroup
} from '@/api/weightCacheApi'
import { ErrorHandler } from '@/utils/errorHandler'

// 导入组件
import CacheWarmupDialog from './components/CacheWarmupDialog.vue'
import CacheDetailDialog from './components/CacheDetailDialog.vue'

// 响应式数据
const loading = ref(false)
const showWarmupDialog = ref(false)
const showDetailDialog = ref(false)
const refreshing = ref(false)

// 搜索和筛选
const searchKeyword = ref('')
const currentPage = ref(1)
const pageSize = ref(50)
const totalCount = ref(0)

// 数据
const quickStats = ref<QuickStatsResponse | null>(null)
const providerGroups = ref<ProviderGroup[]>([])
const selectedProvider = ref('')
const selectedExpressCode = ref('')
const selectedCompanyStats = ref({
  totalCache: 0,
  validCache: 0,
  invalidCache: 0,
  hitCount: 0
})

// 🚀 转换后端数据为前端显示格式
const providerCompanyGroups = computed(() => {
  return providerGroups.value.map(provider => ({
    provider: provider.provider,
    totalCache: provider.total_cache_count,
    totalValid: provider.valid_cache_count,
    companies: (provider.express_companies || []).map(company => ({
      expressCode: company.express_code,
      totalCache: company.cache_count,
      validCache: company.valid_cache_count,
      invalidCache: (company.cache_count || 0) - (company.valid_cache_count || 0),
      hitCount: company.cache_hit_count
    }))
  }))
})

// 🚀 加载快速统计数据
const loadQuickStats = async () => {
  try {
    const response = await weightCacheApi.getQuickStats()
    if (response.success) {
      quickStats.value = response.data
      console.log('✅ 快速统计数据加载成功:', response.data)
    } else {
      throw new Error(response.message || '获取快速统计失败')
    }
  } catch (error) {
    ErrorHandler.handleApiError(error, '快速统计数据加载')
  }
}

// 🚀 加载供应商分组数据（使用优化后的API）
const loadCacheData = async () => {
  loading.value = true
  try {
    console.log('🔄 正在加载供应商分组数据...')

    const response = await weightCacheApi.getProviderGroupedOverviewOptimized()

    if (response.success) {
      // 直接使用供应商分组数据，不需要前端再次分组
      providerGroups.value = response.data || []

      console.log('✅ 供应商分组数据加载成功:', {
        providers: providerGroups.value.length,
        totalCompanies: providerGroups.value.reduce((sum, p) => sum + (p.express_companies?.length || 0), 0)
      })
    } else {
      throw new Error(response.message || '获取供应商分组数据失败')
    }
  } catch (error) {
    ErrorHandler.handleApiError(error, '供应商分组数据加载')
  } finally {
    loading.value = false
  }
}

// 🚀 刷新所有数据
const refreshData = async () => {
  if (refreshing.value) return

  refreshing.value = true
  try {
    await Promise.all([
      loadQuickStats(),
      loadCacheData()
    ])
    ErrorHandler.handleSuccess('数据刷新成功')
  } catch (error) {
    ErrorHandler.handleApiError(error, '数据刷新')
  } finally {
    refreshing.value = false
  }
}

// 🚀 清理无效缓存
const cleanupCache = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清理所有无效缓存吗？此操作将删除过期和无效的缓存数据。',
      '确认清理',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await weightCacheApi.cleanupInvalidCache()
    ErrorHandler.handleSuccess('无效缓存清理成功')
    await loadQuickStats()
  } catch (error: any) {
    if (error !== 'cancel') {
      ErrorHandler.handleApiError(error, '清理无效缓存')
    }
  }
}

// 🚀 处理缓存预热
const handleWarmup = async (params: CacheWarmupRequest) => {
  try {
    await weightCacheApi.warmupCache(params)
    ErrorHandler.handleSuccess('缓存预热已启动')
    showWarmupDialog.value = false
    
    // 延迟刷新数据，等待预热开始
    setTimeout(() => {
      loadQuickStats()
    }, 2000)
  } catch (error) {
    ErrorHandler.handleApiError(error, '缓存预热')
  }
}

// 🚀 查看快递公司缓存明细
const viewCompanyDetails = (provider: string, expressCode: string) => {
  // 从供应商分组数据中找到对应的快递公司信息
  const providerGroup = providerGroups.value.find(p => p.provider === provider)
  const companyInfo = providerGroup?.express_companies?.find(c => c.express_code === expressCode)

  if (companyInfo) {
    // 设置选中的供应商和快递公司信息
    selectedProvider.value = provider
    selectedExpressCode.value = expressCode
    selectedCompanyStats.value = {
      totalCache: companyInfo.cache_count,
      validCache: companyInfo.valid_cache_count,
      invalidCache: (companyInfo.cache_count || 0) - (companyInfo.valid_cache_count || 0),
      hitCount: companyInfo.cache_hit_count
    }
    showDetailDialog.value = true
  } else {
    ElMessage.warning('暂无该快递公司的缓存数据')
  }
}

// 工具函数（添加安全检查）
const formatNumber = (num: number | undefined | null): string => {
  if (num === undefined || num === null) return '0'
  return num.toLocaleString()
}

const formatPercent = (rate: number | undefined | null): string => {
  if (rate === undefined || rate === null) return '0%'
  return `${Math.round(rate * 100)}%`
}

const getProviderTagType = (provider: string): string => {
  const tagTypes: Record<string, string> = {
    'kuaidi100': 'primary',
    'cainiao': 'success',
    'yida': 'warning',
    'yuntong': 'info'
  }
  return tagTypes[provider] || 'default'
}

const getProviderName = (provider: string): string => {
  const names: Record<string, string> = {
    'kuaidi100': '快递100',
    'cainiao': '菜鸟',
    'yida': '易达',
    'yuntong': '云通'
  }
  return names[provider] || provider
}

const getExpressName = (expressCode: string): string => {
  const expressNames: Record<string, string> = {
    'SF': '顺丰速运',
    'YD': '韵达速递',
    'STO': '申通快递',
    'YTO': '圆通速递',
    'ZTO': '中通快递',
    'HTKY': '百世快递',
    'DBL': '德邦快递',
    'JD': '京东快递'
  }
  return expressNames[expressCode] || expressCode
}

const formatPrice = (price: number | undefined | null): string => {
  if (price === undefined || price === null) return '0.00'
  return price.toFixed(2)
}

const formatDateTime = (datetime: string | undefined | null): string => {
  if (!datetime) return '未知'
  return new Date(datetime).toLocaleString('zh-CN')
}

const getProgressColor = (rate: number): string => {
  if (rate >= 80) return '#67c23a'
  if (rate >= 60) return '#e6a23c'
  return '#f56c6c'
}

// 组件挂载时加载数据
onMounted(() => {
  refreshData()
})
</script>

<style scoped lang="scss">
.weight-cache-container {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;

  .page-header {
    background: #fff;
    border-radius: 8px;
    padding: 24px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title-section {
        .page-title {
          display: flex;
          align-items: center;
          gap: 12px;
          margin: 0 0 8px 0;
          font-size: 24px;
          font-weight: 600;
          color: #303133;

          .el-icon {
            color: #409eff;
          }
        }

        .page-description {
          margin: 0;
          color: #909399;
          font-size: 14px;
        }
      }

      .action-buttons {
        display: flex;
        gap: 12px;
      }
    }
  }

  // 🚀 正确布局样式 - 供应商分组
  .provider-overview {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;

    .overview-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 24px;
      border-bottom: 1px solid #e4e7ed;
      background: #f8f9fa;

      h3 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: #303133;
      }

      .header-stats {
        display: flex;
        gap: 20px;

        .stat-item {
          display: flex;
          align-items: center;
          gap: 6px;
          font-size: 14px;
          color: #606266;

          .el-icon {
            color: #409eff;
          }
        }
      }
    }

    .provider-groups {
      padding: 20px;

      .provider-group {
        margin-bottom: 24px;
        border: 1px solid #e4e7ed;
        border-radius: 8px;
        overflow: hidden;

        &:last-child {
          margin-bottom: 0;
        }

        .provider-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 16px 20px;
          background: #f8f9fa;
          border-bottom: 1px solid #e4e7ed;

          .provider-stats {
            font-size: 14px;
            color: #606266;
          }
        }

        .company-list {
          .company-item {
            display: flex;
            align-items: center;
            padding: 16px 20px;
            border-bottom: 1px solid #f0f0f0;
            transition: all 0.3s ease;

            &:hover {
              background: #f8f9fa;
            }

            &:last-child {
              border-bottom: none;
            }

            .company-info {
              flex: 0 0 200px;

              .company-name {
                display: flex;
                flex-direction: column;
                gap: 4px;

                .express-code {
                  font-weight: 600;
                  color: #303133;
                  font-size: 14px;
                }

                .express-name {
                  font-size: 12px;
                  color: #909399;
                }
              }
            }

            .cache-stats {
              flex: 1;
              display: flex;
              gap: 30px;
              padding: 0 20px;

              .stat-item {
                text-align: center;

                .stat-label {
                  display: block;
                  font-size: 12px;
                  color: #909399;
                  margin-bottom: 4px;
                }

                .stat-value {
                  display: block;
                  font-size: 16px;
                  font-weight: 600;
                  color: #303133;
                }

                &.success .stat-value {
                  color: #67c23a;
                }

                &.warning .stat-value {
                  color: #e6a23c;
                }

                &.info .stat-value {
                  color: #409eff;
                }
              }
            }

            .action-section {
              flex: 0 0 200px;
              text-align: right;
            }
          }
        }
      }
    }

    .empty-state {
      padding: 60px 20px;
      text-align: center;
    }
  }

  // 响应式设计
  @media (max-width: 1200px) {
    .company-item {
      flex-direction: column !important;
      align-items: stretch !important;
      gap: 16px;

      .company-info,
      .cache-stats,
      .action-section {
        flex: none !important;
        padding: 0 !important;
      }

      .cache-stats {
        justify-content: space-around;
      }

      .action-section {
        text-align: center !important;
      }
    }
  }

  @media (max-width: 768px) {
    padding: 10px;

    .page-header .header-content {
      flex-direction: column;
      gap: 16px;
      text-align: center;
    }

    .overview-header {
      flex-direction: column !important;
      gap: 12px;
      text-align: center;

      .header-stats {
        flex-direction: column;
        gap: 8px;
      }
    }

    .provider-groups {
      padding: 10px;

      .provider-header {
        flex-direction: column !important;
        gap: 8px;
        text-align: center;
      }

      .cache-stats {
        flex-direction: column !important;
        gap: 12px !important;
      }
    }
  }
}
</style>
