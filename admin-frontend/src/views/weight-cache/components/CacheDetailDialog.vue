<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`${getProviderName(provider)} - ${getExpressName(expressCode)} 缓存详情`"
    width="1200px"
    :before-close="handleClose"
  >
    <div class="cache-detail">
      <!-- 基本信息 -->
      <div class="detail-section">
        <h4>基本信息</h4>
        <el-descriptions :column="3" border>
          <el-descriptions-item label="供应商">
            <el-tag :type="getProviderTagType(provider)">
              {{ getProviderName(provider) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="快递公司">
            <div>
              <div class="express-code">{{ expressCode }}</div>
              <div class="express-name">{{ getExpressName(expressCode) }}</div>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="总缓存数">
            <span class="stat-value">{{ formatNumber(companyStats.totalCache) }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="成功缓存">
            <span class="stat-value success">{{ formatNumber(companyStats.validCache) }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="失败缓存">
            <span class="stat-value warning">{{ formatNumber(companyStats.invalidCache) }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="命中次数">
            <span class="stat-value info">{{ formatNumber(companyStats.hitCount) }}</span>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 线路和价格明细表格 -->
      <div class="detail-section">
        <div class="section-header">
          <h4>线路和价格明细</h4>
          <div class="header-actions">
            <!-- 🚀 新增：状态筛选器 -->
            <el-select
              v-model="filterStatus"
              placeholder="筛选状态"
              style="width: 140px; margin-right: 12px;"
            >
              <el-option label="全部记录" value="all" />
              <el-option label="成功缓存" value="success">
                <span style="color: #67c23a;">✅ 成功缓存</span>
              </el-option>
              <el-option label="失败记录" value="failed">
                <span style="color: #f56c6c;">❌ 失败记录</span>
              </el-option>
            </el-select>

            <el-input
              v-model="searchKeyword"
              placeholder="搜索线路..."
              :prefix-icon="Search"
              clearable
              style="width: 300px; margin-right: 12px;"
            />
            <el-button @click="loadCacheDetails" :icon="Refresh" :loading="loading">
              刷新
            </el-button>
            <el-button
              @click="clearProviderCompanyCache"
              :icon="Delete"
              type="danger"
              :loading="clearingCache"
            >
              清除该快递公司所有缓存
            </el-button>
          </div>
        </div>

        <!-- 🚀 新增：筛选统计信息 -->
        <div class="filter-stats" v-if="cacheDetails.length > 0">
          <div class="stats-item">
            <span class="label">总记录:</span>
            <el-tag size="small">{{ cacheDetails.length }}</el-tag>
          </div>
          <div class="stats-item">
            <span class="label">当前显示:</span>
            <el-tag size="small" type="info">{{ filteredCacheDetails.length }}</el-tag>
          </div>
          <div class="stats-item">
            <span class="label">成功缓存:</span>
            <el-tag size="small" type="success">{{ successCount }}</el-tag>
          </div>
          <div class="stats-item">
            <span class="label">失败记录:</span>
            <el-tag size="small" type="danger">{{ failedCount }}</el-tag>
          </div>
        </div>

        <el-table
          :data="filteredCacheDetails"
          :loading="loading"
          stripe
          border
          height="400"
          empty-text="暂无缓存明细数据"
        >
          <el-table-column prop="route" label="线路" width="200">
            <template #default="{ row }">
              <el-tag size="small" type="info">{{ row.route }}</el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="weightKg" label="重量档位" width="100">
            <template #default="{ row }">
              <el-tag size="small">{{ row.weightKg }}kg</el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="avgPrice" label="缓存价格" width="120">
            <template #default="{ row }">
              <span class="price">¥{{ formatPrice(row.avgPrice) }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="minPrice" label="最低价格" width="120">
            <template #default="{ row }">
              <span class="price-min">¥{{ formatPrice(row.minPrice) }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="maxPrice" label="最高价格" width="120">
            <template #default="{ row }">
              <span class="price-max">¥{{ formatPrice(row.maxPrice) }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="cacheCount" label="缓存次数" width="100">
            <template #default="{ row }">
              <span class="cache-count">{{ formatNumber(row.cacheCount) }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="cacheHitCount" label="命中次数" width="100">
            <template #default="{ row }">
              <span class="hit-count">{{ formatNumber(row.cacheHitCount) }}</span>
            </template>
          </el-table-column>

          <el-table-column label="命中率" width="120">
            <template #default="{ row }">
              <div class="hit-rate-cell">
                <el-progress
                  :percentage="calculateHitRate(row)"
                  :color="getProgressColor(calculateHitRate(row))"
                  :show-text="false"
                  style="margin-bottom: 4px;"
                />
                <span class="hit-rate-text">{{ calculateHitRate(row) }}%</span>
              </div>
            </template>
          </el-table-column>

          <!-- 🚀 新增：失败统计列 -->
          <el-table-column label="失败统计" width="120">
            <template #default="{ row }">
              <div class="failure-stats">
                <div class="failure-count">
                  <span class="label">失败:</span>
                  <el-tag
                    :type="(row.failed_queries || 0) > 0 ? 'danger' : 'success'"
                    size="small"
                  >
                    {{ row.failed_queries || 0 }}
                  </el-tag>
                </div>
                <div class="total-count">
                  <span class="label">总计:</span>
                  <span class="value">{{ row.total_queries || 0 }}</span>
                </div>
              </div>
            </template>
          </el-table-column>

          <!-- 🚀 新增：失败原因列 -->
          <el-table-column label="失败原因" width="300">
            <template #default="{ row }">
              <div class="failure-reasons">
                <div v-if="!row.failure_reasons || row.failure_reasons.length === 0" class="no-failures">
                  <el-tag type="success" size="small">无失败记录</el-tag>
                </div>
                <div v-else class="failure-list">
                  <div
                    v-for="(reason, index) in row.failure_reasons"
                    :key="index"
                    class="failure-item"
                  >
                    <el-tooltip
                      :content="`错误信息: ${reason.error_message}\n出现次数: ${reason.count}\n最后出现: ${formatDateTime(reason.last_occurred)}\n来源: ${reason.source}`"
                      placement="top"
                      raw-content
                    >
                      <div class="failure-summary">
                        <el-tag type="danger" size="small">
                          {{ reason.count }}次
                        </el-tag>
                        <span class="error-preview">
                          {{ truncateErrorMessage(reason.error_message) }}
                        </span>
                      </div>
                    </el-tooltip>
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="lastUpdateTime" label="最后更新" width="160">
            <template #default="{ row }">
              <span class="update-time">{{ formatDateTime(row.lastUpdateTime) }}</span>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="120" fixed="right">
            <template #default="{ row }">
              <el-button size="small" @click="invalidateSpecificCache(row)" type="warning">
                失效
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[20, 50, 100]"
            :total="totalCount"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="refreshAll">刷新所有数据</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Delete } from '@element-plus/icons-vue'
import {
  weightCacheApi,
  type CacheOverviewData,
  type CacheOverviewRequest,
  type FailureReasonInfo
} from '@/api/weightCacheApi'
import { ErrorHandler } from '@/utils/errorHandler'

// Props
interface Props {
  modelValue: boolean
  provider: string
  expressCode: string
  companyStats: {
    totalCache: number
    validCache: number
    invalidCache: number
    hitCount: number
  }
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  refresh: []
}>()

// 响应式数据
const loading = ref(false)
const clearingCache = ref(false)
const searchKeyword = ref('')
const currentPage = ref(1)
const pageSize = ref(50)
const totalCount = ref(0)
const cacheDetails = ref<CacheOverviewData[]>([])

// 🚀 新增：筛选状态
const filterStatus = ref<'all' | 'success' | 'failed'>('all')

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 🚀 更新：支持按成功/失败状态筛选的计算属性
const filteredCacheDetails = computed(() => {
  let filtered = cacheDetails.value

  // 按状态筛选
  if (filterStatus.value === 'success') {
    filtered = filtered.filter(item => (item.failed_queries || 0) === 0)
  } else if (filterStatus.value === 'failed') {
    filtered = filtered.filter(item => (item.failed_queries || 0) > 0)
  }

  // 按关键词筛选
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(item =>
      item.route.toLowerCase().includes(keyword)
    )
  }

  return filtered
})

// 🚀 新增：统计计算属性
const successCount = computed(() => {
  return cacheDetails.value.filter(item => (item.failed_queries || 0) === 0).length
})

const failedCount = computed(() => {
  return cacheDetails.value.filter(item => (item.failed_queries || 0) > 0).length
})

// 方法
const handleClose = () => {
  dialogVisible.value = false
  resetData()
}

const resetData = () => {
  searchKeyword.value = ''
  filterStatus.value = 'all'  // 🚀 重置筛选状态
  currentPage.value = 1
  cacheDetails.value = []
  totalCount.value = 0
}

// 🚀 加载缓存明细数据（包含失败原因）
const loadCacheDetails = async () => {
  if (!props.provider || !props.expressCode) return

  loading.value = true
  try {
    const params = {
      provider: props.provider,
      express_code: props.expressCode,
      from_province: props.fromProvince,
      to_province: props.toProvince,
      weight_kg: props.weightKg,
      page: currentPage.value,
      page_size: pageSize.value
    }

    // 🚀 使用新的缓存详情API（包含失败原因）
    const response = await weightCacheApi.getCacheDetails(params)

    if (response.success) {
      // 🔧 修复：使用正确的响应字段名 records 而不是 routes
      cacheDetails.value = response.data.records || []
      totalCount.value = response.data.total || 0

      console.log('✅ 缓存明细数据加载成功（含失败原因）:', {
        provider: props.provider,
        expressCode: props.expressCode,
        records: cacheDetails.value.length,
        total: totalCount.value,
        hasFailureReasons: cacheDetails.value.some(item => item.failure_reasons && item.failure_reasons.length > 0)
      })
    } else {
      throw new Error(response.message || '获取缓存明细失败')
    }
  } catch (error) {
    ErrorHandler.handleApiError(error, '缓存明细数据加载')
  } finally {
    loading.value = false
  }
}

// 🚀 清除该供应商+快递公司的所有缓存
const clearProviderCompanyCache = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要清除 ${getProviderName(props.provider)} - ${getExpressName(props.expressCode)} 的所有缓存吗？此操作不可恢复！`,
      '确认清除',
      {
        confirmButtonText: '确定清除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    clearingCache.value = true

    // 调用清除API（需要后端支持）
    await weightCacheApi.clearProviderCompanyCache({
      provider: props.provider,
      expressCode: props.expressCode
    })

    ErrorHandler.handleSuccess('该快递公司的所有缓存已清除')
    emit('refresh')
    handleClose()
  } catch (error: any) {
    if (error !== 'cancel') {
      ErrorHandler.handleApiError(error, '清除缓存')
    }
  } finally {
    clearingCache.value = false
  }
}

// 🚀 失效特定缓存
const invalidateSpecificCache = async (row: CacheOverviewData) => {
  try {
    await ElMessageBox.confirm(
      `确定要使 ${row.route} (${row.weightKg}kg) 的缓存失效吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const [fromProvince, toProvince] = row.route.split('->')

    await weightCacheApi.invalidateCache({
      fromProvince: fromProvince.trim(),
      toProvince: toProvince.trim(),
      provider: props.provider,
      expressCode: props.expressCode,
      weight: row.weightKg || 0
    })

    ErrorHandler.handleSuccess('缓存已失效')
    loadCacheDetails()
  } catch (error: any) {
    if (error !== 'cancel') {
      ErrorHandler.handleApiError(error, '使缓存失效')
    }
  }
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  loadCacheDetails()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  loadCacheDetails()
}

const refreshAll = () => {
  loadCacheDetails()
  emit('refresh')
}

// 工具函数（添加安全检查）
const formatNumber = (num: number | string | undefined | null): string => {
  if (num === undefined || num === null) return '0'
  const n = typeof num === 'string' ? parseFloat(num) : num
  return isNaN(n) ? '0' : n.toLocaleString()
}

const formatPrice = (price: number | string | undefined | null): string => {
  if (price === undefined || price === null) return '0.00'
  const p = typeof price === 'string' ? parseFloat(price) : price
  return isNaN(p) ? '0.00' : p.toFixed(2)
}

const formatDateTime = (datetime: string | undefined | null): string => {
  if (!datetime) return '未知'
  return new Date(datetime).toLocaleString('zh-CN')
}

const calculateHitRate = (row: CacheOverviewData): number => {
  if (!row.cacheCount || row.cacheCount === 0) return 0
  return Math.round(((row.cacheHitCount || 0) / row.cacheCount) * 100)
}

const getProgressColor = (rate: number): string => {
  if (rate >= 80) return '#67c23a'
  if (rate >= 60) return '#e6a23c'
  return '#f56c6c'
}

// 🚀 截断错误消息，用于表格显示
const truncateErrorMessage = (message: string, maxLength: number = 50): string => {
  if (!message) return '未知错误'
  if (message.length <= maxLength) return message
  return message.substring(0, maxLength) + '...'
}

const getExpressName = (expressCode: string): string => {
  const expressNames: Record<string, string> = {
    'SF': '顺丰速运',
    'YD': '韵达速递',
    'STO': '申通快递',
    'YTO': '圆通速递',
    'ZTO': '中通快递',
    'HTKY': '百世快递',
    'DBL': '德邦快递',
    'JD': '京东快递',
    'JT': '极兔快递'
  }
  return expressNames[expressCode] || expressCode
}

const getProviderTagType = (provider: string): string => {
  const tagTypes: Record<string, string> = {
    'kuaidi100': 'primary',
    'cainiao': 'success',
    'yida': 'warning',
    'yuntong': 'info'
  }
  return tagTypes[provider] || 'default'
}

const getProviderName = (provider: string): string => {
  const names: Record<string, string> = {
    'kuaidi100': '快递100',
    'cainiao': '菜鸟',
    'yida': '易达',
    'yuntong': '云通'
  }
  return names[provider] || provider
}

// 监听对话框打开，加载数据
watch(() => props.modelValue, (newVal) => {
  if (newVal && props.provider && props.expressCode) {
    loadCacheDetails()
  }
})
</script>

<style scoped lang="scss">
.cache-detail {
  .detail-section {
    margin-bottom: 24px;

    h4 {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      border-bottom: 2px solid #409eff;
      padding-bottom: 8px;
    }

    .express-code {
      font-weight: 500;
      color: #303133;
    }

    .express-name {
      font-size: 12px;
      color: #909399;
      margin-top: 2px;
    }

    .stat-card {
      text-align: center;
      padding: 20px;
      background: #f8f9fa;
      border-radius: 8px;
      border: 1px solid #e9ecef;

      .stat-value {
        font-size: 24px;
        font-weight: 600;
        color: #409eff;
        margin-bottom: 8px;
      }

      .stat-label {
        font-size: 14px;
        color: #909399;
      }
    }

    .action-buttons {
      display: flex;
      gap: 12px;
      flex-wrap: wrap;
    }
  }

  .price {
    font-weight: 500;
    color: #67c23a;
    font-size: 16px;
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h4 {
      margin: 0;
      border-bottom: none;
      padding-bottom: 0;
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: 12px;
    }
  }

  .stat-value {
    font-weight: bold;
    font-size: 16px;

    &.success {
      color: #67c23a;
    }

    &.warning {
      color: #e6a23c;
    }

    &.info {
      color: #409eff;
    }
  }

  .price-min {
    font-weight: bold;
    color: #909399;
    font-size: 14px;
  }

  .price-max {
    font-weight: bold;
    color: #f56c6c;
    font-size: 14px;
  }

  .cache-count {
    font-weight: bold;
    color: #409eff;
  }

  .hit-count {
    font-weight: bold;
    color: #67c23a;
  }

  .hit-rate-cell {
    display: flex;
    flex-direction: column;
    align-items: center;

    .hit-rate-text {
      font-size: 12px;
      color: #606266;
      margin-top: 2px;
    }
  }

  .update-time {
    font-size: 12px;
    color: #909399;
  }

  // 🚀 失败统计样式
  .failure-stats {
    display: flex;
    flex-direction: column;
    gap: 4px;
    font-size: 12px;

    .failure-count, .total-count {
      display: flex;
      align-items: center;
      gap: 4px;

      .label {
        color: #909399;
        font-size: 11px;
      }

      .value {
        font-weight: 500;
        color: #606266;
      }
    }
  }

  // 🚀 失败原因样式
  .failure-reasons {
    .no-failures {
      display: flex;
      justify-content: center;
    }

    .failure-list {
      display: flex;
      flex-direction: column;
      gap: 4px;
      max-height: 80px;
      overflow-y: auto;

      .failure-item {
        .failure-summary {
          display: flex;
          align-items: center;
          gap: 6px;
          padding: 2px 0;
          cursor: pointer;

          &:hover {
            background-color: #f5f7fa;
            border-radius: 4px;
            padding: 2px 4px;
          }

          .error-preview {
            font-size: 11px;
            color: #606266;
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }
  }

  // 🚀 筛选统计信息样式
  .filter-stats {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 12px 16px;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    margin-bottom: 16px;
    font-size: 13px;

    .stats-item {
      display: flex;
      align-items: center;
      gap: 6px;

      .label {
        color: #606266;
        font-weight: 500;
      }
    }
  }

  .pagination-wrapper {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }
}

.dialog-footer {
  text-align: right;
}

// 响应式设计
@media (max-width: 768px) {
  .cache-detail {
    .detail-section {
      .el-row {
        flex-direction: column;
        gap: 12px;
      }

      .action-buttons {
        flex-direction: column;
      }
    }
  }
}
</style>
