<template>
  <div class="cache-overview-optimized">
    <!-- 搜索和筛选 -->
    <div class="search-filters">
      <el-row :gutter="20" align="middle">
        <el-col :span="8">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索路线、供应商或快递公司..."
            :prefix-icon="Search"
            clearable
            @input="handleSearch"
          />
        </el-col>
        <el-col :span="6">
          <el-select v-model="selectedProvider" placeholder="选择供应商" clearable @change="handleProviderChange">
            <el-option
              v-for="option in providerOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-select v-model="pageSize" placeholder="每页显示" @change="handlePageSizeChange">
            <el-option label="20条/页" :value="20" />
            <el-option label="50条/页" :value="50" />
            <el-option label="100条/页" :value="100" />
            <el-option label="200条/页" :value="200" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <div class="action-buttons">
            <el-button @click="refreshData" :icon="Refresh" :loading="loading">
              刷新
            </el-button>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 🚀 分页数据表格 -->
    <div class="data-table">
      <el-table
        :data="tableData"
        :loading="loading"
        stripe
        border
        height="600"
        @sort-change="handleSortChange"
        empty-text="暂无数据"
      >
        <el-table-column prop="route" label="路线" width="200" sortable="custom">
          <template #default="{ row }">
            <div class="route-info">
              <el-tag size="small" type="info">{{ row.route }}</el-tag>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="provider" label="供应商" width="120" sortable="custom">
          <template #default="{ row }">
            <el-tag :type="getProviderTagType(row.provider)" size="small">
              {{ getProviderName(row.provider) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="expressCode" label="快递公司" width="120" sortable="custom">
          <template #default="{ row }">
            <div class="express-info">
              <span class="express-code">{{ row.expressCode }}</span>
              <div class="express-name">{{ getExpressName(row.expressCode) }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="weightKg" label="重量档位" width="100" sortable="custom">
          <template #default="{ row }">
            <el-tag size="small">{{ row.weightKg }}kg</el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="cacheCount" label="缓存数量" width="100" sortable="custom">
          <template #default="{ row }">
            <span class="cache-count">{{ formatNumber(row.cacheCount) }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="validCacheCount" label="有效缓存" width="100" sortable="custom">
          <template #default="{ row }">
            <span class="valid-count">{{ formatNumber(row.validCacheCount) }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="cacheHitCount" label="命中次数" width="100" sortable="custom">
          <template #default="{ row }">
            <span class="hit-count">{{ formatNumber(row.cacheHitCount) }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="avgPrice" label="平均价格" width="100" sortable="custom">
          <template #default="{ row }">
            <span class="price">¥{{ formatPrice(row.avgPrice) }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="lastUpdateTime" label="最后更新" width="160" sortable="custom">
          <template #default="{ row }">
            <span class="update-time">{{ formatDateTime(row.lastUpdateTime) }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewDetails(row)" :icon="View">
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[20, 50, 100, 200]"
          :total="totalCount"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handlePageSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 详情对话框 -->
    <CacheDetailDialog
      v-model="showDetailDialog"
      :cache-info="selectedCacheInfo"
      @refresh="refreshData"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Refresh, View } from '@element-plus/icons-vue'
import { 
  weightCacheApi, 
  type CacheOverviewRequest, 
  type CacheOverviewData 
} from '@/api/weightCacheApi'
import { ErrorHandler } from '@/utils/errorHandler'
import CacheDetailDialog from './CacheDetailDialog.vue'

// Emits
const emit = defineEmits<{
  refresh: []
}>()

// 响应式数据
const loading = ref(false)
const showDetailDialog = ref(false)
const selectedCacheInfo = ref<CacheOverviewData | null>(null)

// 搜索和筛选
const searchKeyword = ref('')
const selectedProvider = ref('')
const currentPage = ref(1)
const pageSize = ref(50)
const totalCount = ref(0)
const sortField = ref('')
const sortOrder = ref('')

// 表格数据
const tableData = ref<CacheOverviewData[]>([])

// 供应商选项
const providerOptions = computed(() => [
  { label: '快递100', value: 'kuaidi100' },
  { label: '菜鸟', value: 'cainiao' },
  { label: '易达', value: 'yida' },
  { label: '云通', value: 'yuntong' }
])

// 🚀 加载分页数据
const loadPageData = async () => {
  loading.value = true
  try {
    const params: CacheOverviewRequest = {
      page: currentPage.value,
      page_size: pageSize.value,
      provider: selectedProvider.value || undefined,
      route: searchKeyword.value || undefined,
      sort_field: sortField.value || undefined,
      sort_order: sortOrder.value || undefined
    }

    console.log('🔄 正在加载缓存概览数据，参数:', params)

    const response = await weightCacheApi.getCacheOverviewOptimized(params)

    if (response.success) {
      // 🔧 修复：后端返回的数据结构是嵌套的
      const actualData = (response.data && 'data' in response.data) ? response.data.data : []
      const actualTotal = (response.data && 'total_count' in response.data) ? response.data.total_count : 0

      tableData.value = actualData
      totalCount.value = actualTotal

      console.log('✅ 分页数据加载成功:', {
        page: currentPage.value,
        pageSize: pageSize.value,
        total: totalCount.value,
        records: tableData.value.length,
        rawResponse: response.data
      })
    } else {
      throw new Error(response.message || '获取数据失败')
    }
  } catch (error: any) {
    console.error('❌ 分页数据加载失败:', error)
    ErrorHandler.handleApiError(error, '缓存概览数据加载')
  } finally {
    loading.value = false
  }
}

// 刷新数据
const refreshData = async () => {
  await loadPageData()
  emit('refresh')
}

// 🔧 使用防抖工具函数优化搜索处理
import { debounce } from '@/utils/debounce'

const handleSearch = debounce(() => {
  console.log('🔍 执行搜索，关键词:', searchKeyword.value)
  currentPage.value = 1
  loadPageData()
}, 500)

// 🔧 供应商筛选处理（添加防抖）
const handleProviderChange = debounce(() => {
  console.log('🏷️ 供应商筛选变更:', selectedProvider.value)
  currentPage.value = 1
  loadPageData()
}, 300)

// 分页处理
const handlePageSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  loadPageData()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  loadPageData()
}

// 排序处理
const handleSortChange = ({ prop, order }: { prop: string; order: string }) => {
  sortField.value = prop
  sortOrder.value = order === 'ascending' ? 'asc' : 'desc'
  loadPageData()
}

// 查看详情
const viewDetails = (row: CacheOverviewData) => {
  selectedCacheInfo.value = row
  showDetailDialog.value = true
}

// 工具函数（添加安全检查）
const formatNumber = (num: number | string | undefined | null): string => {
  if (num === undefined || num === null) return '0'
  const n = typeof num === 'string' ? parseFloat(num) : num
  return isNaN(n) ? '0' : n.toLocaleString()
}

const formatPrice = (price: number | string | undefined | null): string => {
  if (price === undefined || price === null) return '0.00'
  const p = typeof price === 'string' ? parseFloat(price) : price
  return isNaN(p) ? '0.00' : p.toFixed(2)
}

const formatDateTime = (datetime: string | undefined | null): string => {
  if (!datetime) return '未知'
  return new Date(datetime).toLocaleString('zh-CN')
}

const getExpressName = (expressCode: string): string => {
  const expressNames: Record<string, string> = {
    'SF': '顺丰速运',
    'YD': '韵达速递',
    'STO': '申通快递',
    'YTO': '圆通速递',
    'ZTO': '中通快递',
    'HTKY': '百世快递',
    'DBL': '德邦快递',
    'JD': '京东快递'
  }
  return expressNames[expressCode] || expressCode
}

const getProviderTagType = (provider: string): string => {
  const tagTypes: Record<string, string> = {
    'kuaidi100': 'primary',
    'cainiao': 'success',
    'yida': 'warning',
    'yuntong': 'info'
  }
  return tagTypes[provider] || 'default'
}

const getProviderName = (provider: string): string => {
  const names: Record<string, string> = {
    'kuaidi100': '快递100',
    'cainiao': '菜鸟',
    'yida': '易达',
    'yuntong': '云通'
  }
  return names[provider] || provider
}

// 组件挂载
onMounted(() => {
  loadPageData()
})
</script>

<style scoped lang="scss">
.cache-overview-optimized {
  .search-filters {
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e4e7ed;

    .action-buttons {
      display: flex;
      gap: 8px;
    }
  }

  .data-table {
    background: #fff;
    border-radius: 8px;
    border: 1px solid #e4e7ed;
    overflow: hidden;

    .route-info {
      .el-tag {
        font-family: 'Monaco', 'Menlo', monospace;
      }
    }

    .express-info {
      .express-code {
        font-weight: 500;
        color: #303133;
      }

      .express-name {
        font-size: 12px;
        color: #909399;
        margin-top: 2px;
      }
    }

    .cache-count,
    .valid-count,
    .hit-count {
      font-weight: 500;
      color: #409eff;
    }

    .price {
      font-weight: 500;
      color: #67c23a;
    }

    .update-time {
      font-size: 12px;
      color: #909399;
    }

    .pagination-wrapper {
      padding: 16px;
      display: flex;
      justify-content: center;
      border-top: 1px solid #e4e7ed;
      background: #fafafa;
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .search-filters {
      .el-row {
        flex-direction: column;
        gap: 12px;
      }
    }
  }
}

// 表格样式优化
:deep(.el-table) {
  .el-table__header {
    background: #f5f7fa;

    th {
      background: #f5f7fa !important;
      color: #303133;
      font-weight: 600;
    }
  }

  .el-table__row {
    &:hover {
      background: #f5f7fa;
    }
  }
}

// 分页样式优化
:deep(.el-pagination) {
  .el-pagination__total {
    color: #606266;
    font-weight: 500;
  }

  .el-pagination__sizes {
    .el-select {
      .el-input__inner {
        border-color: #dcdfe6;
      }
    }
  }
}
</style>
