<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`${getProviderName(providerData?.provider)} - 缓存详情`"
    width="1200px"
    :before-close="handleClose"
  >
    <div class="provider-detail" v-if="providerData">
      <!-- 供应商基本信息 -->
      <div class="detail-section">
        <h4>供应商信息</h4>
        <el-descriptions :column="4" border>
          <el-descriptions-item label="供应商">
            <el-tag :type="getProviderTagType(providerData.provider)" size="large">
              {{ getProviderName(providerData.provider) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="总缓存数">
            <span class="stat-value">{{ formatNumber(providerData.total_cache_count) }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="有效缓存">
            <span class="stat-value success">{{ formatNumber(providerData.valid_cache_count) }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="失败缓存">
            <span class="stat-value warning">{{ formatNumber(providerData.invalid_cache_count || 0) }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="命中次数">
            <span class="stat-value info">{{ formatNumber(providerData.total_hit_count) }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="平均价格">
            <span class="price">¥{{ formatPrice(providerData.avg_price) }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="最后更新">
            <span>{{ formatDateTime(providerData.last_update_time) }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="刷新时间">
            <span>{{ formatDateTime(providerData.refreshed_at) }}</span>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 缓存明细表格 -->
      <div class="detail-section">
        <div class="section-header">
          <h4>缓存明细</h4>
          <div class="header-actions">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索路线或快递公司..."
              :prefix-icon="Search"
              clearable
              style="width: 300px; margin-right: 12px;"
            />
            <el-button @click="loadCacheDetails" :icon="Refresh" :loading="loading">
              刷新
            </el-button>
          </div>
        </div>

        <el-table
          :data="filteredCacheDetails"
          :loading="loading"
          stripe
          border
          height="400"
          empty-text="暂无缓存明细数据"
        >
          <el-table-column prop="route" label="路线" width="200">
            <template #default="{ row }">
              <el-tag size="small" type="info">{{ row.route }}</el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="expressCode" label="快递公司" width="120">
            <template #default="{ row }">
              <div class="express-info">
                <span class="express-code">{{ row.expressCode }}</span>
                <div class="express-name">{{ getExpressName(row.expressCode) }}</div>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="weightKg" label="重量档位" width="100">
            <template #default="{ row }">
              <el-tag size="small">{{ row.weightKg }}kg</el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="cacheCount" label="缓存数量" width="100">
            <template #default="{ row }">
              <span class="cache-count">{{ formatNumber(row.cacheCount) }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="validCacheCount" label="有效缓存" width="100">
            <template #default="{ row }">
              <span class="valid-count">{{ formatNumber(row.validCacheCount) }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="cacheHitCount" label="命中次数" width="100">
            <template #default="{ row }">
              <span class="hit-count">{{ formatNumber(row.cacheHitCount) }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="avgPrice" label="平均价格" width="100">
            <template #default="{ row }">
              <span class="price">¥{{ formatPrice(row.avgPrice) }}</span>
            </template>
          </el-table-column>

          <el-table-column label="命中率" width="120">
            <template #default="{ row }">
              <div class="hit-rate-cell">
                <el-progress 
                  :percentage="calculateHitRate(row)" 
                  :color="getProgressColor(calculateHitRate(row))"
                  :show-text="false"
                  style="margin-bottom: 4px;"
                />
                <span class="hit-rate-text">{{ calculateHitRate(row) }}%</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="lastUpdateTime" label="最后更新" width="160">
            <template #default="{ row }">
              <span class="update-time">{{ formatDateTime(row.lastUpdateTime) }}</span>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="120" fixed="right">
            <template #default="{ row }">
              <el-button size="small" @click="invalidateCache(row)" type="warning">
                失效
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[20, 50, 100]"
            :total="totalCount"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="refreshAll">刷新所有数据</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh } from '@element-plus/icons-vue'
import { 
  weightCacheApi, 
  type ProviderGroup,
  type CacheOverviewData,
  type CacheOverviewRequest
} from '@/api/weightCacheApi'
import { ErrorHandler } from '@/utils/errorHandler'

// Props
interface Props {
  modelValue: boolean
  providerData: ProviderGroup | null
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  refresh: []
}>()

// 响应式数据
const loading = ref(false)
const searchKeyword = ref('')
const currentPage = ref(1)
const pageSize = ref(50)
const totalCount = ref(0)
const cacheDetails = ref<CacheOverviewData[]>([])

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const filteredCacheDetails = computed(() => {
  if (!searchKeyword.value) return cacheDetails.value
  
  const keyword = searchKeyword.value.toLowerCase()
  return cacheDetails.value.filter(item => 
    item.route.toLowerCase().includes(keyword) ||
    (item.expressCode && item.expressCode.toLowerCase().includes(keyword)) ||
    (item.expressCode && getExpressName(item.expressCode).toLowerCase().includes(keyword))
  )
})

// 方法
const handleClose = () => {
  dialogVisible.value = false
  resetData()
}

const resetData = () => {
  searchKeyword.value = ''
  currentPage.value = 1
  cacheDetails.value = []
  totalCount.value = 0
}

// 🚀 加载缓存明细数据
const loadCacheDetails = async () => {
  if (!props.providerData) return

  loading.value = true
  try {
    const params: CacheOverviewRequest = {
      page: currentPage.value,
      page_size: pageSize.value,
      provider: props.providerData.provider
    }

    const response = await weightCacheApi.getCacheOverviewOptimized(params)
    
    if (response.success) {
      const actualData = (response.data && 'data' in response.data) ? response.data.data : []
      const actualTotal = (response.data && 'total_count' in response.data) ? response.data.total_count : 0
      
      cacheDetails.value = actualData
      totalCount.value = actualTotal
      
      console.log('✅ 缓存明细数据加载成功:', {
        provider: props.providerData.provider,
        records: cacheDetails.value.length,
        total: totalCount.value
      })
    } else {
      throw new Error(response.message || '获取缓存明细失败')
    }
  } catch (error) {
    ErrorHandler.handleApiError(error, '缓存明细数据加载')
  } finally {
    loading.value = false
  }
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  loadCacheDetails()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  loadCacheDetails()
}

const invalidateCache = async (row: CacheOverviewData) => {
  try {
    await ElMessageBox.confirm(
      `确定要使 ${row.route} - ${getExpressName(row.expressCode || '')} (${row.weightKg || 0}kg) 的缓存失效吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const [fromProvince, toProvince] = row.route.split('->')
    
    await weightCacheApi.invalidateCache({
      fromProvince: fromProvince.trim(),
      toProvince: toProvince.trim(),
      provider: props.providerData!.provider,
      expressCode: row.expressCode || '',
      weight: row.weightKg || 0
    })

    ErrorHandler.handleSuccess('缓存已失效')
    loadCacheDetails()
  } catch (error: any) {
    if (error !== 'cancel') {
      ErrorHandler.handleApiError(error, '使缓存失效')
    }
  }
}

const refreshAll = () => {
  loadCacheDetails()
  emit('refresh')
}

// 工具函数
const formatNumber = (num: number | undefined | null): string => {
  if (num === undefined || num === null) return '0'
  return num.toLocaleString()
}

const formatPrice = (price: number | undefined | null): string => {
  if (price === undefined || price === null) return '0.00'
  return price.toFixed(2)
}

const formatDateTime = (datetime: string | undefined | null): string => {
  if (!datetime) return '未知'
  return new Date(datetime).toLocaleString('zh-CN')
}

const calculateHitRate = (row: CacheOverviewData): number => {
  if (!row.cacheCount || row.cacheCount === 0) return 0
  return Math.round(((row.cacheHitCount || 0) / row.cacheCount) * 100)
}

const getProgressColor = (rate: number): string => {
  if (rate >= 80) return '#67c23a'
  if (rate >= 60) return '#e6a23c'
  return '#f56c6c'
}

const getProviderTagType = (provider: string): string => {
  const tagTypes: Record<string, string> = {
    'kuaidi100': 'primary',
    'cainiao': 'success',
    'yida': 'warning',
    'yuntong': 'info'
  }
  return tagTypes[provider] || 'default'
}

const getProviderName = (provider: string): string => {
  const names: Record<string, string> = {
    'kuaidi100': '快递100',
    'cainiao': '菜鸟',
    'yida': '易达',
    'yuntong': '云通'
  }
  return names[provider] || provider
}

const getExpressName = (expressCode: string): string => {
  const expressNames: Record<string, string> = {
    'SF': '顺丰速运',
    'YD': '韵达速递', 
    'STO': '申通快递',
    'YTO': '圆通速递',
    'ZTO': '中通快递',
    'HTKY': '百世快递',
    'DBL': '德邦快递',
    'JD': '京东快递'
  }
  return expressNames[expressCode] || expressCode
}

// 监听对话框打开，加载数据
watch(() => props.modelValue, (newVal) => {
  if (newVal && props.providerData) {
    loadCacheDetails()
  }
})
</script>

<style scoped lang="scss">
.provider-detail {
  .detail-section {
    margin-bottom: 24px;

    h4 {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      border-bottom: 2px solid #409eff;
      padding-bottom: 8px;
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      h4 {
        margin: 0;
        border: none;
        padding: 0;
      }

      .header-actions {
        display: flex;
        align-items: center;
      }
    }

    .stat-value {
      font-weight: 500;

      &.success {
        color: #67c23a;
      }

      &.warning {
        color: #e6a23c;
      }

      &.info {
        color: #409eff;
      }
    }

    .price {
      font-weight: 500;
      color: #67c23a;
      font-size: 16px;
    }

    .express-info {
      .express-code {
        font-weight: 500;
        color: #303133;
      }

      .express-name {
        font-size: 12px;
        color: #909399;
        margin-top: 2px;
      }
    }

    .cache-count,
    .valid-count,
    .hit-count {
      font-weight: 500;
      color: #409eff;
    }

    .hit-rate-cell {
      .hit-rate-text {
        font-size: 12px;
        color: #606266;
      }
    }

    .update-time {
      font-size: 12px;
      color: #909399;
    }

    .pagination-wrapper {
      padding: 16px 0;
      display: flex;
      justify-content: center;
    }
  }
}

.dialog-footer {
  text-align: right;
}

// 表格样式优化
:deep(.el-table) {
  .el-table__header {
    background: #f5f7fa;

    th {
      background: #f5f7fa !important;
      color: #303133;
      font-weight: 600;
    }
  }

  .el-table__row {
    &:hover {
      background: #f5f7fa;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .provider-detail {
    .section-header {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;

      .header-actions {
        justify-content: center;
      }
    }
  }
}
</style>
