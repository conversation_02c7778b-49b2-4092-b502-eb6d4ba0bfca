<template>
  <div class="performance-monitor">
    <!-- 性能指标卡片 -->
    <el-row :gutter="20">
      <el-col :span="6">
        <div class="metric-card" :class="getMetricClass('response_time')">
          <div class="metric-icon">
            <el-icon><Timer /></el-icon>
          </div>
          <div class="metric-content">
            <div class="metric-value">{{ formatResponseTime(metrics.avgResponseTime) }}</div>
            <div class="metric-label">平均响应时间</div>
          </div>
        </div>
      </el-col>

      <el-col :span="6">
        <div class="metric-card" :class="getMetricClass('hit_rate')">
          <div class="metric-icon">
            <el-icon><SuccessFilled /></el-icon>
          </div>
          <div class="metric-content">
            <div class="metric-value">{{ formatPercent(metrics.cacheHitRate) }}</div>
            <div class="metric-label">缓存命中率</div>
          </div>
        </div>
      </el-col>

      <el-col :span="6">
        <div class="metric-card" :class="getMetricClass('query_count')">
          <div class="metric-icon">
            <el-icon><DataBoard /></el-icon>
          </div>
          <div class="metric-content">
            <div class="metric-value">{{ formatNumber(metrics.totalQueries) }}</div>
            <div class="metric-label">总查询次数</div>
          </div>
        </div>
      </el-col>

      <el-col :span="6">
        <div class="metric-card" :class="getMetricClass('error_rate')">
          <div class="metric-icon">
            <el-icon><WarningFilled /></el-icon>
          </div>
          <div class="metric-content">
            <div class="metric-value">{{ formatPercent(metrics.errorRate) }}</div>
            <div class="metric-label">错误率</div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 控制按钮 -->
    <div class="monitor-controls">
      <el-button @click="refreshMetrics" :icon="Refresh" :loading="loading">
        刷新指标
      </el-button>
      <el-button @click="exportData" :icon="Download">
        导出数据
      </el-button>
    </div>

    <!-- 性能建议 -->
    <div class="performance-suggestions" v-if="suggestions.length > 0">
      <el-card>
        <template #header>
          <span>性能优化建议</span>
        </template>
        <el-alert
          v-for="(suggestion, index) in suggestions"
          :key="index"
          :title="suggestion.title"
          :description="suggestion.description"
          :type="suggestion.type"
          :closable="false"
          style="margin-bottom: 12px;"
        />
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Timer, SuccessFilled, DataBoard, WarningFilled,
  Refresh, Download
} from '@element-plus/icons-vue'
import { weightCacheApi } from '@/api/weightCacheApi'
import { ErrorHandler } from '@/utils/errorHandler'

// Emits
const emit = defineEmits<{
  refresh: []
}>()

// 响应式数据
const loading = ref(false)

// 性能指标
const metrics = reactive({
  avgResponseTime: 0,
  cacheHitRate: 0,
  totalQueries: 0,
  errorRate: 0,
  lastUpdated: ''
})

// 性能建议
const suggestions = ref<Array<{
  title: string
  description: string
  type: 'success' | 'warning' | 'error' | 'info'
}>>([])

// 🔧 使用定时器管理器替代原生定时器
import { useTimerManager } from '@/utils/timerManager'
const { createTimer } = useTimerManager('performance-monitor')

// 方法
const refreshMetrics = async () => {
  loading.value = true
  try {
    const response = await weightCacheApi.getQuickStats()
    if (response.success) {
      const data = response.data
      
      // 更新指标（模拟一些性能数据）
      metrics.avgResponseTime = 150 // 模拟响应时间
      metrics.cacheHitRate = data.cache_hit_rate || 0
      metrics.totalQueries = data.total_cache_entries || 0
      metrics.errorRate = 0.02 // 模拟错误率
      metrics.lastUpdated = new Date().toLocaleTimeString()
      
      // 生成性能建议
      generateSuggestions()
      
      console.log('🚀 性能指标已更新:', metrics)
      emit('refresh')
    }
  } catch (error) {
    ErrorHandler.handleApiError(error, '获取性能指标')
  } finally {
    loading.value = false
  }
}

const generateSuggestions = () => {
  suggestions.value = []
  
  // 基于指标生成建议
  if (metrics.cacheHitRate < 0.8) {
    suggestions.value.push({
      title: '缓存命中率偏低',
      description: '当前缓存命中率低于80%，建议执行缓存预热操作，提升常用路线的缓存覆盖率。',
      type: 'warning'
    })
  }
  
  if (metrics.avgResponseTime > 200) {
    suggestions.value.push({
      title: '响应时间较长',
      description: '平均响应时间超过200ms，建议优化查询逻辑或增加缓存容量。',
      type: 'warning'
    })
  }
  
  if (metrics.errorRate > 0.05) {
    suggestions.value.push({
      title: '错误率偏高',
      description: '系统错误率超过5%，建议检查系统日志，排查潜在问题。',
      type: 'error'
    })
  }
  
  if (suggestions.value.length === 0) {
    suggestions.value.push({
      title: '系统运行良好',
      description: '所有性能指标都在正常范围内，系统运行状态良好。',
      type: 'success'
    })
  }
}

const exportData = () => {
  const data = {
    metrics,
    suggestions: suggestions.value,
    timestamp: new Date().toISOString()
  }
  
  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `performance-metrics-${Date.now()}.json`
  a.click()
  URL.revokeObjectURL(url)
  
  ErrorHandler.handleSuccess('性能数据已导出')
}

// 工具函数（添加安全检查）
const formatResponseTime = (time: number | undefined | null): string => {
  if (time === undefined || time === null) return '0ms'
  return `${time.toFixed(0)}ms`
}

const formatPercent = (rate: number | undefined | null): string => {
  if (rate === undefined || rate === null) return '0%'
  return `${Math.round(rate * 100)}%`
}

const formatNumber = (num: number | undefined | null): string => {
  if (num === undefined || num === null) return '0'
  return num.toLocaleString()
}

const getMetricClass = (type: string): string => {
  switch (type) {
    case 'response_time':
      return metrics.avgResponseTime > 200 ? 'warning' : 'success'
    case 'hit_rate':
      return metrics.cacheHitRate > 0.8 ? 'success' : 'warning'
    case 'error_rate':
      return metrics.errorRate > 0.05 ? 'danger' : 'success'
    default:
      return 'info'
  }
}

// 生命周期
onMounted(() => {
  refreshMetrics()

  // 🔧 使用定时器管理器设置智能定时刷新
  createTimer({
    id: 'performance-metrics-refresh',
    type: 'interval',
    delay: 30000,
    pauseWhenHidden: true,
    callback: async () => {
      if (!loading.value) {
        console.log('🔄 定时刷新性能指标')
        await refreshMetrics()
      } else {
        console.log('⏸️ 跳过性能指标刷新（正在加载中）')
      }
    }
  })

  console.log('✅ 性能监控组件初始化完成')
})
</script>

<style scoped lang="scss">
.performance-monitor {
  .metric-card {
    display: flex;
    align-items: center;
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    border: 1px solid #e4e7ed;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    &.success {
      border-left: 4px solid #67c23a;
    }

    &.warning {
      border-left: 4px solid #e6a23c;
    }

    &.danger {
      border-left: 4px solid #f56c6c;
    }

    &.info {
      border-left: 4px solid #409eff;
    }

    .metric-icon {
      font-size: 32px;
      margin-right: 16px;
      color: #409eff;
    }

    .metric-content {
      flex: 1;

      .metric-value {
        font-size: 24px;
        font-weight: 600;
        color: #303133;
        margin-bottom: 4px;
      }

      .metric-label {
        font-size: 14px;
        color: #909399;
      }
    }
  }

  .monitor-controls {
    display: flex;
    justify-content: center;
    gap: 12px;
    margin: 20px 0;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
  }

  .performance-suggestions {
    margin-top: 20px;
  }

  // 响应式设计
  @media (max-width: 768px) {
    .el-row {
      flex-direction: column;
      gap: 12px;
    }

    .metric-card {
      .metric-content {
        .metric-value {
          font-size: 20px;
        }
      }
    }

    .monitor-controls {
      flex-direction: column;
    }
  }
}
</style>
