<template>
  <div class="cache-statistics">
    <!-- 时间范围选择 -->
    <div class="date-range-selector">
      <el-row :gutter="20" align="middle">
        <el-col :span="8">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleDateChange"
          />
        </el-col>
        <el-col :span="6">
          <el-select v-model="selectedProvider" placeholder="选择供应商" clearable @change="loadStatistics">
            <el-option
              v-for="option in providerOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-button @click="loadStatistics" :icon="Refresh" :loading="loading">
            查询
          </el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 统计概览 -->
    <div class="statistics-overview" v-if="statisticsData">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="overview-card">
            <div class="card-icon">
              <el-icon><DataBoard /></el-icon>
            </div>
            <div class="card-content">
              <div class="card-value">{{ formatNumber(statisticsData.total_queries) }}</div>
              <div class="card-label">总查询次数</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="overview-card">
            <div class="card-icon success">
              <el-icon><SuccessFilled /></el-icon>
            </div>
            <div class="card-content">
              <div class="card-value">{{ formatNumber(statisticsData.cache_hits) }}</div>
              <div class="card-label">缓存命中</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="overview-card">
            <div class="card-icon warning">
              <el-icon><WarningFilled /></el-icon>
            </div>
            <div class="card-content">
              <div class="card-value">{{ formatNumber(statisticsData.cache_misses) }}</div>
              <div class="card-label">缓存未命中</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="overview-card">
            <div class="card-icon info">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="card-content">
              <div class="card-value">{{ formatPercent(statisticsData.hit_rate) }}</div>
              <div class="card-label">命中率</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 供应商统计表格 -->
    <div class="provider-statistics" v-if="statisticsData && statisticsData.provider_stats">
      <h4>供应商统计</h4>
      <el-table :data="statisticsData.provider_stats" stripe border>
        <el-table-column prop="provider" label="供应商" width="120">
          <template #default="{ row }">
            <el-tag :type="getProviderTagType(row.provider)" size="small">
              {{ getProviderName(row.provider) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="queries" label="查询次数" width="120">
          <template #default="{ row }">
            <span class="stat-value">{{ formatNumber(row.queries) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="hits" label="命中次数" width="120">
          <template #default="{ row }">
            <span class="stat-value success">{{ formatNumber(row.hits) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="misses" label="未命中次数" width="120">
          <template #default="{ row }">
            <span class="stat-value warning">{{ formatNumber(row.misses) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="hit_rate" label="命中率" width="100">
          <template #default="{ row }">
            <el-progress 
              :percentage="Math.round(row.hit_rate * 100)" 
              :color="getProgressColor(row.hit_rate)"
              :show-text="false"
            />
            <span class="hit-rate-text">{{ formatPercent(row.hit_rate) }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-state">
      <el-skeleton :rows="5" animated />
    </div>

    <!-- 空状态 -->
    <div v-if="!loading && !statisticsData" class="empty-state">
      <el-empty description="请选择时间范围查询统计数据" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { DataBoard, SuccessFilled, WarningFilled, TrendCharts, Refresh } from '@element-plus/icons-vue'
import { 
  weightCacheApi, 
  type CacheStatisticsRequest,
  type CacheStatisticsResponse 
} from '@/api/weightCacheApi'
import { ErrorHandler } from '@/utils/errorHandler'

// Emits
const emit = defineEmits<{
  refresh: []
}>()

// 响应式数据
const loading = ref(false)
const dateRange = ref<[string, string]>(['', ''])
const selectedProvider = ref('')
const statisticsData = ref<CacheStatisticsResponse['data'] | null>(null)

// 供应商选项
const providerOptions = computed(() => [
  { label: '快递100', value: 'kuaidi100' },
  { label: '菜鸟', value: 'cainiao' },
  { label: '易达', value: 'yida' },
  { label: '云通', value: 'yuntong' }
])

// 🚀 加载统计数据
const loadStatistics = async () => {
  if (!dateRange.value || !dateRange.value[0] || !dateRange.value[1]) {
    ElMessage.warning('请选择时间范围')
    return
  }

  loading.value = true
  try {
    const params: CacheStatisticsRequest = {
      startDate: dateRange.value[0],
      endDate: dateRange.value[1],
      provider: selectedProvider.value || undefined
    }

    console.log('🔄 正在加载缓存统计数据，参数:', params)

    const response = await weightCacheApi.getCacheStatistics(params)
    
    if (response.success) {
      // 🔧 修复：处理后端实际返回的数据结构
      const rawData = response.data

      // 转换数据格式以匹配前端期望
      statisticsData.value = {
        total_queries: rawData.total_queries || 0,
        cache_hit_rate: rawData.cache_hit_rate || '0',
        validation_pass_rate: rawData.validation_pass_rate || '0',
        avg_response_time: rawData.avg_response_time || '0',
        provider_stats: Array.isArray(rawData.provider_stats) ? rawData.provider_stats : {},
        daily_stats: rawData.daily_stats || [],
        cache_hits: Math.round((rawData.total_queries || 0) * parseFloat(rawData.cache_hit_rate || '0')),
        cache_misses: Math.round((rawData.total_queries || 0) * (1 - parseFloat(rawData.cache_hit_rate || '0'))),
        hit_rate: parseFloat(rawData.cache_hit_rate || '0')
      }

      console.log('✅ 缓存统计数据加载成功:', {
        raw: rawData,
        processed: statisticsData.value
      })
    } else {
      throw new Error(response.message || '获取统计数据失败')
    }
  } catch (error: any) {
    console.error('❌ 缓存统计数据加载失败:', error)
    ErrorHandler.handleApiError(error, '缓存统计数据加载')
  } finally {
    loading.value = false
  }
}

// 处理日期变化
const handleDateChange = () => {
  if (dateRange.value && dateRange.value[0] && dateRange.value[1]) {
    loadStatistics()
  }
}

// 工具函数（添加安全检查）
const formatNumber = (num: number | undefined | null): string => {
  if (num === undefined || num === null) return '0'
  return num.toLocaleString()
}

const formatPercent = (rate: number | undefined | null): string => {
  if (rate === undefined || rate === null) return '0%'
  return `${Math.round(rate * 100)}%`
}

const getProviderTagType = (provider: string): string => {
  const tagTypes: Record<string, string> = {
    'kuaidi100': 'primary',
    'cainiao': 'success',
    'yida': 'warning',
    'yuntong': 'info'
  }
  return tagTypes[provider] || 'default'
}

const getProviderName = (provider: string): string => {
  const names: Record<string, string> = {
    'kuaidi100': '快递100',
    'cainiao': '菜鸟',
    'yida': '易达',
    'yuntong': '云通'
  }
  return names[provider] || provider
}

const getProgressColor = (rate: number): string => {
  if (rate >= 0.9) return '#67c23a'
  if (rate >= 0.8) return '#e6a23c'
  return '#f56c6c'
}

// 组件挂载时设置默认日期范围
onMounted(() => {
  const endDate = new Date()
  const startDate = new Date()
  startDate.setDate(endDate.getDate() - 7) // 默认查询最近7天

  dateRange.value = [
    startDate.toISOString().split('T')[0],
    endDate.toISOString().split('T')[0]
  ]
  
  loadStatistics()
})
</script>

<style scoped lang="scss">
.cache-statistics {
  .date-range-selector {
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e4e7ed;
  }

  .statistics-overview {
    margin-bottom: 20px;

    .overview-card {
      display: flex;
      align-items: center;
      padding: 20px;
      background: #fff;
      border-radius: 8px;
      border: 1px solid #e4e7ed;
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      .card-icon {
        font-size: 32px;
        color: #409eff;
        margin-right: 16px;

        &.success {
          color: #67c23a;
        }

        &.warning {
          color: #e6a23c;
        }

        &.info {
          color: #909399;
        }
      }

      .card-content {
        .card-value {
          font-size: 24px;
          font-weight: 600;
          color: #303133;
          margin-bottom: 4px;
        }

        .card-label {
          font-size: 14px;
          color: #909399;
        }
      }
    }
  }

  .provider-statistics {
    h4 {
      margin: 0 0 16px 0;
      color: #303133;
    }

    .stat-value {
      font-weight: 500;

      &.success {
        color: #67c23a;
      }

      &.warning {
        color: #e6a23c;
      }
    }

    .hit-rate-text {
      margin-left: 8px;
      font-size: 12px;
      color: #606266;
    }
  }

  .loading-state,
  .empty-state {
    padding: 40px 20px;
    text-align: center;
  }

  // 响应式设计
  @media (max-width: 768px) {
    .date-range-selector {
      .el-row {
        flex-direction: column;
        gap: 12px;
      }
    }

    .statistics-overview {
      .el-row {
        flex-direction: column;
        gap: 12px;
      }
    }
  }
}
</style>
