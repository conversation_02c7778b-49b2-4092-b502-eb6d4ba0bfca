<template>
  <el-dialog
    v-model="dialogVisible"
    title="缓存预热配置"
    width="600px"
    :before-close="handleClose"
  >
    <el-form :model="formData" :rules="rules" ref="formRef" label-width="100px">
      <!-- 路线选择 -->
      <el-form-item label="预热路线" prop="routes">
        <el-select
          v-model="formData.routes"
          multiple
          placeholder="选择要预热的路线"
          style="width: 100%"
        >
          <el-option
            v-for="route in routeOptions"
            :key="route.value"
            :label="route.label"
            :value="route.value"
          />
        </el-select>
        <div class="form-tip">选择常用的热门路线进行预热</div>
      </el-form-item>

      <!-- 供应商选择 -->
      <el-form-item label="供应商" prop="providers">
        <el-checkbox-group v-model="formData.providers">
          <el-checkbox
            v-for="provider in providerOptions"
            :key="provider.value"
            :label="provider.value"
          >
            {{ provider.label }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <!-- 重量档位选择 -->
      <el-form-item label="重量档位" prop="weights">
        <el-checkbox-group v-model="formData.weights">
          <el-checkbox
            v-for="weight in weightOptions"
            :key="weight"
            :label="weight"
          >
            {{ weight }}kg
          </el-checkbox>
        </el-checkbox-group>
        <div class="form-tip">选择常用的重量档位</div>
      </el-form-item>

      <!-- 快递公司选择 -->
      <el-form-item label="快递公司" prop="express_codes">
        <el-select
          v-model="formData.express_codes"
          multiple
          placeholder="选择快递公司"
          style="width: 100%"
        >
          <el-option
            v-for="company in expressOptions"
            :key="company.value"
            :label="company.label"
            :value="company.value"
          />
        </el-select>
      </el-form-item>

      <!-- 预热策略 -->
      <el-form-item label="预热策略">
        <el-radio-group v-model="formData.strategy">
          <el-radio label="fast">快速预热（仅热门路线）</el-radio>
          <el-radio label="full">完整预热（所有选中项）</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>

    <!-- 预热预览 -->
    <div class="warmup-preview">
      <el-alert
        title="预热预览"
        type="info"
        :closable="false"
      >
        <template #default>
          <div class="preview-content">
            <p>将预热 <strong>{{ estimatedTasks }}</strong> 个缓存任务</p>
            <p>预计耗时: <strong>{{ estimatedTime }}</strong></p>
            <p class="preview-tip">建议在业务低峰期执行预热操作</p>
          </div>
        </template>
      </el-alert>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="loading">
          开始预热
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { type CacheWarmupRequest } from '@/api/weightCacheApi'
import { DataValidator } from '@/utils/errorHandler'

// Props
interface Props {
  modelValue: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  confirm: [params: CacheWarmupRequest]
}>()

// 响应式数据
const loading = ref(false)
const formRef = ref<FormInstance>()

// 表单数据
const formData = ref({
  routes: [] as string[],
  providers: ['kuaidi100', 'cainiao'] as string[],
  weights: [1, 2, 3, 5] as number[],
  express_codes: [] as string[],
  strategy: 'fast' as string
})

// 选项数据
const routeOptions = ref([
  { label: '广东->北京', value: '广东->北京' },
  { label: '广东->上海', value: '广东->上海' },
  { label: '广东->江苏', value: '广东->江苏' },
  { label: '广东->浙江', value: '广东->浙江' },
  { label: '北京->上海', value: '北京->上海' },
  { label: '北京->广东', value: '北京->广东' },
  { label: '上海->广东', value: '上海->广东' },
  { label: '江苏->广东', value: '江苏->广东' }
])

const providerOptions = ref([
  { label: '快递100', value: 'kuaidi100' },
  { label: '菜鸟', value: 'cainiao' },
  { label: '易达', value: 'yida' },
  { label: '云通', value: 'yuntong' }
])

const weightOptions = ref([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 15, 20])

const expressOptions = ref([
  { label: '顺丰速运', value: 'SF' },
  { label: '韵达速递', value: 'YD' },
  { label: '申通快递', value: 'STO' },
  { label: '圆通速递', value: 'YTO' },
  { label: '中通快递', value: 'ZTO' },
  { label: '百世快递', value: 'HTKY' },
  { label: '德邦快递', value: 'DBL' },
  { label: '京东快递', value: 'JD' }
])

// 表单验证规则
const rules: FormRules = {
  routes: [
    { required: true, message: '请选择预热路线', trigger: 'change' }
  ],
  providers: [
    { required: true, message: '请选择供应商', trigger: 'change' }
  ],
  weights: [
    { required: true, message: '请选择重量档位', trigger: 'change' }
  ],
  express_codes: [
    { required: true, message: '请选择快递公司', trigger: 'change' }
  ]
}

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const estimatedTasks = computed(() => {
  const routes = formData.value.routes.length || 0
  const providers = formData.value.providers.length || 0
  const weights = formData.value.weights.length || 0
  const companies = formData.value.express_codes.length || 0
  
  return routes * providers * weights * companies
})

const estimatedTime = computed(() => {
  const tasks = estimatedTasks.value
  if (tasks === 0) return '0分钟'
  
  // 假设每个任务平均需要2秒
  const seconds = tasks * 2
  const minutes = Math.ceil(seconds / 60)
  
  if (minutes < 60) {
    return `${minutes}分钟`
  } else {
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    return `${hours}小时${remainingMinutes}分钟`
  }
})

// 监听策略变化，自动调整选项
watch(() => formData.value.strategy, (strategy) => {
  if (strategy === 'fast') {
    // 快速预热：选择热门路线和常用重量
    formData.value.routes = ['广东->北京', '广东->上海', '北京->上海']
    formData.value.weights = [1, 2, 3, 5]
    formData.value.express_codes = ['SF', 'YD', 'STO', 'YTO']
  }
})

// 方法
const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

const handleConfirm = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    
    // 数据验证
    const errors = DataValidator.validateBatch([
      () => DataValidator.validateRequired(formData.value.routes, '预热路线'),
      () => DataValidator.validateRequired(formData.value.providers, '供应商'),
      () => DataValidator.validateRequired(formData.value.weights, '重量档位'),
      () => DataValidator.validateRequired(formData.value.express_codes, '快递公司')
    ])

    if (errors.length > 0) {
      errors.forEach(error => {
        if (error) {
          ElMessage.error(error.message)
        }
      })
      return
    }

    loading.value = true

    const params: CacheWarmupRequest = {
      routes: formData.value.routes,
      providers: formData.value.providers,
      weights: formData.value.weights,
      express_codes: formData.value.express_codes
    }

    emit('confirm', params)
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    loading.value = false
  }
}

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  
  formData.value = {
    routes: [],
    providers: ['kuaidi100', 'cainiao'],
    weights: [1, 2, 3, 5],
    express_codes: [],
    strategy: 'fast'
  }
}
</script>

<style scoped lang="scss">
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.warmup-preview {
  margin: 20px 0;

  .preview-content {
    p {
      margin: 8px 0;
      font-size: 14px;
      color: #606266;

      strong {
        color: #409eff;
      }
    }

    .preview-tip {
      color: #e6a23c;
      font-size: 12px;
    }
  }
}

.dialog-footer {
  text-align: right;
}

// 表单样式优化
:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-checkbox-group) {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

:deep(.el-radio-group) {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
</style>
