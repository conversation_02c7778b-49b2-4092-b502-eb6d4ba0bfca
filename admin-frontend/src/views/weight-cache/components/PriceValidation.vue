<template>
  <div class="price-validation">
    <!-- 时间范围选择 -->
    <div class="date-range-selector">
      <el-row :gutter="20" align="middle">
        <el-col :span="8">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleDateChange"
          />
        </el-col>
        <el-col :span="6">
          <el-select v-model="selectedProvider" placeholder="选择供应商" clearable @change="loadValidationStats">
            <el-option
              v-for="option in providerOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-button @click="loadValidationStats" :icon="Refresh" :loading="loading">
            查询
          </el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 验证统计表格 -->
    <div class="validation-table" v-if="validationData && validationData.length > 0">
      <el-table :data="validationData" stripe border :loading="loading">
        <el-table-column prop="provider" label="供应商" width="120">
          <template #default="{ row }">
            <el-tag :type="getProviderTagType(row.provider)" size="small">
              {{ getProviderName(row.provider) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="express_code" label="快递公司" width="120">
          <template #default="{ row }">
            <span class="express-code">{{ row.express_code }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="total_validations" label="总验证次数" width="120">
          <template #default="{ row }">
            <span class="stat-value">{{ formatNumber(row.total_validations) }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="exact_matches" label="精确匹配" width="120">
          <template #default="{ row }">
            <span class="stat-value success">{{ formatNumber(row.exact_matches) }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="price_increases" label="价格上涨" width="120">
          <template #default="{ row }">
            <span class="stat-value warning">{{ formatNumber(row.price_increases) }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="price_decreases" label="价格下降" width="120">
          <template #default="{ row }">
            <span class="stat-value info">{{ formatNumber(row.price_decreases) }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="success_rate" label="成功率" width="120">
          <template #default="{ row }">
            <el-progress 
              :percentage="Math.round(row.success_rate * 100)" 
              :color="getProgressColor(row.success_rate)"
              :show-text="false"
            />
            <span class="success-rate-text">{{ formatPercent(row.success_rate) }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="avg_price_difference" label="平均价差" width="120">
          <template #default="{ row }">
            <span class="price-diff" :class="getPriceDiffClass(row.avg_price_difference)">
              {{ formatPriceDiff(row.avg_price_difference) }}
            </span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-state">
      <el-skeleton :rows="5" animated />
    </div>

    <!-- 空状态 -->
    <div v-if="!loading && (!validationData || validationData.length === 0)" class="empty-state">
      <el-empty description="请选择时间范围查询验证统计数据" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'
import { 
  weightCacheApi, 
  type PriceValidationStats 
} from '@/api/weightCacheApi'
import { ErrorHandler } from '@/utils/errorHandler'

// Emits
const emit = defineEmits<{
  refresh: []
}>()

// 响应式数据
const loading = ref(false)
const dateRange = ref<[string, string]>(['', ''])
const selectedProvider = ref('')
const validationData = ref<PriceValidationStats[]>([])

// 供应商选项
const providerOptions = computed(() => [
  { label: '快递100', value: 'kuaidi100' },
  { label: '菜鸟', value: 'cainiao' },
  { label: '易达', value: 'yida' },
  { label: '云通', value: 'yuntong' }
])

// 🚀 加载验证统计数据
const loadValidationStats = async () => {
  if (!dateRange.value || !dateRange.value[0] || !dateRange.value[1]) {
    ElMessage.warning('请选择时间范围')
    return
  }

  loading.value = true
  try {
    const params = {
      startDate: dateRange.value[0],
      endDate: dateRange.value[1],
      provider: selectedProvider.value || undefined
    }

    console.log('🔄 正在加载价格验证统计数据，参数:', params)

    const response = await weightCacheApi.getValidationStats(params)
    
    if (response.success) {
      // 🔧 修复：处理后端返回null的情况
      validationData.value = Array.isArray(response.data) ? response.data : []
      console.log('✅ 价格验证统计数据加载成功:', validationData.value.length, '条记录')
    } else {
      throw new Error(response.message || '获取验证统计数据失败')
    }
  } catch (error: any) {
    console.error('❌ 价格验证统计数据加载失败:', error)
    ErrorHandler.handleApiError(error, '价格验证统计数据加载')
  } finally {
    loading.value = false
  }
}

// 处理日期变化
const handleDateChange = () => {
  if (dateRange.value && dateRange.value[0] && dateRange.value[1]) {
    loadValidationStats()
  }
}

// 工具函数（添加安全检查）
const formatNumber = (num: number | undefined | null): string => {
  if (num === undefined || num === null) return '0'
  return num.toLocaleString()
}

const formatPercent = (rate: number | undefined | null): string => {
  if (rate === undefined || rate === null) return '0%'
  return `${Math.round(rate * 100)}%`
}

const formatPriceDiff = (diff: number | undefined | null): string => {
  if (diff === undefined || diff === null) return '¥0.00'
  const sign = diff > 0 ? '+' : ''
  return `${sign}¥${diff.toFixed(2)}`
}

const getProviderTagType = (provider: string): string => {
  const tagTypes: Record<string, string> = {
    'kuaidi100': 'primary',
    'cainiao': 'success',
    'yida': 'warning',
    'yuntong': 'info'
  }
  return tagTypes[provider] || 'default'
}

const getProviderName = (provider: string): string => {
  const names: Record<string, string> = {
    'kuaidi100': '快递100',
    'cainiao': '菜鸟',
    'yida': '易达',
    'yuntong': '云通'
  }
  return names[provider] || provider
}

const getProgressColor = (rate: number): string => {
  if (rate >= 0.9) return '#67c23a'
  if (rate >= 0.8) return '#e6a23c'
  return '#f56c6c'
}

const getPriceDiffClass = (diff: number): string => {
  if (diff > 0) return 'increase'
  if (diff < 0) return 'decrease'
  return 'neutral'
}

// 组件挂载时设置默认日期范围
onMounted(() => {
  const endDate = new Date()
  const startDate = new Date()
  startDate.setDate(endDate.getDate() - 7) // 默认查询最近7天

  dateRange.value = [
    startDate.toISOString().split('T')[0],
    endDate.toISOString().split('T')[0]
  ]
  
  loadValidationStats()
})
</script>

<style scoped lang="scss">
.price-validation {
  .date-range-selector {
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e4e7ed;
  }

  .validation-table {
    .express-code {
      font-weight: 500;
      color: #303133;
    }

    .stat-value {
      font-weight: 500;

      &.success {
        color: #67c23a;
      }

      &.warning {
        color: #e6a23c;
      }

      &.info {
        color: #409eff;
      }
    }

    .success-rate-text {
      margin-left: 8px;
      font-size: 12px;
      color: #606266;
    }

    .price-diff {
      font-weight: 500;

      &.increase {
        color: #f56c6c;
      }

      &.decrease {
        color: #67c23a;
      }

      &.neutral {
        color: #909399;
      }
    }
  }

  .loading-state,
  .empty-state {
    padding: 40px 20px;
    text-align: center;
  }

  // 响应式设计
  @media (max-width: 768px) {
    .date-range-selector {
      .el-row {
        flex-direction: column;
        gap: 12px;
      }
    }
  }
}
</style>
