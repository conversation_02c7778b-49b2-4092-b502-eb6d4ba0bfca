<template>
  <div class="cache-management">
    <!-- 操作卡片 -->
    <el-row :gutter="20">
      <!-- 缓存预热 -->
      <el-col :span="8">
        <div class="management-card">
          <div class="card-header">
            <el-icon class="card-icon"><Upload /></el-icon>
            <h3>缓存预热</h3>
          </div>
          <div class="card-content">
            <p>预先加载常用路线的价格数据到缓存中，提升查询速度。</p>
            <div class="card-stats">
              <span>建议在业务低峰期执行</span>
            </div>
          </div>
          <div class="card-actions">
            <el-button type="primary" @click="handleWarmup" :loading="warmupLoading">
              开始预热
            </el-button>
            <el-button @click="checkWarmupProgress" :loading="progressLoading">
              查看进度
            </el-button>
          </div>
        </div>
      </el-col>

      <!-- 清理无效缓存 -->
      <el-col :span="8">
        <div class="management-card">
          <div class="card-header">
            <el-icon class="card-icon warning"><Delete /></el-icon>
            <h3>清理无效缓存</h3>
          </div>
          <div class="card-content">
            <p>清理过期、无效的缓存数据，释放存储空间。</p>
            <div class="card-stats">
              <span>建议定期执行</span>
            </div>
          </div>
          <div class="card-actions">
            <el-button type="warning" @click="handleCleanup" :loading="cleanupLoading">
              清理缓存
            </el-button>
          </div>
        </div>
      </el-col>

      <!-- 刷新缓存视图 -->
      <el-col :span="8">
        <div class="management-card">
          <div class="card-header">
            <el-icon class="card-icon info"><Refresh /></el-icon>
            <h3>刷新缓存视图</h3>
          </div>
          <div class="card-content">
            <p>刷新缓存统计视图，更新最新的缓存状态信息。</p>
            <div class="card-stats">
              <span>更新统计数据</span>
            </div>
          </div>
          <div class="card-actions">
            <el-button type="info" @click="handleRefreshViews" :loading="refreshLoading">
              刷新视图
            </el-button>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 预热进度显示 -->
    <div v-if="warmupProgress" class="warmup-progress">
      <el-card>
        <template #header>
          <div class="progress-header">
            <span>缓存预热进度</span>
            <el-button size="small" @click="checkWarmupProgress" :loading="progressLoading">
              刷新进度
            </el-button>
          </div>
        </template>
        
        <div class="progress-content">
          <div class="progress-info">
            <el-row :gutter="20">
              <el-col :span="6">
                <div class="info-item">
                  <div class="info-label">状态</div>
                  <div class="info-value">
                    <el-tag :type="getStatusTagType(warmupProgress.status)">
                      {{ getStatusText(warmupProgress.status) }}
                    </el-tag>
                  </div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="info-item">
                  <div class="info-label">总任务数</div>
                  <div class="info-value">{{ warmupProgress.total_tasks }}</div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="info-item">
                  <div class="info-label">已完成</div>
                  <div class="info-value">{{ warmupProgress.completed_tasks }}</div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="info-item">
                  <div class="info-label">失败数</div>
                  <div class="info-value">{{ warmupProgress.failed_tasks }}</div>
                </div>
              </el-col>
            </el-row>
          </div>
          
          <div class="progress-bar">
            <el-progress 
              :percentage="warmupProgress.progress" 
              :color="getProgressColor(warmupProgress.progress)"
              :show-text="true"
            />
          </div>
          
          <div class="progress-time" v-if="warmupProgress.estimated_remaining_time > 0">
            <span>预计剩余时间: {{ formatTime(warmupProgress.estimated_remaining_time) }}</span>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Upload, Delete, Refresh } from '@element-plus/icons-vue'
import { weightCacheApi, type WarmupProgressResponse } from '@/api/weightCacheApi'
import { ErrorHandler } from '@/utils/errorHandler'

// Emits
const emit = defineEmits<{
  warmup: []
  cleanup: []
  refresh: []
}>()

// 响应式数据
const warmupLoading = ref(false)
const cleanupLoading = ref(false)
const refreshLoading = ref(false)
const progressLoading = ref(false)
const warmupProgress = ref<WarmupProgressResponse['data'] | null>(null)

// 🚀 处理缓存预热
const handleWarmup = () => {
  emit('warmup')
}

// 🚀 处理清理无效缓存
const handleCleanup = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清理所有无效缓存吗？此操作将删除过期和无效的缓存数据。',
      '确认清理',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    cleanupLoading.value = true
    await weightCacheApi.cleanupInvalidCache()
    ErrorHandler.handleSuccess('无效缓存清理成功')
    emit('cleanup')
    emit('refresh')
  } catch (error: any) {
    if (error !== 'cancel') {
      ErrorHandler.handleApiError(error, '清理无效缓存')
    }
  } finally {
    cleanupLoading.value = false
  }
}

// 🚀 处理刷新缓存视图
const handleRefreshViews = async () => {
  try {
    refreshLoading.value = true
    await weightCacheApi.refreshCacheViews()
    ErrorHandler.handleSuccess('缓存视图已刷新')
    emit('refresh')
  } catch (error) {
    ErrorHandler.handleApiError(error, '刷新缓存视图')
  } finally {
    refreshLoading.value = false
  }
}

// 🚀 检查预热进度
const checkWarmupProgress = async () => {
  progressLoading.value = true
  try {
    const response = await weightCacheApi.getWarmupProgress()
    if (response.success) {
      warmupProgress.value = response.data
      console.log('✅ 预热进度获取成功:', response.data)
    } else {
      throw new Error(response.message || '获取预热进度失败')
    }
  } catch (error) {
    ErrorHandler.handleApiError(error, '获取预热进度')
  } finally {
    progressLoading.value = false
  }
}

// 工具函数
const getStatusTagType = (status: string): string => {
  switch (status.toLowerCase()) {
    case 'running':
    case 'in_progress':
      return 'warning'
    case 'completed':
    case 'success':
      return 'success'
    case 'failed':
    case 'error':
      return 'danger'
    default:
      return 'info'
  }
}

const getStatusText = (status: string): string => {
  switch (status.toLowerCase()) {
    case 'running':
    case 'in_progress':
      return '进行中'
    case 'completed':
    case 'success':
      return '已完成'
    case 'failed':
    case 'error':
      return '失败'
    case 'pending':
      return '等待中'
    default:
      return status
  }
}

const getProgressColor = (progress: number): string => {
  if (progress >= 100) return '#67c23a'
  if (progress >= 80) return '#e6a23c'
  return '#409eff'
}

const formatTime = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60

  if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  } else if (minutes > 0) {
    return `${minutes}分钟${secs}秒`
  } else {
    return `${secs}秒`
  }
}
</script>

<style scoped lang="scss">
.cache-management {
  .management-card {
    background: #fff;
    border-radius: 8px;
    border: 1px solid #e4e7ed;
    overflow: hidden;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .card-header {
      display: flex;
      align-items: center;
      padding: 20px 20px 16px 20px;
      border-bottom: 1px solid #f0f0f0;

      .card-icon {
        font-size: 24px;
        margin-right: 12px;
        color: #409eff;

        &.warning {
          color: #e6a23c;
        }

        &.info {
          color: #909399;
        }
      }

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }
    }

    .card-content {
      padding: 16px 20px;

      p {
        margin: 0 0 12px 0;
        color: #606266;
        font-size: 14px;
        line-height: 1.5;
      }

      .card-stats {
        span {
          font-size: 12px;
          color: #909399;
        }
      }
    }

    .card-actions {
      padding: 16px 20px 20px 20px;
      display: flex;
      gap: 8px;
    }
  }

  .warmup-progress {
    margin-top: 20px;

    .progress-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .progress-content {
      .progress-info {
        margin-bottom: 20px;

        .info-item {
          text-align: center;

          .info-label {
            font-size: 12px;
            color: #909399;
            margin-bottom: 4px;
          }

          .info-value {
            font-size: 16px;
            font-weight: 500;
            color: #303133;
          }
        }
      }

      .progress-bar {
        margin-bottom: 16px;
      }

      .progress-time {
        text-align: center;
        color: #606266;
        font-size: 14px;
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .el-row {
      flex-direction: column;
      gap: 16px;
    }

    .management-card {
      .card-actions {
        flex-direction: column;
      }
    }

    .progress-info {
      .el-row {
        flex-direction: column;
        gap: 12px;
      }
    }
  }
}
</style>
