<template>
  <div class="provider-grouped-view">
    <!-- 操作栏 -->
    <div class="action-bar">
      <div class="info-section">
        <span class="info-text">
          共 {{ providerGroups.length }} 个供应商，
          总计 {{ totalCacheCount }} 条缓存，
          命中率 {{ overallHitRate }}%
        </span>
      </div>
      <div class="action-buttons">
        <el-button @click="expandAll" :icon="Plus" size="small">
          展开全部
        </el-button>
        <el-button @click="collapseAll" :icon="Minus" size="small">
          收起全部
        </el-button>
        <el-button @click="refreshData" :icon="Refresh" :loading="loading" size="small">
          刷新
        </el-button>
      </div>
    </div>

    <!-- 供应商分组卡片 -->
    <div class="provider-groups" v-loading="loading">
      <div 
        v-for="group in providerGroups" 
        :key="group.provider"
        class="provider-card"
      >
        <!-- 供应商头部 -->
        <div class="provider-header" @click="toggleProvider(group.provider)">
          <div class="provider-info">
            <div class="provider-title">
              <el-tag :type="getProviderTagType(group.provider)" size="large">
                {{ group.provider_name }}
              </el-tag>
              <span class="provider-code">({{ group.provider }})</span>
            </div>
            <div class="provider-stats">
              <span class="stat-item">
                缓存: {{ formatNumber(group.total_cache_count) }}
              </span>
              <span class="stat-item">
                有效: {{ formatNumber(group.valid_cache_count) }}
              </span>
              <span class="stat-item">
                命中: {{ formatNumber(group.total_hit_count) }}
              </span>
              <span class="stat-item">
                更新: {{ formatDateTime(group.last_update_time) }}
              </span>
            </div>
          </div>
          <div class="expand-icon">
            <el-icon>
              <ArrowDown v-if="!expandedProviders.includes(group.provider)" />
              <ArrowUp v-else />
            </el-icon>
          </div>
        </div>

        <!-- 快递公司列表 -->
        <el-collapse-transition>
          <div v-show="expandedProviders.includes(group.provider)" class="companies-section">
            <!-- 🔧 修复：后端返回express_companies为null，显示提示信息 -->
            <div v-if="!group.express_companies || group.express_companies.length === 0" class="no-companies">
              <el-empty description="暂无快递公司详细信息" :image-size="60" />
            </div>
            <div v-else class="companies-grid">
              <div
                v-for="company in group.express_companies"
                :key="company.express_code"
                class="company-card"
              >
                <div class="company-header">
                  <div class="company-name">
                    <span class="express-code">{{ company.express_code }}</span>
                    <span class="express-name">{{ company.express_name }}</span>
                  </div>
                  <div class="company-price">
                    ¥{{ formatPrice(company.avg_price) }}
                  </div>
                </div>
                
                <div class="company-stats">
                  <div class="stat-row">
                    <span class="stat-label">缓存数量:</span>
                    <span class="stat-value">{{ formatNumber(company.cache_count) }}</span>
                  </div>
                  <div class="stat-row">
                    <span class="stat-label">有效缓存:</span>
                    <span class="stat-value">{{ formatNumber(company.valid_cache_count) }}</span>
                  </div>
                  <div class="stat-row">
                    <span class="stat-label">命中次数:</span>
                    <span class="stat-value">{{ formatNumber(company.cache_hit_count) }}</span>
                  </div>
                </div>

                <div class="weight-ranges" v-if="company.weight_ranges && company.weight_ranges.length > 0">
                  <div class="ranges-label">重量档位:</div>
                  <div class="ranges-tags">
                    <el-tag 
                      v-for="weight in company.weight_ranges.slice(0, 5)" 
                      :key="weight"
                      size="small"
                      class="weight-tag"
                    >
                      {{ weight }}kg
                    </el-tag>
                    <el-tag 
                      v-if="company.weight_ranges.length > 5"
                      size="small"
                      type="info"
                    >
                      +{{ company.weight_ranges.length - 5 }}
                    </el-tag>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-collapse-transition>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="!loading && providerGroups.length === 0" class="empty-state">
      <el-empty description="暂无供应商数据" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Minus, Refresh, ArrowDown, ArrowUp } from '@element-plus/icons-vue'
import { weightCacheApi, type ProviderGroup } from '@/api/weightCacheApi'
import { ErrorHandler } from '@/utils/errorHandler'

// Emits
const emit = defineEmits<{
  refresh: []
}>()

// 响应式数据
const loading = ref(false)
const providerGroups = ref<ProviderGroup[]>([])
const expandedProviders = ref<string[]>([])

// 计算属性
const totalCacheCount = computed(() => {
  return providerGroups.value.reduce((sum, group) => sum + group.total_cache_count, 0)
})

const overallHitRate = computed(() => {
  const totalHits = providerGroups.value.reduce((sum, group) => sum + (group.total_hit_count || 0), 0)
  const totalCache = totalCacheCount.value
  return totalCache > 0 ? Math.round((totalHits / totalCache) * 100) : 0
})

// 🚀 加载供应商分组数据
const loadProviderGroupedData = async () => {
  loading.value = true
  try {
    console.log('🔄 正在加载供应商分组数据...')
    
    const response = await weightCacheApi.getProviderGroupedOverviewOptimized()
    
    if (response.success && response.data) {
      providerGroups.value = response.data
      
      console.log('✅ 供应商分组数据加载成功:', {
        providers: response.data.length,
        totalCompanies: response.data.reduce((sum, p) => sum + (p.express_companies?.length || 0), 0)
      })
      
      // 🚀 自动展开第一个供应商（提升用户体验）
      if (response.data.length > 0 && expandedProviders.value.length === 0) {
        expandedProviders.value.push(response.data[0].provider)
      }
    } else {
      throw new Error(response.message || '获取数据失败')
    }
  } catch (error: any) {
    console.error('❌ 供应商分组数据加载失败:', error)
    ErrorHandler.handleApiError(error, '供应商分组数据加载')
  } finally {
    loading.value = false
  }
}

// 刷新数据
const refreshData = async () => {
  await loadProviderGroupedData()
  emit('refresh')
}

// 切换供应商展开状态
const toggleProvider = (provider: string) => {
  const index = expandedProviders.value.indexOf(provider)
  if (index > -1) {
    expandedProviders.value.splice(index, 1)
  } else {
    expandedProviders.value.push(provider)
  }
}

// 展开全部
const expandAll = () => {
  expandedProviders.value = providerGroups.value.map(group => group.provider)
}

// 收起全部
const collapseAll = () => {
  expandedProviders.value = []
}

// 工具函数（添加安全检查）
const formatNumber = (num: number | undefined | null): string => {
  if (num === undefined || num === null) return '0'
  return num.toLocaleString()
}

const formatPercent = (rate: number | undefined | null): string => {
  if (rate === undefined || rate === null) return '0%'
  return `${Math.round(rate * 100)}%`
}

const formatPrice = (price: number | undefined | null): string => {
  if (price === undefined || price === null) return '0.00'
  return price.toFixed(2)
}

const formatDateTime = (datetime: string | undefined | null): string => {
  if (!datetime) return '未知'
  return new Date(datetime).toLocaleString('zh-CN')
}

const getProviderTagType = (provider: string): string => {
  const tagTypes: Record<string, string> = {
    'kuaidi100': 'primary',
    'cainiao': 'success',
    'yida': 'warning',
    'yuntong': 'info'
  }
  return tagTypes[provider] || 'default'
}

// 组件挂载
onMounted(() => {
  loadProviderGroupedData()
})
</script>

<style scoped lang="scss">
.provider-grouped-view {
  .action-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e4e7ed;

    .info-text {
      color: #606266;
      font-size: 14px;
    }

    .action-buttons {
      display: flex;
      gap: 8px;
    }
  }

  .provider-groups {
    .provider-card {
      margin-bottom: 16px;
      background: #fff;
      border-radius: 8px;
      border: 1px solid #e4e7ed;
      overflow: hidden;
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      .provider-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px;
        background: #f8f9fa;
        cursor: pointer;
        transition: background 0.3s ease;

        &:hover {
          background: #e9ecef;
        }

        .provider-info {
          flex: 1;

          .provider-title {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 8px;

            .provider-code {
              color: #909399;
              font-size: 14px;
            }
          }

          .provider-stats {
            display: flex;
            gap: 20px;

            .stat-item {
              font-size: 14px;
              color: #606266;
            }
          }
        }

        .expand-icon {
          color: #909399;
          transition: transform 0.3s ease;
        }
      }

      .companies-section {
        padding: 20px;

        .no-companies {
          text-align: center;
          padding: 40px 20px;
          color: #909399;
        }

        .companies-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
          gap: 16px;

          .company-card {
            padding: 16px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;

            &:hover {
              background: #e9ecef;
              transform: translateY(-2px);
            }

            .company-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 12px;

              .company-name {
                .express-code {
                  font-weight: 600;
                  color: #303133;
                  margin-right: 8px;
                }

                .express-name {
                  color: #606266;
                  font-size: 14px;
                }
              }

              .company-price {
                font-weight: 600;
                color: #67c23a;
                font-size: 16px;
              }
            }

            .company-stats {
              margin-bottom: 12px;

              .stat-row {
                display: flex;
                justify-content: space-between;
                margin-bottom: 4px;

                .stat-label {
                  color: #909399;
                  font-size: 12px;
                }

                .stat-value {
                  color: #409eff;
                  font-weight: 500;
                  font-size: 12px;
                }
              }
            }

            .weight-ranges {
              .ranges-label {
                color: #909399;
                font-size: 12px;
                margin-bottom: 8px;
              }

              .ranges-tags {
                display: flex;
                flex-wrap: wrap;
                gap: 4px;

                .weight-tag {
                  font-size: 11px;
                }
              }
            }
          }
        }
      }
    }
  }

  .empty-state {
    text-align: center;
    padding: 60px 20px;
  }

  // 响应式设计
  @media (max-width: 768px) {
    .action-bar {
      flex-direction: column;
      gap: 12px;
      text-align: center;
    }

    .provider-groups {
      .provider-card {
        .provider-header {
          .provider-info {
            .provider-stats {
              flex-direction: column;
              gap: 8px;
            }
          }
        }

        .companies-section {
          .companies-grid {
            grid-template-columns: 1fr;
          }
        }
      }
    }
  }
}
</style>
