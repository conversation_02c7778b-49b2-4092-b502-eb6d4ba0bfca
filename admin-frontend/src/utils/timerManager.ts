/**
 * 全局定时器管理器
 * 
 * 🎯 功能特性：
 * 1. 统一管理所有定时器，防止内存泄漏
 * 2. 页面可见性检测，智能暂停/恢复定时器
 * 3. 防抖机制，避免重复创建定时器
 * 4. 自动清理机制，组件卸载时自动清理
 * 
 * 🔧 解决问题：
 * - 定时器未正确清理导致的内存泄漏
 * - 多个定时器同时运行造成的资源竞争
 * - 页面不可见时仍然执行定时任务的性能问题
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-29
 */

import { ref, onUnmounted } from 'vue'

// 定时器类型定义
interface TimerConfig {
  id: string
  type: 'interval' | 'timeout'
  callback: () => void | Promise<void>
  delay: number
  immediate?: boolean
  pauseWhenHidden?: boolean
  component?: string // 组件标识，用于批量清理
}

interface ActiveTimer {
  id: string
  type: 'interval' | 'timeout'
  timerId: number
  callback: () => void | Promise<void>
  delay: number
  pauseWhenHidden: boolean
  component?: string
  isPaused: boolean
  lastExecuteTime: number
}

class TimerManager {
  private timers = new Map<string, ActiveTimer>()
  private isPageVisible = ref(true)
  private debounceMap = new Map<string, number>()
  
  constructor() {
    this.initPageVisibilityListener()
  }

  /**
   * 初始化页面可见性监听器
   */
  private initPageVisibilityListener(): void {
    const handleVisibilityChange = () => {
      this.isPageVisible.value = !document.hidden
      
      if (this.isPageVisible.value) {
        console.log('📱 页面变为可见，恢复定时器')
        this.resumeTimers()
      } else {
        console.log('📱 页面变为隐藏，暂停定时器')
        this.pauseTimers()
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    
    // 监听窗口焦点变化
    window.addEventListener('focus', () => {
      this.isPageVisible.value = true
      this.resumeTimers()
    })
    
    window.addEventListener('blur', () => {
      this.isPageVisible.value = false
      this.pauseTimers()
    })
  }

  /**
   * 创建定时器（带防抖机制）
   */
  createTimer(config: TimerConfig): string {
    const { id, type, callback, delay, immediate = false, pauseWhenHidden = true, component } = config
    
    // 防抖检查
    if (this.debounceMap.has(id)) {
      const lastCreateTime = this.debounceMap.get(id)!
      if (Date.now() - lastCreateTime < 100) {
        console.warn(`⚠️ 定时器创建过于频繁，跳过: ${id}`)
        return id
      }
    }
    
    // 清理已存在的同名定时器
    this.clearTimer(id)
    
    // 记录创建时间
    this.debounceMap.set(id, Date.now())
    
    // 包装回调函数，添加错误处理
    const wrappedCallback = async () => {
      try {
        const timer = this.timers.get(id)
        if (timer && !timer.isPaused) {
          timer.lastExecuteTime = Date.now()
          await callback()
        }
      } catch (error) {
        console.error(`🚨 定时器回调执行异常 [${id}]:`, error)
      }
    }

    // 创建定时器
    let timerId: number
    if (type === 'interval') {
      timerId = window.setInterval(wrappedCallback, delay)
    } else {
      timerId = window.setTimeout(wrappedCallback, delay)
    }

    // 存储定时器信息
    const timer: ActiveTimer = {
      id,
      type,
      timerId,
      callback: wrappedCallback,
      delay,
      pauseWhenHidden,
      component,
      isPaused: false,
      lastExecuteTime: Date.now()
    }
    
    this.timers.set(id, timer)
    
    // 立即执行（如果需要）
    if (immediate) {
      wrappedCallback()
    }
    
    console.log(`⏰ 定时器已创建: ${id} (${type}, ${delay}ms)`)
    return id
  }

  /**
   * 清理指定定时器
   */
  clearTimer(id: string): boolean {
    const timer = this.timers.get(id)
    if (!timer) return false

    if (timer.type === 'interval') {
      clearInterval(timer.timerId)
    } else {
      clearTimeout(timer.timerId)
    }

    this.timers.delete(id)
    this.debounceMap.delete(id)
    
    console.log(`🗑️ 定时器已清理: ${id}`)
    return true
  }

  /**
   * 清理组件相关的所有定时器
   */
  clearComponentTimers(component: string): number {
    let clearedCount = 0
    
    for (const [id, timer] of this.timers.entries()) {
      if (timer.component === component) {
        this.clearTimer(id)
        clearedCount++
      }
    }
    
    if (clearedCount > 0) {
      console.log(`🗑️ 已清理组件 [${component}] 的 ${clearedCount} 个定时器`)
    }
    
    return clearedCount
  }

  /**
   * 暂停所有可暂停的定时器
   */
  private pauseTimers(): void {
    for (const timer of this.timers.values()) {
      if (timer.pauseWhenHidden && !timer.isPaused) {
        timer.isPaused = true
      }
    }
  }

  /**
   * 恢复所有暂停的定时器
   */
  private resumeTimers(): void {
    for (const timer of this.timers.values()) {
      if (timer.isPaused) {
        timer.isPaused = false
      }
    }
  }

  /**
   * 获取定时器统计信息
   */
  getStats(): {
    total: number
    active: number
    paused: number
    byType: Record<string, number>
    byComponent: Record<string, number>
  } {
    const stats = {
      total: this.timers.size,
      active: 0,
      paused: 0,
      byType: { interval: 0, timeout: 0 },
      byComponent: {} as Record<string, number>
    }

    for (const timer of this.timers.values()) {
      if (timer.isPaused) {
        stats.paused++
      } else {
        stats.active++
      }
      
      stats.byType[timer.type]++
      
      if (timer.component) {
        stats.byComponent[timer.component] = (stats.byComponent[timer.component] || 0) + 1
      }
    }

    return stats
  }

  /**
   * 清理所有定时器
   */
  clearAll(): void {
    const count = this.timers.size
    
    for (const id of this.timers.keys()) {
      this.clearTimer(id)
    }
    
    console.log(`🗑️ 已清理所有定时器，共 ${count} 个`)
  }
}

// 创建全局单例
export const timerManager = new TimerManager()

/**
 * Vue 组合式函数：在组件中使用定时器管理器
 */
export function useTimerManager(componentName?: string) {
  const componentId = componentName || 'unknown'
  
  // 组件卸载时自动清理定时器
  onUnmounted(() => {
    timerManager.clearComponentTimers(componentId)
  })

  return {
    createTimer: (config: Omit<TimerConfig, 'component'>) => 
      timerManager.createTimer({ ...config, component: componentId }),
    clearTimer: timerManager.clearTimer.bind(timerManager),
    clearComponentTimers: () => timerManager.clearComponentTimers(componentId),
    getStats: timerManager.getStats.bind(timerManager)
  }
}

export default timerManager
