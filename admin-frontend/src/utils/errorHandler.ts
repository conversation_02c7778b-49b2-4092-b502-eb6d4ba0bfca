import { ElMessage, ElNotification } from 'element-plus'

// 错误类型定义
export interface ApiError {
  code: number
  message: string
  details?: any
  timestamp?: string
}

export interface ValidationError {
  field: string
  message: string
  value?: any
}

// 错误处理类
export class ErrorHandler {
  // 🚀 API错误处理
  static handleApiError(error: any, context?: string): void {
    console.error(`❌ API错误 ${context ? `[${context}]` : ''}:`, error)

    let message = '操作失败，请稍后重试'
    let type: 'error' | 'warning' = 'error'

    if (error?.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 400:
          message = data?.message || '请求参数错误'
          type = 'warning'
          break
        case 401:
          message = '登录已过期，请重新登录'
          this.handleAuthError()
          return
        case 403:
          message = '权限不足，无法执行此操作'
          type = 'warning'
          break
        case 404:
          message = '请求的资源不存在'
          type = 'warning'
          break
        case 429:
          message = '请求过于频繁，请稍后再试'
          type = 'warning'
          break
        case 500:
          message = '服务器内部错误，请联系管理员'
          break
        case 502:
        case 503:
        case 504:
          message = '服务暂时不可用，请稍后重试'
          break
        default:
          message = data?.message || `请求失败 (${status})`
      }
    } else if (error?.code === 'NETWORK_ERROR') {
      message = '网络连接失败，请检查网络设置'
    } else if (error?.message) {
      message = error.message
    }

    ElMessage({
      type,
      message,
      duration: type === 'error' ? 5000 : 3000,
      showClose: true
    })
  }

  // 🔧 认证错误处理（优化跳转逻辑）
  static handleAuthError(): void {
    ElNotification({
      title: '登录过期',
      message: '您的登录已过期，请重新登录',
      type: 'warning',
      duration: 5000
    })

    // 🔧 使用用户store的登出方法，确保状态一致性
    try {
      const { useUserStore } = require('@/store/modules/user')
      const userStore = useUserStore()

      // 异步执行登出，避免阻塞当前流程
      setTimeout(() => {
        userStore.logOut()
      }, 100)

    } catch (error) {
      console.error('❌ 调用用户store登出失败:', error)

      // 降级处理：直接清理和跳转
      localStorage.removeItem('token')
      localStorage.removeItem('admin_token')
      localStorage.removeItem('userInfo')

      // 使用hash路由跳转，避免页面刷新
      setTimeout(() => {
        window.location.hash = '#/login'
      }, 1000)
    }
  }

  // 🚀 验证错误处理
  static handleValidationErrors(errors: ValidationError[]): void {
    if (!errors || errors.length === 0) return

    const errorMessages = errors.map(error => `${error.field}: ${error.message}`).join('\n')
    
    ElMessage({
      type: 'warning',
      message: `数据验证失败:\n${errorMessages}`,
      duration: 5000,
      showClose: true,
      dangerouslyUseHTMLString: false
    })
  }

  // 🚀 业务逻辑错误处理
  static handleBusinessError(error: string | ApiError, context?: string): void {
    console.warn(`⚠️ 业务错误 ${context ? `[${context}]` : ''}:`, error)

    const message = typeof error === 'string' ? error : error.message
    
    ElMessage({
      type: 'warning',
      message,
      duration: 4000,
      showClose: true
    })
  }

  // 🚀 成功消息处理
  static handleSuccess(message: string, details?: string): void {
    ElMessage({
      type: 'success',
      message,
      duration: 3000
    })

    if (details) {
      console.log('✅ 操作成功:', details)
    }
  }

  // 🚀 警告消息处理
  static handleWarning(message: string, details?: string): void {
    ElMessage({
      type: 'warning',
      message,
      duration: 4000,
      showClose: true
    })

    if (details) {
      console.warn('⚠️ 警告:', details)
    }
  }

  // 🚀 信息消息处理
  static handleInfo(message: string, details?: string): void {
    ElMessage({
      type: 'info',
      message,
      duration: 3000
    })

    if (details) {
      console.info('ℹ️ 信息:', details)
    }
  }

  // 🚀 加载状态错误处理
  static handleLoadingError(error: any, context: string): void {
    console.error(`❌ 加载失败 [${context}]:`, error)
    
    ElNotification({
      title: '加载失败',
      message: `${context}加载失败，请刷新页面重试`,
      type: 'error',
      duration: 5000,
      position: 'top-right'
    })
  }

  // 🚀 重试机制
  static async withRetry<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    delay: number = 1000,
    context?: string
  ): Promise<T> {
    let lastError: any
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation()
      } catch (error) {
        lastError = error
        
        if (attempt === maxRetries) {
          console.error(`❌ 重试失败 [${context}] (${attempt}/${maxRetries}):`, error)
          break
        }
        
        console.warn(`⚠️ 重试 [${context}] (${attempt}/${maxRetries}):`, error)
        await new Promise(resolve => setTimeout(resolve, delay * attempt))
      }
    }
    
    throw lastError
  }

  // 🚀 异步操作包装器
  static async safeAsync<T>(
    operation: () => Promise<T>,
    context: string,
    showLoading: boolean = true
  ): Promise<T | null> {
    try {
      if (showLoading) {
        console.log(`🔄 开始执行 [${context}]...`)
      }
      
      const result = await operation()
      
      if (showLoading) {
        console.log(`✅ 执行成功 [${context}]`)
      }
      
      return result
    } catch (error) {
      this.handleApiError(error, context)
      return null
    }
  }
}

// 🚀 数据验证工具
export class DataValidator {
  // 验证必填字段
  static validateRequired(value: any, fieldName: string): ValidationError | null {
    if (value === null || value === undefined || value === '') {
      return {
        field: fieldName,
        message: '此字段为必填项',
        value
      }
    }
    return null
  }

  // 验证数字范围
  static validateNumberRange(
    value: number,
    min: number,
    max: number,
    fieldName: string
  ): ValidationError | null {
    if (isNaN(value) || value < min || value > max) {
      return {
        field: fieldName,
        message: `数值必须在 ${min} 到 ${max} 之间`,
        value
      }
    }
    return null
  }

  // 验证字符串长度
  static validateStringLength(
    value: string,
    minLength: number,
    maxLength: number,
    fieldName: string
  ): ValidationError | null {
    if (!value || value.length < minLength || value.length > maxLength) {
      return {
        field: fieldName,
        message: `长度必须在 ${minLength} 到 ${maxLength} 个字符之间`,
        value
      }
    }
    return null
  }

  // 验证邮箱格式
  static validateEmail(value: string, fieldName: string): ValidationError | null {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(value)) {
      return {
        field: fieldName,
        message: '邮箱格式不正确',
        value
      }
    }
    return null
  }

  // 批量验证
  static validateBatch(validators: (() => ValidationError | null)[]): ValidationError[] {
    const errors: ValidationError[] = []
    
    validators.forEach(validator => {
      const error = validator()
      if (error) {
        errors.push(error)
      }
    })
    
    return errors
  }
}

// 导出默认实例
export const errorHandler = ErrorHandler
export const dataValidator = DataValidator
