/**
 * 性能监控中心
 * 
 * 🎯 功能特性：
 * 1. 页面性能监控（FCP、LCP、FID、CLS）
 * 2. 资源加载监控
 * 3. 用户行为追踪
 * 4. 错误监控和报告
 * 
 * 🔧 解决问题：
 * - 缺乏系统性能监控
 * - 用户体验问题难以发现
 * - 性能瓶颈定位困难
 * - 错误追踪不完整
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-29
 */

import { reactive, ref } from 'vue'

// 性能指标接口
interface PerformanceMetrics {
  // Core Web Vitals
  fcp: number // First Contentful Paint
  lcp: number // Largest Contentful Paint
  fid: number // First Input Delay
  cls: number // Cumulative Layout Shift
  
  // 自定义指标
  pageLoadTime: number
  domReadyTime: number
  resourceLoadTime: number
  memoryUsage: number
  
  // 网络信息
  connectionType: string
  effectiveType: string
  downlink: number
  rtt: number
}

// 用户行为事件接口
interface UserEvent {
  id: string
  type: 'click' | 'scroll' | 'input' | 'navigation' | 'error'
  target: string
  timestamp: number
  data: any
  sessionId: string
  userId?: string
}

// 错误信息接口
interface ErrorInfo {
  id: string
  type: 'javascript' | 'resource' | 'network' | 'promise'
  message: string
  stack?: string
  filename?: string
  lineno?: number
  colno?: number
  timestamp: number
  url: string
  userAgent: string
  userId?: string
}

// 资源加载信息接口
interface ResourceInfo {
  name: string
  type: string
  size: number
  duration: number
  startTime: number
  endTime: number
  status: 'success' | 'error'
}

class PerformanceMonitor {
  private metrics = reactive<PerformanceMetrics>({
    fcp: 0,
    lcp: 0,
    fid: 0,
    cls: 0,
    pageLoadTime: 0,
    domReadyTime: 0,
    resourceLoadTime: 0,
    memoryUsage: 0,
    connectionType: 'unknown',
    effectiveType: 'unknown',
    downlink: 0,
    rtt: 0
  })

  private userEvents: UserEvent[] = []
  private errors: ErrorInfo[] = []
  private resources: ResourceInfo[] = []
  private sessionId = this.generateSessionId()
  private isMonitoring = ref(false)
  private observers: PerformanceObserver[] = []

  constructor() {
    this.initMonitoring()
  }

  /**
   * 初始化监控
   */
  private initMonitoring(): void {
    if (typeof window === 'undefined') return

    this.isMonitoring.value = true
    
    // 监控Core Web Vitals
    this.initWebVitalsMonitoring()
    
    // 监控资源加载
    this.initResourceMonitoring()
    
    // 监控用户行为
    this.initUserBehaviorMonitoring()
    
    // 监控错误
    this.initErrorMonitoring()
    
    // 监控网络信息
    this.initNetworkMonitoring()
    
    // 定期更新内存使用情况
    this.initMemoryMonitoring()
    
    console.log('📊 性能监控已启动')
  }

  /**
   * 初始化Web Vitals监控
   */
  private initWebVitalsMonitoring(): void {
    // FCP - First Contentful Paint
    if ('PerformanceObserver' in window) {
      const fcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint')
        if (fcpEntry) {
          this.metrics.fcp = fcpEntry.startTime
          console.log(`🎨 FCP: ${fcpEntry.startTime.toFixed(2)}ms`)
        }
      })
      fcpObserver.observe({ entryTypes: ['paint'] })
      this.observers.push(fcpObserver)

      // LCP - Largest Contentful Paint
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        const lastEntry = entries[entries.length - 1]
        if (lastEntry) {
          this.metrics.lcp = lastEntry.startTime
          console.log(`🖼️ LCP: ${lastEntry.startTime.toFixed(2)}ms`)
        }
      })
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })
      this.observers.push(lcpObserver)

      // FID - First Input Delay
      const fidObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry: any) => {
          if (entry.processingStart && entry.startTime) {
            const fid = entry.processingStart - entry.startTime
            this.metrics.fid = fid
            console.log(`⚡ FID: ${fid.toFixed(2)}ms`)
          }
        })
      })
      fidObserver.observe({ entryTypes: ['first-input'] })
      this.observers.push(fidObserver)

      // CLS - Cumulative Layout Shift
      let clsValue = 0
      const clsObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry: any) => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value
            this.metrics.cls = clsValue
          }
        })
      })
      clsObserver.observe({ entryTypes: ['layout-shift'] })
      this.observers.push(clsObserver)
    }

    // 页面加载时间
    window.addEventListener('load', () => {
      setTimeout(() => {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
        if (navigation) {
          this.metrics.pageLoadTime = navigation.loadEventEnd - navigation.fetchStart
          this.metrics.domReadyTime = navigation.domContentLoadedEventEnd - navigation.fetchStart
          console.log(`📄 页面加载时间: ${this.metrics.pageLoadTime.toFixed(2)}ms`)
        }
      }, 0)
    })
  }

  /**
   * 初始化资源监控
   */
  private initResourceMonitoring(): void {
    if ('PerformanceObserver' in window) {
      const resourceObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry: any) => {
          const resource: ResourceInfo = {
            name: entry.name,
            type: this.getResourceType(entry.name),
            size: entry.transferSize || 0,
            duration: entry.duration,
            startTime: entry.startTime,
            endTime: entry.responseEnd,
            status: entry.responseStatus >= 400 ? 'error' : 'success'
          }
          
          this.resources.push(resource)
          
          // 限制资源记录数量
          if (this.resources.length > 1000) {
            this.resources = this.resources.slice(-500)
          }
        })
        
        // 更新资源加载总时间
        this.updateResourceLoadTime()
      })
      
      resourceObserver.observe({ entryTypes: ['resource'] })
      this.observers.push(resourceObserver)
    }
  }

  /**
   * 初始化用户行为监控
   */
  private initUserBehaviorMonitoring(): void {
    // 点击事件
    document.addEventListener('click', (event) => {
      this.recordUserEvent('click', event.target as Element, {
        x: (event as MouseEvent).clientX,
        y: (event as MouseEvent).clientY,
        button: (event as MouseEvent).button
      })
    })

    // 滚动事件（防抖）
    let scrollTimer: number
    document.addEventListener('scroll', () => {
      clearTimeout(scrollTimer)
      scrollTimer = window.setTimeout(() => {
        this.recordUserEvent('scroll', document.documentElement, {
          scrollTop: window.pageYOffset,
          scrollLeft: window.pageXOffset
        })
      }, 100)
    })

    // 输入事件
    document.addEventListener('input', (event) => {
      const target = event.target as HTMLInputElement
      if (target.type !== 'password') { // 不记录密码输入
        this.recordUserEvent('input', target, {
          value: target.value.length, // 只记录长度，不记录具体内容
          type: target.type
        })
      }
    })

    // 页面导航
    window.addEventListener('beforeunload', () => {
      this.recordUserEvent('navigation', document, {
        type: 'beforeunload',
        url: window.location.href
      })
    })
  }

  /**
   * 初始化错误监控
   */
  private initErrorMonitoring(): void {
    // JavaScript错误
    window.addEventListener('error', (event) => {
      this.recordError({
        type: 'javascript',
        message: event.message,
        stack: event.error?.stack,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno
      })
    })

    // Promise rejection
    window.addEventListener('unhandledrejection', (event) => {
      this.recordError({
        type: 'promise',
        message: event.reason?.message || String(event.reason),
        stack: event.reason?.stack
      })
    })

    // 资源加载错误
    window.addEventListener('error', (event) => {
      if (event.target !== window) {
        this.recordError({
          type: 'resource',
          message: `Resource load error: ${(event.target as any)?.src || (event.target as any)?.href}`,
          filename: (event.target as any)?.src || (event.target as any)?.href
        })
      }
    }, true)
  }

  /**
   * 初始化网络监控
   */
  private initNetworkMonitoring(): void {
    if ('connection' in navigator) {
      const connection = (navigator as any).connection
      this.metrics.connectionType = connection.type || 'unknown'
      this.metrics.effectiveType = connection.effectiveType || 'unknown'
      this.metrics.downlink = connection.downlink || 0
      this.metrics.rtt = connection.rtt || 0

      connection.addEventListener('change', () => {
        this.metrics.connectionType = connection.type || 'unknown'
        this.metrics.effectiveType = connection.effectiveType || 'unknown'
        this.metrics.downlink = connection.downlink || 0
        this.metrics.rtt = connection.rtt || 0
      })
    }
  }

  /**
   * 初始化内存监控
   */
  private initMemoryMonitoring(): void {
    const updateMemory = () => {
      if (performance.memory) {
        this.metrics.memoryUsage = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024)
      }
    }

    updateMemory()
    setInterval(updateMemory, 5000)
  }

  /**
   * 记录用户事件
   */
  private recordUserEvent(type: UserEvent['type'], target: Element | Document, data: any): void {
    const event: UserEvent = {
      id: this.generateId(),
      type,
      target: this.getElementSelector(target),
      timestamp: Date.now(),
      data,
      sessionId: this.sessionId,
      userId: this.getCurrentUserId()
    }

    this.userEvents.push(event)
    
    // 限制事件记录数量
    if (this.userEvents.length > 1000) {
      this.userEvents = this.userEvents.slice(-500)
    }
  }

  /**
   * 记录错误
   */
  private recordError(errorData: Partial<ErrorInfo>): void {
    const error: ErrorInfo = {
      id: this.generateId(),
      type: errorData.type || 'javascript',
      message: errorData.message || 'Unknown error',
      stack: errorData.stack,
      filename: errorData.filename,
      lineno: errorData.lineno,
      colno: errorData.colno,
      timestamp: Date.now(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      userId: this.getCurrentUserId()
    }

    this.errors.push(error)
    
    // 限制错误记录数量
    if (this.errors.length > 100) {
      this.errors = this.errors.slice(-50)
    }

    console.error('🚨 错误已记录:', error)
  }

  /**
   * 更新资源加载时间
   */
  private updateResourceLoadTime(): void {
    const totalDuration = this.resources.reduce((sum, resource) => sum + resource.duration, 0)
    this.metrics.resourceLoadTime = totalDuration
  }

  /**
   * 获取资源类型
   */
  private getResourceType(url: string): string {
    if (url.includes('.js')) return 'script'
    if (url.includes('.css')) return 'stylesheet'
    if (url.match(/\.(png|jpg|jpeg|gif|svg|webp)$/)) return 'image'
    if (url.match(/\.(woff|woff2|ttf|eot)$/)) return 'font'
    return 'other'
  }

  /**
   * 获取元素选择器
   */
  private getElementSelector(element: Element | Document): string {
    if (element === document) return 'document'
    if (!(element instanceof Element)) return 'unknown'

    if (element.id) return `#${element.id}`
    if (element.className) return `.${element.className.split(' ')[0]}`
    return element.tagName.toLowerCase()
  }

  /**
   * 获取当前用户ID
   */
  private getCurrentUserId(): string | undefined {
    // 这里可以从用户store或其他地方获取用户ID
    try {
      const userStore = (window as any).__USER_STORE__
      return userStore?.info?.id
    } catch {
      return undefined
    }
  }

  /**
   * 生成会话ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 生成ID
   */
  private generateId(): string {
    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 获取性能指标
   */
  getMetrics(): PerformanceMetrics {
    return { ...this.metrics }
  }

  /**
   * 获取用户事件
   */
  getUserEvents(limit = 100): UserEvent[] {
    return this.userEvents.slice(-limit)
  }

  /**
   * 获取错误列表
   */
  getErrors(limit = 50): ErrorInfo[] {
    return this.errors.slice(-limit)
  }

  /**
   * 获取资源信息
   */
  getResources(limit = 100): ResourceInfo[] {
    return this.resources.slice(-limit)
  }

  /**
   * 获取性能报告
   */
  getPerformanceReport(): {
    metrics: PerformanceMetrics
    userEvents: UserEvent[]
    errors: ErrorInfo[]
    resources: ResourceInfo[]
    sessionId: string
    timestamp: number
  } {
    return {
      metrics: this.getMetrics(),
      userEvents: this.getUserEvents(50),
      errors: this.getErrors(20),
      resources: this.getResources(50),
      sessionId: this.sessionId,
      timestamp: Date.now()
    }
  }

  /**
   * 清理监控数据
   */
  clear(): void {
    this.userEvents = []
    this.errors = []
    this.resources = []
    console.log('🧹 监控数据已清理')
  }

  /**
   * 停止监控
   */
  stop(): void {
    this.observers.forEach(observer => observer.disconnect())
    this.observers = []
    this.isMonitoring.value = false
    console.log('⏹️ 性能监控已停止')
  }
}

// 创建全局性能监控实例
export const performanceMonitor = new PerformanceMonitor()

/**
 * Vue 组合式函数：在组件中使用性能监控
 */
export function usePerformanceMonitor() {
  return {
    getMetrics: performanceMonitor.getMetrics.bind(performanceMonitor),
    getUserEvents: performanceMonitor.getUserEvents.bind(performanceMonitor),
    getErrors: performanceMonitor.getErrors.bind(performanceMonitor),
    getResources: performanceMonitor.getResources.bind(performanceMonitor),
    getPerformanceReport: performanceMonitor.getPerformanceReport.bind(performanceMonitor),
    clear: performanceMonitor.clear.bind(performanceMonitor)
  }
}

export default performanceMonitor
