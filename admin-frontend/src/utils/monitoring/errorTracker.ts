/**
 * 错误追踪系统
 * 
 * 🎯 功能特性：
 * 1. 全局错误捕获和分类
 * 2. 错误上报和聚合
 * 3. 错误恢复策略
 * 4. 错误分析和统计
 * 
 * 🔧 解决问题：
 * - 错误信息分散，难以统一管理
 * - 缺乏错误恢复机制
 * - 错误分析不够深入
 * - 用户体验受错误影响
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-29
 */

import { reactive, ref } from 'vue'
import { ElNotification } from 'element-plus'

// 错误级别枚举
enum ErrorLevel {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

// 错误类型枚举
enum ErrorType {
  JAVASCRIPT = 'javascript',
  NETWORK = 'network',
  RESOURCE = 'resource',
  PROMISE = 'promise',
  VUE = 'vue',
  API = 'api',
  CUSTOM = 'custom'
}

// 错误信息接口
interface ErrorInfo {
  id: string
  type: ErrorType
  level: ErrorLevel
  message: string
  stack?: string
  filename?: string
  lineno?: number
  colno?: number
  timestamp: number
  url: string
  userAgent: string
  userId?: string
  sessionId: string
  context?: any
  tags: string[]
  fingerprint: string // 错误指纹，用于聚合
  count: number // 同类错误计数
}

// 错误统计接口
interface ErrorStats {
  total: number
  byType: Record<ErrorType, number>
  byLevel: Record<ErrorLevel, number>
  topErrors: Array<{ fingerprint: string; count: number; message: string }>
  errorRate: number // 错误率（错误数/总操作数）
}

// 错误恢复策略接口
interface RecoveryStrategy {
  type: ErrorType
  level: ErrorLevel
  handler: (error: ErrorInfo) => Promise<boolean>
  maxRetries: number
  retryDelay: number
}

class ErrorTracker {
  private errors: ErrorInfo[] = []
  private errorMap = new Map<string, ErrorInfo>() // 用于错误聚合
  private sessionId = this.generateSessionId()
  private isEnabled = ref(true)
  private maxErrors = 1000
  private reportEndpoint = '/api/v1/errors/report'
  
  // 错误统计
  private stats = reactive<ErrorStats>({
    total: 0,
    byType: Object.values(ErrorType).reduce((acc, type) => ({ ...acc, [type]: 0 }), {} as Record<ErrorType, number>),
    byLevel: Object.values(ErrorLevel).reduce((acc, level) => ({ ...acc, [level]: 0 }), {} as Record<ErrorLevel, number>),
    topErrors: [],
    errorRate: 0
  })

  // 恢复策略
  private recoveryStrategies: RecoveryStrategy[] = [
    {
      type: ErrorType.NETWORK,
      level: ErrorLevel.MEDIUM,
      handler: this.handleNetworkError.bind(this),
      maxRetries: 3,
      retryDelay: 1000
    },
    {
      type: ErrorType.API,
      level: ErrorLevel.HIGH,
      handler: this.handleApiError.bind(this),
      maxRetries: 2,
      retryDelay: 2000
    },
    {
      type: ErrorType.RESOURCE,
      level: ErrorLevel.LOW,
      handler: this.handleResourceError.bind(this),
      maxRetries: 1,
      retryDelay: 500
    }
  ]

  constructor() {
    this.initErrorHandlers()
  }

  /**
   * 初始化错误处理器
   */
  private initErrorHandlers(): void {
    // JavaScript错误
    window.addEventListener('error', (event) => {
      this.captureError({
        type: ErrorType.JAVASCRIPT,
        level: this.getErrorLevel(event.error),
        message: event.message,
        stack: event.error?.stack,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        context: { event: 'window.error' }
      })
    })

    // Promise rejection
    window.addEventListener('unhandledrejection', (event) => {
      this.captureError({
        type: ErrorType.PROMISE,
        level: ErrorLevel.HIGH,
        message: event.reason?.message || String(event.reason),
        stack: event.reason?.stack,
        context: { event: 'unhandledrejection', reason: event.reason }
      })
    })

    // 资源加载错误
    window.addEventListener('error', (event) => {
      if (event.target !== window) {
        const target = event.target as HTMLElement
        this.captureError({
          type: ErrorType.RESOURCE,
          level: ErrorLevel.LOW,
          message: `Resource load failed: ${target.tagName}`,
          filename: (target as any).src || (target as any).href,
          context: { 
            tagName: target.tagName,
            src: (target as any).src,
            href: (target as any).href
          }
        })
      }
    }, true)

    console.log('🚨 错误追踪系统已启动')
  }

  /**
   * 捕获错误
   */
  captureError(errorData: Partial<ErrorInfo>): string {
    if (!this.isEnabled.value) return ''

    const error: ErrorInfo = {
      id: this.generateId(),
      type: errorData.type || ErrorType.CUSTOM,
      level: errorData.level || ErrorLevel.MEDIUM,
      message: errorData.message || 'Unknown error',
      stack: errorData.stack,
      filename: errorData.filename,
      lineno: errorData.lineno,
      colno: errorData.colno,
      timestamp: Date.now(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      userId: this.getCurrentUserId(),
      sessionId: this.sessionId,
      context: errorData.context,
      tags: errorData.tags || [],
      fingerprint: this.generateFingerprint(errorData),
      count: 1
    }

    // 错误聚合
    const existingError = this.errorMap.get(error.fingerprint)
    if (existingError) {
      existingError.count++
      existingError.timestamp = error.timestamp // 更新最后发生时间
    } else {
      this.errorMap.set(error.fingerprint, error)
      this.errors.push(error)
    }

    // 更新统计
    this.updateStats(error)

    // 限制错误数量
    if (this.errors.length > this.maxErrors) {
      const removedError = this.errors.shift()!
      this.errorMap.delete(removedError.fingerprint)
    }

    // 尝试恢复
    this.attemptRecovery(error)

    // 用户通知
    this.notifyUser(error)

    // 上报错误
    this.reportError(error)

    console.error('🚨 错误已捕获:', error)
    return error.id
  }

  /**
   * 生成错误指纹
   */
  private generateFingerprint(errorData: Partial<ErrorInfo>): string {
    const parts = [
      errorData.type || 'unknown',
      errorData.message || 'unknown',
      errorData.filename || 'unknown',
      errorData.lineno || 0
    ]
    
    return btoa(parts.join('|')).replace(/[^a-zA-Z0-9]/g, '').substring(0, 16)
  }

  /**
   * 获取错误级别
   */
  private getErrorLevel(error: Error): ErrorLevel {
    if (!error) return ErrorLevel.LOW

    const message = error.message.toLowerCase()
    
    if (message.includes('network') || message.includes('fetch')) {
      return ErrorLevel.MEDIUM
    }
    
    if (message.includes('syntax') || message.includes('reference')) {
      return ErrorLevel.HIGH
    }
    
    if (message.includes('security') || message.includes('permission')) {
      return ErrorLevel.CRITICAL
    }
    
    return ErrorLevel.MEDIUM
  }

  /**
   * 更新统计信息
   */
  private updateStats(error: ErrorInfo): void {
    this.stats.total++
    this.stats.byType[error.type]++
    this.stats.byLevel[error.level]++

    // 更新热门错误
    this.updateTopErrors()

    // 计算错误率（简化版本）
    this.stats.errorRate = (this.stats.total / Math.max(this.stats.total * 10, 1)) * 100
  }

  /**
   * 更新热门错误
   */
  private updateTopErrors(): void {
    const errorCounts = Array.from(this.errorMap.values())
      .sort((a, b) => b.count - a.count)
      .slice(0, 10)
      .map(error => ({
        fingerprint: error.fingerprint,
        count: error.count,
        message: error.message
      }))

    this.stats.topErrors = errorCounts
  }

  /**
   * 尝试错误恢复
   */
  private async attemptRecovery(error: ErrorInfo): Promise<void> {
    const strategy = this.recoveryStrategies.find(
      s => s.type === error.type && s.level === error.level
    )

    if (strategy) {
      try {
        const recovered = await strategy.handler(error)
        if (recovered) {
          console.log(`✅ 错误恢复成功: ${error.id}`)
          error.tags.push('recovered')
        }
      } catch (recoveryError) {
        console.error('❌ 错误恢复失败:', recoveryError)
      }
    }
  }

  /**
   * 网络错误恢复
   */
  private async handleNetworkError(error: ErrorInfo): Promise<boolean> {
    // 检查网络连接
    if (!navigator.onLine) {
      return false
    }

    // 简单的重试逻辑
    try {
      const response = await fetch('/api/health', { method: 'HEAD' })
      return response.ok
    } catch {
      return false
    }
  }

  /**
   * API错误恢复
   */
  private async handleApiError(error: ErrorInfo): Promise<boolean> {
    // 这里可以实现API错误的恢复逻辑
    // 比如重新获取token、切换API端点等
    return false
  }

  /**
   * 资源错误恢复
   */
  private async handleResourceError(error: ErrorInfo): Promise<boolean> {
    // 资源加载错误通常不需要特殊恢复
    return false
  }

  /**
   * 用户通知
   */
  private notifyUser(error: ErrorInfo): void {
    // 只对高级别错误进行用户通知
    if (error.level === ErrorLevel.HIGH || error.level === ErrorLevel.CRITICAL) {
      ElNotification({
        title: '系统错误',
        message: this.getUserFriendlyMessage(error),
        type: 'error',
        duration: 5000
      })
    }
  }

  /**
   * 获取用户友好的错误信息
   */
  private getUserFriendlyMessage(error: ErrorInfo): string {
    switch (error.type) {
      case ErrorType.NETWORK:
        return '网络连接异常，请检查网络设置'
      case ErrorType.API:
        return '服务暂时不可用，请稍后重试'
      case ErrorType.RESOURCE:
        return '资源加载失败，请刷新页面'
      default:
        return '系统遇到了一些问题，我们正在处理中'
    }
  }

  /**
   * 上报错误
   */
  private async reportError(error: ErrorInfo): Promise<void> {
    try {
      // 只上报中高级别的错误
      if (error.level === ErrorLevel.LOW) return

      await fetch(this.reportEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          error,
          timestamp: Date.now(),
          url: window.location.href
        })
      })
    } catch (reportError) {
      console.warn('⚠️ 错误上报失败:', reportError)
    }
  }

  /**
   * 获取当前用户ID
   */
  private getCurrentUserId(): string | undefined {
    try {
      const userStore = (window as any).__USER_STORE__
      return userStore?.info?.id
    } catch {
      return undefined
    }
  }

  /**
   * 生成会话ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 生成ID
   */
  private generateId(): string {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 手动捕获错误
   */
  captureException(error: Error, context?: any, tags?: string[]): string {
    return this.captureError({
      type: ErrorType.CUSTOM,
      level: this.getErrorLevel(error),
      message: error.message,
      stack: error.stack,
      context,
      tags
    })
  }

  /**
   * 捕获消息
   */
  captureMessage(message: string, level: ErrorLevel = ErrorLevel.MEDIUM, context?: any): string {
    return this.captureError({
      type: ErrorType.CUSTOM,
      level,
      message,
      context
    })
  }

  /**
   * 获取错误列表
   */
  getErrors(limit = 100): ErrorInfo[] {
    return this.errors.slice(-limit)
  }

  /**
   * 获取错误统计
   */
  getStats(): ErrorStats {
    return { ...this.stats }
  }

  /**
   * 根据指纹获取错误
   */
  getErrorByFingerprint(fingerprint: string): ErrorInfo | undefined {
    return this.errorMap.get(fingerprint)
  }

  /**
   * 清理错误数据
   */
  clear(): void {
    this.errors = []
    this.errorMap.clear()
    
    // 重置统计
    this.stats.total = 0
    Object.keys(this.stats.byType).forEach(key => {
      this.stats.byType[key as ErrorType] = 0
    })
    Object.keys(this.stats.byLevel).forEach(key => {
      this.stats.byLevel[key as ErrorLevel] = 0
    })
    this.stats.topErrors = []
    this.stats.errorRate = 0
    
    console.log('🧹 错误数据已清理')
  }

  /**
   * 启用/禁用错误追踪
   */
  toggle(): boolean {
    this.isEnabled.value = !this.isEnabled.value
    console.log(`🚨 错误追踪${this.isEnabled.value ? '已启用' : '已禁用'}`)
    return this.isEnabled.value
  }

  /**
   * 导出错误数据
   */
  exportErrors(): string {
    return JSON.stringify({
      errors: this.errors,
      stats: this.stats,
      sessionId: this.sessionId,
      timestamp: Date.now()
    }, null, 2)
  }
}

// 创建全局错误追踪实例
export const errorTracker = new ErrorTracker()

/**
 * Vue 组合式函数：在组件中使用错误追踪
 */
export function useErrorTracker() {
  return {
    captureError: errorTracker.captureError.bind(errorTracker),
    captureException: errorTracker.captureException.bind(errorTracker),
    captureMessage: errorTracker.captureMessage.bind(errorTracker),
    getErrors: errorTracker.getErrors.bind(errorTracker),
    getStats: errorTracker.getStats.bind(errorTracker),
    getErrorByFingerprint: errorTracker.getErrorByFingerprint.bind(errorTracker),
    clear: errorTracker.clear.bind(errorTracker),
    toggle: errorTracker.toggle.bind(errorTracker),
    exportErrors: errorTracker.exportErrors.bind(errorTracker)
  }
}

export { ErrorLevel, ErrorType }
export default errorTracker
