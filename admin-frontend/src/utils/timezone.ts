/**
 * 时区工具函数
 * 统一处理北京时间显示和转换
 */

// 北京时区标识
export const BEIJING_TIMEZONE = 'Asia/Shanghai'

// 预定义的日期时间格式
export const DateTimeFormats = {
  FULL: {
    timeZone: BEIJING_TIMEZONE,
    year: 'numeric' as const,
    month: '2-digit' as const,
    day: '2-digit' as const,
    hour: '2-digit' as const,
    minute: '2-digit' as const,
    second: '2-digit' as const,
    hour12: false
  },
  DATE_ONLY: {
    timeZone: BEIJING_TIMEZONE,
    year: 'numeric' as const,
    month: '2-digit' as const,
    day: '2-digit' as const
  },
  TIME_ONLY: {
    timeZone: BEIJING_TIMEZONE,
    hour: '2-digit' as const,
    minute: '2-digit' as const,
    second: '2-digit' as const,
    hour12: false
  },
  COMPACT: {
    timeZone: BEIJING_TIMEZONE,
    year: 'numeric' as const,
    month: '2-digit' as const,
    day: '2-digit' as const,
    hour: '2-digit' as const,
    minute: '2-digit' as const,
    hour12: false
  }
}

/**
 * 格式化日期时间为北京时间
 * @param dateTime 日期时间字符串、Date对象或时间戳
 * @param options 格式化选项
 * @returns 格式化后的北京时间字符串
 */
export function formatDateTime(
  dateTime: string | Date | number | undefined | null,
  options: Intl.DateTimeFormatOptions = DateTimeFormats.FULL
): string {
  if (!dateTime) return '-'

  let date: Date

  try {
    if (typeof dateTime === 'string') {
      // 处理字符串时间
      date = new Date(dateTime)
    } else if (typeof dateTime === 'number') {
      // 处理时间戳，自动检测秒级或毫秒级
      if (dateTime < 1e12) {
        // 秒级时间戳
        date = new Date(dateTime * 1000)
      } else {
        // 毫秒级时间戳
        date = new Date(dateTime)
      }
    } else {
      // Date对象
      date = dateTime
    }

    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      return '-'
    }

    // 使用 Intl.DateTimeFormat 格式化为北京时间
    return new Intl.DateTimeFormat('zh-CN', options).format(date)
  } catch (error) {
    console.error('时间格式化错误:', error)
    return '-'
  }
}

/**
 * 格式化日期时间为默认格式（北京时间）
 * @param dateTime 日期时间
 * @returns 格式化后的时间字符串，格式：YYYY-MM-DD HH:mm:ss
 */
export function formatDateTimeDefault(dateTime: string | Date | number | undefined | null): string {
  return formatDateTime(dateTime, DateTimeFormats.FULL)
}

/**
 * 格式化日期（北京时间）
 * @param dateTime 日期时间
 * @returns 格式化后的日期字符串，格式：YYYY-MM-DD
 */
export function formatDateOnly(dateTime: string | Date | number | undefined | null): string {
  return formatDateTime(dateTime, DateTimeFormats.DATE_ONLY)
}

/**
 * 格式化时间（北京时间）
 * @param dateTime 日期时间
 * @returns 格式化后的时间字符串，格式：HH:mm:ss
 */
export function formatTimeOnly(dateTime: string | Date | number | undefined | null): string {
  return formatDateTime(dateTime, DateTimeFormats.TIME_ONLY)
}

/**
 * 格式化为紧凑格式（北京时间）
 * @param dateTime 日期时间
 * @returns 格式化后的时间字符串，格式：YYYY-MM-DD HH:mm
 */
export function formatDateTimeCompact(dateTime: string | Date | number | undefined | null): string {
  return formatDateTime(dateTime, DateTimeFormats.COMPACT)
}

/**
 * 获取当前北京时间
 * @returns 当前北京时间的Date对象
 */
export function getNowBeijing(): Date {
  return new Date()
}

/**
 * 获取当前北京时间戳（毫秒）
 * @returns 当前北京时间戳
 */
export function getNowBeijingTimestamp(): number {
  return Date.now()
}

/**
 * 获取当前北京时间的格式化字符串
 * @param options 格式化选项
 * @returns 格式化后的当前北京时间字符串
 */
export function getNowBeijingString(options: Intl.DateTimeFormatOptions = DateTimeFormats.FULL): string {
  return formatDateTime(new Date(), options)
}

/**
 * 检查时间是否有效
 * @param dateTime 日期时间
 * @returns 是否为有效时间
 */
export function isValidDateTime(dateTime: string | Date | number | undefined | null): boolean {
  if (!dateTime) return false

  try {
    const date = new Date(dateTime)
    return !isNaN(date.getTime())
  } catch {
    return false
  }
}

/**
 * 计算时间差（以分钟为单位）
 * @param startTime 开始时间
 * @param endTime 结束时间
 * @returns 时间差（分钟）
 */
export function getTimeDiffMinutes(
  startTime: string | Date | number,
  endTime: string | Date | number = new Date()
): number {
  const start = new Date(startTime)
  const end = new Date(endTime)
  
  if (isNaN(start.getTime()) || isNaN(end.getTime())) {
    return 0
  }
  
  return Math.floor((end.getTime() - start.getTime()) / (1000 * 60))
}

/**
 * 格式化相对时间（多久前）
 * @param dateTime 日期时间
 * @returns 相对时间字符串，如："5分钟前"、"2小时前"
 */
export function formatRelativeTime(dateTime: string | Date | number | undefined | null): string {
  if (!dateTime || !isValidDateTime(dateTime)) {
    return '-'
  }

  const now = new Date()
  const date = new Date(dateTime)
  const diffMs = now.getTime() - date.getTime()
  const diffMinutes = Math.floor(diffMs / (1000 * 60))
  
  if (diffMinutes < 1) {
    return '刚刚'
  } else if (diffMinutes < 60) {
    return `${diffMinutes}分钟前`
  } else if (diffMinutes < 24 * 60) {
    const hours = Math.floor(diffMinutes / 60)
    return `${hours}小时前`
  } else if (diffMinutes < 7 * 24 * 60) {
    const days = Math.floor(diffMinutes / (24 * 60))
    return `${days}天前`
  } else {
    // 超过7天显示具体日期
    return formatDateOnly(dateTime)
  }
}

/**
 * 时区信息
 */
export const TIMEZONE_INFO = {
  name: '北京时间',
  identifier: BEIJING_TIMEZONE,
  offset: 'UTC+8',
  description: '中国标准时间'
}