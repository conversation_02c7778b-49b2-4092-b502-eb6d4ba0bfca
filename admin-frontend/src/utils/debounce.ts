/**
 * 防抖和节流工具函数
 * 
 * 🎯 功能特性：
 * 1. 防抖函数 - 防止函数被频繁调用
 * 2. 节流函数 - 限制函数调用频率
 * 3. 智能防抖 - 根据上下文自动选择策略
 * 4. 取消机制 - 支持手动取消延迟执行
 * 
 * 🔧 解决问题：
 * - 搜索输入框频繁触发请求
 * - 按钮重复点击导致的重复操作
 * - 滚动事件过于频繁的性能问题
 * - 状态更新过于频繁导致的渲染问题
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-29
 */

import { ref, Ref } from 'vue'

// 防抖函数类型定义
type DebounceFunction<T extends (...args: any[]) => any> = {
  (...args: Parameters<T>): ReturnType<T>
  cancel: () => void
  flush: () => void
  pending: () => boolean
}

// 节流函数类型定义
type ThrottleFunction<T extends (...args: any[]) => any> = {
  (...args: Parameters<T>): ReturnType<T>
  cancel: () => void
  flush: () => void
}

/**
 * 防抖函数
 * 在事件被触发n秒后再执行回调，如果在这n秒内又被触发，则重新计时
 * 
 * @param func 要防抖的函数
 * @param wait 延迟时间（毫秒）
 * @param immediate 是否立即执行
 * @returns 防抖后的函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  immediate = false
): DebounceFunction<T> {
  let timeoutId: number | null = null
  let lastArgs: Parameters<T> | null = null
  let lastThis: any = null
  let result: ReturnType<T>

  const debounced = function (this: any, ...args: Parameters<T>) {
    lastArgs = args
    lastThis = this

    const callNow = immediate && !timeoutId

    if (timeoutId) {
      clearTimeout(timeoutId)
    }

    timeoutId = window.setTimeout(() => {
      timeoutId = null
      if (!immediate) {
        result = func.apply(lastThis, lastArgs!)
      }
    }, wait)

    if (callNow) {
      result = func.apply(this, args)
    }

    return result
  } as DebounceFunction<T>

  // 取消延迟执行
  debounced.cancel = () => {
    if (timeoutId) {
      clearTimeout(timeoutId)
      timeoutId = null
    }
  }

  // 立即执行
  debounced.flush = () => {
    if (timeoutId && lastArgs) {
      debounced.cancel()
      result = func.apply(lastThis, lastArgs)
    }
  }

  // 检查是否有待执行的调用
  debounced.pending = () => {
    return timeoutId !== null
  }

  return debounced
}

/**
 * 节流函数
 * 规定在一个单位时间内，只能触发一次函数
 * 
 * @param func 要节流的函数
 * @param wait 间隔时间（毫秒）
 * @param options 配置选项
 * @returns 节流后的函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  options: { leading?: boolean; trailing?: boolean } = {}
): ThrottleFunction<T> {
  let timeoutId: number | null = null
  let lastCallTime = 0
  let lastArgs: Parameters<T> | null = null
  let lastThis: any = null
  let result: ReturnType<T>

  const { leading = true, trailing = true } = options

  const throttled = function (this: any, ...args: Parameters<T>) {
    const now = Date.now()
    lastArgs = args
    lastThis = this

    if (!lastCallTime && !leading) {
      lastCallTime = now
    }

    const remaining = wait - (now - lastCallTime)

    if (remaining <= 0 || remaining > wait) {
      if (timeoutId) {
        clearTimeout(timeoutId)
        timeoutId = null
      }
      lastCallTime = now
      result = func.apply(this, args)
    } else if (!timeoutId && trailing) {
      timeoutId = window.setTimeout(() => {
        lastCallTime = leading ? Date.now() : 0
        timeoutId = null
        result = func.apply(lastThis, lastArgs!)
      }, remaining)
    }

    return result
  } as ThrottleFunction<T>

  // 取消延迟执行
  throttled.cancel = () => {
    if (timeoutId) {
      clearTimeout(timeoutId)
      timeoutId = null
    }
    lastCallTime = 0
  }

  // 立即执行
  throttled.flush = () => {
    if (timeoutId && lastArgs) {
      throttled.cancel()
      result = func.apply(lastThis, lastArgs)
    }
  }

  return throttled
}

/**
 * Vue 组合式函数：防抖状态管理
 * 
 * @param initialValue 初始值
 * @param delay 防抖延迟时间
 * @returns 防抖状态和更新函数
 */
export function useDebouncedRef<T>(initialValue: T, delay: number) {
  const value = ref(initialValue) as Ref<T>
  const debouncedValue = ref(initialValue) as Ref<T>

  const updateDebouncedValue = debounce((newValue: T) => {
    debouncedValue.value = newValue
  }, delay)

  const setValue = (newValue: T) => {
    value.value = newValue
    updateDebouncedValue(newValue)
  }

  return {
    value,
    debouncedValue,
    setValue,
    cancel: updateDebouncedValue.cancel,
    flush: updateDebouncedValue.flush
  }
}

/**
 * 智能防抖函数
 * 根据调用频率自动调整防抖延迟时间
 * 
 * @param func 要防抖的函数
 * @param baseWait 基础延迟时间
 * @param maxWait 最大延迟时间
 * @returns 智能防抖后的函数
 */
export function smartDebounce<T extends (...args: any[]) => any>(
  func: T,
  baseWait: number,
  maxWait: number = baseWait * 3
): DebounceFunction<T> {
  let callCount = 0
  let lastCallTime = 0
  let currentWait = baseWait

  const adjustWait = () => {
    const now = Date.now()
    const timeSinceLastCall = now - lastCallTime

    if (timeSinceLastCall < baseWait) {
      callCount++
      // 调用频率高，增加延迟时间
      currentWait = Math.min(baseWait * (1 + callCount * 0.5), maxWait)
    } else {
      // 调用频率低，重置延迟时间
      callCount = 0
      currentWait = baseWait
    }

    lastCallTime = now
  }

  const smartDebouncedFunc = function (this: any, ...args: Parameters<T>) {
    adjustWait()
    return debounce(func, currentWait).apply(this, args)
  } as DebounceFunction<T>

  // 添加控制方法
  smartDebouncedFunc.cancel = () => {}
  smartDebouncedFunc.flush = () => {}
  smartDebouncedFunc.pending = () => false

  return smartDebouncedFunc
}

/**
 * 操作防抖装饰器
 * 用于防止重复操作（如重复提交表单）
 * 
 * @param wait 防抖时间
 * @param message 提示消息
 * @returns 装饰器函数
 */
export function operationDebounce(wait: number = 1000, message: string = '操作过于频繁，请稍后再试') {
  const lastOperationTime = new Map<string, number>()

  return function <T extends (...args: any[]) => any>(
    target: any,
    propertyKey: string,
    descriptor: TypedPropertyDescriptor<T>
  ) {
    const originalMethod = descriptor.value!

    descriptor.value = function (this: any, ...args: any[]) {
      const key = `${target.constructor.name}.${propertyKey}`
      const now = Date.now()
      const lastTime = lastOperationTime.get(key) || 0

      if (now - lastTime < wait) {
        console.warn(`⚠️ ${message}`)
        return
      }

      lastOperationTime.set(key, now)
      return originalMethod.apply(this, args)
    } as any

    return descriptor
  }
}

/**
 * 创建防抖管理器
 * 用于管理多个防抖函数
 */
export class DebounceManager {
  private debouncedFunctions = new Map<string, DebounceFunction<any>>()

  /**
   * 注册防抖函数
   */
  register<T extends (...args: any[]) => any>(
    key: string,
    func: T,
    wait: number,
    immediate = false
  ): DebounceFunction<T> {
    const debouncedFunc = debounce(func, wait, immediate)
    this.debouncedFunctions.set(key, debouncedFunc)
    return debouncedFunc
  }

  /**
   * 执行防抖函数
   */
  execute(key: string, ...args: any[]): void {
    const debouncedFunc = this.debouncedFunctions.get(key)
    if (debouncedFunc) {
      debouncedFunc(...args)
    } else {
      console.warn(`⚠️ 防抖函数未找到: ${key}`)
    }
  }

  /**
   * 取消指定防抖函数
   */
  cancel(key: string): void {
    const debouncedFunc = this.debouncedFunctions.get(key)
    if (debouncedFunc) {
      debouncedFunc.cancel()
    }
  }

  /**
   * 取消所有防抖函数
   */
  cancelAll(): void {
    for (const debouncedFunc of this.debouncedFunctions.values()) {
      debouncedFunc.cancel()
    }
  }

  /**
   * 清理所有防抖函数
   */
  clear(): void {
    this.cancelAll()
    this.debouncedFunctions.clear()
  }
}

// 导出默认防抖管理器实例
export const defaultDebounceManager = new DebounceManager()
