/**
 * 高级HTTP客户端
 * 
 * 🎯 功能特性：
 * 1. 集成智能缓存管理
 * 2. 自动请求去重
 * 3. 错误重试和恢复
 * 4. 请求优先级管理
 * 
 * 🔧 解决问题：
 * - 统一HTTP请求处理逻辑
 * - 自动缓存和去重管理
 * - 提供更好的开发体验
 * - 减少样板代码
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-29
 */

import { AxiosRequestConfig, AxiosResponse } from 'axios'
import { cacheManager } from './cacheManager'
import { requestDeduplicator } from './requestDeduplicator'
import api from './http/index'

// HTTP客户端配置接口
interface HttpClientConfig extends AxiosRequestConfig {
  // 缓存配置
  cache?: {
    enabled?: boolean
    ttl?: number
    tags?: string[]
    version?: string
    forceRefresh?: boolean
  }
  
  // 去重配置
  deduplication?: {
    enabled?: boolean
    priority?: 'low' | 'normal' | 'high'
    tags?: string[]
  }
  
  // 重试配置
  retry?: {
    enabled?: boolean
    retries?: number
    delay?: number
  }
  
  // 其他配置
  silent?: boolean // 是否静默处理错误
  loading?: boolean // 是否显示加载状态
}

// 响应数据接口
interface ApiResponse<T = any> {
  success: boolean
  data: T
  message: string
  code: number
  timestamp: number
}

class HttpClient {
  private defaultConfig: HttpClientConfig = {
    cache: {
      enabled: true,
      ttl: 5 * 60 * 1000, // 5分钟
      tags: [],
      version: '1.0.0',
      forceRefresh: false
    },
    deduplication: {
      enabled: true,
      priority: 'normal',
      tags: []
    },
    retry: {
      enabled: true,
      retries: 3,
      delay: 1000
    },
    silent: false,
    loading: false,
    timeout: 15000
  }

  /**
   * GET请求
   */
  async get<T = any>(url: string, config?: HttpClientConfig): Promise<T> {
    return this.request<T>({ ...config, method: 'GET', url })
  }

  /**
   * POST请求
   */
  async post<T = any>(url: string, data?: any, config?: HttpClientConfig): Promise<T> {
    return this.request<T>({ ...config, method: 'POST', url, data })
  }

  /**
   * PUT请求
   */
  async put<T = any>(url: string, data?: any, config?: HttpClientConfig): Promise<T> {
    return this.request<T>({ ...config, method: 'PUT', url, data })
  }

  /**
   * DELETE请求
   */
  async delete<T = any>(url: string, config?: HttpClientConfig): Promise<T> {
    return this.request<T>({ ...config, method: 'DELETE', url })
  }

  /**
   * 通用请求方法
   */
  async request<T = any>(config: HttpClientConfig): Promise<T> {
    const finalConfig = this.mergeConfig(config)
    const { url, method = 'GET' } = finalConfig

    if (!url) {
      throw new Error('请求URL不能为空')
    }

    try {
      // 🔧 对于GET请求且启用缓存，使用缓存管理器
      if (method.toUpperCase() === 'GET' && finalConfig.cache?.enabled) {
        return await this.handleCachedRequest<T>(finalConfig)
      }

      // 🔧 对于其他请求，使用去重管理器
      if (finalConfig.deduplication?.enabled) {
        return await this.handleDeduplicatedRequest<T>(finalConfig)
      }

      // 🔧 直接请求
      return await this.executeRequest<T>(finalConfig)

    } catch (error: any) {
      if (!finalConfig.silent) {
        console.error(`❌ HTTP请求失败 [${method} ${url}]:`, error)
      }
      throw error
    }
  }

  /**
   * 处理缓存请求
   */
  private async handleCachedRequest<T>(config: HttpClientConfig): Promise<T> {
    const cacheKey = this.generateCacheKey(config)
    const cacheConfig = config.cache!

    return await cacheManager.get<T>({
      key: cacheKey,
      fetcher: () => this.executeRequest<T>(config),
      config: {
        ttl: cacheConfig.ttl,
        tags: cacheConfig.tags,
        version: cacheConfig.version
      },
      forceRefresh: cacheConfig.forceRefresh
    })
  }

  /**
   * 处理去重请求
   */
  private async handleDeduplicatedRequest<T>(config: HttpClientConfig): Promise<T> {
    const requestConfig = {
      url: config.url!,
      method: config.method,
      data: config.data,
      params: config.params,
      timeout: config.timeout,
      retries: config.retry?.retries,
      priority: config.deduplication?.priority,
      tags: config.deduplication?.tags
    }

    return await requestDeduplicator.request<T>(
      requestConfig,
      () => this.executeRequest<T>(config)
    )
  }

  /**
   * 执行实际请求
   */
  private async executeRequest<T>(config: HttpClientConfig): Promise<T> {
    const axiosConfig: AxiosRequestConfig = {
      url: config.url,
      method: config.method,
      data: config.data,
      params: config.params,
      headers: config.headers,
      timeout: config.timeout,
      ...config
    }

    // 移除自定义配置，避免传递给axios
    delete (axiosConfig as any).cache
    delete (axiosConfig as any).deduplication
    delete (axiosConfig as any).retry
    delete (axiosConfig as any).silent
    delete (axiosConfig as any).loading

    const response: AxiosResponse<ApiResponse<T>> = await api.request(axiosConfig)
    
    // 🔧 统一响应数据格式处理
    if (response.data && typeof response.data === 'object') {
      // 如果是标准API响应格式
      if ('success' in response.data && 'data' in response.data) {
        const apiResponse = response.data as ApiResponse<T>
        if (apiResponse.success) {
          return apiResponse.data
        } else {
          throw new Error(apiResponse.message || '请求失败')
        }
      }
    }

    // 直接返回响应数据
    return response.data as T
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(config: HttpClientConfig): string {
    const { url, method = 'GET', params } = config
    const keyParts = [method.toUpperCase(), url]
    
    if (params) {
      keyParts.push(JSON.stringify(params))
    }
    
    return keyParts.join('|')
  }

  /**
   * 合并配置
   */
  private mergeConfig(config: HttpClientConfig): HttpClientConfig {
    return {
      ...this.defaultConfig,
      ...config,
      cache: { ...this.defaultConfig.cache, ...config.cache },
      deduplication: { ...this.defaultConfig.deduplication, ...config.deduplication },
      retry: { ...this.defaultConfig.retry, ...config.retry }
    }
  }

  /**
   * 清理缓存
   */
  clearCache(tags?: string[]): void {
    if (tags) {
      cacheManager.clearByTags(tags)
    } else {
      cacheManager.clear()
    }
  }

  /**
   * 取消请求
   */
  cancelRequests(tags?: string[]): void {
    if (tags) {
      requestDeduplicator.cancelByTags(tags)
    } else {
      requestDeduplicator.cancelAll()
    }
  }

  /**
   * 获取统计信息
   */
  getStats() {
    return {
      cache: cacheManager.getStats(),
      requests: requestDeduplicator.getStats()
    }
  }

  /**
   * 预热缓存
   */
  async warmupCache(urls: Array<{ url: string; params?: any }>): Promise<void> {
    const configs = urls.map(({ url, params }) => ({
      key: this.generateCacheKey({ url, params }),
      fetcher: () => this.get(url, { params, cache: { enabled: true } }),
      config: { ttl: this.defaultConfig.cache!.ttl }
    }))

    await cacheManager.warmup(configs)
  }
}

// 创建全局HTTP客户端实例
export const httpClient = new HttpClient()

/**
 * Vue 组合式函数：在组件中使用HTTP客户端
 */
export function useHttpClient() {
  return {
    get: httpClient.get.bind(httpClient),
    post: httpClient.post.bind(httpClient),
    put: httpClient.put.bind(httpClient),
    delete: httpClient.delete.bind(httpClient),
    request: httpClient.request.bind(httpClient),
    clearCache: httpClient.clearCache.bind(httpClient),
    cancelRequests: httpClient.cancelRequests.bind(httpClient),
    getStats: httpClient.getStats.bind(httpClient),
    warmupCache: httpClient.warmupCache.bind(httpClient)
  }
}

// 便捷方法导出
export const { get, post, put, delete: del } = httpClient

export default httpClient
