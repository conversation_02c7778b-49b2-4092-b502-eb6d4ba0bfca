/**
 * 智能缓存管理器
 * 
 * 🎯 功能特性：
 * 1. 多级缓存策略（内存缓存 + 本地存储）
 * 2. 智能过期机制（TTL + 版本控制）
 * 3. 请求去重和并发控制
 * 4. 缓存预热和批量更新
 * 
 * 🔧 解决问题：
 * - 重复网络请求导致的性能问题
 * - 缓存策略不统一，过度禁用缓存
 * - 并发请求导致的资源浪费
 * - 缓存数据不一致和过期问题
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-29
 */

import { ref, reactive } from 'vue'

// 缓存项接口定义
interface CacheItem<T = any> {
  data: T
  timestamp: number
  ttl: number // 生存时间（毫秒）
  version: string
  accessCount: number
  lastAccess: number
  tags: string[] // 缓存标签，用于批量清理
}

// 缓存配置接口
interface CacheConfig {
  ttl?: number // 默认TTL（毫秒）
  maxSize?: number // 最大缓存项数
  storage?: 'memory' | 'localStorage' | 'sessionStorage' | 'hybrid'
  version?: string // 缓存版本
  tags?: string[] // 缓存标签
  serialize?: boolean // 是否序列化存储
}

// 请求配置接口
interface RequestConfig {
  key: string
  fetcher: () => Promise<any>
  config?: CacheConfig
  forceRefresh?: boolean
}

class CacheManager {
  private memoryCache = new Map<string, CacheItem>()
  private pendingRequests = new Map<string, Promise<any>>()
  private defaultConfig: Required<CacheConfig> = {
    ttl: 5 * 60 * 1000, // 5分钟
    maxSize: 100,
    storage: 'hybrid',
    version: '1.0.0',
    tags: [],
    serialize: true
  }
  
  // 统计信息
  private stats = reactive({
    hits: 0,
    misses: 0,
    requests: 0,
    errors: 0,
    totalSize: 0
  })

  constructor(config?: Partial<CacheConfig>) {
    if (config) {
      Object.assign(this.defaultConfig, config)
    }
    
    this.initCleanupTimer()
    this.loadFromStorage()
  }

  /**
   * 获取缓存数据或执行请求
   */
  async get<T>(config: RequestConfig): Promise<T> {
    const { key, fetcher, config: itemConfig, forceRefresh = false } = config
    const finalConfig = { ...this.defaultConfig, ...itemConfig }
    
    this.stats.requests++
    
    try {
      // 强制刷新时跳过缓存
      if (!forceRefresh) {
        const cached = this.getFromCache<T>(key, finalConfig)
        if (cached !== null) {
          this.stats.hits++
          console.log(`🎯 缓存命中: ${key}`)
          return cached
        }
      }
      
      this.stats.misses++
      
      // 检查是否有正在进行的请求（请求去重）
      if (this.pendingRequests.has(key)) {
        console.log(`⏳ 请求去重: ${key}`)
        return await this.pendingRequests.get(key)!
      }
      
      // 执行请求
      console.log(`🌐 发起请求: ${key}`)
      const requestPromise = this.executeRequest(key, fetcher, finalConfig)
      this.pendingRequests.set(key, requestPromise)
      
      try {
        const result = await requestPromise
        return result
      } finally {
        this.pendingRequests.delete(key)
      }
      
    } catch (error) {
      this.stats.errors++
      console.error(`❌ 缓存请求失败 [${key}]:`, error)
      throw error
    }
  }

  /**
   * 执行实际请求并缓存结果
   */
  private async executeRequest<T>(key: string, fetcher: () => Promise<T>, config: Required<CacheConfig>): Promise<T> {
    try {
      const data = await fetcher()
      
      // 缓存结果
      this.setCache(key, data, config)
      
      return data
    } catch (error) {
      // 请求失败时，尝试返回过期的缓存数据
      const staleData = this.getFromCache<T>(key, config, true)
      if (staleData !== null) {
        console.warn(`⚠️ 请求失败，返回过期缓存: ${key}`)
        return staleData
      }
      
      throw error
    }
  }

  /**
   * 从缓存获取数据
   */
  private getFromCache<T>(key: string, config: Required<CacheConfig>, allowStale = false): T | null {
    // 先从内存缓存获取
    let item = this.memoryCache.get(key)
    
    // 如果内存缓存没有，尝试从存储获取
    if (!item && (config.storage === 'localStorage' || config.storage === 'hybrid')) {
      item = this.getFromStorage(key) || undefined
      if (item) {
        // 恢复到内存缓存
        this.memoryCache.set(key, item)
      }
    }
    
    if (!item) return null
    
    const now = Date.now()
    const isExpired = now - item.timestamp > item.ttl
    const isVersionMismatch = item.version !== config.version
    
    // 检查是否过期或版本不匹配
    if ((isExpired || isVersionMismatch) && !allowStale) {
      this.delete(key)
      return null
    }
    
    // 更新访问统计
    item.accessCount++
    item.lastAccess = now
    
    return item.data
  }

  /**
   * 设置缓存
   */
  private setCache<T>(key: string, data: T, config: Required<CacheConfig>): void {
    const now = Date.now()
    const item: CacheItem<T> = {
      data,
      timestamp: now,
      ttl: config.ttl,
      version: config.version,
      accessCount: 1,
      lastAccess: now,
      tags: config.tags
    }
    
    // 检查内存缓存大小限制
    if (this.memoryCache.size >= config.maxSize) {
      this.evictLRU()
    }
    
    // 存储到内存缓存
    this.memoryCache.set(key, item)
    
    // 根据配置存储到持久化存储
    if (config.storage === 'localStorage' || config.storage === 'hybrid') {
      this.saveToStorage(key, item)
    }
    
    this.updateStats()
    console.log(`💾 缓存已设置: ${key} (TTL: ${config.ttl}ms)`)
  }

  /**
   * LRU淘汰策略
   */
  private evictLRU(): void {
    let oldestKey = ''
    let oldestTime = Date.now()
    
    for (const [key, item] of this.memoryCache.entries()) {
      if (item.lastAccess < oldestTime) {
        oldestTime = item.lastAccess
        oldestKey = key
      }
    }
    
    if (oldestKey) {
      this.delete(oldestKey)
      console.log(`🗑️ LRU淘汰: ${oldestKey}`)
    }
  }

  /**
   * 从本地存储获取
   */
  private getFromStorage(key: string): CacheItem | null {
    try {
      const stored = localStorage.getItem(`cache_${key}`)
      if (stored) {
        return JSON.parse(stored)
      }
    } catch (error) {
      console.warn(`⚠️ 从存储读取缓存失败 [${key}]:`, error)
    }
    return null
  }

  /**
   * 保存到本地存储
   */
  private saveToStorage(key: string, item: CacheItem): void {
    try {
      localStorage.setItem(`cache_${key}`, JSON.stringify(item))
    } catch (error) {
      console.warn(`⚠️ 保存缓存到存储失败 [${key}]:`, error)
    }
  }

  /**
   * 从存储加载缓存
   */
  private loadFromStorage(): void {
    try {
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i)
        if (key?.startsWith('cache_')) {
          const cacheKey = key.replace('cache_', '')
          const item = this.getFromStorage(cacheKey)
          if (item) {
            // 检查是否过期
            const now = Date.now()
            if (now - item.timestamp <= item.ttl) {
              this.memoryCache.set(cacheKey, item)
            } else {
              // 清理过期的存储缓存
              localStorage.removeItem(key)
            }
          }
        }
      }
      console.log(`📥 从存储加载了 ${this.memoryCache.size} 个缓存项`)
    } catch (error) {
      console.warn('⚠️ 从存储加载缓存失败:', error)
    }
  }

  /**
   * 删除缓存项
   */
  delete(key: string): boolean {
    const deleted = this.memoryCache.delete(key)
    
    // 同时从存储删除
    try {
      localStorage.removeItem(`cache_${key}`)
    } catch (error) {
      console.warn(`⚠️ 从存储删除缓存失败 [${key}]:`, error)
    }
    
    if (deleted) {
      this.updateStats()
      console.log(`🗑️ 缓存已删除: ${key}`)
    }
    
    return deleted
  }

  /**
   * 根据标签批量清理缓存
   */
  clearByTags(tags: string[]): number {
    let cleared = 0
    
    for (const [key, item] of this.memoryCache.entries()) {
      if (item.tags.some(tag => tags.includes(tag))) {
        this.delete(key)
        cleared++
      }
    }
    
    console.log(`🧹 根据标签清理了 ${cleared} 个缓存项`)
    return cleared
  }

  /**
   * 清理过期缓存
   */
  clearExpired(): number {
    const now = Date.now()
    let cleared = 0
    
    for (const [key, item] of this.memoryCache.entries()) {
      if (now - item.timestamp > item.ttl) {
        this.delete(key)
        cleared++
      }
    }
    
    console.log(`🧹 清理了 ${cleared} 个过期缓存项`)
    return cleared
  }

  /**
   * 清理所有缓存
   */
  clear(): void {
    const count = this.memoryCache.size
    this.memoryCache.clear()
    
    // 清理存储中的缓存
    try {
      const keysToRemove: string[] = []
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i)
        if (key?.startsWith('cache_')) {
          keysToRemove.push(key)
        }
      }
      keysToRemove.forEach(key => localStorage.removeItem(key))
    } catch (error) {
      console.warn('⚠️ 清理存储缓存失败:', error)
    }
    
    this.updateStats()
    console.log(`🧹 已清理所有缓存，共 ${count} 个`)
  }

  /**
   * 初始化清理定时器
   */
  private initCleanupTimer(): void {
    // 每5分钟清理一次过期缓存
    setInterval(() => {
      this.clearExpired()
    }, 5 * 60 * 1000)
  }

  /**
   * 更新统计信息
   */
  private updateStats(): void {
    this.stats.totalSize = this.memoryCache.size
  }

  /**
   * 获取缓存统计信息
   */
  getStats() {
    return {
      ...this.stats,
      hitRate: this.stats.requests > 0 ? (this.stats.hits / this.stats.requests * 100).toFixed(2) + '%' : '0%',
      cacheKeys: Array.from(this.memoryCache.keys())
    }
  }

  /**
   * 预热缓存
   */
  async warmup(configs: RequestConfig[]): Promise<void> {
    console.log(`🔥 开始缓存预热，共 ${configs.length} 个项目`)
    
    const promises = configs.map(config => 
      this.get(config).catch(error => {
        console.warn(`⚠️ 预热失败 [${config.key}]:`, error)
        return null
      })
    )
    
    await Promise.allSettled(promises)
    console.log('🔥 缓存预热完成')
  }
}

// 创建全局缓存管理器实例
export const cacheManager = new CacheManager({
  ttl: 5 * 60 * 1000, // 5分钟默认TTL
  maxSize: 200,
  storage: 'hybrid',
  version: '1.0.0'
})

/**
 * Vue 组合式函数：在组件中使用缓存管理器
 */
export function useCache() {
  return {
    get: cacheManager.get.bind(cacheManager),
    delete: cacheManager.delete.bind(cacheManager),
    clear: cacheManager.clear.bind(cacheManager),
    clearByTags: cacheManager.clearByTags.bind(cacheManager),
    getStats: cacheManager.getStats.bind(cacheManager)
  }
}

export default cacheManager
