/**
 * 增强状态管理器
 * 
 * 🎯 功能特性：
 * 1. 状态变更监控和调试
 * 2. 状态持久化优化
 * 3. 状态回滚和时间旅行
 * 4. 性能监控和分析
 * 
 * 🔧 解决问题：
 * - 状态管理性能问题
 * - 状态变更追踪困难
 * - 调试和开发体验不佳
 * - 状态持久化效率低下
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-29
 */

import { reactive, watch, computed, ref } from 'vue'
import { debounce } from '@/utils/debounce'

// 状态变更记录接口
interface StateChange {
  id: string
  storeName: string
  action: string
  payload: any
  previousState: any
  newState: any
  timestamp: number
  duration: number
}

// 状态快照接口
interface StateSnapshot {
  id: string
  timestamp: number
  states: Record<string, any>
  description: string
}

// 性能指标接口
interface PerformanceMetrics {
  totalChanges: number
  averageChangeTime: number
  slowestChange: StateChange | null
  memoryUsage: number
  storeCount: number
}

class StateManager {
  private stores = new Map<string, any>()
  private changeHistory: StateChange[] = []
  private snapshots: StateSnapshot[] = []
  private isRecording = ref(true)
  private maxHistorySize = 1000
  private maxSnapshotSize = 50
  
  // 性能监控
  private metrics = reactive<PerformanceMetrics>({
    totalChanges: 0,
    averageChangeTime: 0,
    slowestChange: null,
    memoryUsage: 0,
    storeCount: 0
  })

  // 状态持久化配置
  private persistConfig = {
    enabled: true,
    key: 'app-state',
    storage: localStorage,
    debounceTime: 1000,
    excludeStores: ['temp', 'cache']
  }

  constructor() {
    this.initPerformanceMonitoring()
    this.loadPersistedState()
  }

  /**
   * 注册store
   */
  registerStore(name: string, store: any): void {
    this.stores.set(name, store)
    this.metrics.storeCount = this.stores.size
    
    // 监听store变化
    this.watchStore(name, store)
    
    console.log(`📦 Store已注册: ${name}`)
  }

  /**
   * 监听store变化
   */
  private watchStore(name: string, store: any): void {
    // 深度监听store状态变化
    watch(
      () => store.$state,
      (newState, oldState) => {
        if (this.isRecording.value) {
          this.recordStateChange(name, 'state_change', null, oldState, newState)
        }
      },
      { deep: true }
    )

    // 监听store actions
    if (store.$onAction) {
      store.$onAction(({ name: actionName, args, after, onError }: any) => {
        const startTime = performance.now()
        const previousState = JSON.parse(JSON.stringify(store.$state))

        after((result: any) => {
          const endTime = performance.now()
          const duration = endTime - startTime
          
          if (this.isRecording.value) {
            this.recordStateChange(
              name,
              actionName,
              args,
              previousState,
              store.$state,
              duration
            )
          }
        })

        onError((error: Error) => {
          console.error(`❌ Store action错误 [${name}.${actionName}]:`, error)
        })
      })
    }
  }

  /**
   * 记录状态变更
   */
  private recordStateChange(
    storeName: string,
    action: string,
    payload: any,
    previousState: any,
    newState: any,
    duration = 0
  ): void {
    const change: StateChange = {
      id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      storeName,
      action,
      payload,
      previousState: JSON.parse(JSON.stringify(previousState)),
      newState: JSON.parse(JSON.stringify(newState)),
      timestamp: Date.now(),
      duration
    }

    this.changeHistory.unshift(change)
    
    // 限制历史记录大小
    if (this.changeHistory.length > this.maxHistorySize) {
      this.changeHistory = this.changeHistory.slice(0, this.maxHistorySize)
    }

    // 更新性能指标
    this.updateMetrics(change)
    
    // 触发持久化
    this.debouncedPersist()
  }

  /**
   * 更新性能指标
   */
  private updateMetrics(change: StateChange): void {
    this.metrics.totalChanges++
    
    // 计算平均变更时间
    const totalDuration = this.changeHistory.reduce((sum, c) => sum + c.duration, 0)
    this.metrics.averageChangeTime = totalDuration / this.changeHistory.length

    // 记录最慢的变更
    if (!this.metrics.slowestChange || change.duration > this.metrics.slowestChange.duration) {
      this.metrics.slowestChange = change
    }

    // 更新内存使用情况
    if (performance.memory) {
      this.metrics.memoryUsage = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024)
    }
  }

  /**
   * 创建状态快照
   */
  createSnapshot(description = '手动快照'): string {
    const snapshot: StateSnapshot = {
      id: `snapshot_${Date.now()}`,
      timestamp: Date.now(),
      states: {},
      description
    }

    // 收集所有store状态
    for (const [name, store] of this.stores.entries()) {
      snapshot.states[name] = JSON.parse(JSON.stringify(store.$state))
    }

    this.snapshots.unshift(snapshot)
    
    // 限制快照数量
    if (this.snapshots.length > this.maxSnapshotSize) {
      this.snapshots = this.snapshots.slice(0, this.maxSnapshotSize)
    }

    console.log(`📸 状态快照已创建: ${snapshot.id}`)
    return snapshot.id
  }

  /**
   * 恢复到指定快照
   */
  restoreSnapshot(snapshotId: string): boolean {
    const snapshot = this.snapshots.find(s => s.id === snapshotId)
    if (!snapshot) {
      console.error(`❌ 快照不存在: ${snapshotId}`)
      return false
    }

    try {
      // 暂停记录，避免恢复过程被记录
      this.isRecording.value = false

      // 恢复各个store状态
      for (const [name, state] of Object.entries(snapshot.states)) {
        const store = this.stores.get(name)
        if (store) {
          Object.assign(store.$state, state)
        }
      }

      console.log(`🔄 已恢复到快照: ${snapshotId}`)
      return true
    } catch (error) {
      console.error(`❌ 快照恢复失败:`, error)
      return false
    } finally {
      // 恢复记录
      setTimeout(() => {
        this.isRecording.value = true
      }, 100)
    }
  }

  /**
   * 时间旅行 - 回滚到指定变更
   */
  timeTravel(changeId: string): boolean {
    const changeIndex = this.changeHistory.findIndex(c => c.id === changeId)
    if (changeIndex === -1) {
      console.error(`❌ 变更记录不存在: ${changeId}`)
      return false
    }

    try {
      this.isRecording.value = false

      // 从最新状态开始，逐步回滚到目标状态
      for (let i = 0; i <= changeIndex; i++) {
        const change = this.changeHistory[i]
        const store = this.stores.get(change.storeName)
        if (store) {
          Object.assign(store.$state, change.previousState)
        }
      }

      console.log(`⏰ 时间旅行完成，回滚到: ${changeId}`)
      return true
    } catch (error) {
      console.error(`❌ 时间旅行失败:`, error)
      return false
    } finally {
      setTimeout(() => {
        this.isRecording.value = true
      }, 100)
    }
  }

  /**
   * 防抖持久化
   */
  private debouncedPersist = debounce(() => {
    this.persistState()
  }, this.persistConfig.debounceTime)

  /**
   * 持久化状态
   */
  private persistState(): void {
    if (!this.persistConfig.enabled) return

    try {
      const stateToSave: Record<string, any> = {}
      
      for (const [name, store] of this.stores.entries()) {
        if (!this.persistConfig.excludeStores.includes(name)) {
          stateToSave[name] = store.$state
        }
      }

      const serialized = JSON.stringify({
        states: stateToSave,
        timestamp: Date.now(),
        version: '1.0.0'
      })

      this.persistConfig.storage.setItem(this.persistConfig.key, serialized)
      console.log('💾 状态已持久化')
    } catch (error) {
      console.error('❌ 状态持久化失败:', error)
    }
  }

  /**
   * 加载持久化状态
   */
  private loadPersistedState(): void {
    if (!this.persistConfig.enabled) return

    try {
      const saved = this.persistConfig.storage.getItem(this.persistConfig.key)
      if (!saved) return

      const { states, timestamp, version } = JSON.parse(saved)
      
      // 检查版本兼容性
      if (version !== '1.0.0') {
        console.warn('⚠️ 状态版本不兼容，跳过加载')
        return
      }

      // 检查时间有效性（7天内）
      if (Date.now() - timestamp > 7 * 24 * 60 * 60 * 1000) {
        console.warn('⚠️ 持久化状态过期，跳过加载')
        return
      }

      // 延迟加载，等待stores注册完成
      setTimeout(() => {
        this.isRecording.value = false
        
        for (const [name, state] of Object.entries(states)) {
          const store = this.stores.get(name)
          if (store) {
            Object.assign(store.$state, state)
          }
        }
        
        this.isRecording.value = true
        console.log('📥 持久化状态已加载')
      }, 1000)
    } catch (error) {
      console.error('❌ 加载持久化状态失败:', error)
    }
  }

  /**
   * 初始化性能监控
   */
  private initPerformanceMonitoring(): void {
    // 定期更新内存使用情况
    setInterval(() => {
      if (performance.memory) {
        this.metrics.memoryUsage = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024)
      }
    }, 5000)
  }

  /**
   * 获取变更历史
   */
  getChangeHistory(limit = 100): StateChange[] {
    return this.changeHistory.slice(0, limit)
  }

  /**
   * 获取快照列表
   */
  getSnapshots(): StateSnapshot[] {
    return [...this.snapshots]
  }

  /**
   * 获取性能指标
   */
  getMetrics(): PerformanceMetrics {
    return { ...this.metrics }
  }

  /**
   * 清理历史记录
   */
  clearHistory(): void {
    this.changeHistory = []
    this.snapshots = []
    this.metrics.totalChanges = 0
    this.metrics.averageChangeTime = 0
    this.metrics.slowestChange = null
    console.log('🧹 状态历史已清理')
  }

  /**
   * 导出状态数据
   */
  exportState(): string {
    const exportData = {
      stores: Object.fromEntries(
        Array.from(this.stores.entries()).map(([name, store]) => [name, store.$state])
      ),
      history: this.changeHistory.slice(0, 50), // 只导出最近50条
      snapshots: this.snapshots,
      metrics: this.metrics,
      timestamp: Date.now()
    }

    return JSON.stringify(exportData, null, 2)
  }

  /**
   * 开启/关闭记录
   */
  toggleRecording(): boolean {
    this.isRecording.value = !this.isRecording.value
    console.log(`📹 状态记录${this.isRecording.value ? '已开启' : '已关闭'}`)
    return this.isRecording.value
  }
}

// 创建全局状态管理器实例
export const stateManager = new StateManager()

/**
 * Vue 组合式函数：在组件中使用状态管理器
 */
export function useStateManager() {
  return {
    createSnapshot: stateManager.createSnapshot.bind(stateManager),
    restoreSnapshot: stateManager.restoreSnapshot.bind(stateManager),
    timeTravel: stateManager.timeTravel.bind(stateManager),
    getChangeHistory: stateManager.getChangeHistory.bind(stateManager),
    getSnapshots: stateManager.getSnapshots.bind(stateManager),
    getMetrics: stateManager.getMetrics.bind(stateManager),
    clearHistory: stateManager.clearHistory.bind(stateManager),
    exportState: stateManager.exportState.bind(stateManager),
    toggleRecording: stateManager.toggleRecording.bind(stateManager)
  }
}

export default stateManager
