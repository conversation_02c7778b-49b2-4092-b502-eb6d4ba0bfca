import{_ as e}from"./index-rNRt1EuS.js";/* empty css                */import{d as a,a1 as l,r as s,f as t,c as n,o as r,e as u,b as o,N as c,a as p,x as i,w as d,H as v,K as m,aV as y}from"./vendor-CAPBtMef.js";const f={class:"simple-order-page"},h=e(a({__name:"simple",setup(e){l();const a=s(""),h=s(""),_=()=>{a.value=(new Date).toLocaleString()},g=()=>{return e=this,a=null,l=function*(){try{const e=yield fetch("/api/v1/admin/orders?page=1&page_size=5"),a=yield e.json();h.value=JSON.stringify(a,null,2)}catch(e){h.value=`API测试失败: ${e.message}`}},new Promise(((s,t)=>{var n=e=>{try{u(l.next(e))}catch(a){t(a)}},r=e=>{try{u(l.throw(e))}catch(a){t(a)}},u=e=>e.done?s(e.value):Promise.resolve(e.value).then(n,r);u((l=l.apply(e,a)).next())}));var e,a,l},x=()=>{};return t((()=>{_(),setInterval(_,1e3)})),(e,l)=>{const s=v,t=y;return r(),n("div",f,[l[5]||(l[5]=u("h1",null,"订单管理 - 简单版本",-1)),u("p",null,"当前路由: "+i(e.$route.path),1),u("p",null,"当前时间: "+i(a.value),1),o(t,null,{default:d((()=>[l[2]||(l[2]=u("h3",null,"路由测试成功！",-1)),l[3]||(l[3]=u("p",null,"如果您能看到这个页面，说明订单管理路由已经正常工作。",-1)),o(s,{type:"primary",onClick:g},{default:d((()=>l[0]||(l[0]=[m("测试API连接")]))),_:1}),o(s,{type:"success",onClick:x},{default:d((()=>l[1]||(l[1]=[m("加载完整版本")]))),_:1})])),_:1}),h.value?(r(),c(t,{key:0,style:{"margin-top":"20px"}},{default:d((()=>[l[4]||(l[4]=u("h4",null,"API测试结果:",-1)),u("pre",null,i(h.value),1)])),_:1})):p("",!0)])}}}),[["__scopeId","data-v-ccbbc75a"]]);export{h as default};
