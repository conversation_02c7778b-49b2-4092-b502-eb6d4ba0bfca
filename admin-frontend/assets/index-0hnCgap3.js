var e=(e,a,l)=>new Promise(((t,r)=>{var d=e=>{try{n(l.next(e))}catch(a){r(a)}},s=e=>{try{n(l.throw(e))}catch(a){r(a)}},n=e=>e.done?t(e.value):Promise.resolve(e.value).then(d,s);n((l=l.apply(e,a)).next())}));import{_ as a}from"./index-rNRt1EuS.js";/* empty css               *//* empty css                             *//* empty css                *//* empty css                       */import{d as l,r as t,X as r,f as d,c as s,o as n,e as u,b as o,w as i,K as _,u as p,aQ as c,H as v,aS as m,Z as y,_ as f,C as b,D as g,ar as h,as as w,b0 as x,aV as k,a2 as V,x as z,N as C,aI as U,aJ as j,aX as D,a as S,aG as Y,aH as I,b1 as R,b2 as H,aT as $,aN as L,Y as M,M as O,ai as T}from"./vendor-CAPBtMef.js";import{A as B}from"./adminCallbackApi-C-jDof5v.js";const K={class:"callback-records-management"},N={class:"page-header"},P={class:"header-right"},A={class:"table-header"},J={class:"table-actions"},X=["title"],q={class:"pagination-wrapper"},E={key:0,class:"record-detail"},G={class:"detail-section",style:{"margin-top":"20px"}},Q={class:"error-info"},Z={class:"error-info"},F={class:"detail-section",style:{"margin-top":"20px"}},W={class:"detail-section",style:{"margin-top":"20px"}},ee=a(l({__name:"index",setup(a){const l=t(!1),ee=t([]),ae=t(null),le=t([]),te=t(null),re=r({user_id:"",provider:"",event_type:"",order_no:"",tracking_no:"",internal_status:"",external_status:"",sort_by:"received_at",sort_order:"desc"}),de=r({page:1,page_size:20,total:0}),se=t(!1),ne=e=>e?new Date(e).toLocaleString("zh-CN"):"-",ue=e=>{if(!e)return"";try{return JSON.stringify(e,null,2)}catch(a){return String(e)}},oe=e=>({yuntong:"primary",yida:"success",kuaidi100:"warning"}[e]||"info"),ie=e=>({yuntong:"云通",yida:"易达",kuaidi100:"快递100"}[e]||e),_e=e=>({order_status_changed:"订单状态变更",billing_updated:"计费更新",ticket_replied:"工单回复"}[e]||e),pe=e=>({success:"success",failed:"danger",pending:"warning"}[e]||"info"),ce=e=>({success:"成功",failed:"失败",pending:"处理中"}[e]||e),ve=()=>e(this,null,(function*(){try{l.value=!0;const e={page:de.page,page_size:de.page_size,user_id:re.user_id,provider:re.provider,event_type:re.event_type,order_no:re.order_no,tracking_no:re.tracking_no,internal_status:re.internal_status,external_status:re.external_status,order_by:re.sort_by,order:re.sort_order};te.value&&2===te.value.length&&(e.start_time=te.value[0],e.end_time=te.value[1]);const a=yield B.getCallbackRecords(e);ee.value=a.records,de.total=a.total,de.page=a.page,de.page_size=a.page_size}catch(e){O.error(e.message||"加载回调记录失败")}finally{l.value=!1}})),me=()=>{de.page=1,ve()},ye=()=>{Object.assign(re,{user_id:"",provider:"",event_type:"",order_no:"",tracking_no:"",internal_status:"",external_status:"",sort_by:"received_at",sort_order:"desc"}),te.value=null,me()},fe=e=>{de.page_size=e,de.page=1,ve()},be=e=>{de.page=e,ve()},ge=a=>e(this,null,(function*(){try{yield T.confirm(`确定要重试回调记录 "${a.id}" 吗？`,"确认重试",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),yield B.retryCallback(a.id),O.success("回调重试已启动"),yield ve(),se.value&&(se.value=!1)}catch(e){"cancel"!==e&&O.error(e.message||"重试回调失败")}})),he=e=>{le.value=e},we=()=>e(this,null,(function*(){if(0!==le.value.length)try{yield T.confirm(`确定要批量重试选中的 ${le.value.length} 条回调记录吗？`,"确认批量重试",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=le.value.map((e=>e.id));yield B.batchRetryCallbacks(e),O.success(`成功启动 ${e.length} 条回调记录的重试`),yield ve(),le.value=[]}catch(e){"cancel"!==e&&O.error(e.message||"批量重试回调失败")}else O.warning("请选择要重试的回调记录")})),xe=()=>{ve()},ke=()=>e(this,null,(function*(){try{const e={user_id:re.user_id,provider:re.provider,event_type:re.event_type,order_no:re.order_no,tracking_no:re.tracking_no,internal_status:re.internal_status,external_status:re.external_status};te.value&&2===te.value.length&&(e.start_time=te.value[0],e.end_time=te.value[1]);const a=yield B.exportCallbackRecords(e),l=window.URL.createObjectURL(a),t=document.createElement("a");t.href=l,t.download=`callback_records_${(new Date).toISOString().slice(0,10)}.xlsx`,document.body.appendChild(t),t.click(),document.body.removeChild(t),window.URL.revokeObjectURL(l),O.success("回调记录导出成功")}catch(e){O.error(e.message||"导出回调记录失败")}}));return d((()=>{ve()})),(e,a)=>{const t=v,r=b,d=f,O=w,T=h,B=x,ve=y,Ve=k,ze=j,Ce=D,Ue=U,je=I,De=H,Se=R,Ye=L,Ie=$,Re=M,He=Y;return n(),s("div",K,[u("div",N,[a[19]||(a[19]=u("div",{class:"header-left"},[u("h1",{class:"page-title"},"回调记录管理"),u("p",{class:"page-description"},"管理和监控系统中的所有回调记录")],-1)),u("div",P,[o(t,{type:"success",icon:p(c),onClick:xe},{default:i((()=>a[17]||(a[17]=[_(" 刷新数据 ")]))),_:1},8,["icon"]),o(t,{type:"primary",icon:p(m),onClick:ke},{default:i((()=>a[18]||(a[18]=[_(" 导出记录 ")]))),_:1},8,["icon"])])]),o(Ve,{class:"search-card"},{default:i((()=>[o(ve,{model:re,inline:""},{default:i((()=>[o(d,{label:"用户ID/用户名"},{default:i((()=>[o(r,{modelValue:re.user_id,"onUpdate:modelValue":a[0]||(a[0]=e=>re.user_id=e),placeholder:"请输入用户ID或用户名",clearable:"",style:{width:"200px"},onKeyup:g(me,["enter"])},null,8,["modelValue"])])),_:1}),o(d,{label:"供应商"},{default:i((()=>[o(T,{modelValue:re.provider,"onUpdate:modelValue":a[1]||(a[1]=e=>re.provider=e),placeholder:"请选择供应商",clearable:"",style:{width:"150px"}},{default:i((()=>[o(O,{label:"全部",value:""}),o(O,{label:"云通",value:"yuntong"}),o(O,{label:"易达",value:"yida"}),o(O,{label:"快递100",value:"kuaidi100"})])),_:1},8,["modelValue"])])),_:1}),o(d,{label:"事件类型"},{default:i((()=>[o(T,{modelValue:re.event_type,"onUpdate:modelValue":a[2]||(a[2]=e=>re.event_type=e),placeholder:"请选择事件类型",clearable:"",style:{width:"150px"}},{default:i((()=>[o(O,{label:"全部",value:""}),o(O,{label:"订单状态变更",value:"order_status_changed"}),o(O,{label:"计费更新",value:"billing_updated"}),o(O,{label:"工单回复",value:"ticket_replied"})])),_:1},8,["modelValue"])])),_:1}),o(d,{label:"内部状态"},{default:i((()=>[o(T,{modelValue:re.internal_status,"onUpdate:modelValue":a[3]||(a[3]=e=>re.internal_status=e),placeholder:"请选择内部状态",clearable:"",style:{width:"120px"}},{default:i((()=>[o(O,{label:"全部",value:""}),o(O,{label:"成功",value:"success"}),o(O,{label:"失败",value:"failed"}),o(O,{label:"处理中",value:"pending"})])),_:1},8,["modelValue"])])),_:1}),o(d,{label:"外部状态"},{default:i((()=>[o(T,{modelValue:re.external_status,"onUpdate:modelValue":a[4]||(a[4]=e=>re.external_status=e),placeholder:"请选择外部状态",clearable:"",style:{width:"120px"}},{default:i((()=>[o(O,{label:"全部",value:""}),o(O,{label:"成功",value:"success"}),o(O,{label:"失败",value:"failed"}),o(O,{label:"处理中",value:"pending"})])),_:1},8,["modelValue"])])),_:1}),o(d,{label:"订单号"},{default:i((()=>[o(r,{modelValue:re.order_no,"onUpdate:modelValue":a[5]||(a[5]=e=>re.order_no=e),placeholder:"请输入订单号",clearable:"",style:{width:"200px"},onKeyup:g(me,["enter"])},null,8,["modelValue"])])),_:1}),o(d,{label:"快递单号"},{default:i((()=>[o(r,{modelValue:re.tracking_no,"onUpdate:modelValue":a[6]||(a[6]=e=>re.tracking_no=e),placeholder:"请输入快递单号",clearable:"",style:{width:"200px"},onKeyup:g(me,["enter"])},null,8,["modelValue"])])),_:1}),o(d,{label:"时间范围"},{default:i((()=>[o(B,{modelValue:te.value,"onUpdate:modelValue":a[7]||(a[7]=e=>te.value=e),type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",style:{width:"350px"}},null,8,["modelValue"])])),_:1}),o(d,{label:"排序方式"},{default:i((()=>[o(T,{modelValue:re.sort_by,"onUpdate:modelValue":a[8]||(a[8]=e=>re.sort_by=e),style:{width:"120px"}},{default:i((()=>[o(O,{label:"接收时间",value:"received_at"}),o(O,{label:"创建时间",value:"created_at"}),o(O,{label:"更新时间",value:"updated_at"})])),_:1},8,["modelValue"])])),_:1}),o(d,{label:"排序"},{default:i((()=>[o(T,{modelValue:re.sort_order,"onUpdate:modelValue":a[9]||(a[9]=e=>re.sort_order=e),style:{width:"100px"}},{default:i((()=>[o(O,{label:"降序",value:"desc"}),o(O,{label:"升序",value:"asc"})])),_:1},8,["modelValue"])])),_:1}),o(d,null,{default:i((()=>[o(t,{type:"primary",onClick:me},{default:i((()=>a[20]||(a[20]=[_(" 搜索 ")]))),_:1}),o(t,{onClick:ye},{default:i((()=>a[21]||(a[21]=[_(" 重置 ")]))),_:1})])),_:1})])),_:1},8,["model"])])),_:1}),o(Ve,{class:"table-card"},{default:i((()=>[u("div",A,[a[22]||(a[22]=u("div",{class:"table-title"},"回调记录列表",-1)),u("div",J,[o(t,{type:"warning",size:"small",disabled:0===le.value.length,onClick:we},{default:i((()=>[_(" 批量重试 ("+z(le.value.length)+") ",1)])),_:1},8,["disabled"])])]),V((n(),C(Ue,{data:ee.value,stripe:"",border:"",style:{width:"100%"},onSelectionChange:he},{default:i((()=>[o(ze,{type:"selection",width:"55"}),o(ze,{prop:"provider",label:"供应商",width:"100"},{default:i((({row:e})=>[o(Ce,{type:oe(e.provider),size:"small"},{default:i((()=>[_(z(ie(e.provider)),1)])),_:2},1032,["type"])])),_:1}),o(ze,{prop:"event_type",label:"事件类型",width:"120"},{default:i((({row:e})=>[o(Ce,{type:"info",size:"small"},{default:i((()=>[_(z(_e(e.event_type)),1)])),_:2},1024)])),_:1}),o(ze,{prop:"order_no",label:"订单号","min-width":"150","show-overflow-tooltip":""}),o(ze,{prop:"tracking_no",label:"快递单号","min-width":"150","show-overflow-tooltip":""}),o(ze,{label:"用户",width:"120","show-overflow-tooltip":""},{default:i((({row:e})=>[u("span",{title:`用户名: ${e.username||"未知"}\n用户ID: ${e.user_id}`},z(e.username||e.user_id),9,X)])),_:1}),o(ze,{prop:"internal_status",label:"内部状态",width:"100"},{default:i((({row:e})=>[o(Ce,{type:pe(e.internal_status),size:"small"},{default:i((()=>[_(z(ce(e.internal_status)),1)])),_:2},1032,["type"])])),_:1}),o(ze,{prop:"external_status",label:"外部状态",width:"100"},{default:i((({row:e})=>[o(Ce,{type:pe(e.external_status),size:"small"},{default:i((()=>[_(z(ce(e.external_status)),1)])),_:2},1032,["type"])])),_:1}),o(ze,{prop:"retry_count",label:"重试次数",width:"100"},{default:i((({row:e})=>[o(Ce,{type:e.retry_count>0?"warning":"info",size:"small"},{default:i((()=>[_(z(e.retry_count),1)])),_:2},1032,["type"])])),_:1}),o(ze,{prop:"received_at",label:"接收时间",width:"180"},{default:i((({row:e})=>[_(z(ne(e.received_at)),1)])),_:1}),o(ze,{label:"操作",width:"200",fixed:"right"},{default:i((({row:e})=>[o(t,{type:"primary",size:"small",onClick:a=>{return l=e,ae.value=l,void(se.value=!0);var l}},{default:i((()=>a[23]||(a[23]=[_(" 查看详情 ")]))),_:2},1032,["onClick"]),"failed"===e.internal_status||"failed"===e.external_status?(n(),C(t,{key:0,type:"warning",size:"small",onClick:a=>ge(e)},{default:i((()=>a[24]||(a[24]=[_(" 重试 ")]))),_:2},1032,["onClick"])):S("",!0)])),_:1})])),_:1},8,["data"])),[[He,l.value]]),u("div",q,[o(je,{"current-page":de.page,"onUpdate:currentPage":a[10]||(a[10]=e=>de.page=e),"page-size":de.page_size,"onUpdate:pageSize":a[11]||(a[11]=e=>de.page_size=e),"page-sizes":[10,20,50,100],total:de.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:fe,onCurrentChange:be},null,8,["current-page","page-size","total"])])])),_:1}),o(Re,{modelValue:se.value,"onUpdate:modelValue":a[16]||(a[16]=e=>se.value=e),title:"回调记录详情",width:"1000px","close-on-click-modal":!1},{footer:i((()=>[o(t,{onClick:a[14]||(a[14]=e=>se.value=!1)},{default:i((()=>a[32]||(a[32]=[_("关闭")]))),_:1}),!ae.value||"failed"!==ae.value.internal_status&&"failed"!==ae.value.external_status?S("",!0):(n(),C(t,{key:0,type:"warning",onClick:a[15]||(a[15]=e=>ge(ae.value))},{default:i((()=>a[33]||(a[33]=[_(" 重试回调 ")]))),_:1}))])),default:i((()=>[ae.value?(n(),s("div",E,[o(Se,{column:2,border:""},{default:i((()=>[o(De,{label:"记录ID"},{default:i((()=>[_(z(ae.value.id),1)])),_:1}),o(De,{label:"供应商"},{default:i((()=>[o(Ce,{type:oe(ae.value.provider),size:"small"},{default:i((()=>[_(z(ie(ae.value.provider)),1)])),_:1},8,["type"])])),_:1}),o(De,{label:"回调类型"},{default:i((()=>[_(z(ae.value.callback_type),1)])),_:1}),o(De,{label:"事件类型"},{default:i((()=>[o(Ce,{type:"info",size:"small"},{default:i((()=>[_(z(_e(ae.value.event_type)),1)])),_:1})])),_:1}),o(De,{label:"订单号"},{default:i((()=>[_(z(ae.value.order_no),1)])),_:1}),o(De,{label:"客户订单号"},{default:i((()=>[_(z(ae.value.customer_order_no),1)])),_:1}),o(De,{label:"快递单号"},{default:i((()=>[_(z(ae.value.tracking_no),1)])),_:1}),o(De,{label:"用户"},{default:i((()=>[u("div",null,[u("div",null,[a[25]||(a[25]=u("strong",null,"用户名:",-1)),_(" "+z(ae.value.username||"未知"),1)]),u("div",null,[a[26]||(a[26]=u("strong",null,"用户ID:",-1)),_(" "+z(ae.value.user_id),1)])])])),_:1}),o(De,{label:"内部状态"},{default:i((()=>[o(Ce,{type:pe(ae.value.internal_status),size:"small"},{default:i((()=>[_(z(ce(ae.value.internal_status)),1)])),_:1},8,["type"])])),_:1}),o(De,{label:"外部状态"},{default:i((()=>[o(Ce,{type:pe(ae.value.external_status),size:"small"},{default:i((()=>[_(z(ce(ae.value.external_status)),1)])),_:1},8,["type"])])),_:1}),o(De,{label:"重试次数"},{default:i((()=>[o(Ce,{type:ae.value.retry_count>0?"warning":"info",size:"small"},{default:i((()=>[_(z(ae.value.retry_count),1)])),_:1},8,["type"])])),_:1}),o(De,{label:"接收时间"},{default:i((()=>[_(z(ne(ae.value.received_at)),1)])),_:1}),o(De,{label:"内部处理时间"},{default:i((()=>[_(z(ae.value.internal_processed_at?ne(ae.value.internal_processed_at):"-"),1)])),_:1}),o(De,{label:"外部处理时间"},{default:i((()=>[_(z(ae.value.external_processed_at?ne(ae.value.external_processed_at):"-"),1)])),_:1})])),_:1}),u("div",G,[a[29]||(a[29]=u("h4",null,"错误信息",-1)),o(Ie,{gutter:20},{default:i((()=>[o(Ye,{span:12},{default:i((()=>[u("div",Q,[a[27]||(a[27]=u("h5",null,"内部错误",-1)),o(r,{modelValue:ae.value.internal_error,"onUpdate:modelValue":a[12]||(a[12]=e=>ae.value.internal_error=e),type:"textarea",rows:3,readonly:"",placeholder:"无错误信息"},null,8,["modelValue"])])])),_:1}),o(Ye,{span:12},{default:i((()=>[u("div",Z,[a[28]||(a[28]=u("h5",null,"外部错误",-1)),o(r,{modelValue:ae.value.external_error,"onUpdate:modelValue":a[13]||(a[13]=e=>ae.value.external_error=e),type:"textarea",rows:3,readonly:"",placeholder:"无错误信息"},null,8,["modelValue"])])])),_:1})])),_:1})]),u("div",F,[a[30]||(a[30]=u("h4",null,"原始数据",-1)),o(r,{"model-value":ue(ae.value.raw_data),type:"textarea",rows:8,readonly:"",placeholder:"无原始数据"},null,8,["model-value"])]),u("div",W,[a[31]||(a[31]=u("h4",null,"标准化数据",-1)),o(r,{"model-value":ue(ae.value.standardized_data),type:"textarea",rows:8,readonly:"",placeholder:"无标准化数据"},null,8,["model-value"])])])):S("",!0)])),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-c9be9b95"]]);export{ee as default};
