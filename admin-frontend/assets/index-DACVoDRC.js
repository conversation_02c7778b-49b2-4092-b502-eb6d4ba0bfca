var e=Object.defineProperty,a=Object.getOwnPropertySymbols,l=Object.prototype.hasOwnProperty,t=Object.prototype.propertyIsEnumerable,r=(a,l,t)=>l in a?e(a,l,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[l]=t,d=(e,d)=>{for(var o in d||(d={}))l.call(d,o)&&r(e,o,d[o]);if(a)for(var o of a(d))t.call(d,o)&&r(e,o,d[o]);return e},o=(e,a,l)=>new Promise(((t,r)=>{var d=e=>{try{i(l.next(e))}catch(a){r(a)}},o=e=>{try{i(l.throw(e))}catch(a){r(a)}},i=e=>e.done?t(e.value):Promise.resolve(e.value).then(d,o);i((l=l.apply(e,a)).next())}));import{h as i,_ as s}from"./index-rNRt1EuS.js";/* empty css                    *//* empty css                *//* empty css               */import{d as n,r as p,X as c,f as u,p as _,c as v,o as g,e as m,a as f,b as h,K as y,w as b,y as x,u as w,aQ as C,H as z,aB as k,aS as S,aN as F,aT as j,b7 as V,aV as O,Z as T,_ as $,ar as E,as as U,C as P,aF as B,a3 as D,aU as Y,a2 as H,N,aI as Z,aJ as A,x as I,aX as J,aG as K,aH as q,b6 as M,M as X,ai as G,T as L}from"./vendor-CAPBtMef.js";import{S as Q}from"./StatCard-DtRQl2IM.js";import R from"./StatsView-D2swftZZ.js";const W={class:"warmup-data-management"},ee={class:"page-header"},ae={class:"page-actions"},le={key:0,class:"overview-cards"},te={class:"search-filters"},re={class:"price-table-view"},de={class:"channel-info"},oe={class:"channel-name"},ie={key:0,class:"channel-desc"},se={key:0},ne={class:"company-name"},pe={class:"company-code"},ce={key:0,class:"price-cell"},ue={class:"price-value"},_e={class:"pagination-wrapper"},ve={class:"data-list"},ge={class:"route-info"},me={class:"from"},fe={class:"to"},he={class:"company-name"},ye={class:"company-code"},be={class:"provider-name"},xe={class:"provider-code"},we={class:"price-amount"},Ce={class:"pagination-wrapper"},ze=s(n({__name:"index",setup(e){const a=p(!1),l=p(!1),t=p(!1),r=p("price-table"),s=p([]),n=p([]),ze=p(null),ke=c({provider:"",express_code:"",from_province:"",to_province:"",price_range:"",cache_hit:void 0}),Se=c({page:1,pageSize:20,total:0}),Fe=()=>o(this,null,(function*(){try{a.value=!0,"price-table"===r.value?yield Ve():yield je()}catch(e){X.error("获取数据失败: "+e.message)}finally{a.value=!1}})),je=()=>o(this,null,(function*(){var e;const a=d({page:Se.page,page_size:Se.pageSize},Object.fromEntries(Object.entries(ke).filter((([e,a])=>""!==a&&void 0!==a)))),l=yield i.get({url:"/api/v1/admin/warmup/data",params:a});l.success?(s.value=l.data.records||[],ze.value=(null==(e=l.data.stats)?void 0:e.total_stats)||null,Se.total=l.data.total):X.error(l.message||"获取数据失败")})),Ve=()=>o(this,null,(function*(){const e=d({page:Se.page,page_size:Se.pageSize},Object.fromEntries(Object.entries(ke).filter((([e,a])=>""!==a&&void 0!==a)))),a=yield i.get({url:"/api/v1/admin/warmup/price-table",params:e});if(a.success)n.value=Oe(a.data.records||[]),Se.total=a.data.total;else{const a=yield i.get({url:"/api/v1/admin/warmup/data",params:e});a.success?(n.value=Oe(a.data.records||[]),Se.total=a.data.total):X.error("获取价格表数据失败")}})),Oe=e=>{const a=new Map;let l=1651;return e.forEach((e=>{const t=`${e.provider}-${e.express_code}`;a.has(t)||a.set(t,{id:l++,channel_id:l.toString(),channel_name:`${e.express_name}-${Ie(e.provider)}全国通用`,channel_desc:`${e.from_province||"全国"}到${e.to_province||"全国"}`,express_name:e.express_name,express_code:e.express_code,provider:e.provider,volume_ratio:e.volume_ratio||Te(e.express_code),user_price:e.price,status:"正常",hasChildren:!1,children:[]})})),Array.from(a.values())},Te=e=>({SF:6e3,JD:8e3,YTO:8e3,ZTO:8e3,STO:8e3,YD:8e3,HTKY:8e3}[e]||8e3),$e=()=>o(this,null,(function*(){try{l.value=!0;const e=yield i.post({url:"/api/v1/admin/warmup/refresh"});e.success?(X.success("预热数据刷新已启动"),setTimeout((()=>Fe()),2e3)):X.error(e.message||"刷新失败")}catch(e){X.error("刷新失败")}finally{l.value=!1}})),Ee=()=>o(this,null,(function*(){try{t.value=!0;const{value:e}=yield G.prompt("请选择导出类型","导出数据",{confirmButtonText:"导出",cancelButtonText:"取消",inputType:"select",inputOptions:{all:"全部数据",current:"当前筛选结果",providers:"按供应商分组",companies:"按快递公司分组"},inputValue:"current"}),a=d({export_type:e},Object.fromEntries(Object.entries(ke).filter((([e,a])=>""!==a&&void 0!==a)))),l=yield i.post({url:"/api/v1/admin/warmup/export",data:a});if(l.success){X.success(`导出成功！文件包含 ${l.data.record_count} 条记录`);const e=`/api/v1/admin/warmup/download/${l.data.file_name}`,a=document.createElement("a");a.href=e,a.download=l.data.file_name,document.body.appendChild(a),a.click(),document.body.removeChild(a)}else X.error(l.message||"导出失败")}catch(e){"cancel"!==e&&X.error("导出失败")}finally{t.value=!1}})),Ue=()=>{Se.page=1,Fe()},Pe=()=>{Object.assign(ke,{provider:"",express_code:"",from_province:"",to_province:"",price_range:"",cache_hit:void 0}),Se.page=1,Fe(),X.success("搜索条件已重置")},Be=(e,a)=>{},De=e=>o(this,null,(function*(){try{const a={provider:e.provider,express_code:e.express_code,page:1,page_size:100},l=yield i.get({url:"/api/v1/admin/warmup/data",params:a});l.success&&l.data.records?Ye(e,l.data.records):X.error("获取价格明细失败")}catch(a){X.error("获取价格明细失败")}})),Ye=(e,a)=>{const l=a.map((e=>({from_location:`${e.from_province} ${e.from_city}`,to_location:`${e.to_province} ${e.to_city}`,weight:e.weight,first_weight_price:e.price,continue_weight_price:(.6*parseFloat(e.price)).toFixed(2),weight_range:He(e.weight)})));G({title:"查看价格明细",message:L("div",{style:"max-height: 500px; overflow-y: auto;"},[L("div",{style:"margin-bottom: 16px; padding: 12px; background: #f5f5f5; border-radius: 4px;"},[L("div",{style:"font-weight: bold; margin-bottom: 8px;"},`商户编号：#25333 渠道编号：#${e.channel_id}`),L("div",{style:"display: flex; gap: 20px;"},[L("span",`快递公司：${e.express_name}`),L("span","计费类型：模板计费")])]),L("table",{style:"width: 100%; border-collapse: collapse; font-size: 12px;",border:"1"},[L("thead",[L("tr",{style:"background: #f0f0f0;"},[L("th",{style:"padding: 8px; border: 1px solid #ddd; text-align: center;"},"始发地"),L("th",{style:"padding: 8px; border: 1px solid #ddd; text-align: center;"},"目的地"),L("th",{style:"padding: 8px; border: 1px solid #ddd; text-align: center;"},"首重量"),L("th",{style:"padding: 8px; border: 1px solid #ddd; text-align: center;"},"首重价格"),L("th",{style:"padding: 8px; border: 1px solid #ddd; text-align: center;"},"续重量"),L("th",{style:"padding: 8px; border: 1px solid #ddd; text-align: center;"},"续重价格"),L("th",{style:"padding: 8px; border: 1px solid #ddd; text-align: center;"},"重量范围")])]),L("tbody",l.map((e=>L("tr",[L("td",{style:"padding: 8px; border: 1px solid #ddd; text-align: center;"},e.from_location),L("td",{style:"padding: 8px; border: 1px solid #ddd; text-align: center;"},e.to_location),L("td",{style:"padding: 8px; border: 1px solid #ddd; text-align: center;"},"20"),L("td",{style:"padding: 8px; border: 1px solid #ddd; text-align: center;"},e.first_weight_price),L("td",{style:"padding: 8px; border: 1px solid #ddd; text-align: center;"},"1"),L("td",{style:"padding: 8px; border: 1px solid #ddd; text-align: center;"},e.continue_weight_price),L("td",{style:"padding: 8px; border: 1px solid #ddd; text-align: center;"},e.weight_range)]))))])]),showCancelButton:!1,confirmButtonText:"导出",cancelButtonText:"关闭",customClass:"price-detail-dialog"}).then((()=>{X.success("导出功能开发中...")})).catch((()=>{}))},He=e=>"1-99",Ne=({prop:e,order:a})=>{Fe()},Ze=e=>{Se.pageSize=e,Se.page=1,Fe()},Ae=e=>{Se.page=e,Fe()},Ie=e=>({kuaidi100:"快递100",yida:"易达",yuntong:"云通"}[e]||e),Je=e=>{if(null==e)return"¥0.00";const a="string"==typeof e?parseFloat(e):e;return isNaN(a)?"¥0.00":`¥${a.toFixed(2)}`};return u((()=>{Fe()})),_(r,(()=>{Se.page=1,Fe()})),(e,d)=>{const o=x,i=z,p=F,c=j,u=U,_=E,X=$,G=P,L=B,je=T,Ve=O,Oe=A,Te=J,Ye=Z,He=q,Ke=V,qe=M,Me=K;return g(),v("div",W,[m("div",ee,[d[13]||(d[13]=m("div",{class:"header-left"},[m("h1",{class:"page-title"},[m("i",{class:"iconfont-sys"},""),y(" 预热数据管理 ")]),m("p",{class:"page-description"},"查看和管理快递价格预热数据，分析供应商性能")],-1)),m("div",ae,[h(i,{onClick:Fe,loading:a.value},{default:b((()=>[h(o,null,{default:b((()=>[h(w(C))])),_:1}),d[10]||(d[10]=y(" 刷新 "))])),_:1},8,["loading"]),h(i,{type:"primary",onClick:$e,loading:l.value},{default:b((()=>[h(o,null,{default:b((()=>[h(w(k))])),_:1}),d[11]||(d[11]=y(" 刷新预热数据 "))])),_:1},8,["loading"]),h(i,{type:"success",onClick:Ee,loading:t.value},{default:b((()=>[h(o,null,{default:b((()=>[h(w(S))])),_:1}),d[12]||(d[12]=y(" 导出 "))])),_:1},8,["loading"])])]),ze.value?(g(),v("div",le,[h(c,{gutter:20},{default:b((()=>[h(p,{span:6},{default:b((()=>[h(Q,{title:"总记录数",value:ze.value.total_records,icon:"el-icon-document","icon-color":"#409EFF","icon-bg-color":"#E6F7FF"},null,8,["value"])])),_:1}),h(p,{span:6},{default:b((()=>[h(Q,{title:"快递公司数",value:ze.value.total_companies,icon:"el-icon-office-building","icon-color":"#67C23A","icon-bg-color":"#F0F9FF"},null,8,["value"])])),_:1}),h(p,{span:6},{default:b((()=>[h(Q,{title:"供应商数",value:ze.value.total_providers,icon:"el-icon-connection","icon-color":"#E6A23C","icon-bg-color":"#FDF6EC"},null,8,["value"])])),_:1}),h(p,{span:6},{default:b((()=>[h(Q,{title:"平均价格",value:Je(ze.value.avg_price),icon:"el-icon-coin","icon-color":"#F56C6C","icon-bg-color":"#FEF0F0"},null,8,["value"])])),_:1})])),_:1})])):f("",!0),h(qe,{modelValue:r.value,"onUpdate:modelValue":d[9]||(d[9]=e=>r.value=e),type:"card"},{default:b((()=>[h(Ke,{label:"价格表视图",name:"price-table"},{default:b((()=>[m("div",te,[h(Ve,null,{default:b((()=>[h(je,{model:ke,"label-width":"80px"},{default:b((()=>[h(c,{gutter:20},{default:b((()=>[h(p,{span:4},{default:b((()=>[h(X,{label:"供应商"},{default:b((()=>[h(_,{modelValue:ke.provider,"onUpdate:modelValue":d[0]||(d[0]=e=>ke.provider=e),placeholder:"全部供应商",clearable:"",style:{width:"100%"}},{default:b((()=>[h(u,{label:"快递100",value:"kuaidi100"}),h(u,{label:"易达",value:"yida"}),h(u,{label:"云通",value:"yuntong"})])),_:1},8,["modelValue"])])),_:1})])),_:1}),h(p,{span:4},{default:b((()=>[h(X,{label:"快递公司"},{default:b((()=>[h(_,{modelValue:ke.express_code,"onUpdate:modelValue":d[1]||(d[1]=e=>ke.express_code=e),placeholder:"全部快递公司",clearable:"",style:{width:"100%"}},{default:b((()=>[h(u,{label:"顺丰速运",value:"SF"}),h(u,{label:"圆通速递",value:"YTO"}),h(u,{label:"中通快递",value:"ZTO"}),h(u,{label:"申通快递",value:"STO"}),h(u,{label:"韵达速递",value:"YD"}),h(u,{label:"百世快递",value:"HTKY"}),h(u,{label:"京东快递",value:"JD"})])),_:1},8,["modelValue"])])),_:1})])),_:1}),h(p,{span:4},{default:b((()=>[h(X,{label:"起始省份"},{default:b((()=>[h(G,{modelValue:ke.from_province,"onUpdate:modelValue":d[2]||(d[2]=e=>ke.from_province=e),placeholder:"起始省份",clearable:""},null,8,["modelValue"])])),_:1})])),_:1}),h(p,{span:4},{default:b((()=>[h(X,{label:"目的省份"},{default:b((()=>[h(G,{modelValue:ke.to_province,"onUpdate:modelValue":d[3]||(d[3]=e=>ke.to_province=e),placeholder:"目的省份",clearable:""},null,8,["modelValue"])])),_:1})])),_:1}),h(p,{span:4},{default:b((()=>[h(X,{label:"价格范围"},{default:b((()=>[h(G,{modelValue:ke.price_range,"onUpdate:modelValue":d[4]||(d[4]=e=>ke.price_range=e),placeholder:"如: 10-20",clearable:""},null,8,["modelValue"])])),_:1})])),_:1}),h(p,{span:4},{default:b((()=>[h(X,{label:" "},{default:b((()=>[h(L,null,{default:b((()=>[h(i,{type:"primary",onClick:Ue,loading:a.value},{default:b((()=>[h(o,null,{default:b((()=>[h(w(D))])),_:1}),d[14]||(d[14]=y(" 搜索 "))])),_:1},8,["loading"]),h(i,{onClick:Pe},{default:b((()=>[h(o,null,{default:b((()=>[h(w(Y))])),_:1}),d[15]||(d[15]=y(" 重置 "))])),_:1})])),_:1})])),_:1})])),_:1})])),_:1})])),_:1},8,["model"])])),_:1})]),m("div",re,[h(Ve,null,{default:b((()=>[H((g(),N(Ye,{data:n.value,stripe:"",border:"",style:{width:"100%"},"row-key":"id","tree-props":{children:"children",hasChildren:"hasChildren"},"default-expand-all":!1,onExpandChange:Be},{default:b((()=>[h(Oe,{label:"渠道编号",width:"100",prop:"channel_id"}),h(Oe,{label:"渠道名称",width:"200"},{default:b((({row:e})=>[m("div",de,[m("div",oe,I(e.channel_name),1),e.channel_desc?(g(),v("div",ie,I(e.channel_desc),1)):f("",!0)])])),_:1}),h(Oe,{label:"快递公司",width:"120"},{default:b((({row:e})=>[e.express_name?(g(),v("div",se,[m("div",ne,I(e.express_name),1),m("div",pe,I(e.express_code),1)])):f("",!0)])),_:1}),h(Oe,{label:"供应商",width:"100"},{default:b((({row:e})=>{return[e.provider?(g(),N(Te,{key:0,type:(a=e.provider,{kuaidi100:"primary",yida:"success",yuntong:"warning"}[a]||"info"),size:"small"},{default:b((()=>[y(I(Ie(e.provider)),1)])),_:2},1032,["type"])):f("",!0)];var a})),_:1}),h(Oe,{label:"抛比",width:"80",prop:"volume_ratio"}),h(Oe,{label:"用户价格",width:"100"},{default:b((({row:e})=>[void 0!==e.user_price?(g(),v("div",ce,[m("span",ue,I(Je(e.user_price)),1)])):f("",!0)])),_:1}),h(Oe,{label:"渠道状态",width:"100"},{default:b((({row:e})=>[void 0!==e.status?(g(),N(Te,{key:0,type:"正常"===e.status?"success":"danger",size:"small"},{default:b((()=>[y(I(e.status),1)])),_:2},1032,["type"])):f("",!0)])),_:1}),h(Oe,{label:"操作",width:"120",fixed:"right"},{default:b((({row:e})=>[e.children?f("",!0):(g(),N(i,{key:0,type:"primary",size:"small",onClick:a=>De(e),link:""},{default:b((()=>d[16]||(d[16]=[y(" 查看价格明细 ")]))),_:2},1032,["onClick"]))])),_:1})])),_:1},8,["data"])),[[Me,a.value]]),m("div",_e,[h(He,{"current-page":Se.page,"onUpdate:currentPage":d[5]||(d[5]=e=>Se.page=e),"page-size":Se.pageSize,"onUpdate:pageSize":d[6]||(d[6]=e=>Se.pageSize=e),"page-sizes":[10,20,50,100],total:Se.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:Ze,onCurrentChange:Ae},null,8,["current-page","page-size","total"])])])),_:1})])])),_:1}),h(Ke,{label:"数据列表",name:"data"},{default:b((()=>[m("div",ve,[h(Ve,null,{default:b((()=>[H((g(),N(Ye,{data:s.value,onSortChange:Ne,stripe:"",border:"",style:{width:"100%"},"row-key":"id"},{default:b((()=>[h(Oe,{label:"路线",width:"180"},{default:b((({row:e})=>[m("div",ge,[m("div",me,I(e.from_province)+" "+I(e.from_city),1),d[17]||(d[17]=m("div",{class:"arrow"},"↓",-1)),m("div",fe,I(e.to_province)+" "+I(e.to_city),1)])])),_:1}),h(Oe,{prop:"weight",label:"重量(kg)",width:"100",sortable:"custom"},{default:b((({row:e})=>[y(I("number"==typeof e.weight?e.weight.toFixed(2):e.weight),1)])),_:1}),h(Oe,{label:"快递公司",width:"140"},{default:b((({row:e})=>[m("div",null,[m("div",he,I(e.express_name),1),m("div",ye,I(e.express_code),1)])])),_:1}),h(Oe,{label:"供应商",width:"120"},{default:b((({row:e})=>[m("div",null,[m("div",be,I(Ie(e.provider)),1),m("div",xe,I(e.provider_code),1)])])),_:1}),h(Oe,{prop:"price",label:"价格",width:"100",sortable:"custom"},{default:b((({row:e})=>[m("div",we,I(Je(e.price)),1)])),_:1}),h(Oe,{prop:"estimated_days",label:"预计天数",width:"100"}),h(Oe,{label:"缓存",width:"80"},{default:b((({row:e})=>[h(Te,{type:e.cache_hit?"success":"danger"},{default:b((()=>[y(I(e.cache_hit?"命中":"未命中"),1)])),_:2},1032,["type"])])),_:1}),h(Oe,{prop:"response_time_ms",label:"响应时间(ms)",width:"120",sortable:"custom"}),h(Oe,{prop:"query_time",label:"查询时间",width:"180"},{default:b((({row:e})=>{return[y(I((a=e.query_time,a?new Date(a).toLocaleString():"-")),1)];var a})),_:1})])),_:1},8,["data"])),[[Me,a.value]]),m("div",Ce,[h(He,{"current-page":Se.page,"onUpdate:currentPage":d[7]||(d[7]=e=>Se.page=e),"page-size":Se.pageSize,"onUpdate:pageSize":d[8]||(d[8]=e=>Se.pageSize=e),"page-sizes":[10,20,50,100],total:Se.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:Ze,onCurrentChange:Ae},null,8,["current-page","page-size","total"])])])),_:1})])])),_:1}),h(Ke,{label:"统计分析",name:"stats"},{default:b((()=>[h(R)])),_:1})])),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-f9e3ddf5"]]);export{ze as default};
