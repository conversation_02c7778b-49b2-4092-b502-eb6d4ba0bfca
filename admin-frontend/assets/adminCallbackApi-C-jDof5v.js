var e,t=Object.defineProperty,r=(e,t,r)=>new Promise(((s,a)=>{var i=e=>{try{o(r.next(e))}catch(t){a(t)}},n=e=>{try{o(r.throw(e))}catch(t){a(t)}},o=e=>e.done?s(e.value):Promise.resolve(e.value).then(i,n);o((r=r.apply(e,t)).next())}));import{h as s}from"./index-rNRt1EuS.js";class a{static getCallbackRecords(){return r(this,arguments,(function*(e={}){var t;try{const r=yield s.get({url:`${this.BASE_URL}/records`,params:{page:e.page||1,page_size:e.page_size||20,user_id:e.user_id||"",provider:e.provider||"",event_type:e.event_type||"",order_no:e.order_no||"",tracking_no:e.tracking_no||"",internal_status:e.internal_status||"",external_status:e.external_status||"",start_time:e.start_time||"",end_time:e.end_time||"",order_by:e.order_by||"received_at",order:e.order||"desc",_t:Date.now()}});if(r&&"object"==typeof r){if("success"in r&&"data"in r&&r.success&&r.data)return{records:r.data.records||[],total:r.data.total||0,page:r.data.page||1,page_size:r.data.page_size||20};if("records"in r){const e=r;return{records:e.records||[],total:e.total||(null==(t=e.records)?void 0:t.length)||0,page:e.page||1,page_size:e.page_size||20}}}throw new Error("获取回调记录失败")}catch(r){throw new Error(r.message||"获取回调记录失败")}}))}static getCallbackRecordById(e){return r(this,null,(function*(){try{const t=yield s.get({url:`${this.BASE_URL}/records/${e}`});if(t&&"object"==typeof t){if("success"in t&&"data"in t&&t.success&&t.data)return t.data;if("id"in t)return t}throw new Error("获取回调记录详情失败")}catch(t){throw new Error(t.message||"获取回调记录详情失败")}}))}static retryCallback(e){return r(this,null,(function*(){try{const t=yield s.post({url:`${this.BASE_URL}/retry/${e}`});if(t&&"object"==typeof t&&"success"in t&&!t.success)throw new Error(t.message||"重试回调失败")}catch(t){throw new Error(t.message||"重试回调失败")}}))}static batchRetryCallbacks(e){return r(this,null,(function*(){try{const t=e.map((e=>this.retryCallback(e)));yield Promise.all(t)}catch(t){throw new Error(t.message||"批量重试回调失败")}}))}static getCallbackStatistics(){return r(this,arguments,(function*(e={}){try{const t=yield s.get({url:`${this.BASE_URL}/statistics`,params:{start_time:e.start_time||"",end_time:e.end_time||"",provider:e.provider||"",event_type:e.event_type||"",user_id:e.user_id||"",_t:Date.now()}});if(t&&"object"==typeof t){let e=null;if("success"in t&&"data"in t&&t.success&&t.data?e=t.data:"total_records"in t&&(e=t),e)return{total_records:e.total_records||0,success_records:e.success_records||0,failed_records:e.failed_records||0,pending_records:e.pending_records||0,success_rate:e.success_rate||0,avg_processing_time:e.avg_processing_time||0,provider_stats:e.provider_stats||{},event_type_stats:e.event_type_stats||{},daily_stats:e.daily_stats||[]}}throw new Error("获取回调统计失败")}catch(t){throw new Error(t.message||"获取回调统计失败")}}))}static exportCallbackRecords(){return r(this,arguments,(function*(e={}){try{return yield s.get({url:`${this.BASE_URL}/export`,params:{user_id:e.user_id||"",provider:e.provider||"",event_type:e.event_type||"",order_no:e.order_no||"",tracking_no:e.tracking_no||"",internal_status:e.internal_status||"",external_status:e.external_status||"",start_time:e.start_time||"",end_time:e.end_time||"",format:"excel"},responseType:"blob"})}catch(t){throw new Error(t.message||"导出回调记录失败")}}))}static getUserCallbackConfig(e){return r(this,null,(function*(){try{const t=yield s.get({url:`${this.BASE_URL}/user-config/${e}`});if(t.success&&t.data)return t.data;throw new Error("获取用户回调配置失败")}catch(t){throw new Error(t.message||"获取用户回调配置失败")}}))}static updateUserCallbackConfig(e,t){return r(this,null,(function*(){try{const r=yield s.post({url:`${this.BASE_URL}/user-config/${e}`,data:t});if(!r.success)throw new Error(r.message||"更新用户回调配置失败")}catch(r){throw new Error(r.message||"更新用户回调配置失败")}}))}}((e,r,s)=>{r in e?t(e,r,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[r]=s})(a,"symbol"!=typeof(e="BASE_URL")?e+"":e,"/api/v1/admin/callbacks");export{a as A};
