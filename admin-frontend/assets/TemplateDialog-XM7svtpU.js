var e=(e,a,l)=>new Promise(((t,i)=>{var s=e=>{try{r(l.next(e))}catch(a){i(a)}},o=e=>{try{r(l.throw(e))}catch(a){i(a)}},r=e=>e.done?t(e.value):Promise.resolve(e.value).then(s,o);r((l=l.apply(e,a)).next())}));import{_ as a}from"./index-rNRt1EuS.js";/* empty css                             */import{d as l,r as t,X as i,j as s,p as o,M as r,N as p,o as n,w as d,e as u,b as c,H as m,u as v,aR as _,K as g,aQ as f,a2 as y,aI as h,aJ as b,x as w,aX as k,aG as x,aH as z,Y as C,c as V,a as T,b1 as j,b2 as U,Z as B,_ as S,C as P,ap as q,ai as D}from"./vendor-CAPBtMef.js";import{S as H}from"./systemConfigApi-Cw3xPQeV.js";const I={class:"toolbar"},L={class:"template-list"},N={class:"pagination-wrapper"},O={key:0,class:"template-detail"},R={class:"config-list"},X={class:"dialog-footer"},$={class:"dialog-footer"},A=a(l({__name:"TemplateDialog",props:{visible:{type:Boolean}},emits:["update:visible","apply-template"],setup(a,{emit:l}){const A=a,F=l,G=t(!1),J=t(!1),K=t(!1),M=t([]),Q=i({page:1,page_size:20,total:0}),Y=t(!1),Z=t(!1),E=t(null),W=t(),ee=i({template_name:"",description:"",is_active:!0,template_data:[]}),ae=s((()=>{var e;return!!(null==(e=E.value)?void 0:e.id)})),le={template_name:[{required:!0,message:"请输入模板名称",trigger:"blur"},{min:2,max:50,message:"模板名称长度在 2 到 50 个字符",trigger:"blur"}],description:[{required:!0,message:"请输入模板描述",trigger:"blur"},{max:500,message:"描述长度不能超过 500 个字符",trigger:"blur"}]};o((()=>A.visible),(e=>{G.value=e,e&&te()})),o(G,(e=>{F("update:visible",e)}));const te=()=>e(this,null,(function*(){try{J.value=!0;const e=yield H.getTemplateList({page:Q.page,page_size:Q.page_size});e.success?(M.value=e.data.templates,Q.total=e.data.total):r.error(e.message||"获取模板列表失败")}catch(e){r.error("获取模板列表失败")}finally{J.value=!1}})),ie=e=>{Q.page=e,te()},se=e=>{Q.page_size=e,Q.page=1,te()},oe=a=>e(this,null,(function*(){try{yield D.confirm(`确认应用模板 "${a.template_name}" 吗？这将覆盖现有的相关配置。`,"确认应用模板",{type:"warning",confirmButtonText:"确认",cancelButtonText:"取消"});const e=yield H.applyTemplate(a.template_name);e.success?(r.success("模板应用成功"),F("apply-template")):r.error(e.message||"模板应用失败")}catch(e){"cancel"!==e&&r.error("模板应用失败")}})),re=()=>{E.value=null,Object.assign(ee,{template_name:"",description:"",is_active:!0,template_data:[]}),Z.value=!0},pe=()=>e(this,null,(function*(){if(W.value)try{if(!(yield W.value.validate()))return;let e;K.value=!0,e=ae.value&&E.value?yield H.updateTemplate(E.value.id,ee):yield H.createTemplate(ee),e.success?(r.success(ae.value?"更新成功":"创建成功"),Z.value=!1,te()):r.error(e.message||(ae.value?"更新失败":"创建失败"))}catch(e){r.error(ae.value?"更新失败":"创建失败")}finally{K.value=!1}})),ne=a=>e(this,null,(function*(){try{yield D.confirm(`确认删除模板 "${a.template_name}" 吗？此操作不可撤销。`,"确认删除",{type:"warning",confirmButtonText:"确认",cancelButtonText:"取消"});const e=yield H.deleteTemplate(a.id);e.success?(r.success("删除成功"),te()):r.error(e.message||"删除失败")}catch(e){"cancel"!==e&&r.error("删除失败")}})),de=()=>{G.value=!1},ue=e=>e?new Date(e).toLocaleString("zh-CN"):"-";return(e,a)=>{const l=m,t=b,i=k,s=h,o=z,r=U,D=j,H=C,A=P,F=S,ce=q,me=B,ve=x;return n(),p(H,{modelValue:G.value,"onUpdate:modelValue":a[8]||(a[8]=e=>G.value=e),title:"配置模板管理",width:"900px","before-close":de},{footer:d((()=>[u("div",$,[c(l,{onClick:de},{default:d((()=>a[16]||(a[16]=[g("关闭")]))),_:1})])])),default:d((()=>[u("div",I,[c(l,{type:"primary",onClick:re,icon:v(_)},{default:d((()=>a[9]||(a[9]=[g(" 创建模板 ")]))),_:1},8,["icon"]),c(l,{onClick:te,icon:v(f)},{default:d((()=>a[10]||(a[10]=[g(" 刷新 ")]))),_:1},8,["icon"])]),u("div",L,[y((n(),p(s,{data:M.value,stripe:"",style:{width:"100%"}},{default:d((()=>[c(t,{prop:"template_name",label:"模板名称",width:"200"}),c(t,{prop:"description",label:"描述","min-width":"250","show-overflow-tooltip":""}),c(t,{label:"配置数量",width:"100"},{default:d((({row:e})=>{var a;return[g(w((null==(a=e.template_data)?void 0:a.length)||0),1)]})),_:1}),c(t,{prop:"is_active",label:"状态",width:"80"},{default:d((({row:e})=>[c(i,{type:e.is_active?"success":"danger",size:"small"},{default:d((()=>[g(w(e.is_active?"启用":"禁用"),1)])),_:2},1032,["type"])])),_:1}),c(t,{prop:"created_at",label:"创建时间",width:"160"},{default:d((({row:e})=>[g(w(ue(e.created_at)),1)])),_:1}),c(t,{label:"操作",width:"200",fixed:"right"},{default:d((({row:e})=>[c(l,{type:"primary",link:"",size:"small",onClick:a=>{return l=e,E.value=l,void(Y.value=!0);var l}},{default:d((()=>a[11]||(a[11]=[g(" 查看 ")]))),_:2},1032,["onClick"]),c(l,{type:"success",link:"",size:"small",onClick:a=>oe(e)},{default:d((()=>a[12]||(a[12]=[g(" 应用 ")]))),_:2},1032,["onClick"]),c(l,{type:"warning",link:"",size:"small",onClick:a=>{return l=e,E.value=l,Object.assign(ee,{template_name:l.template_name,description:l.description,is_active:l.is_active,template_data:l.template_data}),void(Z.value=!0);var l}},{default:d((()=>a[13]||(a[13]=[g(" 编辑 ")]))),_:2},1032,["onClick"]),c(l,{type:"danger",link:"",size:"small",onClick:a=>ne(e)},{default:d((()=>a[14]||(a[14]=[g(" 删除 ")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])),[[ve,J.value]]),u("div",N,[c(o,{"current-page":Q.page,"onUpdate:currentPage":a[0]||(a[0]=e=>Q.page=e),"page-size":Q.page_size,"onUpdate:pageSize":a[1]||(a[1]=e=>Q.page_size=e),total:Q.total,"page-sizes":[10,20,50],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:se,onCurrentChange:ie},null,8,["current-page","page-size","total"])])]),c(H,{modelValue:Y.value,"onUpdate:modelValue":a[2]||(a[2]=e=>Y.value=e),title:"模板详情",width:"700px","append-to-body":""},{default:d((()=>{var e;return[E.value?(n(),V("div",O,[c(D,{column:2,border:""},{default:d((()=>[c(r,{label:"模板名称"},{default:d((()=>[g(w(E.value.template_name),1)])),_:1}),c(r,{label:"状态"},{default:d((()=>[c(i,{type:E.value.is_active?"success":"danger",size:"small"},{default:d((()=>[g(w(E.value.is_active?"启用":"禁用"),1)])),_:1},8,["type"])])),_:1}),c(r,{label:"描述",span:2},{default:d((()=>[g(w(E.value.description),1)])),_:1}),c(r,{label:"创建时间",span:2},{default:d((()=>[g(w(ue(E.value.created_at)),1)])),_:1})])),_:1}),u("div",R,[u("h4",null,"包含的配置项 ("+w((null==(e=E.value.template_data)?void 0:e.length)||0)+")",1),c(s,{data:E.value.template_data,size:"small","max-height":"300"},{default:d((()=>[c(t,{prop:"config_group",label:"配置组",width:"120"}),c(t,{prop:"config_key",label:"配置键",width:"180"}),c(t,{prop:"config_value",label:"配置值","min-width":"150","show-overflow-tooltip":""}),c(t,{prop:"config_type",label:"类型",width:"80"}),c(t,{prop:"description",label:"描述","min-width":"150","show-overflow-tooltip":""})])),_:1},8,["data"])])])):T("",!0)]})),_:1},8,["modelValue"]),c(H,{modelValue:Z.value,"onUpdate:modelValue":a[7]||(a[7]=e=>Z.value=e),title:ae.value?"编辑模板":"创建模板",width:"500px","append-to-body":""},{footer:d((()=>[u("div",X,[c(l,{onClick:a[6]||(a[6]=e=>Z.value=!1)},{default:d((()=>a[15]||(a[15]=[g("取消")]))),_:1}),c(l,{type:"primary",onClick:pe,loading:K.value},{default:d((()=>[g(w(ae.value?"更新":"创建"),1)])),_:1},8,["loading"])])])),default:d((()=>[c(me,{ref_key:"templateFormRef",ref:W,model:ee,rules:le,"label-width":"100px"},{default:d((()=>[c(F,{label:"模板名称",prop:"template_name"},{default:d((()=>[c(A,{modelValue:ee.template_name,"onUpdate:modelValue":a[3]||(a[3]=e=>ee.template_name=e),placeholder:"请输入模板名称",clearable:""},null,8,["modelValue"])])),_:1}),c(F,{label:"描述",prop:"description"},{default:d((()=>[c(A,{modelValue:ee.description,"onUpdate:modelValue":a[4]||(a[4]=e=>ee.description=e),type:"textarea",rows:3,placeholder:"请输入模板描述",maxlength:"500","show-word-limit":""},null,8,["modelValue"])])),_:1}),c(F,{label:"状态",prop:"is_active"},{default:d((()=>[c(ce,{modelValue:ee.is_active,"onUpdate:modelValue":a[5]||(a[5]=e=>ee.is_active=e)},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue","title"])])),_:1},8,["modelValue"])}}}),[["__scopeId","data-v-115d752e"]]);export{A as default};
