var t=Object.defineProperty,e=Object.defineProperties,a=Object.getOwnPropertyDescriptors,n=Object.getOwnPropertySymbols,s=Object.prototype.hasOwnProperty,r=Object.prototype.propertyIsEnumerable,i=(e,a,n)=>a in e?t(e,a,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[a]=n,u=(t,e)=>{for(var a in e||(e={}))s.call(e,a)&&i(t,a,e[a]);if(n)for(var a of n(e))r.call(e,a)&&i(t,a,e[a]);return t},l=(t,n)=>e(t,a(n)),o=(t,e,a)=>new Promise(((n,s)=>{var r=t=>{try{u(a.next(t))}catch(e){s(e)}},i=t=>{try{u(a.throw(t))}catch(e){s(e)}},u=t=>t.done?n(t.value):Promise.resolve(t.value).then(r,i);u((a=a.apply(t,e)).next())}));import{h as c}from"./index-rNRt1EuS.js";const p=[{label:"字符串",value:"string"},{label:"整数",value:"integer"},{label:"浮点数",value:"float"},{label:"布尔值",value:"boolean"},{label:"JSON对象",value:"json"},{label:"数组",value:"array"}];class m{static getConfigList(){return o(this,arguments,(function*(t={}){return c.get({url:"/api/v1/admin/system-configs",params:l(u({},t),{_t:Date.now()})})}))}static getConfig(t){return o(this,null,(function*(){return c.get({url:`/api/v1/admin/system-configs/${t}`,params:{_t:Date.now()}})}))}static createConfig(t){return o(this,null,(function*(){return c.post({url:"/api/v1/admin/system-configs",data:t})}))}static updateConfig(t,e){return o(this,null,(function*(){return c.put({url:`/api/v1/admin/system-configs/${t}`,data:e})}))}static deleteConfig(t){return o(this,null,(function*(){return c.del({url:`/api/v1/admin/system-configs/${t}`})}))}static batchUpdateConfigs(t){return o(this,null,(function*(){return c.post({url:"/api/v1/admin/system-configs/batch",data:t})}))}static getConfigGroups(){return o(this,null,(function*(){const t=yield c.get({url:"/api/v1/admin/system-configs/groups",params:{_t:Date.now()}});return t.success&&t.data&&t.data.groups?l(u({},t),{data:t.data.groups}):t}))}static getConfigsByGroup(t){return o(this,null,(function*(){const e=yield c.get({url:`/api/v1/admin/system-configs/groups/${t}`,params:{_t:Date.now()}});return e.success&&e.data&&e.data.configs?l(u({},e),{data:e.data.configs}):e}))}static getChangeLogList(){return o(this,arguments,(function*(t={}){return c.get({url:"/api/v1/admin/system-configs/change-logs",params:l(u({},t),{_t:Date.now()})})}))}static getTemplateList(){return o(this,arguments,(function*(t={}){const e=yield c.get({url:"/api/v1/admin/system-configs/templates",params:l(u({},t),{_t:Date.now()})});return e.success&&e.data&&e.data.templates?l(u({},e),{data:{templates:e.data.templates,total:e.data.templates.length,page:t.page||1,page_size:t.page_size||20,total_pages:Math.ceil(e.data.templates.length/(t.page_size||20))}}):e}))}static getTemplate(t){return o(this,null,(function*(){return c.get({url:`/api/v1/admin/system-configs/templates/${t}`,params:{_t:Date.now()}})}))}static createTemplate(t){return o(this,null,(function*(){return c.post({url:"/api/v1/admin/system-configs/templates",data:t})}))}static updateTemplate(t,e){return o(this,null,(function*(){return c.put({url:`/api/v1/admin/system-configs/templates/${t}`,data:e})}))}static deleteTemplate(t){return o(this,null,(function*(){return c.del({url:`/api/v1/admin/system-configs/templates/${t}`})}))}static applyTemplate(t){return o(this,null,(function*(){return c.post({url:`/api/v1/admin/system-configs/templates/${t}/apply`})}))}static getBackupList(){return o(this,arguments,(function*(t={}){return c.get({url:"/api/v1/admin/system-configs/backups",params:l(u({},t),{_t:Date.now()})})}))}static getBackup(t){return o(this,null,(function*(){return c.get({url:`/api/v1/admin/system-configs/backups/${t}`,params:{_t:Date.now()}})}))}static createBackup(t){return o(this,null,(function*(){return c.post({url:"/api/v1/admin/system-configs/backups",data:t})}))}static restoreBackup(t){return o(this,null,(function*(){return c.post({url:`/api/v1/admin/system-configs/backups/${t}/restore`})}))}static deleteBackup(t){return o(this,null,(function*(){return c.del({url:`/api/v1/admin/system-configs/backups/${t}`})}))}static refreshCache(){return o(this,null,(function*(){return c.post({url:"/api/v1/admin/system-configs/cache/refresh"})}))}static validateConfig(t){return o(this,null,(function*(){return c.post({url:"/api/v1/admin/system-configs/validate",data:t})}))}}export{p as C,m as S};
