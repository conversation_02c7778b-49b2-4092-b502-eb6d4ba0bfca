function n(n,t="¥",e=2){if(null==n||""===n)return"-";const i="string"==typeof n?parseFloat(n):n;return isNaN(i)?"-":`${t}${i.toFixed(e)}`}function t(n){if(null==n)return!1;if("string"==typeof n){const t=n.trim();if(""===t||"null"===t||"undefined"===t)return!1;const e=parseFloat(t);return!isNaN(e)&&e>=0}return"number"==typeof n&&(!isNaN(n)&&n>=0)}function e(n){return null!=n&&!isNaN(n)&&n>0}function i(n){return null!=n&&!isNaN(n)&&n>0}function r(n,t="full"){if(!n)return"-";const e="string"==typeof n?new Date(n):n;if(isNaN(e.getTime()))return"-";const i={year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1};switch(t){case"date":delete i.hour,delete i.minute,delete i.second;break;case"time":delete i.year,delete i.month,delete i.day}return e.toLocaleString("zh-CN",i)}function u(n,t=1){return null==n||isNaN(n)?"-":`${n.toFixed(t)}%`}function o(n){return{yida:"primary",kuaidi100:"success",yuntong:"warning"}[n]||"info"}function a(n){return{yida:"易达",kuaidi100:"快递100",yuntong:"云通"}[n]||n}function s(n){return{pending:"等待中",running:"运行中",completed:"已完成",failed:"已失败",cancelled:"已取消"}[n]||n}function c(n){return{standard:"标准服务",express:"快递服务",economy:"经济服务",premium:"高端服务"}[n]||n}function f(n,t=200){if(0===n)return"0秒";const e=n*t,i=Math.ceil(e/1e3);if(i<60)return`约${i}秒`;if(i<3600){const n=Math.floor(i/60),t=i%60;return t>0?`约${n}分${t}秒`:`约${n}分钟`}{const n=Math.floor(i/3600),t=Math.floor(i%3600/60);return t>0?`约${n}小时${t}分钟`:`约${n}小时`}}export{a,c as b,r as c,u as d,t as e,n as f,o as g,i as h,e as i,f as j,s as k};
