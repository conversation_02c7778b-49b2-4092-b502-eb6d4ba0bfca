var e=(e,a,l)=>new Promise(((s,u)=>{var t=e=>{try{d(l.next(e))}catch(a){u(a)}},r=e=>{try{d(l.throw(e))}catch(a){u(a)}},d=e=>e.done?s(e.value):Promise.resolve(e.value).then(t,r);d((l=l.apply(e,a)).next())}));import{_ as a}from"./index-rNRt1EuS.js";/* empty css                    */import{F as l}from"./flexiblePriceTableApi-BBM6dCg8.js";import{d as s,r as u,X as t,j as r,N as d,o as i,w as o,a2 as v,c as n,b as c,Z as p,a as m,_ as f,ar as b,as as _,e as g,x as h,b8 as y,aG as k,H as x,K as V,Y as w,M as I}from"./vendor-CAPBtMef.js";const T={class:"task-progress"},q={class:"progress-header"},j={class:"task-name"},P={class:"task-status"},D={class:"progress-details"},U={class:"dialog-footer"},O=a(s({__name:"UpdatePriceDialogSimple",props:{visible:{type:Boolean}},emits:["update:visible","success"],setup(a,{emit:s}){const O=a,S=s,Y=u(),C=u(!1),J=u(null),Z=u(null),A=t({providers:[],express_codes:[]}),B=r({get:()=>O.visible,set:e=>S("update:visible",e)}),F=r((()=>A.providers.length>0&&A.express_codes.length>0&&!C.value)),G=()=>{Z.value&&(clearInterval(Z.value),Z.value=null),S("update:visible",!1)},H=()=>e(this,null,(function*(){if(F.value)try{C.value=!0;const e=yield l.updatePriceData(A);e.success?(J.value=e.data,I.success("更新任务已启动"),K()):(I.error(e.message||"启动更新任务失败"),C.value=!1)}catch(e){I.error("提交失败"),C.value=!1}})),K=()=>{Z.value&&clearInterval(Z.value),Z.value=setInterval((()=>e(this,null,(function*(){if(J.value)try{const e=yield l.getTaskStatus(J.value.id);e.success&&e.data&&(J.value=e.data,"completed"===J.value.status?(I.success("价格数据更新完成"),C.value=!1,clearInterval(Z.value),S("success"),setTimeout((()=>{S("update:visible",!1)}),2e3)):"failed"===J.value.status&&(I.error("价格数据更新失败"),C.value=!1,clearInterval(Z.value)))}catch(e){clearInterval(Z.value),C.value=!1}else clearInterval(Z.value)}))),2e3)};return(e,a)=>{const l=_,s=b,u=f,t=y,r=p,I=x,O=w,S=k;return i(),d(O,{modelValue:B.value,"onUpdate:modelValue":a[2]||(a[2]=e=>B.value=e),title:"更新价格数据（简化版）",width:"600px","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:o((()=>[g("div",U,[c(I,{onClick:G,disabled:C.value},{default:o((()=>a[3]||(a[3]=[V("取消")]))),_:1},8,["disabled"]),c(I,{type:"primary",onClick:H,loading:C.value,disabled:!F.value},{default:o((()=>[V(h(C.value?"更新中...":"开始更新"),1)])),_:1},8,["loading","disabled"])])])),default:o((()=>[v((i(),n("div",null,[c(r,{ref_key:"formRef",ref:Y,model:A,"label-width":"100px"},{default:o((()=>[c(u,{label:"供应商",required:""},{default:o((()=>[c(s,{modelValue:A.providers,"onUpdate:modelValue":a[0]||(a[0]=e=>A.providers=e),placeholder:"请选择供应商",multiple:"",style:{width:"100%"}},{default:o((()=>[c(l,{label:"易达",value:"yida"}),c(l,{label:"云通",value:"yuntong"}),c(l,{label:"快递100",value:"kuaidi100"})])),_:1},8,["modelValue"])])),_:1}),c(u,{label:"快递公司",required:""},{default:o((()=>[c(s,{modelValue:A.express_codes,"onUpdate:modelValue":a[1]||(a[1]=e=>A.express_codes=e),placeholder:"请选择快递公司",multiple:"",style:{width:"100%"}},{default:o((()=>[c(l,{label:"圆通速递",value:"YTO"}),c(l,{label:"申通快递",value:"STO"}),c(l,{label:"韵达速递",value:"YD"}),c(l,{label:"中通快递",value:"ZTO"}),c(l,{label:"极兔速递",value:"JT"}),c(l,{label:"京东物流",value:"JD"})])),_:1},8,["modelValue"])])),_:1}),J.value?(i(),d(u,{key:0,label:"任务进度"},{default:o((()=>{return[g("div",T,[g("div",q,[g("span",j,h(J.value.task_name||"更新任务"),1),g("span",P,h((e=J.value.status,{pending:"等待中",running:"运行中",completed:"已完成",failed:"已失败",cancelled:"已取消"}[e]||e)),1)]),c(t,{percentage:J.value.progress||0,"stroke-width":8},null,8,["percentage"]),g("div",D,[g("span",null,"总查询: "+h(J.value.total_queries||0),1),g("span",null,"已完成: "+h(J.value.completed_queries||0),1),g("span",null,"成功: "+h(J.value.success_queries||0),1),g("span",null,"失败: "+h(J.value.failed_queries||0),1)])])];var e})),_:1})):m("",!0)])),_:1},8,["model"])])),[[S,C.value]])])),_:1},8,["modelValue"])}}}),[["__scopeId","data-v-3af3effe"]]);export{O as default};
