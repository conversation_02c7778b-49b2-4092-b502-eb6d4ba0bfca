import s from"./CardList-Bmc_RYrb.js";import t from"./ActiveUser-D3LDNYKD.js";import o from"./SalesOverview-Dd4TbsyA.js";import i from"./NewUser-CcQc9v4h.js";import a from"./Dynamic-BIhFwANX.js";import{u as n,S as r,s as c,_ as l}from"./index-rNRt1EuS.js";import e from"./TodoList-jr41Y50H.js";import{d as m,j as p,p as d,c as u,b as h,e as g,x as j,u as v,o as w}from"./vendor-CAPBtMef.js";import"./vue3-count-to.esm-Df4TYMKb.js";import"./index-DZFhRe5N.js";import"./install-C7WOJgjY.js";import"./useECharts-NfF4QADV.js";/* empty css                    *//* empty css                       */const f={class:"console"},b={class:"column column2"},y={class:"column column3"},k={class:"bottom-wrap art-custom-card"},C={class:"button-wrap"},x=l(m({__name:"index",setup(l){const m=n(),x=p((()=>m.systemThemeType));d(x,(()=>{m.reload()}));const _=r.name;c();const T=s=>{window.open(s)};return(n,r)=>(w(),u("div",f,[h(s),g("div",b,[h(t),h(o)]),g("div",y,[h(i),h(a),h(e)]),g("div",k,[g("div",null,[r[8]||(r[8]=g("h2",{class:"box-title"},"关于项目",-1)),g("p",null,j(v(_))+" 是一款专注于用户体验和视觉设计的后台管理系统模版",1),r[9]||(r[9]=g("p",null,"使用了 Vue3、TypeScript、Vite、Element Plus 等前沿技术",-1)),g("div",C,[g("div",{class:"btn art-custom-card",onClick:r[0]||(r[0]=s=>T("https://www.lingchen.kim/art-design-pro/docs/"))},r[4]||(r[4]=[g("span",null,"项目官网",-1),g("i",{class:"iconfont-sys"},"",-1)])),g("div",{class:"btn art-custom-card",onClick:r[1]||(r[1]=s=>T("https://www.lingchen.kim/art-design-pro/docs/guide/introduce.html"))},r[5]||(r[5]=[g("span",null,"文档",-1),g("i",{class:"iconfont-sys"},"",-1)])),g("div",{class:"btn art-custom-card",onClick:r[2]||(r[2]=s=>T("https://github.com/Daymychen"))},r[6]||(r[6]=[g("span",null,"Github",-1),g("i",{class:"iconfont-sys"},"",-1)])),g("div",{class:"btn art-custom-card",onClick:r[3]||(r[3]=s=>T("https://www.lingchen.kim"))},r[7]||(r[7]=[g("span",null,"博客",-1),g("i",{class:"iconfont-sys"},"",-1)]))])]),r[10]||(r[10]=g("img",{class:"right-img",src:"/art-design-pro/assets/draw1-Ce1WF34i.png"},null,-1))])]))}}),[["__scopeId","data-v-61492447"]]);export{x as default};
