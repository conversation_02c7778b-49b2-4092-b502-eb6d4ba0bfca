var e=(e,l,a)=>new Promise(((s,t)=>{var r=e=>{try{i(a.next(e))}catch(l){t(l)}},u=e=>{try{i(a.throw(e))}catch(l){t(l)}},i=e=>e.done?s(e.value):Promise.resolve(e.value).then(r,u);i((a=a.apply(e,l)).next())}));import{_ as l}from"./index-rNRt1EuS.js";/* empty css                 *//* empty css                    */import{P as a,E as s,a as t,F as r}from"./flexiblePriceTableApi-BBM6dCg8.js";import{j as u,k as i,a as o}from"./priceTable-dC3_lJoq.js";import{d as n,r as d,S as v,X as c,j as p,p as m,N as f,o as _,w as g,a2 as b,Z as h,b as y,a as x,_ as k,e as V,ar as w,c as j,F as q,A as T,u as U,as as P,x as B,v as $,b8 as I,a$ as M,aG as C,H as S,K as A,Y as D,ai as F,M as H}from"./vendor-CAPBtMef.js";const E={class:"estimate-info"},G={class:"estimate-item"},K={class:"value"},L={class:"estimate-item"},N={class:"value"},O={class:"estimate-item"},R={class:"value highlight"},X={class:"estimate-item"},Y={class:"value"},Z={class:"task-progress"},z={class:"progress-header"},J={class:"task-name"},Q={class:"progress-details"},W={class:"detail-item"},ee={class:"detail-item"},le={class:"detail-item"},ae={class:"detail-item"},se={key:0,class:"error-message"},te={class:"dialog-footer"},re=l(n({__name:"UpdatePriceDialog",props:{visible:{type:Boolean}},emits:["update:visible","success"],setup(l,{emit:n}){const re=l,ue=n,ie=d(),oe=d(!1),ne=v(null),de=d(null),ve=c({providers:[],express_codes:[],from_province:void 0,to_province:void 0}),ce={providers:[{required:!0,message:"请选择供应商",trigger:"change"}],express_codes:[{required:!0,message:"请选择快递公司",trigger:"change"}]},pe=p({get:()=>re.visible,set:e=>ue("update:visible",e)}),me=p((()=>(ve.providers.length||0)*(ve.express_codes.length||0)*(ve.from_province&&ve.to_province?1:961))),fe=p((()=>u(me.value))),_e=p((()=>ve.providers.length>0&&ve.express_codes.length>0&&!oe.value)),ge=p((()=>null!==ne.value)),be=p((()=>{var e;return(null==(e=ne.value)?void 0:e.task_name)||"更新任务"})),he=p((()=>{var e;const l=(null==(e=ne.value)?void 0:e.status)||"pending";return i(l)})),ye=p((()=>{var e;const l=(null==(e=ne.value)?void 0:e.status)||"pending";return Me(l)})),xe=p((()=>{var e;const l=(null==(e=ne.value)?void 0:e.status)||"pending";return Ce(l)})),ke=p((()=>{var e;return(null==(e=ne.value)?void 0:e.progress)||0})),Ve=p((()=>{var e;return(null==(e=ne.value)?void 0:e.total_queries)||0})),we=p((()=>{var e;return(null==(e=ne.value)?void 0:e.completed_queries)||0})),je=p((()=>{var e;return(null==(e=ne.value)?void 0:e.success_queries)||0})),qe=p((()=>{var e;return(null==(e=ne.value)?void 0:e.failed_queries)||0})),Te=p((()=>{var e;return(null==(e=ne.value)?void 0:e.error_message)||""}));m((()=>re.visible),(e=>{e?Ue():Ie()}));const Ue=()=>{Object.assign(ve,{providers:[],express_codes:[],from_province:void 0,to_province:void 0}),ne.value=null,Ie(),ie.value&&ie.value.clearValidate()},Pe=()=>{oe.value?F.confirm("更新任务正在进行中，确定要关闭吗？","确认关闭",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{Ie(),ue("update:visible",!1)})).catch((()=>{})):ue("update:visible",!1)},Be=()=>e(this,null,(function*(){if(ie.value)try{yield ie.value.validate();const e=`\n      即将更新价格数据：\n      • 供应商: ${ve.providers.map((e=>o(e))).join(", ")}\n      • 快递公司: ${ve.express_codes.map((e=>Se(e))).join(", ")}\n      • 预估查询数: ${me.value}\n      • 预估时间: ${fe.value}\n      \n      确定要开始更新吗？\n    `;yield F.confirm(e,"确认更新",{confirmButtonText:"开始更新",cancelButtonText:"取消",type:"info",dangerouslyUseHTMLString:!0}),oe.value=!0;const l=yield r.updatePriceData(ve);l.success?(ne.value=l.data,H.success("更新任务已启动"),$e()):(H.error(l.message||"启动更新任务失败"),oe.value=!1)}catch(e){"cancel"!==e&&(H.error("提交失败"),oe.value=!1)}})),$e=()=>{de.value&&clearInterval(de.value);let l=!1;de.value=setInterval((()=>e(this,null,(function*(){if(ne.value&&!l){l=!0;try{const e=yield r.getTaskStatus(ne.value.id);if(e.success&&e.data){const l=e.data;(!ne.value||ne.value.status!==l.status||Math.abs((ne.value.progress||0)-(l.progress||0))>=1||Math.abs((ne.value.completed_queries||0)-(l.completed_queries||0))>=10)&&(ne.value=l),"completed"===l.status?(H.success("价格数据更新完成"),oe.value=!1,Ie(),ue("success"),setTimeout((()=>{ue("update:visible",!1)}),2e3)):"failed"===l.status&&(H.error("价格数据更新失败: "+(l.error_message||"未知错误")),oe.value=!1,Ie())}else H.warning("任务状态获取失败，可能任务已完成"),Ie(),oe.value=!1}catch(e){H.error("获取任务状态失败"),Ie(),oe.value=!1}finally{l=!1}}}))),3e3)},Ie=()=>{de.value&&(clearInterval(de.value),de.value=null)},Me=e=>{try{return{pending:"status-pending",running:"status-running",completed:"status-completed",failed:"status-failed",cancelled:"status-cancelled"}[e]||"status-pending"}catch(l){return"status-pending"}},Ce=e=>{try{return{completed:"success",failed:"exception",cancelled:"exception"}[e]||""}catch(l){return""}},Se=e=>{try{if(!e)return"未知快递公司";const l=s.find((l=>l.value===e));return(null==l?void 0:l.label)||e}catch(l){return e||"未知快递公司"}};return(e,l)=>{const r=P,u=w,i=k,o=I,n=M,d=h,v=S,c=D,p=C;return _(),f(c,{modelValue:pe.value,"onUpdate:modelValue":l[4]||(l[4]=e=>pe.value=e),title:"更新价格数据",width:"600px","close-on-click-modal":!1,"close-on-press-escape":!1,onClose:Pe},{footer:g((()=>[V("div",te,[y(v,{onClick:Pe,disabled:oe.value},{default:g((()=>l[13]||(l[13]=[A("取消")]))),_:1},8,["disabled"]),y(v,{type:"primary",onClick:Be,loading:oe.value,disabled:!_e.value},{default:g((()=>[A(B(oe.value?"更新中...":"开始更新"),1)])),_:1},8,["loading","disabled"])])])),default:g((()=>[b((_(),f(d,{ref_key:"formRef",ref:ie,model:ve,rules:ce,"label-width":"100px"},{default:g((()=>[y(i,{label:"供应商",prop:"providers",required:""},{default:g((()=>[y(u,{modelValue:ve.providers,"onUpdate:modelValue":l[0]||(l[0]=e=>ve.providers=e),placeholder:"请选择供应商",multiple:"",style:{width:"100%"}},{default:g((()=>[(_(!0),j(q,null,T(U(a),(e=>(_(),f(r,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"]),l[5]||(l[5]=V("div",{class:"form-tip"},"可选择多个供应商同时更新",-1))])),_:1}),y(i,{label:"快递公司",prop:"express_codes",required:""},{default:g((()=>[y(u,{modelValue:ve.express_codes,"onUpdate:modelValue":l[1]||(l[1]=e=>ve.express_codes=e),placeholder:"请选择快递公司",multiple:"",style:{width:"100%"}},{default:g((()=>[(_(!0),j(q,null,T(U(s),(e=>(_(),f(r,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"]),l[6]||(l[6]=V("div",{class:"form-tip"},"可选择多个快递公司同时更新",-1))])),_:1}),y(i,{label:"发货省份",prop:"from_province"},{default:g((()=>[y(u,{modelValue:ve.from_province,"onUpdate:modelValue":l[2]||(l[2]=e=>ve.from_province=e),placeholder:"请选择发货省份（可选）",clearable:"",style:{width:"100%"}},{default:g((()=>[(_(!0),j(q,null,T(U(t),(e=>(_(),f(r,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"]),l[7]||(l[7]=V("div",{class:"form-tip"},"留空则更新所有省份路线",-1))])),_:1}),y(i,{label:"收货省份",prop:"to_province"},{default:g((()=>[y(u,{modelValue:ve.to_province,"onUpdate:modelValue":l[3]||(l[3]=e=>ve.to_province=e),placeholder:"请选择收货省份（可选）",clearable:"",style:{width:"100%"}},{default:g((()=>[(_(!0),j(q,null,T(U(t),(e=>(_(),f(r,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"]),l[8]||(l[8]=V("div",{class:"form-tip"},"留空则更新所有省份路线",-1))])),_:1}),y(i,{label:"预估查询"},{default:g((()=>[V("div",E,[V("div",G,[l[9]||(l[9]=V("span",{class:"label"},"供应商数量:",-1)),V("span",K,B(ve.providers.length),1)]),V("div",L,[l[10]||(l[10]=V("span",{class:"label"},"快递公司数量:",-1)),V("span",N,B(ve.express_codes.length),1)]),V("div",O,[l[11]||(l[11]=V("span",{class:"label"},"预估查询数:",-1)),V("span",R,B(me.value),1)]),V("div",X,[l[12]||(l[12]=V("span",{class:"label"},"预估时间:",-1)),V("span",Y,B(fe.value),1)])])])),_:1}),ge.value?(_(),f(i,{key:0,label:"任务进度"},{default:g((()=>[V("div",Z,[V("div",z,[V("span",J,B(be.value),1),V("span",{class:$(["task-status",ye.value])},B(he.value),3)]),y(o,{percentage:ke.value,status:xe.value,"stroke-width":8},null,8,["percentage","status"]),V("div",Q,[V("div",W,[V("span",null,"总查询: "+B(Ve.value),1)]),V("div",ee,[V("span",null,"已完成: "+B(we.value),1)]),V("div",le,[V("span",null,"成功: "+B(je.value),1)]),V("div",ae,[V("span",null,"失败: "+B(qe.value),1)])]),Te.value?(_(),j("div",se,[y(n,{title:Te.value,type:"error",closable:!1,"show-icon":""},null,8,["title"])])):x("",!0)])])),_:1})):x("",!0)])),_:1},8,["model"])),[[p,oe.value]])])),_:1},8,["modelValue"])}}}),[["__scopeId","data-v-ed2d6e22"]]);export{re as default};
