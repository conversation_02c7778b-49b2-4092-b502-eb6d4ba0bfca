var e=Object.defineProperty,a=Object.defineProperties,l=Object.getOwnPropertyDescriptors,o=Object.getOwnPropertySymbols,i=Object.prototype.hasOwnProperty,t=Object.prototype.propertyIsEnumerable,s=(a,l,o)=>l in a?e(a,l,{enumerable:!0,configurable:!0,writable:!0,value:o}):a[l]=o,n=(e,a)=>{for(var l in a||(a={}))i.call(a,l)&&s(e,l,a[l]);if(o)for(var l of o(a))t.call(a,l)&&s(e,l,a[l]);return e},r=(e,o)=>a(e,l(o)),c=(e,a,l)=>new Promise(((o,i)=>{var t=e=>{try{n(l.next(e))}catch(a){i(a)}},s=e=>{try{n(l.throw(e))}catch(a){i(a)}},n=e=>e.done?o(e.value):Promise.resolve(e.value).then(t,s);n((l=l.apply(e,a)).next())}));import{_ as d}from"./index-rNRt1EuS.js";import{d as u,r as p,X as g,j as f,f as v,M as _,c as y,o as m,e as b,b as h,w,K as k,u as C,aR as j,H as z,aQ as x,aS as U,aE as V,y as O,an as S,ah as D,aj as B,ag as P,be as T,bH as I,bI as L,_ as R,ar as $,F as E,A,N as H,as as J,C as N,D as G,a3 as K,aB as Q,Z as X,a2 as q,aG as F,aJ as M,aX as Z,x as W,ap as Y,aI as ee,aH as ae,ai as le}from"./vendor-CAPBtMef.js";import{S as oe,C as ie}from"./systemConfigApi-Cw3xPQeV.js";import te from"./ConfigEditDialog-Cc2V-jpf.js";import se from"./JsonViewDialog-h0pOttTR.js";import ne from"./ChangeLogDialog-C5jZqRzN.js";import re from"./TemplateDialog-XM7svtpU.js";import ce from"./BackupDialog-DeytXXHo.js";import de from"./BatchUpdateDialog-BsF508NG.js";/* empty css                 *//* empty css                       *//* empty css                 *//* empty css                             *//* empty css                       */const ue={class:"system-config-container"},pe={class:"toolbar"},ge={class:"toolbar-left"},fe={class:"toolbar-right"},ve={class:"filter-section"},_e={class:"table-section"},ye={class:"config-value"},me={key:0},be={key:1},he={key:2,class:"value-text"},we={class:"pagination-wrapper"},ke=d(u({__name:"index",setup(e){const a=p(!1),l=p(!1),o=p([]),i=p([]),t=p([]),s=g({page:1,page_size:20,config_group:"",keyword:"",config_type:void 0,is_active:void 0,order_by:"display_order",order:"asc"}),d=g({page:1,page_size:20,total:0,total_pages:0}),u=p(!1),ke=p(!1),Ce=p(!1),je=p(!1),ze=p(!1),xe=p(!1),Ue=p(null),Ve=p("");f((()=>i.value.length>0)),v((()=>{Oe(),Se()}));const Oe=()=>c(this,null,(function*(){try{a.value=!0;const e=yield oe.getConfigList(r(n({},s),{page:d.page,page_size:d.page_size}));e.success?(o.value=e.data.configs,d.total=e.data.total,d.total_pages=e.data.total_pages):_.error(e.message||"获取配置列表失败")}catch(e){_.error("获取配置列表失败")}finally{a.value=!1}})),Se=()=>c(this,null,(function*(){try{const e=yield oe.getConfigGroups();e.success&&(t.value=e.data)}catch(e){}})),De=()=>{d.page=1,Oe()},Be=()=>{Object.assign(s,{page:1,page_size:20,config_group:"",keyword:"",config_type:void 0,is_active:void 0,order_by:"display_order",order:"asc"}),d.page=1,Oe()},Pe=e=>{d.page=e,Oe()},Te=e=>{d.page_size=e,d.page=1,Oe()},Ie=({prop:e,order:a})=>{s.order_by=e,s.order="ascending"===a?"asc":"desc",Oe()},Le=e=>{i.value=e},Re=()=>{Ue.value=null,u.value=!0},$e=()=>{Oe()},Ee=e=>c(this,null,(function*(){try{yield le.confirm(`确认删除配置 "${e.config_key}" 吗？此操作不可撤销。`,"确认删除",{type:"warning",confirmButtonText:"确认",cancelButtonText:"取消"});const a=yield oe.deleteConfig(e.id);a.success?(_.success("删除成功"),Oe()):_.error(a.message||"删除失败")}catch(a){"cancel"!==a&&_.error("删除失败")}})),Ae=e=>c(this,null,(function*(){try{const a=yield oe.updateConfig(e.id,{config_group:e.config_group,config_key:e.config_key,config_value:e.config_value,config_type:e.config_type,description:e.description,validation_rule:e.validation_rule,default_value:e.default_value,display_order:e.display_order,change_reason:(e.is_active?"启用":"禁用")+"配置"});a.success?_.success((e.is_active?"启用":"禁用")+"成功"):(e.is_active=!e.is_active,_.error(a.message||"操作失败"))}catch(a){e.is_active=!e.is_active,_.error("操作失败")}})),He=()=>c(this,null,(function*(){try{l.value=!0;const e=yield oe.refreshCache();e.success?_.success("缓存刷新成功"):_.error(e.message||"缓存刷新失败")}catch(e){_.error("缓存刷新失败")}finally{l.value=!1}})),Je=()=>{ze.value=!0},Ne=e=>{switch(e){case"batchUpdate":xe.value=!0;break;case"batchDelete":Ge();break;case"export":Ke()}},Ge=()=>c(this,null,(function*(){try{yield le.confirm(`确认删除选中的 ${i.value.length} 个配置吗？此操作不可撤销。`,"确认批量删除",{type:"warning",confirmButtonText:"确认",cancelButtonText:"取消"});const e=i.value.map((e=>oe.deleteConfig(e.id)));yield Promise.all(e),_.success("批量删除成功"),Oe()}catch(e){"cancel"!==e&&_.error("批量删除失败")}})),Ke=()=>{const e=i.value.map((e=>({"配置组":e.config_group,"配置键":e.config_key,"配置值":e.config_value,"配置类型":Fe(e.config_type),"描述":e.description,"默认值":e.default_value,"排序":e.display_order,"状态":e.is_active?"启用":"禁用","更新时间":Me(e.updated_at)}))),a=[Object.keys(e[0]).join(","),...e.map((e=>Object.values(e).join(",")))].join("\n"),l=new Blob([a],{type:"text/csv;charset=utf-8;"}),o=document.createElement("a");o.href=URL.createObjectURL(l),o.download=`system_configs_${(new Date).toISOString().slice(0,10)}.csv`,o.click()},Qe=()=>{Oe()},Xe=()=>{Oe()},qe=()=>{Oe()},Fe=e=>{const a=ie.find((a=>a.value===e));return(null==a?void 0:a.label)||e},Me=e=>e?new Date(e).toLocaleString("zh-CN"):"-";return(e,c)=>{const p=z,g=O,f=B,v=D,_=P,le=J,oe=$,Oe=R,Se=N,Ge=X,Ke=M,Ze=Z,We=Y,Ye=ee,ea=ae,aa=F;return m(),y("div",ue,[c[31]||(c[31]=b("div",{class:"page-header"},[b("h2",{class:"page-title"},"系统配置管理"),b("p",{class:"page-description"},"管理系统运行时配置参数，支持分组管理、版本控制和模板应用")],-1)),b("div",pe,[b("div",ge,[h(p,{type:"primary",onClick:Re,icon:C(j)},{default:w((()=>c[15]||(c[15]=[k(" 新增配置 ")]))),_:1},8,["icon"]),h(p,{onClick:He,icon:C(x),loading:l.value},{default:w((()=>c[16]||(c[16]=[k(" 刷新缓存 ")]))),_:1},8,["icon","loading"]),h(p,{onClick:Je,icon:C(U)},{default:w((()=>c[17]||(c[17]=[k(" 创建备份 ")]))),_:1},8,["icon"]),h(_,{onCommand:Ne},{dropdown:w((()=>[h(v,null,{default:w((()=>[h(f,{command:"batchUpdate",disabled:0===i.value.length},{default:w((()=>c[19]||(c[19]=[k(" 批量更新 ")]))),_:1},8,["disabled"]),h(f,{command:"batchDelete",disabled:0===i.value.length},{default:w((()=>c[20]||(c[20]=[k(" 批量删除 ")]))),_:1},8,["disabled"]),h(f,{command:"export",disabled:0===i.value.length},{default:w((()=>c[21]||(c[21]=[k(" 导出配置 ")]))),_:1},8,["disabled"])])),_:1})])),default:w((()=>[h(p,{icon:C(V)},{default:w((()=>[c[18]||(c[18]=k(" 批量操作")),h(g,{class:"el-icon--right"},{default:w((()=>[h(C(S))])),_:1})])),_:1},8,["icon"])])),_:1})]),b("div",fe,[h(p,{onClick:c[0]||(c[0]=e=>Ce.value=!0),icon:C(T)},{default:w((()=>c[22]||(c[22]=[k(" 变更日志 ")]))),_:1},8,["icon"]),h(p,{onClick:c[1]||(c[1]=e=>je.value=!0),icon:C(I)},{default:w((()=>c[23]||(c[23]=[k(" 配置模板 ")]))),_:1},8,["icon"]),h(p,{onClick:c[2]||(c[2]=e=>ze.value=!0),icon:C(L)},{default:w((()=>c[24]||(c[24]=[k(" 配置备份 ")]))),_:1},8,["icon"])])]),b("div",ve,[h(Ge,{model:s,inline:"",class:"search-form"},{default:w((()=>[h(Oe,{label:"配置组"},{default:w((()=>[h(oe,{modelValue:s.config_group,"onUpdate:modelValue":c[3]||(c[3]=e=>s.config_group=e),placeholder:"选择配置组",clearable:"",style:{width:"180px"},onChange:De},{default:w((()=>[(m(!0),y(E,null,A(t.value,(e=>(m(),H(le,{key:e,label:e,value:e},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),h(Oe,{label:"配置类型"},{default:w((()=>[h(oe,{modelValue:s.config_type,"onUpdate:modelValue":c[4]||(c[4]=e=>s.config_type=e),placeholder:"选择配置类型",clearable:"",style:{width:"150px"},onChange:De},{default:w((()=>[(m(!0),y(E,null,A(C(ie),(e=>(m(),H(le,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),h(Oe,{label:"状态"},{default:w((()=>[h(oe,{modelValue:s.is_active,"onUpdate:modelValue":c[5]||(c[5]=e=>s.is_active=e),placeholder:"选择状态",clearable:"",style:{width:"120px"},onChange:De},{default:w((()=>[h(le,{label:"启用",value:!0}),h(le,{label:"禁用",value:!1})])),_:1},8,["modelValue"])])),_:1}),h(Oe,{label:"关键词"},{default:w((()=>[h(Se,{modelValue:s.keyword,"onUpdate:modelValue":c[6]||(c[6]=e=>s.keyword=e),placeholder:"搜索配置键或描述",clearable:"",style:{width:"200px"},onKeyup:G(De,["enter"]),onClear:De},{append:w((()=>[h(p,{icon:C(K),onClick:De},null,8,["icon"])])),_:1},8,["modelValue"])])),_:1}),h(Oe,null,{default:w((()=>[h(p,{onClick:Be,icon:C(Q)},{default:w((()=>c[25]||(c[25]=[k("重置")]))),_:1},8,["icon"])])),_:1})])),_:1},8,["model"])]),b("div",_e,[q((m(),H(Ye,{data:o.value,onSelectionChange:Le,onSortChange:Ie,stripe:"",style:{width:"100%"}},{default:w((()=>[h(Ke,{type:"selection",width:"55"}),h(Ke,{prop:"config_group",label:"配置组",width:"120",sortable:"custom"},{default:w((({row:e})=>[h(Ze,{type:"info",size:"small"},{default:w((()=>[k(W(e.config_group),1)])),_:2},1024)])),_:1}),h(Ke,{prop:"config_key",label:"配置键",width:"200",sortable:"custom","show-overflow-tooltip":""}),h(Ke,{prop:"config_value",label:"配置值","min-width":"200","show-overflow-tooltip":""},{default:w((({row:e})=>[b("div",ye,["boolean"===e.config_type?(m(),y("span",me,[h(Ze,{type:"true"===e.config_value?"success":"danger",size:"small"},{default:w((()=>[k(W("true"===e.config_value?"是":"否"),1)])),_:2},1032,["type"])])):"json"===e.config_type?(m(),y("span",be,[h(p,{type:"primary",link:"",size:"small",onClick:a=>{return l=e.config_value,Ve.value=l,void(ke.value=!0);var l}},{default:w((()=>c[26]||(c[26]=[k(" 查看JSON ")]))),_:2},1032,["onClick"])])):(m(),y("span",he,W(e.config_value),1))])])),_:1}),h(Ke,{prop:"config_type",label:"类型",width:"100"},{default:w((({row:e})=>{return[h(Ze,{type:(a=e.config_type,{string:"primary",integer:"success",float:"warning",boolean:"info",json:"danger",array:"default"}[a]||"default"),size:"small"},{default:w((()=>[k(W(Fe(e.config_type)),1)])),_:2},1032,["type"])];var a})),_:1}),h(Ke,{prop:"description",label:"描述","min-width":"150","show-overflow-tooltip":""}),h(Ke,{prop:"display_order",label:"排序",width:"80",sortable:"custom"}),h(Ke,{prop:"is_active",label:"状态",width:"80"},{default:w((({row:e})=>[h(We,{modelValue:e.is_active,"onUpdate:modelValue":a=>e.is_active=a,onChange:a=>Ae(e),disabled:e.is_readonly},null,8,["modelValue","onUpdate:modelValue","onChange","disabled"])])),_:1}),h(Ke,{prop:"updated_at",label:"更新时间",width:"160",sortable:"custom"},{default:w((({row:e})=>[k(W(Me(e.updated_at)),1)])),_:1}),h(Ke,{label:"操作",width:"200",fixed:"right"},{default:w((({row:e})=>[h(p,{type:"primary",link:"",size:"small",onClick:a=>{return l=e,Ue.value=n({},l),void(u.value=!0);var l}},{default:w((()=>c[27]||(c[27]=[k(" 编辑 ")]))),_:2},1032,["onClick"]),h(p,{type:"primary",link:"",size:"small",onClick:e=>{Ce.value=!0}},{default:w((()=>c[28]||(c[28]=[k(" 历史 ")]))),_:2},1032,["onClick"]),h(p,{type:"primary",link:"",size:"small",onClick:a=>(e=>{const a=r(n({},e),{config_key:`${e.config_key}_copy`,description:`${e.description} (副本)`});delete a.id,delete a.created_at,delete a.updated_at,Ue.value=a,u.value=!0})(e)},{default:w((()=>c[29]||(c[29]=[k(" 复制 ")]))),_:2},1032,["onClick"]),h(p,{type:"danger",link:"",size:"small",onClick:a=>Ee(e),disabled:e.is_readonly},{default:w((()=>c[30]||(c[30]=[k(" 删除 ")]))),_:2},1032,["onClick","disabled"])])),_:1})])),_:1},8,["data"])),[[aa,a.value]]),b("div",we,[h(ea,{"current-page":d.page,"onUpdate:currentPage":c[7]||(c[7]=e=>d.page=e),"page-size":d.page_size,"onUpdate:pageSize":c[8]||(c[8]=e=>d.page_size=e),total:d.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:Te,onCurrentChange:Pe},null,8,["current-page","page-size","total"])])]),h(te,{visible:u.value,"onUpdate:visible":c[9]||(c[9]=e=>u.value=e),config:Ue.value,"config-groups":t.value,onSuccess:$e},null,8,["visible","config","config-groups"]),h(se,{visible:ke.value,"onUpdate:visible":c[10]||(c[10]=e=>ke.value=e),"json-data":Ve.value},null,8,["visible","json-data"]),h(ne,{visible:Ce.value,"onUpdate:visible":c[11]||(c[11]=e=>Ce.value=e)},null,8,["visible"]),h(re,{visible:je.value,"onUpdate:visible":c[12]||(c[12]=e=>je.value=e),onApplyTemplate:Xe},null,8,["visible"]),h(ce,{visible:ze.value,"onUpdate:visible":c[13]||(c[13]=e=>ze.value=e),onRestoreBackup:qe},null,8,["visible"]),h(de,{visible:xe.value,"onUpdate:visible":c[14]||(c[14]=e=>xe.value=e),configs:i.value,onSuccess:Qe},null,8,["visible","configs"])])}}}),[["__scopeId","data-v-7f6b7ab4"]]);export{ke as default};
