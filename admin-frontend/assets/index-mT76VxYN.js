var e=(e,a,l)=>new Promise(((s,t)=>{var r=e=>{try{d(l.next(e))}catch(a){t(a)}},o=e=>{try{d(l.throw(e))}catch(a){t(a)}},d=e=>e.done?s(e.value):Promise.resolve(e.value).then(r,o);d((l=l.apply(e,a)).next())}));import{_ as a}from"./index-rNRt1EuS.js";/* empty css                             *//* empty css                */import{d as l,r as s,X as t,f as r,c as o,o as d,e as i,b as u,w as n,K as c,u as p,aR as m,H as _,Z as y,_ as v,C as b,D as f,ar as g,as as h,aV as w,a2 as V,N as z,aI as x,aJ as k,a as C,aX as U,x as j,aG as P,aH as q,ap as O,Y as S,b1 as B,b2 as D,M as H,ai as I}from"./vendor-CAPBtMef.js";import{P as K}from"./rolePermissionApi-CKaU4rQM.js";const L={class:"permission-management"},N={class:"page-header"},R={class:"header-right"},T={class:"permission-info"},X={class:"permission-name"},A={class:"pagination-wrapper"},F={key:0,class:"permission-detail"},G=a(l({__name:"index",setup(a){const l=s(!1),G=s(!1),J=s([]),M=s(null),Y=t({keyword:"",resource:"",action:"",is_system:"",sort_by:"created_at",sort_order:"desc"}),Z=t({page:1,page_size:20,total:0}),$=s(!1),E=s(!1),Q=s(!1),W=s(),ee=t({name:"",description:"",resource:"",action:"",is_system:!1}),ae={name:[{required:!0,message:"请输入权限名称",trigger:"blur"},{min:2,max:100,message:"权限名称长度在 2 到 100 个字符",trigger:"blur"}],resource:[{required:!0,message:"请选择资源类型",trigger:"change"}],action:[{required:!0,message:"请选择操作类型",trigger:"change"}]},le=e=>e?new Date(e).toLocaleString("zh-CN"):"-",se=e=>({create:"success",read:"info",update:"warning",delete:"danger"}[e]||"info"),te=e=>({create:"创建",read:"读取",update:"更新",delete:"删除"}[e]||e),re=()=>e(this,null,(function*(){try{l.value=!0;const e=yield K.getPermissionList({page:Z.page,page_size:Z.page_size,keyword:Y.keyword,resource:Y.resource,action:Y.action,is_system:""===Y.is_system?void 0:Y.is_system,order_by:Y.sort_by,order:Y.sort_order});J.value=e.items,Z.total=e.total,Z.page=e.page,Z.page_size=e.page_size}catch(e){H.error(e.message||"加载权限列表失败")}finally{l.value=!1}})),oe=()=>{Z.page=1,re()},de=()=>{Object.assign(Y,{keyword:"",resource:"",action:"",is_system:"",sort_by:"created_at",sort_order:"desc"}),oe()},ie=e=>{Z.page_size=e,Z.page=1,re()},ue=e=>{Z.page=e,re()},ne=()=>{Q.value=!0,Object.assign(ee,{name:"",description:"",resource:"",action:"",is_system:!1}),$.value=!0},ce=a=>e(this,null,(function*(){try{yield I.confirm(`确定要删除权限 "${a.name}" 吗？此操作不可恢复。`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),yield K.deletePermission(a.id),H.success("权限删除成功"),yield re()}catch(e){"cancel"!==e&&H.error(e.message||"删除权限失败")}})),pe=()=>e(this,null,(function*(){try{if(!(yield W.value.validate()))return;if(G.value=!0,Q.value){const e={name:ee.name,description:ee.description,resource:ee.resource,action:ee.action,is_system:ee.is_system};yield K.createPermission(e),H.success("权限创建成功")}else{const e={name:ee.name,description:ee.description,resource:ee.resource,action:ee.action,is_system:ee.is_system};yield K.updatePermission(M.value.id,e),H.success("权限更新成功")}$.value=!1,yield re()}catch(e){H.error(e.message||"保存权限失败")}finally{G.value=!1}}));return r((()=>{re()})),(e,a)=>{const s=_,t=b,r=v,H=h,I=g,K=y,re=w,me=U,_e=k,ye=x,ve=q,be=O,fe=S,ge=D,he=B,we=P;return d(),o("div",L,[i("div",N,[a[17]||(a[17]=i("div",{class:"header-left"},[i("h1",{class:"page-title"},"权限管理"),i("p",{class:"page-description"},"管理系统中的所有权限和资源访问控制")],-1)),i("div",R,[u(s,{type:"primary",icon:p(m),onClick:ne},{default:n((()=>a[16]||(a[16]=[c(" 新增权限 ")]))),_:1},8,["icon"])])]),u(re,{class:"search-card"},{default:n((()=>[u(K,{model:Y,inline:""},{default:n((()=>[u(r,{label:"权限名称"},{default:n((()=>[u(t,{modelValue:Y.keyword,"onUpdate:modelValue":a[0]||(a[0]=e=>Y.keyword=e),placeholder:"请输入权限名称",clearable:"",style:{width:"200px"},onKeyup:f(oe,["enter"])},null,8,["modelValue"])])),_:1}),u(r,{label:"资源类型"},{default:n((()=>[u(I,{modelValue:Y.resource,"onUpdate:modelValue":a[1]||(a[1]=e=>Y.resource=e),placeholder:"请选择资源类型",clearable:"",style:{width:"150px"}},{default:n((()=>[u(H,{label:"全部",value:""}),u(H,{label:"用户管理",value:"user"}),u(H,{label:"角色管理",value:"role"}),u(H,{label:"权限管理",value:"permission"}),u(H,{label:"订单管理",value:"order"}),u(H,{label:"余额管理",value:"balance"}),u(H,{label:"系统配置",value:"system"}),u(H,{label:"快递公司",value:"express_company"}),u(H,{label:"快递价格",value:"express_price"})])),_:1},8,["modelValue"])])),_:1}),u(r,{label:"操作类型"},{default:n((()=>[u(I,{modelValue:Y.action,"onUpdate:modelValue":a[2]||(a[2]=e=>Y.action=e),placeholder:"请选择操作类型",clearable:"",style:{width:"120px"}},{default:n((()=>[u(H,{label:"全部",value:""}),u(H,{label:"创建",value:"create"}),u(H,{label:"读取",value:"read"}),u(H,{label:"更新",value:"update"}),u(H,{label:"删除",value:"delete"})])),_:1},8,["modelValue"])])),_:1}),u(r,{label:"权限类型"},{default:n((()=>[u(I,{modelValue:Y.is_system,"onUpdate:modelValue":a[3]||(a[3]=e=>Y.is_system=e),placeholder:"请选择权限类型",clearable:"",style:{width:"150px"}},{default:n((()=>[u(H,{label:"全部",value:""}),u(H,{label:"系统权限",value:!0}),u(H,{label:"自定义权限",value:!1})])),_:1},8,["modelValue"])])),_:1}),u(r,{label:"排序方式"},{default:n((()=>[u(I,{modelValue:Y.sort_by,"onUpdate:modelValue":a[4]||(a[4]=e=>Y.sort_by=e),style:{width:"120px"}},{default:n((()=>[u(H,{label:"创建时间",value:"created_at"}),u(H,{label:"更新时间",value:"updated_at"}),u(H,{label:"权限名称",value:"name"}),u(H,{label:"资源类型",value:"resource"})])),_:1},8,["modelValue"])])),_:1}),u(r,{label:"排序"},{default:n((()=>[u(I,{modelValue:Y.sort_order,"onUpdate:modelValue":a[5]||(a[5]=e=>Y.sort_order=e),style:{width:"100px"}},{default:n((()=>[u(H,{label:"降序",value:"desc"}),u(H,{label:"升序",value:"asc"})])),_:1},8,["modelValue"])])),_:1}),u(r,null,{default:n((()=>[u(s,{type:"primary",onClick:oe},{default:n((()=>a[18]||(a[18]=[c(" 搜索 ")]))),_:1}),u(s,{onClick:de},{default:n((()=>a[19]||(a[19]=[c(" 重置 ")]))),_:1})])),_:1})])),_:1},8,["model"])])),_:1}),u(re,{class:"table-card"},{default:n((()=>[V((d(),z(ye,{data:J.value,stripe:"",border:"",style:{width:"100%"}},{default:n((()=>[u(_e,{prop:"name",label:"权限名称","min-width":"180"},{default:n((({row:e})=>[i("div",T,[e.is_system?(d(),z(me,{key:0,type:"warning",size:"small"},{default:n((()=>a[20]||(a[20]=[c("系统")]))),_:1})):C("",!0),i("span",X,j(e.name),1)])])),_:1}),u(_e,{prop:"description",label:"权限描述","min-width":"200","show-overflow-tooltip":""}),u(_e,{prop:"resource",label:"资源类型",width:"120"},{default:n((({row:e})=>[u(me,{type:"info",size:"small"},{default:n((()=>[c(j(e.resource),1)])),_:2},1024)])),_:1}),u(_e,{prop:"action",label:"操作类型",width:"100"},{default:n((({row:e})=>[u(me,{type:se(e.action),size:"small"},{default:n((()=>[c(j(te(e.action)),1)])),_:2},1032,["type"])])),_:1}),u(_e,{prop:"is_system",label:"权限类型",width:"100"},{default:n((({row:e})=>[u(me,{type:e.is_system?"warning":"success",size:"small"},{default:n((()=>[c(j(e.is_system?"系统权限":"自定义"),1)])),_:2},1032,["type"])])),_:1}),u(_e,{prop:"created_at",label:"创建时间",width:"180"},{default:n((({row:e})=>[c(j(le(e.created_at)),1)])),_:1}),u(_e,{prop:"updated_at",label:"更新时间",width:"180"},{default:n((({row:e})=>[c(j(le(e.updated_at)),1)])),_:1}),u(_e,{label:"操作",width:"200",fixed:"right"},{default:n((({row:e})=>[u(s,{type:"primary",size:"small",onClick:a=>{return l=e,M.value=l,void(E.value=!0);var l}},{default:n((()=>a[21]||(a[21]=[c(" 查看 ")]))),_:2},1032,["onClick"]),e.is_system?C("",!0):(d(),z(s,{key:0,type:"warning",size:"small",onClick:a=>{return l=e,Q.value=!1,Object.assign(ee,{name:l.name,description:l.description,resource:l.resource,action:l.action,is_system:l.is_system}),M.value=l,void($.value=!0);var l}},{default:n((()=>a[22]||(a[22]=[c(" 编辑 ")]))),_:2},1032,["onClick"])),e.is_system?C("",!0):(d(),z(s,{key:1,type:"danger",size:"small",onClick:a=>ce(e)},{default:n((()=>a[23]||(a[23]=[c(" 删除 ")]))),_:2},1032,["onClick"]))])),_:1})])),_:1},8,["data"])),[[we,l.value]]),i("div",A,[u(ve,{"current-page":Z.page,"onUpdate:currentPage":a[6]||(a[6]=e=>Z.page=e),"page-size":Z.page_size,"onUpdate:pageSize":a[7]||(a[7]=e=>Z.page_size=e),"page-sizes":[10,20,50,100],total:Z.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:ie,onCurrentChange:ue},null,8,["current-page","page-size","total"])])])),_:1}),u(fe,{modelValue:$.value,"onUpdate:modelValue":a[14]||(a[14]=e=>$.value=e),title:Q.value?"新增权限":"编辑权限",width:"600px","close-on-click-modal":!1},{footer:n((()=>[u(s,{onClick:a[13]||(a[13]=e=>$.value=!1)},{default:n((()=>a[25]||(a[25]=[c("取消")]))),_:1}),u(s,{type:"primary",loading:G.value,onClick:pe},{default:n((()=>[c(j(Q.value?"创建":"更新"),1)])),_:1},8,["loading"])])),default:n((()=>[u(K,{ref_key:"editFormRef",ref:W,model:ee,rules:ae,"label-width":"80px"},{default:n((()=>[u(r,{label:"权限名称",prop:"name"},{default:n((()=>[u(t,{modelValue:ee.name,"onUpdate:modelValue":a[8]||(a[8]=e=>ee.name=e),placeholder:"请输入权限名称"},null,8,["modelValue"])])),_:1}),u(r,{label:"权限描述",prop:"description"},{default:n((()=>[u(t,{modelValue:ee.description,"onUpdate:modelValue":a[9]||(a[9]=e=>ee.description=e),type:"textarea",rows:3,placeholder:"请输入权限描述"},null,8,["modelValue"])])),_:1}),u(r,{label:"资源类型",prop:"resource"},{default:n((()=>[u(I,{modelValue:ee.resource,"onUpdate:modelValue":a[10]||(a[10]=e=>ee.resource=e),placeholder:"请选择资源类型",style:{width:"100%"}},{default:n((()=>[u(H,{label:"用户管理",value:"user"}),u(H,{label:"角色管理",value:"role"}),u(H,{label:"权限管理",value:"permission"}),u(H,{label:"订单管理",value:"order"}),u(H,{label:"余额管理",value:"balance"}),u(H,{label:"系统配置",value:"system"}),u(H,{label:"快递公司",value:"express_company"}),u(H,{label:"快递价格",value:"express_price"})])),_:1},8,["modelValue"])])),_:1}),u(r,{label:"操作类型",prop:"action"},{default:n((()=>[u(I,{modelValue:ee.action,"onUpdate:modelValue":a[11]||(a[11]=e=>ee.action=e),placeholder:"请选择操作类型",style:{width:"100%"}},{default:n((()=>[u(H,{label:"创建",value:"create"}),u(H,{label:"读取",value:"read"}),u(H,{label:"更新",value:"update"}),u(H,{label:"删除",value:"delete"})])),_:1},8,["modelValue"])])),_:1}),u(r,{label:"系统权限",prop:"is_system"},{default:n((()=>[u(be,{modelValue:ee.is_system,"onUpdate:modelValue":a[12]||(a[12]=e=>ee.is_system=e),disabled:!Q.value},null,8,["modelValue","disabled"]),a[24]||(a[24]=i("span",{class:"form-tip"},"系统权限不可删除和修改核心属性",-1))])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue","title"]),u(fe,{modelValue:E.value,"onUpdate:modelValue":a[15]||(a[15]=e=>E.value=e),title:"权限详情",width:"600px"},{default:n((()=>[M.value?(d(),o("div",F,[u(he,{column:2,border:""},{default:n((()=>[u(ge,{label:"权限名称"},{default:n((()=>[c(j(M.value.name),1)])),_:1}),u(ge,{label:"权限类型"},{default:n((()=>[u(me,{type:M.value.is_system?"warning":"success",size:"small"},{default:n((()=>[c(j(M.value.is_system?"系统权限":"自定义权限"),1)])),_:1},8,["type"])])),_:1}),u(ge,{label:"资源类型"},{default:n((()=>[u(me,{type:"info",size:"small"},{default:n((()=>[c(j(M.value.resource),1)])),_:1})])),_:1}),u(ge,{label:"操作类型"},{default:n((()=>[u(me,{type:se(M.value.action),size:"small"},{default:n((()=>[c(j(te(M.value.action)),1)])),_:1},8,["type"])])),_:1}),u(ge,{label:"权限描述",span:2},{default:n((()=>[c(j(M.value.description||"暂无描述"),1)])),_:1}),u(ge,{label:"创建时间"},{default:n((()=>[c(j(le(M.value.created_at)),1)])),_:1}),u(ge,{label:"更新时间"},{default:n((()=>[c(j(le(M.value.updated_at)),1)])),_:1})])),_:1})])):C("",!0)])),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-fdcc6044"]]);export{G as default};
