var e=(e,a,l)=>new Promise(((t,s)=>{var i=e=>{try{d(l.next(e))}catch(a){s(a)}},o=e=>{try{d(l.throw(e))}catch(a){s(a)}},d=e=>e.done?t(e.value):Promise.resolve(e.value).then(i,o);d((l=l.apply(e,a)).next())}));import{_ as a}from"./index-rNRt1EuS.js";/* empty css               *//* empty css                             *//* empty css                */import{d as l,r as t,X as s,j as i,f as o,c as d,o as r,e as u,b as n,w as p,K as c,u as m,aR as v,H as _,Z as y,_ as f,C as h,D as g,ar as b,as as w,aV as V,a2 as k,N as C,aI as x,aJ as z,a as U,aX as j,x as P,aG as L,aH as R,ap as S,Y as T,b1 as B,b2 as N,aT as O,aN as $,M as D,ai as F}from"./vendor-CAPBtMef.js";import{R as H,P as I}from"./rolePermissionApi-CKaU4rQM.js";const K={class:"role-management"},X={class:"page-header"},q={class:"header-right"},A={class:"role-info"},G={class:"role-name"},J={class:"pagination-wrapper"},M={key:0,class:"role-detail"},Y={class:"permissions-section",style:{"margin-top":"20px"}},Z={key:0,class:"permission-management"},E={class:"role-info-header"},Q={class:"permission-section"},W={style:{"margin-top":"10px","text-align":"center"}},ee={class:"permission-section"},ae={style:{"margin-top":"10px","text-align":"center"}},le=a(l({__name:"index",setup(a){const l=t(!1),le=t(!1),te=t(!1),se=t(!1),ie=t([]),oe=t(null),de=t([]),re=t([]),ue=s({keyword:"",is_system:"",sort_by:"created_at",sort_order:"desc"}),ne=s({page:1,page_size:20,total:0}),pe=t(!1),ce=t(!1),me=t(!1),ve=t(!1),_e=t(),ye=s({name:"",description:"",is_system:!1}),fe={name:[{required:!0,message:"请输入角色名称",trigger:"blur"},{min:2,max:100,message:"角色名称长度在 2 到 100 个字符",trigger:"blur"}]},he=t(""),ge=t(""),be=t([]),we=t([]),Ve=i((()=>{const e=de.value.map((e=>e.id));return re.value.filter((a=>!e.includes(a.id))).filter((e=>{if(!he.value)return!0;const a=he.value.toLowerCase();return e.name.toLowerCase().includes(a)||e.resource.toLowerCase().includes(a)||e.action.toLowerCase().includes(a)}))})),ke=i((()=>de.value.filter((e=>{if(!ge.value)return!0;const a=ge.value.toLowerCase();return e.name.toLowerCase().includes(a)||e.resource.toLowerCase().includes(a)||e.action.toLowerCase().includes(a)})))),Ce=e=>e?new Date(e).toLocaleString("zh-CN"):"-",xe=()=>e(this,null,(function*(){try{l.value=!0;const e=yield H.getRoleList({page:ne.page,page_size:ne.page_size,keyword:ue.keyword,is_system:""===ue.is_system?void 0:ue.is_system,order_by:ue.sort_by,order:ue.sort_order});ie.value=e.items,ne.total=e.total,ne.page=e.page,ne.page_size=e.page_size}catch(e){D.error(e.message||"加载角色列表失败")}finally{l.value=!1}})),ze=()=>e(this,null,(function*(){try{se.value=!0;const e=yield I.getPermissionList({page:1,page_size:1e3});re.value=e.items}catch(e){D.error(e.message||"加载权限列表失败")}finally{se.value=!1}})),Ue=a=>e(this,null,(function*(){try{te.value=!0,de.value=yield I.getPermissionsByRole(a)}catch(e){D.error(e.message||"加载角色权限失败")}finally{te.value=!1}})),je=()=>{ne.page=1,xe()},Pe=()=>{Object.assign(ue,{keyword:"",is_system:"",sort_by:"created_at",sort_order:"desc"}),je()},Le=e=>{ne.page_size=e,ne.page=1,xe()},Re=e=>{ne.page=e,xe()},Se=()=>{ve.value=!0,Object.assign(ye,{name:"",description:"",is_system:!1}),pe.value=!0},Te=a=>e(this,null,(function*(){oe.value=a,ce.value=!0,yield Ue(a.id)})),Be=a=>e(this,null,(function*(){try{yield F.confirm(`确定要删除角色 "${a.name}" 吗？此操作不可恢复。`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),yield H.deleteRole(a.id),D.success("角色删除成功"),yield xe()}catch(e){"cancel"!==e&&D.error(e.message||"删除角色失败")}})),Ne=()=>e(this,null,(function*(){try{if(!(yield _e.value.validate()))return;if(le.value=!0,ve.value){const e={name:ye.name,description:ye.description,is_system:ye.is_system};yield H.createRole(e),D.success("角色创建成功")}else{const e={name:ye.name,description:ye.description,is_system:ye.is_system};yield H.updateRole(oe.value.id,e),D.success("角色更新成功")}pe.value=!1,yield xe()}catch(e){D.error(e.message||"保存角色失败")}finally{le.value=!1}})),Oe=a=>e(this,null,(function*(){oe.value=a,me.value=!0,yield Promise.all([Ue(a.id),ze()]),be.value=[],we.value=[]})),$e=e=>{be.value=e},De=e=>{we.value=e},Fe=()=>e(this,null,(function*(){if(oe.value&&0!==be.value.length)try{const e=be.value.map((e=>H.addPermissionToRole(oe.value.id,e.id)));yield Promise.all(e),D.success(`成功添加 ${be.value.length} 个权限`),yield Ue(oe.value.id),be.value=[]}catch(e){D.error(e.message||"添加权限失败")}})),He=()=>e(this,null,(function*(){if(oe.value&&0!==we.value.length)try{const e=we.value.map((e=>H.removePermissionFromRole(oe.value.id,e.id)));yield Promise.all(e),D.success(`成功移除 ${we.value.length} 个权限`),yield Ue(oe.value.id),we.value=[]}catch(e){D.error(e.message||"移除权限失败")}}));return o((()=>{xe()})),(e,a)=>{const t=_,s=h,i=f,o=w,D=b,F=y,H=V,I=j,re=z,xe=x,ze=R,Ue=S,Ie=T,Ke=N,Xe=B,qe=$,Ae=O,Ge=L;return r(),d("div",K,[u("div",X,[a[17]||(a[17]=u("div",{class:"header-left"},[u("h1",{class:"page-title"},"角色管理"),u("p",{class:"page-description"},"管理系统中的所有角色和权限分配")],-1)),u("div",q,[n(t,{type:"primary",icon:m(v),onClick:Se},{default:p((()=>a[16]||(a[16]=[c(" 新增角色 ")]))),_:1},8,["icon"])])]),n(H,{class:"search-card"},{default:p((()=>[n(F,{model:ue,inline:""},{default:p((()=>[n(i,{label:"角色名称"},{default:p((()=>[n(s,{modelValue:ue.keyword,"onUpdate:modelValue":a[0]||(a[0]=e=>ue.keyword=e),placeholder:"请输入角色名称",clearable:"",style:{width:"200px"},onKeyup:g(je,["enter"])},null,8,["modelValue"])])),_:1}),n(i,{label:"角色类型"},{default:p((()=>[n(D,{modelValue:ue.is_system,"onUpdate:modelValue":a[1]||(a[1]=e=>ue.is_system=e),placeholder:"请选择角色类型",clearable:"",style:{width:"150px"}},{default:p((()=>[n(o,{label:"全部",value:""}),n(o,{label:"系统角色",value:!0}),n(o,{label:"自定义角色",value:!1})])),_:1},8,["modelValue"])])),_:1}),n(i,{label:"排序方式"},{default:p((()=>[n(D,{modelValue:ue.sort_by,"onUpdate:modelValue":a[2]||(a[2]=e=>ue.sort_by=e),style:{width:"120px"}},{default:p((()=>[n(o,{label:"创建时间",value:"created_at"}),n(o,{label:"更新时间",value:"updated_at"}),n(o,{label:"角色名称",value:"name"})])),_:1},8,["modelValue"])])),_:1}),n(i,{label:"排序"},{default:p((()=>[n(D,{modelValue:ue.sort_order,"onUpdate:modelValue":a[3]||(a[3]=e=>ue.sort_order=e),style:{width:"100px"}},{default:p((()=>[n(o,{label:"降序",value:"desc"}),n(o,{label:"升序",value:"asc"})])),_:1},8,["modelValue"])])),_:1}),n(i,null,{default:p((()=>[n(t,{type:"primary",onClick:je},{default:p((()=>a[18]||(a[18]=[c(" 搜索 ")]))),_:1}),n(t,{onClick:Pe},{default:p((()=>a[19]||(a[19]=[c(" 重置 ")]))),_:1})])),_:1})])),_:1},8,["model"])])),_:1}),n(H,{class:"table-card"},{default:p((()=>[k((r(),C(xe,{data:ie.value,stripe:"",border:"",style:{width:"100%"}},{default:p((()=>[n(re,{prop:"name",label:"角色名称","min-width":"150"},{default:p((({row:e})=>[u("div",A,[e.is_system?(r(),C(I,{key:0,type:"warning",size:"small"},{default:p((()=>a[20]||(a[20]=[c("系统")]))),_:1})):U("",!0),u("span",G,P(e.name),1)])])),_:1}),n(re,{prop:"description",label:"角色描述","min-width":"200","show-overflow-tooltip":""}),n(re,{prop:"is_system",label:"角色类型",width:"100"},{default:p((({row:e})=>[n(I,{type:e.is_system?"warning":"success",size:"small"},{default:p((()=>[c(P(e.is_system?"系统角色":"自定义"),1)])),_:2},1032,["type"])])),_:1}),n(re,{prop:"permissions",label:"权限数量",width:"100"},{default:p((({row:e})=>[n(I,{type:"info",size:"small"},{default:p((()=>[c(P(e.permissions?e.permissions.length:0),1)])),_:2},1024)])),_:1}),n(re,{prop:"created_at",label:"创建时间",width:"180"},{default:p((({row:e})=>[c(P(Ce(e.created_at)),1)])),_:1}),n(re,{prop:"updated_at",label:"更新时间",width:"180"},{default:p((({row:e})=>[c(P(Ce(e.updated_at)),1)])),_:1}),n(re,{label:"操作",width:"280",fixed:"right"},{default:p((({row:e})=>[n(t,{type:"primary",size:"small",onClick:a=>Te(e)},{default:p((()=>a[21]||(a[21]=[c(" 查看 ")]))),_:2},1032,["onClick"]),n(t,{type:"success",size:"small",onClick:a=>Oe(e)},{default:p((()=>a[22]||(a[22]=[c(" 权限管理 ")]))),_:2},1032,["onClick"]),e.is_system?U("",!0):(r(),C(t,{key:0,type:"warning",size:"small",onClick:a=>{return l=e,ve.value=!1,Object.assign(ye,{name:l.name,description:l.description,is_system:l.is_system}),oe.value=l,void(pe.value=!0);var l}},{default:p((()=>a[23]||(a[23]=[c(" 编辑 ")]))),_:2},1032,["onClick"])),e.is_system?U("",!0):(r(),C(t,{key:1,type:"danger",size:"small",onClick:a=>Be(e)},{default:p((()=>a[24]||(a[24]=[c(" 删除 ")]))),_:2},1032,["onClick"]))])),_:1})])),_:1},8,["data"])),[[Ge,l.value]]),u("div",J,[n(ze,{"current-page":ne.page,"onUpdate:currentPage":a[4]||(a[4]=e=>ne.page=e),"page-size":ne.page_size,"onUpdate:pageSize":a[5]||(a[5]=e=>ne.page_size=e),"page-sizes":[10,20,50,100],total:ne.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:Le,onCurrentChange:Re},null,8,["current-page","page-size","total"])])])),_:1}),n(Ie,{modelValue:pe.value,"onUpdate:modelValue":a[10]||(a[10]=e=>pe.value=e),title:ve.value?"新增角色":"编辑角色",width:"600px","close-on-click-modal":!1},{footer:p((()=>[n(t,{onClick:a[9]||(a[9]=e=>pe.value=!1)},{default:p((()=>a[26]||(a[26]=[c("取消")]))),_:1}),n(t,{type:"primary",loading:le.value,onClick:Ne},{default:p((()=>[c(P(ve.value?"创建":"更新"),1)])),_:1},8,["loading"])])),default:p((()=>[n(F,{ref_key:"editFormRef",ref:_e,model:ye,rules:fe,"label-width":"80px"},{default:p((()=>[n(i,{label:"角色名称",prop:"name"},{default:p((()=>[n(s,{modelValue:ye.name,"onUpdate:modelValue":a[6]||(a[6]=e=>ye.name=e),placeholder:"请输入角色名称"},null,8,["modelValue"])])),_:1}),n(i,{label:"角色描述",prop:"description"},{default:p((()=>[n(s,{modelValue:ye.description,"onUpdate:modelValue":a[7]||(a[7]=e=>ye.description=e),type:"textarea",rows:3,placeholder:"请输入角色描述"},null,8,["modelValue"])])),_:1}),n(i,{label:"系统角色",prop:"is_system"},{default:p((()=>[n(Ue,{modelValue:ye.is_system,"onUpdate:modelValue":a[8]||(a[8]=e=>ye.is_system=e),disabled:!ve.value},null,8,["modelValue","disabled"]),a[25]||(a[25]=u("span",{class:"form-tip"},"系统角色不可删除和修改核心属性",-1))])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue","title"]),n(Ie,{modelValue:ce.value,"onUpdate:modelValue":a[11]||(a[11]=e=>ce.value=e),title:"角色详情",width:"800px"},{default:p((()=>[oe.value?(r(),d("div",M,[n(Xe,{column:2,border:""},{default:p((()=>[n(Ke,{label:"角色名称"},{default:p((()=>[c(P(oe.value.name),1)])),_:1}),n(Ke,{label:"角色类型"},{default:p((()=>[n(I,{type:oe.value.is_system?"warning":"success",size:"small"},{default:p((()=>[c(P(oe.value.is_system?"系统角色":"自定义角色"),1)])),_:1},8,["type"])])),_:1}),n(Ke,{label:"角色描述",span:2},{default:p((()=>[c(P(oe.value.description||"暂无描述"),1)])),_:1}),n(Ke,{label:"创建时间"},{default:p((()=>[c(P(Ce(oe.value.created_at)),1)])),_:1}),n(Ke,{label:"更新时间"},{default:p((()=>[c(P(Ce(oe.value.updated_at)),1)])),_:1})])),_:1}),u("div",Y,[a[27]||(a[27]=u("h4",null,"角色权限",-1)),k((r(),C(xe,{data:de.value,stripe:"",border:"","max-height":"300"},{default:p((()=>[n(re,{prop:"name",label:"权限名称","min-width":"150"}),n(re,{prop:"description",label:"权限描述","min-width":"200","show-overflow-tooltip":""}),n(re,{prop:"resource",label:"资源",width:"120"}),n(re,{prop:"action",label:"操作",width:"120"}),n(re,{prop:"is_system",label:"类型",width:"100"},{default:p((({row:e})=>[n(I,{type:e.is_system?"warning":"success",size:"small"},{default:p((()=>[c(P(e.is_system?"系统":"自定义"),1)])),_:2},1032,["type"])])),_:1})])),_:1},8,["data"])),[[Ge,te.value]])])])):U("",!0)])),_:1},8,["modelValue"]),n(Ie,{modelValue:me.value,"onUpdate:modelValue":a[15]||(a[15]=e=>me.value=e),title:"权限管理",width:"1000px","close-on-click-modal":!1},{footer:p((()=>[n(t,{onClick:a[14]||(a[14]=e=>me.value=!1)},{default:p((()=>a[32]||(a[32]=[c("关闭")]))),_:1})])),default:p((()=>[oe.value?(r(),d("div",Z,[u("div",E,[u("h4",null,'为角色 "'+P(oe.value.name)+'" 分配权限',1)]),n(Ae,{gutter:20},{default:p((()=>[n(qe,{span:12},{default:p((()=>[u("div",Q,[a[29]||(a[29]=u("h5",null,"可用权限",-1)),n(s,{modelValue:he.value,"onUpdate:modelValue":a[12]||(a[12]=e=>he.value=e),placeholder:"搜索权限",clearable:"",style:{"margin-bottom":"10px"}},null,8,["modelValue"]),k((r(),C(xe,{data:Ve.value,stripe:"",border:"","max-height":"400",onSelectionChange:$e},{default:p((()=>[n(re,{type:"selection",width:"55"}),n(re,{prop:"name",label:"权限名称","min-width":"120","show-overflow-tooltip":""}),n(re,{prop:"resource",label:"资源",width:"80"}),n(re,{prop:"action",label:"操作",width:"80"})])),_:1},8,["data"])),[[Ge,se.value]]),u("div",W,[n(t,{type:"primary",disabled:0===be.value.length,onClick:Fe},{default:p((()=>a[28]||(a[28]=[c(" 添加权限 → ")]))),_:1},8,["disabled"])])])])),_:1}),n(qe,{span:12},{default:p((()=>[u("div",ee,[a[31]||(a[31]=u("h5",null,"已分配权限",-1)),n(s,{modelValue:ge.value,"onUpdate:modelValue":a[13]||(a[13]=e=>ge.value=e),placeholder:"搜索权限",clearable:"",style:{"margin-bottom":"10px"}},null,8,["modelValue"]),k((r(),C(xe,{data:ke.value,stripe:"",border:"","max-height":"400",onSelectionChange:De},{default:p((()=>[n(re,{type:"selection",width:"55"}),n(re,{prop:"name",label:"权限名称","min-width":"120","show-overflow-tooltip":""}),n(re,{prop:"resource",label:"资源",width:"80"}),n(re,{prop:"action",label:"操作",width:"80"})])),_:1},8,["data"])),[[Ge,te.value]]),u("div",ae,[n(t,{type:"danger",disabled:0===we.value.length,onClick:He},{default:p((()=>a[30]||(a[30]=[c(" ← 移除权限 ")]))),_:1},8,["disabled"])])])])),_:1})])),_:1})])):U("",!0)])),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-f2bf51fb"]]);export{le as default};
