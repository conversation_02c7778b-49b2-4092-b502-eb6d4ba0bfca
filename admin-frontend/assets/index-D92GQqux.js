var e=Object.defineProperty,l=Object.defineProperties,a=Object.getOwnPropertyDescriptors,s=Object.getOwnPropertySymbols,o=Object.prototype.hasOwnProperty,r=Object.prototype.propertyIsEnumerable,t=(l,a,s)=>a in l?e(l,a,{enumerable:!0,configurable:!0,writable:!0,value:s}):l[a]=s,i=(e,l)=>{for(var a in l||(l={}))o.call(l,a)&&t(e,a,l[a]);if(s)for(var a of s(l))r.call(l,a)&&t(e,a,l[a]);return e},u=(e,s)=>l(e,a(s)),d=(e,l,a)=>new Promise(((s,o)=>{var r=e=>{try{i(a.next(e))}catch(l){o(l)}},t=e=>{try{i(a.throw(e))}catch(l){o(l)}},i=e=>e.done?s(e.value):Promise.resolve(e.value).then(r,t);i((a=a.apply(e,l)).next())}));import{_ as n}from"./index-rNRt1EuS.js";/* empty css                *//* empty css               */import{d as c,r as p,X as v,f,M as m,c as _,o as b,e as g,a as y,b as h,K as x,w as j,y as w,u as D,aQ as C,H as $,bB as k,aN as F,aT as U,Z as R,_ as O,ar as S,F as E,A as P,N as V,as as z,aF as q,a3 as L,aU as T,aV as I,aS as B,by as N,ai as A}from"./vendor-CAPBtMef.js";import{S as G}from"./StatCard-DtRQl2IM.js";import Q from"./UpdatePriceDialogV2-1z3q7TpW.js";import H from"./DetailDialog-D7iL4t9Z.js";import K from"./RouteDetailsDialog-C2Na4fca.js";import M from"./DeleteDialog-C8XwK2jU.js";import X from"./ImportDialog-2yz6lqHx.js";import Z from"./QuickDeleteDialog-CPGriYNo.js";import J from"./ProviderDataSection-Dc9fTa8_.js";import{F as W,P as Y,E as ee}from"./flexiblePriceTableApi-BBM6dCg8.js";import{a as le}from"./priceTable-dC3_lJoq.js";/* empty css                 *//* empty css                    */import"./EnhancedRouteUpdateDialog-DfXZIVhN.js";/* empty css                       *//* empty css                 *//* empty css                   *//* empty css                       *//* empty css                             *//* empty css                  */const ae={class:"flexible-price-table"},se={class:"page-header"},oe={class:"page-actions"},re={key:0,class:"summary-cards"},te={class:"search-filters"},ie={class:"provider-grouped-data"},ue={class:"global-actions"},de={class:"global-actions-content"},ne={class:"actions-buttons"},ce=n(c({__name:"index",setup(e){const l=p(!1),a=p(!1);p(!1);const s=p(!1),o=p(!1),r=p(!1),t=p(!1),n=p(!1),c=p(!1),ce=p(null),pe=p([]);p([]);const ve=p(null),fe=p({}),me=p(""),_e=p(""),be=p(""),ge=p({provider:"",expressCode:"",expressName:"",totalRoutes:0}),ye=p(),he=v({page:1,page_size:20,providers:void 0,express_codes:void 0,query_success:void 0}),xe=v({page:1,pageSize:20,total:0});f((()=>{je(),we()}));const je=()=>d(this,null,(function*(){try{const e=yield W.getSummary();e.success&&(ce.value=e.data)}catch(e){}})),we=()=>d(this,null,(function*(){try{l.value=!0;const e=u(i({},he),{page:xe.page,page_size:xe.pageSize}),a=yield W.getGroupedData(e);a.success?(pe.value=a.data.records,xe.total=a.data.total,xe.page=a.data.page,xe.pageSize=a.data.page_size,De()):m.error(a.message||"获取分组数据失败")}catch(e){m.error("获取分组数据失败")}finally{l.value=!1}})),De=()=>{const e={};Y.forEach((l=>{e[l.value]=[]})),pe.value.forEach((l=>{e[l.provider]&&e[l.provider].push(l)})),fe.value=e},Ce=e=>fe.value[e]||[],$e=()=>{je(),we()},ke=e=>d(this,null,(function*(){try{l.value=!0;const a=u(i({},he),{providers:[e],page:1,page_size:100}),s=yield W.getGroupedData(a);s.success?(fe.value[e]=s.data.records,m.success(`${le(e)} 数据刷新成功`)):m.error(`刷新 ${le(e)} 数据失败`)}catch(a){m.error(`刷新 ${le(e)} 数据失败`)}finally{l.value=!1}})),Fe=()=>{xe.page=1,we()},Ue=()=>{Object.assign(he,{page:1,page_size:20,providers:void 0,express_codes:void 0,query_success:void 0}),xe.page=1,xe.pageSize=20,we(),m.success("搜索条件已重置")},Re=e=>{me.value=e.provider,_e.value=e.express_code,be.value=e.express_name,r.value=!0},Oe=()=>{$e()},Se=e=>d(this,null,(function*(){try{a.value=!0;const l={providers:[e],format:"csv",include_failed:!0},s=yield W.downloadFile(l),o=(new Date).toISOString().slice(0,19).replace(/[-:]/g,"").replace("T","_"),r=`${e}_price_data_${o}.csv`,t=window.URL.createObjectURL(s),i=document.createElement("a");i.href=t,i.download=r,i.style.display="none",document.body.appendChild(i),i.click(),document.body.removeChild(i),window.URL.revokeObjectURL(t),m.success(`${le(e)} 数据导出成功：${r}`)}catch(l){m.error(`导出 ${le(e)} 数据失败`)}finally{a.value=!1}})),Ee=e=>d(this,null,(function*(){try{const l=le(e),a=Ce(e),s=a.reduce(((e,l)=>e+l.total_routes),0),o=`确定要删除 ${l} 的所有价格数据吗？\n\n这将删除 ${a.length} 个快递公司的 ${s} 条路线数据，此操作不可撤销！`;yield A.confirm(o,"确认删除",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning",confirmButtonClass:"el-button--danger"});const r={providers:[e],dry_run:!1},t=yield W.deletePriceData(r);t.success?(m.success(`${l} 数据删除成功：已删除 ${t.data.affected_rows} 条记录`),$e()):m.error(`删除 ${l} 数据失败`)}catch(l){"cancel"!==l&&m.error(`删除 ${le(e)} 数据失败`)}})),Pe=()=>d(this,null,(function*(){var e,l;try{a.value=!0;const s={providers:he.providers,express_codes:he.express_codes,from_provinces:he.from_provinces,to_provinces:he.to_provinces,format:"csv",include_failed:!0},o=yield W.downloadFile(s),r=(new Date).toISOString().slice(0,19).replace(/[-:]/g,"").replace("T","_");let t=`price_data_${r}.csv`;1===(null==(e=s.providers)?void 0:e.length)&&1===(null==(l=s.express_codes)?void 0:l.length)&&(t=`${s.providers[0]}_${s.express_codes[0]}_${r}.csv`);const i=window.URL.createObjectURL(o),u=document.createElement("a");u.href=i,u.download=t,u.style.display="none",document.body.appendChild(u),u.click(),document.body.removeChild(u),window.URL.revokeObjectURL(i),m.success(`导出成功：${t}`)}catch(s){m.error("导出失败："+(s.message||"未知错误"))}finally{a.value=!1}})),Ve=e=>{ge.value={provider:e.provider,expressCode:e.express_code,expressName:e.express_name,totalRoutes:e.total_routes},c.value=!0},ze=e=>d(this,null,(function*(){try{ye.value&&ye.value.setDeleting(!0);const l={providers:[e.provider],express_codes:[e.expressCode],dry_run:!1},a=yield W.deletePriceData(l);a.success?(m.success(`删除成功：已删除 ${a.data.affected_rows} 条记录`),c.value=!1,$e()):m.error(a.message||"删除失败")}catch(l){m.error("删除失败："+((null==l?void 0:l.message)||"未知错误"))}finally{ye.value&&ye.value.setDeleting(!1)}})),qe=()=>{t.value=!0},Le=()=>{n.value=!0},Te=()=>{$e()};return(e,i)=>{const u=w,d=$,p=F,v=U,f=z,m=S,A=O,W=q,le=R,pe=I;return b(),_("div",ae,[g("div",se,[i[12]||(i[12]=g("div",{class:"header-left"},[g("h1",{class:"page-title"},[g("i",{class:"iconfont-sys"},""),x(" 灵活价格表管理 ")]),g("p",{class:"page-description"},"管理多供应商价格数据、查询对比和更新维护")],-1)),g("div",oe,[h(d,{onClick:$e,loading:l.value},{default:j((()=>[h(u,null,{default:j((()=>[h(D(C))])),_:1}),i[10]||(i[10]=x(" 刷新 "))])),_:1},8,["loading"]),h(d,{type:"primary",onClick:i[0]||(i[0]=e=>s.value=!0)},{default:j((()=>[h(u,null,{default:j((()=>[h(D(k))])),_:1}),i[11]||(i[11]=x(" 更新价格 "))])),_:1})])]),ce.value?(b(),_("div",re,[h(v,{gutter:20},{default:j((()=>[h(p,{span:6},{default:j((()=>{var e;return[h(G,{title:"总记录数",value:(null==(e=ce.value)?void 0:e.total_records)||0,icon:"el-icon-document","icon-color":"#409EFF","icon-bg-color":"#E6F7FF"},null,8,["value"])]})),_:1}),h(p,{span:6},{default:j((()=>{var e;return[h(G,{title:"供应商数量",value:Object.keys((null==(e=ce.value)?void 0:e.provider_stats)||{}).length,icon:"el-icon-office-building","icon-color":"#67C23A","icon-bg-color":"#F0F9FF"},null,8,["value"])]})),_:1}),h(p,{span:6},{default:j((()=>{var e;return[h(G,{title:"成功查询",value:(null==(e=ce.value)?void 0:e.successful_queries)||0,icon:"el-icon-success","icon-color":"#E6A23C","icon-bg-color":"#FDF6EC"},null,8,["value"])]})),_:1}),h(p,{span:6},{default:j((()=>{var e,l;return[h(G,{title:"成功率",value:`${(((null==(e=ce.value)?void 0:e.successful_queries)||0)/((null==(l=ce.value)?void 0:l.total_records)||1)*100).toFixed(1)}%`,icon:"el-icon-coin","icon-color":"#F56C6C","icon-bg-color":"#FEF0F0"},null,8,["value"])]})),_:1})])),_:1})])):y("",!0),g("div",te,[h(pe,null,{default:j((()=>[h(le,{model:he,"label-width":"100px"},{default:j((()=>[h(v,{gutter:20},{default:j((()=>[h(p,{span:6},{default:j((()=>[h(A,{label:"供应商"},{default:j((()=>[h(m,{modelValue:he.providers,"onUpdate:modelValue":i[1]||(i[1]=e=>he.providers=e),placeholder:"请选择供应商",multiple:"",clearable:"",style:{width:"100%"}},{default:j((()=>[(b(!0),_(E,null,P(D(Y),(e=>(b(),V(f,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1})])),_:1}),h(p,{span:6},{default:j((()=>[h(A,{label:"快递公司"},{default:j((()=>[h(m,{modelValue:he.express_codes,"onUpdate:modelValue":i[2]||(i[2]=e=>he.express_codes=e),placeholder:"请选择快递公司",multiple:"",clearable:"",style:{width:"100%"}},{default:j((()=>[(b(!0),_(E,null,P(D(ee),(e=>(b(),V(f,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1})])),_:1}),h(p,{span:6},{default:j((()=>[h(A,{label:"查询状态"},{default:j((()=>[h(m,{modelValue:he.query_success,"onUpdate:modelValue":i[3]||(i[3]=e=>he.query_success=e),placeholder:"请选择查询状态",clearable:"",style:{width:"100%"}},{default:j((()=>[h(f,{label:"成功",value:!0}),h(f,{label:"失败",value:!1})])),_:1},8,["modelValue"])])),_:1})])),_:1}),h(p,{span:6},{default:j((()=>[h(A,{label:" "},{default:j((()=>[h(W,null,{default:j((()=>[h(d,{type:"primary",onClick:Fe,loading:l.value},{default:j((()=>[h(u,null,{default:j((()=>[h(D(L))])),_:1}),i[13]||(i[13]=x(" 搜索 "))])),_:1},8,["loading"]),h(d,{onClick:Ue},{default:j((()=>[h(u,null,{default:j((()=>[h(D(T))])),_:1}),i[14]||(i[14]=x(" 重置 "))])),_:1})])),_:1})])),_:1})])),_:1})])),_:1})])),_:1},8,["model"])])),_:1})]),g("div",ie,[g("div",ue,[h(pe,null,{default:j((()=>[g("div",de,[i[18]||(i[18]=g("div",{class:"actions-title"},[g("h3",null,"全局操作"),g("span",null,"对所有供应商数据进行统一操作")],-1)),g("div",ne,[h(d,{type:"primary",onClick:Pe,loading:a.value},{default:j((()=>[h(u,null,{default:j((()=>[h(D(B))])),_:1}),i[15]||(i[15]=x(" 导出全部数据 "))])),_:1},8,["loading"]),h(d,{type:"success",onClick:Le},{default:j((()=>[h(u,null,{default:j((()=>[h(D(k))])),_:1}),i[16]||(i[16]=x(" 导入数据 "))])),_:1}),h(d,{type:"danger",onClick:qe},{default:j((()=>[h(u,null,{default:j((()=>[h(D(N))])),_:1}),i[17]||(i[17]=x(" 批量删除 "))])),_:1})])])])),_:1})]),(b(!0),_(E,null,P(D(Y),(e=>(b(),_("div",{key:e.value,class:"provider-section"},[h(J,{provider:e.value,"provider-label":e.label,data:Ce(e.value),loading:l.value,onRefresh:l=>ke(e.value),onExport:l=>Se(e.value),onDelete:l=>Ee(e.value),onShowRouteDetails:Re,onRowDelete:Ve},null,8,["provider","provider-label","data","loading","onRefresh","onExport","onDelete"])])))),128))]),h(Q,{visible:s.value,"onUpdate:visible":i[4]||(i[4]=e=>s.value=e),onSuccess:Oe},null,8,["visible"]),h(H,{visible:o.value,"onUpdate:visible":i[5]||(i[5]=e=>o.value=e),"price-data":ve.value},null,8,["visible","price-data"]),h(K,{visible:r.value,"onUpdate:visible":i[6]||(i[6]=e=>r.value=e),provider:me.value,"express-code":_e.value,"express-name":be.value},null,8,["visible","provider","express-code","express-name"]),h(M,{visible:t.value,"onUpdate:visible":i[7]||(i[7]=e=>t.value=e),onDeleted:Te},null,8,["visible"]),h(X,{visible:n.value,"onUpdate:visible":i[8]||(i[8]=e=>n.value=e),onImported:Te},null,8,["visible"]),h(Z,{ref_key:"quickDeleteDialogRef",ref:ye,visible:c.value,"onUpdate:visible":i[9]||(i[9]=e=>c.value=e),provider:ge.value.provider,"express-code":ge.value.expressCode,"express-name":ge.value.expressName,"total-routes":ge.value.totalRoutes,onConfirm:ze},null,8,["visible","provider","express-code","express-name","total-routes"])])}}}),[["__scopeId","data-v-c4da6b5e"]]);export{ce as default};
