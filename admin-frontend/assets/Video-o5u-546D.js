var e=Object.defineProperty,t=Object.getOwnPropertySymbols,i=Object.prototype.hasOwnProperty,n=Object.prototype.propertyIsEnumerable,o=(t,i,n)=>i in t?e(t,i,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[i]=n,r=(e,r)=>{for(var s in r||(r={}))i.call(r,s)&&o(e,s,r[s]);if(t)for(var s of t(r))n.call(r,s)&&o(e,s,r[s]);return e};import{Q as s,d as a,r as l,f as c,bM as u,c as h,o as d,e as f,b as p}from"./vendor-CAPBtMef.js";import{l as g}from"./lock_screen_1-Cw4SEq4H.js";import{_ as v}from"./index-rNRt1EuS.js";function y(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),i.push.apply(i,n)}return i}function m(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?y(Object(i),!0).forEach((function(t){w(e,t,i[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):y(Object(i)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))}))}return e}function k(e){return(k="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function C(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function b(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,D(n.key),n)}}function _(e,t,i){return t&&b(e.prototype,t),i&&b(e,i),Object.defineProperty(e,"prototype",{writable:!1}),e}function w(e,t,i){return(t=D(t))in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}function T(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&S(e,t)}function x(e){return(x=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function S(e,t){return(S=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function E(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function P(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return E(e)}function I(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var i,n=x(e);if(t){var o=x(this).constructor;i=Reflect.construct(n,arguments,o)}else i=n.apply(this,arguments);return P(this,i)}}function L(){return L="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,i){var n=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=x(e)););return e}(e,t);if(n){var o=Object.getOwnPropertyDescriptor(n,t);return o.get?o.get.call(arguments.length<3?e:i):o.value}},L.apply(this,arguments)}function A(e){return function(e){if(Array.isArray(e))return O(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return O(e,t);var i=Object.prototype.toString.call(e).slice(8,-1);"Object"===i&&e.constructor&&(i=e.constructor.name);if("Map"===i||"Set"===i)return Array.from(e);if("Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return O(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function O(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,n=new Array(t);i<t;i++)n[i]=e[i];return n}function D(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var i=e[Symbol.toPrimitive];if(void 0!==i){var n=i.call(e,t);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}var R,M={exports:{}};var N=(R||(R=1,function(e){var t=Object.prototype.hasOwnProperty,i="~";function n(){}function o(e,t,i){this.fn=e,this.context=t,this.once=i||!1}function r(e,t,n,r,s){if("function"!=typeof n)throw new TypeError("The listener must be a function");var a=new o(n,r||e,s),l=i?i+t:t;return e._events[l]?e._events[l].fn?e._events[l]=[e._events[l],a]:e._events[l].push(a):(e._events[l]=a,e._eventsCount++),e}function s(e,t){0==--e._eventsCount?e._events=new n:delete e._events[t]}function a(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),(new n).__proto__||(i=!1)),a.prototype.eventNames=function(){var e,n,o=[];if(0===this._eventsCount)return o;for(n in e=this._events)t.call(e,n)&&o.push(i?n.slice(1):n);return Object.getOwnPropertySymbols?o.concat(Object.getOwnPropertySymbols(e)):o},a.prototype.listeners=function(e){var t=i?i+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var o=0,r=n.length,s=new Array(r);o<r;o++)s[o]=n[o].fn;return s},a.prototype.listenerCount=function(e){var t=i?i+e:e,n=this._events[t];return n?n.fn?1:n.length:0},a.prototype.emit=function(e,t,n,o,r,s){var a=i?i+e:e;if(!this._events[a])return!1;var l,c,u=this._events[a],h=arguments.length;if(u.fn){switch(u.once&&this.removeListener(e,u.fn,void 0,!0),h){case 1:return u.fn.call(u.context),!0;case 2:return u.fn.call(u.context,t),!0;case 3:return u.fn.call(u.context,t,n),!0;case 4:return u.fn.call(u.context,t,n,o),!0;case 5:return u.fn.call(u.context,t,n,o,r),!0;case 6:return u.fn.call(u.context,t,n,o,r,s),!0}for(c=1,l=new Array(h-1);c<h;c++)l[c-1]=arguments[c];u.fn.apply(u.context,l)}else{var d,f=u.length;for(c=0;c<f;c++)switch(u[c].once&&this.removeListener(e,u[c].fn,void 0,!0),h){case 1:u[c].fn.call(u[c].context);break;case 2:u[c].fn.call(u[c].context,t);break;case 3:u[c].fn.call(u[c].context,t,n);break;case 4:u[c].fn.call(u[c].context,t,n,o);break;default:if(!l)for(d=1,l=new Array(h-1);d<h;d++)l[d-1]=arguments[d];u[c].fn.apply(u[c].context,l)}}return!0},a.prototype.on=function(e,t,i){return r(this,e,t,i,!1)},a.prototype.once=function(e,t,i){return r(this,e,t,i,!0)},a.prototype.removeListener=function(e,t,n,o){var r=i?i+e:e;if(!this._events[r])return this;if(!t)return s(this,r),this;var a=this._events[r];if(a.fn)a.fn!==t||o&&!a.once||n&&a.context!==n||s(this,r);else{for(var l=0,c=[],u=a.length;l<u;l++)(a[l].fn!==t||o&&!a[l].once||n&&a[l].context!==n)&&c.push(a[l]);c.length?this._events[r]=1===c.length?c[0]:c:s(this,r)}return this},a.prototype.removeAllListeners=function(e){var t;return e?(t=i?i+e:e,this._events[t]&&s(this,t)):(this._events=new n,this._eventsCount=0),this},a.prototype.off=a.prototype.removeListener,a.prototype.addListener=a.prototype.on,a.prefixed=i,a.EventEmitter=a,e.exports=a}(M)),M.exports);const F=s(N);var H="undefined"!=typeof window&&window.location&&window.location.href.indexOf("xgplayerdebugger=1")>-1,B={info:"color: #525252; background-color: #90ee90;",error:"color: #525252; background-color: red;",warn:"color: #525252; background-color: yellow; "},U="%c[xgplayer]",j={config:{debug:H?3:0},logInfo:function(e){for(var t,i=arguments.length,n=new Array(i>1?i-1:0),o=1;o<i;o++)n[o-1]=arguments[o];this.config.debug>=3&&(t=console).log.apply(t,[U,B.info,e].concat(n))},logWarn:function(e){for(var t,i=arguments.length,n=new Array(i>1?i-1:0),o=1;o<i;o++)n[o-1]=arguments[o];this.config.debug>=1&&(t=console).warn.apply(t,[U,B.warn,e].concat(n))},logError:function(e){var t;if(!(this.config.debug<1)){for(var i=this.config.debug>=2?"trace":"error",n=arguments.length,o=new Array(n>1?n-1:0),r=1;r<n;r++)o[r-1]=arguments[r];(t=console)[i].apply(t,[U,B.error,e].concat(o))}}};var V=function(){function e(t){C(this,e),this.bufferedList=t}return _(e,[{key:"start",value:function(e){return this.bufferedList[e].start}},{key:"end",value:function(e){return this.bufferedList[e].end}},{key:"length",get:function(){return this.bufferedList.length}}]),e}(),W={};function G(e){var t=k(e);return null!==e&&("object"===t||"function"===t)}function z(e,t,i){var n,o,r,s,a,l,c=0,u=!1,h=!1,d=!0,f=!t&&0!==t&&"function"==typeof window.requestAnimationFrame;if("function"!=typeof e)throw new TypeError("Expected a function");function p(t){var i=n,r=o;return n=o=void 0,c=t,s=e.apply(r,i)}function g(e,t){return f?(window.cancelAnimationFrame(a),window.requestAnimationFrame(e)):setTimeout(e,t)}function v(e){var i=e-l;return void 0===l||i>=t||i<0||h&&e-c>=r}function y(){var e=Date.now();if(v(e))return m(e);a=g(y,function(e){var i=e-c,n=t-(e-l);return h?Math.min(n,r-i):n}(e))}function m(e){return a=void 0,d&&n?p(e):(n=o=void 0,s)}function k(){for(var e=Date.now(),i=v(e),r=arguments.length,d=new Array(r),f=0;f<r;f++)d[f]=arguments[f];if(n=d,o=this,l=e,i){if(void 0===a)return function(e){return c=e,a=g(y,t),u?p(e):s}(l);if(h)return a=g(y,t),p(l)}return void 0===a&&(a=g(y,t)),s}return t=+t||0,G(i)&&(u=!!i.leading,r=(h="maxWait"in i)?Math.max(+i.maxWait||0,t):r,d="trailing"in i?!!i.trailing:d),k.cancel=function(){void 0!==a&&function(e){if(f)return window.cancelAnimationFrame(e);clearTimeout(e)}(a),c=0,n=l=o=a=void 0},k.flush=function(){return void 0===a?s:m(Date.now())},k.pending=function(){return void 0!==a},k}W.createDom=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"div",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",o=document.createElement(e);return o.className=n,o.innerHTML=t,Object.keys(i).forEach((function(t){var n=t,r=i[t];"video"===e||"audio"===e||"live-video"===e?r&&o.setAttribute(n,r):o.setAttribute(n,r)})),o},W.createDomFromHtml=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";try{var n=document.createElement("div");n.innerHTML=e;var o=n.children;return n=null,o.length>0?(o=o[0],i&&W.addClass(o,i),t&&Object.keys(t).forEach((function(e){o.setAttribute(e,t[e])})),o):null}catch(r){return j.logError("util.createDomFromHtml",r),null}},W.hasClass=function(e,t){if(!e||!t)return!1;try{return Array.prototype.some.call(e.classList,(function(e){return e===t}))}catch(n){var i=e.className&&"object"===k(e.className)?e.getAttribute("class"):e.className;return i&&!!i.match(new RegExp("(\\s|^)"+t+"(\\s|$)"))}},W.addClass=function(e,t){if(e&&t)try{t.replace(/(^\s+|\s+$)/g,"").split(/\s+/g).forEach((function(t){t&&e.classList.add(t)}))}catch(i){W.hasClass(e,t)||(e.className&&"object"===k(e.className)?e.setAttribute("class",e.getAttribute("class")+" "+t):e.className+=" "+t)}},W.removeClass=function(e,t){if(e&&t)try{t.replace(/(^\s+|\s+$)/g,"").split(/\s+/g).forEach((function(t){t&&e.classList.remove(t)}))}catch(i){W.hasClass(e,t)&&t.split(/\s+/g).forEach((function(t){var i=new RegExp("(\\s|^)"+t+"(\\s|$)");e.className&&"object"===k(e.className)?e.setAttribute("class",e.getAttribute("class").replace(i," ")):e.className=e.className.replace(i," ")}))}},W.toggleClass=function(e,t){e&&t.split(/\s+/g).forEach((function(t){W.hasClass(e,t)?W.removeClass(e,t):W.addClass(e,t)}))},W.classNames=function(){for(var e=arguments,t=[],i=function(i){"String"===W.typeOf(e[i])?t.push(e[i]):"Object"===W.typeOf(e[i])&&Object.keys(e[i]).map((function(n){e[i][n]&&t.push(n)}))},n=0;n<arguments.length;n++)i(n);return t.join(" ")},W.findDom=function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document,i=arguments.length>1?arguments[1]:void 0;try{e=t.querySelector(i)}catch(n){j.logError("util.findDom",n),0===i.indexOf("#")&&(e=t.getElementById(i.slice(1)))}return e},W.getCss=function(e,t){return e.currentStyle?e.currentStyle[t]:document.defaultView.getComputedStyle(e,!1)[t]},W.padStart=function(e,t,i){for(var n=String(i),o=t|0,r=Math.ceil(o/n.length),s=[],a=String(e);r--;)s.push(n);return s.join("").substring(0,o-a.length)+a},W.format=function(e){if(window.isNaN(e))return"";e=Math.round(e);var t=W.padStart(Math.floor(e/3600),2,0),i=W.padStart(Math.floor((e-3600*t)/60),2,0),n=W.padStart(Math.floor(e-3600*t-60*i),2,0);return("00"===t?[i,n]:[t,i,n]).join(":")},W.event=function(e){if(e.touches){var t=e.touches[0]||e.changedTouches[0];e.clientX=t.clientX||0,e.clientY=t.clientY||0,e.offsetX=t.pageX-t.target.offsetLeft,e.offsetY=t.pageY-t.target.offsetTop}e._target=e.target||e.srcElement},W.typeOf=function(e){return Object.prototype.toString.call(e).match(/([^\s.*]+)(?=]$)/g)[0]},W.deepCopy=function(e,t){if("Object"===W.typeOf(t)&&"Object"===W.typeOf(e))return Object.keys(t).forEach((function(i){"Object"!==W.typeOf(t[i])||t[i]instanceof Node?"Array"===W.typeOf(t[i])?e[i]="Array"===W.typeOf(e[i])?e[i].concat(t[i]):t[i]:e[i]=t[i]:void 0===e[i]||void 0===e[i]?e[i]=t[i]:W.deepCopy(e[i],t[i])})),e},W.deepMerge=function(e,t){return Object.keys(t).map((function(i){var n;"Array"===W.typeOf(t[i])&&"Array"===W.typeOf(e[i])?"Array"===W.typeOf(e[i])&&(n=e[i]).push.apply(n,A(t[i])):W.typeOf(e[i])!==W.typeOf(t[i])||null===e[i]||"Object"!==W.typeOf(e[i])||t[i]instanceof window.Node?null!==t[i]&&(e[i]=t[i]):W.deepMerge(e[i],t[i])})),e},W.getBgImage=function(e){var t=(e.currentStyle||window.getComputedStyle(e,null)).backgroundImage;if(!t||"none"===t)return"";var i=document.createElement("a");return i.href=t.replace(/url\("|"\)/g,""),i.href},W.copyDom=function(e){if(e&&1===e.nodeType){var t=document.createElement(e.tagName);return Array.prototype.forEach.call(e.attributes,(function(e){t.setAttribute(e.name,e.value)})),e.innerHTML&&(t.innerHTML=e.innerHTML),t}return""},W.setInterval=function(e,t,i,n){e._interval[t]||(e._interval[t]=window.setInterval(i.bind(e),n))},W.clearInterval=function(e,t){clearInterval(e._interval[t]),e._interval[t]=null},W.setTimeout=function(e,t,i){e._timers||(e._timers=[]);var n=setTimeout((function(){t(),W.clearTimeout(e,n)}),i);return e._timers.push(n),n},W.clearTimeout=function(e,t){var i=e._timers;if("Array"===W.typeOf(i)){for(var n=0;n<i.length;n++)if(i[n]===t){i.splice(n,1),clearTimeout(t);break}}else clearTimeout(t)},W.clearAllTimers=function(e){var t=e._timers;"Array"===W.typeOf(t)&&(t.map((function(e){clearTimeout(e)})),e._timerIds=[])},W.createImgBtn=function(e,t,i,n){var o,r,s,a=W.createDom("xg-".concat(e),"",{},"xgplayer-".concat(e,"-img"));(a.style.backgroundImage='url("'.concat(t,'")'),i&&n)&&(["px","rem","em","pt","dp","vw","vh","vm","%"].every((function(e){return!(i.indexOf(e)>-1&&n.indexOf(e)>-1)||(o=parseFloat(i.slice(0,i.indexOf(e)).trim()),r=parseFloat(n.slice(0,n.indexOf(e)).trim()),s=e,!1)})),a.style.width="".concat(o).concat(s),a.style.height="".concat(r).concat(s),a.style.backgroundSize="".concat(o).concat(s," ").concat(r).concat(s),a.style.margin="start"===e?"-".concat(r/2).concat(s," auto auto -").concat(o/2).concat(s):"auto 5px auto 5px");return a},W.Hex2RGBA=function(e,t){var i=[];if(/^\#[0-9A-F]{3}$/i.test(e)){var n="#";e.replace(/[0-9A-F]/gi,(function(e){n+=e+e})),e=n}return/^#[0-9A-F]{6}$/i.test(e)?(e.replace(/[0-9A-F]{2}/gi,(function(e){i.push(parseInt(e,16))})),"rgba(".concat(i.join(","),", ").concat(t,")")):"rgba(255, 255, 255, 0.1)"},W.getFullScreenEl=function(){return document.fullscreenElement||document.webkitFullscreenElement||document.mozFullScreenElement||document.msFullscreenElement},W.checkIsFunction=function(e){return e&&"function"==typeof e},W.checkIsObject=function(e){return null!==e&&"object"===k(e)},W.hide=function(e){e.style.display="none"},W.show=function(e,t){e.style.display=t||"block"},W.isUndefined=function(e){if(null==e)return!0},W.isNotNull=function(e){return!(null==e)},W.setStyleFromCsstext=function(e,t){t&&("String"===W.typeOf(t)?t.replace(/\s+/g,"").split(";").map((function(t){if(t){var i=t.split(":");i.length>1&&(e.style[i[0]]=i[1])}})):Object.keys(t).map((function(i){e.style[i]=t[i]})))},W.filterStyleFromText=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:["width","height","top","left","bottom","right","position","z-index","padding","margin","transform"],i=e.style.cssText;if(!i)return{};var n=i.replace(/\s+/g,"").split(";"),o={},r={};return n.map((function(e){if(e){var i=e.split(":");i.length>1&&(!function(e,t){for(var i=0,n=t.length;i<n;i++)if(e.indexOf(t[i])>-1)return!0;return!1}(i[0],t)?r[i[0]]=i[1]:o[i[0]]=i[1])}})),e.setAttribute("style",""),Object.keys(r).map((function(t){e.style[t]=r[t]})),o},W.getStyleFromCsstext=function(e){var t=e.style.cssText;if(!t)return{};var i=t.replace(/\s+/g,"").split(";"),n={};return i.map((function(e){if(e){var t=e.split(":");t.length>1&&(n[t[0]]=t[1])}})),n},W.preloadImg=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:function(){},i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:function(){};if(e){var n=new window.Image;n.onload=function(e){n=null,t&&t(e)},n.onerror=function(e){n=null,i&&i(e)},n.src=e}},W.stopPropagation=function(e){e&&e.stopPropagation()},W.scrollTop=function(){return window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0},W.scrollLeft=function(){return window.pageXOffset||document.documentElement.scrollLeft||document.body.scrollLeft||0},W.checkTouchSupport=function(){return"ontouchstart"in window},W.getBuffered2=function(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:.5,i=[],n=0;n<e.length;n++)i.push({start:e.start(n)<.5?0:e.start(n),end:e.end(n)});i.sort((function(e,t){var i=e.start-t.start;return i||t.end-e.end}));var o=[];if(t)for(var r=0;r<i.length;r++){var s=o.length;if(s){var a=o[s-1].end;i[r].start-a<t?i[r].end>a&&(o[s-1].end=i[r].end):o.push(i[r])}else o.push(i[r])}else o=i;return new V(o)},W.getEventPos=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;return e.touches&&e.touches.length>0&&(e=e.touches[0]),{x:e.x/t,y:e.y/t,clientX:e.clientX/t,clientY:e.clientY/t,offsetX:e.offsetX/t,offsetY:e.offsetY/t,pageX:e.pageX/t,pageY:e.pageY/t}},W.requestAnimationFrame=function(e){var t=window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.oRequestAnimationFrame||window.msRequestAnimationFrame;if(t)return t(e)},W.getHostFromUrl=function(e){if("String"!==W.typeOf(e))return"";var t=e.split("/"),i="";return t.length>3&&t[2]&&(i=t[2]),i},W.cancelAnimationFrame=function(e){var t=window.cancelAnimationFrame||window.mozCancelAnimationFrame||window.cancelRequestAnimationFrame;t&&t(e)},W.isMSE=function(e){return e.media&&(e=e.media),!!(e&&e instanceof HTMLMediaElement)&&(/^blob/.test(e.currentSrc)||/^blob/.test(e.src))},W.isBlob=function(e){return"string"==typeof e&&/^blob/.test(e)},W.generateSessionId=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=(new Date).getTime();try{e=parseInt(e)}catch(i){e=0}return t+=e,window.performance&&"function"==typeof window.performance.now&&(t+=parseInt(window.performance.now())),"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var i=(t+16*Math.random())%16|0;return t=Math.floor(t/16),("x"===e?i:3&i|8).toString(16)}))},W.createEvent=function(e){var t;return"function"==typeof window.Event?t=new Event(e):(t=document.createEvent("Event")).initEvent(e,!0,!0),t},W.adjustTimeByDuration=function(e,t,i){return t&&e&&(e>t||i&&e<t)?t:e},W.createPositionBar=function(e,t){var i=W.createDom("xg-bar","",{"data-index":-1},e);return t.appendChild(i),i},W.getTransformStyle=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{x:0,y:0,scale:1,rotate:0},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",i={scale:"".concat(e.scale||1),translate:"".concat(e.x||0,"%, ").concat(e.y||0,"%"),rotate:"".concat(e.rotate||0,"deg")};return Object.keys(i).forEach((function(e){var n=new RegExp("".concat(e,"\\([^\\(]+\\)"),"g"),o="".concat(e,"(").concat(i[e],")");n.test(t)?(n.lastIndex=-1,t=t.replace(n,o)):t+="".concat(o," ")})),t},W.convertDeg=function(e){return Math.abs(e)<=1?360*e:e%360},W.getIndexByTime=function(e,t){var i=t.length,n=-1;if(i<1)return n;if(e<=t[0].end||i<2)n=0;else if(e>t[i-1].end)n=i-1;else for(var o=1;o<i;o++)if(e>t[o-1].end&&e<=t[o].end){n=o;break}return n},W.getOffsetCurrentTime=function(e,t){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:-1,n=-1;if((n=i>=0&&i<t.length?i:W.getIndexByTime(e,t))<0)return-1;var o=t.length,r=t[n],s=r.start,a=r.end,l=r.cTime,c=r.offset;return e<s?l:e>=s&&e<=a?e-c:e>a&&n>=o-1?a:-1},W.getCurrentTimeByOffset=function(e,t){var i=-1;if(!t||t.length<0)return e;for(var n=0;n<t.length;n++)if(e<=t[n].duration){i=n;break}if(-1!==i){var o=t[i].start;return i-1<0?o+e:o+(e-t[i-1].duration)}return e};var K=/(Android)\s([\d.]+)/,Y=/(Version)\/([\d.]+)/,X=["avc1.42E01E, mp4a.40.2","avc1.58A01E, mp4a.40.2","avc1.4D401E, mp4a.40.2","avc1.64001E, mp4a.40.2","avc1.42E01E","mp4v.20.8","mp4v.20.8, mp4a.40.2","mp4v.20.240, mp4a.40.2"],q={get device(){return q.os.isPc?"pc":"mobile"},get browser(){if("undefined"==typeof navigator)return"";var e=navigator.userAgent.toLowerCase(),t={ie:/rv:([\d.]+)\) like gecko/,firefox:/firefox\/([\d.]+)/,chrome:/chrome\/([\d.]+)/,opera:/opera.([\d.]+)/,safari:/version\/([\d.]+).*safari/};return[].concat(Object.keys(t).filter((function(i){return t[i].test(e)})))[0]},get os(){if("undefined"==typeof navigator)return{};var e=navigator.userAgent,t=/(?:Windows Phone)/.test(e),i=/(?:SymbianOS)/.test(e)||t,n=/(?:Android)/.test(e),o=/(?:Firefox)/.test(e),r=/(?:iPad|PlayBook)/.test(e)||"MacIntel"===navigator.platform&&navigator.maxTouchPoints>1,s=r||n&&!/(?:Mobile)/.test(e)||o&&/(?:Tablet)/.test(e),a=/(?:iPhone)/.test(e)&&!s;return{isTablet:s,isPhone:a,isIpad:r,isIos:a||r,isAndroid:n,isPc:!(a||n||i||s),isSymbian:i,isWindowsPhone:t,isFireFox:o}},get osVersion(){if("undefined"==typeof navigator)return 0;var e=navigator.userAgent,t="",i=(t=/(?:iPhone)|(?:iPad|PlayBook)/.test(e)?Y:K)?t.exec(e):[];if(i&&i.length>=3){var n=i[2].split(".");return n.length>0?parseInt(n[0]):0}return 0},get isWeixin(){if("undefined"==typeof navigator)return!1;return!!/(micromessenger)\/([\d.]+)/.exec(navigator.userAgent.toLocaleLowerCase())},isSupportMP4:function(){var e={isSupport:!1,mime:""};if("undefined"==typeof document)return e;if(this.supportResult)return this.supportResult;var t=document.createElement("video");return"function"==typeof t.canPlayType&&X.map((function(i){"probably"===t.canPlayType('video/mp4; codecs="'.concat(i,'"'))&&(e.isSupport=!0,e.mime+="||".concat(i))})),this.supportResult=e,t=null,e},isMSESupport:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:'video/mp4; codecs="avc1.42E01E,mp4a.40.2"';if("undefined"==typeof MediaSource||!MediaSource)return!1;try{return MediaSource.isTypeSupported(e)}catch(t){return this._logger.error(e,t),!1}},isHevcSupported:function(){return!("undefined"==typeof MediaSource||!MediaSource.isTypeSupported)&&(MediaSource.isTypeSupported('video/mp4;codecs="hev1.1.6.L120.90"')||MediaSource.isTypeSupported('video/mp4;codecs="hev1.2.4.L120.90"')||MediaSource.isTypeSupported('video/mp4;codecs="hev1.3.E.L120.90"')||MediaSource.isTypeSupported('video/mp4;codecs="hev1.4.10.L120.90"'))},probeConfigSupported:function(e){var t={supported:!1,smooth:!1,powerEfficient:!1};if(!e||"undefined"==typeof navigator)return Promise.resolve(t);if(navigator.mediaCapabilities&&navigator.mediaCapabilities.decodingInfo)return navigator.mediaCapabilities.decodingInfo(e);var i=e.video||{},n=e.audio||{};try{var o=MediaSource.isTypeSupported(i.contentType),r=MediaSource.isTypeSupported(n.contentType);return Promise.resolve({supported:o&&r,smooth:!1,powerEfficient:!1})}catch(s){return Promise.resolve(t)}}},Z="3.0.20",J={1:"media",2:"media",3:"media",4:"media",5:"media",6:"media"},$={1:5101,2:5102,3:5103,4:5104,5:5105,6:5106},Q=_((function e(t){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{errorType:"",errorCode:0,errorMessage:"",originError:"",ext:{},mediaError:null};C(this,e);var n=t&&t.i18n?t.i18n.ERROR_TYPES:null;if(t.media){var o=i.mediaError?i.mediaError:t.media.error||{},r=t.duration,s=t.currentTime,a=t.ended,l=t.src,c=t.currentSrc,u=t.media,h=u.readyState,d=u.networkState,f=i.errorCode||o.code;$[f]&&(f=$[f]);var p={playerVersion:Z,currentTime:s,duration:r,ended:a,readyState:h,networkState:d,src:l||c,errorType:i.errorType,errorCode:f,message:i.errorMessage||o.message,mediaError:o,originError:i.originError?i.originError.stack:"",host:W.getHostFromUrl(l||c)};return i.ext&&Object.keys(i.ext).map((function(e){p[e]=i.ext[e]})),p}if(arguments.length>1){for(var g={playerVersion:Z,domain:document.domain},v=["errorType","currentTime","duration","networkState","readyState","src","currentSrc","ended","errd","errorCode","mediaError"],y=0;y<arguments.length;y++)g[v[y]]=arguments[y];return g.ex=n?(n[arguments[0]]||{}).msg:"",g}})),ee="play",te="playing",ie="ended",ne="pause",oe="error",re="seeking",se="seeked",ae="timeupdate",le="waiting",ce="canplay",ue="durationchange",he="volumechange",de="loadeddata",fe="ratechange",pe="progress",ge="loadstart",ve="emptied",ye="focus",me="blur",ke="ready",Ce="urlNull",be="autoplay_started",_e="autoplay_was_prevented",we="complete",Te="replay",xe="destroy",Se="urlchange",Ee="download_speed_change",Pe="leaveplayer",Ie="enterplayer",Le="loading",Ae="fullscreen_change",Oe="cssFullscreen_change",De="mini_state_change",Re="definition_change",Me="after_definition_change",Ne="video_resize",Fe="pip_change",He="rotate",Be="screenShot",Ue="playnext",je="shortcut",Ve="xglog",We="user_action",Ge="reset",ze="source_error",Ke="source_success",Ye=["play","playing","ended","pause","error","seeking","seeked","timeupdate","waiting","canplay","canplaythrough","durationchange","volumechange","loadeddata","loadedmetadata","ratechange","progress","loadstart","emptied","stalled","suspend","abort","lowdecode"],Xe={STATS_INFO:"stats_info",STATS_DOWNLOAD:"stats_download",STATS_RESET:"stats_reset"},qe="fps_stuck";const Ze=Object.freeze(Object.defineProperty({__proto__:null,ABORT:"abort",AFTER_DEFINITION_CHANGE:Me,AUTOPLAY_PREVENTED:_e,AUTOPLAY_STARTED:be,BEFORE_DEFINITION_CHANGE:"before_definition_change",BUFFER_CHANGE:"bufferedChange",CANPLAY:ce,CANPLAY_THROUGH:"canplaythrough",COMPLETE:we,CSS_FULLSCREEN_CHANGE:Oe,DEFINITION_CHANGE:Re,DESTROY:xe,DOWNLOAD_SPEED_CHANGE:Ee,DURATION_CHANGE:ue,EMPTIED:ve,ENDED:ie,ENTER_PLAYER:Ie,ERROR:oe,FPS_STUCK:qe,FULLSCREEN_CHANGE:Ae,LEAVE_PLAYER:Pe,LOADED_DATA:de,LOADED_METADATA:"loadedmetadata",LOADING:Le,LOAD_START:ge,MINI_STATE_CHANGE:De,PAUSE:ne,PIP_CHANGE:Fe,PLAY:ee,PLAYER_BLUR:me,PLAYER_FOCUS:ye,PLAYING:te,PLAYNEXT:Ue,PROGRESS:pe,RATE_CHANGE:fe,READY:ke,REPLAY:Te,RESET:Ge,RETRY:"retry",ROTATE:He,SCREEN_SHOT:Be,SEEKED:se,SEEKING:re,SEI_PARSED:"SEI_PARSED",SHORTCUT:je,SOURCE_ERROR:ze,SOURCE_SUCCESS:Ke,STALLED:"stalled",STATS_EVENTS:Xe,SUSPEND:"suspend",SWITCH_SUBTITLE:"switch_subtitle",TIME_UPDATE:ae,URL_CHANGE:Se,URL_NULL:Ce,USER_ACTION:We,VIDEO_EVENTS:Ye,VIDEO_RESIZE:Ne,VOLUME_CHANGE:he,WAITING:le,XGLOG:Ve},Symbol.toStringTag,{value:"Module"}));function Je(e,t){this&&this.emit&&("error"===e?this.errorHandler(e,t.error):this.emit(e,t))}var $e=function(){T(t,F);var e=I(t);function t(i){var n;C(this,t),(n=e.call(this,i))._hasStart=!1,n._currentTime=0,n._duration=0,n._internalOp={},n._lastMuted=!1,n.vtype="MP4",n._rate=-1,n.mediaConfig=Object.assign({},{controls:!1,autoplay:i.autoplay,playsinline:i.playsinline,"x5-playsinline":i.playsinline,"webkit-playsinline":i.playsinline,"x5-video-player-fullscreen":i["x5-video-player-fullscreen"]||i.x5VideoPlayerFullscreen,"x5-video-orientation":i["x5-video-orientation"]||i.x5VideoOrientation,airplay:i.airplay,"webkit-airplay":i.airplay,tabindex:0|i.tabindex,mediaType:i.mediaType||"video","data-index":-1},i.videoConfig,i.videoAttributes);var o=i["x5-video-player-type"]||i.x5VideoPlayerType;return q.isWeixin&&q.os.isAndroid&&o&&(n.mediaConfig["x5-video-player-type"]=o,delete n.mediaConfig.playsinline,delete n.mediaConfig["webkit-playsinline"],delete n.mediaConfig["x5-playsinline"]),i.loop&&(n.mediaConfig.loop="loop"),i.autoplayMuted&&!Object.prototype.hasOwnProperty.call(n.mediaConfig,"muted")&&(n.mediaConfig.muted=!0),n.media=W.createDom(n.mediaConfig.mediaType,"",n.mediaConfig,""),i.defaultPlaybackRate&&(n.media.defaultPlaybackRate=n.media.playbackRate=i.defaultPlaybackRate),"Number"===W.typeOf(i.volume)&&(n.volume=i.volume),i.autoplayMuted&&(n.media.muted=!0,n._lastMuted=!0),i.autoplay&&(n.media.autoplay=!0),n._interval={},n.mediaEventMiddleware={},n.attachVideoEvents(),n}return _(t,[{key:"setEventsMiddleware",value:function(e){var t=this;Object.keys(e).map((function(i){t.mediaEventMiddleware[i]=e[i]}))}},{key:"removeEventsMiddleware",value:function(e){var t=this;Object.keys(e).map((function(e){delete t.mediaEventMiddleware[e]}))}},{key:"attachVideoEvents",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.media;this._evHandlers||(this._evHandlers=Ye.map((function(t){var i="on".concat(t.charAt(0).toUpperCase()).concat(t.slice(1));return"function"==typeof e[i]&&e.on(t,e[i]),w({},t,function(e,t){return function(i,n){var o={player:t,eventName:e,originalEvent:i,detail:i.detail||{},timeStamp:i.timeStamp,currentTime:t.currentTime,duration:t.duration,paused:t.paused,ended:t.ended,isInternalOp:!!t._internalOp[i.type],muted:t.muted,volume:t.volume,host:W.getHostFromUrl(t.currentSrc),vtype:t.vtype};if(t.removeInnerOP(i.type),"timeupdate"===e&&(t._currentTime=t.media&&t.media.currentTime),"ratechange"===e){var r=t.media?t.media.playbackRate:0;if(r&&t._rate===r)return;t._rate=t.media&&t.media.playbackRate}if("durationchange"===e&&(t._duration=t.media.duration),"volumechange"===e&&(o.isMutedChange=t._lastMuted!==t.muted,t._lastMuted=t.muted),"error"===e&&(o.error=n||t.video.error),t.mediaEventMiddleware[e]){var s=Je.bind(t,e,o);try{t.mediaEventMiddleware[e].call(t,o,s)}catch(a){throw Je.call(t,e,o),a}}else Je.call(t,e,o)}}(t,e))}))),this._evHandlers.forEach((function(e){var i=Object.keys(e)[0];t.addEventListener(i,e[i],!1)}))}},{key:"detachVideoEvents",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.media;this._evHandlers.forEach((function(e){var i=Object.keys(e)[0];t.removeEventListener(i,e[i],!1)})),this._evHandlers.forEach((function(t){var i=Object.keys(t)[0],n="on".concat(i.charAt(0).toUpperCase()).concat(i.slice(1));"function"==typeof e[n]&&e.off(i,e[n])})),this._evHandlers=null}},{key:"_attachSourceEvents",value:function(e,t){var i=this;e.removeAttribute("src"),e.load(),t.forEach((function(e,t){i.media.appendChild(W.createDom("source","",{src:"".concat(e.src),type:"".concat(e.type||""),"data-index":t+1}))}));var n=e.children;if(n){this._videoSourceCount=n.length,this._videoSourceIndex=n.length,this._vLoadeddata=function(e){i.emit(Ke,{src:e.target.currentSrc,host:W.getHostFromUrl(e.target.currentSrc)})};for(var o=null,r=0;r<this._evHandlers.length;r++)if("error"===Object.keys(this._evHandlers[r])[0]){o=this._evHandlers[r];break}!this._sourceError&&(this._sourceError=function(e){var t=parseInt(e.target.getAttribute("data-index"),10);if(i._videoSourceIndex--,0===i._videoSourceIndex||t>=i._videoSourceCount){var n={code:4,message:"sources_load_error"};o?o.error(e,n):i.errorHandler("error",n)}var r=J[4];i.emit(ze,new Q(i,{errorType:r,errorCode:4,errorMessage:"sources_load_error",mediaError:{code:4,message:"sources_load_error"},src:e.target.src}))});for(var s=0;s<n.length;s++)n[s].addEventListener("error",this._sourceError);e.addEventListener("loadeddata",this._vLoadeddata)}}},{key:"_detachSourceEvents",value:function(e){var t=e.children;if(t&&0!==t.length&&this._sourceError){for(var i=0;i<t.length;i++)t[i].removeEventListener("error",this._sourceError);for(;t.length>0;)e.removeChild(t[0]);this._vLoadeddata&&e.removeEventListener("loadeddata",this._vLoadeddata)}}},{key:"errorHandler",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(this.media&&(this.media.error||t)){var i=this.media.error||t,n=i.code?J[i.code]:"other";i.message;this.media.currentSrc||(i={code:6,message:"empty_src"}),this.emit(e,new Q(this,{errorType:n,errorCode:i.code,errorMessage:i.message||"",mediaError:i}))}}},{key:"destroy",value:function(){for(var e in this.media&&(this.media.pause&&(this.media.pause(),this.media.muted=!0),this.media.removeAttribute("src"),this.media.load()),this._currentTime=0,this._duration=0,this.mediaConfig=null,this._interval)Object.prototype.hasOwnProperty.call(this._interval,e)&&(clearInterval(this._interval[e]),this._interval[e]=null);this.detachVideoEvents(),this.media=null,this.mediaEventMiddleware={},this.removeAllListeners()}},{key:"video",get:function(){return this.media},set:function(e){this.media=e}},{key:"play",value:function(){return this.media?this.media.play():null}},{key:"pause",value:function(){this.media&&this.media.pause()}},{key:"load",value:function(){this.media&&this.media.load()}},{key:"canPlayType",value:function(e){return!!this.media&&this.media.canPlayType(e)}},{key:"getBufferedRange",value:function(e){var t=[0,0];if(!this.media)return t;e||(e=this.media.buffered);var i=this.media.currentTime;if(e)for(var n=0,o=e.length;n<o&&(t[0]=e.start(n),t[1]=e.end(n),!(t[0]<=i&&i<=t[1]));n++);return t[0]-i<=0&&i-t[1]<=0?t:[0,0]}},{key:"autoplay",get:function(){return!!this.media&&this.media.autoplay},set:function(e){this.media&&(this.media.autoplay=e)}},{key:"buffered",get:function(){return this.media?this.media.buffered:null}},{key:"buffered2",get:function(){return this.media&&this.media.buffered?W.getBuffered2(this.media.buffered):null}},{key:"bufferedPoint",get:function(){var e={start:0,end:0};if(!this.media)return e;var t=this.media.buffered;if(!t||0===t.length)return e;for(var i=0;i<t.length;i++)if((t.start(i)<=this.currentTime||t.start(i)<.1)&&t.end(i)>=this.currentTime)return{start:t.start(i),end:t.end(i)};return e}},{key:"crossOrigin",get:function(){return this.media?this.media.crossOrigin:""},set:function(e){this.media&&(this.media.crossOrigin=e)}},{key:"currentSrc",get:function(){return this.media?this.media.currentSrc:""},set:function(e){this.media&&(this.media.currentSrc=e)}},{key:"currentTime",get:function(){return this.media?void 0!==this.media.currentTime?this.media.currentTime:this._currentTime:0},set:function(e){this.media&&(this.media.currentTime=e)}},{key:"defaultMuted",get:function(){return!!this.media&&this.media.defaultMuted},set:function(e){this.media&&(this.media.defaultMuted=e)}},{key:"duration",get:function(){return this._duration}},{key:"ended",get:function(){return!!this.media&&this.media.ended}},{key:"error",get:function(){return this.media.error}},{key:"errorNote",get:function(){if(!this.media.error)return"";return["MEDIA_ERR_ABORTED","MEDIA_ERR_NETWORK","MEDIA_ERR_DECODE","MEDIA_ERR_SRC_NOT_SUPPORTED"][this.media.error.code-1]}},{key:"loop",get:function(){return!!this.media&&this.media.loop},set:function(e){this.media&&(this.media.loop=e)}},{key:"muted",get:function(){return!!this.media&&this.media.muted},set:function(e){this.media&&this.media.muted!==e&&(this._lastMuted=this.media.muted,this.media.muted=e)}},{key:"networkState",get:function(){return this.media.networkState}},{key:"paused",get:function(){return!this.media||this.media.paused}},{key:"playbackRate",get:function(){return this.media?this.media.playbackRate:0},set:function(e){this.media&&e!==1/0&&(this.media.defaultPlaybackRate=e,this.media.playbackRate=e)}},{key:"played",get:function(){return this.media?this.media.played:null}},{key:"preload",get:function(){return!!this.media&&this.media.preload},set:function(e){this.media&&(this.media.preload=e)}},{key:"readyState",get:function(){return this.media.readyState}},{key:"seekable",get:function(){return!!this.media&&this.media.seekable}},{key:"seeking",get:function(){return!!this.media&&this.media.seeking}},{key:"src",get:function(){return this.media?this.media.src:""},set:function(e){this.media&&(this.emit(Se,e),this.emit(le),this._currentTime=0,this._duration=0,W.isMSE(this.media)?this.onWaiting():(this._detachSourceEvents(this.media),"Array"===W.typeOf(e)?this._attachSourceEvents(this.media,e):e?this.media.src=e:this.media.removeAttribute("src"),this.load()))}},{key:"volume",get:function(){return this.media?this.media.volume:0},set:function(e){e!==1/0&&this.media&&(this.media.volume=e)}},{key:"aspectRatio",get:function(){return this.media?this.media.videoWidth/this.media.videoHeight:0}},{key:"addInnerOP",value:function(e){this._internalOp[e]=!0}},{key:"removeInnerOP",value:function(e){delete this._internalOp[e]}},{key:"emit",value:function(e,i){for(var n,o=arguments.length,r=new Array(o>2?o-2:0),s=2;s<o;s++)r[s-2]=arguments[s];(n=L(x(t.prototype),"emit",this)).call.apply(n,[this,e,i].concat(r))}},{key:"on",value:function(e,i){for(var n,o=arguments.length,r=new Array(o>2?o-2:0),s=2;s<o;s++)r[s-2]=arguments[s];(n=L(x(t.prototype),"on",this)).call.apply(n,[this,e,i].concat(r))}},{key:"once",value:function(e,i){for(var n,o=arguments.length,r=new Array(o>2?o-2:0),s=2;s<o;s++)r[s-2]=arguments[s];(n=L(x(t.prototype),"once",this)).call.apply(n,[this,e,i].concat(r))}},{key:"off",value:function(e,i){for(var n,o=arguments.length,r=new Array(o>2?o-2:0),s=2;s<o;s++)r[s-2]=arguments[s];(n=L(x(t.prototype),"off",this)).call.apply(n,[this,e,i].concat(r))}},{key:"offAll",value:function(){L(x(t.prototype),"removeAllListeners",this).call(this)}}]),t}(),Qe=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{name:"xgplayer",version:1,db:null,ojstore:{name:"xg-m4a",keypath:"vid"}};C(this,e),this.indexedDB=window.indexedDB||window.webkitindexedDB,this.IDBKeyRange=window.IDBKeyRange||window.webkitIDBKeyRange,this.myDB=t}return _(e,[{key:"openDB",value:function(e){var t=this,i=this,n=this.myDB.version||1,o=i.indexedDB.open(i.myDB.name,n);o.onerror=function(e){},o.onsuccess=function(n){t.myDB.db=n.target.result,e.call(i)},o.onupgradeneeded=function(e){var t=e.target.result;e.target.transaction,t.objectStoreNames.contains(i.myDB.ojstore.name)||t.createObjectStore(i.myDB.ojstore.name,{keyPath:i.myDB.ojstore.keypath})}}},{key:"deletedb",value:function(){this.indexedDB.deleteDatabase(this.myDB.name)}},{key:"closeDB",value:function(){this.myDB.db.close()}},{key:"addData",value:function(e,t){for(var i,n=this.myDB.db.transaction(e,"readwrite").objectStore(e),o=0;o<t.length;o++)(i=n.add(t[o])).onerror=function(){},i.onsuccess=function(){}}},{key:"putData",value:function(e,t){for(var i,n=this.myDB.db.transaction(e,"readwrite").objectStore(e),o=0;o<t.length;o++)(i=n.put(t[o])).onerror=function(){},i.onsuccess=function(){}}},{key:"getDataByKey",value:function(e,t,i){var n=this,o=this.myDB.db.transaction(e,"readwrite").objectStore(e).get(t);o.onerror=function(){i.call(n,null)},o.onsuccess=function(e){var t=e.target.result;i.call(n,t)}}},{key:"deleteData",value:function(e,t){this.myDB.db.transaction(e,"readwrite").objectStore(e).delete(t)}},{key:"clearData",value:function(e){this.myDB.db.transaction(e,"readwrite").objectStore(e).clear()}}]),e}(),et=["fullscreenchange","webkitfullscreenchange","mozfullscreenchange","MSFullscreenChange"],tt=["requestFullscreen","webkitRequestFullscreen","mozRequestFullScreen","msRequestFullscreen"],it=["exitFullscreen","webkitExitFullscreen","mozCancelFullScreen","msExitFullscreen"],nt="data-xgplayerid";function ot(e,t,i){for(var n=arguments.length,o=new Array(n>3?n-3:0),r=3;r<n;r++)o[r-3]=arguments[r];var s=t.call.apply(t,[e].concat(o));i&&"function"==typeof i&&(s&&s.then?s.then((function(){for(var t=arguments.length,n=new Array(t),o=0;o<t;o++)n[o]=arguments[o];i.call.apply(i,[e].concat(n))})):i.call.apply(i,[e].concat(o)))}function rt(e,t){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{pre:null,next:null};return this.__hooks||(this.__hooks={}),!this.__hooks[e]&&(this.__hooks[e]=null),function(){var n=arguments,o=this;if(i.pre)try{var r;(r=i.pre).call.apply(r,[this].concat(Array.prototype.slice.call(arguments)))}catch(a){throw a.message="[pluginName: ".concat(this.pluginName,":").concat(e,":pre error] >> ").concat(a.message),a}if(this.__hooks&&this.__hooks[e])try{var s=ft(this,e,t);s?s.then?s.then((function(e){!1!==e&&ot.apply(void 0,[o,t,i.next].concat(A(n)))})).catch((function(e){throw e})):ot.apply(void 0,[this,t,i.next].concat(Array.prototype.slice.call(arguments))):void 0===s&&ot.apply(void 0,[this,t,i.next].concat(Array.prototype.slice.call(arguments)))}catch(a){throw a.message="[pluginName: ".concat(this.pluginName,":").concat(e,"] >> ").concat(a.message),a}else ot.apply(void 0,[this,t,i.next].concat(Array.prototype.slice.call(arguments)))}.bind(this)}function st(e,t){var i=this.__hooks;if(!i||!Array.isArray(i[e]))return-1;for(var n=i[e],o=0;o<n.length;o++)if(n[o]===t)return o;return-1}function at(e,t){var i=this.__hooks;if(i)return!!i.hasOwnProperty(e)&&(Array.isArray(i[e])||(i[e]=[]),-1===st.call(this,e,t)&&i[e].push(t),!0)}function lt(e,t){var i=this.__hooks;if(i){if(Array.isArray(i[e])){var n=i[e],o=st.call(this,e,t);-1!==o&&n.splice(o,1)}delete i[e]}}function ct(e){if(this.plugins&&this.plugins[e.toLowerCase()]){for(var t=this.plugins[e.toLowerCase()],i=arguments.length,n=new Array(i>1?i-1:0),o=1;o<i;o++)n[o-1]=arguments[o];return t.useHooks&&t.useHooks.apply(t,n)}}function ut(e){if(this.plugins&&this.plugins[e.toLowerCase()]){var t=this.plugins[e.toLowerCase()];if(t){for(var i=arguments.length,n=new Array(i>1?i-1:0),o=1;o<i;o++)n[o-1]=arguments[o];return t.removeHooks&&t.removeHooks.apply(t,n)}}}function ht(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];e.__hooks={},t&&t.map((function(t){e.__hooks[t]=null})),Object.defineProperty(e,"hooks",{get:function(){return e.__hooks&&Object.keys(e.__hooks).map((function(t){if(e.__hooks[t])return t}))}})}function dt(e){e.__hooks=null}function ft(e,t,i){for(var n=arguments.length,o=new Array(n>3?n-3:0),r=3;r<n;r++)o[r-3]=arguments[r];if(e.__hooks&&Array.isArray(e.__hooks[t])){var s=e.__hooks[t],a=-1;return function e(t,i,n){for(var o=arguments.length,r=new Array(o>3?o-3:0),l=3;l<o;l++)r[l-3]=arguments[l];if(a++,0===s.length||a===s.length)return n.call.apply(n,[t,t].concat(r));var c=s[a],u=c.call.apply(c,[t,t].concat(r));return u&&u.then?u.then((function(o){return!1===o?null:e.apply(void 0,[t,i,n].concat(r))})).catch((function(e){})):!1!==u?e.apply(void 0,[t,i,n].concat(r)):void 0}.apply(void 0,[e,t,i].concat(o))}return i.call.apply(i,[e,e].concat(o))}function pt(e,t){j.logError("[".concat(e,"] event or callback cant be undefined or null when call ").concat(t))}var gt,vt,yt,mt,kt=function(){function e(t){C(this,e),W.checkIsFunction(this.beforeCreate)&&this.beforeCreate(t),ht(this),this.__args=t,this.__events={},this.__onceEvents={},this.config=t.config||{},this.player=null,this.playerConfig={},this.pluginName="",this.__init(t)}return _(e,[{key:"beforeCreate",value:function(e){}},{key:"afterCreate",value:function(){}},{key:"beforePlayerInit",value:function(){}},{key:"onPluginsReady",value:function(){}},{key:"afterPlayerInit",value:function(){}},{key:"destroy",value:function(){}},{key:"__init",value:function(e){this.player=e.player,this.playerConfig=e.player&&e.player.config,this.pluginName=e.pluginName?e.pluginName.toLowerCase():this.constructor.pluginName.toLowerCase(),this.logger=e.player&&e.player.logger}},{key:"updateLang",value:function(e){e||(e=this.lang)}},{key:"lang",get:function(){return this.player.lang}},{key:"i18n",get:function(){return this.player.i18n}},{key:"i18nKeys",get:function(){return this.player.i18nKeys}},{key:"domEventType",get:function(){var e=W.checkTouchSupport()?"touch":"mouse";return!this.playerConfig||"touch"!==this.playerConfig.domEventType&&"mouse"!==this.playerConfig.domEventType||(e=this.playerConfig.domEventType),e}},{key:"on",value:function(e,t){var i=this;e&&t&&this.player?"string"==typeof e?(this.__events[e]=t,this.player.on(e,t)):Array.isArray(e)&&e.forEach((function(e){i.__events[e]=t,i.player.on(e,t)})):pt(this.pluginName,"plugin.on(event, callback)")}},{key:"once",value:function(e,t){var i=this;e&&t&&this.player?"string"==typeof e?(this.__onceEvents[e]=t,this.player.once(e,t)):Array.isArray(e)&&e.forEach((function(n){i.__onceEvents[n]=t,i.player.once(e,t)})):pt(this.pluginName,"plugin.once(event, callback)")}},{key:"off",value:function(e,t){var i=this;e&&t&&this.player?"string"==typeof e?(delete this.__events[e],this.player.off(e,t)):Array.isArray(e)&&e.forEach((function(n){delete i.__events[e],i.player.off(n,t)})):pt(this.pluginName,"plugin.off(event, callback)")}},{key:"offAll",value:function(){var e=this;["__events","__onceEvents"].forEach((function(t){Object.keys(e[t]).forEach((function(i){e[t][i]&&e.off(i,e[t][i]),i&&delete e[t][i]}))})),this.__events={},this.__onceEvents={}}},{key:"emit",value:function(e){var t;if(this.player){for(var i=arguments.length,n=new Array(i>1?i-1:0),o=1;o<i;o++)n[o-1]=arguments[o];(t=this.player).emit.apply(t,[e].concat(n))}}},{key:"emitUserAction",value:function(e,t){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(this.player){var n=m(m({},i),{},{pluginName:this.pluginName});this.player.emitUserAction(e,t,n)}}},{key:"hook",value:function(e,t){return rt.call.apply(rt,[this].concat(Array.prototype.slice.call(arguments)))}},{key:"useHooks",value:function(e,t){for(var i=arguments.length,n=new Array(i>2?i-2:0),o=2;o<i;o++)n[o-2]=arguments[o];return at.call.apply(at,[this].concat(Array.prototype.slice.call(arguments)))}},{key:"removeHooks",value:function(e,t){for(var i=arguments.length,n=new Array(i>2?i-2:0),o=2;o<i;o++)n[o-2]=arguments[o];return lt.call.apply(lt,[this].concat(Array.prototype.slice.call(arguments)))}},{key:"registerPlugin",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";if(this.player)return i&&(t.pluginName=i),this.player.registerPlugin({plugin:e,options:t})}},{key:"getPlugin",value:function(e){return this.player?this.player.getPlugin(e):null}},{key:"__destroy",value:function(){var e=this,t=this.player,i=this.pluginName;this.offAll(),W.clearAllTimers(this),W.checkIsFunction(this.destroy)&&this.destroy(),["player","playerConfig","pluginName","logger","__args","__hooks"].map((function(t){e[t]=null})),t.unRegisterPlugin(i),dt(this)}}],[{key:"defineGetterOrSetter",value:function(e,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&Object.defineProperty(e,i,t[i])}},{key:"defineMethod",value:function(e,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&"function"==typeof t[i]&&Object.defineProperty(e,i,{configurable:!0,value:t[i]})}},{key:"defaultConfig",get:function(){return{}}},{key:"pluginName",get:function(){return"pluginName"}}]),e}();const Ct=s(function(){if(mt)return yt;mt=1;var e=function(){if(vt)return gt;if(vt=1,"undefined"!=typeof Element&&!Element.prototype.matches){var e=Element.prototype;e.matches=e.matchesSelector||e.mozMatchesSelector||e.msMatchesSelector||e.oMatchesSelector||e.webkitMatchesSelector}return gt=function(e,t){for(;e&&9!==e.nodeType;){if("function"==typeof e.matches&&e.matches(t))return e;e=e.parentNode}}}();function t(e,t,n,o,r){var s=i.apply(this,arguments);return e.addEventListener(n,s,r),{destroy:function(){e.removeEventListener(n,s,r)}}}function i(t,i,n,o){return function(n){n.delegateTarget=e(n.target,i),n.delegateTarget&&o.call(t,n)}}return yt=function(e,i,n,o,r){return"function"==typeof e.addEventListener?t.apply(null,arguments):"function"==typeof n?t.bind(null,document).apply(null,arguments):("string"==typeof e&&(e=document.querySelectorAll(e)),Array.prototype.map.call(e,(function(e){return t(e,i,n,o,r)})))}}());var bt={CONTROLS:"controls",ROOT:"root"},_t={ROOT:"root",ROOT_LEFT:"rootLeft",ROOT_RIGHT:"rootRight",ROOT_TOP:"rootTop",CONTROLS_LEFT:"controlsLeft",CONTROLS_RIGTH:"controlsRight",CONTROLS_RIGHT:"controlsRight",CONTROLS_CENTER:"controlsCenter",CONTROLS:"controls"},wt="xg-icon-disable";function Tt(e){return!!e&&(e.indexOf&&/^(?:http|data:|\/)/.test(e))}function xt(e,t){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:"",r=null;if(e instanceof window.Element)return W.addClass(e,i),Object.keys(n).map((function(t){e.setAttribute(t,n[t])})),e;if(Tt(e)||Tt(e.url))return n.src=Tt(e)?e:e.url||"",r=W.createDom(e.tag||"img","",n,"xg-img ".concat(i));if("function"==typeof e)try{return(r=e())instanceof window.Element?(W.addClass(r,i),Object.keys(n).map((function(e){r.setAttribute(e,n[e])})),r):(j.logWarn("warn>>icons.".concat(t," in config of plugin named [").concat(o,"] is a function mast return an Element Object")),null)}catch(s){return j.logError("Plugin named [".concat(o,"]:createIcon"),s),null}return"string"==typeof e?W.createDomFromHtml(e,n,i):(j.logWarn("warn>>icons.".concat(t," in config of plugin named [").concat(o,"] is invalid")),null)}function St(e,t){var i=t.config.icons||t.playerConfig.icons;Object.keys(e).map((function(n){var o=e[n],r=o&&o.class?o.class:"",s=o&&o.attr?o.attr:{},a=null;i&&i[n]&&(r=function(e,t){return"object"===k(e)&&e.class&&"string"==typeof e.class?"".concat(t," ").concat(e.class):t}(i[n],r),s=function(e,t){return"object"===k(e)&&e.attr&&"object"===k(e.attr)&&Object.keys(e.attr).map((function(i){t[i]=e.attr[i]})),t}(i[n],s),a=xt(i[n],n,r,s,t.pluginName)),!a&&o&&(a=xt(o.icon?o.icon:o,s,r,{},t.pluginName)),t.icons[n]=a}))}var Et=function(){T(t,kt);var e=I(t);function t(){var i,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return C(this,t),(i=e.call(this,n)).__delegates=[],i}return _(t,[{key:"__init",value:function(e){if(L(x(t.prototype),"__init",this).call(this,e),e.root){var i=e.root,n=null;this.icons={},this.root=null,this.parent=null,St(this.registerIcons()||{},this),this.langText={};var o,r,s=this.registerLanguageTexts()||{};o=s,r=this,Object.keys(o).map((function(e){Object.defineProperty(r.langText,e,{get:function(){var t=r.lang,i=r.i18n;return i[e]?i[e]:o[e]&&o[e][t]||""}})}));var a="";try{a=this.render()}catch(u){throw j.logError("Plugin:".concat(this.pluginName,":render"),u),new Error("Plugin:".concat(this.pluginName,":render:").concat(u.message))}if(a)(n=t.insert(a,i,e.index)).setAttribute("data-index",e.index);else{if(!e.tag)return;(n=W.createDom(e.tag,"",e.attr,e.name)).setAttribute("data-index",e.index),i.appendChild(n)}this.root=n,this.parent=i;var l=this.config.attr||{},c=this.config.style||{};this.setAttr(l),this.setStyle(c),this.config.index&&this.root.setAttribute("data-index",this.config.index),this.__registerChildren()}}},{key:"__registerChildren",value:function(){var e=this;if(this.root){this._children=[];var t=this.children();t&&"object"===k(t)&&Object.keys(t).length>0&&Object.keys(t).map((function(i){var n,o,r=i,s=t[r],a={root:e.root};"function"==typeof s?(n=e.config[r]||{},o=s):"object"===k(s)&&"function"==typeof s.plugin&&(n=s.options?W.deepCopy(e.config[r]||{},s.options):e.config[r]||{},o=s.plugin),a.config=n,void 0!==n.index&&(a.index=n.index),n.root&&(a.root=n.root),e.registerPlugin(o,a,r)}))}}},{key:"updateLang",value:function(e){e||(e=this.lang);var t=this.root,i=this.i18n,n=this.langText;t&&function e(t,i){for(var n=0;n<t.children.length;n++)t.children[n].children.length>0?e(t.children[n],i):i(t.children[n])}(t,(function(t){var o=t.getAttribute&&t.getAttribute("lang-key");if(o){var r=i[o.toUpperCase()]||n[o];r&&(t.innerHTML="function"==typeof r?r(e):r)}}))}},{key:"lang",get:function(){return this.player.lang}},{key:"changeLangTextKey",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",i=this.i18n||{},n=this.langText;e.setAttribute&&e.setAttribute("lang-key",t);var o=i[t]||n[t]||"";o&&(e.innerHTML=o)}},{key:"plugins",value:function(){return this._children}},{key:"disable",value:function(){this.config.disable=!0,W.addClass(this.find(".xgplayer-icon"),wt)}},{key:"enable",value:function(){this.config.disable=!1,W.removeClass(this.find(".xgplayer-icon"),wt)}},{key:"children",value:function(){return{}}},{key:"registerPlugin",value:function(e){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";i.root=i.root||this.root;var o=L(x(t.prototype),"registerPlugin",this).call(this,e,i,n);return this._children.push(o),o}},{key:"registerIcons",value:function(){return{}}},{key:"registerLanguageTexts",value:function(){return{}}},{key:"find",value:function(e){if(this.root)return this.root.querySelector(e)}},{key:"bind",value:function(e,i,n){var o=this;if(arguments.length<3&&"function"==typeof i)Array.isArray(e)?e.forEach((function(e){o.bindEL(e,i)})):this.bindEL(e,i);else{var r=t.delegate.call(this,this.root,e,i,n);this.__delegates=this.__delegates.concat(r)}}},{key:"unbind",value:function(e,t){var i=this;if(arguments.length<3&&"function"==typeof t)Array.isArray(e)?e.forEach((function(e){i.unbindEL(e,t)})):this.unbindEL(e,t);else for(var n="".concat(e,"_").concat(t),o=0;o<this.__delegates.length;o++)if(this.__delegates[o].key===n){this.__delegates[o].destroy(),this.__delegates.splice(o,1);break}}},{key:"setStyle",value:function(e,t){var i=this;if(this.root)return"String"===W.typeOf(e)?this.root.style[e]=t:void("Object"===W.typeOf(e)&&Object.keys(e).map((function(t){i.root.style[t]=e[t]})))}},{key:"setAttr",value:function(e,t){var i=this;if(this.root)return"String"===W.typeOf(e)?this.root.setAttribute(e,t):void("Object"===W.typeOf(e)&&Object.keys(e).map((function(t){i.root.setAttribute(t,e[t])})))}},{key:"setHtml",value:function(e,t){this.root&&(this.root.innerHTML=e,"function"==typeof t&&t())}},{key:"bindEL",value:function(e,t){var i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];this.root&&"on".concat(e)in this.root&&"function"==typeof t&&this.root.addEventListener(e,t,i)}},{key:"unbindEL",value:function(e,t){var i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];this.root&&"on".concat(e)in this.root&&"function"==typeof t&&this.root.removeEventListener(e,t,i)}},{key:"show",value:function(e){if(this.root)return this.root.style.display=void 0!==e?e:"block","none"===window.getComputedStyle(this.root,null).getPropertyValue("display")?this.root.style.display="block":void 0}},{key:"hide",value:function(){this.root&&(this.root.style.display="none")}},{key:"appendChild",value:function(e,t){if(!this.root)return null;if(arguments.length<2&&arguments[0]instanceof window.Element)return this.root.appendChild(arguments[0]);if(!(t&&t instanceof window.Element))return null;try{return"string"==typeof e?this.find(e).appendChild(t):e.appendChild(t)}catch(i){return j.logError("Plugin:appendChild",i),null}}},{key:"render",value:function(){return""}},{key:"destroy",value:function(){}},{key:"__destroy",value:function(){var e=this,i=this.player;this.__delegates.map((function(e){e.destroy()})),this.__delegates=[],this._children instanceof Array&&(this._children.map((function(e){i.unRegisterPlugin(e.pluginName)})),this._children=null),this.root&&(this.root.hasOwnProperty("remove")?this.root.remove():this.root.parentNode&&this.root.parentNode.removeChild(this.root)),L(x(t.prototype),"__destroy",this).call(this),this.icons={},["root","parent"].map((function(t){e[t]=null}))}}],[{key:"insert",value:function(e,t){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,n=t.children.length,o=Number(i),r=e instanceof window.Node;if(n){for(var s=0,a=null,l="";s<n;s++){a=t.children[s];var c=Number(a.getAttribute("data-index"));if(c>=o){l="beforebegin";break}c<o&&(l="afterend")}return r?"afterend"===l?t.appendChild(e):t.insertBefore(e,a):a.insertAdjacentHTML(l,e),"afterend"===l?t.children[t.children.length-1]:t.children[s]}return r?t.appendChild(e):t.insertAdjacentHTML("beforeend",e),t.children[t.children.length-1]}},{key:"defaultConfig",get:function(){return{}}},{key:"delegate",value:function(e,t,i,n){var o=arguments.length>4&&void 0!==arguments[4]&&arguments[4],r=[];if(e instanceof window.Node&&"function"==typeof n)if(Array.isArray(i))i.forEach((function(i){var s=Ct(e,t,i,n,o);s.key="".concat(t,"_").concat(i),r.push(s)}));else{var s=Ct(e,t,i,n,o);s.key="".concat(t,"_").concat(i),r.push(s)}return r}},{key:"ROOT_TYPES",get:function(){return bt}},{key:"POSITIONS",get:function(){return _t}}]),t}(),Pt=function(){function e(){var t=this;if(C(this,e),w(this,"__trigger",(function(e){var i=(new Date).getTime();t.timeStamp=i;for(var n=0;n<e.length;n++)t.__runHandler(e[n].target)})),this.__handlers=[],this.timeStamp=0,this.observer=null,window.ResizeObserver)try{this.observer=new window.ResizeObserver(function(e,t,i){var n=!0,o=!0;if("function"!=typeof e)throw new TypeError("Expected a function");return G(i)&&(n="leading"in i?!!i.leading:n,o="trailing"in i?!!i.trailing:o),z(e,t,{leading:n,trailing:o,maxWait:t})}(this.__trigger,100,{trailing:!0})),this.timeStamp=(new Date).getTime()}catch(i){}}return _(e,[{key:"addObserver",value:function(e,t){if(this.observer){this.observer.observe(e);for(var i=e.getAttribute(nt),n=this.__handlers,o=-1,r=0;r<n.length;r++)n[r]&&e===n[r].target&&(o=r);o>-1?this.__handlers[o].handler=t:this.__handlers.push({target:e,handler:t,playerId:i})}}},{key:"unObserver",value:function(e){var t=-1;this.__handlers.map((function(i,n){e===i.target&&(t=n)}));try{var i;null===(i=this.observer)||void 0===i||i.unobserve(e)}catch(n){}t>-1&&this.__handlers.splice(t,1)}},{key:"destroyObserver",value:function(){var e;null===(e=this.observer)||void 0===e||e.disconnect(),this.observer=null,this.__handlers=null}},{key:"__runHandler",value:function(e){for(var t=this.__handlers,i=0;i<t.length;i++)if(t[i]&&e===t[i].target){try{t[i].handler(e)}catch(n){}return!0}return!1}}]),e}(),It=null;var Lt={pluginGroup:{},init:function(e){var t,i,n=e._pluginInfoId;n||(n=(new Date).getTime(),e._pluginInfoId=n),!e.config.closeResizeObserver&&(t=e.root,i=function(){e.resize()},It||(It=new Pt),It.addObserver(t,i)),this.pluginGroup[n]={_originalOptions:e.config||{},_plugins:{}}},formatPluginInfo:function(e,t){var i=null,n=null;return e.plugin&&"function"==typeof e.plugin?(i=e.plugin,n=e.options):(i=e,n={}),t&&(n.config=t||{}),{PLUFGIN:i,options:n}},checkPluginIfExits:function(e,t){for(var i=0;i<t.length;i++)if(e.toLowerCase()===t[i].pluginName.toLowerCase())return!0;return!1},getRootByConfig:function(e,t){for(var i=Object.keys(t),n=null,o=0;o<i.length;o++)if(e.toLowerCase()===i[o].toLowerCase()){n=t[i[o]];break}return"Object"===W.typeOf(n)?{root:n.root,position:n.position}:{}},lazyRegister:function(e,t){var i=this,n=t.timeout||1500;return Promise.race([t.loader().then((function(t){var n;n=t&&t.__esModule?t.default:t,i.register(e,n,t.options)})),new Promise((function(e,t){setTimeout((function(){t(new Error("timeout"))}),n)}))])},register:function(e,t){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(e&&t&&"function"==typeof t&&void 0!==t.prototype){var n=e._pluginInfoId;if(n&&this.pluginGroup[n]){this.pluginGroup[n]._plugins||(this.pluginGroup[n]._plugins={});var o=this.pluginGroup[n]._plugins,r=this.pluginGroup[n]._originalOptions;i.player=e;var s=i.pluginName||t.pluginName;if(!s)throw new Error("The property pluginName is necessary");if(!t.isSupported||t.isSupported(e.config.mediaType,e.config.codecType)){i.config||(i.config={});for(var a=Object.keys(r),l=0;l<a.length;l++)if(s.toLowerCase()===a[l].toLowerCase()){var c=r[a[l]];"Object"===W.typeOf(c)?i.config=Object.assign({},i.config,r[a[l]]):"Boolean"===W.typeOf(c)&&(i.config.disable=!c);break}t.defaultConfig&&Object.keys(t.defaultConfig).forEach((function(e){void 0===i.config[e]&&(i.config[e]=t.defaultConfig[e])})),i.root?"string"==typeof i.root&&(i.root=e[i.root]):i.root=e.root,i.index=i.config.index||0;try{o[s.toLowerCase()]&&this.unRegister(n,s.toLowerCase());var u=new t(i);return o[s.toLowerCase()]=u,o[s.toLowerCase()].func=t,u&&"function"==typeof u.afterCreate&&u.afterCreate(),u}catch(h){throw h}}}}},unRegister:function(e,t){e._pluginInfoId&&(e=e._pluginInfoId),t=t.toLowerCase();try{var i=this.pluginGroup[e]._plugins[t];i&&(i.pluginName&&i.__destroy(),delete this.pluginGroup[e]._plugins[t])}catch(n){}},deletePlugin:function(e,t){var i=e._pluginInfoId;i&&this.pluginGroup[i]&&this.pluginGroup[i]._plugins&&delete this.pluginGroup[i]._plugins[t]},getPlugins:function(e){var t=e._pluginInfoId;return t&&this.pluginGroup[t]?this.pluginGroup[t]._plugins:{}},findPlugin:function(e,t){var i=e._pluginInfoId;if(!i||!this.pluginGroup[i])return null;var n=t.toLowerCase();return this.pluginGroup[i]._plugins[n]},beforeInit:function(e){var t=this;function i(e){return e&&e.then?e:new Promise((function(e){e()}))}return new Promise((function(n){if(t.pluginGroup)return(e._loadingPlugins&&e._loadingPlugins.length?Promise.all(e._loadingPlugins):Promise.resolve()).then((function(){var o=e._pluginInfoId;if(t.pluginGroup[o]){var r=t.pluginGroup[o]._plugins,s=[];Object.keys(r).forEach((function(e){if(r[e]&&r[e].beforePlayerInit)try{var t=r[e].beforePlayerInit();s.push(i(t))}catch(n){throw s.push(i(null)),n}})),Promise.all([].concat(s)).then((function(){n()})).catch((function(e){n()}))}else n()}))}))},afterInit:function(e){var t=e._pluginInfoId;if(t&&this.pluginGroup[t]){var i=this.pluginGroup[t]._plugins;Object.keys(i).forEach((function(e){i[e]&&i[e].afterPlayerInit&&i[e].afterPlayerInit()}))}},setLang:function(e,t){var i=t._pluginInfoId;if(i&&this.pluginGroup[i]){var n=this.pluginGroup[i]._plugins;Object.keys(n).forEach((function(t){if(n[t].updateLang)n[t].updateLang(e);else try{n[t].lang=e}catch(i){}}))}},reRender:function(e){var t=this,i=e._pluginInfoId;if(i&&this.pluginGroup[i]){var n=[],o=this.pluginGroup[i]._plugins;Object.keys(o).forEach((function(e){"controls"!==e&&o[e]&&(n.push({plugin:o[e].func,options:o[e].__args}),t.unRegister(i,e))})),n.forEach((function(i){t.register(e,i.plugin,i.options)}))}},onPluginsReady:function(e){var t=e._pluginInfoId;if(t&&this.pluginGroup[t]){var i=this.pluginGroup[t]._plugins||{};Object.keys(i).forEach((function(e){i[e].onPluginsReady&&"function"==typeof i[e].onPluginsReady&&i[e].onPluginsReady()}))}},destroy:function(e){var t=e._pluginInfoId;if(this.pluginGroup[t]){var i,n,o;i=e.root,null===(o=It)||void 0===o||o.unObserver(i,n);for(var r=this.pluginGroup[t]._plugins,s=0,a=Object.keys(r);s<a.length;s++){var l=a[s];this.unRegister(t,l)}delete this.pluginGroup[t],delete e._pluginInfoId}}},At={DEFAULT:"xgplayer",DEFAULT_SKIN:"xgplayer-skin-default",ENTER:"xgplayer-is-enter",PAUSED:"xgplayer-pause",PLAYING:"xgplayer-playing",ENDED:"xgplayer-ended",CANPLAY:"xgplayer-canplay",LOADING:"xgplayer-isloading",ERROR:"xgplayer-is-error",REPLAY:"xgplayer-replay",NO_START:"xgplayer-nostart",ACTIVE:"xgplayer-active",INACTIVE:"xgplayer-inactive",FULLSCREEN:"xgplayer-is-fullscreen",CSS_FULLSCREEN:"xgplayer-is-cssfullscreen",ROTATE_FULLSCREEN:"xgplayer-rotate-fullscreen",PARENT_ROTATE_FULLSCREEN:"xgplayer-rotate-parent",PARENT_FULLSCREEN:"xgplayer-fullscreen-parent",INNER_FULLSCREEN:"xgplayer-fullscreen-inner",NO_CONTROLS:"no-controls",FLEX_CONTROLS:"flex-controls",CONTROLS_FOLLOW:"controls-follow",CONTROLS_AUTOHIDE:"controls-autohide",TOP_BAR_AUTOHIDE:"top-bar-autohide",NOT_ALLOW_AUTOPLAY:"not-allow-autoplay",SEEKING:"seeking",PC:"xgplayer-pc",MOBILE:"xgplayer-mobile",MINI:"xgplayer-mini"};function Ot(){return{id:"",el:null,url:"",domEventType:"default",nullUrlStart:!1,width:600,height:337.5,fluid:!1,fitVideoSize:"fixed",videoFillMode:"auto",volume:.6,autoplay:!1,autoplayMuted:!1,loop:!1,isLive:!1,zoom:1,videoInit:!0,poster:"",isMobileSimulateMode:!1,defaultPlaybackRate:1,execBeforePluginsCall:null,allowSeekAfterEnded:!0,enableContextmenu:!0,closeVideoClick:!1,closeVideoDblclick:!1,closePlayerBlur:!1,closeDelayBlur:!1,leavePlayerTime:3e3,closePlayVideoFocus:!1,closePauseVideoFocus:!1,closeFocusVideoFocus:!0,closeControlsBlur:!0,topBarAutoHide:!0,videoAttributes:{},startTime:0,seekedStatus:"play",miniprogress:!1,disableSwipeHandler:function(){},enableSwipeHandler:function(){},preProcessUrl:null,ignores:[],whitelist:[],inactive:3e3,lang:(e=(document.documentElement.getAttribute("lang")||navigator.language||"zh-cn").toLocaleLowerCase(),"zh-cn"===e&&(e="zh"),e),controls:!0,marginControls:!1,fullscreenTarget:null,screenShot:!1,rotate:!1,pip:!1,download:!1,mini:!1,cssFullscreen:!0,keyShortcut:!0,presets:[],plugins:[],playbackRate:1,definition:{list:[]},playsinline:!0,customDuration:0,timeOffset:0,icons:{},i18n:[],tabindex:0,thumbnail:null,videoConfig:{},isHideTips:!1,minWaitDelay:200,commonStyle:{progressColor:"",playedColor:"",cachedColor:"",sliderBtnStyle:{},volumeColor:""}};var e}var Dt=function(){T(t,Et);var e=I(t);function t(){var i;C(this,t);for(var n=arguments.length,o=new Array(n),r=0;r<n;r++)o[r]=arguments[r];return w(E(i=e.call.apply(e,[this].concat(o))),"onMouseEnter",(function(e){var t=E(i),n=t.player;t.playerConfig.closeControlsBlur&&n.focus({autoHide:!1})})),w(E(i),"onMouseLeave",(function(e){E(i).player.focus()})),i}return _(t,[{key:"beforeCreate",value:function(e){e.config.mode||"mobile"!==q.device||(e.config.mode="flex"),e.player.config.marginControls&&(e.config.autoHide=!1)}},{key:"afterCreate",value:function(){var e=this,t=this.config,i=t.disable,n=t.height,o=t.mode;if(!i){"flex"===o&&this.player.addClass(At.FLEX_CONTROLS);var r={height:"".concat(n,"px")};Object.keys(r).map((function(t){e.root.style[t]=r[t]})),this.left=this.find("xg-left-grid"),this.center=this.find("xg-center-grid"),this.right=this.find("xg-right-grid"),this.innerRoot=this.find("xg-inner-controls"),this.on(De,(function(t){t?W.addClass(e.root,"mini-controls"):W.removeClass(e.root,"mini-controls")}));var s=this.playerConfig.isMobileSimulateMode;"mobile"!==q.device&&"mobile"!==s&&(this.bind("mouseenter",this.onMouseEnter),this.bind("mouseleave",this.onMouseLeave))}}},{key:"focus",value:function(){this.player.focus({autoHide:!1})}},{key:"focusAwhile",value:function(){this.player.focus({autoHide:!0})}},{key:"blur",value:function(){this.player.blur({ignorePaused:!0})}},{key:"recoverAutoHide",value:function(){this.config.autoHide&&W.addClass(this.root,At.CONTROLS_AUTOHIDE)}},{key:"pauseAutoHide",value:function(){W.removeClass(this.root,At.CONTROLS_AUTOHIDE)}},{key:"show",value:function(e){this.root.style.display="",this.player.focus()}},{key:"hide",value:function(){this.root.style.display="none"}},{key:"mode",get:function(){return this.config.mode}},{key:"registerPlugin",value:function(e){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0;if(this.root){var o=e.defaultConfig||{};if(!i.root){switch(i.position?i.position:i.config&&i.config.position?i.config.position:o.position){case _t.CONTROLS_LEFT:i.root=this.left;break;case _t.CONTROLS_RIGHT:i.root=this.right;break;case _t.CONTROLS_CENTER:i.root=this.center;break;case _t.CONTROLS:i.root=this.root;break;default:i.root=this.left}return L(x(t.prototype),"registerPlugin",this).call(this,e,i,n)}}}},{key:"destroy",value:function(){"mobile"!==q.device&&(this.unbind("mouseenter",this.onMouseEnter),this.unbind("mouseleave",this.onMouseLeave))}},{key:"render",value:function(){var e=this.config,t=e.mode,i=e.autoHide,n=e.initShow;if(!e.disable){var o=W.classNames({"xgplayer-controls":!0},{"flex-controls":"flex"===t},{"bottom-controls":"bottom"===t},w({},At.CONTROLS_AUTOHIDE,i),{"xgplayer-controls-initshow":n||!i});return'<xg-controls class="'.concat(o,'" unselectable="on">\n    <xg-inner-controls class="xg-inner-controls xg-pos">\n      <xg-left-grid class="xg-left-grid">\n      </xg-left-grid>\n      <xg-center-grid class="xg-center-grid"></xg-center-grid>\n      <xg-right-grid class="xg-right-grid">\n      </xg-right-grid>\n    </xg-inner-controls>\n    </xg-controls>')}}}],[{key:"pluginName",get:function(){return"controls"}},{key:"defaultConfig",get:function(){return{disable:!1,autoHide:!0,mode:"",initShow:!1}}}]),t}(),Rt={lang:{},langKeys:[],textKeys:[]};function Mt(e,t){return Object.keys(t).forEach((function(i){var n,o=W.typeOf(t[i]),r=W.typeOf(e[i]);"Array"===o?("Array"!==r&&(e[i]=[]),(n=e[i]).push.apply(n,A(t[i]))):"Object"===o?("Object"!==r&&(e[i]={}),Mt(e[i],t[i])):e[i]=t[i]})),e}function Nt(){Object.keys(Rt.lang.en).map((function(e){Rt.textKeys[e]=e}))}function Ft(e,t){var i=e.LANG;if(t||(t=Rt),t.lang){var n=e.TEXT||{};"zh"===i&&(i="zh-cn"),t.lang[i]?Mt(t.lang[i],n):(t.langKeys.push(i),t.lang[i]=n),Nt()}}Ft({LANG:"en",TEXT:{ERROR_TYPES:{network:{code:1,msg:"video download error"},mse:{code:2,msg:"stream append error"},parse:{code:3,msg:"parsing error"},format:{code:4,msg:"wrong format"},decoder:{code:5,msg:"decoding error"},runtime:{code:6,msg:"grammatical errors"},timeout:{code:7,msg:"play timeout"},other:{code:8,msg:"other errors"}},HAVE_NOTHING:"There is no information on whether audio/video is ready",HAVE_METADATA:"Audio/video metadata is ready ",HAVE_CURRENT_DATA:"Data about the current play location is available, but there is not enough data to play the next frame/millisecond",HAVE_FUTURE_DATA:"Current and at least one frame of data is available",HAVE_ENOUGH_DATA:"The available data is sufficient to start playing",NETWORK_EMPTY:"Audio/video has not been initialized",NETWORK_IDLE:"Audio/video is active and has been selected for resources, but no network is used",NETWORK_LOADING:"The browser is downloading the data",NETWORK_NO_SOURCE:"No audio/video source was found",MEDIA_ERR_ABORTED:"The fetch process is aborted by the user",MEDIA_ERR_NETWORK:"An error occurred while downloading",MEDIA_ERR_DECODE:"An error occurred while decoding",MEDIA_ERR_SRC_NOT_SUPPORTED:"Audio/video is not supported",REPLAY:"Replay",ERROR:"Network is offline",PLAY_TIPS:"Play",PAUSE_TIPS:"Pause",PLAYNEXT_TIPS:"Play next",DOWNLOAD_TIPS:"Download",ROTATE_TIPS:"Rotate",RELOAD_TIPS:"Reload",FULLSCREEN_TIPS:"Fullscreen",EXITFULLSCREEN_TIPS:"Exit fullscreen",CSSFULLSCREEN_TIPS:"Cssfullscreen",EXITCSSFULLSCREEN_TIPS:"Exit cssfullscreen",TEXTTRACK:"Caption",PIP:"PIP",SCREENSHOT:"Screenshot",LIVE:"LIVE",OFF:"Off",OPEN:"Open",MINI_DRAG:"Click and hold to drag",MINISCREEN:"Miniscreen",REFRESH_TIPS:"Please Try",REFRESH:"Refresh",FORWARD:"forward",LIVE_TIP:"Live"}});var Ht={get textKeys(){return Rt.textKeys},get langKeys(){return Rt.langKeys},get lang(){var e={};return Rt.langKeys.map((function(t){e[t]=Rt.lang[t]})),Rt.lang["zh-cn"]&&(e.zh=Rt.lang["zh-cn"]||{}),e},extend:function(e,t){var i=[];if(t||(t=Rt),t.lang){i="Array"!==W.typeOf(e)?Object.keys(e).map((function(t){return{LANG:"zh"===t?"zh-cn":t,TEXT:e[t]}})):e;var n=t.lang;i.map((function(e){"zh"===e.LANG&&(e.LANG="zh-cn"),n[e.LANG]?Mt(n[e.LANG]||{},e.TEXT||{}):Ft(e,t)})),Nt()}},use:Ft,init:function(e){var t,i={lang:{},langKeys:[],textKeys:{},pId:e};return Mt(i.lang,Rt.lang),(t=i.langKeys).push.apply(t,A(Rt.langKeys)),Mt(i.textKeys,Rt.textKeys),i}},Bt=1,Ut=2,jt=3,Vt=4,Wt=5,Gt=6,zt=7,Kt=["ERROR","INITIAL","READY","ATTACHING","ATTACHED","NOTALLOW","RUNNING","ENDED","DESTROYED"],Yt={},Xt=null,qt=function(){T(t,N.EventEmitter);var e=I(t);function t(){return C(this,t),e.apply(this,arguments)}return _(t,[{key:"add",value:function(e){e&&(Yt[e.playerId]=e,1===Object.keys(Yt).length&&this.setActive(e.playerId,!0))}},{key:"remove",value:function(e){e&&(e.isUserActive,delete Yt[e.playerId])}},{key:"_iterate",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];for(var i in Yt)if(Object.prototype.hasOwnProperty.call(Yt,i)){var n=Yt[i];if(t){if(e(n))break}else e(n)}}},{key:"forEach",value:function(e){this._iterate(e)}},{key:"find",value:function(e){var t=null;return this._iterate((function(i){var n=e(i);return n&&(t=i),n}),!0),t}},{key:"findAll",value:function(e){var t=[];return this._iterate((function(i){e(i)&&t.push(i)})),t}},{key:"setActive",value:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if(Yt[e])return t?this.forEach((function(t){e===t.playerId?(t.isUserActive=!0,t.isInstNext=!1):t.isUserActive=!1})):Yt[e].isUserActive=t,e}},{key:"getActiveId",value:function(){for(var e=Object.keys(Yt),t=0;t<e.length;t++){var i=Yt[e[t]];if(i&&i.isUserActive)return e[t]}return null}},{key:"setNext",value:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if(Yt[e])return t?this.forEach((function(t){e===t.playerId?(t.isUserActive=!1,t.isInstNext=!0):t.isInstNext=!1})):Yt[e].isInstNext=t,e}}],[{key:"getInstance",value:function(){return Xt||(Xt=new t),Xt}}]),t}();var Zt=["play","pause","replay","retry"],Jt=0,$t=0,Qt=null,ei=function(){T(t,$e);var e=I(t);function t(i){var n;C(this,t);var o,r=W.deepMerge(Ot(),i);w(E(n=e.call(this,r)),"canPlayFunc",(function(){if(n.config){var e=n.config,t=e.autoplay,i=e.defaultPlaybackRate;j.logInfo("player","canPlayFunc, startTime",n.__startTime),n._seekToStartTime(),n.playbackRate=i,(t||n._useAutoplay)&&n.mediaPlay(),n.off(ce,n.canPlayFunc),n.removeClass(At.ENTER)}})),w(E(n),"onFullscreenChange",(function(e,t){var i=function(){W.setTimeout(E(n),(function(){n.resize()}),100)},o=W.getFullScreenEl();n._fullActionFrom?n._fullActionFrom="":n.emit(We,{eventType:"system",action:"switch_fullscreen",pluginName:"player",currentTime:n.currentTime,duration:n.duration,props:[{prop:"fullscreen",from:!0,to:!1}]});var r=function(e,t,i){if(e){var n=e.getAttribute(i);return!(!n||n!==t||"VIDEO"!==e.tagName&&"AUDIO"!==e.tagName)}}(o,n.playerId,nt);if(t||o&&(o===n._fullscreenEl||r))i(),!n.config.closeFocusVideoFocus&&n.media.focus(),n.fullscreen=!0,n.changeFullStyle(n.root,o,At.FULLSCREEN),n.emit(Ae,!0,n._fullScreenOffset),n.cssfullscreen&&n.exitCssFullscreen();else if(n.fullscreen){i();var s=E(n),a=s._fullScreenOffset;if(s.config.needFullscreenScroll?(window.scrollTo(a.left,a.top),W.setTimeout(E(n),(function(){n.fullscreen=!1,n._fullScreenOffset=null}),100)):(!n.config.closeFocusVideoFocus&&n.media.focus(),n.fullscreen=!1,n._fullScreenOffset=null),n.cssfullscreen)n.removeClass(At.FULLSCREEN);else{var l=n._fullscreenEl;l||!n.root.contains(e.target)&&e.target!==n.root||(l=e.target),n.recoverFullStyle(n.root,l,At.FULLSCREEN)}n._fullscreenEl=null,n.emit(Ae,!1)}})),w(E(n),"_onWebkitbeginfullscreen",(function(e){n._fullscreenEl=n.media,n.onFullscreenChange(e,!0)})),w(E(n),"_onWebkitendfullscreen",(function(e){n.onFullscreenChange(e,!1)})),ht(E(n),Zt),n.config=r,n._pluginInfoId=W.generateSessionId(),(o=E(n)).logInfo=j.logInfo.bind(o),o.logWarn=j.logWarn.bind(o),o.logError=j.logError.bind(o);var s=n.constructor.defaultPreset;if(n.config.presets.length){var a=n.config.presets.indexOf("default");a>=0&&s&&(n.config.presets[a]=s)}else s&&n.config.presets.push(s);if(n.userTimer=null,n.waitTimer=null,n.handleSource=!0,n._state=Bt,n.isAd=!1,n.isError=!1,n._hasStart=!1,n.isSeeking=!1,n.isCanplay=!1,n._useAutoplay=!1,n.__startTime=-1,n.rotateDeg=0,n.isActive=!1,n.fullscreen=!1,n.cssfullscreen=!1,n.isRotateFullscreen=!1,n._fullscreenEl=null,n.timeSegments=[],n._cssfullscreenEl=null,n.curDefinition=null,n._orgCss="",n._fullScreenOffset=null,n._videoHeight=0,n._videoWidth=0,n.videoPos={pi:1,scale:0,rotate:-1,x:0,y:0,h:-1,w:-1,vy:0,vx:0},n.sizeInfo={width:0,height:0,left:0,top:0},n._accPlayed={t:0,acc:0,loopAcc:0},n._offsetInfo={currentTime:-1,duration:0},n.innerContainer=null,n.controls=null,n.topBar=null,n.root=null,n.__i18n=Ht.init(n._pluginInfoId),q.os.isAndroid&&q.osVersion>0&&q.osVersion<6&&(n.config.autoplay=!1),n.database=new Qe,n.isUserActive=!1,n._onceSeekCanplay=null,n._isPauseBeforeSeek=0,n.innerStates={isActiveLocked:!1},n.instManager=Qt,!n._initDOM())return P(n);var l=n.config,c=l.definition,u=void 0===c?{}:c;if(!l.url&&u.list&&u.list.length>0){var h=u.list.find((function(e){return e.definition&&e.definition===u.defaultDefinition}));h||(u.defaultDefinition=u.list[0].definition,h=u.list[0]),n.config.url=h.url,n.curDefinition=h}return n._bindEvents(),n._registerPresets(),n._registerPlugins(),Lt.onPluginsReady(E(n)),n.getInitDefinition(),n.setState(Ut),W.setTimeout(E(n),(function(){n.emit(ke)}),0),n.onReady&&n.onReady(),(n.config.videoInit||n.config.autoplay)&&(!n.hasStart||n.state<Vt)&&n.start(),n}return _(t,[{key:"_initDOM",value:function(){var e,t=this;if(this.root=this.config.id?document.getElementById(this.config.id):null,!this.root){var i=this.config.el;if(!i||1!==i.nodeType)return this.emit(oe,new Q("use",this.config.vid,{line:32,handle:"Constructor",msg:"container id can't be empty"})),!1;this.root=i}var n=function(e){for(var t=Object.keys(Yt),i=0;i<t.length;i++){var n=Yt[t[i]];if(n.root===e)return n}return null}(this.root);n&&(j.logWarn("The is an Player instance already exists in this.root, destroy it and reinitialize"),n.destroy()),this.root.setAttribute(nt,this.playerId),null===(e=Qt)||void 0===e||e.add(this),Lt.init(this),this._initBaseDoms();var o=this.constructor.XgVideoProxy;if(o&&this.mediaConfig.mediaType===o.mediaType){var r=this.innerContainer||this.root;this.detachVideoEvents(this.media);var s=new o(r,this.config,this.mediaConfig);this.attachVideoEvents(s),this.media=s}if(this.media.setAttribute(nt,this.playerId),this.config.controls){var a=this.config.controls.root||null,l=Lt.register(this,Dt,{root:a});this.controls=l}var c="mobile"===this.config.isMobileSimulateMode?"mobile":q.device;if(this.addClass("".concat(At.DEFAULT," ").concat(At.INACTIVE," xgplayer-").concat(c," ").concat(this.config.controls?"":At.NO_CONTROLS)),this.config.autoplay?this.addClass(At.ENTER):this.addClass(At.NO_START),this.config.fluid){var u=this.config,h=u.width,d=u.height;"number"==typeof h&&"number"==typeof d||(h=600,d=337.5);var f={width:"100%",height:"0","max-width":"100%","padding-top":"".concat(100*d/h,"%")};Object.keys(f).forEach((function(e){t.root.style[e]=f[e]}))}else["width","height"].forEach((function(e){t.config[e]&&("number"!=typeof t.config[e]?t.root.style[e]=t.config[e]:t.root.style[e]="".concat(t.config[e],"px"))}));var p=this.root.getBoundingClientRect(),g=p.width,v=p.height,y=p.left,m=p.top;return this.sizeInfo.width=g,this.sizeInfo.height=v,this.sizeInfo.left=y,this.sizeInfo.top=m,!0}},{key:"_initBaseDoms",value:function(){this.topBar=null,this.leftBar=null,this.rightBar=null,this.config.marginControls&&(this.innerContainer=W.createDom("xg-video-container","",{"data-index":-1},"xg-video-container"),this.root.appendChild(this.innerContainer))}},{key:"_bindEvents",value:function(){var e=this;["focus","blur"].forEach((function(t){e.on(t,e["on"+t.charAt(0).toUpperCase()+t.slice(1)])})),et.forEach((function(t){document&&document.addEventListener(t,e.onFullscreenChange)})),q.os.isIos&&(this.media.addEventListener("webkitbeginfullscreen",this._onWebkitbeginfullscreen),this.media.addEventListener("webkitendfullscreen",this._onWebkitendfullscreen)),this.once(de,this.resize),this.playFunc=function(){e.config.closeFocusVideoFocus||e.media.focus()},this.once(ee,this.playFunc)}},{key:"_unbindEvents",value:function(){var e=this;this.root.removeEventListener("mousemove",this.mousemoveFunc),et.forEach((function(t){document.removeEventListener(t,e.onFullscreenChange)})),this.playFunc&&this.off(ee,this.playFunc),this.off(ce,this.canPlayFunc),this.media.removeEventListener("webkitbeginfullscreen",this._onWebkitbeginfullscreen),this.media.removeEventListener("webkitendfullscreen",this._onWebkitendfullscreen)}},{key:"_clearUserTimer",value:function(){this.userTimer&&(W.clearTimeout(this,this.userTimer),this.userTimer=null)}},{key:"_startInit",value:function(e){var t=this;if(this.media&&(e&&""!==e&&("Array"!==W.typeOf(e)||0!==e.length)||(e="",this.emit(Ce),j.logWarn("config.url is null, please get url and run player._startInit(url)"),!this.config.nullUrlStart))){this.handleSource&&(this._detachSourceEvents(this.media),"Array"===W.typeOf(e)&&e.length>0?this._attachSourceEvents(this.media,e):this.media.src&&this.media.src===e?e||this.media.removeAttribute("src"):this.media.src=e),"Number"===W.typeOf(this.config.volume)&&(this.volume=this.config.volume);var i=this.innerContainer?this.innerContainer:this.root;this.media instanceof window.Element&&!i.contains(this.media)&&i.insertBefore(this.media,i.firstChild);var n=this.media.readyState;j.logInfo("_startInit readyState",n),this.config.autoplay&&(!W.isMSE(this.media)&&this.load(),(q.os.isIpad||q.os.isPhone)&&this.mediaPlay());var o=this.config.startTime;this.__startTime=o>0?o:-1,this.config.startTime=0,n>=2&&this.duration>0?this.canPlayFunc():this.on(ce,this.canPlayFunc),(!this.hasStart||this.state<Vt)&&Lt.afterInit(this),this.hasStart=!0,this.setState(Vt),W.setTimeout(this,(function(){t.emit(we)}),0)}}},{key:"_registerPlugins",value:function(){var e=this,t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];this._loadingPlugins=[];var i=this.config.ignores||[],n=this.config.plugins||[],o=this.config.i18n||[];t&&Ht.extend(o,this.__i18n);var r=i.join("||").toLowerCase().split("||"),s=this.plugins;n.forEach((function(i){try{var n=i.plugin?i.plugin.pluginName:i.pluginName;if(n&&r.indexOf(n.toLowerCase())>-1)return null;if(!t&&s[n.toLowerCase()])return;if(i.lazy&&i.loader){var o=Lt.lazyRegister(e,i);return void(i.forceBeforeInit&&(o.then((function(){e._loadingPlugins.splice(e._loadingPlugins.indexOf(o),1)})).catch((function(t){j.logError("_registerPlugins:loadingPlugin",t),e._loadingPlugins.splice(e._loadingPlugins.indexOf(o),1)})),e._loadingPlugins.push(o)))}return e.registerPlugin(i)}catch(a){j.logError("_registerPlugins:",a)}}))}},{key:"_registerPresets",value:function(){var e=this;this.config.presets.forEach((function(t){!function(e,t){var i,n,o=t.preset&&t.options?new t.preset(t.options,e.config):new t({},e.config),r=o.plugins,s=void 0===r?[]:r,a=o.ignores,l=void 0===a?[]:a,c=o.icons,u=void 0===c?{}:c,h=o.i18n,d=void 0===h?[]:h;e.config.plugins||(e.config.plugins=[]),e.config.ignores||(e.config.ignores=[]),(i=e.config.plugins).push.apply(i,A(s)),(n=e.config.ignores).push.apply(n,A(l)),Object.keys(u).map((function(t){e.config.icons[t]||(e.config.icons[t]=u[t])}));var f=e.config.i18n||[];d.push.apply(d,A(f)),e.config.i18n=d}(e,t)}))}},{key:"_getRootByPosition",value:function(e){var t=null;switch(e){case _t.ROOT_RIGHT:this.rightBar||(this.rightBar=W.createPositionBar("xg-right-bar",this.root)),t=this.rightBar;break;case _t.ROOT_LEFT:this.leftBar||(this.leftBar=W.createPositionBar("xg-left-bar",this.root)),t=this.leftBar;break;case _t.ROOT_TOP:this.topBar||(this.topBar=W.createPositionBar("xg-top-bar",this.root),this.config.topBarAutoHide&&W.addClass(this.topBar,At.TOP_BAR_AUTOHIDE)),t=this.topBar;break;default:t=this.innerContainer||this.root}return t}},{key:"registerPlugin",value:function(e,t){var i=Lt.formatPluginInfo(e,t),n=i.PLUFGIN,o=i.options,r=this.config.plugins;!Lt.checkPluginIfExits(n.pluginName,r)&&r.push(n);var s=Lt.getRootByConfig(n.pluginName,this.config);s.root&&(o.root=s.root),s.position&&(o.position=s.position);var a,l=o.position?o.position:o.config&&o.config.position||n.defaultConfig&&n.defaultConfig.position;return!o.root&&"string"==typeof l&&l.indexOf("controls")>-1?null===(a=this.controls)||void 0===a?void 0:a.registerPlugin(n,o,n.pluginName):(o.root||(o.root=this._getRootByPosition(l)),Lt.register(this,n,o))}},{key:"deregister",value:function(e){"string"==typeof e?Lt.unRegister(this,e):e instanceof kt&&Lt.unRegister(this,e.pluginName)}},{key:"unRegisterPlugin",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];this.deregister(e),t&&this.removePluginFromConfig(e)}},{key:"removePluginFromConfig",value:function(e){var t;if("string"==typeof e?t=e:e instanceof kt&&(t=e.pluginName),t)for(var i=this.config.plugins.length-1;i>-1;i--){if(this.config.plugins[i].pluginName.toLowerCase()===t.toLowerCase()){this.config.plugins.splice(i,1);break}}}},{key:"plugins",get:function(){return Lt.getPlugins(this)}},{key:"getPlugin",value:function(e){var t=Lt.findPlugin(this,e);return t&&t.pluginName?t:null}},{key:"addClass",value:function(e){this.root&&(W.hasClass(this.root,e)||W.addClass(this.root,e))}},{key:"removeClass",value:function(e){this.root&&W.removeClass(this.root,e)}},{key:"hasClass",value:function(e){if(this.root)return W.hasClass(this.root,e)}},{key:"setAttribute",value:function(e,t){this.root&&this.root.setAttribute(e,t)}},{key:"removeAttribute",value:function(e,t){this.root&&this.root.removeAttribute(e,t)}},{key:"start",value:function(e){var t=this;if(!(this.state>jt))return e||this.config.url||this.getInitDefinition(),this.hasStart=!0,this.setState(jt),this._registerPlugins(!1),Lt.beforeInit(this).then((function(){if(t.config){e||(e=t.url||t.config.url);var i=t._preProcessUrl(e);return t._startInit(i.url)}})).catch((function(e){throw e.fileName="player",e.lineNumber="236",j.logError("start:beforeInit:",e),e}))}},{key:"switchURL",value:function(e,t){var i=this,n=e;"Object"===W.typeOf(e)&&(n=e.url),n=this._preProcessUrl(n).url;var o=this.currentTime;this.__startTime=o;var r=this.paused&&!this.isError;return this.src=n,new Promise((function(e,t){var o=function(e){i.off("timeupdate",s),i.off("canplay",s),t(e)},s=function(){i._seekToStartTime(),r&&i.pause(),i.off("error",o),e(!0)};i.once("error",o),n?(q.os.isAndroid?i.once("timeupdate",s):i.once("canplay",s),i.play()):i.errorHandler("error",{code:6,message:"empty_src"})}))}},{key:"videoPlay",value:function(){this.mediaPlay()}},{key:"mediaPlay",value:function(){var e=this;if(!this.hasStart&&this.state<Vt)return this.removeClass(At.NO_START),this.addClass(At.ENTER),this.start(),void(this._useAutoplay=!0);this.state<Gt&&(this.removeClass(At.NO_START),!this.isCanplay&&this.addClass(At.ENTER));var i=L(x(t.prototype),"play",this).call(this);return void 0!==i&&i&&i.then?i.then((function(){e.removeClass(At.NOT_ALLOW_AUTOPLAY),e.addClass(At.PLAYING),e.state<Gt&&(j.logInfo(">>>>playPromise.then"),e.setState(Gt),e.emit(be))})).catch((function(t){if(j.logWarn(">>>>playPromise.catch",t.name),e.media&&e.media.error)return e.onError(),void e.removeClass(At.ENTER);"NotAllowedError"===t.name&&(e._errorTimer=W.setTimeout(e,(function(){e._errorTimer=null,e.emit(_e),e.addClass(At.NOT_ALLOW_AUTOPLAY),e.removeClass(At.ENTER),e.pause(),e.setState(Wt)}),0))})):(j.logWarn("video.play not return promise"),this.state<Gt&&(this.setState(Gt),this.removeClass(At.NOT_ALLOW_AUTOPLAY),this.removeClass(At.NO_START),this.removeClass(At.ENTER),this.addClass(At.PLAYING),this.emit(be))),i}},{key:"mediaPause",value:function(){L(x(t.prototype),"pause",this).call(this)}},{key:"videoPause",value:function(){L(x(t.prototype),"pause",this).call(this)}},{key:"play",value:function(){var e=this;return this.removeClass(At.PAUSED),ft(this,"play",(function(){return e.mediaPlay()}))}},{key:"pause",value:function(){var e=this;ft(this,"pause",(function(){L(x(t.prototype),"pause",e).call(e)}))}},{key:"seek",value:function(e,t){var i=this;if(this.media&&!Number.isNaN(Number(e))&&this.hasStart){var n=this.config,o=n.isSeekedPlay,r=n.seekedStatus,s=t||(o?"play":r);e=e<0?0:e>this.duration?parseInt(this.duration,10):e,!this._isPauseBeforeSeek&&(this._isPauseBeforeSeek=this.paused?2:1),this._onceSeekCanplay&&this.off(se,this._onceSeekCanplay),this._onceSeekCanplay=function(){switch(i.removeClass(At.ENTER),i.isSeeking=!1,s){case"play":i.play();break;case"pause":i.pause();break;default:i._isPauseBeforeSeek>1||i.paused?i.pause():i.play()}i._isPauseBeforeSeek=0,i._onceSeekCanplay=null},this.once(se,this._onceSeekCanplay),this.state<Gt?(this.removeClass(At.NO_START),this.currentTime=e,this.play()):this.currentTime=e}}},{key:"getInitDefinition",value:function(){var e=this,t=this.config,i=t.definition;!t.url&&i&&i.list&&i.list.length>0&&i.defaultDefinition&&i.list.map((function(t){t.definition===i.defaultDefinition&&(e.config.url=t.url,e.curDefinition=t)}))}},{key:"changeDefinition",value:function(e,t){var i=this,n=this.config.definition;if(Array.isArray(null==n?void 0:n.list)&&n.list.forEach((function(t){(null==e?void 0:e.definition)===t.definition&&(i.curDefinition=t)})),null!=e&&e.bitrate&&"number"!=typeof e.bitrate&&(e.bitrate=parseInt(e.bitrate,10)||0),this.emit(Re,{from:t,to:e}),this.hasStart){var o=this.switchURL(e.url,m({seamless:!1!==n.seamless&&"undefined"!=typeof MediaSource&&"function"==typeof MediaSource.isTypeSupported},e));o&&o.then?o.then((function(){i.emit(Me,{from:t,to:e})})):this.emit(Me,{from:t,to:e})}else this.config.url=e.url}},{key:"reload",value:function(){this.load(),this.reloadFunc=function(){this.play()},this.once(de,this.reloadFunc)}},{key:"resetState",value:function(){var e=this,t=[At.NOT_ALLOW_AUTOPLAY,At.PLAYING,At.NO_START,At.PAUSED,At.REPLAY,At.ENTER,At.ENDED,At.ERROR,At.LOADING];this.hasStart=!1,this.isError=!1,this._useAutoplay=!1,this.mediaPause(),this._accPlayed.acc=0,this._accPlayed.t=0,this._accPlayed.loopAcc=0,t.forEach((function(t){e.removeClass(t)})),this.addClass(At.NO_START),this.emit(Ge)}},{key:"reset",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],i=arguments.length>1?arguments[1]:void 0;if(this.resetState(),this.plugins&&(t.map((function(t){e.deregister(t)})),i)){var n=Ot();Object.keys(this.config).keys((function(t){"undefined"===e.config[t]||"plugins"!==t&&"presets"!==t&&"el"!==t&&"id"!==t||(e.config[t]=n[t])}))}}},{key:"destroy",value:function(){var e,i=this,n=this.innerContainer,o=this.root,r=this.media;if(o&&r){if(this.hasStart=!1,this._useAutoplay=!1,o.removeAttribute(nt),this.updateAcc("destroy"),this._unbindEvents(),this._detachSourceEvents(this.media),W.clearAllTimers(this),this.emit(xe),null===(e=Qt)||void 0===e||e.remove(this),Lt.destroy(this),dt(this),L(x(t.prototype),"destroy",this).call(this),this.fullscreen&&this._fullscreenEl===this.root&&this.exitFullscreen(),n)for(var s=n.children,a=0;a<s.length;a++)n.removeChild(s[a]);!n&&r instanceof window.Node&&o.contains(r)&&o.removeChild(r),["topBar","leftBar","rightBar","innerContainer"].map((function(e){i[e]&&o.removeChild(i[e]),i[e]=null}));var l=o.className.split(" ");l.length>0?o.className=l.filter((function(e){return e.indexOf("xgplayer")<0})).join(" "):o.className="",this.removeAttribute("data-xgfill"),["isSeeking","isCanplay","isActive","cssfullscreen","fullscreen"].forEach((function(e){i[e]=!1}))}}},{key:"replay",value:function(){var e=this;this.removeClass(At.ENDED),this.currentTime=0,this.isSeeking=!1,ft(this,"replay",(function(){e.once(se,(function(){var t=e.mediaPlay();t&&t.catch&&t.catch((function(e){}))})),e.emit(Te),e.onPlay()}))}},{key:"retry",value:function(){var e=this;this.removeClass(At.ERROR),this.addClass(At.LOADING),ft(this,"retry",(function(){var t=e.currentTime,i=e.config.url,n=W.isMSE(e.media)?{url:i}:e._preProcessUrl(i);e.src=n.url,!e.config.isLive&&(e.currentTime=t),e.once(ce,(function(){e.mediaPlay()}))}))}},{key:"changeFullStyle",value:function(e,t,i,n){e&&(n||(n=At.PARENT_FULLSCREEN),this._orgCss||(this._orgCss=W.filterStyleFromText(e)),W.addClass(e,i),t&&t!==e&&!this._orgPCss&&(this._orgPCss=W.filterStyleFromText(t),W.addClass(t,n),t.setAttribute(nt,this.playerId)))}},{key:"recoverFullStyle",value:function(e,t,i,n){n||(n=At.PARENT_FULLSCREEN),this._orgCss&&(W.setStyleFromCsstext(e,this._orgCss),this._orgCss=""),W.removeClass(e,i),t&&t!==e&&this._orgPCss&&(W.setStyleFromCsstext(t,this._orgPCss),this._orgPCss="",W.removeClass(t,n),t.removeAttribute(nt))}},{key:"getFullscreen",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.config.fullscreenTarget,t=this.root,i=this.media;if("video"!==e&&"media"!==e||(e=this[e]),e||(e=t),this._fullScreenOffset={top:W.scrollTop(),left:W.scrollLeft()},this._fullscreenEl=e,this._fullActionFrom="get",W.getFullScreenEl()===this._fullscreenEl)return this.onFullscreenChange(),Promise.resolve();try{for(var n=0;n<tt.length;n++){var o=tt[n];if(e[o]){var r="webkitRequestFullscreen"===o?e.webkitRequestFullscreen(window.Element.ALLOW_KEYBOARD_INPUT):e[o]();return r&&r.then?r:Promise.resolve()}}return i.fullscreenEnabled||i.webkitSupportsFullscreen?(i.webkitEnterFullscreen(),Promise.resolve()):Promise.reject(new Error("call getFullscreen fail"))}catch(s){return Promise.reject(new Error("call getFullscreen fail"))}}},{key:"exitFullscreen",value:function(e){if(this.isRotateFullscreen&&this.exitRotateFullscreen(),this._fullscreenEl||W.getFullScreenEl()){this.root;var t=this.media;this._fullActionFrom="exit";try{for(var i=0;i<it.length;i++){var n=it[i];if(document[n]){var o=document[n]();return o&&o.then?o:Promise.resolve()}}return t&&t.webkitSupportsFullscreen?(t.webkitExitFullScreen(),Promise.resolve()):Promise.reject(new Error("call exitFullscreen fail"))}catch(r){return Promise.reject(new Error("call exitFullscreen fail"))}}}},{key:"getCssFullscreen",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.config.fullscreenTarget;this.isRotateFullscreen?this.exitRotateFullscreen():this.fullscreen&&this.exitFullscreen();var t=e?"".concat(At.INNER_FULLSCREEN," ").concat(At.CSS_FULLSCREEN):At.CSS_FULLSCREEN;this.changeFullStyle(this.root,e,t);var i=this.config.fullscreen,n=void 0===i?{}:i;(!0===n.useCssFullscreen||"function"==typeof n.useCssFullscreen&&n.useCssFullscreen())&&(this.fullscreen=!0,this.emit(Ae,!0)),this._cssfullscreenEl=e,this.cssfullscreen=!0,this.emit(Oe,!0)}},{key:"exitCssFullscreen",value:function(){var e=this._cssfullscreenEl?"".concat(At.INNER_FULLSCREEN," ").concat(At.CSS_FULLSCREEN):At.CSS_FULLSCREEN;if(this.fullscreen){var t=this.config.fullscreen,i=void 0===t?{}:t;!0===i.useCssFullscreen||"function"==typeof i.useCssFullscreen&&i.useCssFullscreen()?(this.recoverFullStyle(this.root,this._cssfullscreenEl,e),this.fullscreen=!1,this.emit(Ae,!1)):this.removeClass(e)}else this.recoverFullStyle(this.root,this._cssfullscreenEl,e);this._cssfullscreenEl=null,this.cssfullscreen=!1,this.emit(Oe,!1)}},{key:"getRotateFullscreen",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.config.fullscreenTarget;this.cssfullscreen&&this.exitCssFullscreen(e);var t=e?"".concat(At.INNER_FULLSCREEN," ").concat(At.ROTATE_FULLSCREEN):At.ROTATE_FULLSCREEN;this._fullscreenEl=e||this.root,this.changeFullStyle(this.root,e,t,At.PARENT_ROTATE_FULLSCREEN),this.isRotateFullscreen=!0,this.fullscreen=!0,this.setRotateDeg(90),this._rootStyle=this.root.getAttribute("style"),this.root.style.width="".concat(window.innerHeight,"px"),this.emit(Ae,!0)}},{key:"exitRotateFullscreen",value:function(e){var t=this._fullscreenEl!==this.root?"".concat(At.INNER_FULLSCREEN," ").concat(At.ROTATE_FULLSCREEN):At.ROTATE_FULLSCREEN;this.recoverFullStyle(this.root,this._fullscreenEl,t,At.PARENT_ROTATE_FULLSCREEN),this.isRotateFullscreen=!1,this.fullscreen=!1,this.setRotateDeg(0),this.emit(Ae,!1),this._rootStyle&&(this.root.style.style=this._rootStyle,this._rootStyle=!1)}},{key:"setRotateDeg",value:function(e){90===window.orientation||-90===window.orientation?this.rotateDeg=0:this.rotateDeg=e}},{key:"focus",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{autoHide:!this.config.closeDelayBlur,delay:this.config.inactive};this.isActive?this.onFocus(e):this.emit(ye,m({paused:this.paused,ended:this.ended},e))}},{key:"blur",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{ignorePaused:!1};this.isActive?(this._clearUserTimer(),this.emit(me,m({paused:this.paused,ended:this.ended},e))):this.onBlur(e)}},{key:"onFocus",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{autoHide:!0,delay:3e3},i=this.innerStates;if(this.isActive=!0,this.removeClass(At.INACTIVE),this._clearUserTimer(),void 0!==t.isLock&&(i.isActiveLocked=t.isLock),!1===t.autoHide||!0===t.isLock||i.isActiveLocked)this._clearUserTimer();else{var n=t&&t.delay?t.delay:this.config.inactive;this.userTimer=W.setTimeout(this,(function(){e.userTimer=null,e.blur()}),n)}}},{key:"onBlur",value:function(){var e=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}).ignorePaused,t=void 0!==e&&e;if(!this.innerStates.isActiveLocked){var i=this.config.closePauseVideoFocus;this.isActive=!1,(t||i||!this.paused&&!this.ended)&&this.addClass(At.INACTIVE)}}},{key:"onEmptied",value:function(){this.updateAcc("emptied")}},{key:"onCanplay",value:function(){this.removeClass(At.ENTER),this.removeClass(At.ERROR),this.removeClass(At.LOADING),this.isCanplay=!0,this.waitTimer&&W.clearTimeout(this,this.waitTimer)}},{key:"onLoadeddata",value:function(){var e=this;this.isError=!1,this.isSeeking=!1,this.__startTime>0&&(this.duration>0?this._seekToStartTime():this.once(ue,(function(){e._seekToStartTime()})))}},{key:"onLoadstart",value:function(){this.removeClass(At.ERROR),this.isCanplay=!1}},{key:"onPlay",value:function(){this.state===zt&&this.setState(Gt),this.removeClass(At.PAUSED),this.ended&&this.removeClass(At.ENDED),!this.config.closePlayVideoFocus&&this.focus()}},{key:"onPause",value:function(){this.addClass(At.PAUSED),this.updateAcc("pause"),this.config.closePauseVideoFocus||(this._clearUserTimer(),this.focus())}},{key:"onEnded",value:function(){this.updateAcc("ended"),this.addClass(At.ENDED),this.setState(zt)}},{key:"onError",value:function(){this.isError=!0,this.updateAcc("error"),this.removeClass(At.NOT_ALLOW_AUTOPLAY),this.removeClass(At.NO_START),this.removeClass(At.ENTER),this.removeClass(At.LOADING),this.addClass(At.ERROR)}},{key:"onSeeking",value:function(){this.isSeeking||this.updateAcc("seeking"),this.isSeeking=!0,this.addClass(At.SEEKING)}},{key:"onSeeked",value:function(){this.isSeeking=!1,this.waitTimer&&W.clearTimeout(this,this.waitTimer),this.removeClass(At.LOADING),this.removeClass(At.SEEKING)}},{key:"onWaiting",value:function(){var e=this;this.waitTimer&&W.clearTimeout(this,this.waitTimer),this.updateAcc("waiting"),this.waitTimer=W.setTimeout(this,(function(){e.addClass(At.LOADING),e.emit(Le),W.clearTimeout(e,e.waitTimer),e.waitTimer=null}),this.config.minWaitDelay)}},{key:"onPlaying",value:function(){var e=this;this.isError=!1,[At.NO_START,At.PAUSED,At.ENDED,At.ERROR,At.REPLAY,At.LOADING].forEach((function(t){e.removeClass(t)})),this._accPlayed.t||this.paused||this.ended||(this._accPlayed.t=(new Date).getTime())}},{key:"onTimeupdate",value:function(){!this._videoHeight&&this.media.videoHeight&&this.resize(),(this.waitTimer||this.hasClass(At.LOADING))&&this.media.readyState>2&&(this.removeClass(At.LOADING),W.clearTimeout(this,this.waitTimer),this.waitTimer=null),!this.paused&&this.state===Wt&&this.duration&&(this.setState(Gt),this.emit(be)),this._accPlayed.t||this.paused||this.ended||(this._accPlayed.t=(new Date).getTime())}},{key:"onVolumechange",value:function(){"Number"===W.typeOf(this.config.volume)&&(this.config.volume=this.volume)}},{key:"onRatechange",value:function(){this.config.defaultPlaybackRate=this.playbackRate}},{key:"emitUserAction",value:function(e,t,i){if(this.media&&t&&e){var n="String"===W.typeOf(e)?e:e.type||"";i.props&&"Array"!==W.typeOf(i.props)&&(i.props=[i.props]),this.emit(We,m({eventType:n,action:t,currentTime:this.currentTime,duration:this.duration,ended:this.ended,event:e},i))}}},{key:"updateAcc",value:function(e){if(this._accPlayed.t){var t=(new Date).getTime()-this._accPlayed.t;this._accPlayed.acc+=t,this._accPlayed.t=0,("ended"===e||this.ended)&&(this._accPlayed.loopAcc=this._accPlayed.acc)}}},{key:"checkBuffer",value:function(e){var t=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:{startDiff:0,endDiff:0})||{},i=t.startDiff,n=void 0===i?0:i,o=t.endDiff,r=void 0===o?0:o,s=this.media.buffered;if(!s||0===s.length||!this.duration)return!0;for(var a=e||this.media.currentTime||.2,l=s.length,c=0;c<l;c++)if(s.start(c)+n<=a&&s.end(c)-r>a)return!0;return!1}},{key:"resizePosition",value:function(){var e=this.videoPos,t=e.vy,i=e.vx,n=e.h,o=e.w,r=this.videoPos.rotate;if(!(r<0&&n<0&&o<0)){var s=this.videoPos._pi;if(!s&&this.media.videoHeight&&(s=this.media.videoWidth/this.media.videoHeight*100),s){this.videoPos.pi=s;var a={rotate:r=r<0?0:r},l=0,c=0,u=1,h=Math.abs(r/90),d=this.root,f=this.innerContainer,p=d.offsetWidth,g=f?f.offsetHeight:d.offsetHeight,v=g,y=p;if(h%2==0)u=n>0?100/n:o>0?100/o:1,a.scale=u,l=t>0?(100-n)/2-t:0,a.y=2===h?0-l:l,c=i>0?(100-o)/2-i:0,a.x=2===h?0-c:c,this.media.style.width="".concat(y,"px"),this.media.style.height="".concat(v,"px");else if(h%2==1){v=p;var m=g-p;c=-m/2/(y=g)*100,a.x=3===h?c+t/2:c-t/2,l=m/2/v*100,a.y=3===h?l+i/2:l-i/2,a.scale=u,this.media.style.width="".concat(y,"px"),this.media.style.height="".concat(v,"px")}var k=W.getTransformStyle(a,this.media.style.transform||this.media.style.webkitTransform);this.media.style.transform=k,this.media.style.webkitTransform=k}}}},{key:"position",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{h:0,y:0,x:0,w:0};if(this.media&&e&&e.h){var t=this.videoPos;t.h=100*e.h||0,t.w=100*e.w||0,t.vx=100*e.x||0,t.vy=100*e.y||0,this.resizePosition()}}},{key:"setConfig",value:function(e){var t=this;e&&Object.keys(e).map((function(i){if("plugins"!==i){t.config[i]=e[i];var n=t.plugins[i.toLowerCase()];n&&"Function"===W.typeOf(n.setConfig)&&n.setConfig(e[i])}}))}},{key:"playNext",value:function(e){var t=this;this.resetState(),this.setConfig(e),this._currentTime=0,this._duration=0,ft(this,"playnext",(function(){t.start(),t.emit(Ue,e)}))}},{key:"resize",value:function(){var e=this;if(this.media){var t=this.root.getBoundingClientRect();this.sizeInfo.width=t.width,this.sizeInfo.height=t.height,this.sizeInfo.left=t.left,this.sizeInfo.top=t.top;var i=this.media,n=i.videoWidth,o=i.videoHeight,r=this.config,s=r.fitVideoSize,a=r.videoFillMode;if("fill"!==a&&"cover"!==a&&"contain"!==a||this.setAttribute("data-xgfill",a),o&&n){this._videoHeight=o,this._videoWidth=n;var l=this.controls&&this.innerContainer?this.controls.root.getBoundingClientRect().height:0,c=t.width,u=t.height-l,h=parseInt(n/o*1e3,10),d=parseInt(c/u*1e3,10),f=c,p=u,g={};"auto"===s&&d>h||"fixWidth"===s?(p=c/h*1e3,this.config.fluid?g.paddingTop="".concat(100*p/f,"%"):g.height="".concat(p+l,"px")):("auto"===s&&d<h||"fixHeight"===s)&&(f=h*u/1e3,g.width="".concat(f,"px")),this.fullscreen||this.cssfullscreen||Object.keys(g).forEach((function(t){e.root.style[t]=g[t]})),("fillHeight"===a&&d<h||"fillWidth"===a&&d>h)&&this.setAttribute("data-xgfill","cover");var v={videoScale:h,vWidth:f,vHeight:p,cWidth:f,cHeight:p+l};this.resizePosition(),this.emit(Ne,v)}}}},{key:"updateObjectPosition",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;this.media.updateObjectPosition?this.media.updateObjectPosition(e,t):this.media.style.objectPosition="".concat(100*e,"% ").concat(100*t,"%")}},{key:"setState",value:function(e){j.logInfo("setState","state from:".concat(Kt[this.state]," to:").concat(Kt[e])),this._state=e}},{key:"_preProcessUrl",value:function(e,t){var i=this.config,n=i.preProcessUrl,o=i.preProcessUrlOptions,r=Object.assign({},o,t);return W.isBlob(e)||"function"!=typeof n?{url:e}:n(e,r)}},{key:"_seekToStartTime",value:function(){this.__startTime>0&&this.duration>0&&(this.currentTime=this.__startTime>this.duration?this.duration:this.__startTime,this.__startTime=-1)}},{key:"state",get:function(){return this._state}},{key:"isFullscreen",get:function(){return this.fullscreen}},{key:"isCssfullScreen",get:function(){return this.cssfullscreen}},{key:"hasStart",get:function(){return this._hasStart},set:function(e){"boolean"==typeof e&&(this._hasStart=e,!1===e&&this.setState(Ut),this.emit("hasstart"))}},{key:"isPlaying",get:function(){return this._state===Gt||this._state===zt},set:function(e){e?this.setState(Gt):this._state>=Gt&&this.setState(Vt)}},{key:"definitionList",get:function(){return this.config&&this.config.definition&&this.config.definition.list||[]},set:function(e){var t=this,i=this.config.definition,n=null,o=null;i.list=e,this.emit("resourceReady",e),e.forEach((function(e){var r;(null===(r=t.curDefinition)||void 0===r?void 0:r.definition)===e.definition&&(n=e),i.defaultDefinition===e.definition&&(o=e)})),!o&&e.length>0&&(o=e[0]),n?this.changeDefinition(n):o&&this.changeDefinition(o)}},{key:"videoFrameInfo",get:function(){var e={total:0,dropped:0,corrupted:0,droppedRate:0,droppedDuration:0};if(!this.media||!this.media.getVideoPlaybackQuality)return e;var t=this.media.getVideoPlaybackQuality();return e.dropped=t.droppedVideoFrames||0,e.total=t.totalVideoFrames||0,e.corrupted=t.corruptedVideoFrames||0,e.total>0&&(e.droppedRate=e.dropped/e.total*100,e.droppedDuration=parseInt(this.cumulateTime/e.total*e.dropped,0)),e}},{key:"lang",get:function(){return this.config.lang},set:function(e){0===Ht.langKeys.filter((function(t){return t===e})).length&&"zh"!==e||(this.config.lang=e,Lt.setLang(e,this))}},{key:"i18n",get:function(){var e=this.config.lang;return"zh"===e&&(e="zh-cn"),this.__i18n.lang[e]||this.__i18n.lang.en}},{key:"i18nKeys",get:function(){return this.__i18n.textKeys||{}}},{key:"version",get:function(){return Z}},{key:"playerId",get:function(){return this._pluginInfoId}},{key:"url",get:function(){return this.__url||this.config.url},set:function(e){this.__url=e}},{key:"poster",get:function(){return this.plugins.poster?this.plugins.poster.config.poster:this.config.poster},set:function(e){this.plugins.poster&&this.plugins.poster.update(e)}},{key:"readyState",get:function(){return L(x(t.prototype),"readyState",this)}},{key:"error",get:function(){var e=L(x(t.prototype),"error",this);return this.i18n[e]||e}},{key:"networkState",get:function(){return L(x(t.prototype),"networkState",this)}},{key:"fullscreenChanging",get:function(){return!(null===this._fullScreenOffset)}},{key:"cumulateTime",get:function(){var e=this._accPlayed,t=e.acc,i=e.t;return i?(new Date).getTime()-i+t:t}},{key:"zoom",get:function(){return this.config.zoom},set:function(e){this.config.zoom=e}},{key:"videoRotateDeg",get:function(){return this.videoPos.rotate},set:function(e){(e=W.convertDeg(e))%90==0&&e!==this.videoPos.rotate&&(this.videoPos.rotate=e,this.resizePosition())}},{key:"avgSpeed",get:function(){return $t},set:function(e){$t=e}},{key:"realTimeSpeed",get:function(){return Jt},set:function(e){Jt=e}},{key:"offsetCurrentTime",get:function(){return this._offsetInfo.currentTime||0},set:function(e){this._offsetInfo.currentTime=e}},{key:"offsetDuration",get:function(){return this._offsetInfo.duration||0},set:function(e){this._offsetInfo.duration=e||0}},{key:"hook",value:function(e,t){return rt.call.apply(rt,[this].concat(Array.prototype.slice.call(arguments)))}},{key:"useHooks",value:function(e,t){return at.call.apply(at,[this].concat(Array.prototype.slice.call(arguments)))}},{key:"removeHooks",value:function(e,t){return lt.call.apply(lt,[this].concat(Array.prototype.slice.call(arguments)))}},{key:"usePluginHooks",value:function(e,t,i){for(var n=arguments.length,o=new Array(n>3?n-3:0),r=3;r<n;r++)o[r-3]=arguments[r];return ct.call.apply(ct,[this].concat(Array.prototype.slice.call(arguments)))}},{key:"removePluginHooks",value:function(e,t,i){for(var n=arguments.length,o=new Array(n>3?n-3:0),r=3;r<n;r++)o[r-3]=arguments[r];return ut.call.apply(ut,[this].concat(Array.prototype.slice.call(arguments)))}},{key:"setUserActive",value:function(e,t){var i;"boolean"==typeof t&&t!==this.muted&&(this.addInnerOP("volumechange"),W.typeOf(t)===Boolean&&(this.muted=t)),null===(i=Qt)||void 0===i||i.setActive(this.playerId,e)}}],[{key:"debugger",get:function(){return j.config.debug},set:function(e){j.config.debug=e}},{key:"instManager",get:function(){return Qt},set:function(e){Qt=e}},{key:"getCurrentUserActivePlayerId",value:function(){var e;return null===(e=Qt)||void 0===e?void 0:e.getActiveId()}},{key:"setCurrentUserActive",value:function(e,t){var i;null===(i=Qt)||void 0===i||i.setActive(e,t)}},{key:"isHevcSupported",value:function(){return q.isHevcSupported()}},{key:"probeConfigSupported",value:function(e){return q.probeConfigSupported(e)}},{key:"install",value:function(e,i){t.plugins||(t.plugins={}),t.plugins[e]||(t.plugins[e]=i)}},{key:"use",value:function(e,i){t.plugins||(t.plugins={}),t.plugins[e]=i}}]),t}();function ti(){return(new Date).getTime()}w(ei,"defaultPreset",null),w(ei,"XgVideoProxy",null),ei.instManager=qt.getInstance();var ii="loadstart",ni="loadeddata",oi="firstFrame",ri="waitingStart",si="waitingEnd",ai="seekStart",li="seekEnd",ci=function(){T(t,Et);var e=I(t);function t(){var i;C(this,t);for(var n=arguments.length,o=new Array(n),r=0;r<n;r++)o[r]=arguments[r];return w(E(i=e.call.apply(e,[this].concat(o))),"_onTimeupdate",(function(){i._state.isTimeUpdate=!0,i._state.autoplayStart&&(j.logInfo("[xgLogger]".concat(i.player.playerId," _onTimeupdate")),i._sendFF("onTimeupdate"))})),w(E(i),"_onAutoplayStart",(function(){j.logInfo("[xgLogger]".concat(i.player.playerId," _onAutoplayStart")),i._state.autoplayStart=!0,i.vt&&i._sendFF("onAutoplayStart")})),w(E(i),"_onReset",(function(){i._state={autoplayStart:!1,isFFLoading:!1,isTimeUpdate:!1,isFFSend:!1,isLs:!1},i.vt=0,i.pt=0,i.fvt=0,i.newPointTime=ti(),i.loadedCostTime=0,i.startCostTime=0,i._isSeeking=!1,i.seekingStart=0,i.waitingStart=0,i.fixedWaitingStart=0,i._isWaiting=!1,i._waitTimer&&W.clearTimeout(E(i),i._waitTimer),i._waittTimer&&W.clearTimeout(E(i),i._waittTimer),i._waitTimer=null,i._waittTimer=null,i._waitType=0})),w(E(i),"_onSeeking",(function(){i.seekingStart||(i.suspendWaitingStatus("seek"),i.seekingStart=ti(),i.emitLog(ai,{start:ti()}))})),w(E(i),"_onSeeked",(function(){i.suspendSeekingStatus("seeked")})),w(E(i),"_onWaitingLoadStart",(function(){i._isWaiting||i.vt||(i._isWaiting=!0,i.waitingStart=ti(),i.fixedWaitingStart=ti(),i._waitType=1,i.emitLog(ri,{fixedStart:i.fixedWaitingStart,start:i.waitingStart,type:1,endType:"loadstart"}))})),w(E(i),"_onWaiting",(function(){!i._isWaiting&&i.vt&&(i._isWaiting=!0,i.vt?i.seekingStart?i._waitType=2:i._waitType=0:i._waitType=1,i.fixedWaitingStart=ti(),i._waitTimer=W.setTimeout(E(i),(function(){i._isWaiting&&(i.waitingStart=ti(),W.clearTimeout(E(i),i._waitTimer),i._waitTimer=null,i._startWaitTimeout(),i.emitLog(ri,{fixedStart:i.fixedWaitingStart,start:i.waitingStart,type:i._waitType,endType:2===i._waitType?"seek":"playing"}))}),200))})),w(E(i),"_onError",(function(){i.suspendSeekingStatus("error"),i.suspendWaitingStatus("error")})),w(E(i),"_onPlaying",(function(){i._isWaiting&&i.suspendWaitingStatus("playing")})),i}return _(t,[{key:"afterCreate",value:function(){var e=this;this._onReset(),this._waitType="firstFrame",this._initOnceEvents(),this.newPointTime=ti(),this.loadedCostTime=0,this.startCostTime=0,this.on(ge,(function(){var t=e._state,i=t.autoplayStart,n=t.isFFSend;e.startCostTime=ti()-e.newPointTime,j.logInfo("[xgLogger]".concat(e.player.playerId," LOAD_START"),"autoplayStart:".concat(i," isFFSend:").concat(n," startCostTime:").concat(e.startCostTime," newPointTime").concat(e.newPointTime)),n||(!t.isLs&&e.emitLog(ii,{}),t.isLs=!0,t.isTimeUpdate=!1,t.isFFLoading=!0,e.pt=ti(),e.vt=0,e.fvt=0,e._initOnceEvents(),e._onWaitingLoadStart())})),this.on(de,(function(){e.vt=ti(),e.fvt=e.vt-e.pt,e.loadedCostTime=e.vt-e.newPointTime;var t=e._state,i=t.isTimeUpdate,n=t.isFFSend,o=t.autoplayStart;j.logInfo("[xgLogger]".concat(e.player.playerId," LOADED_DATA"),"fvt:".concat(e.fvt," isTimeUpdate:").concat(e._state.isTimeUpdate," loadedCostTime:").concat(e.loadedCostTime)),(i||o)&&e._sendFF("loadedData"),n||e.emitLog(ni,{}),e.suspendWaitingStatus("loadeddata")})),this.on(re,this._onSeeking),this.on(se,this._onSeeked),this.on(xe,(function(){e.endState("destroy")})),this.on(Se,(function(){e.endState("urlChange"),j.logInfo("[xgLogger]".concat(e.player.playerId," URL_CHANGE")),e._state.isFFSend&&e._onReset()})),this.on([te,ce],this._onPlaying),this.on(le,this._onWaiting),this.on(oe,this._onError),this.on(Ge,(function(){j.logInfo("[xgLogger]".concat(e.player.playerId," RESET")),e.endState("reset"),e._initOnceEvents(),e._onReset()}))}},{key:"_initOnceEvents",value:function(){this.off(be,this._onAutoplayStart),this.off(ae,this._onTimeupdate),this.once(be,this._onAutoplayStart),this.once(ae,this._onTimeupdate)}},{key:"_sendFF",value:function(e){this.s=ti();var t=this._state,i=t.isFFLoading,n=t.isFFSend;j.logInfo("[xgLogger]".concat(this.player.playerId," _sendFF"),"".concat(e," fvt:").concat(this.fvt," isFFLoading:").concat(i," !isFFSend:").concat(!n)),this.vt>0&&i&&!n&&(j.logInfo("[xgLogger]".concat(this.player.playerId," emitLog_firstFrame"),e),this._state.isFFLoading=!1,this._state.isFFSend=!0,this.emitLog(oi,{fvt:this.fvt,costTime:this.fvt,vt:this.vt,startCostTime:this.startCostTime,loadedCostTime:this.loadedCostTime}))}},{key:"_startWaitTimeout",value:function(){var e=this;this._waittTimer&&W.clearTimeout(this,this._waittTimer),this._waittTimer=W.setTimeout(this,(function(){e.suspendWaitingStatus("timeout"),W.clearTimeout(e,e._waittTimer),e._waittTimer=null}),this.config.waitTimeout)}},{key:"endState",value:function(e){this.suspendWaitingStatus(e),this.suspendSeekingStatus(e)}},{key:"suspendSeekingStatus",value:function(e){if(this.seekingStart){var t=ti(),i=t-this.seekingStart;this.seekingStart=0,this.emitLog(li,{end:t,costTime:i,endType:e})}}},{key:"suspendWaitingStatus",value:function(e){if(this._waitTimer&&(W.clearTimeout(this,this._waitTimer),this._waitTimer=null),this._waittTimer&&(W.clearTimeout(this,this._waittTimer),this._waittTimer=null),this._isWaiting=!1,this.waitingStart){var t=ti(),i=t-this.waitingStart,n=t-this.fixedWaitingStart,o=this.config.waitTimeout;this._isWaiting=!1,this.waitingStart=0,this.fixedWaitingStart=0,this.emitLog(si,{fixedCostTime:n>o?o:n,costTime:i>o?o:i,type:"loadeddata"===e?1:this._waitType,endType:2===this._waitType?"seek":e})}}},{key:"emitLog",value:function(e,t){var i=this.player;this.emit(Ve,m({t:ti(),host:W.getHostFromUrl(i.currentSrc),vtype:i.vtype,eventType:e,currentTime:this.player.currentTime,readyState:i.video.readyState,networkState:i.video.networkState},t))}}],[{key:"pluginName",get:function(){return"xgLogger"}},{key:"defaultConfig",get:function(){return{waitTimeout:1e4}}}]),t}();function ui(){return(new DOMParser).parseFromString('<svg class="xgplayer-replay-svg" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 78 78" width="78" height="78">\n  <path fill="#fff" transform="translate(20, 20)" d="M8.22708362,13.8757234 L11.2677371,12.6472196 C11.7798067,12.4403301 12.3626381,12.6877273 12.5695276,13.1997969 L12.9441342,14.1269807 C13.1510237,14.6390502 12.9036264,15.2218816 12.3915569,15.4287712 L6.8284538,17.6764107 L5.90126995,18.0510173 C5.38920044,18.2579068 4.80636901,18.0105096 4.5994795,17.49844 L1.97723335,11.0081531 C1.77034384,10.4960836 2.0177411,9.91325213 2.52981061,9.70636262 L3.45699446,9.33175602 C3.96906396,9.12486652 4.5518954,9.37226378 4.75878491,9.88433329 L5.67885163,12.1615783 C7.99551726,6.6766934 13.3983951,3 19.5,3 C27.7842712,3 34.5,9.71572875 34.5,18 C34.5,26.2842712 27.7842712,33 19.5,33 C15.4573596,33 11.6658607,31.3912946 8.87004692,28.5831991 C8.28554571,27.9961303 8.28762719,27.0463851 8.87469603,26.4618839 C9.46176488,25.8773827 10.4115101,25.8794641 10.9960113,26.466533 C13.2344327,28.7147875 16.263503,30 19.5,30 C26.127417,30 31.5,24.627417 31.5,18 C31.5,11.372583 26.127417,6 19.5,6 C14.4183772,6 9.94214483,9.18783811 8.22708362,13.8757234 Z"></path>\n</svg>\n',"image/svg+xml").firstChild}var hi=function(){T(t,Et);var e=I(t);function t(){return C(this,t),e.apply(this,arguments)}return _(t,[{key:"registerIcons",value:function(){return{replay:ui}}},{key:"afterCreate",value:function(){var e=this;Et.insert(this.icons.replay,this.root,0),this.__handleReplay=this.hook("replayClick",(function(){e.player.replay()}),{pre:function(e){e.preventDefault(),e.stopPropagation()}}),this.bind(".xgplayer-replay",["click","touchend"],this.__handleReplay),this.on(ie,(function(){if(e.playerConfig.loop||W.addClass(e.player.root,"replay"),!e.config.disable){e.show();var t=e.root.querySelector("path");if(t){var i=window.getComputedStyle(t).getPropertyValue("transform");if("string"==typeof i&&i.indexOf("none")>-1)return null;t.setAttribute("transform",i)}}})),this.on(ee,(function(){e.hide()}))}},{key:"handleReplay",value:function(e){e.preventDefault(),e.stopPropagation(),this.player.replay(),W.removeClass(this.player.root,"replay")}},{key:"show",value:function(e){this.config.disable||(this.root.style.display="flex")}},{key:"enable",value:function(){this.config.disable=!1}},{key:"disable",value:function(){this.config.disable=!0,this.hide()}},{key:"destroy",value:function(){this.unbind(".xgplayer-replay",["click","touchend"],this.__handleReplay)}},{key:"render",value:function(){return'<xg-replay class="xgplayer-replay">\n      <xg-replay-txt class="xgplayer-replay-txt" lang-key="'.concat(this.i18nKeys.REPLAY,'">').concat(this.i18n.REPLAY,"</xg-replay-txt>\n    </xg-replay>")}}],[{key:"pluginName",get:function(){return"replay"}},{key:"defaultConfig",get:function(){return{disable:!1}}}]),t}(),di=function(){T(t,Et);var e=I(t);function t(){return C(this,t),e.apply(this,arguments)}return _(t,[{key:"isEndedShow",get:function(){return this.config.isEndedShow},set:function(e){this.config.isEndedShow=e}},{key:"hide",value:function(){W.addClass(this.root,"hide")}},{key:"show",value:function(e){W.removeClass(this.root,"hide")}},{key:"beforeCreate",value:function(e){"string"==typeof e.player.config.poster&&(e.config.poster=e.player.config.poster)}},{key:"afterCreate",value:function(){var e=this;this.on(ie,(function(){e.isEndedShow&&W.removeClass(e.root,"hide")})),this.config.hideCanplay?(this.once(ae,(function(){e.onTimeUpdate()})),this.on(Se,(function(){W.removeClass(e.root,"hide"),W.addClass(e.root,"xg-showplay"),e.once(ae,(function(){e.onTimeUpdate()}))}))):this.on(ee,(function(){W.addClass(e.root,"hide")}))}},{key:"setConfig",value:function(e){var t=this;Object.keys(e).forEach((function(i){t.config[i]=e[i]}));var i=this.config.poster;this.update(i)}},{key:"onTimeUpdate",value:function(){var e=this;this.player.currentTime?W.removeClass(this.root,"xg-showplay"):this.once(ae,(function(){e.onTimeUpdate()}))}},{key:"update",value:function(e){e&&(this.config.poster=e,this.root.style.backgroundImage="url(".concat(e,")"))}},{key:"getBgSize",value:function(e){var t="";switch(e){case"cover":t="cover";break;case"contain":t="contain";break;case"fixHeight":t="auto 100%";break;default:t=""}return t?"background-size: ".concat(t,";"):""}},{key:"render",value:function(){var e=this.config,t=e.poster,i=e.hideCanplay,n=e.fillMode,o=e.notHidden,r=this.getBgSize(n),s=t?"background-image:url(".concat(t,");").concat(r):r;return'<xg-poster class="xgplayer-poster '.concat(o?"xg-not-hidden":i?"xg-showplay":"",'" style="').concat(s,'">\n    </xg-poster>')}}],[{key:"pluginName",get:function(){return"poster"}},{key:"defaultConfig",get:function(){return{isEndedShow:!0,hideCanplay:!1,notHidden:!1,poster:"",fillMode:"fixWidth"}}}]),t}();function fi(){return(new DOMParser).parseFromString('<svg class="play" xmlns="http://www.w3.org/2000/svg" width="28" height="40" viewBox="3 -4 28 40">\n  <path fill="#fff" transform="scale(0.0320625 0.0320625)" d="M576,363L810,512L576,661zM342,214L576,363L576,661L342,810z"></path>\n</svg>\n',"image/svg+xml").firstChild}function pi(){return(new DOMParser).parseFromString('<svg class="pause" xmlns="http://www.w3.org/2000/svg" width="28" height="40" viewBox="3 -4 28 40">\n  <path fill="#fff" transform="scale(0.0320625 0.0320625)" d="M598,214h170v596h-170v-596zM256 810v-596h170v596h-170z"></path>\n</svg>\n',"image/svg+xml").firstChild}var gi={};function vi(e){e?window.clearTimeout(e):Object.keys(gi).map((function(e){window.clearTimeout(gi[e].id),delete gi[e]}))}var yi=function(){T(t,Et);var e=I(t);function t(i){var n;return C(this,t),w(E(n=e.call(this,i)),"onPlayerReset",(function(){n.autoPlayStart=!1;var e="auto"===n.config.mode?"auto-hide":"hide";n.setAttr("data-state","play"),W.removeClass(n.root,e),n.show()})),w(E(n),"onAutoplayStart",(function(){if(!n.autoPlayStart){var e="auto"===n.config.mode?"auto-hide":"hide";W.addClass(n.root,e),n.autoPlayStart=!0,n.toggleTo("play")}})),n.autoPlayStart=!1,n}return _(t,[{key:"afterCreate",value:function(){var e=this.playerConfig;this.initIcons(),this.listenEvents(),this.bindClickEvents(),e.autoplay||this.show()}},{key:"listenEvents",value:function(){var e=this,t=this.player,i=this.playerConfig;this.once(ke,(function(){i&&(i.lang&&"en"===i.lang?W.addClass(t.root,"lang-is-en"):"jp"===i.lang&&W.addClass(t.root,"lang-is-jp"))})),this.on(be,this.onAutoplayStart),this.on(_e,(function(){var t="auto"===e.config.mode?"auto-hide":"hide";e.setAttr("data-state","play"),W.removeClass(e.root,t),e.show()})),this.on(ee,(function(){e.toggleTo("play")})),this.on(ne,(function(){e.toggleTo("pause")})),this.on(Ge,(function(){e.onPlayerReset()}))}},{key:"bindClickEvents",value:function(){var e=this;this.clickHandler=this.hook("startClick",this.switchPausePlay,{pre:function(t){t.cancelable&&t.preventDefault(),t.stopPropagation();var i=e.player.paused;e.emitUserAction(t,"switch_play_pause",{props:"paused",from:i,to:!i})}}),this.bind(["click","touchend"],this.clickHandler)}},{key:"registerIcons",value:function(){return{startPlay:{icon:fi,class:"xg-icon-play"},startPause:{icon:pi,class:"xg-icon-pause"}}}},{key:"initIcons",value:function(){var e=this.icons;this.appendChild("xg-start-inner",e.startPlay),this.appendChild("xg-start-inner",e.startPause)}},{key:"hide",value:function(){W.addClass(this.root,"hide")}},{key:"show",value:function(e){W.removeClass(this.root,"hide")}},{key:"focusHide",value:function(){W.addClass(this.root,"focus-hide")}},{key:"recover",value:function(){W.removeClass(this.root,"focus-hide")}},{key:"switchStatus",value:function(e){e?this.setAttr("data-state",this.player.paused?"pause":"play"):this.setAttr("data-state",this.player.paused?"play":"pause")}},{key:"animate",value:function(e){var t=this;this._animateId=function(e,t){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{start:null,end:null};return gi[e]&&window.clearTimeout(gi[e].id),gi[e]={},i.start&&i.start(),gi[e].id=window.setTimeout((function(){i.end&&i.end(),window.clearTimeout(gi[e].id),delete gi[e]}),t),gi[e].id}("pauseplay",400,{start:function(){W.addClass(t.root,"interact"),t.show(),t.switchStatus(!0)},end:function(){W.removeClass(t.root,"interact"),!e&&t.hide(),t._animateId=null}})}},{key:"endAnimate",value:function(){W.removeClass(this.root,"interact"),vi(this._animateId),this._animateId=null}},{key:"switchPausePlay",value:function(e){var t=this.player;(e.cancelable&&e.preventDefault(),e.stopPropagation(),t.state<Ut)||(this.player.paused||t.state!==Gt?t.play():t.pause())}},{key:"onPlayPause",value:function(e){this.toggleTo(e)}},{key:"toggleTo",value:function(e){var t=this.config,i=this.player;if(i&&!(i.state<Gt)&&this.autoPlayStart){if("show"===t.mode)return this.switchStatus(),void this.show();if("auto"!==t.mode){if(t.isShowPause&&i.paused&&!i.ended||t.isShowEnd&&i.ended)return this.switchStatus(),this.show(),void this.endAnimate();if(t.disableAnimate)return this.switchStatus(),void this.hide();if("play"===e)this.autoPlayStart?this.animate():this.hide();else{if(!this.autoPlayStart||i.ended)return;this.animate()}}else this.switchStatus()}}},{key:"destroy",value:function(){this.unbind(["click","touchend"],this.clickHandler),vi(this._animateId)}},{key:"render",value:function(){var e=this.playerConfig.autoplay?"auto"===this.config.mode?"auto-hide":"hide":"";return'\n    <xg-start class="xgplayer-start '.concat(e,'">\n    <xg-start-inner></xg-start-inner>\n    </xg-start>')}}],[{key:"pluginName",get:function(){return"start"}},{key:"defaultConfig",get:function(){return{isShowPause:!1,isShowEnd:!1,disableAnimate:!1,mode:"hide"}}}]),t}(),mi=function(){T(t,Et);var e=I(t);function t(){return C(this,t),e.apply(this,arguments)}return _(t,[{key:"render",value:function(){var e=this.config.innerHtml,t=W.createDom("xg-enter","",{},"xgplayer-enter");if(e&&e instanceof window.HTMLElement)t.appendChild(e);else if(e&&"string"==typeof e)t.innerHTML=e;else{for(var i="",n=1;n<=12;n++)i+='<div class="xgplayer-enter-bar'.concat(n,'"></div>');t.innerHTML='<div class="xgplayer-enter-spinner">'.concat(i,"</div>")}return t}}],[{key:"pluginName",get:function(){return"enter"}},{key:"defaultConfig",get:function(){return{innerHtml:"",logo:""}}}]),t}();function ki(e,t,i){try{return' <div class="xg-tips '.concat(i?"hide":" ",'" lang-key="').concat(e.i18nKeys[t],'">\n    ').concat(e.i18n[t],"\n    </div>")}catch(n){return'<div class="xg-tips hide"></div>'}}var Ci=function(){T(t,Et);var e=I(t);function t(){return C(this,t),e.apply(this,arguments)}return _(t,[{key:"afterCreate",value:function(){this.getMini=this.getMini.bind(this),this.exitMini=this.exitMini.bind(this),this.bind("click",this.getMini)}},{key:"getMini",value:function(){this.config.onClick&&this.config.onClick()}},{key:"exitMini",value:function(){this.config.onClick&&this.config.onClick()}},{key:"destroy",value:function(){this.unbind(["click","touchend"],this.getMini)}},{key:"render",value:function(){var e="MINISCREEN";return'\n      <xg-icon class="xgplayer-miniicon">\n      <div class="xgplayer-icon btn-text"><span class="icon-text" lang-key="'.concat(this.i18nKeys[e],'">').concat(this.i18n[e],"</span></div>\n      </xg-icon>")}}],[{key:"pluginName",get:function(){return"miniscreenIcon"}},{key:"defaultConfig",get:function(){return{position:_t.CONTROLS_RIGHT,index:10}}}]),t}();function bi(e){var t=parseFloat(e);return-1===e.indexOf("%")&&!Number.isNaN(t)&&t}var _i=["paddingLeft","paddingRight","paddingTop","paddingBottom","marginLeft","marginRight","marginTop","marginBottom","borderLeftWidth","borderRightWidth","borderTopWidth","borderBottomWidth"],wi=_i.length;function Ti(e){if("string"==typeof e&&(e=document.querySelector(e)),e&&"object"===k(e)&&e.nodeType){var t=function(e){return window.getComputedStyle(e)}(e);if("none"===t.display)return function(){for(var e={width:0,height:0,innerWidth:0,innerHeight:0,outerWidth:0,outerHeight:0},t=0;t<wi;t++)e[_i[t]]=0;return e}();var i={};i.width=e.offsetWidth,i.height=e.offsetHeight;for(var n=i.isBorderBox="border-box"===t.boxSizing,o=0;o<wi;o++){var r=_i[o],s=t[r],a=parseFloat(s);i[r]=Number.isNaN(a)?0:a}var l=i.paddingLeft+i.paddingRight,c=i.paddingTop+i.paddingBottom,u=i.marginLeft+i.marginRight,h=i.marginTop+i.marginBottom,d=i.borderLeftWidth+i.borderRightWidth,f=i.borderTopWidth+i.borderBottomWidth,p=n,g=bi(t.width);!1!==g&&(i.width=g+(p?0:l+d));var v=bi(t.height);return!1!==v&&(i.height=v+(p?0:c+f)),i.innerWidth=i.width-(l+d),i.innerHeight=i.height-(c+f),i.outerWidth=i.width+u,i.outerHeight=i.height+h,i}}function xi(e,t){for(var i=0;i<e.length;i++){var n=e[i];if(n.identifier===t)return n}}var Si="dragStart",Ei="dragMove",Pi="dragEnded",Ii={mousedown:["mousemove","mouseup"],touchstart:["touchmove","touchend","touchcancel"],pointerdown:["pointermove","pointerup","pointercancel"]},Li=function(){T(t,F);var e=I(t);function t(i){var n,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return C(this,t),(n=e.call(this)).isEnabled=!0,n.isDragging=!1,n.isDown=!1,n.position={},n.downPoint={},n.dragPoint={x:0,y:0},n.startPos={x:0,y:0},n._root=i instanceof Element?i:document.querySelector(i),n._handlerDom=o.handle instanceof Element?o.handle:document.querySelector(o.handle),n._root&&n._handlerDom?(n._bindStartEvent(),n):P(n)}return _(t,[{key:"_bindStartEvent",value:function(){var e=this;"ontouchstart"in window?this._startKey="touchstart":this._startKey="mousedown",this["on".concat(this._startKey)]=this["on".concat(this._startKey)].bind(this),this._handlerDom.addEventListener(this._startKey,this["on".concat(this._startKey)]),Ii[this._startKey].map((function(t){e["on".concat(t)]=e["on".concat(t)].bind(e)}))}},{key:"_unbindStartEvent",value:function(){this._handlerDom.removeEventListener(this._startKey,this["on".concat(this._startKey)])}},{key:"_bindPostStartEvents",value:function(e){var t=this;if(e){var i=Ii[this._startKey];i.map((function(e){window.addEventListener(e,t["on".concat(e)])})),this._boundPointerEvents=i}}},{key:"_unbindPostStartEvents",value:function(){var e=this;this._boundPointerEvents&&(this._boundPointerEvents.map((function(t){window.removeEventListener(t,e["on".concat(t)])})),delete this._boundPointerEvents)}},{key:"enable",value:function(){this.isEnabled=!0}},{key:"disable",value:function(){this.isEnabled=!1,this.isDragging&&this.onUp()}},{key:"onDocUp",value:function(e){this.onUp()}},{key:"animate",value:function(){var e=this;this.isDragging&&(this.positionDrag(),window.requestAnimationFrame((function(){e.animate()})))}},{key:"positionDrag",value:function(){var e="translate3d(".concat(this.dragPoint.x,"px, ").concat(this.dragPoint.y,"px, 0)");this._root.style.transform=e,this._root.style.webKitTransform=e}},{key:"setLeftTop",value:function(){this._root.style.left=this.position.x+"px",this._root.style.top=this.position.y+"px"}},{key:"onmousedown",value:function(e){this.dragStart(e,e)}},{key:"onmousemove",value:function(e){this.dragMove(e,e)}},{key:"onmouseup",value:function(e){this.dragEnd(e,e)}},{key:"ontouchstart",value:function(e){var t=e.changedTouches[0];this.dragStart(e,t),this.touchIdentifier=void 0!==t.pointerId?t.pointerId:t.identifier,e.preventDefault()}},{key:"ontouchmove",value:function(e){var t=xi(e.changedTouches,this.touchIdentifier);t&&this.dragMove(e,t)}},{key:"ontouchend",value:function(e){var t=xi(e.changedTouches,this.touchIdentifier);t&&this.dragEnd(e,t),e.preventDefault()}},{key:"ontouchcancel",value:function(e){var t=xi(e.changedTouches,this.touchIdentifier);t&&this.dragCancel(e,t)}},{key:"dragStart",value:function(e,t){if(this._root&&!this.isDown&&this.isEnabled){this.downPoint=t,this.dragPoint.x=0,this.dragPoint.y=0,this._getPosition();var i=Ti(this._root);this.startPos.x=this.position.x,this.startPos.y=this.position.y,this.startPos.maxY=window.innerHeight-i.height,this.startPos.maxX=window.innerWidth-i.width,this.setLeftTop(),this.isDown=!0,this._bindPostStartEvents(e)}}},{key:"dragRealStart",value:function(e,t){this.isDragging=!0,this.animate(),this.emit(Si,this.startPos)}},{key:"dragEnd",value:function(e,t){this._root&&(this._unbindPostStartEvents(),this.isDragging&&(this._root.style.transform="",this.setLeftTop(),this.emit(Pi)),this.presetInfo())}},{key:"_dragPointerMove",value:function(e,t){var i={x:t.pageX-this.downPoint.pageX,y:t.pageY-this.downPoint.pageY};return!this.isDragging&&this.hasDragStarted(i)&&this.dragRealStart(e,t),i}},{key:"dragMove",value:function(e,t){if(e=e||window.event,this.isDown){var i=this.startPos,n=i.x,o=i.y,r=this._dragPointerMove(e,t),s=r.x,a=r.y;s=this.checkContain("x",s,n),a=this.checkContain("y",a,o),this.position.x=n+s,this.position.y=o+a,this.dragPoint.x=s,this.dragPoint.y=a,this.emit(Ei,this.position)}}},{key:"dragCancel",value:function(e,t){this.dragEnd(e,t)}},{key:"presetInfo",value:function(){this.isDragging=!1,this.startPos={x:0,y:0},this.dragPoint={x:0,y:0},this.isDown=!1}},{key:"destroy",value:function(){this._unbindStartEvent(),this._unbindPostStartEvents(),this.isDragging&&this.dragEnd(),this.removeAllListeners(),this._handlerDom=null}},{key:"hasDragStarted",value:function(e){return Math.abs(e.x)>3||Math.abs(e.y)>3}},{key:"checkContain",value:function(e,t,i){return t+i<0?0-i:"x"===e&&t+i>this.startPos.maxX?this.startPos.maxX-i:"y"===e&&t+i>this.startPos.maxY?this.startPos.maxY-i:t}},{key:"_getPosition",value:function(){var e=window.getComputedStyle(this._root),t=this._getPositionCoord(e.left,"width"),i=this._getPositionCoord(e.top,"height");this.position.x=Number.isNaN(t)?0:t,this.position.y=Number.isNaN(i)?0:i,this._addTransformPosition(e)}},{key:"_addTransformPosition",value:function(e){var t=e.transform;if(0===t.indexOf("matrix")){var i=t.split(","),n=0===t.indexOf("matrix3d")?12:4,o=parseInt(i[n],10),r=parseInt(i[n+1],10);this.position.x+=o,this.position.y+=r}}},{key:"_getPositionCoord",value:function(e,t){if(-1!==e.indexOf("%")){var i=Ti(this._root.parentNode);return i?parseFloat(e)/100*i[t]:0}return parseInt(e,10)}}]),t}(),Ai=function(){T(t,Et);var e=I(t);function t(i){var n;C(this,t),w(E(n=e.call(this,i)),"onCancelClick",(function(e){n.exitMini(),n.isClose=!0})),w(E(n),"onCenterClick",(function(e){var t=E(n).player;t.paused?t.play():t.pause()})),w(E(n),"onScroll",(function(e){if(!(!window.scrollY&&0!==window.scrollY||Math.abs(window.scrollY-n.pos.scrollY)<50)){var t=parseInt(W.getCss(n.player.root,"height"));t+=n.config.scrollTop,n.pos.scrollY=window.scrollY,window.scrollY>t+5?!n.isMini&&!n.isClose&&n.getMini():window.scrollY<=t&&(n.isMini&&n.exitMini(),n.isClose=!1)}})),n.isMini=!1,n.isClose=!1;var o=E(n).config;return n.pos={left:o.left<0?window.innerWidth-o.width-20:o.left,top:o.top<0?window.innerHeight-o.height-20:o.top,height:n.config.height,width:n.config.width,scrollY:window.scrollY||0},n.lastStyle=null,n}return _(t,[{key:"beforeCreate",value:function(e){"boolean"==typeof e.player.config.mini&&(e.config.isShowIcon=e.player.config.mini)}},{key:"afterCreate",value:function(){var e=this;this.initIcons(),this.on(ne,(function(){e.setAttr("data-state","pause")})),this.on(ee,(function(){e.setAttr("data-state","play")}))}},{key:"onPluginsReady",value:function(){var e=this,t=this.player;if(!this.config.disable){if(this.config.isShowIcon){var i={config:{onClick:function(){e.getMini()}}};t.controls.registerPlugin(Ci,i,Ci.pluginName)}var n=W.checkTouchSupport()?"touchend":"click";this.bind(".mini-cancel-btn",n,this.onCancelClick),this.bind(".play-icon",n,this.onCenterClick),this.config.disableDrag||(this._draggabilly=new Li(this.player.root,{handle:this.root})),this.config.isScrollSwitch&&window.addEventListener("scroll",this.onScroll)}}},{key:"registerIcons",value:function(){return{play:{icon:fi,class:"xg-icon-play"},pause:{icon:pi,class:"xg-icon-pause"}}}},{key:"initIcons",value:function(){var e=this.icons;this.appendChild(".play-icon",e.play),this.appendChild(".play-icon",e.pause)}},{key:"getMini",value:function(){var e=this;if(!this.isMini){var t=this.player,i=this.playerConfig,n=this.config.target||this.player.root;this.lastStyle={},W.addClass(t.root,"xgplayer-mini"),["width","height","top","left"].map((function(t){e.lastStyle[t]=n.style[t],n.style[t]="".concat(e.pos[t],"px")})),i.fluid&&(n.style["padding-top"]=""),this.emit(De,!0),t.isMini=this.isMini=!0}}},{key:"exitMini",value:function(){var e=this;if(!this.isMini)return!1;var t=this.player,i=this.playerConfig,n=this.config.target||this.player.root;W.removeClass(t.root,"xgplayer-mini"),this.lastStyle&&Object.keys(this.lastStyle).map((function(t){n.style[t]=e.lastStyle[t]})),this.lastStyle=null,i.fluid&&(t.root.style.width="100%",t.root.style.height="0",t.root.style["padding-top"]="".concat(100*i.height/i.width,"%")),this.emit(De,!1),this.isMini=t.isMini=!1}},{key:"destroy",value:function(){window.removeEventListener("scroll",this.onScroll);var e=W.checkTouchSupport()?"touchend":"click";this.unbind(".mini-cancel-btn",e,this.onCancelClick),this.unbind(".play-icon",e,this.onCenterClick),this._draggabilly&&this._draggabilly.destroy(),this._draggabilly=null,this.exitMini()}},{key:"render",value:function(){if(!this.config.disable)return'\n      <xg-mini-layer class="xg-mini-layer">\n      <xg-mini-header class="xgplayer-mini-header">\n      '.concat(ki(this,"MINI_DRAG",this.playerConfig.isHideTips),'\n      </xg-mini-header>\n      <div class="mini-cancel-btn">\n        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20">\n          <path fill="#fff" fill-rule="evenodd" d="M3.99 3.49a1 1 0 0 1 1.414 0L10 8.085l4.596-4.595a1 1 0 1 1 1.414 1.414L11.414 9.5l4.596 4.596a1 1 0 0 1 .084 1.32l-.084.094a1 1 0 0 1-1.414 0L10 10.914 5.404 15.51a1 1 0 0 1-1.414-1.414L8.585 9.5 3.99 4.904a1 1 0 0 1-.084-1.32z"></path>\n        </svg>\n      </div>\n      <div class="play-icon">\n      </div>\n      </xg-mini-layer>')}}],[{key:"pluginName",get:function(){return"miniscreen"}},{key:"defaultConfig",get:function(){return{index:10,disable:!1,width:320,height:180,left:-1,top:-1,isShowIcon:!1,isScrollSwitch:!1,scrollTop:0,disableDrag:!1}}}]),t}(),Oi={mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",mousemove:"onMouseMove"},Di=["videoClick","videoDbClick"],Ri=function(){T(t,kt);var e=I(t);function t(){var i;C(this,t);for(var n=arguments.length,o=new Array(n),r=0;r<n;r++)o[r]=arguments[r];return w(E(i=e.call.apply(e,[this].concat(o))),"onMouseMove",(function(e){var t=E(i),n=t.player,o=t.playerConfig;n.isActive||(n.focus({autoHide:!o.closeDelayBlur}),!o.closeFocusVideoFocus&&n.media.focus())})),w(E(i),"onMouseEnter",(function(e){var t=E(i),n=t.playerConfig,o=t.player;!n.closeFocusVideoFocus&&o.media.focus(),n.closeDelayBlur?o.focus({autoHide:!1}):o.focus(),i.emit(Ie)})),w(E(i),"onMouseLeave",(function(e){var t=i.playerConfig,n=t.closePlayerBlur,o=t.leavePlayerTime,r=t.closeDelayBlur;n||r||(o?i.player.focus({autoHide:!0,delay:o}):i.player.blur({ignorePaused:!0})),i.emit(Pe)})),w(E(i),"onVideoClick",(function(e){var t=E(i),n=t.player,o=t.playerConfig;e.target&&o.closeVideoClick||e.target!==n.root&&e.target!==n.media&&e.target!==n.innerContainer&&e.target!==n.media.__canvas||(e.preventDefault(),o.closeVideoStopPropagation||e.stopPropagation(),i._clickCount++,i.clickTimer&&(clearTimeout(i.clickTimer),i.clickTimer=null),i.clickTimer=setTimeout((function(){i._clickCount&&(i._clickCount--,ft(E(i),Di[0],(function(e,t){i.switchPlayPause(t.e)}),{e:e,paused:n.paused}),clearTimeout(i.clickTimer),i.clickTimer=null)}),300))})),w(E(i),"onVideoDblClick",(function(e){var t=E(i),n=t.player,o=t.playerConfig;o.closeVideoDblclick||!e.target||e.target!==n.media&&e.target!==n.media.__canvas||(!o.closeVideoClick&&i._clickCount<2?i._clickCount=0:(i._clickCount=0,i.clickTimer&&(clearTimeout(i.clickTimer),i.clickTimer=null),e.preventDefault(),e.stopPropagation(),ft(E(i),Di[1],(function(e,t){i.emitUserAction(t.e,"switch_fullscreen",{props:"fullscreen",from:n.fullscreen,to:!n.fullscreen}),n.fullscreen?n.exitFullscreen():n.getFullscreen()}),{e:e,fullscreen:n.fullscreen})))})),i}return _(t,[{key:"afterCreate",value:function(){var e=this;this._clickCount=0,Di.map((function(t){e.__hooks[t]=null})),"mobile"===this.playerConfig.isMobileSimulateMode||"mobile"===q.device&&!q.os.isIpad||this.initEvents()}},{key:"initEvents",value:function(){var e=this,t=this.player,i=t.media,n=t.root,o=this.playerConfig.enableContextmenu;n&&n.addEventListener("click",this.onVideoClick,!1),n&&n.addEventListener("dblclick",this.onVideoDblClick,!1),Object.keys(Oi).map((function(t){n.addEventListener(t,e[Oi[t]],!1)})),!o&&i&&i.addEventListener("contextmenu",this.onContextmenu,!1)}},{key:"switchPlayPause",value:function(e){var t=this.player;this.emitUserAction(e,"switch_play_pause",{props:"paused",from:t.paused,to:!t.paused}),t.ended?t.duration!==1/0&&t.duration>0&&t.replay():t.paused?t.play():t.pause()}},{key:"onContextmenu",value:function(e){(e=e||window.event).preventDefault&&e.preventDefault(),e.stopPropagation?e.stopPropagation():(e.returnValue=!1,e.cancelBubble=!0)}},{key:"destroy",value:function(){var e=this,t=this.player,i=t.video,n=t.root;this.clickTimer&&clearTimeout(this.clickTimer),n.removeEventListener("click",this.onVideoClick,!1),n.removeEventListener("dblclick",this.onVideoDblClick,!1),i.removeEventListener("contextmenu",this.onContextmenu,!1),Object.keys(Oi).map((function(t){n.removeEventListener(t,e[Oi[t]],!1)}))}}],[{key:"pluginName",get:function(){return"pc"}},{key:"defaultConfig",get:function(){return{}}}]),t}(),Mi="press",Ni="pressend",Fi="doubleclick",Hi="click",Bi="touchmove",Ui="touchstart",ji="touchend",Vi={start:"touchstart",end:"touchend",move:"touchmove",cancel:"touchcancel"},Wi={start:"mousedown",end:"mouseup",move:"mousemove",cancel:"mouseleave"};function Gi(e){return e&&e.length>0?e[e.length-1]:null}var zi=function(){function e(t){var i=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{eventType:"touch"};C(this,e),w(this,"onTouchStart",(function(e){var t=i._pos,n=i.root,o=Gi(e.touches);t.x=o?parseInt(o.pageX,10):e.pageX,t.y=o?parseInt(o.pageX,10):e.pageX,t.start=!0,i.__setPress(e),n.addEventListener(i.events.end,i.onTouchEnd),n.addEventListener(i.events.cancel,i.onTouchCancel),n.addEventListener(i.events.move,i.onTouchMove),i.trigger(Ui,e)})),w(this,"onTouchCancel",(function(e){i.onTouchEnd(e)})),w(this,"onTouchEnd",(function(e){var t=i._pos,n=i.root;i.__clearPress(),n.removeEventListener(i.events.cancel,i.onTouchCancel),n.removeEventListener(i.events.end,i.onTouchEnd),n.removeEventListener(i.events.move,i.onTouchMove),e.moving=t.moving,e.press=t.press,t.press&&i.trigger(Ni,e),i.trigger(ji,e),!t.press&&!t.moving&&i.__setDb(e),t.press=!1,t.start=!1,t.moving=!1})),w(this,"onTouchMove",(function(e){var t=i._pos,n=i.config,o=Gi(e.touches),r=o?parseInt(o.pageX,10):e.pageX,s=o?parseInt(o.pageY,10):e.pageX,a=r-t.x,l=s-t.y;Math.abs(l)<n.miniStep&&Math.abs(a)<n.miniStep||(i.__clearPress(),t.press&&i.trigger(Ni,e),t.press=!1,t.moving=!0,i.trigger(Bi,e))})),this._pos={moving:!1,start:!1,x:0,y:0},this.config={pressDelay:600,dbClickDelay:200,disablePress:!1,disableDbClick:!1,miniStep:2,needPreventDefault:!0},Object.keys(n).map((function(e){i.config[e]=n[e]})),this.root=t,this.events="mouse"===n.eventType?Wi:Vi,this.pressIntrvalId=null,this.dbIntrvalId=null,this.__handlers={},this._initEvent()}return _(e,[{key:"_initEvent",value:function(){this.root.addEventListener(this.events.start,this.onTouchStart)}},{key:"__setPress",value:function(e){var t=this,i=this.config;this.pressIntrvalId&&this.__clearPress(),this.pressIntrvalId=setTimeout((function(){t.trigger(Mi,e),t._pos.press=!0,t.__clearPress()}),i.pressDelay)}},{key:"__clearPress",value:function(){window.clearTimeout(this.pressIntrvalId),this.pressIntrvalId=null}},{key:"__setDb",value:function(e){var t=this,i=this.config;if(this.dbIntrvalId)return this.__clearDb(),void this.trigger(Fi,e);this.dbIntrvalId=setTimeout((function(){t.__clearDb(),t._pos.start||t._pos.press||t._pos.moving||t.trigger(Hi,e)}),i.dbClickDelay)}},{key:"__clearDb",value:function(){clearTimeout(this.dbIntrvalId),this.dbIntrvalId=null}},{key:"on",value:function(e,t){this.__handlers[e]||(this.__handlers[e]=[]),this.__handlers[e].push(t)}},{key:"off",value:function(e,t){if(this.__handlers[e]){for(var i=this.__handlers[e],n=-1,o=0;o<i.length;o++)if(i[o]===t){n=o;break}n>=0&&this.__handlers[e].splice(n,1)}}},{key:"trigger",value:function(e,t){this.__handlers[e]&&this.__handlers[e].map((function(e){try{e(t)}catch(i){}}))}},{key:"destroy",value:function(){var e=this,t={touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart"};Object.keys(t).forEach((function(i){e.root.removeEventListener(i,e[t[i]])}))}}]),e}();function Ki(){return(new DOMParser).parseFromString('<svg width="20" height="9" viewBox="0 0 8 9" fill="none" xmlns="http://www.w3.org/2000/svg"\n  xmlns:xlink="http://www.w3.org/1999/xlink">\n  <path opacity="0.54"\n    d="M7.5 3.63397C8.16667 4.01887 8.16667 4.98113 7.5 5.36603L1.5 8.83013C0.833334 9.21503 0 8.7339 0 7.9641L0 1.0359C0 0.266098 0.833333 -0.215027 1.5 0.169873L7.5 3.63397Z"\n    fill="white" />\n  <path transform="translate(5 0)" d="M7.5 3.63397C8.16667 4.01887 8.16667 4.98113 7.5 5.36603L1.5 8.83013C0.833334 9.21503 0 8.7339 0 7.9641L0 1.0359C0 0.266098 0.833333 -0.215027 1.5 0.169873L7.5 3.63397Z" fill="white"/>\n</svg>',"image/svg+xml").firstChild}var Yi="auto",Xi="seeking",qi="playbackrate",Zi=["videoClick","videoDbClick"],Ji=function(){T(t,Et);var e=I(t);function t(i){var n;return C(this,t),w(E(n=e.call(this,i)),"onTouchStart",(function(e){var t=E(n),i=t.player,o=t.config,r=t.pos,s=t.playerConfig,a=n.getTouche(e);if(a&&!o.disableGesture&&n.duration>0&&!i.ended){r.isStart=!0,n.timer&&clearTimeout(n.timer),W.checkIsFunction(s.disableSwipeHandler)&&s.disableSwipeHandler(),n.find(".xg-dur").innerHTML=W.format(n.duration);var l=n.root.getBoundingClientRect();90===i.rotateDeg?(r.top=l.left,r.left=l.top,r.width=l.height,r.height=l.width):(r.top=l.top,r.left=l.left,r.width=l.width,r.height=l.height);var c=parseInt(a.pageX-r.left,10),u=parseInt(a.pageY-r.top,10);r.x=90===i.rotateDeg?u:c,r.y=90===i.rotateDeg?c:u,r.scopeL=o.scopeL*r.width,r.scopeR=(1-o.scopeR)*r.width,r.scopeM1=r.width*(1-o.scopeM)/2,r.scopeM2=r.width-r.scopeM1}})),w(E(n),"onTouchMove",(function(e){var t=n.getTouche(e),i=E(n),o=i.pos,r=i.config,s=i.player;if(t&&!r.disableGesture&&n.duration&&o.isStart){var a=r.miniMoveStep,l=r.hideControlsActive,c=parseInt(t.pageX-o.left,10),u=parseInt(t.pageY-o.top,10),h=90===s.rotateDeg?u:c,d=90===s.rotateDeg?c:u;if(Math.abs(h-o.x)>a||Math.abs(d-o.y)>a){var f=h-o.x,p=d-o.y,g=o.scope;if(-1===g&&(0===(g=n.checkScope(h,d,f,p,o))&&(l?s.blur():s.focus({autoHide:!1}),!o.time&&(o.time=parseInt(1e3*s.currentTime,10)+1e3*n.timeOffset)),o.scope=g),-1===g||g>0&&!r.gestureY||0===g&&!r.gestureX)return;n.executeMove(f,p,g,o.width,o.height),o.x=h,o.y=d}}})),w(E(n),"onTouchEnd",(function(e){var t=E(n),i=t.player,o=t.pos,r=t.playerConfig;if(setTimeout((function(){i.getPlugin("progress")&&i.getPlugin("progress").resetSeekState()}),10),o.isStart){o.scope>-1&&e.cancelable&&e.preventDefault();var s=n.config,a=s.disableGesture,l=s.gestureX;!a&&l?n.endLastMove(o.scope):o.time=0,o.scope=-1,n.resetPos(),W.checkIsFunction(r.enableSwipeHandler)&&r.enableSwipeHandler(),n.changeAction(Yi)}})),w(E(n),"onRootTouchMove",(function(e){!n.config.disableGesture&&n.config.gestureX&&n.checkIsRootTarget(e)&&(e.stopPropagation(),n.pos.isStart?n.onTouchMove(e):n.onTouchStart(e))})),w(E(n),"onRootTouchEnd",(function(e){n.pos.scope>-1&&n.onTouchEnd(e)})),n.pos={isStart:!1,x:0,y:0,time:0,volume:0,rate:1,light:0,width:0,height:0,scopeL:0,scopeR:0,scopeM1:0,scopeM2:0,scope:-1},n.timer=null,n}return _(t,[{key:"duration",get:function(){return this.playerConfig.customDuration||this.player.duration}},{key:"timeOffset",get:function(){return this.playerConfig.timeOffset||0}},{key:"registerIcons",value:function(){return{seekTipIcon:{icon:Ki,class:"xg-seek-pre"}}}},{key:"afterCreate",value:function(){var e=this;Zi.map((function(t){e.__hooks[t]=null}));var t=this.playerConfig,i=this.config,n=this.player;!0===t.closeVideoDblclick&&(i.closedbClick=!0),this.resetPos(),W.isUndefined(t.disableGesture)||(i.disableGesture=!!t.disableGesture),this.appendChild(".xg-seek-icon",this.icons.seekTipIcon),this.xgMask=W.createDom("xg-mask","",{},"xgmask"),n.root.appendChild(this.xgMask),this.initCustomStyle(),this.registerThumbnail();var o="mouse"===this.domEventType?"mouse":"touch";this.touch=new zi(this.root,{eventType:o,needPreventDefault:!this.config.disableGesture}),this.root.addEventListener("contextmenu",(function(e){e.preventDefault()})),n.root.addEventListener("touchmove",this.onRootTouchMove,!0),n.root.addEventListener("touchend",this.onRootTouchEnd,!0),n.root.addEventListener("touchcancel",this.onRootTouchEnd,!0);var r=this.player.controls;r&&r.center&&(r.center.addEventListener("touchmove",this.onRootTouchMove,!0),r.center.addEventListener("touchend",this.onRootTouchEnd,!0),r.center.addEventListener("touchcancel",this.onRootTouchEnd,!0)),this.on(ue,(function(){var t=e.player,i=e.config;t.duration>0&&1e3*t.duration<i.moveDuration&&(i.moveDuration=1e3*t.duration)})),this.on([ce,ie],(function(){var t=e.pos,i=t.time;!t.isStart&&i>0&&(e.pos.time=0)}));var s={touchstart:"onTouchStart",touchmove:"onTouchMove",touchend:"onTouchEnd",press:"onPress",pressend:"onPressEnd",click:"onClick",doubleclick:"onDbClick"};if(Object.keys(s).map((function(t){e.touch.on(t,(function(i){e[s[t]](i)}))})),!i.disableActive){var a=n.plugins.progress;a&&(a.addCallBack("dragmove",(function(t){e.activeSeekNote(t.currentTime,t.forward)})),["dragend","click"].forEach((function(t){a.addCallBack(t,(function(){e.changeAction(Yi)}))})))}}},{key:"registerThumbnail",value:function(){var e=this.player.plugins.thumbnail;if(e&&e.usable){this.thumbnail=e.createThumbnail(null,"mobile-thumbnail");var t=this.find(".time-preview");t.insertBefore(this.thumbnail,t.children[0])}}},{key:"initCustomStyle",value:function(){var e=(this.playerConfig||{}).commonStyle,t=e.playedColor,i=e.progressColor,n=e.timePreviewStyle,o=e.curTimeColor,r=e.durationColor;if(t&&(this.find(".xg-curbar").style.backgroundColor=t),i&&(this.find(".xg-bar").style.backgroundColor=i),n){var s=this.find(".time-preview");Object.keys(n).forEach((function(e){s.style[e]=n[e]}))}var a=o||t,l=r;a&&(this.find(".xg-cur").style.color=a),l&&(this.find(".xg-dur").style.color=l),this.config.disableTimeProgress&&W.addClass(this.find(".xg-timebar"),"hide")}},{key:"resetPos",value:function(){var e=this;this.pos?(this.pos.isStart=!1,this.pos.scope=-1,["x","y","width","height","scopeL","scopeR","scopeM1","scopeM2"].map((function(t){e.pos[t]=0}))):this.pos={isStart:!1,x:0,y:0,volume:0,rate:1,light:0,width:0,height:0,scopeL:0,scopeR:0,scopeM1:0,scopeM2:0,scope:-1,time:0}}},{key:"changeAction",value:function(e){var t=this.player;this.root.setAttribute("data-xg-action",e);var i=t.plugins.start;i&&i.recover()}},{key:"getTouche",value:function(e){this.player.rotateDeg;var t=e.touches&&e.touches.length>0?e.touches[e.touches.length-1]:e;return{pageX:t.pageX,pageY:t.pageY}}},{key:"checkScope",value:function(e,t,i,n,o){var r=o.width,s=-1;if(e<0||e>r)return s;var a=0===n?Math.abs(i):Math.abs(i/n);return Math.abs(i)>0&&a>=1.73&&e>o.scopeM1&&e<o.scopeM2?s=0:(0===Math.abs(i)||a<=.57)&&(s=e<o.scopeL?1:e>o.scopeR?2:3),s}},{key:"executeMove",value:function(e,t,i,n,o){switch(i){case 0:this.updateTime(e/n*this.config.scopeM);break;case 1:this.updateBrightness(t/o);break;case 2:q.os.isIos||this.updateVolume(t/o)}}},{key:"endLastMove",value:function(e){var t=this,i=this.pos,n=this.player,o=this.config,r=(i.time-this.timeOffset)/1e3;if(0===e)n.seek(Number(r).toFixed(1)),o.hideControlsEnd?n.blur():n.focus(),this.timer=setTimeout((function(){t.pos.time=0}),500);this.changeAction(Yi)}},{key:"checkIsRootTarget",value:function(e){var t=this.player.plugins||{};return(!t.progress||!t.progress.root.contains(e.target))&&(t.start&&t.start.root.contains(e.target)||t.controls&&t.controls.root.contains(e.target))}},{key:"sendUseAction",value:function(e){var t=this.player.paused;this.emitUserAction(e,"switch_play_pause",{prop:"paused",from:t,to:!t})}},{key:"clickHandler",value:function(e){var t=this.player,i=this.config,n=this.playerConfig;t.state<Gt?n.closeVideoClick||(this.sendUseAction(W.createEvent("click")),t.play()):!i.closedbClick||n.closeVideoClick?t.isActive?t.blur():t.focus():n.closeVideoClick||((t.isActive||i.focusVideoClick)&&(this.sendUseAction(W.createEvent("click")),this.switchPlayPause()),t.focus())}},{key:"dbClickHandler",value:function(e){var t=this.config,i=this.player;!t.closedbClick&&i.state>=Gt&&(this.sendUseAction(W.createEvent("dblclick")),this.switchPlayPause())}},{key:"onClick",value:function(e){var t=this,i=this.player;ft(this,Zi[0],(function(e,i){t.clickHandler(i.e)}),{e:e,paused:i.paused})}},{key:"onDbClick",value:function(e){var t=this,i=this.player;ft(this,Zi[1],(function(e,i){t.dbClickHandler(i.e)}),{e:e,paused:i.paused})}},{key:"onPress",value:function(e){var t=this.pos,i=this.config,n=this.player;i.disablePress||(t.rate=this.player.playbackRate,this.emitUserAction("press","change_rate",{prop:"playbackRate",from:n.playbackRate,to:i.pressRate}),n.playbackRate=i.pressRate,this.changeAction(qi))}},{key:"onPressEnd",value:function(e){var t=this.pos,i=this.config,n=this.player;i.disablePress||(this.emitUserAction("pressend","change_rate",{prop:"playbackRate",from:n.playbackRate,to:t.rate}),n.playbackRate=t.rate,t.rate=1,this.changeAction(Yi))}},{key:"updateTime",value:function(e){var t=this.player,i=this.config,n=this.player.duration;e=Number(e.toFixed(4));var o=parseInt(e*i.moveDuration,10)+this.timeOffset;o=(o+=this.pos.time)<0?0:o>1e3*n?1e3*n-200:o,t.getPlugin("time")&&t.getPlugin("time").updateTime(o/1e3),t.getPlugin("progress")&&t.getPlugin("progress").updatePercent(o/1e3/this.duration,!0),this.activeSeekNote(o/1e3,e>0),i.isTouchingSeek&&t.seek(Number((o-this.timeOffset)/1e3).toFixed(1)),this.pos.time=o}},{key:"updateVolume",value:function(e){this.player.rotateDeg&&(e=-e);var t=this.player,i=this.pos;if(e=parseInt(100*e,10),i.volume+=e,!(Math.abs(i.volume)<10)){var n=parseInt(10*t.volume,10)-parseInt(i.volume/10,10);n=n>10?10:n<1?0:n,t.volume=n/10,i.volume=0}}},{key:"updateBrightness",value:function(e){var t=this.pos,i=this.config,n=this.xgMask;if(i.darkness){this.player.rotateDeg&&(e=-e);var o=t.light+.8*e;o=o>i.maxDarkness?i.maxDarkness:o<0?0:o,n&&(n.style.backgroundColor="rgba(0,0,0,".concat(o,")")),t.light=o}}},{key:"activeSeekNote",value:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],i=this.player,n=this.config,o=!(this.duration!==1/0&&this.duration>0);if(e&&"number"==typeof e&&!o&&!n.disableActive){e<0?e=0:e>i.duration&&(e=i.duration-.2),this.changeAction(Xi);var r=i.plugins.start;r&&r.focusHide(),this.find(".xg-dur").innerHTML=W.format(this.duration),this.find(".xg-cur").innerHTML=W.format(e),this.find(".xg-curbar").style.width="".concat(e/this.duration*100,"%"),t?W.removeClass(this.find(".xg-seek-show"),"xg-back"):W.addClass(this.find(".xg-seek-show"),"xg-back"),this.updateThumbnails(e)}}},{key:"updateThumbnails",value:function(e){var t=this.player.plugins.thumbnail;t&&t.usable&&this.thumbnail&&t.update(this.thumbnail,e,160,90)}},{key:"switchPlayPause",value:function(){var e=this.player;if(e.state<Vt)return!1;e.ended||(e.paused?e.play():e.pause())}},{key:"disableGesture",value:function(){this.config.disableGesture=!0}},{key:"enableGesture",value:function(){this.config.disableGesture=!1}},{key:"destroy",value:function(){var e=this.player;this.timer&&clearTimeout(this.timer),this.thumbnail=null,e.root.removeChild(this.xgMask),this.xgMask=null,this.touch&&this.touch.destroy(),this.touch=null,e.root.removeEventListener("touchmove",this.onRootTouchMove,!0),e.root.removeEventListener("touchend",this.onRootTouchEnd,!0),e.root.removeEventListener("touchcancel",this.onRootTouchEnd,!0);var t=this.player.controls;t&&t.center&&(t.center.removeEventListener("touchmove",this.onRootTouchMove,!0),t.center.removeEventListener("touchend",this.onRootTouchEnd,!0),t.center.removeEventListener("touchcancel",this.onRootTouchEnd,!0))}},{key:"render",value:function(){var e="normal"!==this.config.gradient?"gradient ".concat(this.config.gradient):"gradient";return'\n     <xg-trigger class="trigger">\n     <div class="'.concat(e,'"></div>\n        <div class="time-preview">\n            <div class="xg-seek-show ').concat(this.config.disableSeekIcon?" hide-seek-icon":"",'">\n              <i class="xg-seek-icon"></i>\n              <span class="xg-cur">00:00</span>\n              <span class="xg-separator">/</span>\n              <span class="xg-dur">00:00</span>\n            </div>\n              <div class="xg-bar xg-timebar">\n                <div class="xg-curbar"></div>\n              </div>\n        </div>\n        <div class="xg-playbackrate xg-top-note">\n            <span><i>').concat(this.config.pressRate,"X</i>").concat(this.i18n.FORWARD,"</span>\n        </div>\n     </xg-trigger>\n    ")}}],[{key:"pluginName",get:function(){return"mobile"}},{key:"defaultConfig",get:function(){return{index:0,disableGesture:!1,gestureX:!0,gestureY:!0,gradient:"normal",isTouchingSeek:!1,miniMoveStep:5,miniYPer:5,scopeL:.25,scopeR:.25,scopeM:.9,pressRate:2,darkness:!0,maxDarkness:.8,disableActive:!1,disableTimeProgress:!1,hideControlsActive:!1,hideControlsEnd:!1,moveDuration:36e4,closedbClick:!1,disablePress:!0,disableSeekIcon:!1,focusVideoClick:!1}}}]),t}();function $i(e){var t=e.tagName;return!("INPUT"!==t&&"TEXTAREA"!==t&&!e.isContentEditable)}var Qi=function(){T(t,kt);var e=I(t);function t(){var i;C(this,t);for(var n=arguments.length,o=new Array(n),r=0;r<n;r++)o[r]=arguments[r];return w(E(i=e.call.apply(e,[this].concat(o))),"onBodyKeyDown",(function(e){if(i.player){var t=e||window.event,n=t.keyCode,o=E(i),r=o._keyState,s=o.player,a=i.config,l=a.disable,c=a.disableBodyTrigger,u=a.isIgnoreUserActive;l||c||!s.isUserActive&&!u||$i(t.target)||!i.checkIsVisible()||t.metaKey||t.altKey||t.ctrlKey?r.isBodyKeyDown=!1:(e.repeat||r.isKeyDown||((t.target===document.body||i.config.isGlobalTrigger&&!$i(t.target))&&i.checkCode(n,!0)&&(r.isBodyKeyDown=!0),document.addEventListener("keyup",i.onBodyKeyUp)),r.isBodyKeyDown&&i.handleKeyDown(t))}})),w(E(i),"onBodyKeyUp",(function(e){i.player&&(document.removeEventListener("keyup",i.onBodyKeyUp),i.handleKeyUp(e))})),w(E(i),"onKeydown",(function(e){if(i.player){var t=e||window.event,n=E(i)._keyState;if(!t.repeat){if(i.config.disable||i.config.disableRootTrigger||t.metaKey||t.altKey||t.ctrlKey)return;!t||37!==t.keyCode&&!i.checkCode(t.keyCode)||t.target!==i.player.root&&t.target!==i.player.video&&t.target!==i.player.controls.el||(n.isKeyDown=!0),i.player.root.addEventListener("keyup",i.onKeyup)}n.isKeyDown&&i.handleKeyDown(t)}})),w(E(i),"onKeyup",(function(e){i.player&&(i.player.root.removeEventListener("keyup",i.onKeyup),i.handleKeyUp(e))})),i}return _(t,[{key:"mergekeyCodeMap",value:function(){var e=this,t=this.config.keyCodeMap;t&&Object.keys(t).map((function(i){e.keyCodeMap[i]?["keyCode","action","disable","pressAction","disablePress","isBodyTarget"].map((function(n){t[i][n]&&(e.keyCodeMap[i][n]=t[i][n])})):e.keyCodeMap[i]=t[i]}))}},{key:"afterCreate",value:function(){this.config.disable=!this.playerConfig.keyShortcut;var e="function"==typeof this.config.seekStep?this.config.seekStep(this.player):this.config.seekStep;e&&"number"==typeof e&&(this.seekStep=e),this.keyCodeMap={space:{keyCode:32,action:"playPause",disable:!1,disablePress:!1,noBodyTarget:!1},up:{keyCode:38,action:"upVolume",disable:!1,disablePress:!1,noBodyTarget:!0},down:{keyCode:40,action:"downVolume",disable:!1,disablePress:!1,noBodyTarget:!0},left:{keyCode:37,action:"seekBack",disablePress:!1,disable:!1},right:{keyCode:39,action:"seek",pressAction:"changePlaybackRate",disablePress:!1,disable:!1},esc:{keyCode:27,action:"exitFullscreen",disablePress:!0,disable:!1}},this.mergekeyCodeMap(),this._keyState={isKeyDown:!1,isBodyKeyDown:!1,isPress:!1,tt:0,playbackRate:0},this.player.root.addEventListener("keydown",this.onKeydown),document.addEventListener("keydown",this.onBodyKeyDown)}},{key:"setConfig",value:function(e){var t=this;Object.keys(e).forEach((function(i){t.config[i]=e[i]}))}},{key:"checkIsVisible",value:function(){if(!this.config.checkVisible)return!0;var e=this.player.root.getBoundingClientRect(),t=e.height,i=e.top,n=e.bottom,o=window.innerHeight;return!(i<0&&i<0-.9*t||n>0&&n-o>.9*t)}},{key:"checkCode",value:function(e,t){var i=this,n=!1;return Object.keys(this.keyCodeMap).map((function(o){i.keyCodeMap[o]&&e===i.keyCodeMap[o].keyCode&&!i.keyCodeMap[o].disable&&(n=!t||t&&!i.keyCodeMap[o].noBodyTarget)})),n}},{key:"downVolume",value:function(e){var t=this.player;if(!(t.volume<=0)){var i=parseFloat((t.volume-.1).toFixed(1)),n={volume:{from:t.volume,to:i}};this.emitUserAction(e,"change_volume",{props:n}),t.volume=i>=0?i:0}}},{key:"upVolume",value:function(e){var t=this.player;if(!(t.volume>=1)){var i=parseFloat((t.volume+.1).toFixed(1)),n={volume:{from:t.volume,to:i}};this.emitUserAction(e,"change_volume",{props:n}),t.volume=i<=1?i:1}}},{key:"seek",value:function(e){var t=this.player,i=t.currentTime,n=t.offsetCurrentTime,o=t.duration,r=t.offsetDuration,s=t.timeSegments,a=n>-1?n:i,l=r||o,c=e.repeat&&this.seekStep>=4?parseInt(this.seekStep/2,10):this.seekStep;a+c<=l?a+=c:a=l;var u=W.getCurrentTimeByOffset(a,s),h={currentTime:{from:i,to:u}};this.emitUserAction(e,"seek",{props:h}),this.player.currentTime=u}},{key:"seekBack",value:function(e){var t=this.player,i=t.currentTime,n=t.offsetCurrentTime,o=t.timeSegments,r=(n>-1?n:i)-(e.repeat?parseInt(this.seekStep/2,10):this.seekStep);r<0&&(r=0);var s={currentTime:{from:i,to:r=W.getCurrentTimeByOffset(r,o)}};this.emitUserAction(e,"seek",{props:s}),this.player.currentTime=r}},{key:"changePlaybackRate",value:function(e){var t=this._keyState,i=this.config,n=this.player;0===t.playbackRate&&(t.playbackRate=n.playbackRate,n.playbackRate=i.playbackRate)}},{key:"playPause",value:function(e){var t=this.player;t&&(this.emitUserAction(e,"switch_play_pause"),t.paused?t.play():t.pause())}},{key:"exitFullscreen",value:function(e){var t=this.player,i=t.fullscreen,n=t.cssfullscreen;i&&(this.emitUserAction("keyup","switch_fullscreen",{prop:"fullscreen",from:i,to:!i}),t.exitFullscreen()),n&&(this.emitUserAction("keyup","switch_css_fullscreen",{prop:"cssfullscreen",from:n,to:!n}),t.exitCssFullscreen())}},{key:"handleKeyDown",value:function(e){var t=this._keyState;if(e.repeat){t.isPress=!0;var i=Date.now();if(i-t.tt<200)return;t.tt=i}this.handleKeyCode(e.keyCode,e,t.isPress)}},{key:"handleKeyUp",value:function(e){var t=this._keyState;t.playbackRate>0&&(this.player.playbackRate=t.playbackRate,t.playbackRate=0),t.isKeyDown=!1,t.isPress=!1,t.tt=0}},{key:"handleKeyCode",value:function(e,t,i){for(var n,o=Object.keys(this.keyCodeMap),r=0;r<o.length;r++){var s=this.keyCodeMap[o[r]],a=s.action,l=s.keyCode,c=s.disable,u=s.pressAction,h=s.disablePress;if(l===e){if(!(c||i&&h)){var d=i&&u||a;"function"==typeof d?a(t,this.player,i):"string"==typeof d&&"function"==typeof this[d]&&this[d](t,this.player,i),this.emit(je,m({key:o[r],target:t.target,isPress:i},this.keyCodeMap[o[r]]))}(n=t).preventDefault(),n.returnValue=!1,t.stopPropagation();break}}}},{key:"destroy",value:function(){this.player.root.removeEventListener("keydown",this.onKeydown),document.removeEventListener("keydown",this.onBodyKeyDown),this.player.root.removeEventListener("keyup",this.onKeyup),document.removeEventListener("keyup",this.onBodyKeyUp)}},{key:"disable",value:function(){this.config.disable=!0}},{key:"enable",value:function(){this.config.disable=!1}}],[{key:"pluginName",get:function(){return"keyboard"}},{key:"defaultConfig",get:function(){return{seekStep:10,checkVisible:!1,disableBodyTrigger:!1,disableRootTrigger:!1,isGlobalTrigger:!0,keyCodeMap:{},disable:!1,playbackRate:2,isIgnoreUserActive:!0}}}]),t}();function en(){return(new DOMParser).parseFromString('<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="-5 -5 110 110">\n  <path d="M100,50A50,50,0,1,1,50,0" stroke-width="5" stroke="#ddd" stroke-dasharray="236" fill="none"></path>\n</svg>\n',"image/svg+xml").firstChild}var tn=function(){T(t,Et);var e=I(t);function t(){return C(this,t),e.apply(this,arguments)}return _(t,[{key:"registerIcons",value:function(){return{loadingIcon:en}}},{key:"afterCreate",value:function(){this.appendChild("xg-loading-inner",this.icons.loadingIcon)}},{key:"render",value:function(){return'\n    <xg-loading class="xgplayer-loading">\n      <xg-loading-inner></xg-loading-inner>\n    </xg-loading>'}}],[{key:"pluginName",get:function(){return"loading"}},{key:"defaultConfig",get:function(){return{position:_t.ROOT}}}]),t}(),nn=[{tag:"xg-cache",className:"xgplayer-progress-cache",styleKey:"cachedColor"},{tag:"xg-played",className:"xgplayer-progress-played",styleKey:"playedColor"}],on=function(){function e(t){C(this,e),this.fragments=t.fragments||[],0===this.fragments.length&&this.fragments.push({percent:1}),this._callBack=t.actionCallback,this.fragConfig={fragFocusClass:t.fragFocusClass||"inner-focus-point",fragAutoFocus:!!t.fragAutoFocus,fragClass:t.fragClass||""},this.style=t.style||{playedColor:"",cachedColor:"",progressColor:""},this.duration=0,this.cachedIndex=0,this.playedIndex=0,this.focusIndex=-1}return _(e,[{key:"updateDuration",value:function(e){var t=this;this.duration=e;var i=0,n=this.fragments;this.fragments=n.map((function(e){return e.start=parseInt(i,10),e.end=parseInt(i+e.percent*t.duration,10),e.duration=parseInt(e.percent*t.duration,10),i+=e.percent*t.duration,e}))}},{key:"updateProgress",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"played",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{newIndex:0,curIndex:0,millisecond:0},i=this.progressList,n=this.fragments;if(!(i.length<1)){var o=t.newIndex,r=t.curIndex,s=t.millisecond;o!==r&&i.map((function(t,i){i<o?t[e].style.width="100%":i>o&&(t[e].style.width=0)}));var a=n[o],l=0===s?0:(s-a.start)/a.duration;i[o][e].style.width=l<0?0:"".concat(100*l,"%")}}},{key:"updateFocus",value:function(e){if(this.fragConfig.fragAutoFocus&&!(this.fragments.length<2))if(e){var t=this.findIndex(1e3*e.currentTime,this.focusIndex);if(t>=0&&t!==this.focusIndex){this.focusIndex>-1&&this.unHightLight(this.focusIndex),this.setHightLight(t);var i={index:t,preIndex:this.focusIndex,fragment:this.fragments[this.focusIndex]};this.focusIndex=t,this._callBack&&this._callBack(i)}}else if(this.focusIndex>-1){this.unHightLight(this.focusIndex);var n={index:-1,preIndex:this.focusIndex,fragment:null};this._callBack&&this._callBack(n),this.focusIndex=-1}}},{key:"update",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{cached:0,played:0},t=arguments.length>1?arguments[1]:void 0;if(!this.duration||parseInt(1e3*t,10)!==this.duration){if(!t&&0!==t)return;this.updateDuration(parseInt(1e3*t,10))}var i=this.playedIndex,n=this.cachedIndex;if("Undefined"!==W.typeOf(e.played)){var o=this.findIndex(1e3*e.played,i);if(o<0)return;this.updateProgress("played",{newIndex:o,curIndex:i,millisecond:parseInt(1e3*e.played,10)}),this.playedIndex=o}if("Undefined"!==W.typeOf(e.cached)){var r=this.findIndex(1e3*e.cached,n);if(r<0)return;this.updateProgress("cached",{newIndex:r,curIndex:n,millisecond:parseInt(1e3*e.cached,10)}),this.cachedIndex=r}}},{key:"findIndex",value:function(e,t){var i=this.fragments;if(!i||0===i.length)return-1;if(1===i.length)return 0;if(t>-1&&t<i.length&&e>i[t].start&&e<i[t].end)return t;if(e>i[i.length-1].start)return i.length-1;for(var n=0;n<i.length;n++)if(e>i[n].start&&e<=i[n].end){t=n;break}return t}},{key:"findHightLight",value:function(){for(var e=this.root.children,t=0;t<e.length;t++)if(W.hasClass(e[t],this.fragConfig.fragFocusClass))return{dom:e[t],pos:e[t].getBoundingClientRect()}}},{key:"findFragment",value:function(e){var t=this.root.children;return e<0||e>=t.length?null:{dom:t[e],pos:t[e].getBoundingClientRect()}}},{key:"unHightLight",value:function(){for(var e=this.root.children,t=0;t<e.length;t++)W.removeClass(e[t],this.fragConfig.fragFocusClass)}},{key:"setHightLight",value:function(e){var t=this.root.children;if(e<t.length)return W.addClass(t[e],this.fragConfig.fragFocusClass),{dom:t[e],pos:t[e].getBoundingClientRect()}}},{key:"destroy",value:function(){this.progressList=null,this.fragments=null,this.root.innerHTML=""}},{key:"reset",value:function(e){var t=this;if(Object.keys(this.fragConfig).forEach((function(i){void 0!==e[i]&&(t.fragConfig[i]=e[i])})),e.fragments){if(this.fragments=0===e.fragments.length?[{percent:1}]:e.fragments,this.updateDuration(this.duration),this.playedIndex=0,this.cachedIndex=0,this.root)for(var i=this.root.children;i.length>0;)this.root.removeChild(i[0]);this.render()}}},{key:"render",value:function(){var e=this,t=this.style.progressColor;if(this.root||(this.root=W.createDom("xg-inners","",{},"progress-list")),this.fragments){var i=this.fragConfig,n=i.fragClass,o=i.fragFocusClass;this.progressList=this.fragments.map((function(i){var r=W.createDom("xg-inner","",{style:t?"background:".concat(t,"; flex: ").concat(i.percent):"flex: ".concat(i.percent)},"".concat(i.isFocus?o:""," xgplayer-progress-inner ").concat(n));return e.root.appendChild(r),nn.forEach((function(t){r.appendChild(W.createDom(t.tag,"",{style:t.styleKey?"background: ".concat(e.style[t.styleKey],"; width:0;"):"width:0;"},t.className))})),{cached:r.children[0],played:r.children[1]}}))}return this.root}}]),e}(),rn={POINT:"inner-focus-point",HIGHLIGHT:"inner-focus-highlight"},sn=function(){T(t,Et);var e=I(t);function t(i){var n;return C(this,t),w(E(n=e.call(this,i)),"onMoveOnly",(function(e,t){var i=E(n),o=i.pos,r=i.config,s=i.player,a=t;if(e){W.event(e);var l=W.getEventPos(e,s.zoom),c=90===s.rotateDeg?l.clientY:l.clientX;if(o.moving&&Math.abs(o.x-c)<r.miniMoveStep)return;o.moving=!0,o.x=c,a=n.computeTime(e,c)}n.triggerCallbacks("dragmove",a,e),n._updateInnerFocus(a)})),w(E(n),"onBodyClick",(function(e){n.pos.isLocked&&(n.pos.isLocked=!1,e.preventDefault(),e.stopPropagation())})),w(E(n),"_mouseDownHandler",(function(e,t){n._state.time=t.currentTime,n.updateWidth(t.currentTime,t.seekTime,t.percent,0),n._updateInnerFocus(t)})),w(E(n),"_mouseUpHandler",(function(e,t){E(n).pos.moving&&n.updateWidth(t.currentTime,t.seekTime,t.percent,2)})),w(E(n),"_mouseMoveHandler",(function(e,t){var i=E(n),o=i._state,r=i.pos,s=i.config,a=i.player;o.time<t.currentTime?t.forward=!0:t.forward=!1,o.time=t.currentTime,r.isDown&&!r.moving&&(r.moving=!0,s.isPauseMoving&&a.pause(),n.triggerCallbacks("dragstart",t,e),n.emitUserAction("drag","dragstart",t)),n.updateWidth(t.currentTime,t.seekTime,t.percent,1),n.triggerCallbacks("dragmove",t,e),n._updateInnerFocus(t)})),w(E(n),"onMouseDown",(function(e){var t=E(n),i=t._state,o=t.player,r=t.pos,s=t.config,a=t.playerConfig,l=W.getEventPos(e,o.zoom),c=90===o.rotateDeg?l.clientY:l.clientX;if(!(o.isMini||s.closeMoveSeek||!a.allowSeekAfterEnded&&o.ended)){if(o.duration||o.isPlaying){e.stopPropagation(),n.focus(),W.checkIsFunction(a.disableSwipeHandler)&&a.disableSwipeHandler(),W.checkIsFunction(s.onMoveStart)&&s.onMoveStart(),W.event(e),r.x=c,r.isDown=!0,r.moving=!1,i.prePlayTime=o.currentTime,o.focus({autoHide:!1}),n.isProgressMoving=!0,W.addClass(n.progressBtn,"active");var u=n.computeTime(e,c);return u.prePlayTime=i.prePlayTime,n._mouseDownHandlerHook(e,u),"touchstart"===e.type?(n.root.addEventListener("touchmove",n.onMouseMove),n.root.addEventListener("touchend",n.onMouseUp),n.root.addEventListener("touchcancel",n.onMouseUp)):(n.unbind("mousemove",n.onMoveOnly),document.addEventListener("mousemove",n.onMouseMove,!1),document.addEventListener("mouseup",n.onMouseUp,!1)),!0}o.play()}})),w(E(n),"onMouseUp",(function(e){var t=E(n),i=t.player,o=t.config,r=t.pos,s=t.playerConfig,a=t._state;e.stopPropagation(),e.preventDefault(),W.checkIsFunction(s.enableSwipeHandler)&&s.enableSwipeHandler(),W.checkIsFunction(o.onMoveEnd)&&o.onMoveEnd(),W.event(e),W.removeClass(n.progressBtn,"active");var l=n.computeTime(e,r.x);l.prePlayTime=a.prePlayTime,r.moving?(n.triggerCallbacks("dragend",l,e),n.emitUserAction("drag","dragend",l)):(n.triggerCallbacks("click",l,e),n.emitUserAction("click","click",l)),n._mouseUpHandlerHook(e,l),r.moving=!1,r.isDown=!1,r.x=0,r.y=0,r.isLocked=!0,a.prePlayTime=0,a.time=0;var c=e.type;"touchend"===c||"touchcancel"===c?(n.root.removeEventListener("touchmove",n.onMouseMove),n.root.removeEventListener("touchend",n.onMouseUp),n.root.removeEventListener("touchcancel",n.onMouseUp),n.blur()):(document.removeEventListener("mousemove",n.onMouseMove,!1),document.removeEventListener("mouseup",n.onMouseUp,!1),r.isEnter?"mobile"!==s.isMobileSimulateMode&&n.bind("mousemove",n.onMoveOnly):n.onMouseLeave(e)),W.setTimeout(E(n),(function(){n.resetSeekState()}),1),i.focus()})),w(E(n),"onMouseMove",(function(e){var t=E(n),i=t._state,o=t.pos,r=t.player,s=t.config;W.checkTouchSupport()&&e.preventDefault(),W.event(e);var a=W.getEventPos(e,r.zoom),l=90===r.rotateDeg?a.clientY:a.clientX,c=Math.abs(o.x-l);if(!(o.moving&&c<s.miniMoveStep||!o.moving&&c<s.miniStartStep)){o.x=l;var u=n.computeTime(e,l);u.prePlayTime=i.prePlayTime,n._mouseMoveHandlerHook(e,u)}})),w(E(n),"onMouseOut",(function(e){n.triggerCallbacks("mouseout",null,e)})),w(E(n),"onMouseOver",(function(e){n.triggerCallbacks("mouseover",null,e)})),w(E(n),"onMouseEnter",(function(e){var t=E(n),i=t.player,o=t.pos;if(!(o.isDown||o.isEnter||i.isMini||!i.config.allowSeekAfterEnded&&i.ended)){o.isEnter=!0,n.bind("mousemove",n.onMoveOnly),n.bind("mouseleave",n.onMouseLeave),W.event(e);var r=W.getEventPos(e,i.zoom),s=90===i.rotateDeg?r.clientY:r.clientX,a=n.computeTime(e,s);n.triggerCallbacks("mouseenter",a,e),n.focus()}})),w(E(n),"onMouseLeave",(function(e){n.triggerCallbacks("mouseleave",null,e),n.unlock(),n._updateInnerFocus(null)})),w(E(n),"onVideoResize",(function(){var e=n.pos,t=e.x,i=e.isDown;if(e.isEnter&&!i){var o=n.computeTime(null,t);n.onMoveOnly(null,o)}})),n.useable=!1,n.isProgressMoving=!1,n.__dragCallBacks=[],n._state={now:-1,direc:0,time:0,prePlayTime:-1},n._disableBlur=!1,"boolean"==typeof n.config.isDragingSeek&&(n.config.isDraggingSeek=n.config.isDragingSeek),n}return _(t,[{key:"offsetDuration",get:function(){return this.playerConfig.customDuration||this.player.offsetDuration||this.player.duration}},{key:"duration",get:function(){return this.playerConfig.customDuration||this.player.duration}},{key:"timeOffset",get:function(){return this.playerConfig.timeOffset||0}},{key:"currentTime",get:function(){var e=this.player,t=e.offsetCurrentTime,i=e.currentTime;return t>=0?t:i+this.timeOffset}},{key:"changeState",value:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];this.useable=e}},{key:"show",value:function(e){this.root&&(this.root.style.display="flex")}},{key:"_initInner",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};t&&0!==t.length||(t=[{percent:1}]);var n=m(m({fragments:t},i),{},{actionCallback:function(t){e.emitUserAction("fragment_focus","fragment_focus",t)}});this.innerList?this.innerList.reset(n):(this.innerList=new on(n),this.outer.insertBefore(this.innerList.render(),this.outer.children[0]),["findHightLight","unHightLight","setHightLight","findFragment"].map((function(t){e[t]=e.innerList[t].bind(e.innerList)})))}},{key:"_updateInnerFocus",value:function(e){this.innerList&&this.innerList.updateFocus(e)}},{key:"afterCreate",value:function(){if(!this.config.disable&&!this.playerConfig.isLive){this.pos={x:0,y:0,moving:!1,isDown:!1,isEnter:!1,isLocked:!1},this.outer=this.find("xg-outer");var e=this.config,t=e.fragFocusClass,i=e.fragAutoFocus,n=e.fragClass;this._initInner(this.config.fragments,{fragFocusClass:t,fragAutoFocus:i,fragClass:n,style:this.playerConfig.commonStyle||{}}),"mobile"===q.device&&(this.config.isDraggingSeek=!1,this.isMobile=!0),this.progressBtn=this.find(".xgplayer-progress-btn"),this.listenEvents(),this.bindDomEvents(),this.initCustomStyle()}}},{key:"listenEvents",value:function(){var e=this;this.on(ue,(function(){e.onMouseLeave()})),this.on(ae,(function(){e.onTimeupdate()})),this.on(se,(function(){e.onTimeupdate(),e.onCacheUpdate()})),this.on(pe,(function(){e.onCacheUpdate()})),this.on(ie,(function(){e.onCacheUpdate(!0),e.onTimeupdate(!0),e._state.now=0})),this.on(ve,(function(){e.onReset()})),this.on(Ne,(function(){e.onVideoResize()}))}},{key:"setConfig",value:function(e){var t=this,i=null;Object.keys(e).forEach((function(n){t.config[n]=e[n],"fragments"===n&&(i=e[n])})),i&&this._initInner(i,e)}},{key:"initCustomStyle",value:function(){var e=(this.playerConfig||{}).commonStyle.sliderBtnStyle,t=this.progressBtn;e&&("string"==typeof e?t.style.boxShadow=e:"object"===k(e)&&Object.keys(e).map((function(i){t.style[i]=e[i]})))}},{key:"triggerCallbacks",value:function(e,t,i){this.__dragCallBacks.length>0&&this.__dragCallBacks.map((function(n){if(n&&n.handler&&n.type===e)try{n.handler(t,i)}catch(o){}}))}},{key:"addCallBack",value:function(e,t){t&&"function"==typeof t&&this.__dragCallBacks.push({type:e,handler:t})}},{key:"removeCallBack",value:function(e,t){var i=this.__dragCallBacks,n=-1;i.map((function(i,o){i&&i.type===e&&i.handler===t&&(n=o)})),n>-1&&i.splice(n,1)}},{key:"unlock",value:function(){var e=this.player,t=this.pos;t.isEnter=!1,t.isLocked=!1,e.isMini||(this.unbind("mousemove",this.onMoveOnly),t.isDown?this.unbind("mouseleave",this.onMouseLeave):this.blur())}},{key:"bindDomEvents",value:function(){var e=this.player.config;this._mouseDownHandlerHook=this.hook("dragstart",this._mouseDownHandler),this._mouseUpHandlerHook=this.hook("dragend",this._mouseUpHandler),this._mouseMoveHandlerHook=this.hook("drag",this._mouseMoveHandler),"touch"!==this.domEventType&&"compatible"!==this.domEventType||this.root.addEventListener("touchstart",this.onMouseDown),"mouse"!==this.domEventType&&"compatible"!==this.domEventType||(this.bind("mousedown",this.onMouseDown),"mobile"!==e.isMobileSimulateMode&&this.bind("mouseenter",this.onMouseEnter),this.bind("mouseover",this.onMouseOver),this.bind("mouseout",this.onMouseOut),this.player.root.addEventListener("click",this.onBodyClick,!0))}},{key:"focus",value:function(){this.player.controls.pauseAutoHide(),W.addClass(this.root,"active")}},{key:"blur",value:function(){this._disableBlur||(this.player.controls.recoverAutoHide(),W.removeClass(this.root,"active"))}},{key:"disableBlur",value:function(){this._disableBlur=!0}},{key:"enableBlur",value:function(){this._disableBlur=!1}},{key:"updateWidth",value:function(e,t,i,n){var o=this.config,r=this.player;if(!o.isCloseClickSeek||0!==n){var s=t=t>=r.duration?r.duration-o.endedDiff:Number(t).toFixed(1);this.updatePercent(i),this.updateTime(e),(1!==n||o.isDraggingSeek&&"audio"!==r.config.mediaType)&&(this._state.now=s,this._state.direc=s>r.currentTime?0:1,r.seek(s))}}},{key:"computeTime",value:function(e,t){var i,n,o=this.player,r=this.root.getBoundingClientRect(),s=r.width,a=r.height,l=r.top,c=r.left,u=t;90===o.rotateDeg?(i=a,n=l):(i=s,n=c);var h=u-n,d=(h=h>i?i:h<0?0:h)/i;d=d<0?0:d>1?1:d;var f=parseInt(d*this.offsetDuration*1e3,10)/1e3;return{percent:d,currentTime:f,seekTime:W.getCurrentTimeByOffset(f,o.timeSegments),offset:h,width:i,left:n,e:e}}},{key:"updateTime",value:function(e){var t=this.player,i=this.duration;e>i?e=i:e<0&&(e=0);var n=t.plugins.time;n&&n.updateTime(e)}},{key:"resetSeekState",value:function(){this.isProgressMoving=!1;var e=this.player.plugins.time;e&&e.resetActive()}},{key:"updatePercent",value:function(e,t){if(this.isProgressMoving=!0,!this.config.disable){e=e>1?1:e<0?0:e,this.progressBtn.style.left="".concat(100*e,"%"),this.innerList.update({played:e*this.offsetDuration},this.offsetDuration);var i=this.player.plugins.miniprogress;i&&i.update({played:e*this.offsetDuration},this.offsetDuration)}}},{key:"onTimeupdate",value:function(e){var t=this.player,i=this._state,n=this.offsetDuration;if(!(t.isSeeking&&t.media.seeking||this.isProgressMoving)&&t.hasStart){if(i.now>-1){var o=parseInt(1e3*i.now,10)-parseInt(1e3*t.currentTime,10);if(0===i.direc&&o>300||1===i.direc&&o>-300)return void(i.now=-1);i.now=-1}var r=this.currentTime;r=W.adjustTimeByDuration(r,n,e),this.innerList.update({played:r},n),this.progressBtn.style.left="".concat(r/n*100,"%")}}},{key:"onCacheUpdate",value:function(e){var t=this.player,i=this.duration;if(t){var n=t.bufferedPoint.end;n=W.adjustTimeByDuration(n,i,e),this.innerList.update({cached:n},i)}}},{key:"onReset",value:function(){this.innerList.update({played:0,cached:0},0),this.progressBtn.style.left="0%"}},{key:"destroy",value:function(){var e=this.player;this.thumbnailPlugin=null,this.innerList.destroy(),this.innerList=null;var t=this.domEventType;"touch"!==t&&"compatible"!==t||(this.root.removeEventListener("touchstart",this.onMouseDown),this.root.removeEventListener("touchmove",this.onMouseMove),this.root.removeEventListener("touchend",this.onMouseUp),this.root.removeEventListener("touchcancel",this.onMouseUp)),"mouse"!==t&&"compatible"!==t||(this.unbind("mousedown",this.onMouseDown),this.unbind("mouseenter",this.onMouseEnter),this.unbind("mousemove",this.onMoveOnly),this.unbind("mouseleave",this.onMouseLeave),document.removeEventListener("mousemove",this.onMouseMove,!1),document.removeEventListener("mouseup",this.onMouseUp,!1),e.root.removeEventListener("click",this.onBodyClick,!0))}},{key:"render",value:function(){if(!this.config.disable&&!this.playerConfig.isLive){var e=this.player.controls?this.player.controls.config.mode:"";return'\n    <xg-progress class="xgplayer-progress '.concat("bottom"===e?"xgplayer-progress-bottom":"",'">\n      <xg-outer class="xgplayer-progress-outer">\n        <xg-progress-btn class="xgplayer-progress-btn"></xg-progress-btn>\n      </xg-outer>\n    </xg-progress>\n    ')}}}],[{key:"pluginName",get:function(){return"progress"}},{key:"defaultConfig",get:function(){return{position:_t.CONTROLS_CENTER,index:0,disable:!1,isDraggingSeek:!0,closeMoveSeek:!1,isPauseMoving:!1,isCloseClickSeek:!1,fragments:[{percent:1}],fragFocusClass:rn.POINT,fragClass:"",fragAutoFocus:!1,miniMoveStep:5,miniStartStep:2,onMoveStart:function(){},onMoveEnd:function(){},endedDiff:.2}}},{key:"FRAGMENT_FOCUS_CLASS",get:function(){return rn}}]),t}(),an=function(){T(t,Et);var e=I(t);function t(){var i;C(this,t);for(var n=arguments.length,o=new Array(n),r=0;r<n;r++)o[r]=arguments[r];return w(E(i=e.call.apply(e,[this].concat(o))),"_onMouseenter",(function(e){i.emit("icon_mouseenter",{pluginName:i.pluginName})})),w(E(i),"_onMouseLeave",(function(e){i.emit("icon_mouseleave",{pluginName:i.pluginName})})),i}return _(t,[{key:"afterCreate",value:function(){this.bind("mouseenter",this._onMouseenter),this.bind("mouseleave",this._onMouseLeave),this.config.disable&&this.disable()}},{key:"destroy",value:function(){this.unbind("mouseenter",this._onMouseenter),this.unbind("mouseleave",this._onMouseLeave)}}]),t}(),ln=function(){T(t,an);var e=I(t);function t(){var i;C(this,t);for(var n=arguments.length,o=new Array(n),r=0;r<n;r++)o[r]=arguments[r];return w(E(i=e.call.apply(e,[this].concat(o))),"btnClick",(function(e){e.preventDefault(),e.stopPropagation();var t=E(i).player;return i.emitUserAction(e,"switch_play_pause",{prop:"paused",from:t.paused,to:!t.paused}),t.ended?t.replay():t.paused?(t.play(),i.animate(!1)):(t.pause(),i.animate(!0)),!1})),i}return _(t,[{key:"afterCreate",value:function(){L(x(t.prototype),"afterCreate",this).call(this),this.config.disable||(this.initIcons(),this.bind(["touchend","click"],this.btnClick),this.listenEvents(),this.animate(!0))}},{key:"listenEvents",value:function(){var e=this,t=this.player;this.on([ee,ne,oe,ve],(function(){e.animate(t.paused)}))}},{key:"registerIcons",value:function(){return{play:{icon:fi,class:"xg-icon-play"},pause:{icon:pi,class:"xg-icon-pause"}}}},{key:"initIcons",value:function(){var e=this.icons;this.appendChild(".xgplayer-icon",e.play),this.appendChild(".xgplayer-icon",e.pause)}},{key:"animate",value:function(e){if(this.player){var t=this.i18nKeys,i=this.find(".xg-tips");e?(this.setAttr("data-state","pause"),i&&this.changeLangTextKey(i,t.PLAY_TIPS)):(this.setAttr("data-state","play"),i&&this.changeLangTextKey(i,t.PAUSE_TIPS))}}},{key:"destroy",value:function(){L(x(t.prototype),"destroy",this).call(this),this.unbind(["touchend","click"],this.btnClick)}},{key:"render",value:function(){if(!this.config.disable)return'<xg-icon class="xgplayer-play">\n    <div class="xgplayer-icon">\n    </div>\n    '.concat(ki(this,"PLAY_TIPS",this.playerConfig.isHideTips),"\n    </xg-icon>")}}],[{key:"pluginName",get:function(){return"play"}},{key:"defaultConfig",get:function(){return{position:_t.CONTROLS_LEFT,index:0,disable:!1}}}]),t}();function cn(){return(new DOMParser).parseFromString('<svg width="32px" height="40px" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">\n    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <path d="M11.2374369,14 L17.6187184,7.61871843 C17.9604272,7.27700968 17.9604272,6.72299032 17.6187184,6.38128157 C17.2770097,6.03957281 16.7229903,6.03957281 16.3812816,6.38128157 L9.38128157,13.3812816 C9.03957281,13.7229903 9.03957281,14.2770097 9.38128157,14.6187184 L16.3812816,21.6187184 C16.7229903,21.9604272 17.2770097,21.9604272 17.6187184,21.6187184 C17.9604272,21.2770097 17.9604272,20.7229903 17.6187184,20.3812816 L11.2374369,14 L11.2374369,14 Z" fill="#FFFFFF"></path>\n    </g>\n</svg>',"image/svg+xml").firstChild}var un=function(){T(t,Et);var e=I(t);function t(){return C(this,t),e.apply(this,arguments)}return _(t,[{key:"afterCreate",value:function(){var e=this;this.initIcons(),this.onClick=function(t){t.preventDefault(),t.stopPropagation(),e.config.onClick(t)},this.bind(["click","touchend"],this.onClick)}},{key:"registerIcons",value:function(){return{screenBack:{icon:cn,class:"xg-fullscreen-back"}}}},{key:"initIcons",value:function(){var e=this.icons;this.appendChild(this.root,e.screenBack)}},{key:"show",value:function(){W.addClass(this.root,"show")}},{key:"hide",value:function(){W.removeClass(this.root,"show")}},{key:"render",value:function(){return'<xg-icon class="xgplayer-back">\n    </xg-icon>'}}],[{key:"pluginName",get:function(){return"topbackicon"}},{key:"defaultConfig",get:function(){return{position:_t.ROOT_TOP,index:0}}}]),t}();function hn(){return(new DOMParser).parseFromString('<svg xmlns="http://www.w3.org/2000/svg" width="28" height="40" viewBox="2 -4 28 40">\n  <path fill="#fff" transform="scale(0.0320625 0.0320625)" d="M598 214h212v212h-84v-128h-128v-84zM726 726v-128h84v212h-212v-84h128zM214 426v-212h212v84h-128v128h-84zM298 598v128h128v84h-212v-212h84z"></path>\n</svg>\n',"image/svg+xml").firstChild}function dn(){return(new DOMParser).parseFromString('<svg xmlns="http://www.w3.org/2000/svg" width="28" height="40" viewBox="2 -4 28 40">\n  <path fill="#fff" transform="scale(0.0320625 0.0320625)" d="M682 342h128v84h-212v-212h84v128zM598 810v-212h212v84h-128v128h-84zM342 342v-128h84v212h-212v-84h128zM214 682v-84h212v212h-84v-128h-128z"></path>\n</svg>\n',"image/svg+xml").firstChild}var fn=function(){T(t,an);var e=I(t);function t(){var i;C(this,t);for(var n=arguments.length,o=new Array(n),r=0;r<n;r++)o[r]=arguments[r];return w(E(i=e.call.apply(e,[this].concat(o))),"_onOrientationChange",(function(e){i.player.fullscreen&&i.config.rotateFullscreen&&(90===window.orientation||-90===window.orientation?i.player.setRotateDeg(0):i.player.setRotateDeg(90))})),i}return _(t,[{key:"afterCreate",value:function(){var e=this;L(x(t.prototype),"afterCreate",this).call(this);var i=this.config,n=this.playerConfig;if(!i.disable){i.target&&(this.playerConfig.fullscreenTarget=this.config.target);var o=W.getFullScreenEl();n.fullscreenTarget===o&&this.player.getFullscreen().catch((function(e){})),this.initIcons(),this.handleFullscreen=this.hook("fullscreenChange",this.toggleFullScreen,{pre:function(t){var i=e.player.fullscreen;e.emitUserAction(t,"switch_fullscreen",{prop:"fullscreen",from:i,to:!i})}}),this.bind(".xgplayer-fullscreen",["touchend","click"],this.handleFullscreen),this.on(Ae,(function(t){var i=e.find(".xg-tips");i&&e.changeLangTextKey(i,t?e.i18nKeys.EXITFULLSCREEN_TIPS:e.i18nKeys.FULLSCREEN_TIPS),e.animate(t)})),this.config.needBackIcon&&(this.topBackIcon=this.player.registerPlugin({plugin:un,options:{config:{onClick:function(t){e.handleFullscreen(t)}}}})),"mobile"===q.device&&window.addEventListener("orientationchange",this._onOrientationChange)}}},{key:"registerIcons",value:function(){return{fullscreen:{icon:hn,class:"xg-get-fullscreen"},exitFullscreen:{icon:dn,class:"xg-exit-fullscreen"}}}},{key:"destroy",value:function(){L(x(t.prototype),"destroy",this).call(this),this.unbind(".xgplayer-icon","mobile"===q.device?"touchend":"click",this.handleFullscreen),"mobile"===q.device&&window.removeEventListener("orientationchange",this._onOrientationChange)}},{key:"initIcons",value:function(){var e=this.icons;this.appendChild(".xgplayer-icon",e.fullscreen),this.appendChild(".xgplayer-icon",e.exitFullscreen)}},{key:"toggleFullScreen",value:function(e){e&&(e.preventDefault(),e.stopPropagation());var t=this.player,i=this.config;!0===i.useCssFullscreen||"function"==typeof i.useCssFullscreen&&i.useCssFullscreen()?(t.fullscreen?t.exitCssFullscreen():t.getCssFullscreen(),this.animate(t.fullscreen)):i.rotateFullscreen?(t.fullscreen?t.exitRotateFullscreen():t.getRotateFullscreen(),this.animate(t.fullscreen)):i.switchCallback&&"function"==typeof i.switchCallback?i.switchCallback(t.fullscreen):t.fullscreen?(t.exitFullscreen(),i.useScreenOrientation&&this.unlockScreen()):(t.getFullscreen().catch((function(e){})),i.useScreenOrientation&&t.aspectRatio>1&&this.lockScreen(i.lockOrientationType))}},{key:"animate",value:function(e){e?this.setAttr("data-state","full"):this.setAttr("data-state","normal"),this.topBackIcon&&(e?(this.topBackIcon.show(),this.hide()):(this.topBackIcon.hide(),this.show()))}},{key:"render",value:function(){if(!this.config.disable){return'<xg-icon class="xgplayer-fullscreen">\n    <div class="xgplayer-icon">\n    </div>\n    '.concat(ki(this,"FULLSCREEN_TIPS",this.playerConfig.isHideTips),"\n    </xg-icon>")}}},{key:"lockScreen",value:function(e){try{screen.orientation.lock(e).catch((function(e){}))}catch(t){}}},{key:"unlockScreen",value:function(){try{screen.orientation.unlock().catch((function(e){}))}catch(e){}}}],[{key:"pluginName",get:function(){return"fullscreen"}},{key:"defaultConfig",get:function(){return{position:_t.CONTROLS_RIGHT,index:0,useCssFullscreen:!1,rotateFullscreen:!1,useScreenOrientation:!1,lockOrientationType:"landscape",switchCallback:null,target:null,disable:!1,needBackIcon:!1}}}]),t}(),pn=function(){T(t,Et);var e=I(t);function t(i){var n;return C(this,t),(n=e.call(this,i)).isActiving=!1,n}return _(t,[{key:"duration",get:function(){var e=this.player,t=e.offsetDuration,i=e.duration;return this.playerConfig.customDuration||t||i}},{key:"currentTime",get:function(){var e=this.player,t=e.offsetCurrentTime,i=e.currentTime;return t>=0?t:i}},{key:"timeOffset",get:function(){return this.playerConfig.timeOffset||0}},{key:"afterCreate",value:function(){var e=this.player.controls.config.mode;this.mode="flex"===e?"flex":"normal",this.config.disable||("flex"===this.mode&&(this.createCenterTime(),this.root.style.display="none"),this.durationDom=this.find(".time-duration"),this.timeDom=this.find(".time-current"),this.listenEvents())}},{key:"listenEvents",value:function(){var e=this;this.on([ue,se,ae],(function(t){"durationchange"===t.eventName&&(e.isActiving=!1),e.onTimeUpdate()})),this.on(ie,(function(){e.onTimeUpdate(!0)})),this.on(ve,(function(){e.onReset()}))}},{key:"show",value:function(e){if("flex"===this.mode)return this.centerCurDom&&(this.centerCurDom.style.display="block"),void(this.centerDurDom&&(this.centerDurDom.style.display="block"));this.root.style.display="block"}},{key:"hide",value:function(){if("flex"===this.mode)return this.centerCurDom&&(this.centerCurDom.style.display="none"),void(this.centerDurDom&&(this.centerDurDom.style.display="none"));this.root.style.display="none"}},{key:"onTimeUpdate",value:function(e){var t=this.player,i=this.config,n=this.duration;if(!i.disable&&!this.isActiving&&t.hasStart){var o=this.currentTime+this.timeOffset;o=W.adjustTimeByDuration(o,n,e),"flex"===this.mode?(this.centerCurDom.innerHTML=this.minWidthTime(W.format(o)),n!==1/0&&n>0&&(this.centerDurDom.innerHTML=W.format(n))):(this.timeDom.innerHTML=this.minWidthTime(W.format(o)),n!==1/0&&n>0&&(this.durationDom.innerHTML=W.format(n)))}}},{key:"onReset",value:function(){"flex"===this.mode?(this.centerCurDom.innerHTML=this.minWidthTime(W.format(0)),this.centerDurDom.innerHTML=W.format(0)):(this.timeDom.innerHTML=this.minWidthTime(W.format(0)),this.durationDom.innerHTML=W.format(0))}},{key:"createCenterTime",value:function(){var e=this.player;if(e.controls&&e.controls.center){var t=e.controls.center;this.centerCurDom=W.createDom("xg-icon","00:00",{},"xgplayer-time xg-time-left"),this.centerDurDom=W.createDom("xg-icon","00:00",{},"xgplayer-time xg-time-right"),t.children.length>0?t.insertBefore(this.centerCurDom,t.children[0]):t.appendChild(this.centerCurDom),t.appendChild(this.centerDurDom)}}},{key:"afterPlayerInit",value:function(){var e=this.config;this.duration===1/0||this.playerConfig.isLive?(W.hide(this.durationDom),W.hide(this.timeDom),W.hide(this.find(".time-separator")),W.show(this.find(".time-live-tag"))):W.hide(this.find(".time-live-tag")),e.hide?this.hide():this.show()}},{key:"changeLiveState",value:function(e){e?(W.hide(this.durationDom),W.hide(this.timeDom),W.hide(this.find(".time-separator")),W.show(this.find(".time-live-tag"))):(W.hide(this.find(".time-live-tag")),W.show(this.find(".time-separator")),W.show(this.durationDom),W.show(this.timeDom))}},{key:"updateTime",value:function(e){this.isActiving=!0,!e&&0!==e||e>this.duration||("flex"!==this.mode?this.timeDom.innerHTML=this.minWidthTime(W.format(e)):this.centerCurDom.innerHTML=this.minWidthTime(W.format(e)))}},{key:"minWidthTime",value:function(e){return e.split(":").map((function(e){return'<span class="time-min-width">'.concat(e,"</span>")})).join(":")}},{key:"resetActive",value:function(){var e=this,t=this.player,i=function(){e.isActiving=!1};this.off(se,i),t.isSeeking&&t.media.seeking?this.once(se,i):this.isActiving=!1}},{key:"destroy",value:function(){var e=this.player.controls.center;this.centerCurDom&&e.removeChild(this.centerCurDom),this.centerCurDom=null,this.centerDurDom&&e.removeChild(this.centerDurDom),this.centerDurDom=null}},{key:"render",value:function(){if(!this.config.disable)return'<xg-icon class="xgplayer-time">\n    <span class="time-current">00:00</span>\n    <span class="time-separator">/</span>\n    <span class="time-duration">00:00</span>\n    <span class="time-live-tag">'.concat(this.i18n.LIVE_TIP,"</span>\n    </xg-icon>")}}],[{key:"pluginName",get:function(){return"time"}},{key:"defaultConfig",get:function(){return{position:_t.CONTROLS_LEFT,index:2,disable:!1}}}]),t}(),gn=function(){T(t,kt);var e=I(t);function t(){var i;C(this,t);for(var n=arguments.length,o=new Array(n),r=0;r<n;r++)o[r]=arguments[r];return w(E(i=e.call.apply(e,[this].concat(o))),"_onDurationChange",(function(){i.updateSegments();var e=i.player,t=e.currentTime,n=e.timeSegments;if(i._checkIfEnabled(n)){var o=W.getIndexByTime(t,n),r=W.getOffsetCurrentTime(t,n,o);i.player.offsetCurrentTime=r,i.changeIndex(o,n)}})),w(E(i),"_onLoadedData",(function(){var e=i.player.timeSegments;if(i._checkIfEnabled(e)){var t=W.getOffsetCurrentTime(0,e);i.player.offsetCurrentTime=t,i.changeIndex(0,e),i.curPos.start>0&&(i.player.currentTime=i.curPos.start)}})),w(E(i),"_onTimeupdate",(function(){var e=i.player,t=e.currentTime,n=e.timeSegments;if(i._checkIfEnabled(n)){var o=n.length;i.lastCurrentTime=t;var r=W.getIndexByTime(t,n);r!==i.curIndex&&i.changeIndex(r,n);var s=W.getOffsetCurrentTime(t,n,r);if(i.player.offsetCurrentTime=s,i.curPos){var a=i.curPos,l=a.start,c=a.end;t<l?i.player.currentTime=l:t>c&&r>=o-1&&i.player.pause()}}})),w(E(i),"_onSeeking",(function(){var e=i.player,t=e.currentTime,n=e.timeSegments;if(i._checkIfEnabled(n))if(t<n[0].start)i.player.currentTime=n[0].start;else if(t>n[n.length-1].end)i.player.currentTime=n[n.length-1].end;else{var o=W.getIndexByTime(t,n);if(o>=0){var r=i.getSeekTime(t,i.lastCurrentTime,o,n);r>=0&&(i.player.currentTime=r)}}})),w(E(i),"_onPlay",(function(){var e=i.player,t=e.currentTime,n=e.timeSegments;i._checkIfEnabled(n)&&t>=n[n.length-1].end&&(i.player.currentTime=n[0].start)})),i}return _(t,[{key:"afterCreate",value:function(){this.curIndex=-1,this.curPos=null,this.lastCurrentTime=0,this.updateSegments(),this.on(ue,this._onDurationChange),this.on(de,this._onLoadedData),this.on(ae,this._onTimeupdate),this.on(re,this._onSeeking),this.on(ee,this._onPlay)}},{key:"setConfig",value:function(e){var t=this;if(e){var i=Object.keys(e);i.length<1||(i.forEach((function(i){t.config[i]=e[i]})),this.updateSegments())}}},{key:"updateSegments",value:function(){var e=this.config,t=e.disable,i=e.segments,n=this.player;if(t||!i||0===i.length)n.timeSegments=[],n.offsetDuration=0,n.offsetCurrentTime=-1;else{var o=this.formatTimeSegments(i,n.duration);n.timeSegments=o,n.offsetDuration=o.length>0?o[o.length-1].duration:0}}},{key:"formatTimeSegments",value:function(e,t){var i=[];return e?(e.sort((function(e,t){return e.start-t.start})),e.forEach((function(e,n){var o={};if(o.start=e.start<0?0:e.start,o.end=t>0&&e.end>t?t:e.end,!(t>0&&o.start>t)){i.push(o);var r=o.end-o.start;if(0===n)o.offset=e.start,o.cTime=0,o.segDuration=r,o.duration=r;else{var s=i[n-1];o.offset=s.offset+(o.start-s.end),o.cTime=s.duration+s.cTime,o.segDuration=r,o.duration=s.duration+r}}})),i):[]}},{key:"getSeekTime",value:function(e,t,i,n){var o=-1,r=n[i],s=r.start,a=r.end;if(e>=s&&e<=a)return o;var l=e-t;if(l<0&&e<s){var c=t>s?t-s:0;return o=i-1>=0?n[i-1].end+l+c:0}return-1}},{key:"_checkIfEnabled",value:function(e){return!(!e||e.length<1)}},{key:"changeIndex",value:function(e,t){this.curIndex=e,e>=0&&t.length>0?this.curPos=t[e]:this.curPos=null}}],[{key:"pluginName",get:function(){return"TimeSegmentsControls"}},{key:"defaultConfig",get:function(){return{disable:!0,segments:[]}}}]),t}();function vn(){return(new DOMParser).parseFromString('<svg xmlns="http://www.w3.org/2000/svg" width="28" height="40" viewBox="0 -10 28 40">\n  <path fill="#fff" transform="scale(0.0220625 0.0220625)" d="M358.4 358.4h-204.8v307.2h204.8l256 256v-819.2l-256 256z"></path>\n  <path fill="#fff" transform="scale(0.0220625 0.0220625)" d="M940.632 837.632l-72.192-72.192c65.114-64.745 105.412-154.386 105.412-253.44s-40.299-188.695-105.396-253.424l-0.016-0.016 72.192-72.192c83.639 83.197 135.401 198.37 135.401 325.632s-51.762 242.434-135.381 325.612l-0.020 0.020zM795.648 693.248l-72.704-72.704c27.756-27.789 44.921-66.162 44.921-108.544s-17.165-80.755-44.922-108.546l0.002 0.002 72.704-72.704c46.713 46.235 75.639 110.363 75.639 181.248s-28.926 135.013-75.617 181.227l-0.021 0.021z"></path>\n</svg>\n',"image/svg+xml").firstChild}function yn(){return(new DOMParser).parseFromString('<svg xmlns="http://www.w3.org/2000/svg" width="28" height="40" viewBox="0 -10 28 40">\n  <path fill="#fff" transform="scale(0.0220625 0.0220625)" d="M358.4 358.4h-204.8v307.2h204.8l256 256v-819.2l-256 256z"></path>\n  <path fill="#fff" transform="scale(0.0220625 0.0220625)" d="M795.648 693.248l-72.704-72.704c27.756-27.789 44.921-66.162 44.921-108.544s-17.165-80.755-44.922-108.546l0.002 0.002 72.704-72.704c46.713 46.235 75.639 110.363 75.639 181.248s-28.926 135.013-75.617 181.227l-0.021 0.021zM795.648 693.248l-72.704-72.704c27.756-27.789 44.921-66.162 44.921-108.544s-17.165-80.755-44.922-108.546l0.002 0.002 72.704-72.704c46.713 46.235 75.639 110.363 75.639 181.248s-28.926 135.013-75.617 181.227l-0.021 0.021z"></path>\n</svg>\n',"image/svg+xml").firstChild}function mn(){return(new DOMParser).parseFromString('<svg xmlns="http://www.w3.org/2000/svg" width="28" height="40" viewBox="0 -10 28 40">\n  <path fill="#fff" transform="scale(0.0220625 0.0220625)" d="M358.4 358.4h-204.8v307.2h204.8l256 256v-819.2l-256 256z"></path>\n  <path fill="#fff" transform="scale(0.0220625 0.0220625)" d="M920.4 439.808l-108.544-109.056-72.704 72.704 109.568 108.544-109.056 108.544 72.704 72.704 108.032-109.568 108.544 109.056 72.704-72.704-109.568-108.032 109.056-108.544-72.704-72.704-108.032 109.568z"></path>\n</svg>\n',"image/svg+xml").firstChild}var kn=function(){T(t,Et);var e=I(t);function t(){var i;C(this,t);for(var n=arguments.length,o=new Array(n),r=0;r<n;r++)o[r]=arguments[r];return w(E(i=e.call.apply(e,[this].concat(o))),"onBarMousedown",(function(e){var t=E(i).player,n=i.find(".xgplayer-bar");W.event(e);var o=n.getBoundingClientRect(),r=W.getEventPos(e,t.zoom),s=o.height-(r.clientY-o.top);if(r.h=s,r.barH=o.height,i.pos=r,!(s<-2))return i.updateVolumePos(s,e),document.addEventListener("mouseup",i.onBarMouseUp),i._d.isStart=!0,!1})),w(E(i),"onBarMouseMove",(function(e){var t=E(i)._d;if(t.isStart){var n=E(i),o=n.pos,r=n.player;e.preventDefault(),e.stopPropagation(),W.event(e);var s=W.getEventPos(e,r.zoom);t.isMoving=!0;var a=o.h-s.clientY+o.clientY;a>o.barH||i.updateVolumePos(a,e)}})),w(E(i),"onBarMouseUp",(function(e){W.event(e),document.removeEventListener("mouseup",i.onBarMouseUp);var t=E(i)._d;t.isStart=!1,t.isMoving=!1})),w(E(i),"onMouseenter",(function(e){i._d.isActive=!0,i.focus(),i.emit("icon_mouseenter",{pluginName:i.pluginName})})),w(E(i),"onMouseleave",(function(e){i._d.isActive=!1,i.unFocus(100,!1,e),i.emit("icon_mouseleave",{pluginName:i.pluginName})})),w(E(i),"onVolumeChange",(function(e){if(i.player){var t=i.player,n=t.muted,o=t.volume;i._d.isMoving||(i.find(".xgplayer-drag").style.height=n||0===o?"4px":"".concat(100*o,"%"),i.config.showValueLabel&&i.updateVolumeValue()),i.animate(n,o)}})),i}return _(t,[{key:"registerIcons",value:function(){return{volumeSmall:{icon:yn,class:"xg-volume-small"},volumeLarge:{icon:vn,class:"xg-volume"},volumeMuted:{icon:mn,class:"xg-volume-mute"}}}},{key:"afterCreate",value:function(){var e=this;if(this._timerId=null,this._d={isStart:!1,isMoving:!1,isActive:!1},!this.config.disable){this.initIcons();var t=this.playerConfig,i=t.commonStyle,n=t.volume;i.volumeColor&&(this.find(".xgplayer-drag").style.backgroundColor=i.volumeColor),this.changeMutedHandler=this.hook("mutedChange",(function(t){e.changeMuted(t)}),{pre:function(e){e.preventDefault(),e.stopPropagation()}}),this._onMouseenterHandler=this.hook("mouseenter",this.onMouseenter),this._onMouseleaveHandler=this.hook("mouseleave",this.onMouseleave),"mobile"!==q.device&&"mobile"!==this.playerConfig.isMobileSimulateMode&&(this.bind("mouseenter",this._onMouseenterHandler),this.bind(["blur","mouseleave"],this._onMouseleaveHandler),this.bind(".xgplayer-slider","mousedown",this.onBarMousedown),this.bind(".xgplayer-slider","mousemove",this.onBarMouseMove),this.bind(".xgplayer-slider","mouseup",this.onBarMouseUp)),this.bind(".xgplayer-icon",["touchend","click"],this.changeMutedHandler),this.on(he,this.onVolumeChange),this.once(de,this.onVolumeChange),"Number"!==W.typeOf(n)&&(this.player.volume=this.config.default),this.onVolumeChange()}}},{key:"updateVolumePos",value:function(e,t){var i=this.player,n=this.find(".xgplayer-drag"),o=this.find(".xgplayer-bar");if(o&&n){var r=parseInt(e/o.getBoundingClientRect().height*1e3,10);n.style.height="".concat(e,"px");var s=Math.max(Math.min(r/1e3,1),0),a={volume:{from:i.volume,to:s}};i.muted&&(a.muted={from:!0,to:!1}),this.emitUserAction(t,"change_volume",{muted:i.muted,volume:i.volume,props:a}),i.volume=Math.max(Math.min(r/1e3,1),0),i.muted&&(i.muted=!1),this.config.showValueLabel&&this.updateVolumeValue()}}},{key:"updateVolumeValue",value:function(){var e=this.player,t=e.volume,i=e.muted,n=this.find(".xgplayer-value-label"),o=Math.max(Math.min(t,1),0);n.innerText=i?0:Math.round(100*o)}},{key:"focus",value:function(){this.player.focus({autoHide:!1}),this._timerId&&(W.clearTimeout(this,this._timerId),this._timerId=null),W.addClass(this.root,"slide-show")}},{key:"unFocus",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:100,i=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n=arguments.length>2?arguments[2]:void 0,o=this._d,r=this.player;o.isActive||(this._timerId&&(W.clearTimeout(this,this._timerId),this._timerId=null),this._timerId=W.setTimeout(this,(function(){o.isActive||(i?r.blur():r.focus(),W.removeClass(e.root,"slide-show"),o.isStart&&e.onBarMouseUp(n)),e._timerId=null}),t))}},{key:"changeMuted",value:function(e){e&&e.stopPropagation();var t=this.player;this._d.isStart&&this.onBarMouseUp(e),this.emitUserAction(e,"change_muted",{muted:t.muted,volume:t.volume,props:{muted:{from:t.muted,to:!t.muted}}}),t.volume>0&&(t.muted=!t.muted),t.volume<.01&&(t.volume=this.config.miniVolume)}},{key:"animate",value:function(e,t){e||0===t?this.setAttr("data-state","mute"):t<.5&&this.icons.volumeSmall?this.setAttr("data-state","small"):this.setAttr("data-state","normal")}},{key:"initIcons",value:function(){var e=this.icons;this.appendChild(".xgplayer-icon",e.volumeSmall),this.appendChild(".xgplayer-icon",e.volumeLarge),this.appendChild(".xgplayer-icon",e.volumeMuted)}},{key:"destroy",value:function(){this._timerId&&(W.clearTimeout(this,this._timerId),this._timerId=null),this.unbind("mouseenter",this.onMouseenter),this.unbind(["blur","mouseleave"],this.onMouseleave),this.unbind(".xgplayer-slider","mousedown",this.onBarMousedown),this.unbind(".xgplayer-slider","mousemove",this.onBarMouseMove),this.unbind(".xgplayer-slider","mouseup",this.onBarMouseUp),document.removeEventListener("mouseup",this.onBarMouseUp),this.unbind(".xgplayer-icon","mobile"===q.device?"touchend":"click",this.changeMutedHandler)}},{key:"render",value:function(){if(!this.config.disable){var e=this.config.default||this.player.volume,t=this.config.showValueLabel;return'\n    <xg-icon class="xgplayer-volume" data-state="normal">\n      <div class="xgplayer-icon">\n      </div>\n      <xg-slider class="xgplayer-slider">\n        '.concat(t?'<div class="xgplayer-value-label">'.concat(100*e,"</div>"):"",'\n        <div class="xgplayer-bar">\n          <xg-drag class="xgplayer-drag" style="height: ').concat(100*e,'%"></xg-drag>\n        </div>\n      </xg-slider>\n    </xg-icon>')}}}],[{key:"pluginName",get:function(){return"volume"}},{key:"defaultConfig",get:function(){return{position:_t.CONTROLS_RIGHT,index:1,disable:!1,showValueLabel:!1,default:.6,miniVolume:.2}}}]),t}();function Cn(){return(new DOMParser).parseFromString('<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="-4 -6 40 40" fill="none">\n  <g>\n    <path transform="scale(1.5 1.5)" d="M11.6665 9.16663H4.1665C2.78579 9.16663 1.6665 10.2859 1.6665 11.6666V15.8333C1.6665 17.214 2.78579 18.3333 4.1665 18.3333H11.6665C13.0472 18.3333 14.1665 17.214 14.1665 15.8333V11.6666C14.1665 10.2859 13.0472 9.16663 11.6665 9.16663Z" fill="white"/>\n    <path transform="scale(1.5 1.5)" fill-rule="evenodd" clip-rule="evenodd" d="M3.88148 4.06298C3.75371 4.21005 3.67667 4.40231 3.67749 4.61242C3.67847 4.87253 3.79852 5.10435 3.98581 5.25646L6.99111 8.05895C7.32771 8.37283 7.85502 8.35443 8.16891 8.01782C8.48279 7.68122 8.46437 7.15391 8.12778 6.84003L6.62061 5.43457L9.8198 5.4224C9.82848 5.42239 9.8372 5.42221 9.84591 5.4219C10.9714 5.38233 12.0885 5.6285 13.0931 6.13744C14.0976 6.64635 14.957 7.40148 15.5908 8.33234C16.2246 9.2632 16.6122 10.3394 16.7177 11.4606C16.823 12.5819 16.6427 13.7115 16.1934 14.7442C16.0098 15.1661 16.203 15.6571 16.6251 15.8408C17.0471 16.0243 17.5381 15.8311 17.7216 15.4091C18.2833 14.1183 18.5087 12.7063 18.3771 11.3047C18.2453 9.90318 17.7607 8.55792 16.9684 7.39433C16.1761 6.23073 15.1021 5.28683 13.8463 4.65065C12.5946 4.01651 11.203 3.70872 9.80072 3.75583L6.43415 3.76862L7.96326 2.12885C8.27715 1.79225 8.25872 1.26494 7.92213 0.951061C7.58553 0.63718 7.05822 0.655585 6.74433 0.99219L3.90268 4.0395C3.89545 4.04724 3.88841 4.05509 3.88154 4.06303L3.88148 4.06298Z" fill="white"/>\n  </g>\n  <defs>\n    <clipPath>\n      <rect width="40" height="40" fill="white"/>\n    </clipPath>\n  </defs>\n</svg>\n',"image/svg+xml").firstChild}var bn=function(){T(t,an);var e=I(t);function t(i){var n;return C(this,t),(n=e.call(this,i)).rotateDeg=n.config.rotateDeg||0,n}return _(t,[{key:"afterCreate",value:function(){var e=this;if(!this.config.disable){L(x(t.prototype),"afterCreate",this).call(this),this.appendChild(".xgplayer-icon",this.icons.rotate),this.onBtnClick=this.onBtnClick.bind(this),this.bind(".xgplayer-icon",["click","touchend"],this.onBtnClick),this.on(Ne,(function(){e.rotateDeg&&e.config.innerRotate&&W.setTimeout(e,(function(){e.updateRotateDeg(e.rotateDeg,e.config.innerRotate)}),100)}));var i=this.player.root;this.rootWidth=i.style.width||i.offsetWidth||i.clientWidth,this.rootHeight=i.style.height||i.offsetHeight||i.clientHeight,this.rotateDeg&&this.updateRotateDeg(this.rotateDeg,this.config.innerRotate)}}},{key:"destroy",value:function(){L(x(t.prototype),"destroy",this).call(this),this.unbind(".xgplayer-icon",["click","touchend"],this.onBtnClick)}},{key:"onBtnClick",value:function(e){e.preventDefault(),e.stopPropagation(),this.emitUserAction(e,"rotate"),this.rotate(this.config.clockwise,this.config.innerRotate,1)}},{key:"updateRotateDeg",value:function(e,t){if(e||(e=0),t)this.player.videoRotateDeg=e;else{var i=this.player,n=this.rootWidth,o=this.rootHeight,r=i.root,s=i.innerContainer,a=i.media,l=r.offsetWidth,c=s&&t?s.offsetHeight:r.offsetHeight,u=n,h=o,d=0,f=0;.75!==e&&.25!==e||(u="".concat(c,"px"),h="".concat(l,"px"),d=-(c-l)/2,f=-(l-c)/2);var p="translate(".concat(d,"px,").concat(f,"px) rotate(").concat(e,"turn)"),g={transformOrigin:"center center",transform:p,webKitTransform:p,height:h,width:u},v=t?a:r,y=t?i.getPlugin("poster"):null;Object.keys(g).map((function(e){v.style[e]=g[e],y&&y.root&&(y.root.style[e]=g[e])}))}}},{key:"rotate",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=this.player;this.rotateDeg||(this.rotateDeg=0);var o=e?1:-1;this.rotateDeg=(this.rotateDeg+1+.25*o*i)%1,this.updateRotateDeg(this.rotateDeg,t),n.emit(He,360*this.rotateDeg)}},{key:"registerIcons",value:function(){return{rotate:Cn}}},{key:"render",value:function(){if(!this.config.disable)return'\n    <xg-icon class="xgplayer-rotate">\n      <div class="xgplayer-icon">\n      </div>\n      '.concat(ki(this,"ROTATE_TIPS",this.playerConfig.isHideTips),"\n    </xg-icon>")}}],[{key:"pluginName",get:function(){return"rotate"}},{key:"defaultConfig",get:function(){return{position:_t.CONTROLS_RIGHT,index:6,innerRotate:!0,clockwise:!1,rotateDeg:0,disable:!1}}}]),t}();function _n(){return(new DOMParser).parseFromString('<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">\n  <path fill-rule="evenodd" clip-rule="evenodd"\n    d="M16.5 4.3H3.5C3.38954 4.3 3.3 4.38954 3.3 4.5V15.5C3.3 15.6105 3.38954 15.7 3.5 15.7H8.50005L8.50006 17.5H3.5C2.39543 17.5 1.5 16.6046 1.5 15.5V4.5C1.5 3.39543 2.39543 2.5 3.5 2.5H16.5C17.6046 2.5 18.5 3.39543 18.5 4.5V8.5H16.7V4.5C16.7 4.38954 16.6105 4.3 16.5 4.3ZM12 11.5C11.4477 11.5 11 11.9477 11 12.5L11 16.5C11 17.0523 11.4478 17.5 12 17.5H17.5C18.0523 17.5 18.5 17.0523 18.5 16.5L18.5 12.5C18.5 11.9477 18.0523 11.5 17.5 11.5H12Z"\n    fill="white" />\n</svg>',"image/svg+xml").firstChild}function wn(){return(new DOMParser).parseFromString('<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">\n  <path fill-rule="evenodd" clip-rule="evenodd"\n    d="M16.5 4.3H3.5C3.38954 4.3 3.3 4.38954 3.3 4.5V15.5C3.3 15.6105 3.38954 15.7 3.5 15.7H8.50005L8.50006 17.5H3.5C2.39543 17.5 1.5 16.6046 1.5 15.5V4.5C1.5 3.39543 2.39543 2.5 3.5 2.5H16.5C17.6046 2.5 18.5 3.39543 18.5 4.5V8.5H16.7V4.5C16.7 4.38954 16.6105 4.3 16.5 4.3ZM12 11.5C11.4477 11.5 11 11.9477 11 12.5L11 16.5C11 17.0523 11.4478 17.5 12 17.5H17.5C18.0523 17.5 18.5 17.0523 18.5 16.5L18.5 12.5C18.5 11.9477 18.0523 11.5 17.5 11.5H12Z"\n    fill="white" />\n  <path fill-rule="evenodd" clip-rule="evenodd"\n    d="M9.4998 7.7C9.77595 7.7 9.9998 7.47614 9.9998 7.2V6.5C9.9998 6.22386 9.77595 6 9.4998 6H5.5402L5.52754 6.00016H5.5C5.22386 6.00016 5 6.22401 5 6.50016V10.4598C5 10.7359 5.22386 10.9598 5.5 10.9598H6.2C6.47614 10.9598 6.7 10.7359 6.7 10.4598V8.83005L8.76983 10.9386C8.96327 11.1357 9.27984 11.1386 9.47691 10.9451L9.97645 10.4548C10.1735 10.2613 10.1764 9.94476 9.983 9.7477L7.97289 7.7H9.4998Z"\n    fill="white" />\n</svg>',"image/svg+xml").firstChild}var Tn="picture-in-picture",xn="inline",Sn="fullscreen",En=function(){T(t,an);var e=I(t);function t(){var i;C(this,t);for(var n=arguments.length,o=new Array(n),r=0;r<n;r++)o[r]=arguments[r];return w(E(i=e.call.apply(e,[this].concat(o))),"switchPIP",(function(e){if(!i.isPIPAvailable())return!1;e.stopPropagation&&e.stopPropagation(),i.isPip?(i.exitPIP(),i.emitUserAction(e,"change_pip",{props:"pip",from:!0,to:!1}),i.setAttr("data-state","normal")):4===i.player.media.readyState&&(i.requestPIP(),i.emitUserAction(e,"change_pip",{props:"pip",from:!1,to:!0}),i.setAttr("data-state","pip"))})),i}return _(t,[{key:"beforeCreate",value:function(e){"boolean"==typeof e.player.config.pip&&(e.config.showIcon=e.player.config.pip)}},{key:"afterCreate",value:function(){var e=this;this.isPIPAvailable()&&(L(x(t.prototype),"afterCreate",this).call(this),this.pMode=xn,this.initPipEvents(),this.config.showIcon&&this.initIcons(),this.once(we,(function(){e.config.showIcon&&(W.removeClass(e.find(".xgplayer-icon"),"xg-icon-disable"),e.bind("click",e.switchPIP))})))}},{key:"registerIcons",value:function(){return{pipIcon:{icon:_n,class:"xg-get-pip"},pipIconExit:{icon:wn,class:"xg-exit-pip"}}}},{key:"initIcons",value:function(){var e=this.icons;this.appendChild(".xgplayer-icon",e.pipIcon),this.appendChild(".xgplayer-icon",e.pipIconExit)}},{key:"initPipEvents",value:function(){var e=this,i=this.player;this.leavePIPCallback=function(){var t=i.paused;W.setTimeout(e,(function(){!t&&i.mediaPlay()}),0),!t&&i.mediaPlay(),e.setAttr("data-state","normal"),e.pipWindow=null,i.emit(Fe,!1)},this.enterPIPCallback=function(t){i.emit(Fe,!0),null!=t&&t.pictureInPictureWindow&&(e.pipWindow=t.pictureInPictureWindow),e.setAttr("data-state","pip")},this.onWebkitpresentationmodechanged=function(t){var n=i.media.webkitPresentationMode;e.pMode===Sn&&n!==Sn&&i.onFullscreenChange(null,!1),e.pMode=n,n===Tn?e.enterPIPCallback(t):n===xn&&e.leavePIPCallback(t)},i.media&&(i.media.addEventListener("enterpictureinpicture",this.enterPIPCallback),i.media.addEventListener("leavepictureinpicture",this.leavePIPCallback),t.checkWebkitSetPresentationMode(i.media)&&i.media.addEventListener("webkitpresentationmodechanged",this.onWebkitpresentationmodechanged))}},{key:"copyStyleIntoPiPWindow",value:function(e){var t=A(document.styleSheets).map((function(t){try{return A(t.cssRules).map((function(e){return e.cssText})).join("")}catch(n){var i=document.createElement("link");i.rel="stylesheet",i.type=t.type,i.media=t.media,i.href=t.href,e.document.head.appendChild(i)}return""})).filter(Boolean).join("\n"),i=document.createElement("style");i.textContent=t,e.document.head.appendChild(i)}},{key:"requestPIP",value:function(){var e=this,i=this.player,n=this.playerConfig,o=this.config;if(this.isPIPAvailable()&&!this.isPip)try{var r=n.poster;if(r&&(i.media.poster="String"===W.typeOf(r)?r:r.poster),o.preferDocument&&this.isDocPIPAvailable()){var s={};if(o.width&&o.height)s.width=o.width,s.height=o.height;else{var a=i.root.getBoundingClientRect();s.width=a.width,s.height=a.height}documentPictureInPicture.requestWindow(s).then((function(t){var n=o.docPiPNode,r=o.docPiPStyle;e.enterPIPCallback();var s=n||i.root,a=s.parentElement,l=s.previousSibling,c=s.nextSibling;e.copyStyleIntoPiPWindow(t);var u=document.createElement("style");if(u.append("body{padding:0; margin:0;}"),r){var h="";"string"==typeof r?h=r:"function"==typeof r&&(h=r.call(o)),h&&u.append(h)}else s===i.root&&u.append("\n              .xgplayer{width: 100%!important; height: 100%!important;}\n            ");t.document.head.append(u),t.document.body.append(s),t.addEventListener("pagehide",(function(t){a&&(c?a.insertBefore(s,c):l?a.insertBefore(s,l.nextSibling):a.appendChild(s)),e.leavePIPCallback()}),{once:!0})}))}else t.checkWebkitSetPresentationMode(i.media)?i.media.webkitSetPresentationMode("picture-in-picture"):i.media.requestPictureInPicture();return!0}catch(l){return!1}}},{key:"exitPIP",value:function(){var e=this.player;try{var i;if(this.isPIPAvailable()&&this.isPip)this.isDocPIPAvailable()&&null!==(i=documentPictureInPicture)&&void 0!==i&&i.window?documentPictureInPicture.window.close():t.checkWebkitSetPresentationMode(e.media)?e.media.webkitSetPresentationMode("inline"):document.exitPictureInPicture();return!0}catch(n){return!1}}},{key:"isPip",get:function(){var e,t=this.player;return!(!this.isDocPIPAvailable()||null===(e=documentPictureInPicture)||void 0===e||!e.window)||document.pictureInPictureElement&&document.pictureInPictureElement===t.media||t.media.webkitPresentationMode===Tn}},{key:"isPIPAvailable",value:function(){var e=this.player.media;return"Boolean"===W.typeOf(document.pictureInPictureEnabled)&&document.pictureInPictureEnabled&&("Boolean"===W.typeOf(e.disablePictureInPicture)&&!e.disablePictureInPicture||e.webkitSupportsPresentationMode&&"Function"===W.typeOf(e.webkitSetPresentationMode))||this.isDocPIPAvailable()}},{key:"isDocPIPAvailable",value:function(){return"documentPictureInPicture"in window&&/^(https|file)/.test(location.protocol)}},{key:"destroy",value:function(){L(x(t.prototype),"destroy",this).call(this);var e=this.player;e.media.removeEventListener("enterpictureinpicture",this.enterPIPCallback),e.media.removeEventListener("leavepictureinpicture",this.leavePIPCallback),t.checkWebkitSetPresentationMode(e.media)&&e.media.removeEventListener("webkitpresentationmodechanged",this.onWebkitpresentationmodechanged),this.exitPIP(),this.unbind("click",this.btnClick)}},{key:"render",value:function(){if(this.config.showIcon&&this.isPIPAvailable())return'<xg-icon class="xgplayer-pip">\n      <div class="xgplayer-icon xg-icon-disable">\n      </div>\n      '.concat(ki(this,"PIP",this.playerConfig.isHideTips),"\n    </xg-icon>")}}],[{key:"pluginName",get:function(){return"pip"}},{key:"defaultConfig",get:function(){return{position:_t.CONTROLS_RIGHT,index:6,showIcon:!1,preferDocument:!1,width:void 0,height:void 0,docPiPNode:void 0,docPiPStyle:void 0}}},{key:"checkWebkitSetPresentationMode",value:function(e){return"function"==typeof e.webkitSetPresentationMode}}]),t}();function Pn(){return(new DOMParser).parseFromString('<svg xmlns="http://www.w3.org/2000/svg" width="24" height="40" viewBox="10 0 24 40">\n  <path transform="scale(0.038 0.028)" d="M800 380v768h-128v-352l-320 320v-704l320 320v-352z"></path>\n</svg>\n',"image/svg+xml").firstChild}var In,Ln=function(){T(t,Et);var e=I(t);function t(i){var n;return C(this,t),w(E(n=e.call(this,i)),"playNext",(function(e){var t=E(n).player;e.preventDefault(),e.stopPropagation(),n.idx+1<n.config.urlList.length?(n.idx++,n.nextHandler(n.config.urlList[n.idx],n.idx),t.emit(Ue,n.idx+1)):(n.nextHandler(),t.emit(Ue))})),n.idx=-1,n}return _(t,[{key:"afterCreate",value:function(){this.config.urlList&&0!==this.config.urlList.length&&(this.appendChild(".xgplayer-icon",this.icons.playNext),this.initEvents())}},{key:"registerIcons",value:function(){return{playNext:Pn}}},{key:"initEvents",value:function(){this.nextHandler=this.hook("nextClick",this.changeSrc);var e="mobile"===q.device?"touchend":"click";this.bind(e,this.playNext),this.show()}},{key:"changeSrc",value:function(e){var t=this.player;e&&(t.pause(),t.currentTime=0,t.switchURL?t.switchURL(e):t.src=e,t.config.url=e,t.play())}},{key:"destroy",value:function(){this.unbind(["touchend","click"],this.playNext)}},{key:"render",value:function(){if(this.config.urlList&&0!==this.config.urlList.length)return'\n     <xg-icon class="xgplayer-playnext">\n      <div class="xgplayer-icon">\n      </div>\n      '.concat(ki(this,"PLAYNEXT_TIPS",this.playerConfig.isHideTips),"\n     </xg-icon>\n    ")}}],[{key:"pluginName",get:function(){return"playNext"}},{key:"defaultConfig",get:function(){return{position:_t.CONTROLS_LEFT,index:1,url:null,urlList:[]}}}]),t}(),An={exports:{}};const On=s(In?An.exports:(In=1,An.exports=function e(t,i,n){var o,r,s=window,a="application/octet-stream",l=n||a,c=t,u=!i&&!n&&c,h=document.createElement("a"),d=function(e){return String(e)},f=s.Blob||s.MozBlob||s.WebKitBlob||d,p=i||"download";if(f=f.call?f.bind(s):Blob,"true"===String(this)&&(l=(c=[c,l])[0],c=c[1]),u&&u.length<2048&&(p=u.split("/").pop().split("?")[0],h.href=u,-1!==h.href.indexOf(u))){var g=new XMLHttpRequest;return g.open("GET",u,!0),g.responseType="blob",g.onload=function(t){e(t.target.response,p,a)},setTimeout((function(){g.send()}),0),g}if(/^data:([\w+-]+\/[\w+.-]+)?[,;]/.test(c)){if(!(c.length>2096103.424&&f!==d))return navigator.msSaveBlob?navigator.msSaveBlob(k(c),p):C(c);l=(c=k(c)).type||a}else if(/([\x80-\xff])/.test(c)){for(var v=0,y=new Uint8Array(c.length),m=y.length;v<m;++v)y[v]=c.charCodeAt(v);c=new f([y],{type:l})}function k(e){for(var t=e.split(/[:;,]/),i=t[1],n=("base64"==t[2]?atob:decodeURIComponent)(t.pop()),o=n.length,r=0,s=new Uint8Array(o);r<o;++r)s[r]=n.charCodeAt(r);return new f([s],{type:i})}function C(e,t){if("download"in h)return h.href=e,h.setAttribute("download",p),h.className="download-js-link",h.innerHTML="downloading...",h.style.display="none",document.body.appendChild(h),setTimeout((function(){h.click(),document.body.removeChild(h),!0===t&&setTimeout((function(){s.URL.revokeObjectURL(h.href)}),250)}),66),!0;if(/(Version)\/(\d+)\.(\d+)(?:\.(\d+))?.*Safari\//.test(navigator.userAgent))return/^data:/.test(e)&&(e="data:"+e.replace(/^data:([\w\/\-\+]+)/,a)),window.open(e)||confirm("Displaying New Document\n\nUse Save As... to download, then click back to return to this page.")&&(location.href=e),!0;var i=document.createElement("iframe");document.body.appendChild(i),!t&&/^data:/.test(e)&&(e="data:"+e.replace(/^data:([\w\/\-\+]+)/,a)),i.src=e,setTimeout((function(){document.body.removeChild(i)}),333)}if(o=c instanceof f?c:new f([c],{type:l}),navigator.msSaveBlob)return navigator.msSaveBlob(o,p);if(s.URL)C(s.URL.createObjectURL(o),!0);else{if("string"==typeof o||o.constructor===d)try{return C("data:"+l+";base64,"+s.btoa(o))}catch(b){return C("data:"+l+","+encodeURIComponent(o))}(r=new FileReader).onload=function(e){C(this.result)},r.readAsDataURL(o)}return!0}));function Dn(){return(new DOMParser).parseFromString('<svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" viewBox="0 0 24 24">\n  <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n    <g transform="translate(-488.000000, -340.000000)" fill="#FFFFFF">\n      <g id="Group-2">\n        <g id="volme_big-copy" transform="translate(488.000000, 340.000000)">\n          <rect id="Rectangle-18" x="11" y="4" width="2" height="12" rx="1"></rect>\n          <rect id="Rectangle-2" x="3" y="18" width="18" height="2" rx="1"></rect>\n          <rect id="Rectangle-2" transform="translate(4.000000, 17.500000) rotate(90.000000) translate(-4.000000, -17.500000) " x="1.5" y="16.5" width="5" height="2" rx="1"></rect><rect id="Rectangle-2-Copy-3" transform="translate(20.000000, 17.500000) rotate(90.000000) translate(-20.000000, -17.500000) " x="17.5" y="16.5" width="5" height="2" rx="1"></rect>\n          <path d="M9.48791171,8.26502656 L9.48791171,14.2650266 C9.48791171,14.8173113 9.04019646,15.2650266 8.48791171,15.2650266 C7.93562696,15.2650266 7.48791171,14.8173113 7.48791171,14.2650266 L7.48791171,7.26502656 C7.48791171,6.71274181 7.93562696,6.26502656 8.48791171,6.26502656 L15.4879117,6.26502656 C16.0401965,6.26502656 16.4879117,6.71274181 16.4879117,7.26502656 C16.4879117,7.81731131 16.0401965,8.26502656 15.4879117,8.26502656 L9.48791171,8.26502656 Z" id="Combined-Shape" transform="translate(11.987912, 10.765027) scale(1, -1) rotate(45.000000) translate(-11.987912, -10.765027) "></path>\n        </g>\n      </g>\n    </g>\n  </g>\n</svg>\n',"image/svg+xml").firstChild}var Rn=function(){T(t,an);var e=I(t);function t(i){var n;return C(this,t),w(E(n=e.call(this,i)),"download",(function(e){if(!n.isLock){n.emitUserAction(e,"download");var t=n.playerConfig.url,i="";"String"===W.typeOf(t)?i=t:"Array"===W.typeOf(t)&&t.length>0&&(i=t[0].src);var o=n.getAbsoluteURL(i);On(o),n.isLock=!0,n.timer=window.setTimeout((function(){n.isLock=!1,window.clearTimeout(n.timer),n.timer=null}),300)}})),n.timer=null,n.isLock=!1,n}return _(t,[{key:"afterCreate",value:function(){L(x(t.prototype),"afterCreate",this).call(this),this.config.disable||(this.appendChild(".xgplayer-icon",this.icons.download),this._handler=this.hook("click",this.download,{pre:function(e){e.preventDefault(),e.stopPropagation()}}),this.bind(["click","touchend"],this._handler))}},{key:"registerIcons",value:function(){return{download:Dn}}},{key:"getAbsoluteURL",value:function(e){if(!e.match(/^https?:\/\//)){var t=document.createElement("div");t.innerHTML='<a href="'.concat(e,'">x</a>'),e=t.firstChild.href}return e}},{key:"destroy",value:function(){L(x(t.prototype),"destroy",this).call(this),this.unbind(["click","touchend"],this.download),window.clearTimeout(this.timer),this.timer=null}},{key:"render",value:function(){if(!this.config.disable)return'<xg-icon class="xgplayer-download">\n   <div class="xgplayer-icon">\n   </div>\n   '.concat(ki(this,"DOWNLOAD_TIPS",this.playerConfig.isHideTips),"\n    </xg-icon>")}}],[{key:"pluginName",get:function(){return"download"}},{key:"defaultConfig",get:function(){return{position:_t.CONTROLS_RIGHT,index:3,disable:!0}}}]),t}(),Mn=function(){T(t,an);var e=I(t);function t(){return C(this,t),e.apply(this,arguments)}return _(t,[{key:"beforeCreate",value:function(e){"boolean"==typeof e.player.config.screenShot&&(e.config.disable=!e.player.config.screenShot)}},{key:"afterCreate",value:function(){L(x(t.prototype),"afterCreate",this).call(this),this.appendChild(".xgplayer-icon",this.icons.screenshotIcon);var e=this.config;this.initSize=function(t){e.fitVideo&&(e.width=t.vWidth,e.height=t.vHeight)},this.once(Ne,this.initSize)}},{key:"onPluginsReady",value:function(){this.show(),this.onClickBtn=this.onClickBtn.bind(this),this.bind(["click","touchend"],this.onClickBtn)}},{key:"saveScreenShot",value:function(e,t){var i,n=document.createElement("a");n.href=e,n.download=t;try{"undefined"!=typeof MouseEvent?i=new MouseEvent("click",{bubbles:!0,cancelable:!0,view:window}):(i=document.createEvent("MouseEvents")).initMouseEvent("click",!0,!1,window,0,0,0,0,0,!1,!1,!1,!1,0,null)}catch(o){}i&&n.dispatchEvent(i)}},{key:"createCanvas",value:function(e,t){var i=document.createElement("canvas"),n=i.getContext("2d");this.canvasCtx=n,this.canvas=i,i.width=e||this.config.width,i.height=t||this.config.height,n.imageSmoothingEnabled=!0,n.imageSmoothingEnabled&&(n.imageSmoothingQuality="high")}},{key:"onClickBtn",value:function(e){var t=this;e.preventDefault(),e.stopPropagation(),this.emitUserAction(e,"shot");var i=this.config;this.shot(i.width,i.height).then((function(e){t.emit(Be,e),i.saveImg&&t.saveScreenShot(e,i.name+i.format)}))}},{key:"shot",value:function(e,t){var i=this,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{quality:.92,type:"image/png"},o=this.config,r=this.player,s=n.quality||o.quality,a=n.type||o.type;return new Promise((function(n,l){var c,u=null;if(r.media.canvas)u=r.media.canvas;else{i.canvas?(i.canvas.width=e||o.width,i.canvas.height=t||o.height):i.createCanvas(e,t),u=i.canvas,c=i.canvasCtx;var h,d,f,p,g=r.media.videoWidth/r.media.videoHeight,v=u.width/u.height,y=r.media.videoWidth,m=r.media.videoHeight;g>v?(f=u.width,p=u.width/g,h=0,d=Math.round((u.height-p)/2)):g===v?(f=u.width,p=u.height,h=0,d=0):g<v&&(f=u.height*g,p=u.height,h=Math.round((u.width-f)/2),d=0),c.drawImage(r.media,0,0,y,m,h,d,f,p)}var k=u.toDataURL(a,s).replace(a,"image/octet-stream");n(k=k.replace(/^data:image\/[^;]+/,"data:application/octet-stream"))}))}},{key:"registerIcons",value:function(){return{screenshotIcon:null}}},{key:"destroy",value:function(){L(x(t.prototype),"destroy",this).call(this),this.unbind(["click","touchend"],this.onClickBtn),this.off(Ne,this.initSize)}},{key:"render",value:function(){if(!this.config.disable){var e=this.icons.screenshotIcon?"xgplayer-icon":"xgplayer-icon btn-text",t="SCREENSHOT";return'\n      <xg-icon class="xgplayer-shot">\n      <div class="'.concat(e,'">\n      ').concat(this.icons.screenshotIcon?"":'<span lang-key="'.concat(this.i18nKeys[t],'">').concat(this.i18n[t],"</span>"),"\n      </div>\n    </xg-icon>")}}}],[{key:"pluginName",get:function(){return"screenShot"}},{key:"defaultConfig",get:function(){return{position:_t.CONTROLS_RIGHT,index:5,quality:.92,type:"image/png",format:".png",width:600,height:337,saveImg:!0,fitVideo:!0,disable:!1,name:"screenshot"}}}]),t}(),Nn=function(){function e(t){C(this,e),this.config=t.config,this.parent=t.root,this.root=W.createDom("ul","",{},"xg-options-list xg-list-slide-scroll ".concat(this.config.className)),t.root.appendChild(this.root);var i=this.config.maxHeight;i&&this.setStyle({maxHeight:i}),this.onItemClick=this.onItemClick.bind(this),this.renderItemList();var n="touch"===this.config.domEventType?"touchend":"click";this._delegates=Et.delegate.call(this,this.root,"li",n,this.onItemClick)}return _(e,[{key:"renderItemList",value:function(e){var t=this,i=this.config,n=this.root;e?i.data=e:e=i.data,i.style&&Object.keys(i.style).map((function(e){n.style[e]=i[e]})),e.length>0&&(this.attrKeys=Object.keys(e[0])),this.root.innerHTML="",e.map((function(e,i){var n=e.selected?"option-item selected":"option-item";e["data-index"]=i,t.root.appendChild(W.createDom("li","<span>".concat(e.showText,"</span>"),e,n))}))}},{key:"onItemClick",value:function(e){e.delegateTarget||(e.delegateTarget=e.target);var t=e.delegateTarget;if(t&&W.hasClass(t,"selected"))return!1;var i="function"==typeof this.config.onItemClick?this.config.onItemClick:null,n=this.root.querySelector(".selected");W.addClass(t,"selected"),n&&W.removeClass(n,"selected"),i(e,{from:n?this.getAttrObj(n,this.attrKeys):null,to:this.getAttrObj(t,this.attrKeys)})}},{key:"getAttrObj",value:function(e,t){if(!e||!t)return{};var i={};t.map((function(t){i[t]=e.getAttribute(t)}));var n=e.getAttribute("data-index");return n&&(i.index=Number(n)),i}},{key:"show",value:function(){W.removeClass(this.root,"hide"),W.addClass(this.root,"active")}},{key:"hide",value:function(){W.removeClass(this.root,"active"),W.addClass(this.root,"hide")}},{key:"setStyle",value:function(e){var t=this;Object.keys(e).forEach((function(i){t.root.style[i]=e[i]}))}},{key:"destroy",value:function(){this._delegates&&(this._delegates.map((function(e){e.destroy&&e.destroy()})),this._delegates=null),this.root.innerHTML=null,this.parent.removeChild(this.root),this.root=null}}]),e}(),Fn="side",Hn="middle",Bn="default",Un="click",jn="hover";var Vn="mobile"===q.device,Wn=function(){T(t,Et);var e=I(t);function t(i){var n;return C(this,t),w(E(n=e.call(this,i)),"onEnter",(function(e){e.stopPropagation(),n.emit("icon_mouseenter",{pluginName:n.pluginName}),n.switchActiveState(e)})),w(E(n),"switchActiveState",(function(e){e.stopPropagation(),n.config.toggleMode===Un?n.toggle(!n.isActive):n.toggle(!0)})),w(E(n),"onLeave",(function(e){e.stopPropagation(),n.emit("icon_mouseleave",{pluginName:n.pluginName}),n.config.listType!==Fn&&n.isActive&&n.toggle(!1)})),w(E(n),"onListEnter",(function(e){n.enterType=2})),w(E(n),"onListLeave",(function(e){n.enterType=0,n.isActive&&n.toggle(!1)})),n.isIcons=!1,n.isActive=!1,n.curValue=null,n.curIndex=0,n}return _(t,[{key:"updateLang",value:function(e){this.renderItemList(this.config.list,this.curIndex)}},{key:"afterCreate",value:function(){var e=this,t=this.config;this.initIcons(),(Vn=Vn||"touch"===this.domEventType)&&"mobile"===q.device&&t.listType===Bn&&(t.listType=Fn),t.hidePortrait&&W.addClass(this.root,"portrait"),this.on([Ne,Ae],(function(){e._resizeList()})),this.once(ce,(function(){t.list&&t.list.length>0&&(e.renderItemList(t.list),e.show())})),Vn&&this.on(ye,(function(){e.isActive&&(e.optionsList&&e.optionsList.hide(),e.isActive=!1)})),Vn?(t.toggleMode=Un,this.activeEvent="touchend"):this.activeEvent=t.toggleMode===Un?"click":"mouseenter",t.toggleMode===Un?this.bind(this.activeEvent,this.switchActiveState):(this.bind(this.activeEvent,this.onEnter),this.bind("mouseleave",this.onLeave)),this.isIcons&&this.bind("click",this.onIconClick)}},{key:"initIcons",value:function(){var e=this,t=this.icons,i=Object.keys(t),n=!1;i.length>0&&(i.forEach((function(i){e.appendChild(".xgplayer-icon",t[i]),!n&&(n=t[i])})),this.isIcons=n),n||(this.appendChild(".xgplayer-icon",W.createDom("span","",{},"icon-text")),W.addClass(this.find(".xgplayer-icon"),"btn-text"))}},{key:"show",value:function(e){!this.config.list||this.config.list.length<2||W.addClass(this.root,"show")}},{key:"hide",value:function(){W.removeClass(this.root,"show")}},{key:"getTextByLang",value:function(e,t,i){if(void 0===e)return"";var n=this.config.list;!i&&(i=this.player.lang),t=!t||W.isUndefined(e[t])?"text":t,"number"==typeof e&&(e=n[e]);try{return"object"===k(e[t])?e[t][i]||e[t].en:e[t]}catch(o){return""}}},{key:"toggle",value:function(e){if(e!==this.isActive&&!this.config.disable){var t=this.player.controls,i=this.config.listType;e?(i===Fn?t.blur():t.focus(),this.optionsList&&this.optionsList.show()):(i===Fn?t.focus():t.focusAwhile(),this.optionsList&&this.optionsList.hide()),this.isActive=e}}},{key:"onItemClick",value:function(e,t){e.stopPropagation();var i=this.config,n=i.listType,o=i.list;this.curIndex=t.to.index,this.curItem=o[this.curIndex],this.changeCurrentText(),(this.config.isItemClickHide||Vn||n===Fn)&&this.toggle(!1)}},{key:"onIconClick",value:function(e){}},{key:"changeCurrentText",value:function(){if(!this.isIcons){var e=this.config.list,t=e[this.curIndex<e.length?this.curIndex:0];t&&(this.find(".icon-text").innerHTML=this.getTextByLang(t,"iconText"))}}},{key:"renderItemList",value:function(e,t){var i=this,n=this.config,o=this.optionsList,r=this.player;if("number"==typeof t&&(this.curIndex=t,this.curItem=n.list[t]),o)return o.renderItemList(e),void this.changeCurrentText();var s,a,l={config:{data:e||[],className:(s=n.listType,a=n.position,s===Fn?a===_t.CONTROLS_LEFT?"xg-side-list xg-left-side":"xg-side-list xg-right-side":""),onItemClick:function(e,t){i.onItemClick(e,t)},domEventType:Vn?"touch":"mouse"},root:n.listType===Fn?r.innerContainer||r.root:this.root};if(this.config.isShowIcon){var c=this.player.root.getBoundingClientRect().height,u=n.listType===Hn?c-50:c;u&&n.heightLimit&&(l.config.maxHeight="".concat(u,"px")),this.optionsList=new Nn(l),this.changeCurrentText(),this.show()}this._resizeList()}},{key:"_resizeList",value:function(){if(this.config.heightLimit){var e=this.player.root.getBoundingClientRect().height,t=this.config.listType===Hn?e-50:e;this.optionsList&&this.optionsList.setStyle({maxHeight:"".concat(t,"px")})}}},{key:"destroy",value:function(){this.config.toggleMode===Un?this.unbind(this.activeEvent,this.switchActiveState):(this.unbind(this.activeEvent,this.onEnter),this.unbind("mouseleave",this.onLeave)),this.isIcons&&this.unbind("click",this.onIconClick),this.optionsList&&(this.optionsList.destroy(),this.optionsList=null)}},{key:"render",value:function(){if(this.config.isShowIcon)return'<xg-icon class="xg-options-icon '.concat(this.config.className||"",'">\n    <div class="xgplayer-icon">\n    </div>\n   </xg-icon>')}}],[{key:"pluginName",get:function(){return"optionsIcon"}},{key:"defaultConfig",get:function(){return{position:_t.CONTROLS_RIGHT,index:100,list:[],listType:"default",listStyle:{},hidePortrait:!0,isShowIcon:!1,isItemClickHide:!0,toggleMode:jn,heightLimit:!0}}}]),t}(),Gn=function(){T(t,Wn);var e=I(t);function t(i){var n;return C(this,t),(n=e.call(this,i)).curTime=0,n.isPaused=!0,n}return _(t,[{key:"beforeCreate",value:function(e){var t=e.config.list;Array.isArray(t)&&t.length>0&&(e.config.list=t.map((function(e){return!e.text&&e.name&&(e.text=e.name),e.text||(e.text=e.definition),e})))}},{key:"afterCreate",value:function(){var e=this;L(x(t.prototype),"afterCreate",this).call(this),this.on("resourceReady",(function(t){e.changeDefinitionList(t)})),this.on(Re,(function(t){e.renderItemList(e.config.list,t.to)})),this.player.definitionList.length<2&&this.hide()}},{key:"show",value:function(e){!this.config.list||this.config.list.length<2||W.addClass(this.root,"show")}},{key:"initDefinition",value:function(){var e=this.config,t=e.list,i=e.defaultDefinition;if(t.length>0){var n=null;t.map((function(e){e.definition===i&&(n=e)})),n||(n=t[0]),this.changeDefinition(n)}}},{key:"renderItemList",value:function(){var e=this,i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.config.list||[],n=arguments.length>1?arguments[1]:void 0,o=n&&n.definition?n.definition:this.config.defaultDefinition;n&&i.forEach((function(e){e.selected=!1}));var r=0,s=i.map((function(t,i){var n=m(m({},t),{},{showText:e.getTextByLang(t)||t.definition,selected:!1});return(t.selected||t.definition&&t.definition==o)&&(n.selected=!0,r=i),n}));L(x(t.prototype),"renderItemList",this).call(this,s,r)}},{key:"changeDefinitionList",value:function(e){Array.isArray(e)&&(this.config.list=e.map((function(e){return!e.text&&e.name&&(e.text=e.name),e.text||(e.text=e.definition),e})),this.renderItemList(),this.config.list.length<2?this.hide():this.show())}},{key:"changeDefinition",value:function(e,t){this.player.changeDefinition(e,t)}},{key:"onItemClick",value:function(e,i){var n=this.player.definitionList;L(x(t.prototype),"onItemClick",this).apply(this,arguments),this.emitUserAction(e,"change_definition",{from:i.from,to:i.to});for(var o=0;o<n.length;o++)i.to&&n[o].definition===i.to.definition&&(i.to.url=n[o].url),i.from&&n[o].definition===i.from.definition&&(i.from.url=n[o].url);this.player.changeDefinition(i.to,i.from)}}],[{key:"pluginName",get:function(){return"definition"}},{key:"defaultConfig",get:function(){return m(m({},Wn.defaultConfig),{},{position:_t.CONTROLS_RIGHT,index:3,list:[],defaultDefinition:"",disable:!1,hidePortrait:!1,className:"xgplayer-definition",isShowIcon:!0})}}]),t}(),zn=function(){T(t,Wn);var e=I(t);function t(i){var n;return C(this,t),(n=e.call(this,i)).curRate=1,n}return _(t,[{key:"beforeCreate",value:function(e){var t=e.player.config.playbackRate,i=t?Array.isArray(t)?t:e.config.list:[];Array.isArray(i)&&(e.config.list=i.map((function(e){return"number"==typeof e?e={rate:e,text:"".concat(e,"x")}:!e.text&&e.rate&&(e.text="".concat(e.rate,"x")),e})))}},{key:"afterCreate",value:function(){var e=this;L(x(t.prototype),"afterCreate",this).call(this),this.on(fe,(function(){e.curValue!==e.player.playbackRate&&e.renderItemList()})),this.renderItemList()}},{key:"show",value:function(e){this.config.list&&0!==this.config.list.length&&L(x(t.prototype),"show",this).call(this)}},{key:"onItemClick",value:function(e,i){L(x(t.prototype),"onItemClick",this).call(this,e,i);var n=e.delegateTarget,o=Number(n.getAttribute("rate"));if(!o||o===this.curValue)return!1;var r={playbackRate:{from:this.player.playbackRate,to:o}};this.emitUserAction(e,"change_rate",{props:r}),this.curValue=o,this.player.playbackRate=o}},{key:"renderItemList",value:function(){var e=this,i=this.player.playbackRate||1;this.curValue=i;var n=-1,o=this.config.list.map((function(t,o){var r={rate:t.rate};return r.rate===i&&(r.selected=!0,n=o),r.showText=e.getTextByLang(t),r}));L(x(t.prototype),"renderItemList",this).call(this,o,n)}},{key:"changeCurrentText",value:function(){if(!this.isIcons){var e=this.config.list,t=e[this.curIndex<e.length?this.curIndex:0],i="";i=!t||this.curIndex<0?"".concat(this.player.playbackRate,"x"):this.getTextByLang(t,"iconText"),this.find(".icon-text").innerHTML=i}}},{key:"destroy",value:function(){L(x(t.prototype),"destroy",this).call(this)}}],[{key:"pluginName",get:function(){return"playbackRate"}},{key:"defaultConfig",get:function(){return m(m({},Wn.defaultConfig),{},{position:_t.CONTROLS_RIGHT,index:4,list:[2,1.5,1,.75,.5],className:"xgplayer-playbackrate",isShowIcon:!0,hidePortrait:!1})}}]),t}();function Kn(){return(new DOMParser).parseFromString('<svg xmlns="http://www.w3.org/2000/svg" width="31" height="40" viewBox="0 -5 31 40">\n  <path fill="#fff" transform="scale(1.3, 1.3)" class=\'path_full\' d="M9,10v1a.9.9,0,0,1-1,1,.9.9,0,0,1-1-1V9A.9.9,0,0,1,8,8h2a.9.9,0,0,1,1,1,.9.9,0,0,1-1,1Zm6,4V13a1,1,0,0,1,2,0v2a.9.9,0,0,1-1,1H14a1,1,0,0,1,0-2Zm3-7H6V17H18Zm2,0V17a2,2,0,0,1-2,2H6a2,2,0,0,1-2-2V7A2,2,0,0,1,6,5H18A2,2,0,0,1,20,7Z"></path>\n</svg>\n',"image/svg+xml").firstChild}function Yn(){return(new DOMParser).parseFromString('<svg xmlns="http://www.w3.org/2000/svg" width="31" height="40" viewBox="0 -5 31 40">\n  <path fill="#fff" transform="scale(1.3, 1.3)" d="M9,10V9a.9.9,0,0,1,1-1,.9.9,0,0,1,1,1v2a.9.9,0,0,1-1,1H8a.9.9,0,0,1-1-1,.9.9,0,0,1,1-1Zm6,4v1a1,1,0,0,1-2,0V13a.9.9,0,0,1,1-1h2a1,1,0,0,1,0,2Zm3-7H6V17H18Zm2,0V17a2,2,0,0,1-2,2H6a2,2,0,0,1-2-2V7A2,2,0,0,1,6,5H18A2,2,0,0,1,20,7Z"></path>\n</svg>\n',"image/svg+xml").firstChild}var Xn=function(){T(t,an);var e=I(t);function t(){return C(this,t),e.apply(this,arguments)}return _(t,[{key:"beforeCreate",value:function(e){"boolean"==typeof e.player.config.cssFullscreen&&(e.config.disable=!e.player.config.cssFullscreen)}},{key:"afterCreate",value:function(){var e=this;L(x(t.prototype),"afterCreate",this).call(this),this.config.disable||(this.config.target&&(this.playerConfig.fullscreenTarget=this.config.target),this.initIcons(),this.on(Oe,(function(t){e.animate(t)})),this.btnClick=this.btnClick.bind(this),this.handleCssFullscreen=this.hook("cssFullscreen_change",this.btnClick,{pre:function(e){e.preventDefault(),e.stopPropagation()}}),this.bind(["click","touchend"],this.handleCssFullscreen))}},{key:"initIcons",value:function(){var e=this.icons,t=this.find(".xgplayer-icon");t.appendChild(e.cssFullscreen),t.appendChild(e.exitCssFullscreen)}},{key:"btnClick",value:function(e){e.preventDefault(),e.stopPropagation();var t=this.player.isCssfullScreen;this.emitUserAction(e,"switch_cssfullscreen",{cssfullscreen:t}),t?this.player.exitCssFullscreen():this.player.getCssFullscreen()}},{key:"animate",value:function(e){this.root&&(e?this.setAttr("data-state","full"):this.setAttr("data-state","normal"),this.switchTips(e))}},{key:"switchTips",value:function(e){var t=this.i18nKeys,i=this.find(".xg-tips");i&&this.changeLangTextKey(i,e?t.EXITCSSFULLSCREEN_TIPS:t.CSSFULLSCREEN_TIPS)}},{key:"registerIcons",value:function(){return{cssFullscreen:{icon:Kn,class:"xg-get-cssfull"},exitCssFullscreen:{icon:Yn,class:"xg-exit-cssfull"}}}},{key:"destroy",value:function(){L(x(t.prototype),"destroy",this).call(this),this.unbind(["click","touchend"],this.btnClick)}},{key:"render",value:function(){if(!this.config.disable)return"<xg-icon class='xgplayer-cssfullscreen'>\n    <div class=\"xgplayer-icon\">\n    </div>\n    ".concat(ki(this,"CSSFULLSCREEN_TIPS",this.playerConfig.isHideTips),"\n    </xg-icon>")}}],[{key:"pluginName",get:function(){return"cssFullscreen"}},{key:"defaultConfig",get:function(){return{position:_t.CONTROLS_RIGHT,index:1,disable:!1,target:null}}}]),t}(),qn=function(){T(t,Et);var e=I(t);function t(){return C(this,t),e.apply(this,arguments)}return _(t,[{key:"afterCreate",value:function(){var e=this;this.clickHandler=this.hook("errorRetry",this.errorRetry,{pre:function(e){e.preventDefault(),e.stopPropagation()}}),this.onError=this.hook("showError",this.handleError),this.bind(".xgplayer-error-refresh","click",this.clickHandler),this.on(oe,(function(t){e.onError(t)}))}},{key:"errorRetry",value:function(e){this.emitUserAction(e,"error_retry",{}),this.player.retry()}},{key:"handleError",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=this.player,i=e.errorType,n=t.errorNote?this.i18n[t.errorNote]:"";if(!n)switch(i){case"decoder":n=this.i18n.MEDIA_ERR_DECODE;break;case"network":n=this.i18n.MEDIA_ERR_NETWORK;break;default:n=this.i18n.MEDIA_ERR_SRC_NOT_SUPPORTED}this.find(".xgplayer-error-text").innerHTML=n,this.find(".xgplayer-error-tips").innerHTML="".concat(this.i18n.REFRESH_TIPS,'<span class="xgplayer-error-refresh">').concat(this.i18n.REFRESH,"</span>")}},{key:"destroy",value:function(){this.unbind(".xgplayer-error-refresh","click",this.clickHandler)}},{key:"render",value:function(){return'<xg-error class="xgplayer-error">\n      <div class="xgplayer-errornote">\n       <span class="xgplayer-error-text"></span>\n       <span class="xgplayer-error-tips"><em class="xgplayer-error-refresh"></em></span>\n      </div>\n    </xg-error>'}}],[{key:"pluginName",get:function(){return"error"}}]),t}(),Zn=function(){T(t,Et);var e=I(t);function t(){return C(this,t),e.apply(this,arguments)}return _(t,[{key:"afterCreate",value:function(){var e=this;this.intervalId=0,this.customConfig=null,this.bind(".highlight",["click","touchend"],(function(t){(e.config.onClick||e.customOnClick)&&(t.preventDefault(),t.stopPropagation(),e.customOnClick?e.customOnClick(t):e.config.onClick(t))})),this.player.showPrompt=function(){e.showPrompt.apply(e,arguments)},this.player.hidePrompt=function(){e.hide()}}},{key:"setStyle",value:function(e){var t=this;Object.keys(e).map((function(i){t.root.style[i]=e[i]}))}},{key:"showPrompt",value:function(e){var t=this,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:function(){};if(e){this.customOnClick=n;var o=this.config.interval;if(this.intervalId&&(clearTimeout(this.intervalId),this.intervalId=null),W.addClass(this.root,"show"),"arrow"===i.mode&&W.addClass(this.root,"arrow"),this.find(".xgplayer-prompt-detail").innerHTML="string"==typeof e?e:"".concat(e.text||"")+"".concat(e.highlight?'<i class="highlight">'.concat(e.highlight,"</i>"):""),i.style&&this.setStyle(i.style),"boolean"==typeof i.autoHide?i.autoHide:this.config.autoHide){var r=i.interval||o;this.intervalId=setTimeout((function(){t.hide()}),r)}}}},{key:"hide",value:function(){W.removeClass(this.root,"show"),W.removeClass(this.root,"arrow"),this.root.removeAttribute("style"),this.customOnClick=null}},{key:"render",value:function(){return'<xg-prompt class="xgplayer-prompt '.concat(At.CONTROLS_FOLLOW,'">\n    <span class="xgplayer-prompt-detail"></span>\n    </xg-prompt>')}}],[{key:"pluginName",get:function(){return"prompt"}},{key:"defaultConfig",get:function(){return{interval:3e3,style:{},mode:"arrow",autoHide:!0,detail:{text:"",highlight:""},onClick:function(){}}}}]),t}(),Jn={time:0,text:"",id:1,duration:1,color:"#fff",style:{},width:6,height:6};function $n(e){Object.keys(Jn).map((function(t){void 0===e[t]&&(e[t]=Jn[t])}))}var Qn={_updateDotDom:function(e,t){if(t){var i=this.calcuPosition(e.time,e.duration),n=e.style||{};n.left="".concat(i.left,"%"),n.width="".concat(i.width,"%"),t.setAttribute("data-text",e.text),t.setAttribute("data-time",e.time),i.isMini?W.addClass(t,"mini"):W.removeClass(t,"mini"),Object.keys(n).map((function(e){t.style[e]=n[e]}))}},initDots:function(){var e=this;this._ispots.map((function(t){e.createDot(t,!1)})),this.ispotsInit=!0},createDot:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],i=this.player.plugins.progress;if(i&&(t&&($n(e),this._ispots.push(e)),this.ispotsInit||!t)){var n=this.calcuPosition(e.time,e.duration),o=e.style||{};o.left="".concat(n.left,"%"),o.width="".concat(n.width,"%");var r="xgspot_".concat(e.id," xgplayer-spot");n.isMini&&(r+=" mini");var s=e.template?'<div class="xgplayer-spot-pop">'.concat(e.template,"</div>"):"",a=W.createDom("xg-spot",s,{"data-text":e.text,"data-time":e.time,"data-id":e.id},r);Object.keys(o).map((function(e){a.style[e]=o[e]})),i.outer&&i.outer.appendChild(a),this.positionDot(a,e.id)}},findDot:function(e){if(this.player.plugins.progress){var t=this._ispots.filter((function(t,i){return t.id===e}));return t.length>0?t[0]:null}},updateDot:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=this.player.plugins.progress;if(i){var n=this.findDot(e.id);if(n&&Object.keys(e).map((function(t){n[t]=e[t]})),this.ispotsInit){var o=i.find('xg-spot[data-id="'.concat(e.id,'"]'));o&&(this._updateDotDom(e,o),t&&this.showDot(e.id))}}},deleteDot:function(e){var t=this._ispots,i=this.player.plugins.progress;if(i){for(var n=[],o=0;o<t.length;o++)t[o].id===e&&n.push(o);for(var r=n.length-1;r>=0;r--)if(t.splice(n[r],1),this.ispotsInit){var s=i.find('xg-spot[data-id="'.concat(e,'"]'));s&&s.parentElement.removeChild(s)}}},deleteAllDots:function(){var e=this.player.plugins.progress;if(e)if(this.ispotsInit){for(var t=e.root.getElementsByTagName("xg-spot"),i=t.length-1;i>=0;i--)e.outer.removeChild(t[i]);this._ispots=[]}else this._ispots=[]},updateAllDots:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],i=this.player.plugins.progress;if(i)if(this.ispotsInit){this._ispots=[];var n=i.root.getElementsByTagName("xg-spot"),o=n.length;if(o>t.length)for(var r=o-1;r>t.length-1;r--)i.outer.removeChild(n[r]);t.forEach((function(t,i){i<o?(n[i].setAttribute("data-id","".concat(t.id)),e._ispots.push(t),e.updateDot(t)):e.createDot(t)}))}else this._ispots=t},positionDots:function(){var e=this,t=this._ispots,i=this.playerSize,n=this.player.sizeInfo,o=this.player.plugins.progress;o&&n.width!==i.width&&(i.width=n.width,i.left=n.left,t.forEach((function(t){var i=o.find('xg-spot[data-id="'.concat(t.id,'"]'));i&&e.positionDot(i,t.id)})))},positionDot:function(e,t){var i=W.findDom(e,".xgplayer-spot-pop");if(i){var n=this.playerSize,o=e.getBoundingClientRect(),r=i.getBoundingClientRect(),s=o.left-n.left,a=n.width-s-o.width/2;if(s<r.width/2||n.width<r.width){var l=r.width/2-s;i.style.left="".concat(l,"px")}else if(a<r.width/2){var c=a-r.width/2+o.width/2;i.style.left="".concat(c,"px")}else i.style.left="50%"}},updateDuration:function(){var e=this,t=this.player.plugins.progress;t&&this._ispots.forEach((function(i){var n=t.find('xg-spot[data-id="'.concat(i.id,'"]'));e._updateDotDom(i,n)}))},getAllDotsDom:function(){var e=this.player.plugins.progress;return e?e.root.getElementsByTagName("xg-spot"):[]},getDotDom:function(e){var t=this.player.plugins.progress;if(t)return t.find('xg-spot[data-id="'.concat(e,'"]'))}};var eo={dragmove:"onProgressMove",dragstart:"onProgressDragStart",dragend:"onProgressDragEnd",click:"onProgressClick",mouseover:"onProgressMouseOver",mouseenter:"onProgressMouseOver"},to=function(){T(t,Et);var e=I(t);function t(i){var n;return C(this,t),w(E(n=e.call(this,i)),"onMousemove",(function(e){n.config.disable||(W.hasClass(e.target,"xg-spot-content")&&n.config.isHideThumbnailHover?n.player.plugins.progress.onMouseLeave(e):(n._state.f||W.hasClass(e.target,"xg-spot-content"))&&(W.event(e),e.stopPropagation()))})),w(E(n),"onMousedown",(function(e){n.config.disable||(n._state.f||W.hasClass(e.target,"xg-spot-content"))&&(W.event(e),e.stopPropagation())})),w(E(n),"onMouseup",(function(e){if(n.isDrag){var t=n.player.plugins.progress;t&&t.pos&&(t.onMouseUp(e),!t.pos.isEnter&&t.onMouseLeave(e))}})),w(E(n),"onDotMouseLeave",(function(e){if(!n.config.disable){n._curDot.removeEventListener("mouseleave",n.onDotMouseLeave),n.blurDot(e.target),n._curDot=null;var t=n.player.plugins.progress;t&&t.enableBlur(),n.show()}})),w(E(n),"onProgressMouseOver",(function(e,t){if(!n.config.disable&&W.hasClass(t.target,"xgplayer-spot")&&!n._curDot){n._curDot=t.target,n.focusDot(t.target),n._curDot.children.length>0&&n.hide();var i=n.player.plugins.progress;i&&i.disableBlur(),n._curDot.addEventListener("mouseleave",n.onDotMouseLeave)}})),n._ispots=[],n.videoPreview=null,n.videothumbnail=null,n.thumbnail=null,n.timeStr="",n._state={now:0,f:!1},n}return _(t,[{key:"beforeCreate",value:function(e){var t=e.player.plugins.progress;t&&(e.root=t.root)}},{key:"afterCreate",value:function(){var e=this;this._curDot=null,this.handlerSpotClick=this.hook("spotClick",(function(t,i){i.seekTime&&e.player.seek(i.seekTime)})),this.transformTimeHook=this.hook("transformTime",(function(t){e.setTimeContent(W.format(t))})),function(e){var t=e.config,i=e.player;Object.keys(Qn).map((function(t){e[t]=Qn[t].bind(e)}));var n=i.config.progressDot||t.ispots||[];e._ispots=n.map((function(e){return $n(e),e})),e.ispotsInit=!1,e.playerSize={left:i.sizeInfo.left,width:i.sizeInfo.width},e.on(ue,(function(){e.ispotsInit?e.updateDuration():e.initDots()})),e.on(Ne,(function(){e.positionDots()}))}(this),this.on(ue,(function(){e.show()})),this.config.disable&&this.disable(),this.extTextRoot=this.find(".xg-spot-ext-text")}},{key:"setConfig",value:function(e){var t=this;e&&Object.keys(e).map((function(i){t.config[i]=e[i]}))}},{key:"onPluginsReady",value:function(){this.player.plugins.progress&&(this.previewLine=this.find(".xg-spot-line"),this.timePoint=this.find(".xgplayer-progress-point"),this.timeText=this.find(".xg-spot-time"),this.tipText=this.find(".spot-inner-text"),this._hasThumnail=!1,this.registerThumbnail(),this.bindEvents())}},{key:"bindEvents",value:function(){var e=this,t=this.player.plugins.progress;if(t&&(Object.keys(eo).map((function(i){e[eo[i]]=e[eo[i]].bind(e),t.addCallBack(i,e[eo[i]])})),"mobile"!==q.device)){this.bind(".xg-spot-info","mousemove",this.onMousemove),this.bind(".xg-spot-info","mousedown",this.onMousedown),this.bind(".xg-spot-info","mouseup",this.onMouseup);var i=this.hook("previewClick",(function(){}));this.handlerPreviewClick=function(t){t.stopPropagation(),i(parseInt(1e3*e._state.now,10)/1e3,t)},this.bind(".xg-spot-content","mouseup",this.handlerPreviewClick)}}},{key:"onProgressMove",value:function(e,t){!this.config.disable&&this.player.duration&&this.updatePosition(e.offset,e.width,e.currentTime,e.e)}},{key:"onProgressDragStart",value:function(e){!this.config.disable&&this.player.duration&&(this.isDrag=!0,this.videoPreview&&W.addClass(this.videoPreview,"show"))}},{key:"onProgressDragEnd",value:function(e){!this.config.disable&&this.player.duration&&(this.isDrag=!1,this.videoPreview&&W.removeClass(this.videoPreview,"show"))}},{key:"onProgressClick",value:function(e,t){this.config.disable||W.hasClass(t.target,"xgplayer-spot")&&(t.stopPropagation(),t.preventDefault(),["time","id","text"].map((function(i){e[i]=t.target.getAttribute("data-".concat(i))})),e.time&&(e.time=Number(e.time)),this.handlerSpotClick(t,e))}},{key:"updateLinePos",value:function(e,t){var i=this.root,n=this.previewLine,o=this.player,r=this.config,s="flex"===o.controls.mode,a=i.getBoundingClientRect().width;if(a||!this._hasThumnail){var l,c=e-(a=this._hasThumnail&&a<r.width?r.width:a)/2;c<0&&!s?(c=0,l=e-a/2):c>t-a&&!s?(l=c-(t-a),c=t-a):l=0,void 0!==l&&(n.style.transform="translateX(".concat(l.toFixed(2),"px)")),i.style.transform="translateX(".concat(c.toFixed(2),"px) translateZ(0)")}}},{key:"updateTimeText",value:function(e){var t=this.timeText,i=this.timePoint;t.innerHTML=e,!this.thumbnail&&(i.innerHTML=e)}},{key:"updatePosition",value:function(e,t,i,n){var o=this.root,r=this.config,s=this._state;if(o){s.now=i,this.transformTimeHook(i);var a=this.timeStr;n&&n.target&&W.hasClass(n.target,"xgplayer-spot")?(this.showTips(n.target.getAttribute("data-text"),!1,a),this.focusDot(n.target),s.f=!0,r.isFocusDots&&s.f&&(s.now=parseInt(n.target.getAttribute("data-time"),10))):r.defaultText?(s.f=!1,this.showTips(r.defaultText,!0,a)):(s.f=!1,this.hideTips("")),this.updateTimeText(a),this.updateThumbnails(s.now),this.updateLinePos(e,t)}}},{key:"setTimeContent",value:function(e){this.timeStr=e}},{key:"updateThumbnails",value:function(e){var t=this.player,i=this.videoPreview,n=this.config,o=t.plugins.thumbnail;if(o&&o.usable){this.thumbnail&&o.update(this.thumbnail,e,n.width,n.height);var r=i&&i.getBoundingClientRect();this.videothumbnail&&o.update(this.videothumbnail,e,r.width,r.height)}}},{key:"registerThumbnail",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if("mobile"!==q.device){var t=this.player,i=this.config,n=t.getPlugin("thumbnail");if(n&&n.setConfig(e),n&&n.usable&&i.isShowThumbnail){W.removeClass(this.root,"short-line no-thumbnail"),"short"===i.mode&&W.addClass(this.root,"short-line"),this._hasThumnail=!0;var o=this.find(".xg-spot-thumbnail");this.thumbnail=n.createThumbnail(o,"progress-thumbnail"),i.isShowCoverPreview&&(this.videoPreview=W.createDom("xg-video-preview","",{},"xgvideo-preview"),t.root.appendChild(this.videoPreview),this.videothumbnail=n.createThumbnail(this.videoPreview,"xgvideo-thumbnail")),this.updateThumbnails(0)}else W.addClass(this.root,"short-line no-thumbnail")}}},{key:"calcuPosition",value:function(e,t){var i=this.player.plugins.progress,n=this.player,o=i.root.getBoundingClientRect().width,r=n.duration/o*6;return e+t>n.duration&&(t=n.duration-e),n.duration,n.duration,{left:e/n.duration*100,width:t/n.duration*100,isMini:t<r}}},{key:"showDot",value:function(e){var t=this.findDot(e);if(t){var i=this.root.getBoundingClientRect().width,n=t.time/this.player.duration*i;this.updatePosition(n,i,t.time)}}},{key:"focusDot",value:function(e,t){e&&(t||(t=e.getAttribute("data-id")),W.addClass(e,"active"),this._activeDotId=t)}},{key:"blurDot",value:function(e){if(!e){var t=this._activeDotId;e=this.getDotDom(t)}e&&(W.removeClass(e,"active"),this._activeDotId=null)}},{key:"showTips",value:function(e,t){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";W.addClass(this.root,"no-timepoint"),e&&(W.addClass(this.find(".xg-spot-content"),"show-text"),t&&"production"===this.config.mode?(W.addClass(this.root,"product"),this.tipText.textContent=e):(W.removeClass(this.root,"product"),this.tipText.textContent=this._hasThumnail?e:"".concat(i," ").concat(e)))}},{key:"hideTips",value:function(){W.removeClass(this.root,"no-timepoint"),this.tipText.textContent="",W.removeClass(this.find(".xg-spot-content"),"show-text"),W.removeClass(this.root,"product")}},{key:"hide",value:function(){W.addClass(this.root,"hide")}},{key:"show",value:function(e){W.removeClass(this.root,"hide")}},{key:"enable",value:function(){var e=this.config,t=this.playerConfig;this.config.disable=!1,this.show(),!this.thumbnail&&e.isShowThumbnail&&this.registerThumbnail(t.thumbnail||{})}},{key:"disable",value:function(){this.config.disable=!0,this.hide()}},{key:"destroy",value:function(){var e=this,t=this.player.plugins.progress;t&&Object.keys(eo).map((function(i){t.removeCallBack(i,e[eo[i]])})),this.videothumbnail=null,this.thumbnail=null,this.videoPreview&&this.player.root.removeChild(this.videoPreview),this.unbind(".xg-spot-info","mousemove",this.onMousemove),this.unbind(".xg-spot-info","mousedown",this.onMousedown),this.unbind(".xg-spot-info","mouseup",this.onMouseup),this.unbind(".xg-spot-content","mouseup",this.handlerPreviewClick)}},{key:"render",value:function(){return"mobile"===q.device||"mobile"===this.playerConfig.isMobileSimulateMode?"":'<div class="xg-spot-info hide '.concat("short"===this.config.mode?"short-line":"",'">\n      <div class="xg-spot-content">\n        <div class="xg-spot-thumbnail">\n          <span class="xg-spot-time"></span>\n        </div>\n        <div class="xg-spot-text"><span class="spot-inner-text"></span></div>\n      </div>\n      <div class="xgplayer-progress-point">00:00</div>\n      <div class="xg-spot-ext-text"></div>\n      <div class="xg-spot-line"></div>\n    </div>')}}],[{key:"pluginName",get:function(){return"progresspreview"}},{key:"defaultConfig",get:function(){return{index:1,miniWidth:6,ispots:[],defaultText:"",isFocusDots:!0,isHideThumbnailHover:!0,isShowThumbnail:!0,isShowCoverPreview:!1,mode:"",disable:!1,width:160,height:90}}}]),t}(),io=function(){T(t,Et);var e=I(t);function t(i){var n;return C(this,t),(n=e.call(this,i)).ratio=1,n.interval=null,n._preloadMark={},n}return _(t,[{key:"afterCreate",value:function(){var e=this;this.usable&&this.initThumbnail(),this.on([ue],(function(){var t=e.config,i=t.pic_num,n=t.interval;e.usable&&(e.interval=n>0?n:Math.round(1e3*e.player.duration/i)/1e3)}))}},{key:"setConfig",value:function(e){var t=this;if(e){var i=Object.keys(e);i.length<1||(i.forEach((function(i){t.config[i]=e[i]})),this.usable&&this.initThumbnail())}}},{key:"usable",get:function(){var e=this.config,t=e.urls,i=e.pic_num;return t&&t.length>0&&i>0}},{key:"initThumbnail",value:function(){var e=this.config,t=e.width,i=e.height,n=e.pic_num,o=e.interval;this.ratio=t/i*100,this.interval=o||Math.round(this.player.duration/n),this._preloadMark={}}},{key:"getUrlByIndex",value:function(e){return e>=0&&e<this.config.urls.length?this.config.urls[e]:""}},{key:"preload",value:function(e){var t=this;if(!this._preloadMark[e]){var i=this.config.urls,n=i.length,o=[];e>0&&o.push(e-1),o.push(e),e>0&&e<n-1&&o.push(e+1),o.map((function(e){!t._preloadMark[e]&&e>=0&&e<n&&(t._preloadMark[e]=1,W.preloadImg(i[e],(function(){t._preloadMark[e]=2})))}))}}},{key:"getPosition",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,n=this.config,o=n.pic_num,r=n.row,s=n.col,a=n.width,l=n.height;this.interval=Math.round(this.player.duration/o);var c=Math.ceil(e/this.interval),u=(c=c>o?o:c)<r*s?0:Math.ceil(c/(r*s))-1,h=c-u*(s*r),d=h>0?Math.ceil(h/s)-1:0,f=h>0?h-d*s-1:0,p=0,g=0;t&&i?t/i<a/l?p=(g=i)*(a/l):g=(p=t)/(a/l):i?t||(p=(g=i||l)*(a/l)):g=(p=t||a)/(a/l);var v=this.getUrlByIndex(u);return{urlIndex:u,rowIndex:d,colIndex:f,url:v,height:g,width:p,style:{backgroundImage:"url(".concat(v,")"),backgroundSize:"".concat(p*s,"px auto"),backgroundPosition:"-".concat(f*p,"px -").concat(d*g,"px"),width:"".concat(p,"px"),height:"".concat(g,"px")}}}},{key:"update",value:function(e,t){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:"",r=this.config,s=r.pic_num,a=r.urls;if(!(s<=0)&&a&&0!==a.length){var l=this.getPosition(t,i,n);this.preload(l.urlIndex),Object.keys(l.style).map((function(t){e.style[t]=l.style[t]})),Object.keys(o).map((function(t){e.style[t]=o[t]}))}}},{key:"changeConfig",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.setConfig(e)}},{key:"createThumbnail",value:function(e,t){var i=W.createDom("xg-thumbnail","",{},"thumbnail ".concat(t));return e&&e.appendChild(i),i}}],[{key:"pluginName",get:function(){return"thumbnail"}},{key:"defaultConfig",get:function(){return{isShow:!1,urls:[],pic_num:0,col:0,row:0,height:90,width:160,scale:1,className:"",hidePortrait:!1}}}]),t}();function no(e){return e?"background:".concat(e,";"):""}var oo=function(){T(t,Et);var e=I(t);function t(){var i;C(this,t);for(var n=arguments.length,o=new Array(n),r=0;r<n;r++)o[r]=arguments[r];return w(E(i=e.call.apply(e,[this].concat(o))),"onTimeupdate",(function(){var e=i.player.ended,t=E(i).offsetDuration,n=i.currentTime;n=W.adjustTimeByDuration(n,t,e),i.update({played:n},t)})),i}return _(t,[{key:"offsetDuration",get:function(){return this.playerConfig.customDuration||this.player.offsetDuration||this.player.duration}},{key:"currentTime",get:function(){var e=this.player,t=e.offsetCurrentTime,i=e.currentTime;return t>=0?t:i}},{key:"afterCreate",value:function(){var e=this;this.root&&(this.on(ae,this.onTimeupdate),this.on(ve,(function(){e.reset()})))}},{key:"reset",value:function(){this.update({played:0,cached:0},0)}},{key:"update",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{cached:0,played:0},t=arguments.length>1?arguments[1]:void 0;t&&this.root&&(e.cached&&(this.find("xg-mini-progress-cache").style.width="".concat(e.cached/t*100,"%")),e.played&&(this.find("xg-mini-progress-played").style.width="".concat(e.played/t*100,"%")))}},{key:"render",value:function(){var e=this.playerConfig,t=e.commonStyle;if(e.miniprogress){var i=this.config,n=i.mode,o=i.height,r={cached:no(t.cachedColor),played:no(t.playedColor),progress:no(t.progressColor),height:o>0&&2!==o?"height: ".concat(o,"px;"):""};return'<xg-mini-progress class="xg-mini-progress '.concat("show"===n?"xg-mini-progress-show":"",'" style="').concat(r.progress," ").concat(r.height,'">\n    <xg-mini-progress-cache class="xg-mini-progress-cache" style="').concat(r.cached,'"></xg-mini-progress-cache>\n    <xg-mini-progress-played class="xg-mini-progress-played" style="').concat(r.played,'"></xg-mini-progress-played>\n    </xg-mini-progress>')}}}],[{key:"pluginName",get:function(){return"MiniProgress"}},{key:"defaultConfig",get:function(){return{mode:"auto",height:2}}}]),t}(),ro="realtime",so="firstframe",ao="poster";var lo=null,co=function(){T(t,Et);var e=I(t);function t(){var i;C(this,t);for(var n=arguments.length,o=new Array(n),r=0;r<n;r++)o[r]=arguments[r];return w(E(i=e.call.apply(e,[this].concat(o))),"onLoadedData",(function(e){i.player&&(i._frameCount=i.config.startFrameCount,i.stop(),i.renderOnTimeupdate(e),i.off(ae,i.renderOnTimeupdate),i.on(ae,i.renderOnTimeupdate))})),w(E(i),"onVisibilitychange",(function(e){"visible"===document.visibilityState?i._checkIfCanStart()&&i.start():"hidden"===document.visibilityState&&i.stop()})),w(E(i),"renderOnTimeupdate",(function(e){if(i._frameCount>0)i.renderOnce(),i._frameCount--;else{i._isLoaded=!0,i.off(ae,i.renderOnTimeupdate);var t=i.config.startInterval;!i.player.paused&&i._checkIfCanStart()&&i.start(0,t)}})),w(E(i),"start",(function(e,t){var n=i.player.video,o=function(){try{return parseInt(window.performance.now(),10)}catch(e){return(new Date).getTime()}}(),r=i.checkVideoIsSupport(n);r&&i.canvasCtx&&(t||(t=i.interval),i.stop(),n.videoWidth&&n.videoHeight&&(i.videoPI=n.videoHeight>0?parseInt(n.videoWidth/n.videoHeight*100,10):0,(i.config.mode===ro||o-i.preTime>=t)&&(n&&n.videoWidth&&i.update(r,i.videoPI),i.preTime=o)),i.frameId="timer"===i._loopType?W.setTimeout(E(i),i.start,t):W.requestAnimationFrame(i.start))})),w(E(i),"stop",(function(){i.frameId&&("timer"===i._loopType?W.clearTimeout(E(i),i.frameId):W.cancelAnimationFrame(i.frameId),i.frameId=null)})),i}return _(t,[{key:"afterCreate",value:function(){var e=this;!0===this.playerConfig.dynamicBg&&(this.config.disable=!1),t.isSupport||(this.config.disable=!0);var i=this.config,n=i.disable,o=i.mode,r=i.frameRate;n||(this._pos={width:0,height:0,rwidth:0,rheight:0,x:0,y:0,pi:0},this.isStart=!1,this._isLoaded=!1,this.videoPI=0,this.preTime=0,this.interval=parseInt(1e3/r,10),this.canvas=null,this.canvasCtx=null,this._frameCount=0,this._loopType=this.config.mode!==ro&&this.interval>=1e3?"timer":"animation",this.once(we,(function(){e.player&&(e.init(),e.renderByPoster(),e.player.paused||e.start())})),o!==ao&&(o!==so&&(this.on(ve,(function(){e.stop()})),this.on(ee,(function(){var t=e.config.startInterval;e._checkIfCanStart()&&e.start(0,t)})),this.on(ne,(function(){e.stop()}))),this.on(de,this.onLoadedData),this.on(ge,(function(){e._isLoaded=!1,e.stop()})),document.addEventListener("visibilitychange",this.onVisibilitychange)))}},{key:"setConfig",value:function(e){var t=this;Object.keys(e).forEach((function(i){"root"===i&&e[i]!==t.config[i]?t.reRender(e[i]):"frameRate"===i?t.interval=parseInt(1e3/e[i],10):"disable"===i&&e[i]&&t.stop(),t.config[i]=e[i]}))}},{key:"init",value:function(e){var i=this.player,n=this.config;this.canvasFilter=t.supportCanvasFilter();try{var o=e||n.root;o||(o=n.isInnerRender&&i.innerContainer||i.root),o.insertAdjacentHTML("afterbegin",'<div class="xgplayer-dynamic-bg" data-index="'.concat(n.index,'"><canvas>\n        </canvas><xgmask></xgmask></div>')),this.root=o.children[0],this.canvas=this.find("canvas"),this.canvasFilter||(this.canvas.style.filter=n.filter,this.canvas.style.webkitFilter=n.filter),this.mask=this.find("xgmask"),n.addMask&&(this.mask.style.background=n.maskBg),this.canvasCtx=this.canvas.getContext("2d")}catch(r){j.logError("plugin:DynamicBg",r)}}},{key:"reRender",value:function(e){if(this.config.disable||this.root){this.stop();var t=this.root?this.root.parentElement:null;if(t!==e&&t.removeChild(this.root),e){this.init(e),this.renderOnce();var i=this.config.startInterval;this._checkIfCanStart()&&this.start(0,i)}else this.root=null}}},{key:"checkVideoIsSupport",value:function(e){if(!e)return null;var t=e&&e instanceof window.HTMLVideoElement?e:e.canvas?e.canvas:e.flyVideo?e.flyVideo:null;if(t&&("safari"!==q.browser||!W.isMSE(t)))return t;var i=t?t.tagName.toLowerCase():"";return"canvas"===i||"img"===i?t:null}},{key:"renderByPoster",value:function(){var e=this.playerConfig.poster;if(e){var t="String"===W.typeOf(e)?e:"String"===W.typeOf(e.poster)?e.poster:null;this.updateImg(t)}}},{key:"_checkIfCanStart",value:function(){var e=this.config.mode;return this._isLoaded&&!this.player.paused&&e!==so&&e!==ao}},{key:"renderOnce",value:function(){var e=this.player.video;if(e.videoWidth&&e.videoHeight){this.videoPI=parseInt(e.videoWidth/e.videoHeight*100,10);var t=this.checkVideoIsSupport(e);t&&this.update(t,this.videoPI)}}},{key:"updateImg",value:function(e){var t=this;if(e){var i=this.canvas.getBoundingClientRect(),n=i.width,o=i.height,r=new window.Image;r.onload=function(){if(t.canvas&&!t.frameId&&!t.isStart){t.canvas.height=o,t.canvas.width=n;var e=parseInt(n/o*100,10);t.update(r,e),r=null}},r.src=e}}},{key:"update",value:function(e,t){if(this.canvas&&this.canvasCtx&&t)try{var i=this._pos,n=this.config,o=this.canvas.getBoundingClientRect(),r=o.width,s=o.height;if(r!==i.width||s!==i.height||i.pi!==t){var a=parseInt(r/s*100,10);i.pi=t,i.width!==r&&(i.width=this.canvas.width=r),i.height!==s&&(i.height=this.canvas.height=s);var l=s,c=r;a<t?c=parseInt(s*t/100,10):a>t&&(l=parseInt(100*r/t,10)),i.rwidth=c*n.multiple,i.rheight=l*n.multiple,i.x=(r-i.rwidth)/2,i.y=(s-i.rheight)/2}this.canvasFilter&&(this.canvasCtx.filter=n.filter),this.canvasCtx.drawImage(e,i.x,i.y,i.rwidth,i.rheight)}catch(u){j.logError("plugin:DynamicBg",u)}}},{key:"destroy",value:function(){this.stop(),document.removeEventListener("visibilitychange",this.onVisibilitychange),this.canvasCtx=null,this.canvas=null}},{key:"render",value:function(){return""}}],[{key:"pluginName",get:function(){return"dynamicBg"}},{key:"defaultConfig",get:function(){return{isInnerRender:!1,disable:!0,index:-1,mode:"framerate",frameRate:10,filter:"blur(50px)",startFrameCount:2,startInterval:0,addMask:!0,multiple:1.2,maskBg:"rgba(0,0,0,0.7)"}}},{key:"isSupport",get:function(){return"boolean"==typeof lo?lo:lo=function(){try{return!!document.createElement("canvas").getContext}catch(e){return!1}}()}},{key:"supportCanvasFilter",value:function(){return!("safari"===q.browser||"firefox"===q.browser)}}]),t}(),uo="info",ho=Xe,fo=function(){T(t,kt);var e=I(t);function t(){var i;C(this,t);for(var n=arguments.length,o=new Array(n),r=0;r<n;r++)o[r]=arguments[r];return w(E(i=e.call.apply(e,[this].concat(o))),"_recordUserActions",(function(e){var t=i._getTime(),n=Object.assign({},e,{msg:e.msg||e.action});i._stats[uo].push(m(m({type:"userAction"},t),{},{payload:n}))})),w(E(i),"_onReset",(function(){i.reset()})),w(E(i),"_recordInfo",(function(e){i.info(e)})),w(E(i),"_downloadStats",(function(){var e=i.getStats(),t=new Blob([JSON.stringify(e)],{type:"application/json"}),n=window.URL.createObjectURL(t),o=document.createElement("a");o.style.display="none",o.href=n,o.download="player.txt",o.disabled=!1,o.click()})),i}return _(t,[{key:"_getTime",value:function(){return{timestamp:Date.now(),timeFormat:(new Date).toISOString()}}},{key:"afterCreate",value:function(){this.reset(),this.on(We,this._recordUserActions),this.on(ho.STATS_INFO,this._recordInfo),this.on(ho.STATS_DOWNLOAD,this._downloadStats),this.on(ho.STATS_RESET,this._onReset)}},{key:"destroy",value:function(){this.offAll()}},{key:"downloadStats",value:function(){this._downloadStats()}},{key:"info",value:function(e){e.profile?this._infoProfile(e):this._info(e)}},{key:"_info",value:function(e){var t=this._getTime();this._stats[uo].push(m(m({},t),{},{payload:e}))}},{key:"_infoProfile",value:function(e){if(e&&e.startMs){var t=m({cat:"function",dur:Date.now()-e.startMs,name:e.name||e.msg,ph:"X",pid:0,tid:0,ts:e.startMs,profile:!0},e);this._info(t)}}},{key:"reset",value:function(){var e;this._stats=(w(e={},uo,[]),w(e,"media",{}),e)}},{key:"getStats",value:function(){for(var e=this.player.media,t=[],i=0;i<e.buffered.length;i++)t.push({start:e.buffered.start(i),end:e.buffered.end(i)});var n={currentTime:e.currentTime,readyState:e.readyState,buffered:t,paused:e.paused,ended:e.ended};return this._stats.media=n,{raw:this._stats,timestat:this._getTimeStats(),profile:this._getProfile()}}},{key:"_getTimeStats",value:function(){return this._stats[uo].map((function(e){var t=e.payload.data,i="";try{t instanceof Error?i=t.msg:void 0!==t&&(i=JSON.stringify(t))}catch(n){}return"[".concat(e.timeFormat,"] : ").concat(e.payload.msg," ").concat(i," ")}))}},{key:"_getProfile",value:function(){var e={traceEvents:[]};return this._stats[uo].forEach((function(t){t.payload.profile&&e.traceEvents.push(t.payload)})),e}}],[{key:"pluginName",get:function(){return"stats"}},{key:"defaultConfig",get:function(){return{}}}]),t}(),po=function(){T(t,Et);var e=I(t);function t(){var i;C(this,t);for(var n=arguments.length,o=new Array(n),r=0;r<n;r++)o[r]=arguments[r];return w(E(i=e.call.apply(e,[this].concat(o))),"onGapJump",(function(){var e=E(i),n=e.player,o=e.config;if(n.media.readyState!==HTMLMediaElement.HAVE_NOTHING){if(n.media.seeking){if(!i.seekingEventReceived)return}else i.seekingEventReceived=!1;if(!n.media.paused||0===n.media.currentTime||!i.hasPlayed){var r=n.media.buffered,s=o.smallGapLimit||.5,a=o.gapDetectionThreshold||.3,l=n.media.currentTime,c=i._getIndex(r,l,a);if(null!==c&&0!==c){var u=r.start(c)+.1;if(!(u>n.media.duration)){var h=u-l,d=h<=s;h<t.BROWSER_GAP_TOLERANCE||d&&(!1!==o.useGapJump&&(n.media.currentTime=i.isSafari?u+.1:u),i.player&&i.player.emit("detectGap"),.08!==u&&n&&n.emit("log",{type:"oneevent",end_type:"gap",vid:n.config.vid,ext:{video_postion:Math.floor(1e3*u)}}))}}}}})),i}return _(t,[{key:"afterCreate",value:function(){var e=this;!1!==this.config.useGapJump&&(this.hasPlayed=!1,this.seekingEventReceived=!1,this.isSafari=/(Safari|iPhone|iPad|iPod)/.test(navigator.userAgent)&&!/Chrome/.test(navigator.userAgent)&&!/BlackBerry/.test(navigator.platform),this.on(le,this.onGapJump),this.on(ee,(function(){e.hasPlayed=!0})),this.on(re,(function(){e.seekingEventReceived=!0})))}},{key:"_getIndex",value:function(e,t,i){if(!e||!e.length)return null;if(1===e.length&&e.end(0)-e.start(0)<1e-6)return null;for(var n=this._getBuffered(e),o=null,r=0;r<n.length;r++){if(n[r].start>t&&(0===r||n[r-1].end-t<=i)){o=r;break}}return o}},{key:"_getBuffered",value:function(e){if(!e)return[];for(var t=[],i=0;i<e.length;i++)t.push({start:e.start(i),end:e.end(i)});return t}}],[{key:"pluginName",get:function(){return"gapJump"}},{key:"defaultConfig",get:function(){return{useGapJump:!1,smallGapLimit:.5,gapDetectionThreshold:.3}}}]),t}();po.BROWSER_GAP_TOLERANCE=.001;var go=function(){T(t,Et);var e=I(t);function t(){var i;C(this,t);for(var n=arguments.length,o=new Array(n),r=0;r<n;r++)o[r]=arguments[r];return w(E(i=e.call.apply(e,[this].concat(o))),"onWaiting",(function(){var e=E(i).config;i.jumpCnt>e.jumpCntMax||i.timer||!1===e.useWaitingTimeoutJump||(i.timer=setTimeout(i.onJump,1e3*e.waitingTime))})),w(E(i),"onJump",(function(){var e=E(i),t=e.player,n=e.config;if(clearTimeout(i.timer),i.timer=null,!(i.jumpCnt>n.jumpCntMax||!1===n.useWaitingTimeoutJump||t.media.paused&&0!==t.media.currentTime&&i.hasPlayed)){i.jumpSize=n.jumpSize*(i.jumpCnt+1),i.jumpCnt===n.jumpSize&&i.jumpSize<6&&(i.jumpSize=6);var o=t.currentTime+i.jumpSize;o>t.media.duration||(i.jumpCnt++,t.currentTime=o)}})),i}return _(t,[{key:"afterCreate",value:function(){var e=this,t=this.config,i=t.useWaitingTimeoutJump,n=t.jumpSize;!1!==i&&(this.hasPlayed=!1,this.jumpCnt=0,this.timer=null,this.jumpSize=n,this.on(le,this.onWaiting),this.on([te,ce],(function(){clearTimeout(e.timer),e.timer=null,e.jumpSize=e.config.jumpSize})),this.on(ee,(function(){e.hasPlayed=!0})))}}],[{key:"pluginName",get:function(){return"waitingTimeoutJump"}},{key:"defaultConfig",get:function(){return{useWaitingTimeoutJump:!1,waitingTime:15,jumpSize:2,jumpCntMax:4}}}]),t}(),vo="cdn",yo=["cdn"],mo=function(){T(t,Et);var e=I(t);function t(){var i;C(this,t);for(var n=arguments.length,o=new Array(n),r=0;r<n;r++)o[r]=arguments[r];return w(E(i=e.call.apply(e,[this].concat(o))),"getSpeed",(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:vo;if(!i.speedListCache||!i.speedListCache[e])return 0;if(i.speedListCache[e].length<=0)return 0;var t=0;return i.speedListCache[e].map((function(e){t+=e})),Math.floor(t/i.speedListCache[e].length)})),w(E(i),"startTimer",(function(){W.isMSE(i.player.video)||(i.initSpeedList(),i.cnt=0,i.timer=setTimeout(i.testSpeed,i.config.testTimeStep))})),w(E(i),"initSpeedList",(function(){i.speedListCache={},yo.forEach((function(e){i.speedListCache[e]=[]}))})),w(E(i),"_onRealSpeedChange",(function(e){e.speed&&i.appendList(e.speed,e.type||vo)})),w(E(i),"testSpeed",(function(){if(clearTimeout(i.timer),i.timer=null,i.player&&i.config.openSpeed){var e=i.config,t=e.url,n=e.loadSize,o=e.testCnt,r=e.testTimeStep,s=t+(t.indexOf("?")<0?"?testst=":"&testst=")+Date.now();if(!(i.cnt>=o)){i.cnt++;try{var a=(new Date).getTime(),l=null,c=new XMLHttpRequest;i.xhr=c,c.open("GET",s);var u={},h=Math.floor(10*Math.random());u.Range="bytes="+h+"-"+(n+h),u&&Object.keys(u).forEach((function(e){c.setRequestHeader(e,u[e])})),c.onreadystatechange=function(){if(4===c.readyState){i.xhr=null,l=(new Date).getTime();var e=c.getResponseHeader("Content-Length")/1024*8,t=Math.round(1e3*e/(l-a));i.appendList(t),i.timer=setTimeout(i.testSpeed,r)}},c.send()}catch(d){}}}})),w(E(i),"appendList",(function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:vo;if(i.speedListCache&&i.speedListCache[t]){var n=i.config.saveSpeedMax;i.speedListCache[t].length>=n&&i.speedListCache[t].shift(),i.speedListCache[t].push(e);var o=E(i).player;o&&(t===vo?o.realTimeSpeed=e:o[i.getSpeedName("realTime",t)]=e),i.updateSpeed(t)}})),w(E(i),"updateSpeed",(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:vo,t=i.getSpeed(e),n=E(i).player;if(n)if(e===vo)n.avgSpeed&&t===n.avgSpeed||(n.avgSpeed=t,n.emit(Ee,{speed:t,realTimeSpeed:n.realTimeSpeed}));else{var o=i.getSpeedName("avg",e);n[o]&&t===n[o]||(n[o]=t,n.emit(Ee,{speed:t,realTimeSpeed:n.realTimeSpeed}))}})),i}return _(t,[{key:"afterCreate",value:function(){var e=this.config,t=e.openSpeed,i=e.addSpeedTypeList;(null==i?void 0:i.length)>0&&yo.push.apply(yo,A(i)),this.initSpeedList(),this.on("real_time_speed",this._onRealSpeedChange),this.timer=null,this.cnt=0,this.xhr=null,t&&this.on([de,Te],this.startTimer)}},{key:"getSpeedName",value:function(e,t){return e+"Speed"+t.toUpperCase()}},{key:"openSpeed",get:function(){return this.config.openSpeed},set:function(e){if(this.config.openSpeed=e,!e&&this.timer)return clearTimeout(this.timer),void(this.timer=null);if(this.config.openSpeed){if(this.timer)return;this.timer=setTimeout(this.testSpeed,this.config.testTimeStep)}}},{key:"destroy",value:function(){var e=this;this.off("real_time_speed",this._onRealSpeedChange),this.off([de,Te],this.startTimer),yo.forEach((function(t){e.speedListCache&&e.speedListCache[t]&&(e.speedListCache[t]=[])})),this.speedListCache&&(this.speedListCache={}),clearTimeout(this.timer),this.timer=null,this.xhr&&4!==this.xhr.readyState&&(this.xhr.cancel&&this.xhr.cancel(),this.xhr=null)}}],[{key:"pluginName",get:function(){return"testspeed"}},{key:"defaultConfig",get:function(){return{openSpeed:!1,testCnt:3,loadSize:204800,testTimeStep:3e3,url:"",saveSpeedMax:5,addSpeedTypeList:[]}}}]),t}(),ko=function(){T(t,Et);var e=I(t);function t(){return C(this,t),e.apply(this,arguments)}return _(t,[{key:"afterCreate",value:function(){var e=this,t=this.player,i=this.config,n=t.media||t.video;(this.timer=null,this._lastDecodedFrames=0,this._currentStuckCount=0,this._lastCheckPoint=null,this._payload=[],i.disabled)||n.getVideoPlaybackQuality&&(this.on(ee,(function(){e._startTick()})),this.on(ne,(function(){e._stopTick()})),this.on(ie,(function(){e._stopTick()})),this.on(ve,(function(){e._stopTick()})))}},{key:"_startTick",value:function(){var e=this;this._stopTick(),this._timer=setTimeout((function(){e._checkDecodeFPS(),e._startTick()}),this.config.tick)}},{key:"_stopTick",value:function(){clearTimeout(this._timer),this._timer=null}},{key:"_checkBuffer",value:function(e,t){for(var i=!1,n=[],o=0;o<t.length;o++){var r=t.start(o),s=t.end(o);if(n.push({start:r,end:s}),r<=e&&e<=s-1){i=!0;break}}return{enoughBuffer:i,buffers:n}}},{key:"_checkStuck",value:function(e,t,i,n){var o=this.player.media||this.player.video,r=document.hidden,s=o.paused,a=o.readyState,l=o.currentTime,c=o.buffered;if(!(r||s||a<4)){var u=this._checkBuffer(l,c),h=u.enoughBuffer,d=u.buffers;h&&(e<=this.config.reportFrame?(this._currentStuckCount++,this._payload.push({currentTime:l,buffers:d,curDecodedFrames:e,totalVideoFrames:t,droppedVideoFrames:i,checkInterval:n}),this._currentStuckCount>=this.config.stuckCount&&(this.emit(qe,this._payload),this._reset())):this._reset())}}},{key:"_reset",value:function(){this._payload=[],this._currentStuckCount=0}},{key:"_checkDecodeFPS",value:function(){var e=this.player.media||this.player.video;if(e){var t=e.getVideoPlaybackQuality(),i=t.totalVideoFrames,n=t.droppedVideoFrames,o=performance.now();if(i&&this._lastCheckPoint){var r=i-this._lastDecodedFrames,s=o-this._lastCheckPoint;this._checkStuck(r,i,n,s)}this._lastDecodedFrames=i,this._lastCheckPoint=o}}},{key:"destroy",value:function(){this._stopTick()}}],[{key:"pluginName",get:function(){return"FpsDetect"}},{key:"defaultConfig",get:function(){return{disabled:!1,tick:1e3,stuckCount:3,reportFrame:0}}}]),t}();Ht.use({LANG:"zh-cn",TEXT:{ERROR_TYPES:{network:{code:1,msg:"视频下载错误"},mse:{code:2,msg:"流追加错误"},parse:{code:3,msg:"解析错误"},format:{code:4,msg:"格式错误"},decoder:{code:5,msg:"解码错误"},runtime:{code:6,msg:"语法错误"},timeout:{code:7,msg:"播放超时"},other:{code:8,msg:"其他错误"}},HAVE_NOTHING:"没有关于音频/视频是否就绪的信息",HAVE_METADATA:"音频/视频的元数据已就绪",HAVE_CURRENT_DATA:"关于当前播放位置的数据是可用的，但没有足够的数据来播放下一帧/毫秒",HAVE_FUTURE_DATA:"当前及至少下一帧的数据是可用的",HAVE_ENOUGH_DATA:"可用数据足以开始播放",NETWORK_EMPTY:"音频/视频尚未初始化",NETWORK_IDLE:"音频/视频是活动的且已选取资源，但并未使用网络",NETWORK_LOADING:"浏览器正在下载数据",NETWORK_NO_SOURCE:"未找到音频/视频来源",MEDIA_ERR_ABORTED:"取回过程被用户中止",MEDIA_ERR_NETWORK:"网络错误",MEDIA_ERR_DECODE:"解码错误",MEDIA_ERR_SRC_NOT_SUPPORTED:"不支持的音频/视频格式",REPLAY:"重播",ERROR:"网络连接似乎出现了问题",PLAY_TIPS:"播放",PAUSE_TIPS:"暂停",PLAYNEXT_TIPS:"下一集",DOWNLOAD_TIPS:"下载",ROTATE_TIPS:"旋转",RELOAD_TIPS:"重新载入",FULLSCREEN_TIPS:"进入全屏",EXITFULLSCREEN_TIPS:"退出全屏",CSSFULLSCREEN_TIPS:"进入样式全屏",EXITCSSFULLSCREEN_TIPS:"退出样式全屏",TEXTTRACK:"字幕",PIP:"画中画",SCREENSHOT:"截图",LIVE:"正在直播",OFF:"关闭",OPEN:"开启",MINI_DRAG:"点击按住可拖动视频",MINISCREEN:"小屏幕",REFRESH_TIPS:"请试试",REFRESH:"刷新",FORWARD:"快进中",LIVE_TIP:"直播"}});var Co=_((function e(t,i){var n,o,r;C(this,e);var s=i&&"mobile"===i.isMobileSimulateMode,a=i.isLive,l=[].concat(a?[]:[gn,sn,oo,to,pn],[ln,fn,bn,Ln,Gn,zn,Rn,Mn,kn,En]),c=[hi,di,yi,tn,mi,qn,Zn,io,Ai];this.plugins=[fo,ci].concat(A(l),c,[po,go]);var u=s?"mobile":q.device;switch(u){case"pc":(n=this.plugins).push.apply(n,[Qi,Ri,Xn,mo,ko]);break;case"mobile":(o=this.plugins).push.apply(o,[Ji]);break;default:(r=this.plugins).push.apply(r,[Qi,Ri,Xn])}(q.os.isIpad||"pc"===u)&&this.plugins.push(co),q.os.isIpad&&this.plugins.push(Ri),this.ignores=[],this.i18n=[]})),bo=function(){T(t,ei);var e=I(t);function t(){return C(this,t),e.apply(this,arguments)}return _(t)}();w(bo,"defaultPreset",Co),w(bo,"Util",W),w(bo,"Sniffer",q),w(bo,"Errors",Q),w(bo,"Events",Ze),w(bo,"Plugin",Et),w(bo,"BasePlugin",kt),w(bo,"I18N",Ht),w(bo,"STATE_CLASS",At),w(bo,"InstManager",qt);const _o=["id"],wo=a({__name:"VideoPlayer",props:{playerId:{},videoUrl:{},posterUrl:{},autoplay:{type:Boolean},volume:{},playbackRates:{},loop:{type:Boolean},muted:{type:Boolean},commonStyle:{}},setup(e){var t,i,n,o,s;const a=e,f=null!=(t=a.autoplay)&&t,p=null!=(i=a.volume)?i:.5,g=null!=(n=a.playbackRates)?n:[.5,.75,1,1.5,2],v=null!=(o=a.loop)&&o,y=null!=(s=a.muted)&&s,m=l(null),k={progressColor:"rgba(255, 255, 255, 0.3)",playedColor:"#00AEED",cachedColor:"rgba(255, 255, 255, 0.6)",sliderBtnStyle:{width:"10px",height:"10px",backgroundColor:"#00AEED"},volumeColor:"#00AEED"};return c((()=>{m.value=new bo({id:a.playerId,lang:"zh",volume:p,autoplay:f,screenShot:!0,url:a.videoUrl,poster:a.posterUrl,fluid:!0,playbackRate:g,loop:v,muted:y,commonStyle:r(r({},k),a.commonStyle)}),m.value.on("play",(()=>{})),m.value.on("pause",(()=>{})),m.value.on("error",(e=>{}))})),u((()=>{m.value&&m.value.destroy()})),(e,t)=>(d(),h("div",{id:e.playerId},null,8,_o))}}),To={class:"page-content"},xo={class:"video-container"},So=v(a({__name:"Video",setup(e){const t=l("//lf3-static.bytednsdoc.com/obj/eden-cn/nupenuvpxnuvo/xgplayer_doc/xgplayer-demo.mp4"),i=l(g);return(e,n)=>{const o=wo;return d(),h("div",To,[f("div",xo,[p(o,{playerId:"my-video-1",videoUrl:t.value,posterUrl:i.value,autoplay:!1,volume:.5,playbackRates:[.5,1,1.5,2]},null,8,["videoUrl","posterUrl"])])])}}}),[["__scopeId","data-v-51428207"]]);export{So as default};
