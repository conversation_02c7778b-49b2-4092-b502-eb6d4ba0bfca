var a=(a,e,t)=>new Promise(((r,n)=>{var l=a=>{try{s(t.next(a))}catch(e){n(e)}},i=a=>{try{s(t.throw(a))}catch(e){n(e)}},s=a=>a.done?r(a.value):Promise.resolve(a.value).then(l,i);s((t=t.apply(a,e)).next())}));import{h as e}from"./index-rNRt1EuS.js";class t{static getBalanceOverview(){return a(this,null,(function*(){return e.get({url:"/api/v1/admin/balance/overview",headers:{"Cache-Control":"no-cache, no-store, must-revalidate",Pragma:"no-cache",Expires:"0"}})}))}static getUserBalanceList(){return a(this,arguments,(function*(a={}){return e.get({url:"/api/v1/admin/balance/users",params:a})}))}static getUserBalanceDetail(t){return a(this,null,(function*(){return e.get({url:`/api/v1/admin/balance/users/${t}`})}))}static getBalanceAnomalies(){return a(this,arguments,(function*(a={}){return e.get({url:"/api/v1/admin/balance/anomalies",params:a})}))}static manualDeposit(t){return a(this,null,(function*(){return e.post({url:"/api/v1/admin/balance/manual-deposit",data:t})}))}static adjustBalance(t){return a(this,null,(function*(){return e.post({url:"/api/v1/admin/balance/adjustment",data:t})}))}static forceRefund(t){return a(this,null,(function*(){return e.post({url:"/api/v1/admin/balance/force-refund",data:t})}))}static batchOperation(t){return a(this,null,(function*(){return e.post({url:"/api/v1/admin/balance/batch-operation",data:t})}))}static getBalanceStatistics(t){return a(this,null,(function*(){return e.get({url:"/api/v1/admin/balance/statistics",params:t})}))}static getTransactionStatistics(t){return a(this,null,(function*(){return e.get({url:"/api/v1/admin/balance/transaction-statistics",params:t})}))}static getAuditLogs(t){return a(this,null,(function*(){return e.get({url:"/api/v1/admin/balance/audit-logs",params:t})}))}static exportUserBalances(t){return a(this,null,(function*(){return e.get({url:"/api/v1/admin/balance/users/export",params:t,responseType:"blob"})}))}static exportTransactions(t){return a(this,null,(function*(){return e.get({url:"/api/v1/admin/balance/transactions/export",params:t,responseType:"blob"})}))}static exportAuditLogs(t){return a(this,null,(function*(){return e.get({url:"/api/v1/admin/balance/audit-logs/export",params:t,responseType:"blob"})}))}}const r=[{label:"正常",value:"active"},{label:"冻结",value:"frozen"},{label:"禁用",value:"disabled"}],n=[{label:"用户充值",value:"user_deposit"},{label:"管理员充值",value:"admin_deposit"},{label:"下单预收",value:"order_pre_charge"},{label:"超重补收费用",value:"overweight_charge"},{label:"订单拦截_补收",value:"order_intercept_charge"},{label:"退回收费",value:"return_charge"},{label:"订单复活重计费",value:"order_revive_recharge"},{label:"订单取消_退款",value:"order_cancel_refund"},{label:"超轻退款",value:"underweight_refund"},{label:"调账_多退少补",value:"balance_adjustment"}];export{r as B,n as T,t as a};
