var e=(e,a,t)=>new Promise(((r,l)=>{var i=e=>{try{n(t.next(e))}catch(a){l(a)}},u=e=>{try{n(t.throw(e))}catch(a){l(a)}},n=e=>e.done?r(e.value):Promise.resolve(e.value).then(i,u);n((t=t.apply(e,a)).next())}));import{h as a}from"./index-rNRt1EuS.js";class t{static getAdminOrderList(t){return e(this,null,(function*(){return a.get({url:"/api/v1/admin/orders",params:t})}))}static getAdminOrderDetail(t){return e(this,null,(function*(){return(yield a.get({url:`/api/v1/admin/orders/${t}`})).data}))}static updateOrderStatus(t,r){return e(this,null,(function*(){return a.put({url:`/api/v1/admin/orders/${t}/status`,data:r})}))}static batchUpdateOrderStatus(t){return e(this,null,(function*(){return(yield a.put({url:"/api/v1/admin/orders/batch/status",data:t})).data}))}static getOrderStatistics(t){return e(this,null,(function*(){return(yield a.get({url:"/api/v1/admin/orders/statistics",params:t})).data}))}static exportOrders(t){return e(this,null,(function*(){return a.get({url:"/api/v1/admin/orders/export",params:t,responseType:"blob"})}))}static queryProviderShippingFee(t){return e(this,null,(function*(){return a.get({url:`/api/v1/admin/orders/${t}/shipping-fee`})}))}static batchQueryProviderShippingFee(t){return e(this,null,(function*(){return a.post({url:"/api/v1/admin/orders/batch/shipping-fee",data:{order_ids:t}})}))}static batchValidatePrices(t){return e(this,null,(function*(){return a.post({url:"/api/v1/admin/orders/batch/validate-prices",data:t})}))}static syncOrderStatus(t){return e(this,null,(function*(){return a.post({url:`/api/v1/admin/orders/${t}/sync-status`})}))}static batchSyncOrderStatus(t){return e(this,null,(function*(){return a.post({url:"/api/v1/admin/orders/batch/sync-status",data:t})}))}}const r=[{label:"已提交",value:"submitted"},{label:"提交失败",value:"submit_failed"},{label:"面单生成失败",value:"print_failed"},{label:"已分配",value:"assigned"},{label:"等待揽收",value:"awaiting_pickup"},{label:"已揽收",value:"picked_up"},{label:"揽收失败",value:"pickup_failed"},{label:"运输中",value:"in_transit"},{label:"派送中",value:"out_for_delivery"},{label:"已签收",value:"delivered"},{label:"异常签收",value:"delivered_abnormal"},{label:"已计费",value:"billed"},{label:"异常",value:"exception"},{label:"已退回",value:"returned"},{label:"已转寄",value:"forwarded"},{label:"取消中",value:"cancelling"},{label:"已取消",value:"cancelled"},{label:"已作废",value:"voided"},{label:"重量更新",value:"weight_updated"},{label:"订单复活",value:"revived"}],l=[{label:"待计费",value:"pending"},{label:"已计费",value:"calculated"},{label:"已支付",value:"paid"},{label:"已退款",value:"refunded"}];export{l as B,r as O,t as a};
