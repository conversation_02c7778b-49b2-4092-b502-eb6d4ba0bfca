var e=(e,a,l)=>new Promise(((s,t)=>{var d=e=>{try{r(l.next(e))}catch(a){t(a)}},i=e=>{try{r(l.throw(e))}catch(a){t(a)}},r=e=>e.done?s(e.value):Promise.resolve(e.value).then(d,i);r((l=l.apply(e,a)).next())}));import{_ as a}from"./index-rNRt1EuS.js";/* empty css                             *//* empty css               *//* empty css                */import{d as l,r as s,X as t,f as d,c as i,o as r,e as u,a2 as o,b as n,w as c,K as p,u as m,aR as _,H as v,aG as f,aN as g,aV as y,y as b,bl as w,x as h,bk as k,bf as V,aT as z,Z as x,_ as U,C,D as j,ar as T,as as $,ap as B,N as S,aI as A,aJ as P,B as q,aX as D,F as H,A as L,ag as M,an as N,ah as O,aj as F,aH as I,a as K,b1 as R,b2 as X,Y as G,M as J,ai as Y}from"./vendor-CAPBtMef.js";import{U as Z}from"./userManagementApi-BPI8-eRA.js";const E={class:"users-management"},Q={class:"page-header"},W={class:"header-right"},ee={class:"stats-cards"},ae={class:"stat-content"},le={class:"stat-icon total"},se={class:"stat-info"},te={class:"stat-value"},de={class:"stat-content"},ie={class:"stat-icon active"},re={class:"stat-info"},ue={class:"stat-value"},oe={class:"stat-content"},ne={class:"stat-icon new"},ce={class:"stat-info"},pe={class:"stat-value"},me={class:"stat-content"},_e={class:"stat-icon login"},ve={class:"stat-info"},fe={class:"stat-value"},ge={class:"user-info"},ye={class:"username"},be={key:0},we={key:1,class:"text-muted"},he={class:"pagination-wrapper"},ke={key:0,class:"user-detail"},Ve=a(l({__name:"index",setup(a){const l=s(!1),Ve=s(!1),ze=s(!1),xe=s([]),Ue=s({}),Ce=t({keyword:"",status:"all",sort_by:"created_at",sort_order:"desc",include_deleted:!1}),je=t({page:1,page_size:20,total:0}),Te=s(!1),$e=s(!1),Be=s(null),Se=s(!1),Ae=s(),Pe=t({username:"",email:"",password:"",is_active:!0}),qe={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:50,message:"用户名长度在 3 到 50 个字符",trigger:"blur"}],email:[{required:!0,message:"请输入邮箱",trigger:"blur"},{type:"email",message:"请输入正确的邮箱格式",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:8,message:"密码长度不能少于8位",trigger:"blur"}]},De=()=>e(this,null,(function*(){try{l.value=!0;const e=yield Z.getUserList({page:je.page,page_size:je.page_size,keyword:Ce.keyword,status:"all"===Ce.status?"":Ce.status,order_by:Ce.sort_by,order:Ce.sort_order,include_deleted:Ce.include_deleted});xe.value=e.items,je.total=e.total,je.page=e.page,je.page_size=e.page_size}catch(e){J.error(e.message||"加载用户列表失败")}finally{l.value=!1}})),He=()=>e(this,null,(function*(){try{Ve.value=!0;const e=yield Z.getUserStatistics();Ue.value=e}catch(e){J.error(e.message||"加载统计数据失败")}finally{Ve.value=!1}})),Le=()=>{je.page=1,De()},Me=()=>{Object.assign(Ce,{keyword:"",status:"all",sort_by:"created_at",sort_order:"desc",include_deleted:!1}),Le()},Ne=e=>{je.page_size=e,je.page=1,De()},Oe=e=>{je.page=e,De()},Fe=()=>{Se.value=!0,Object.assign(Pe,{username:"",email:"",password:"",is_active:!0}),$e.value=!0},Ie=(a,l)=>e(this,null,(function*(){switch(a){case"toggle-status":yield Ke(l);break;case"reset-password":yield Re(l);break;case"delete":yield Xe(l)}})),Ke=a=>e(this,null,(function*(){try{const e=a.is_active?"禁用":"启用";yield Y.confirm(`确定要${e}用户 "${a.username}" 吗？`,`${e}用户`,{type:"warning"});const l={is_active:!a.is_active};yield Z.updateUserStatus(a.id,l),J.success(`${e}成功`),yield De()}catch(e){"cancel"!==e&&J.error(e.message||"操作失败")}})),Re=a=>e(this,null,(function*(){try{const{value:e}=yield Y.prompt(`请输入用户 "${a.username}" 的新密码`,"重置密码",{confirmButtonText:"确定",cancelButtonText:"取消",inputType:"password",inputValidator:e=>!(!e||e.length<8)||"密码长度不能少于8位"}),l={new_password:e};yield Z.resetUserPassword(a.id,l),J.success("密码重置成功")}catch(e){"cancel"!==e&&J.error(e.message||"重置密码失败")}})),Xe=a=>e(this,null,(function*(){try{yield Y.confirm(`确定要删除用户 "${a.username}" 吗？删除后可以恢复。`,"删除用户",{type:"warning",confirmButtonText:"确定删除",cancelButtonText:"取消"}),yield Z.deleteUser(a.id),J.success("删除成功"),yield De(),yield He()}catch(e){"cancel"!==e&&J.error(e.message||"删除失败")}})),Ge=a=>e(this,null,(function*(){try{yield Y.confirm(`确定要永久删除用户 "${a.username}" 吗？此操作不可恢复！`,"永久删除用户",{type:"error",confirmButtonText:"确定永久删除",cancelButtonText:"取消",dangerouslyUseHTMLString:!0,message:`<p>用户: <strong>${a.username}</strong></p><p style="color: red;">⚠️ 警告：此操作将永久删除用户及其所有相关数据，无法恢复！</p>`}),yield Z.forceDeleteUser(a.id),J.success("永久删除成功"),yield De(),yield He()}catch(e){"cancel"!==e&&J.error(e.message||"永久删除失败")}})),Je=()=>e(this,null,(function*(){try{if(!(yield Ae.value.validate()))return;if(ze.value=!0,Se.value){const e={username:Pe.username,email:Pe.email,password:Pe.password,is_active:Pe.is_active};yield Z.createUser(e),J.success("用户创建成功"),yield De(),yield He()}else{const e={username:Pe.username,email:Pe.email,is_active:Pe.is_active};yield Z.updateUser(Be.value.id,e),J.success("用户更新成功"),yield De()}$e.value=!1}catch(e){J.error(e.message||(Se.value?"创建用户失败":"更新用户失败"))}finally{ze.value=!1}})),Ye=e=>new Date(e).toLocaleString("zh-CN");return d((()=>{De(),He()})),(e,a)=>{const s=v,t=b,d=y,J=g,Y=z,Z=C,De=U,He=$,Ke=T,Re=B,Xe=x,Ze=q,Ee=P,Qe=D,We=F,ea=O,aa=M,la=A,sa=I,ta=X,da=R,ia=G,ra=f;return r(),i("div",E,[u("div",Q,[a[14]||(a[14]=u("div",{class:"header-left"},[u("h1",{class:"page-title"},"用户管理"),u("p",{class:"page-description"},"管理系统中的所有用户账户")],-1)),u("div",W,[n(s,{type:"primary",icon:m(_),onClick:Fe},{default:c((()=>a[13]||(a[13]=[p(" 新增用户 ")]))),_:1},8,["icon"])])]),o((r(),i("div",ee,[n(Y,{gutter:20},{default:c((()=>[n(J,{span:6},{default:c((()=>[n(d,{class:"stat-card"},{default:c((()=>[u("div",ae,[u("div",le,[n(t,{size:"24"},{default:c((()=>[n(m(w))])),_:1})]),u("div",se,[u("div",te,h(Ue.value.total_users||0),1),a[15]||(a[15]=u("div",{class:"stat-label"},"总用户数",-1))])])])),_:1})])),_:1}),n(J,{span:6},{default:c((()=>[n(d,{class:"stat-card"},{default:c((()=>[u("div",de,[u("div",ie,[n(t,{size:"24"},{default:c((()=>[n(m(k))])),_:1})]),u("div",re,[u("div",ue,h(Ue.value.active_users||0),1),a[16]||(a[16]=u("div",{class:"stat-label"},"活跃用户",-1))])])])),_:1})])),_:1}),n(J,{span:6},{default:c((()=>[n(d,{class:"stat-card"},{default:c((()=>[u("div",oe,[u("div",ne,[n(t,{size:"24"},{default:c((()=>[n(m(_))])),_:1})]),u("div",ce,[u("div",pe,h(Ue.value.new_users_today||0),1),a[17]||(a[17]=u("div",{class:"stat-label"},"今日新增",-1))])])])),_:1})])),_:1}),n(J,{span:6},{default:c((()=>[n(d,{class:"stat-card"},{default:c((()=>[u("div",me,[u("div",_e,[n(t,{size:"24"},{default:c((()=>[n(m(V))])),_:1})]),u("div",ve,[u("div",fe,h(Ue.value.login_users_today||0),1),a[18]||(a[18]=u("div",{class:"stat-label"},"今日登录",-1))])])])),_:1})])),_:1})])),_:1})])),[[ra,Ve.value]]),n(d,{class:"search-card"},{default:c((()=>[n(Xe,{model:Ce,inline:!0,class:"search-form"},{default:c((()=>[n(De,{label:"关键词"},{default:c((()=>[n(Z,{modelValue:Ce.keyword,"onUpdate:modelValue":a[0]||(a[0]=e=>Ce.keyword=e),placeholder:"搜索用户名或邮箱",clearable:"",style:{width:"200px"},onKeyup:j(Le,["enter"])},null,8,["modelValue"])])),_:1}),n(De,{label:"状态"},{default:c((()=>[n(Ke,{modelValue:Ce.status,"onUpdate:modelValue":a[1]||(a[1]=e=>Ce.status=e),placeholder:"选择状态",clearable:"",style:{width:"120px"}},{default:c((()=>[n(He,{label:"全部",value:"all"}),n(He,{label:"活跃",value:"active"}),n(He,{label:"禁用",value:"inactive"})])),_:1},8,["modelValue"])])),_:1}),n(De,{label:"排序"},{default:c((()=>[n(Ke,{modelValue:Ce.sort_by,"onUpdate:modelValue":a[2]||(a[2]=e=>Ce.sort_by=e),style:{width:"120px"}},{default:c((()=>[n(He,{label:"创建时间",value:"created_at"}),n(He,{label:"最后登录",value:"last_login"}),n(He,{label:"用户名",value:"username"})])),_:1},8,["modelValue"])])),_:1}),n(De,{label:"显示软删除用户"},{default:c((()=>[n(Re,{modelValue:Ce.include_deleted,"onUpdate:modelValue":a[3]||(a[3]=e=>Ce.include_deleted=e),"active-text":"显示","inactive-text":"隐藏",onChange:Le},null,8,["modelValue"])])),_:1}),n(De,null,{default:c((()=>[n(s,{type:"primary",onClick:Le,loading:l.value},{default:c((()=>a[19]||(a[19]=[p(" 搜索 ")]))),_:1},8,["loading"]),n(s,{onClick:Me},{default:c((()=>a[20]||(a[20]=[p("重置")]))),_:1})])),_:1})])),_:1},8,["model"])])),_:1}),n(d,{class:"table-card"},{default:c((()=>[o((r(),S(la,{data:xe.value,stripe:"",border:"",style:{width:"100%"}},{default:c((()=>[n(Ee,{prop:"username",label:"用户名","min-width":"120"},{default:c((({row:e})=>[u("div",ge,[n(Ze,{size:32},{default:c((()=>[p(h(e.username.charAt(0).toUpperCase()),1)])),_:2},1024),u("span",ye,h(e.username),1)])])),_:1}),n(Ee,{prop:"email",label:"邮箱","min-width":"180"}),n(Ee,{prop:"is_active",label:"状态",width:"100"},{default:c((({row:e})=>[n(Qe,{type:e.is_active?"success":"danger",size:"small"},{default:c((()=>[p(h(e.is_active?"活跃":"禁用"),1)])),_:2},1032,["type"])])),_:1}),n(Ee,{prop:"roles",label:"角色","min-width":"120"},{default:c((({row:e})=>[(r(!0),i(H,null,L(e.roles,(e=>(r(),S(Qe,{key:e.id,size:"small",class:"role-tag"},{default:c((()=>[p(h(e.name),1)])),_:2},1024)))),128))])),_:1}),n(Ee,{prop:"login_count",label:"登录次数",width:"100"}),n(Ee,{prop:"last_login",label:"最后登录",width:"160"},{default:c((({row:e})=>[e.last_login?(r(),i("span",be,h(Ye(e.last_login)),1)):(r(),i("span",we,"从未登录"))])),_:1}),n(Ee,{prop:"created_at",label:"创建时间",width:"160"},{default:c((({row:e})=>[p(h(Ye(e.created_at)),1)])),_:1}),n(Ee,{label:"操作",width:"250",fixed:"right"},{default:c((({row:e})=>[e.deleted_at?(r(),i(H,{key:0},[n(Qe,{type:"danger",size:"small",style:{"margin-right":"8px"}},{default:c((()=>a[21]||(a[21]=[p("已删除")]))),_:1}),n(s,{type:"danger",size:"small",onClick:a=>Ge(e)},{default:c((()=>a[22]||(a[22]=[p(" 永久删除 ")]))),_:2},1032,["onClick"])],64)):(r(),i(H,{key:1},[n(s,{type:"primary",size:"small",onClick:a=>{return l=e,Be.value=l,void(Te.value=!0);var l}},{default:c((()=>a[23]||(a[23]=[p(" 查看 ")]))),_:2},1032,["onClick"]),n(s,{type:"warning",size:"small",onClick:a=>{return l=e,Se.value=!1,Object.assign(Pe,{username:l.username,email:l.email,password:"",is_active:l.is_active}),Be.value=l,void($e.value=!0);var l}},{default:c((()=>a[24]||(a[24]=[p(" 编辑 ")]))),_:2},1032,["onClick"]),n(aa,{onCommand:a=>Ie(a,e)},{dropdown:c((()=>[n(ea,null,{default:c((()=>[n(We,{command:"toggle-status"},{default:c((()=>[p(h(e.is_active?"禁用":"启用"),1)])),_:2},1024),n(We,{command:"reset-password"},{default:c((()=>a[26]||(a[26]=[p(" 重置密码 ")]))),_:1}),n(We,{command:"delete",divided:""},{default:c((()=>a[27]||(a[27]=[p(" 删除用户 ")]))),_:1})])),_:2},1024)])),default:c((()=>[n(s,{type:"info",size:"small"},{default:c((()=>[a[25]||(a[25]=p(" 更多")),n(t,{class:"el-icon--right"},{default:c((()=>[n(m(N))])),_:1})])),_:1})])),_:2},1032,["onCommand"])],64))])),_:1})])),_:1},8,["data"])),[[ra,l.value]]),u("div",he,[n(sa,{"current-page":je.page,"onUpdate:currentPage":a[4]||(a[4]=e=>je.page=e),"page-size":je.page_size,"onUpdate:pageSize":a[5]||(a[5]=e=>je.page_size=e),"page-sizes":[10,20,50,100],total:je.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:Ne,onCurrentChange:Oe},null,8,["current-page","page-size","total"])])])),_:1}),n(ia,{modelValue:Te.value,"onUpdate:modelValue":a[6]||(a[6]=e=>Te.value=e),title:"用户详情",width:"800px","close-on-click-modal":!1},{default:c((()=>[Be.value?(r(),i("div",ke,[n(da,{column:2,border:""},{default:c((()=>[n(ta,{label:"用户名"},{default:c((()=>[p(h(Be.value.username),1)])),_:1}),n(ta,{label:"邮箱"},{default:c((()=>[p(h(Be.value.email),1)])),_:1}),n(ta,{label:"状态"},{default:c((()=>[n(Qe,{type:Be.value.is_active?"success":"danger"},{default:c((()=>[p(h(Be.value.is_active?"活跃":"禁用"),1)])),_:1},8,["type"])])),_:1}),n(ta,{label:"角色"},{default:c((()=>[(r(!0),i(H,null,L(Be.value.roles,(e=>(r(),S(Qe,{key:e.id,class:"role-tag"},{default:c((()=>[p(h(e.name),1)])),_:2},1024)))),128))])),_:1}),n(ta,{label:"登录次数"},{default:c((()=>[p(h(Be.value.login_count||0),1)])),_:1}),n(ta,{label:"最后登录"},{default:c((()=>[p(h(Be.value.last_login?Ye(Be.value.last_login):"从未登录"),1)])),_:1}),n(ta,{label:"创建时间"},{default:c((()=>[p(h(Ye(Be.value.created_at)),1)])),_:1}),n(ta,{label:"更新时间"},{default:c((()=>[p(h(Ye(Be.value.updated_at)),1)])),_:1})])),_:1})])):K("",!0)])),_:1},8,["modelValue"]),n(ia,{modelValue:$e.value,"onUpdate:modelValue":a[12]||(a[12]=e=>$e.value=e),title:Se.value?"新增用户":"编辑用户",width:"600px","close-on-click-modal":!1},{footer:c((()=>[n(s,{onClick:a[11]||(a[11]=e=>$e.value=!1)},{default:c((()=>a[28]||(a[28]=[p("取消")]))),_:1}),n(s,{type:"primary",onClick:Je,loading:ze.value},{default:c((()=>[p(h(Se.value?"创建":"保存"),1)])),_:1},8,["loading"])])),default:c((()=>[n(Xe,{ref_key:"editFormRef",ref:Ae,model:Pe,rules:qe,"label-width":"80px"},{default:c((()=>[n(De,{label:"用户名",prop:"username"},{default:c((()=>[n(Z,{modelValue:Pe.username,"onUpdate:modelValue":a[7]||(a[7]=e=>Pe.username=e),disabled:!Se.value},null,8,["modelValue","disabled"])])),_:1}),n(De,{label:"邮箱",prop:"email"},{default:c((()=>[n(Z,{modelValue:Pe.email,"onUpdate:modelValue":a[8]||(a[8]=e=>Pe.email=e)},null,8,["modelValue"])])),_:1}),Se.value?(r(),S(De,{key:0,label:"密码",prop:"password"},{default:c((()=>[n(Z,{modelValue:Pe.password,"onUpdate:modelValue":a[9]||(a[9]=e=>Pe.password=e),type:"password","show-password":""},null,8,["modelValue"])])),_:1})):K("",!0),n(De,{label:"状态",prop:"is_active"},{default:c((()=>[n(Re,{modelValue:Pe.is_active,"onUpdate:modelValue":a[10]||(a[10]=e=>Pe.is_active=e)},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue","title"])])}}}),[["__scopeId","data-v-41f97db5"]]);export{Ve as default};
