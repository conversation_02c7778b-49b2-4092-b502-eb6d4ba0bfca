var e=Object.defineProperty,a=Object.defineProperties,l=Object.getOwnPropertyDescriptors,t=Object.getOwnPropertySymbols,i=Object.prototype.hasOwnProperty,s=Object.prototype.propertyIsEnumerable,d=(a,l,t)=>l in a?e(a,l,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[l]=t,o=(e,a)=>{for(var l in a||(a={}))i.call(a,l)&&d(e,l,a[l]);if(t)for(var l of t(a))s.call(a,l)&&d(e,l,a[l]);return e},r=(e,t)=>a(e,l(t)),u=(e,a,l)=>new Promise(((t,i)=>{var s=e=>{try{o(l.next(e))}catch(a){i(a)}},d=e=>{try{o(l.throw(e))}catch(a){i(a)}},o=e=>e.done?t(e.value):Promise.resolve(e.value).then(s,d);o((l=l.apply(e,a)).next())}));import{_ as n}from"./index-rNRt1EuS.js";/* empty css                *//* empty css                   *//* empty css                       *//* empty css               */import{d as c,r as p,X as _,j as v,f as m,s as f,c as y,o as g,e as h,a as b,b as w,K as x,w as V,y as k,u as C,aQ as Y,H as z,aS as U,aN as j,aT as F,Z as D,a2 as O,_ as S,C as H,D as M,ar as $,F as E,A as q,N as L,as as P,b0 as T,aF as A,a3 as R,aU as Z,x as I,an as N,bn as B,bp as G,aq as K,a4 as X,aV as J,aW as Q,am as W,aI as ee,aJ as ae,aX as le,bo as te,aY as ie,aG as se,aH as de,bF as oe,Y as re,aD as ue,M as ne,ai as ce}from"./vendor-CAPBtMef.js";import{S as pe}from"./StatCard-DtRQl2IM.js";import _e from"./UpdateStatusDialog-DxSzkJQ-.js";import ve from"./BatchUpdateStatusDialog-BOXLeOiJ.js";import me from"./OrderDetailDialog-OKzos713.js";import{O as fe,B as ye,a as ge}from"./orderApi-DbkZO4__.js";/* empty css                 *//* empty css                             *//* empty css                    */const he={class:"order-management"},be={class:"page-header"},we={class:"page-actions"},xe={key:0,class:"statistics-cards"},Ve={class:"search-filters"},ke={key:1,class:"batch-operations"},Ce={class:"batch-actions"},Ye={class:"selected-info"},ze={class:"order-list"},Ue={class:"user-name"},je={class:"user-email"},Fe={class:"price"},De={class:"billing-status"},Oe={style:{display:"flex","align-items":"center",gap:"4px","flex-wrap":"wrap"}},Se={class:"price-validation"},He={key:0,class:"validation-not-started"},Me={key:1,class:"validation-loading"},$e={key:2,class:"validation-failed"},Ee={key:0,class:"error-msg"},qe={key:3,class:"validation-unsupported"},Le={class:"provider-info"},Pe={key:4,class:"validation-result"},Te={class:"price-comparison"},Ae={class:"system-price"},Re={class:"provider-price"},Ze={class:"profit-status"},Ie={key:0,class:"query-time"},Ne={key:5,class:"validation-unknown"},Be={class:"status-info"},Ge={class:"pagination-wrapper"},Ke={key:0,class:"validation-results"},Xe={key:0,class:"validation-summary"},Je={class:"summary-amount profit"},Qe={class:"summary-amount loss"},We={key:1,class:"summary-info",style:{"margin-top":"16px"}},ea={style:{color:"#67C23A","font-size":"12px"}},aa={style:{color:"#F56C6C","font-size":"12px"}},la={class:"dialog-footer"},ta=n(c({__name:"index",setup(e){const a=p(!1),l=p(!1),t=p(!1),i=p(!1),s=p(!1),d=p(!1),n=p([]),c=p(null),ta=p([]),ia=p(null),sa=p(null),da=p(!1),oa=p(!1),ra=_({provider:"",start_time:"",end_time:"",max_count:100}),ua=p(null),na=p(!1),ca=p(!1),pa=_({provider:"",dateRange:null,start_time:"",end_time:"",max_count:100,dry_run:!1,only_failed:!1,force_sync:!1}),_a=p(null),va=_({page:1,page_size:20,sort_by:"created_at",sort_order:"desc",search_keyword:void 0,status:void 0,express_type:void 0,username:void 0,billing_status:void 0,start_time:void 0,end_time:void 0,price_min:void 0,price_max:void 0,weight_min:void 0,weight_max:void 0}),ma=p(null),fa=_({page:1,pageSize:20,total:0}),ya=v((()=>ta.value.map((e=>e.id)))),ga=()=>u(this,null,(function*(){try{a.value=!0;const e=r(o({},va),{page:fa.page,page_size:fa.pageSize}),l=yield ge.getAdminOrderList(e);l.success?(n.value=l.data.items,fa.total=l.data.total,c.value=l.data.statistics||null):ne.error(l.message||"获取订单列表失败")}catch(e){ne.error("获取订单列表失败")}finally{a.value=!1}})),ha=()=>u(this,null,(function*(){fa.page=1,va.sort_by="created_at",va.sort_order="desc",yield ga(),ne.success("数据已刷新")})),ba=()=>u(this,null,(function*(){try{a.value=!0,Object.assign(va,{page:1,page_size:20,sort_by:"created_at",sort_order:"desc",search_keyword:void 0,status:void 0,express_type:void 0,username:void 0,billing_status:void 0,start_time:void 0,end_time:void 0,price_min:void 0,price_max:void 0,weight_min:void 0,weight_max:void 0}),fa.page=1,fa.pageSize=20,ma.value=null,yield ga(),ne.success("已显示最新订单列表")}catch(e){ne.error("刷新失败")}finally{a.value=!1}})),wa=()=>{fa.page=1,ga()},xa=()=>{Object.assign(va,{page:1,page_size:20,sort_by:"created_at",sort_order:"desc",search_keyword:void 0,status:void 0,express_type:void 0,username:void 0,billing_status:void 0,start_time:void 0,end_time:void 0,price_min:void 0,price_max:void 0,weight_min:void 0,weight_max:void 0}),ma.value=null,fa.page=1,fa.pageSize=20,ga(),ne.success("搜索条件已重置")},Va=()=>{t.value=!t.value},ka=e=>{e?(va.start_time=e[0],va.end_time=e[1]):(va.start_time=void 0,va.end_time=void 0)},Ca=e=>{ta.value=e},Ya=()=>{ta.value=[]},za=({prop:e,order:a})=>{a?(va.sort_by=e,va.sort_order="ascending"===a?"asc":"desc"):(va.sort_by="created_at",va.sort_order="desc"),ga()},Ua=e=>{fa.pageSize=e,fa.page=1,ga()},ja=e=>{fa.page=e,ga()},Fa=()=>{i.value=!1,ga(),ne.success("订单状态更新成功")},Da=()=>{s.value=!1,Ya(),ga(),ne.success("批量更新订单状态成功")},Oa=()=>u(this,null,(function*(){try{l.value=!0;const e=r(o({},va),{page:1,page_size:1e4}),a=yield ge.exportOrders(e),t=window.URL.createObjectURL(a),i=document.createElement("a");i.href=t,i.download=`orders_${(new Date).getTime()}.xlsx`,document.body.appendChild(i),i.click(),document.body.removeChild(i),window.URL.revokeObjectURL(t),ne.success("订单导出成功")}catch(e){ne.error("订单导出失败")}finally{l.value=!1}})),Sa=()=>{ra.provider="",ra.start_time="",ra.end_time="",ra.max_count=100,ua.value=null,da.value=!0},Ha=()=>u(this,null,(function*(){var e,a;try{oa.value=!0;const l={max_count:ra.max_count};ra.provider&&(l.provider=ra.provider),ra.start_time&&(l.start_time=ra.start_time),ra.end_time&&(l.end_time=ra.end_time);const t=yield ge.batchValidatePrices(l);t.success?(ua.value=t.data,(null==(e=t.data)?void 0:e.results)&&Ma(t.data.results),ne.success(`批量价格验证完成！处理了 ${(null==(a=t.data)?void 0:a.processed_orders)||0} 个订单`),yield ga()):ne.error(t.message||"批量价格验证失败")}catch(l){ne.error("批量价格验证失败")}finally{oa.value=!1}})),Ma=e=>{const a=new Map;e.forEach((e=>{a.set(e.order_id,{provider_price:e.provider_price,system_price:e.system_price,profit_status:e.profit_status,query_status:e.query_status,query_time:e.query_time,error_message:e.error_message,supported:e.supported})})),n.value.forEach((e=>{const l=a.get(e.id);l&&(e.price_validation||(e.price_validation={}),Object.assign(e.price_validation,l))}))},$a=()=>{da.value=!1,ua.value=null},Ea=()=>{pa.provider="",pa.dateRange=null,pa.start_time="",pa.end_time="",pa.max_count=100,pa.dry_run=!1,pa.only_failed=!1,pa.force_sync=!1,_a.value=null,na.value=!0},qa=e=>{e?(pa.start_time=e[0],pa.end_time=e[1]):(pa.start_time="",pa.end_time="")},La=()=>u(this,null,(function*(){var e,a,l,t;try{ca.value=!0;const i={max_count:pa.max_count,dry_run:pa.dry_run,only_failed:pa.only_failed,force_sync:pa.force_sync};pa.provider&&(i.provider=pa.provider),pa.start_time&&(i.start_time=pa.start_time),pa.end_time&&(i.end_time=pa.end_time);const s=yield ge.batchSyncOrderStatus(i);if(s.success){_a.value=s.data;const i=pa.dry_run?`试运行完成！检查了 ${(null==(e=s.data)?void 0:e.processed_orders)||0} 个订单`:`批量状态同步完成！处理了 ${(null==(a=s.data)?void 0:a.processed_orders)||0} 个订单，${(null==(l=s.data)?void 0:l.changed_orders)||0} 个订单状态发生变化`;ne.success(i),pa.dry_run||(yield ga()),(null==(t=s.data)?void 0:t.results)&&s.data.results.length>0&&Pa(s.data)}else ne.error(s.message||"批量状态同步失败")}catch(i){ne.error("批量状态同步失败")}finally{ca.value=!1}})),Pa=e=>{const a=e.summary||{};e.results;let l=`同步结果汇总：\n总订单数：${e.total_orders}\n处理订单数：${e.processed_orders}\n成功订单数：${e.success_orders}\n失败订单数：${e.failed_orders}\n状态变更订单数：${e.changed_orders}\n处理耗时：${e.duration}\n\n供应商统计：`;Object.entries(a.provider_stats||{}).forEach((([e,a])=>{l+=`\n${e}: ${a}个`})),a.status_change_stats&&Object.keys(a.status_change_stats).length>0&&(l+="\n\n状态变更统计：",Object.entries(a.status_change_stats).forEach((([e,a])=>{Object.entries(a).forEach((([a,t])=>{l+=`\n${e} → ${a}: ${t}个`}))}))),ce.alert(l,"同步结果",{type:"success"})},Ta=()=>{na.value=!1,_a.value=null},Aa=e=>{const a=fe.find((a=>a.value===e));return(null==a?void 0:a.label)||e},Ra=e=>{const a=ye.find((a=>a.value===e));return(null==a?void 0:a.label)||e},Za=e=>`¥${(null==e?void 0:e.toFixed(2))||"0.00"}`,Ia=e=>{if(!e)return"-";try{return new Date(e).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})}catch(a){return e}},Na=e=>({profit:"盈利",loss:"亏损",break_even:"持平",unknown:"未知"}[e]||e),Ba=e=>{try{return new Date(e).toLocaleString("zh-CN",{month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})}catch(a){return e}},Ga=e=>u(this,null,(function*(){try{e.syncing=!0;const a=yield ge.syncOrderStatus(e.id);a.success?(ne.success("订单状态同步成功"),yield ga()):ne.error(a.message||"订单状态同步失败")}catch(a){ne.error("同步订单状态失败")}finally{e.syncing=!1}}));return m((()=>{ga(),document.addEventListener("visibilitychange",(()=>{document.hidden||ha()}))})),f((()=>{document.removeEventListener("visibilitychange",(()=>{}))})),(e,o)=>{const r=k,u=z,p=j,_=F,v=H,m=S,f=P,ne=$,ce=T,ge=A,_a=G,ga=K,Ma=D,Pa=J,Ka=ae,Xa=le,Ja=ee,Qa=de,Wa=oe,el=re,al=ue,ll=se;return g(),y("div",he,[h("div",be,[o[31]||(o[31]=h("div",{class:"header-left"},[h("h1",{class:"page-title"},[h("i",{class:"iconfont-sys"},""),x(" 订单管理 ")]),h("p",{class:"page-description"},"管理和查看所有订单信息")],-1)),h("div",we,[w(u,{onClick:ha,loading:a.value},{default:V((()=>[w(r,null,{default:V((()=>[w(C(Y))])),_:1}),o[28]||(o[28]=x(" 刷新 "))])),_:1},8,["loading"]),w(u,{onClick:ba,loading:a.value,type:"primary"},{default:V((()=>[w(r,null,{default:V((()=>[w(C(Y))])),_:1}),o[29]||(o[29]=x(" 最新订单 "))])),_:1},8,["loading"]),w(u,{type:"primary",onClick:Oa,loading:l.value},{default:V((()=>[w(r,null,{default:V((()=>[w(C(U))])),_:1}),o[30]||(o[30]=x(" 导出 "))])),_:1},8,["loading"])])]),c.value?(g(),y("div",xe,[w(_,{gutter:20},{default:V((()=>[w(p,{span:6},{default:V((()=>[w(pe,{title:"总订单数",value:c.value.total_orders,icon:"el-icon-shopping-cart-2","icon-color":"#409EFF","icon-bg-color":"#E6F7FF"},null,8,["value"])])),_:1}),w(p,{span:6},{default:V((()=>[w(pe,{title:"今日订单",value:c.value.today_orders,icon:"el-icon-clock","icon-color":"#67C23A","icon-bg-color":"#F0F9FF"},null,8,["value"])])),_:1}),w(p,{span:6},{default:V((()=>[w(pe,{title:"总金额",value:Za(c.value.total_amount),icon:"el-icon-coin","icon-color":"#E6A23C","icon-bg-color":"#FDF6EC"},null,8,["value"])])),_:1}),w(p,{span:6},{default:V((()=>[w(pe,{title:"今日金额",value:Za(c.value.today_amount),icon:"el-icon-wallet","icon-color":"#F56C6C","icon-bg-color":"#FEF0F0"},null,8,["value"])])),_:1})])),_:1})])):b("",!0),h("div",Ve,[w(Pa,null,{default:V((()=>[w(Ma,{model:va,"label-width":"80px"},{default:V((()=>[w(_,{gutter:20},{default:V((()=>[w(p,{span:6},{default:V((()=>[w(m,{label:"订单号"},{default:V((()=>[w(v,{modelValue:va.search_keyword,"onUpdate:modelValue":o[0]||(o[0]=e=>va.search_keyword=e),placeholder:"请输入订单号",clearable:"",onKeyup:M(wa,["enter"]),style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1}),w(p,{span:6},{default:V((()=>[w(m,{label:"订单状态"},{default:V((()=>[w(ne,{modelValue:va.status,"onUpdate:modelValue":o[1]||(o[1]=e=>va.status=e),placeholder:"请选择状态",clearable:"",style:{width:"100%"}},{default:V((()=>[(g(!0),y(E,null,q(C(fe),(e=>(g(),L(f,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1})])),_:1}),w(p,{span:6},{default:V((()=>[w(m,{label:"快递公司"},{default:V((()=>[w(ne,{modelValue:va.express_type,"onUpdate:modelValue":o[2]||(o[2]=e=>va.express_type=e),placeholder:"请选择快递公司",clearable:"",style:{width:"100%"}},{default:V((()=>[w(f,{label:"顺丰",value:"SF"}),w(f,{label:"圆通",value:"YTO"}),w(f,{label:"中通",value:"ZTO"}),w(f,{label:"申通",value:"STO"}),w(f,{label:"韵达",value:"YD"})])),_:1},8,["modelValue"])])),_:1})])),_:1}),w(p,{span:6},{default:V((()=>[w(m,{label:"用户名"},{default:V((()=>[w(v,{modelValue:va.username,"onUpdate:modelValue":o[3]||(o[3]=e=>va.username=e),placeholder:"请输入用户名",clearable:"",style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),w(_,{gutter:20},{default:V((()=>[w(p,{span:6},{default:V((()=>[w(m,{label:"计费状态"},{default:V((()=>[w(ne,{modelValue:va.billing_status,"onUpdate:modelValue":o[4]||(o[4]=e=>va.billing_status=e),placeholder:"请选择计费状态",clearable:"",style:{width:"100%"}},{default:V((()=>[(g(!0),y(E,null,q(C(ye),(e=>(g(),L(f,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1})])),_:1}),w(p,{span:8},{default:V((()=>[w(m,{label:"创建时间"},{default:V((()=>[w(ce,{modelValue:ma.value,"onUpdate:modelValue":o[5]||(o[5]=e=>ma.value=e),type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",onChange:ka,style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1}),w(p,{span:10},{default:V((()=>[w(m,{label:" "},{default:V((()=>[w(ge,null,{default:V((()=>[w(u,{type:"primary",onClick:wa,loading:a.value},{default:V((()=>[w(r,null,{default:V((()=>[w(C(R))])),_:1}),o[32]||(o[32]=x(" 搜索 "))])),_:1},8,["loading"]),w(u,{onClick:xa},{default:V((()=>[w(r,null,{default:V((()=>[w(C(Z))])),_:1}),o[33]||(o[33]=x(" 重置 "))])),_:1}),w(u,{type:"info",onClick:Va},{default:V((()=>[x(I(t.value?"收起":"展开")+" ",1),w(r,null,{default:V((()=>[t.value?(g(),L(C(B),{key:1})):(g(),L(C(N),{key:0}))])),_:1})])),_:1})])),_:1})])),_:1})])),_:1})])),_:1}),O(h("div",null,[w(_a,{"content-position":"left"},{default:V((()=>o[34]||(o[34]=[x("高级搜索")]))),_:1}),w(_,{gutter:20},{default:V((()=>[w(p,{span:6},{default:V((()=>[w(m,{label:"价格范围"},{default:V((()=>[w(ga,{modelValue:va.price_min,"onUpdate:modelValue":o[6]||(o[6]=e=>va.price_min=e),placeholder:"最小价格",min:0,precision:2,style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1}),w(p,{span:6},{default:V((()=>[w(m,{label:"　"},{default:V((()=>[w(ga,{modelValue:va.price_max,"onUpdate:modelValue":o[7]||(o[7]=e=>va.price_max=e),placeholder:"最大价格",min:0,precision:2,style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1}),w(p,{span:6},{default:V((()=>[w(m,{label:"重量范围"},{default:V((()=>[w(ga,{modelValue:va.weight_min,"onUpdate:modelValue":o[8]||(o[8]=e=>va.weight_min=e),placeholder:"最小重量(kg)",min:0,precision:2,style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1}),w(p,{span:6},{default:V((()=>[w(m,{label:"　"},{default:V((()=>[w(ga,{modelValue:va.weight_max,"onUpdate:modelValue":o[9]||(o[9]=e=>va.weight_max=e),placeholder:"最大重量(kg)",min:0,precision:2,style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})],512),[[X,t.value]])])),_:1},8,["model"])])),_:1})]),ta.value.length>0?(g(),y("div",ke,[w(Pa,null,{default:V((()=>[h("div",Ce,[h("span",Ye,"已选择 "+I(ta.value.length)+" 个订单",1),w(ge,null,{default:V((()=>[w(u,{type:"warning",onClick:o[10]||(o[10]=e=>s.value=!0)},{default:V((()=>[w(r,null,{default:V((()=>[w(C(Q))])),_:1}),o[35]||(o[35]=x(" 批量更新状态 "))])),_:1}),w(u,{type:"danger",onClick:Ya},{default:V((()=>[w(r,null,{default:V((()=>[w(C(W))])),_:1}),o[36]||(o[36]=x(" 取消选择 "))])),_:1})])),_:1})])])),_:1})])):b("",!0),h("div",ze,[w(Pa,null,{default:V((()=>[O((g(),L(Ja,{ref:"orderTable",data:n.value,onSelectionChange:Ca,onSortChange:za,stripe:"",border:"",style:{width:"100%"}},{default:V((()=>[w(Ka,{type:"selection",width:"55"}),w(Ka,{prop:"id",label:"ID",width:"80",sortable:"custom"}),w(Ka,{prop:"order_no",label:"订单号",width:"180","show-overflow-tooltip":""}),w(Ka,{prop:"customer_order_no",label:"客户订单号",width:"150","show-overflow-tooltip":""}),w(Ka,{label:"用户信息",width:"120"},{default:V((({row:e})=>{var a,l;return[h("div",null,[h("div",Ue,I((null==(a=e.user)?void 0:a.username)||"-"),1),h("div",je,I((null==(l=e.user)?void 0:l.email)||"-"),1)])]})),_:1}),w(Ka,{prop:"express_type",label:"快递公司",width:"100"}),w(Ka,{label:"状态",width:"100"},{default:V((({row:e})=>{return[w(Xa,{type:(a=e.status,{submitted:"primary",submit_failed:"danger",print_failed:"danger",assigned:"info",awaiting_pickup:"warning",picked_up:"primary",pickup_failed:"danger",in_transit:"info",out_for_delivery:"info",delivered:"success",delivered_abnormal:"warning",billed:"success",exception:"danger",returned:"warning",forwarded:"info",cancelling:"warning",cancelled:"danger",voided:"danger",weight_updated:"info",revived:"primary"}[a]||"info")},{default:V((()=>[x(I(Aa(e.status)),1)])),_:2},1032,["type"])];var a})),_:1}),w(Ka,{prop:"weight",label:"重量(kg)",width:"100",sortable:"custom"}),w(Ka,{label:"费用信息",width:"120"},{default:V((({row:e})=>{var a,l;return[h("div",null,[h("div",Fe,"¥"+I((null==(a=e.price)?void 0:a.toFixed(2))||"0.00"),1),h("div",De,[w(Xa,{size:"small",type:(l=e.billing_status,{pending:"warning",calculated:"primary",paid:"success",refunded:"info"}[l]||"info")},{default:V((()=>[x(I(Ra(e.billing_status)),1)])),_:2},1032,["type"])])])]})),_:1}),w(Ka,{label:"价格验证",width:"280"},{header:V((()=>[h("div",Oe,[o[39]||(o[39]=h("span",{style:{"white-space":"nowrap"}},"价格验证",-1)),w(u,{type:"primary",size:"small",onClick:Sa,title:"批量价格验证",style:{"font-size":"11px",padding:"2px 6px"}},{default:V((()=>o[37]||(o[37]=[x(" 批量验证 ")]))),_:1}),w(u,{type:"info",size:"small",onClick:Ea,title:"批量状态同步",style:{"font-size":"11px",padding:"2px 6px"}},{default:V((()=>o[38]||(o[38]=[x(" 状态同步 ")]))),_:1})])])),default:V((({row:e})=>{var a,l,t;return[h("div",Se,[!e.price_validation||!e.price_validation.query_time&&"pending"===e.price_validation.query_status?(g(),y("div",He,[w(Xa,{type:"info",size:"small"},{default:V((()=>o[40]||(o[40]=[x("未验证")]))),_:1})])):"pending"===e.price_validation.query_status?(g(),y("div",Me,[w(r,{class:"is-loading"},{default:V((()=>[w(C(te))])),_:1}),o[41]||(o[41]=h("span",null,"查询中...",-1))])):"failed"===e.price_validation.query_status?(g(),y("div",$e,[w(Xa,{type:"danger",size:"small"},{default:V((()=>o[42]||(o[42]=[x("查询失败")]))),_:1}),e.price_validation.error_message?(g(),y("div",Ee,I(e.price_validation.error_message),1)):b("",!0)])):e.price_validation.supported?"success"===e.price_validation.query_status?(g(),y("div",Pe,[h("div",Te,[h("div",Ae,"系统: ¥"+I((null==(a=e.price_validation.system_price)?void 0:a.toFixed(2))||"0.00"),1),h("div",Re,"供应商: ¥"+I((null==(l=e.price_validation.provider_price)?void 0:l.toFixed(2))||"0.00"),1)]),h("div",Ze,[w(Xa,{type:(t=e.price_validation.profit_status,{profit:"success",loss:"danger",break_even:"warning",unknown:"info"}[t]||"info"),size:"small"},{default:V((()=>[x(I(Na(e.price_validation.profit_status)),1)])),_:2},1032,["type"])]),e.price_validation.query_time?(g(),y("div",Ie,I(Ba(e.price_validation.query_time)),1)):b("",!0)])):(g(),y("div",Ne,[w(Xa,{type:"warning",size:"small"},{default:V((()=>o[44]||(o[44]=[x("未知状态")]))),_:1}),h("div",Be,I(e.price_validation.query_status),1)])):(g(),y("div",qe,[w(Xa,{type:"info",size:"small"},{default:V((()=>o[43]||(o[43]=[x("不支持")]))),_:1}),h("div",Le,I(e.provider),1)]))])]})),_:1}),w(Ka,{prop:"created_at",label:"创建时间",width:"160",sortable:"custom"},{default:V((({row:e})=>[x(I(Ia(e.created_at)),1)])),_:1}),w(Ka,{label:"操作",width:"240",fixed:"right"},{default:V((({row:e})=>[w(ge,null,{default:V((()=>[w(u,{type:"primary",size:"small",onClick:a=>{return l=e.id,sa.value=l,void(d.value=!0);var l}},{default:V((()=>[w(r,null,{default:V((()=>[w(C(ie))])),_:1}),o[45]||(o[45]=x(" 详情 "))])),_:2},1032,["onClick"]),w(u,{type:"warning",size:"small",onClick:a=>{return l=e,ia.value=l,void(i.value=!0);var l}},{default:V((()=>[w(r,null,{default:V((()=>[w(C(Q))])),_:1}),o[46]||(o[46]=x(" 状态 "))])),_:2},1032,["onClick"]),w(u,{type:"info",size:"small",onClick:a=>Ga(e),loading:e.syncing},{default:V((()=>[w(r,null,{default:V((()=>[w(C(Y))])),_:1}),o[47]||(o[47]=x(" 同步 "))])),_:2},1032,["onClick","loading"])])),_:2},1024)])),_:1})])),_:1},8,["data"])),[[ll,a.value]]),h("div",Ge,[w(Qa,{"current-page":fa.page,"onUpdate:currentPage":o[11]||(o[11]=e=>fa.page=e),"page-size":fa.pageSize,"onUpdate:pageSize":o[12]||(o[12]=e=>fa.pageSize=e),"page-sizes":[10,20,50,100],total:fa.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:Ua,onCurrentChange:ja},null,8,["current-page","page-size","total"])])])),_:1})]),w(_e,{visible:i.value,"onUpdate:visible":o[13]||(o[13]=e=>i.value=e),order:ia.value,onSuccess:Fa},null,8,["visible","order"]),w(ve,{visible:s.value,"onUpdate:visible":o[14]||(o[14]=e=>s.value=e),"order-ids":ya.value,onSuccess:Da},null,8,["visible","order-ids"]),w(me,{visible:d.value,"onUpdate:visible":o[15]||(o[15]=e=>d.value=e),"order-id":sa.value},null,8,["visible","order-id"]),w(el,{modelValue:da.value,"onUpdate:modelValue":o[20]||(o[20]=e=>da.value=e),title:"批量价格验证",width:"600px","close-on-click-modal":!1},{footer:V((()=>[h("span",la,[w(u,{onClick:$a},{default:V((()=>o[54]||(o[54]=[x("取消")]))),_:1}),w(u,{type:"primary",onClick:Ha,loading:oa.value},{default:V((()=>o[55]||(o[55]=[x(" 开始验证 ")]))),_:1},8,["loading"])])])),default:V((()=>[w(Ma,{model:ra,"label-width":"100px"},{default:V((()=>[w(m,{label:"供应商"},{default:V((()=>[w(ne,{modelValue:ra.provider,"onUpdate:modelValue":o[16]||(o[16]=e=>ra.provider=e),placeholder:"选择供应商（可选）",clearable:"",style:{width:"100%"}},{default:V((()=>[w(f,{label:"快递100",value:"kuaidi100"}),w(f,{label:"云通",value:"yuntong"}),w(f,{label:"易达",value:"yida"})])),_:1},8,["modelValue"])])),_:1}),w(m,{label:"时间范围"},{default:V((()=>[w(ce,{modelValue:ra.start_time,"onUpdate:modelValue":o[17]||(o[17]=e=>ra.start_time=e),type:"datetime",placeholder:"开始时间（可选）",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DDTHH:mm:ss.000Z",style:{width:"48%","margin-right":"4%"}},null,8,["modelValue"]),w(ce,{modelValue:ra.end_time,"onUpdate:modelValue":o[18]||(o[18]=e=>ra.end_time=e),type:"datetime",placeholder:"结束时间（可选）",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DDTHH:mm:ss.000Z",style:{width:"48%"}},null,8,["modelValue"])])),_:1}),w(m,{label:"最大数量"},{default:V((()=>[w(ga,{modelValue:ra.max_count,"onUpdate:modelValue":o[19]||(o[19]=e=>ra.max_count=e),min:1,max:500,placeholder:"最大处理订单数量",style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1},8,["model"]),ua.value?(g(),y("div",Ke,[w(_a,{"content-position":"left"},{default:V((()=>o[48]||(o[48]=[x("验证结果")]))),_:1}),w(_,{gutter:16},{default:V((()=>[w(p,{span:6},{default:V((()=>[w(Wa,{title:"总订单数",value:ua.value.total_orders},null,8,["value"])])),_:1}),w(p,{span:6},{default:V((()=>[w(Wa,{title:"已处理",value:ua.value.processed_orders},null,8,["value"])])),_:1}),w(p,{span:6},{default:V((()=>[w(Wa,{title:"成功",value:ua.value.success_orders},null,8,["value"])])),_:1}),w(p,{span:6},{default:V((()=>[w(Wa,{title:"失败",value:ua.value.failed_orders},null,8,["value"])])),_:1})])),_:1}),ua.value.summary?(g(),y("div",Xe,[w(_a,{"content-position":"left"},{default:V((()=>o[49]||(o[49]=[x("盈亏汇总")]))),_:1}),w(_,{gutter:16},{default:V((()=>[w(p,{span:6},{default:V((()=>{var e;return[w(Wa,{title:"盈利订单",value:ua.value.summary.profit_count},{suffix:V((()=>o[50]||(o[50]=[h("span",{style:{color:"#67C23A"}},"个",-1)]))),_:1},8,["value"]),h("div",Je,"+¥"+I((null==(e=ua.value.summary.total_profit)?void 0:e.toFixed(2))||"0.00"),1)]})),_:1}),w(p,{span:6},{default:V((()=>{var e;return[w(Wa,{title:"亏损订单",value:ua.value.summary.loss_count},{suffix:V((()=>o[51]||(o[51]=[h("span",{style:{color:"#F56C6C"}},"个",-1)]))),_:1},8,["value"]),h("div",Qe,"-¥"+I((null==(e=ua.value.summary.total_loss)?void 0:e.toFixed(2))||"0.00"),1)]})),_:1}),w(p,{span:6},{default:V((()=>[w(Wa,{title:"持平订单",value:ua.value.summary.break_even_count},{suffix:V((()=>o[52]||(o[52]=[h("span",{style:{color:"#E6A23C"}},"个",-1)]))),_:1},8,["value"])])),_:1}),w(p,{span:6},{default:V((()=>[w(Wa,{title:"不支持",value:ua.value.summary.unsupported_count},{suffix:V((()=>o[53]||(o[53]=[h("span",{style:{color:"#909399"}},"个",-1)]))),_:1},8,["value"])])),_:1})])),_:1})])):b("",!0),ua.value.summary?(g(),y("div",We,[w(_,{gutter:16},{default:V((()=>[w(p,{span:8},{default:V((()=>{var e;return[w(Wa,{title:"盈利订单",value:ua.value.summary.profit_count},null,8,["value"]),h("div",ea," +¥"+I((null==(e=ua.value.summary.total_profit)?void 0:e.toFixed(2))||"0.00"),1)]})),_:1}),w(p,{span:8},{default:V((()=>{var e;return[w(Wa,{title:"亏损订单",value:ua.value.summary.loss_count},null,8,["value"]),h("div",aa," -¥"+I((null==(e=ua.value.summary.total_loss)?void 0:e.toFixed(2))||"0.00"),1)]})),_:1}),w(p,{span:8},{default:V((()=>[w(Wa,{title:"持平订单",value:ua.value.summary.break_even_count},null,8,["value"])])),_:1})])),_:1})])):b("",!0)])):b("",!0)])),_:1},8,["modelValue"]),w(el,{modelValue:na.value,"onUpdate:modelValue":o[27]||(o[27]=e=>na.value=e),title:"批量状态同步",width:"600px","close-on-click-modal":!1},{footer:V((()=>[w(u,{onClick:Ta},{default:V((()=>o[59]||(o[59]=[x("取消")]))),_:1}),w(u,{type:"primary",onClick:La,loading:ca.value},{default:V((()=>[x(I(pa.dry_run?"试运行":"开始同步"),1)])),_:1},8,["loading"])])),default:V((()=>[w(Ma,{model:pa,"label-width":"100px"},{default:V((()=>[w(m,{label:"供应商"},{default:V((()=>[w(ne,{modelValue:pa.provider,"onUpdate:modelValue":o[21]||(o[21]=e=>pa.provider=e),placeholder:"选择供应商（可选）",clearable:""},{default:V((()=>[w(f,{label:"快递100",value:"kuaidi100"}),w(f,{label:"云通",value:"yuntong"})])),_:1},8,["modelValue"])])),_:1}),w(m,{label:"时间范围"},{default:V((()=>[w(ce,{modelValue:pa.dateRange,"onUpdate:modelValue":o[22]||(o[22]=e=>pa.dateRange=e),type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DDTHH:mm:ssZ",onChange:qa},null,8,["modelValue"])])),_:1}),w(m,{label:"最大数量"},{default:V((()=>[w(ga,{modelValue:pa.max_count,"onUpdate:modelValue":o[23]||(o[23]=e=>pa.max_count=e),min:1,max:500,placeholder:"最大处理数量"},null,8,["modelValue"])])),_:1}),w(m,{label:"同步选项"},{default:V((()=>[w(al,{modelValue:pa.dry_run,"onUpdate:modelValue":o[24]||(o[24]=e=>pa.dry_run=e)},{default:V((()=>o[56]||(o[56]=[x("试运行模式")]))),_:1},8,["modelValue"]),w(al,{modelValue:pa.only_failed,"onUpdate:modelValue":o[25]||(o[25]=e=>pa.only_failed=e)},{default:V((()=>o[57]||(o[57]=[x("只同步失败订单")]))),_:1},8,["modelValue"]),w(al,{modelValue:pa.force_sync,"onUpdate:modelValue":o[26]||(o[26]=e=>pa.force_sync=e)},{default:V((()=>o[58]||(o[58]=[x("强制同步")]))),_:1},8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-8b500795"]]);export{ta as default};
