import{_ as a}from"./index-rNRt1EuS.js";/* empty css                *//* empty css               */import{a as e,B as l,T as s}from"./balanceApi-B8_MfzSO.js";import{d as t,r,p as n,M as i,N as o,o as d,w as u,a2 as c,c as p,a as v,b as _,aV as f,aT as b,aN as m,e as h,x as g,aX as w,K as y,aI as C,aJ as x,n as j,aG as F,H as I,Y as D}from"./vendor-CAPBtMef.js";const A={key:0},k={class:"info-item"},V={class:"value"},z={class:"info-item"},B={class:"value"},N={class:"info-item"},U={class:"value"},$={class:"balance-item"},M={class:"balance-value primary"},P={class:"balance-item"},T={class:"balance-value success"},E={class:"balance-item"},G={class:"balance-value info"},H={class:"balance-item"},J={class:"balance-value"},K={class:"dialog-footer"},L=a(t({__name:"UserDetailDialog",props:{visible:{type:Boolean},userId:{}},emits:["update:visible"],setup(a,{emit:t}){const L=a,S=t,X=r(!1),Y=r(!1),q=r(null);n((()=>L.visible),(a=>{X.value=a,a&&L.userId&&O()})),n(X,(a=>{S("update:visible",a)}));const O=()=>{return a=this,l=null,s=function*(){if(L.userId)try{Y.value=!0;const a=yield e.getUserBalanceDetail(L.userId);a.success?q.value=a.data:i.error(a.message||"获取用户详情失败")}catch(a){i.error("获取用户详情失败")}finally{Y.value=!1}},new Promise(((e,t)=>{var r=a=>{try{i(s.next(a))}catch(e){t(e)}},n=a=>{try{i(s.throw(a))}catch(e){t(e)}},i=a=>a.done?e(a.value):Promise.resolve(a.value).then(r,n);i((s=s.apply(a,l)).next())}));var a,l,s},Q=()=>{O()},R=()=>{X.value=!1,q.value=null},W=a=>{const e=l.find((e=>e.value===a));return(null==e?void 0:e.label)||a},Z=a=>{const e=s.find((e=>e.value===a));return(null==e?void 0:e.label)||a},aa=(a,e)=>{const l=parseFloat(a);return`${["user_deposit","admin_deposit","order_cancel_refund"].includes(e)?"+":"-"}¥${Math.abs(l).toFixed(2)}`},ea=a=>{const e="string"==typeof a?parseFloat(a):a;return`¥${(null==e?void 0:e.toFixed(2))||"0.00"}`};return(a,e)=>{const l=m,s=b,t=f,r=w,n=x,i=C,L=I,S=D,O=F;return d(),o(S,{modelValue:X.value,"onUpdate:modelValue":e[0]||(e[0]=a=>X.value=a),title:"用户余额详情",width:"800px","before-close":R},{footer:u((()=>[h("div",K,[_(L,{onClick:R},{default:u((()=>e[11]||(e[11]=[y("关闭")]))),_:1}),_(L,{type:"primary",onClick:Q,loading:Y.value},{default:u((()=>e[12]||(e[12]=[y(" 刷新 ")]))),_:1},8,["loading"])])])),default:u((()=>[c((d(),p("div",null,[q.value?(d(),p("div",A,[_(t,{class:"user-basic-info",style:{"margin-bottom":"20px"}},{header:u((()=>e[1]||(e[1]=[h("span",null,"用户基本信息",-1)]))),default:u((()=>[_(s,{gutter:20},{default:u((()=>[_(l,{span:8},{default:u((()=>[h("div",k,[e[2]||(e[2]=h("span",{class:"label"},"用户ID:",-1)),h("span",V,g(q.value.user_id),1)])])),_:1}),_(l,{span:8},{default:u((()=>[h("div",z,[e[3]||(e[3]=h("span",{class:"label"},"用户名:",-1)),h("span",B,g(q.value.username),1)])])),_:1}),_(l,{span:8},{default:u((()=>[h("div",N,[e[4]||(e[4]=h("span",{class:"label"},"邮箱:",-1)),h("span",U,g(q.value.email),1)])])),_:1})])),_:1})])),_:1}),_(t,{class:"balance-info",style:{"margin-bottom":"20px"}},{header:u((()=>e[5]||(e[5]=[h("span",null,"余额信息",-1)]))),default:u((()=>[_(s,{gutter:20},{default:u((()=>[_(l,{span:6},{default:u((()=>[h("div",$,[e[6]||(e[6]=h("div",{class:"balance-label"},"总余额",-1)),h("div",M,g(ea(q.value.balance)),1)])])),_:1}),_(l,{span:6},{default:u((()=>[h("div",P,[e[7]||(e[7]=h("div",{class:"balance-label"},"可用余额",-1)),h("div",T,g(ea(q.value.available_balance)),1)])])),_:1}),_(l,{span:6},{default:u((()=>[h("div",E,[e[8]||(e[8]=h("div",{class:"balance-label"},"交易次数",-1)),h("div",G,g(q.value.transaction_count),1)])])),_:1}),_(l,{span:6},{default:u((()=>{return[h("div",H,[e[9]||(e[9]=h("div",{class:"balance-label"},"状态",-1)),h("div",J,[_(r,{type:(a=q.value.status,{active:"success",frozen:"warning",disabled:"danger"}[a]||"info")},{default:u((()=>[y(g(W(q.value.status)),1)])),_:1},8,["type"])])])];var a})),_:1})])),_:1})])),_:1}),_(t,{class:"recent-transactions"},{header:u((()=>e[10]||(e[10]=[h("span",null,"最近交易记录",-1)]))),default:u((()=>[_(i,{data:q.value.recent_transactions,style:{width:"100%"},"max-height":"300"},{default:u((()=>[_(n,{prop:"id",label:"交易ID",width:"120","show-overflow-tooltip":""}),_(n,{label:"类型",width:"100"},{default:u((({row:a})=>{return[_(r,{type:(e=a.transaction_type,{user_deposit:"success",admin_deposit:"success",order_pre_charge:"warning",order_intercept_charge:"warning",return_charge:"warning",order_revive_recharge:"warning",order_cancel_refund:"info",balance_adjustment:"primary"}[e]||"default"),size:"small"},{default:u((()=>[y(g(Z(a.transaction_type)),1)])),_:2},1032,["type"])];var e})),_:1}),_(n,{prop:"amount",label:"金额",width:"120"},{default:u((({row:a})=>{return[h("span",{style:j({color:(e=a.transaction_type,{user_deposit:"#67C23A",admin_deposit:"#67C23A",order_cancel_refund:"#67C23A",order_pre_charge:"#F56C6C",order_intercept_charge:"#F56C6C",return_charge:"#F56C6C",order_revive_recharge:"#F56C6C",balance_adjustment:"#E6A23C"}[e]||"#303133")})},g(aa(a.amount,a.transaction_type)),5)];var e})),_:1}),_(n,{prop:"balance_after",label:"余额",width:"120"},{default:u((({row:a})=>[y(g(ea(a.balance_after)),1)])),_:1}),_(n,{prop:"description",label:"描述","show-overflow-tooltip":""}),_(n,{prop:"created_at",label:"时间",width:"160"},{default:u((({row:a})=>{return[y(g((e=a.created_at,e?new Date(e).toLocaleString("zh-CN"):"-")),1)];var e})),_:1})])),_:1},8,["data"])])),_:1})])):v("",!0)])),[[O,Y.value]])])),_:1},8,["modelValue"])}}}),[["__scopeId","data-v-41f33557"]]);export{L as default};
