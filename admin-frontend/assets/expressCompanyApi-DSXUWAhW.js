var t=Object.defineProperty,e=Object.defineProperties,r=Object.getOwnPropertyDescriptors,n=Object.getOwnPropertySymbols,a=Object.prototype.hasOwnProperty,i=Object.prototype.propertyIsEnumerable,s=(e,r,n)=>r in e?t(e,r,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[r]=n,u=(t,e)=>{for(var r in e||(e={}))a.call(e,r)&&s(t,r,e[r]);if(n)for(var r of n(e))i.call(e,r)&&s(t,r,e[r]);return t},p=(t,n)=>e(t,r(n)),l=(t,e,r)=>new Promise(((n,a)=>{var i=t=>{try{u(r.next(t))}catch(e){a(e)}},s=t=>{try{u(r.throw(t))}catch(e){a(e)}},u=t=>t.done?n(t.value):Promise.resolve(t.value).then(i,s);u((r=r.apply(t,e)).next())}));import{q as o}from"./index-rNRt1EuS.js";class c{static getCompanies(t){return l(this,null,(function*(){return o.get({url:"/api/v1/admin/express/companies",params:p(u({},t),{_t:Date.now()})})}))}static getCompany(t){return l(this,null,(function*(){return o.get({url:`/api/v1/admin/express/companies/${t}`,params:{_t:Date.now()}})}))}static createCompany(t){return l(this,null,(function*(){return o.post({url:"/api/v1/admin/express/companies",data:t})}))}static updateCompany(t,e){return l(this,null,(function*(){return o.put({url:`/api/v1/admin/express/companies/${t}`,data:e})}))}static deleteCompany(t){return l(this,null,(function*(){return o.del({url:`/api/v1/admin/express/companies/${t}`})}))}static getProviders(t){return l(this,null,(function*(){return o.get({url:"/api/v1/admin/express/providers",params:p(u({},t),{_t:Date.now()})})}))}static getProvider(t){return l(this,null,(function*(){return o.get({url:`/api/v1/admin/express/providers/${t}`,params:{_t:Date.now()}})}))}static createProvider(t){return l(this,null,(function*(){return o.post({url:"/api/v1/admin/express/providers",data:t})}))}static updateProvider(t,e){return l(this,null,(function*(){return o.put({url:`/api/v1/admin/express/providers/${t}`,data:e})}))}static deleteProvider(t){return l(this,null,(function*(){return o.del({url:`/api/v1/admin/express/providers/${t}`})}))}static getMappings(t){return l(this,null,(function*(){return o.get({url:"/api/v1/admin/express/mappings",params:p(u({},t),{_t:Date.now()})})}))}static getMapping(t){return l(this,null,(function*(){return o.get({url:`/api/v1/admin/express/mappings/${t}`,params:{_t:Date.now()}})}))}static createMapping(t){return l(this,null,(function*(){return o.post({url:"/api/v1/admin/express/mappings",data:t})}))}static updateMapping(t,e){return l(this,null,(function*(){return o.put({url:`/api/v1/admin/express/mappings/${t}`,data:e})}))}static deleteMapping(t){return l(this,null,(function*(){return o.del({url:`/api/v1/admin/express/mappings/${t}`})}))}static getMappingsByCompany(t){return l(this,null,(function*(){return o.get({url:`/api/v1/admin/express/mappings/company/${t}`,params:{_t:Date.now()}})}))}static getMappingsByProvider(t){return l(this,null,(function*(){return o.get({url:`/api/v1/admin/express/mappings/provider/${t}`,params:{_t:Date.now()}})}))}static createService(t){return l(this,null,(function*(){return o.post({url:"/api/v1/admin/express/services",data:t})}))}static getServicesByCompany(t){return l(this,null,(function*(){return o.get({url:`/api/v1/admin/express/services/company/${t}`,params:{_t:Date.now()}})}))}static updateService(t,e){return l(this,null,(function*(){return o.put({url:`/api/v1/admin/express/services/${t}`,data:e})}))}static deleteService(t){return l(this,null,(function*(){return o.del({url:`/api/v1/admin/express/services/${t}`})}))}static createConfig(t){return l(this,null,(function*(){return o.post({url:"/api/v1/admin/express/configs",data:t})}))}static getConfigsByCompany(t){return l(this,null,(function*(){return o.get({url:`/api/v1/admin/express/configs/company/${t}`,params:{_t:Date.now()}})}))}static getConfig(t,e){return l(this,null,(function*(){return o.get({url:`/api/v1/admin/express/configs/company/${t}/${e}`,params:{_t:Date.now()}})}))}static updateConfig(t,e){return l(this,null,(function*(){return o.put({url:`/api/v1/admin/express/configs/${t}`,data:e})}))}static deleteConfig(t){return l(this,null,(function*(){return o.del({url:`/api/v1/admin/express/configs/${t}`})}))}static getAuditLogs(t){return l(this,null,(function*(){return o.get({url:"/api/v1/admin/express/audit-logs",params:p(u({},t),{_t:Date.now()})})}))}static getActiveCompanies(){return l(this,null,(function*(){return o.get({url:"/api/v1/express/companies",params:{_t:Date.now()}})}))}static getActiveProviders(){return l(this,null,(function*(){return o.get({url:"/api/v1/express/providers",params:{_t:Date.now()}})}))}static getCompanyByCode(t){return l(this,null,(function*(){return o.get({url:`/api/v1/express/companies/${t}`,params:{_t:Date.now()}})}))}static getProviderByCode(t){return l(this,null,(function*(){return o.get({url:`/api/v1/express/providers/${t}`,params:{_t:Date.now()}})}))}static getProviderCompanyCode(t,e){return l(this,null,(function*(){return o.get({url:`/api/v1/express/mappings/provider-code/${t}/${e}`,params:{_t:Date.now()}})}))}}const d=[{label:"全部",value:void 0},{label:"启用",value:!0},{label:"禁用",value:!1}],m=[{label:"代码",value:"code"},{label:"名称",value:"name"},{label:"排序",value:"sort_order"},{label:"创建时间",value:"created_at"},{label:"更新时间",value:"updated_at"}],v=[{label:"升序",value:"ASC"},{label:"降序",value:"DESC"}];export{c as E,v as S,d as a,m as b};
