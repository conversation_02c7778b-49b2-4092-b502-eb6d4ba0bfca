var e=Object.defineProperty,a=Object.defineProperties,l=Object.getOwnPropertyDescriptors,t=Object.getOwnPropertySymbols,s=Object.prototype.hasOwnProperty,o=Object.prototype.propertyIsEnumerable,u=(a,l,t)=>l in a?e(a,l,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[l]=t,i=(e,a)=>{for(var l in a||(a={}))s.call(a,l)&&u(e,l,a[l]);if(t)for(var l of t(a))o.call(a,l)&&u(e,l,a[l]);return e},r=(e,t)=>a(e,l(t)),n=(e,a,l)=>new Promise(((t,s)=>{var o=e=>{try{i(l.next(e))}catch(a){s(a)}},u=e=>{try{i(l.throw(e))}catch(a){s(a)}},i=e=>e.done?t(e.value):Promise.resolve(e.value).then(o,u);i((l=l.apply(e,a)).next())}));import{_ as d}from"./index-rNRt1EuS.js";/* empty css                *//* empty css               */import{d as c,r as v,X as p,j as _,f as b,c as f,o as g,e as m,a as y,b as h,K as w,w as C,y as j,u as k,aQ as z,H as S,aR as F,aS as U,aN as D,aT as x,Z as O,_ as V,C as P,D as E,ar as A,F as B,A as L,N as R,as as I,aF as N,a3 as T,aU as H,aV as K,x as X,aW as $,am as G,a2 as J,aI as M,aJ as Q,aX as W,aY as Y,aG as Z,aH as q,M as ee}from"./vendor-CAPBtMef.js";import{S as ae}from"./StatCard-DtRQl2IM.js";import le from"./DepositDialog-BOdK2s8G.js";import te from"./AdjustDialog-I97FHFJq.js";import se from"./RefundDialog-BGaw8-eE.js";import oe from"./BatchDepositDialog-UM9tSWks.js";import ue from"./BatchAdjustDialog-CY-yLWVl.js";import ie from"./UserDetailDialog-ia2htbXj.js";import{B as re,a as ne}from"./balanceApi-B8_MfzSO.js";/* empty css                       *//* empty css                 *//* empty css                 */const de={class:"balance-management"},ce={class:"page-header"},ve={class:"page-actions"},pe={key:0,class:"overview-cards"},_e={class:"search-filters"},be={key:1,class:"batch-operations"},fe={class:"batch-actions"},ge={class:"selected-info"},me={class:"balance-list"},ye={class:"user-name"},he={class:"user-email"},we={class:"balance-amount"},Ce={class:"available-balance"},je={class:"pagination-wrapper"},ke=d(c({__name:"index",setup(e){const a=v(!1),l=v(!1),t=v(!1),s=v(!1),o=v(!1),u=v(!1),d=v(!1),c=v(!1),ke=v(null),ze=v([]),Se=v([]),Fe=v(null),Ue=v(null),De=p({page:1,page_size:20,keyword:void 0,status:void 0,order_by:"created_at",order:"desc"}),xe=p({page:1,pageSize:20,total:0}),Oe=_((()=>Se.value.map((e=>e.user_id)))),Ve=()=>n(this,null,(function*(){try{const e=yield ne.getBalanceOverview();e.success&&(ke.value=e.data)}catch(e){}})),Pe=()=>n(this,null,(function*(){try{a.value=!0;const e=r(i({},De),{page:xe.page,page_size:xe.pageSize}),l=yield ne.getUserBalanceList(e);l.success?(ze.value=l.data.items,xe.total=l.data.total):ee.error(l.message||"获取余额列表失败")}catch(e){ee.error("获取余额列表失败")}finally{a.value=!1}})),Ee=()=>{Ve(),Pe()},Ae=()=>{xe.page=1,Pe()},Be=()=>{Object.assign(De,{page:1,page_size:20,keyword:void 0,status:void 0,order_by:"created_at",order:"desc"}),xe.page=1,xe.pageSize=20,Pe(),ee.success("搜索条件已重置")},Le=e=>{Se.value=e},Re=()=>{Se.value=[]},Ie=({prop:e,order:a})=>{a?(De.order_by=e,De.order="ascending"===a?"asc":"desc"):(De.order_by="created_at",De.order="desc"),Pe()},Ne=e=>{xe.pageSize=e,xe.page=1,Pe()},Te=e=>{xe.page=e,Pe()},He=()=>{t.value=!1,s.value=!1,o.value=!1,Ee(),ee.success("操作成功")},Ke=()=>{u.value=!1,d.value=!1,Re(),Ee(),ee.success("批量操作成功")},Xe=()=>n(this,null,(function*(){try{l.value=!0;const e=r(i({},De),{page:1,page_size:1e4}),a=yield ne.exportUserBalances(e),t=window.URL.createObjectURL(a),s=document.createElement("a");s.href=t,s.download=`user_balances_${(new Date).getTime()}.xlsx`,document.body.appendChild(s),s.click(),document.body.removeChild(s),window.URL.revokeObjectURL(t),ee.success("导出成功")}catch(e){ee.error("导出失败")}finally{l.value=!1}})),$e=e=>{const a=re.find((a=>a.value===e));return(null==a?void 0:a.label)||e},Ge=e=>{const a="string"==typeof e?parseFloat(e):e;return`¥${(null==a?void 0:a.toFixed(2))||"0.00"}`},Je=e=>e?new Date(e).toLocaleString("zh-CN"):"-";return b((()=>{Ee()})),(e,i)=>{const r=j,n=S,v=D,p=x,_=P,b=V,ee=I,ne=A,Ve=N,Pe=O,Me=K,Qe=Q,We=W,Ye=M,Ze=q,qe=Z;return g(),f("div",de,[m("div",ce,[i[17]||(i[17]=m("div",{class:"header-left"},[m("h1",{class:"page-title"},[m("i",{class:"iconfont-sys"},""),w(" 余额管理 ")]),m("p",{class:"page-description"},"管理用户余额、充值、调整和统计")],-1)),m("div",ve,[h(n,{onClick:Ee,loading:a.value},{default:C((()=>[h(r,null,{default:C((()=>[h(k(z))])),_:1}),i[14]||(i[14]=w(" 刷新 "))])),_:1},8,["loading"]),h(n,{type:"primary",onClick:i[0]||(i[0]=e=>t.value=!0)},{default:C((()=>[h(r,null,{default:C((()=>[h(k(F))])),_:1}),i[15]||(i[15]=w(" 手动充值 "))])),_:1}),h(n,{type:"success",onClick:Xe,loading:l.value},{default:C((()=>[h(r,null,{default:C((()=>[h(k(U))])),_:1}),i[16]||(i[16]=w(" 导出 "))])),_:1},8,["loading"])])]),ke.value?(g(),f("div",pe,[h(p,{gutter:20},{default:C((()=>[h(v,{span:6},{default:C((()=>[h(ae,{title:"总用户数",value:ke.value.total_users,icon:"el-icon-user","icon-color":"#409EFF","icon-bg-color":"#E6F7FF"},null,8,["value"])])),_:1}),h(v,{span:6},{default:C((()=>[h(ae,{title:"活跃用户",value:ke.value.active_users,icon:"el-icon-user-solid","icon-color":"#67C23A","icon-bg-color":"#F0F9FF"},null,8,["value"])])),_:1}),h(v,{span:6},{default:C((()=>[h(ae,{title:"总余额",value:Ge(ke.value.total_balance),icon:"el-icon-coin","icon-color":"#E6A23C","icon-bg-color":"#FDF6EC"},null,8,["value"])])),_:1}),h(v,{span:6},{default:C((()=>[h(ae,{title:"平均余额",value:Ge(ke.value.avg_balance),icon:"el-icon-wallet","icon-color":"#F56C6C","icon-bg-color":"#FEF0F0"},null,8,["value"])])),_:1})])),_:1})])):y("",!0),m("div",_e,[h(Me,null,{default:C((()=>[h(Pe,{model:De,"label-width":"80px"},{default:C((()=>[h(p,{gutter:20},{default:C((()=>[h(v,{span:6},{default:C((()=>[h(b,{label:"关键词"},{default:C((()=>[h(_,{modelValue:De.keyword,"onUpdate:modelValue":i[1]||(i[1]=e=>De.keyword=e),placeholder:"用户名/邮箱",clearable:"",onKeyup:E(Ae,["enter"]),style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1}),h(v,{span:6},{default:C((()=>[h(b,{label:"余额状态"},{default:C((()=>[h(ne,{modelValue:De.status,"onUpdate:modelValue":i[2]||(i[2]=e=>De.status=e),placeholder:"请选择状态",clearable:"",style:{width:"100%"}},{default:C((()=>[(g(!0),f(B,null,L(k(re),(e=>(g(),R(ee,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1})])),_:1}),h(v,{span:6},{default:C((()=>[h(b,{label:"排序方式"},{default:C((()=>[h(ne,{modelValue:De.order_by,"onUpdate:modelValue":i[3]||(i[3]=e=>De.order_by=e),placeholder:"请选择排序",clearable:"",style:{width:"100%"}},{default:C((()=>[h(ee,{label:"余额",value:"balance"}),h(ee,{label:"创建时间",value:"created_at"}),h(ee,{label:"更新时间",value:"updated_at"}),h(ee,{label:"交易次数",value:"transaction_count"})])),_:1},8,["modelValue"])])),_:1})])),_:1}),h(v,{span:6},{default:C((()=>[h(b,{label:" "},{default:C((()=>[h(Ve,null,{default:C((()=>[h(n,{type:"primary",onClick:Ae,loading:a.value},{default:C((()=>[h(r,null,{default:C((()=>[h(k(T))])),_:1}),i[18]||(i[18]=w(" 搜索 "))])),_:1},8,["loading"]),h(n,{onClick:Be},{default:C((()=>[h(r,null,{default:C((()=>[h(k(H))])),_:1}),i[19]||(i[19]=w(" 重置 "))])),_:1})])),_:1})])),_:1})])),_:1})])),_:1})])),_:1},8,["model"])])),_:1})]),Se.value.length>0?(g(),f("div",be,[h(Me,null,{default:C((()=>[m("div",fe,[m("span",ge,"已选择 "+X(Se.value.length)+" 个用户",1),h(Ve,null,{default:C((()=>[h(n,{type:"primary",onClick:i[4]||(i[4]=e=>u.value=!0)},{default:C((()=>[h(r,null,{default:C((()=>[h(k(F))])),_:1}),i[20]||(i[20]=w(" 批量充值 "))])),_:1}),h(n,{type:"warning",onClick:i[5]||(i[5]=e=>d.value=!0)},{default:C((()=>[h(r,null,{default:C((()=>[h(k($))])),_:1}),i[21]||(i[21]=w(" 批量调整 "))])),_:1}),h(n,{type:"danger",onClick:Re},{default:C((()=>[h(r,null,{default:C((()=>[h(k(G))])),_:1}),i[22]||(i[22]=w(" 取消选择 "))])),_:1})])),_:1})])])),_:1})])):y("",!0),m("div",me,[h(Me,null,{default:C((()=>[J((g(),R(Ye,{ref:"balanceTable",data:ze.value,onSelectionChange:Le,onSortChange:Ie,stripe:"",border:"",style:{width:"100%"}},{default:C((()=>[h(Qe,{type:"selection",width:"55"}),h(Qe,{prop:"user_id",label:"用户ID",width:"120","show-overflow-tooltip":""}),h(Qe,{label:"用户信息",width:"200"},{default:C((({row:e})=>[m("div",null,[m("div",ye,X(e.username),1),m("div",he,X(e.email),1)])])),_:1}),h(Qe,{prop:"balance",label:"余额",width:"120",sortable:"custom"},{default:C((({row:e})=>[m("div",we,X(Ge(e.balance)),1)])),_:1}),h(Qe,{prop:"available_balance",label:"可用余额",width:"120"},{default:C((({row:e})=>[m("div",Ce,X(Ge(e.available_balance)),1)])),_:1}),h(Qe,{label:"状态",width:"100"},{default:C((({row:e})=>{return[h(We,{type:(a=e.status,{active:"success",frozen:"warning",disabled:"danger"}[a]||"info")},{default:C((()=>[w(X($e(e.status)),1)])),_:2},1032,["type"])];var a})),_:1}),h(Qe,{prop:"transaction_count",label:"交易次数",width:"100",sortable:"custom"}),h(Qe,{prop:"last_transaction_at",label:"最后交易",width:"160"},{default:C((({row:e})=>[w(X(Je(e.last_transaction_at)),1)])),_:1}),h(Qe,{prop:"created_at",label:"创建时间",width:"160",sortable:"custom"},{default:C((({row:e})=>[w(X(Je(e.created_at)),1)])),_:1}),h(Qe,{label:"操作",width:"280",fixed:"right"},{default:C((({row:e})=>[h(Ve,null,{default:C((()=>[h(n,{type:"primary",size:"small",onClick:a=>{return l=e.user_id,Ue.value=l,void(c.value=!0);var l}},{default:C((()=>[h(r,null,{default:C((()=>[h(k(Y))])),_:1}),i[23]||(i[23]=w(" 详情 "))])),_:2},1032,["onClick"]),h(n,{type:"success",size:"small",onClick:a=>{return l=e,Fe.value=l,void(t.value=!0);var l}},{default:C((()=>[h(r,null,{default:C((()=>[h(k(F))])),_:1}),i[24]||(i[24]=w(" 充值 "))])),_:2},1032,["onClick"]),h(n,{type:"warning",size:"small",onClick:a=>{return l=e,Fe.value=l,void(s.value=!0);var l}},{default:C((()=>[h(r,null,{default:C((()=>[h(k($))])),_:1}),i[25]||(i[25]=w(" 调整 "))])),_:2},1032,["onClick"]),h(n,{type:"danger",size:"small",onClick:a=>{return l=e,Fe.value=l,void(o.value=!0);var l}},{default:C((()=>[h(r,null,{default:C((()=>[h(k(H))])),_:1}),i[26]||(i[26]=w(" 退款 "))])),_:2},1032,["onClick"])])),_:2},1024)])),_:1})])),_:1},8,["data"])),[[qe,a.value]]),m("div",je,[h(Ze,{"current-page":xe.page,"onUpdate:currentPage":i[6]||(i[6]=e=>xe.page=e),"page-size":xe.pageSize,"onUpdate:pageSize":i[7]||(i[7]=e=>xe.pageSize=e),"page-sizes":[10,20,50,100],total:xe.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:Ne,onCurrentChange:Te},null,8,["current-page","page-size","total"])])])),_:1})]),h(le,{visible:t.value,"onUpdate:visible":i[8]||(i[8]=e=>t.value=e),user:Fe.value,onSuccess:He},null,8,["visible","user"]),h(te,{visible:s.value,"onUpdate:visible":i[9]||(i[9]=e=>s.value=e),user:Fe.value,onSuccess:He},null,8,["visible","user"]),h(se,{visible:o.value,"onUpdate:visible":i[10]||(i[10]=e=>o.value=e),user:Fe.value,onSuccess:He},null,8,["visible","user"]),h(oe,{visible:u.value,"onUpdate:visible":i[11]||(i[11]=e=>u.value=e),"user-ids":Oe.value,onSuccess:Ke},null,8,["visible","user-ids"]),h(ue,{visible:d.value,"onUpdate:visible":i[12]||(i[12]=e=>d.value=e),"user-ids":Oe.value,onSuccess:Ke},null,8,["visible","user-ids"]),h(ie,{visible:c.value,"onUpdate:visible":i[13]||(i[13]=e=>c.value=e),"user-id":Ue.value},null,8,["visible","user-id"])])}}}),[["__scopeId","data-v-c3f6dfc4"]]);export{ke as default};
