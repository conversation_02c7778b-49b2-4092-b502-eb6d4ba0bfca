var e=(e,s,a)=>new Promise(((l,t)=>{var i=e=>{try{d(a.next(e))}catch(s){t(s)}},u=e=>{try{d(a.throw(e))}catch(s){t(s)}},d=e=>e.done?l(e.value):Promise.resolve(e.value).then(i,u);d((a=a.apply(e,s)).next())}));import{_ as s}from"./index-rNRt1EuS.js";/* empty css                 *//* empty css                    *//* empty css               */import{d as a,bA as l,r as t,j as i,p as u,s as d,N as r,o,w as v,a2 as c,c as n,a as p,e as m,b as g,Z as f,aT as h,aN as b,_ as y,ar as _,as as x,x as Q,aX as C,K as k,b8 as w,a$ as R,aG as M,H as T,y as V,u as j,aQ as q,Y as P,ai as $,M as D}from"./vendor-CAPBtMef.js";import{F as U}from"./flexiblePriceTableApi-BBM6dCg8.js";const B={class:"dialog-header"},F={class:"header-icon"},I={class:"dialog-body","element-loading-text":"正在启动更新任务..."},O={key:0,class:"config-card"},Y={class:"card-body"},z={key:1,class:"estimate-card"},A={class:"card-body"},J={class:"estimate-grid"},N={class:"estimate-item"},S={class:"estimate-value"},Z={class:"estimate-item"},E={class:"estimate-value"},G={class:"estimate-item"},H={class:"estimate-value highlight"},K={class:"estimate-item"},X={class:"estimate-value"},L={key:2,class:"progress-card"},W={class:"card-header"},ee={class:"card-body"},se={class:"progress-section"},ae={class:"progress-text"},le={class:"stats-grid"},te={class:"stat-item"},ie={class:"stat-value"},ue={class:"stat-item"},de={class:"stat-value"},re={class:"stat-item"},oe={class:"stat-value"},ve={class:"stat-item"},ce={class:"stat-value"},ne={key:0,class:"error-section"},pe={class:"dialog-footer"},me=s(a({__name:"UpdatePriceDialogV2",props:{visible:{type:Boolean}},emits:["update:visible","success"],setup(s,{emit:a}){const me=s,ge=a,{visible:fe}=l(me),he=t(!1),be=t(null),ye=t({providers:[],expressCodes:[]}),_e=t({id:"",name:"",status:"",progress:0,totalQueries:0,completedQueries:0,successQueries:0,failedQueries:0,errorMessage:"",isRunning:!1,isCompleted:!1}),xe=i((()=>ye.value.providers.length>0&&ye.value.expressCodes.length>0)),Qe=i((()=>ye.value.providers.length*ye.value.expressCodes.length*961)),Ce=i((()=>{const e=200*Qe.value,s=Math.ceil(e/1e3);if(s<60)return`约${s}秒`;if(s<3600){return`约${Math.floor(s/60)}分钟`}return`约${Math.floor(s/3600)}小时${Math.floor(s%3600/60)}分钟`})),ke=i((()=>({pending:"等待中",running:"运行中",completed:"已完成",failed:"已失败",cancelled:"已取消"}[_e.value.status]||_e.value.status))),we=i((()=>({pending:"info",running:"warning",completed:"success",failed:"danger",cancelled:"info"}[_e.value.status]||"info"))),Re=i((()=>"completed"===_e.value.status?"success":"failed"===_e.value.status?"exception":void 0));u(fe,(e=>{e?Me():Pe()}));const Me=()=>{ye.value={providers:[],expressCodes:[]},_e.value={id:"",name:"",status:"",progress:0,totalQueries:0,completedQueries:0,successQueries:0,failedQueries:0,errorMessage:"",isRunning:!1,isCompleted:!1}},Te=()=>{_e.value.isRunning?$.confirm("更新任务正在进行中，确定要关闭吗？","确认关闭",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{Pe(),ge("update:visible",!1)})).catch((()=>{})):ge("update:visible",!1)},Ve=()=>e(this,null,(function*(){if(xe.value)try{he.value=!0;const e=yield U.updatePriceData({providers:ye.value.providers,express_codes:ye.value.expressCodes});e.success&&e.data?(je(e.data),_e.value.isRunning=!0,D.success("更新任务已启动"),qe()):D.error(e.message||"启动更新任务失败")}catch(e){D.error("提交失败")}finally{he.value=!1}})),je=e=>{_e.value.id=e.id||"",_e.value.name=e.task_name||"更新任务",_e.value.status=e.status||"pending",_e.value.progress=e.progress||0,_e.value.totalQueries=e.total_queries||0,_e.value.completedQueries=e.completed_queries||0,_e.value.successQueries=e.success_queries||0,_e.value.failedQueries=e.failed_queries||0,_e.value.errorMessage=e.error_message||"",_e.value.isCompleted="completed"===e.status},qe=()=>{Pe(),be.value=setInterval((()=>e(this,null,(function*(){if(_e.value.id)try{const e=yield U.getTaskStatus(_e.value.id);e.success&&e.data&&(je(e.data),"completed"===_e.value.status?(_e.value.isRunning=!1,Pe(),D.success("价格数据更新完成"),ge("success")):"failed"===_e.value.status&&(_e.value.isRunning=!1,Pe(),D.error("价格数据更新失败")))}catch(e){Pe(),_e.value.isRunning=!1}}))),2e3)},Pe=()=>{be.value&&(clearInterval(be.value),be.value=null)};return d((()=>{Pe()})),(e,s)=>{const a=V,l=x,t=_,i=y,u=b,d=h,$=f,D=C,U=w,me=R,be=T,Me=P,je=M;return o(),r(Me,{"model-value":j(fe),"onUpdate:modelValue":s[2]||(s[2]=e=>ge("update:visible",e)),title:"",width:"680px","close-on-click-modal":!1,"close-on-press-escape":!1,class:"update-dialog",onClose:Te},{header:v((()=>[m("div",B,[m("div",F,[g(a,{size:"24",color:"#409EFF"},{default:v((()=>[g(j(q))])),_:1})]),s[3]||(s[3]=m("div",{class:"header-content"},[m("h3",{class:"header-title"},"更新价格数据"),m("p",{class:"header-subtitle"},"选择供应商和快递公司进行价格数据更新")],-1))])])),footer:v((()=>[m("div",pe,[g(be,{onClick:Te,disabled:he.value},{default:v((()=>s[14]||(s[14]=[k(" 取消 ")]))),_:1},8,["disabled"]),_e.value.isRunning?p("",!0):(o(),r(be,{key:0,type:"primary",onClick:Ve,loading:he.value,disabled:!xe.value},{default:v((()=>s[15]||(s[15]=[k(" 开始更新 ")]))),_:1},8,["loading","disabled"])),_e.value.isCompleted?(o(),r(be,{key:1,type:"success",onClick:Te},{default:v((()=>s[16]||(s[16]=[k(" 完成 ")]))),_:1})):p("",!0)])])),default:v((()=>[c((o(),n("div",I,[_e.value.isRunning?p("",!0):(o(),n("div",O,[s[4]||(s[4]=m("div",{class:"card-header"},[m("h4",null,"更新配置")],-1)),m("div",Y,[g($,{model:ye.value,"label-position":"top",class:"config-form"},{default:v((()=>[g(d,{gutter:20},{default:v((()=>[g(u,{span:12},{default:v((()=>[g(i,{label:"供应商",required:""},{default:v((()=>[g(t,{modelValue:ye.value.providers,"onUpdate:modelValue":s[0]||(s[0]=e=>ye.value.providers=e),placeholder:"选择供应商",multiple:"","collapse-tags":"","collapse-tags-tooltip":"",style:{width:"100%"}},{default:v((()=>[g(l,{label:"易达",value:"yida"}),g(l,{label:"云通",value:"yuntong"}),g(l,{label:"快递100",value:"kuaidi100"})])),_:1},8,["modelValue"])])),_:1})])),_:1}),g(u,{span:12},{default:v((()=>[g(i,{label:"快递公司",required:""},{default:v((()=>[g(t,{modelValue:ye.value.expressCodes,"onUpdate:modelValue":s[1]||(s[1]=e=>ye.value.expressCodes=e),placeholder:"选择快递公司",multiple:"","collapse-tags":"","collapse-tags-tooltip":"",style:{width:"100%"}},{default:v((()=>[g(l,{label:"圆通速递",value:"YTO"}),g(l,{label:"申通快递",value:"STO"}),g(l,{label:"韵达速递",value:"YD"}),g(l,{label:"中通快递",value:"ZTO"}),g(l,{label:"极兔速递",value:"JT"}),g(l,{label:"京东物流",value:"JD"})])),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model"])])])),!_e.value.isRunning&&xe.value?(o(),n("div",z,[s[9]||(s[9]=m("div",{class:"card-header"},[m("h4",null,"预估信息")],-1)),m("div",A,[m("div",J,[m("div",N,[s[5]||(s[5]=m("div",{class:"estimate-label"},"供应商数量",-1)),m("div",S,Q(ye.value.providers.length),1)]),m("div",Z,[s[6]||(s[6]=m("div",{class:"estimate-label"},"快递公司数量",-1)),m("div",E,Q(ye.value.expressCodes.length),1)]),m("div",G,[s[7]||(s[7]=m("div",{class:"estimate-label"},"预估查询数",-1)),m("div",H,Q(Qe.value),1)]),m("div",K,[s[8]||(s[8]=m("div",{class:"estimate-label"},"预估时间",-1)),m("div",X,Q(Ce.value),1)])])])])):p("",!0),_e.value.isRunning?(o(),n("div",L,[m("div",W,[m("h4",null,Q(_e.value.name),1),g(D,{type:we.value,size:"small"},{default:v((()=>[k(Q(ke.value),1)])),_:1},8,["type"])]),m("div",ee,[m("div",se,[g(U,{percentage:_e.value.progress,status:Re.value,"stroke-width":12,"show-text":!1},null,8,["percentage","status"]),m("div",ae,Q(_e.value.progress)+"%",1)]),m("div",le,[m("div",te,[m("div",ie,Q(_e.value.totalQueries),1),s[10]||(s[10]=m("div",{class:"stat-label"},"总查询",-1))]),m("div",ue,[m("div",de,Q(_e.value.completedQueries),1),s[11]||(s[11]=m("div",{class:"stat-label"},"已完成",-1))]),m("div",re,[m("div",oe,Q(_e.value.successQueries),1),s[12]||(s[12]=m("div",{class:"stat-label"},"成功",-1))]),m("div",ve,[m("div",ce,Q(_e.value.failedQueries),1),s[13]||(s[13]=m("div",{class:"stat-label"},"失败",-1))])]),_e.value.errorMessage?(o(),n("div",ne,[g(me,{title:_e.value.errorMessage,type:"error",closable:!1,"show-icon":""},null,8,["title"])])):p("",!0)])])):p("",!0)])),[[je,he.value]])])),_:1},8,["model-value"])}}}),[["__scopeId","data-v-01c50237"]]);export{me as default};
