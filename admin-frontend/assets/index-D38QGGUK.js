var e=Object.defineProperty,a=Object.getOwnPropertySymbols,t=Object.prototype.hasOwnProperty,l=Object.prototype.propertyIsEnumerable,s=(a,t,l)=>t in a?e(a,t,{enumerable:!0,configurable:!0,writable:!0,value:l}):a[t]=l,r=(e,r)=>{for(var d in r||(r={}))t.call(r,d)&&s(e,d,r[d]);if(a)for(var d of a(r))l.call(r,d)&&s(e,d,r[d]);return e},d=(e,a,t)=>new Promise(((l,s)=>{var r=e=>{try{i(t.next(e))}catch(a){s(a)}},d=e=>{try{i(t.throw(e))}catch(a){s(a)}},i=e=>e.done?l(e.value):Promise.resolve(e.value).then(r,d);i((t=t.apply(e,a)).next())}));import{_ as i}from"./index-rNRt1EuS.js";/* empty css                    *//* empty css                    *//* empty css               *//* empty css                *//* empty css                       */import{d as o,r as n,X as c,j as u,f as p,g as v,c as f,o as _,e as h,b as m,w as b,K as y,u as g,aQ as w,H as x,Z as k,_ as j,b0 as C,ar as V,as as O,C as S,D as z,aV as Y,aN as D,y as T,b3 as A,x as H,b4 as I,ao as M,b5 as P,aT as U,b6 as F,b7 as X,aI as E,aJ as R,aX as W,b8 as B,M as K}from"./vendor-CAPBtMef.js";import"./index-DZFhRe5N.js";import{A as L}from"./adminCallbackApi-C-jDof5v.js";import{i as q}from"./install-C7WOJgjY.js";const G={class:"callback-statistics"},J={class:"page-header"},N={class:"header-right"},Q={class:"stat-content"},Z={class:"stat-icon success"},$={class:"stat-info"},ee={class:"stat-value"},ae={class:"stat-content"},te={class:"stat-icon success"},le={class:"stat-info"},se={class:"stat-value"},re={class:"stat-content"},de={class:"stat-icon danger"},ie={class:"stat-info"},oe={class:"stat-value"},ne={class:"stat-content"},ce={class:"stat-icon warning"},ue={class:"stat-info"},pe={class:"stat-value"},ve={class:"chart-container"},fe={class:"chart-container"},_e={class:"chart-container"},he={class:"status-progress"},me={class:"progress-text"},be={class:"status-progress"},ye={class:"progress-text"},ge={class:"status-progress"},we={class:"progress-text"},xe=i(o({__name:"index",setup(e){const a=n(!1),t=n({total_records:0,success_records:0,failed_records:0,pending_records:0,success_rate:0,avg_processing_time:0,provider_stats:{},event_type_stats:{},daily_stats:[]}),l=n(null),s=n("provider"),i=c({provider:"",event_type:"",user_id:""}),o=n(),xe=n(),ke=n();let je=null,Ce=null,Ve=null;const Oe=u((()=>Object.entries(t.value.provider_stats||{}).map((([e,a])=>r({provider:e},a))))),Se=u((()=>Object.entries(t.value.event_type_stats||{}).map((([e,a])=>r({event_type:e},a))))),ze=u((()=>t.value.daily_stats||[])),Ye=e=>`${e.toFixed(2)}%`,De=e=>({yuntong:"云通",yida:"易达",kuaidi100:"快递100"}[e]||e),Te=e=>({order_status_changed:"订单状态变更",billing_updated:"计费更新",ticket_replied:"工单回复"}[e]||e),Ae=()=>d(this,null,(function*(){try{a.value=!0;const e={provider:i.provider,event_type:i.event_type,user_id:i.user_id};l.value&&2===l.value.length&&(e.start_time=l.value[0],e.end_time=l.value[1]);const s=yield L.getCallbackStatistics(e);t.value=s,yield v(),He()}catch(e){K.error(e.message||"加载回调统计失败")}finally{a.value=!1}})),He=()=>{Ie(),Me(),Pe()},Ie=()=>{if(!je)return;const e=Oe.value.map((e=>({name:De(e.provider),value:e.total}))),a={title:{text:"供应商分布",left:"center",textStyle:{fontSize:14,fontWeight:"normal"}},tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",left:"left",data:e.map((e=>e.name))},series:[{name:"回调数量",type:"pie",radius:"50%",data:e,emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]};je.setOption(a)},Me=()=>{if(!Ce)return;const e=Se.value.map((e=>({name:Te(e.event_type),value:e.total}))),a={title:{text:"事件类型分布",left:"center",textStyle:{fontSize:14,fontWeight:"normal"}},tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",left:"left",data:e.map((e=>e.name))},series:[{name:"回调数量",type:"pie",radius:"50%",data:e,emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]};Ce.setOption(a)},Pe=()=>{if(!Ve)return;const e={title:{text:"回调趋势",left:"center",textStyle:{fontSize:14,fontWeight:"normal"}},tooltip:{trigger:"axis"},legend:{data:["总数","成功","失败"],top:30},grid:{left:"3%",right:"4%",bottom:"3%",top:"15%",containLabel:!0},xAxis:{type:"category",boundaryGap:!1,data:ze.value.map((e=>e.date))},yAxis:{type:"value"},series:[{name:"总数",type:"line",stack:"Total",data:ze.value.map((e=>e.total)),itemStyle:{color:"#409EFF"}},{name:"成功",type:"line",stack:"Total",data:ze.value.map((e=>e.success)),itemStyle:{color:"#67C23A"}},{name:"失败",type:"line",stack:"Total",data:ze.value.map((e=>e.failed)),itemStyle:{color:"#F56C6C"}}]};Ve.setOption(e)},Ue=()=>{Ae()},Fe=()=>{Ae()},Xe=()=>{Object.assign(i,{provider:"",event_type:"",user_id:""}),l.value=null,Ae()},Ee=()=>{Ae()},Re=()=>{};return p((()=>d(this,null,(function*(){yield v(),o.value&&(je=q(o.value)),xe.value&&(Ce=q(xe.value)),ke.value&&(Ve=q(ke.value)),window.addEventListener("resize",(()=>{null==je||je.resize(),null==Ce||Ce.resize(),null==Ve||Ve.resize()})),yield Ae()})))),(e,a)=>{const r=x,d=C,n=j,c=O,u=V,p=S,v=k,K=Y,L=T,q=D,je=U,Ce=W,Ve=R,Ae=B,He=E,Ie=X,Me=F;return _(),f("div",G,[h("div",J,[a[6]||(a[6]=h("div",{class:"header-left"},[h("h1",{class:"page-title"},"回调统计监控"),h("p",{class:"page-description"},"监控回调系统的运行状态和性能指标")],-1)),h("div",N,[m(r,{type:"success",icon:g(w),onClick:Ee},{default:b((()=>a[5]||(a[5]=[y(" 刷新数据 ")]))),_:1},8,["icon"])])]),m(K,{class:"filter-card"},{default:b((()=>[m(v,{model:i,inline:""},{default:b((()=>[m(n,{label:"时间范围"},{default:b((()=>[m(d,{modelValue:l.value,"onUpdate:modelValue":a[0]||(a[0]=e=>l.value=e),type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",style:{width:"350px"},onChange:Ue},null,8,["modelValue"])])),_:1}),m(n,{label:"供应商"},{default:b((()=>[m(u,{modelValue:i.provider,"onUpdate:modelValue":a[1]||(a[1]=e=>i.provider=e),placeholder:"请选择供应商",clearable:"",style:{width:"150px"},onChange:Fe},{default:b((()=>[m(c,{label:"全部",value:""}),m(c,{label:"云通",value:"yuntong"}),m(c,{label:"易达",value:"yida"}),m(c,{label:"快递100",value:"kuaidi100"})])),_:1},8,["modelValue"])])),_:1}),m(n,{label:"事件类型"},{default:b((()=>[m(u,{modelValue:i.event_type,"onUpdate:modelValue":a[2]||(a[2]=e=>i.event_type=e),placeholder:"请选择事件类型",clearable:"",style:{width:"150px"},onChange:Fe},{default:b((()=>[m(c,{label:"全部",value:""}),m(c,{label:"订单状态变更",value:"order_status_changed"}),m(c,{label:"计费更新",value:"billing_updated"}),m(c,{label:"工单回复",value:"ticket_replied"})])),_:1},8,["modelValue"])])),_:1}),m(n,{label:"用户ID"},{default:b((()=>[m(p,{modelValue:i.user_id,"onUpdate:modelValue":a[3]||(a[3]=e=>i.user_id=e),placeholder:"请输入用户ID",clearable:"",style:{width:"200px"},onKeyup:z(Fe,["enter"])},null,8,["modelValue"])])),_:1}),m(n,null,{default:b((()=>[m(r,{type:"primary",onClick:Fe},{default:b((()=>a[7]||(a[7]=[y(" 查询 ")]))),_:1}),m(r,{onClick:Xe},{default:b((()=>a[8]||(a[8]=[y(" 重置 ")]))),_:1})])),_:1})])),_:1},8,["model"])])),_:1}),m(je,{gutter:20,class:"stats-cards"},{default:b((()=>[m(q,{span:6},{default:b((()=>[m(K,{class:"stat-card"},{default:b((()=>[h("div",Q,[h("div",Z,[m(L,null,{default:b((()=>[m(g(A))])),_:1})]),h("div",$,[h("div",ee,H(t.value.total_records||0),1),a[9]||(a[9]=h("div",{class:"stat-label"},"总回调数",-1))])])])),_:1})])),_:1}),m(q,{span:6},{default:b((()=>[m(K,{class:"stat-card"},{default:b((()=>[h("div",ae,[h("div",te,[m(L,null,{default:b((()=>[m(g(I))])),_:1})]),h("div",le,[h("div",se,H(t.value.success_records||0),1),a[10]||(a[10]=h("div",{class:"stat-label"},"成功回调",-1))])])])),_:1})])),_:1}),m(q,{span:6},{default:b((()=>[m(K,{class:"stat-card"},{default:b((()=>[h("div",re,[h("div",de,[m(L,null,{default:b((()=>[m(g(M))])),_:1})]),h("div",ie,[h("div",oe,H(t.value.failed_records||0),1),a[11]||(a[11]=h("div",{class:"stat-label"},"失败回调",-1))])])])),_:1})])),_:1}),m(q,{span:6},{default:b((()=>[m(K,{class:"stat-card"},{default:b((()=>[h("div",ne,[h("div",ce,[m(L,null,{default:b((()=>[m(g(P))])),_:1})]),h("div",ue,[h("div",pe,H(Ye(t.value.success_rate)),1),a[12]||(a[12]=h("div",{class:"stat-label"},"成功率",-1))])])])),_:1})])),_:1})])),_:1}),m(je,{gutter:20,class:"charts-section"},{default:b((()=>[m(q,{span:12},{default:b((()=>[m(K,{class:"chart-card"},{header:b((()=>a[13]||(a[13]=[h("div",{class:"card-header"},[h("span",null,"供应商回调统计")],-1)]))),default:b((()=>[h("div",ve,[h("div",{ref_key:"providerChartRef",ref:o,class:"chart",style:{height:"300px"}},null,512)])])),_:1})])),_:1}),m(q,{span:12},{default:b((()=>[m(K,{class:"chart-card"},{header:b((()=>a[14]||(a[14]=[h("div",{class:"card-header"},[h("span",null,"事件类型统计")],-1)]))),default:b((()=>[h("div",fe,[h("div",{ref_key:"eventTypeChartRef",ref:xe,class:"chart",style:{height:"300px"}},null,512)])])),_:1})])),_:1})])),_:1}),m(je,{gutter:20,class:"trend-section"},{default:b((()=>[m(q,{span:24},{default:b((()=>[m(K,{class:"chart-card"},{header:b((()=>a[15]||(a[15]=[h("div",{class:"card-header"},[h("span",null,"回调趋势图")],-1)]))),default:b((()=>[h("div",_e,[h("div",{ref_key:"trendChartRef",ref:ke,class:"chart",style:{height:"400px"}},null,512)])])),_:1})])),_:1})])),_:1}),m(K,{class:"table-card"},{header:b((()=>a[16]||(a[16]=[h("div",{class:"card-header"},[h("span",null,"详细统计数据")],-1)]))),default:b((()=>[m(Me,{modelValue:s.value,"onUpdate:modelValue":a[4]||(a[4]=e=>s.value=e),onTabChange:Re},{default:b((()=>[m(Ie,{label:"供应商统计",name:"provider"},{default:b((()=>[m(He,{data:Oe.value,stripe:"",border:""},{default:b((()=>[m(Ve,{prop:"provider",label:"供应商",width:"120"},{default:b((({row:e})=>{return[m(Ce,{type:(a=e.provider,{yuntong:"primary",yida:"success",kuaidi100:"warning"}[a]||"info"),size:"small"},{default:b((()=>[y(H(De(e.provider)),1)])),_:2},1032,["type"])];var a})),_:1}),m(Ve,{prop:"total",label:"总数",width:"100"}),m(Ve,{prop:"success",label:"成功",width:"100"}),m(Ve,{prop:"failed",label:"失败",width:"100"}),m(Ve,{prop:"pending",label:"处理中",width:"100"}),m(Ve,{label:"成功率",width:"120"},{default:b((({row:e})=>[y(H(Ye(e.total>0?e.success/e.total*100:0)),1)])),_:1}),m(Ve,{label:"状态分布","min-width":"200"},{default:b((({row:e})=>[h("div",he,[m(Ae,{percentage:e.total>0?e.success/e.total*100:0,"stroke-width":8,"show-text":!1,status:"success"},null,8,["percentage"]),h("span",me," 成功: "+H(e.success)+" | 失败: "+H(e.failed)+" | 处理中: "+H(e.pending),1)])])),_:1})])),_:1},8,["data"])])),_:1}),m(Ie,{label:"事件类型统计",name:"event_type"},{default:b((()=>[m(He,{data:Se.value,stripe:"",border:""},{default:b((()=>[m(Ve,{prop:"event_type",label:"事件类型",width:"150"},{default:b((({row:e})=>[m(Ce,{type:"info",size:"small"},{default:b((()=>[y(H(Te(e.event_type)),1)])),_:2},1024)])),_:1}),m(Ve,{prop:"total",label:"总数",width:"100"}),m(Ve,{prop:"success",label:"成功",width:"100"}),m(Ve,{prop:"failed",label:"失败",width:"100"}),m(Ve,{prop:"pending",label:"处理中",width:"100"}),m(Ve,{label:"成功率",width:"120"},{default:b((({row:e})=>[y(H(Ye(e.total>0?e.success/e.total*100:0)),1)])),_:1}),m(Ve,{label:"状态分布","min-width":"200"},{default:b((({row:e})=>[h("div",be,[m(Ae,{percentage:e.total>0?e.success/e.total*100:0,"stroke-width":8,"show-text":!1,status:"success"},null,8,["percentage"]),h("span",ye," 成功: "+H(e.success)+" | 失败: "+H(e.failed)+" | 处理中: "+H(e.pending),1)])])),_:1})])),_:1},8,["data"])])),_:1}),m(Ie,{label:"每日统计",name:"daily"},{default:b((()=>[m(He,{data:ze.value,stripe:"",border:"","max-height":"400"},{default:b((()=>[m(Ve,{prop:"date",label:"日期",width:"120"}),m(Ve,{prop:"total",label:"总数",width:"100"}),m(Ve,{prop:"success",label:"成功",width:"100"}),m(Ve,{prop:"failed",label:"失败",width:"100"}),m(Ve,{prop:"pending",label:"处理中",width:"100"}),m(Ve,{label:"成功率",width:"120"},{default:b((({row:e})=>[y(H(Ye(e.total>0?e.success/e.total*100:0)),1)])),_:1}),m(Ve,{label:"状态分布","min-width":"200"},{default:b((({row:e})=>[h("div",ge,[m(Ae,{percentage:e.total>0?e.success/e.total*100:0,"stroke-width":8,"show-text":!1,status:"success"},null,8,["percentage"]),h("span",we," 成功: "+H(e.success)+" | 失败: "+H(e.failed)+" | 处理中: "+H(e.pending),1)])])),_:1})])),_:1},8,["data"])])),_:1})])),_:1},8,["modelValue"])])),_:1})])}}}),[["__scopeId","data-v-97090f52"]]);export{xe as default};
