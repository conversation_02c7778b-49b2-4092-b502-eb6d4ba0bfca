var e=Object.defineProperty,a=Object.getOwnPropertySymbols,l=Object.prototype.hasOwnProperty,t=Object.prototype.propertyIsEnumerable,o=(a,l,t)=>l in a?e(a,l,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[l]=t,s=(e,s)=>{for(var r in s||(s={}))l.call(s,r)&&o(e,r,s[r]);if(a)for(var r of a(s))t.call(s,r)&&o(e,r,s[r]);return e},r=(e,a,l)=>new Promise(((t,o)=>{var s=e=>{try{i(l.next(e))}catch(a){o(a)}},r=e=>{try{i(l.throw(e))}catch(a){o(a)}},i=e=>e.done?t(e.value):Promise.resolve(e.value).then(s,r);i((l=l.apply(e,a)).next())}));import{_ as i}from"./index-rNRt1EuS.js";import n from"./CompanyEditDialog-C0yzMtfS.js";import{d as c,r as u,X as d,f as p,M as v,c as m,o as _,e as b,b as y,w as g,K as f,u as h,aR as w,H as C,aQ as k,aS as x,_ as j,C as z,D as S,ar as V,F as U,A as O,N as D,as as P,a3 as A,Z as E,a2 as I,aG as L,aJ as M,bb as B,ap as R,x as H,aI as K,aH as N,ai as T}from"./vendor-CAPBtMef.js";import{E as $,a as q,b as F,S as G}from"./expressCompanyApi-DSXUWAhW.js";import J from"./MappingManageDialog-BOMcZn0u.js";import Q from"./ServiceManageDialog-Co0x1g86.js";const X={class:"express-company-container"},Z={class:"action-bar"},W={class:"action-left"},Y={class:"action-right"},ee={class:"filter-section"},ae={class:"table-section"},le={key:1,class:"text-gray-400"},te={class:"action-buttons"},oe={class:"pagination-wrapper"},se=i(c({__name:"index",setup(e){const a=u(!1),l=u([]),t=d({keyword:"",is_active:void 0,sort_by:"sort_order",sort_order:"ASC"}),o=d({page:1,page_size:20,total:0}),i=u(!1),c=u(!1),se=u(!1),re=u(null);p((()=>{ie()}));const ie=()=>r(this,null,(function*(){try{a.value=!0;const e=yield $.getCompanies(s({page:o.page,page_size:o.page_size},t));e.success?(l.value=e.data.companies,o.total=e.data.total):v.error(e.message||"获取快递公司列表失败")}catch(e){v.error("获取快递公司列表失败")}finally{a.value=!1}})),ne=()=>{o.page=1,ie()},ce=()=>{Object.assign(t,{keyword:"",is_active:void 0,sort_by:"sort_order",sort_order:"ASC"}),o.page=1,ie()},ue=()=>{ie()},de=e=>{o.page=e,ie()},pe=e=>{o.page_size=e,o.page=1,ie()},ve=({prop:e,order:a})=>{t.sort_by=e,t.sort_order="ascending"===a?"ASC":"DESC",ie()},me=()=>{re.value=null,i.value=!0},_e=()=>{ie()},be=e=>r(this,null,(function*(){try{const a=yield $.updateCompany(e.id,{is_active:e.is_active});a.success?v.success((e.is_active?"启用":"禁用")+"成功"):(e.is_active=!e.is_active,v.error(a.message||"操作失败"))}catch(a){e.is_active=!e.is_active,v.error("操作失败")}})),ye=e=>r(this,null,(function*(){try{yield T.confirm(`确认删除快递公司 "${e.name}" 吗？此操作不可撤销。`,"确认删除",{type:"warning",confirmButtonText:"确认",cancelButtonText:"取消"});const a=yield $.deleteCompany(e.id);a.success?(v.success("删除成功"),ie()):v.error(a.message||"删除失败")}catch(a){"cancel"!==a&&v.error("删除失败")}})),ge=()=>{},fe=()=>{},he=()=>{const e=l.value.map((e=>({"快递公司代码":e.code,"快递公司名称":e.name,"客服电话":e.customer_service_phone||"","描述":e.description||"","状态":e.is_active?"启用":"禁用","排序":e.sort_order,"更新时间":we(e.updated_at)}))),a=[Object.keys(e[0]).join(","),...e.map((e=>Object.values(e).join(",")))].join("\n"),t=new Blob([a],{type:"text/csv;charset=utf-8;"}),o=document.createElement("a");o.href=URL.createObjectURL(t),o.download=`express_companies_${(new Date).toISOString().slice(0,10)}.csv`,o.click()},we=e=>new Date(e).toLocaleString("zh-CN");return(e,r)=>{const u=C,d=z,p=j,v=P,T=V,$=E,ie=M,Ce=B,ke=R,xe=K,je=N,ze=L;return _(),m("div",X,[r[18]||(r[18]=b("div",{class:"page-header"},[b("h2",null,"快递公司管理"),b("p",null,"管理系统中的快递公司信息，包括基本信息、供应商映射、服务配置等")],-1)),b("div",Z,[b("div",W,[y(u,{type:"primary",icon:h(w),onClick:me},{default:g((()=>r[9]||(r[9]=[f(" 新增快递公司 ")]))),_:1},8,["icon"]),y(u,{icon:h(k),onClick:ue},{default:g((()=>r[10]||(r[10]=[f(" 刷新 ")]))),_:1},8,["icon"])]),b("div",Y,[y(u,{icon:h(x),onClick:he},{default:g((()=>r[11]||(r[11]=[f(" 导出数据 ")]))),_:1},8,["icon"])])]),b("div",ee,[y($,{model:t,inline:""},{default:g((()=>[y(p,{label:"搜索关键词"},{default:g((()=>[y(d,{modelValue:t.keyword,"onUpdate:modelValue":r[0]||(r[0]=e=>t.keyword=e),placeholder:"请输入快递公司代码或名称",clearable:"",style:{width:"200px"},onKeyup:S(ne,["enter"])},null,8,["modelValue"])])),_:1}),y(p,{label:"状态"},{default:g((()=>[y(T,{modelValue:t.is_active,"onUpdate:modelValue":r[1]||(r[1]=e=>t.is_active=e),placeholder:"请选择状态",clearable:"",style:{width:"120px"}},{default:g((()=>[(_(!0),m(U,null,O(h(q),(e=>(_(),D(v,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),y(p,{label:"排序字段"},{default:g((()=>[y(T,{modelValue:t.sort_by,"onUpdate:modelValue":r[2]||(r[2]=e=>t.sort_by=e),placeholder:"请选择排序字段",style:{width:"120px"}},{default:g((()=>[(_(!0),m(U,null,O(h(F),(e=>(_(),D(v,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),y(p,{label:"排序方向"},{default:g((()=>[y(T,{modelValue:t.sort_order,"onUpdate:modelValue":r[3]||(r[3]=e=>t.sort_order=e),placeholder:"请选择排序方向",style:{width:"100px"}},{default:g((()=>[(_(!0),m(U,null,O(h(G),(e=>(_(),D(v,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),y(p,null,{default:g((()=>[y(u,{type:"primary",icon:h(A),onClick:ne},{default:g((()=>r[12]||(r[12]=[f(" 搜索 ")]))),_:1},8,["icon"]),y(u,{onClick:ce},{default:g((()=>r[13]||(r[13]=[f(" 重置 ")]))),_:1})])),_:1})])),_:1},8,["model"])]),b("div",ae,[I((_(),D(xe,{data:l.value,stripe:"",border:"",style:{width:"100%"},onSortChange:ve},{default:g((()=>[y(ie,{prop:"code",label:"快递公司代码",width:"140",sortable:"custom"}),y(ie,{prop:"name",label:"快递公司名称",width:"160",sortable:"custom","show-overflow-tooltip":""}),y(ie,{label:"Logo",width:"100",align:"center"},{default:g((({row:e})=>[e.logo_url?(_(),D(Ce,{key:0,src:e.logo_url,"preview-src-list":[e.logo_url],fit:"cover",style:{width:"40px",height:"40px","border-radius":"6px"}},null,8,["src","preview-src-list"])):(_(),m("span",le,"无"))])),_:1}),y(ie,{prop:"customer_service_phone",label:"客服电话",width:"140","show-overflow-tooltip":""}),y(ie,{label:"状态",width:"100",align:"center"},{default:g((({row:e})=>[y(ke,{modelValue:e.is_active,"onUpdate:modelValue":a=>e.is_active=a,onChange:a=>be(e)},null,8,["modelValue","onUpdate:modelValue","onChange"])])),_:1}),y(ie,{prop:"sort_order",label:"排序",width:"100",align:"center",sortable:"custom"}),y(ie,{prop:"updated_at",label:"更新时间",width:"180",sortable:"custom"},{default:g((({row:e})=>[f(H(we(e.updated_at)),1)])),_:1}),y(ie,{label:"操作",width:"220",fixed:"right",align:"center"},{default:g((({row:e})=>[b("div",te,[y(u,{type:"primary",size:"small",onClick:a=>{return l=e,re.value=s({},l),void(i.value=!0);var l}},{default:g((()=>r[14]||(r[14]=[f(" 编辑 ")]))),_:2},1032,["onClick"]),y(u,{type:"info",size:"small",onClick:a=>{return l=e,re.value=l,void(c.value=!0);var l}},{default:g((()=>r[15]||(r[15]=[f(" 映射 ")]))),_:2},1032,["onClick"]),y(u,{type:"warning",size:"small",onClick:a=>{return l=e,re.value=l,void(se.value=!0);var l}},{default:g((()=>r[16]||(r[16]=[f(" 服务 ")]))),_:2},1032,["onClick"]),y(u,{type:"danger",size:"small",onClick:a=>ye(e)},{default:g((()=>r[17]||(r[17]=[f(" 删除 ")]))),_:2},1032,["onClick"])])])),_:1})])),_:1},8,["data"])),[[ze,a.value]]),b("div",oe,[y(je,{"current-page":o.page,"onUpdate:currentPage":r[4]||(r[4]=e=>o.page=e),"page-size":o.page_size,"onUpdate:pageSize":r[5]||(r[5]=e=>o.page_size=e),total:o.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:pe,onCurrentChange:de},null,8,["current-page","page-size","total"])])]),y(n,{visible:i.value,"onUpdate:visible":r[6]||(r[6]=e=>i.value=e),company:re.value,onSuccess:_e},null,8,["visible","company"]),y(J,{visible:c.value,"onUpdate:visible":r[7]||(r[7]=e=>c.value=e),company:re.value,onSuccess:ge},null,8,["visible","company"]),y(Q,{visible:se.value,"onUpdate:visible":r[8]||(r[8]=e=>se.value=e),company:re.value,onSuccess:fe},null,8,["visible","company"])])}}}),[["__scopeId","data-v-50f2376e"]]);export{se as default};
