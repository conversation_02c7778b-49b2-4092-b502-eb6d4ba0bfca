var e=Object.defineProperty,t=(t,r,s)=>((t,r,s)=>r in t?e(t,r,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[r]=s)(t,"symbol"!=typeof r?r+"":r,s),r=(e,t,r)=>new Promise(((s,i)=>{var n=e=>{try{a(r.next(e))}catch(t){i(t)}},o=e=>{try{a(r.throw(e))}catch(t){i(t)}},a=e=>e.done?s(e.value):Promise.resolve(e.value).then(n,o);a((r=r.apply(e,t)).next())}));import{h as s}from"./index-rNRt1EuS.js";class i{static getRoleList(){return r(this,arguments,(function*(e={}){try{const t=yield s.get({url:this.BASE_URL,params:{page:e.page||1,page_size:e.page_size||20,keyword:e.keyword||"",is_system:e.is_system,order_by:e.order_by||"created_at",order:e.order||"desc",_t:Date.now()}});return{items:t||[],total:(null==t?void 0:t.length)||0,page:e.page||1,page_size:e.page_size||20}}catch(t){throw new Error(t.message||"获取角色列表失败")}}))}static getRoleById(e){return r(this,null,(function*(){try{return yield s.get({url:`${this.BASE_URL}/${e}`})}catch(t){throw new Error(t.message||"获取角色详情失败")}}))}static createRole(e){return r(this,null,(function*(){try{return yield s.post({url:this.BASE_URL,data:e})}catch(t){throw new Error(t.message||"创建角色失败")}}))}static updateRole(e,t){return r(this,null,(function*(){try{return yield s.put({url:`${this.BASE_URL}/${e}`,data:t})}catch(r){throw new Error(r.message||"更新角色失败")}}))}static deleteRole(e){return r(this,null,(function*(){try{yield s.delete({url:`${this.BASE_URL}/${e}`})}catch(t){throw new Error(t.message||"删除角色失败")}}))}static addPermissionToRole(e,t){return r(this,null,(function*(){try{yield s.post({url:`${this.BASE_URL}/${e}/permissions`,data:{permission_id:t}})}catch(r){throw new Error(r.message||"添加权限失败")}}))}static removePermissionFromRole(e,t){return r(this,null,(function*(){try{yield s.delete({url:`${this.BASE_URL}/${e}/permissions/${t}`})}catch(r){throw new Error(r.message||"移除权限失败")}}))}}t(i,"BASE_URL","/api/v1/admin/roles");class n{static getPermissionList(){return r(this,arguments,(function*(e={}){try{const t=yield s.get({url:this.BASE_URL,params:{page:e.page||1,page_size:e.page_size||20,keyword:e.keyword||"",resource:e.resource||"",action:e.action||"",is_system:e.is_system,order_by:e.order_by||"created_at",order:e.order||"desc",_t:Date.now()}});return{items:t||[],total:(null==t?void 0:t.length)||0,page:e.page||1,page_size:e.page_size||20}}catch(t){throw new Error(t.message||"获取权限列表失败")}}))}static getPermissionById(e){return r(this,null,(function*(){try{return yield s.get({url:`${this.BASE_URL}/${e}`})}catch(t){throw new Error(t.message||"获取权限详情失败")}}))}static createPermission(e){return r(this,null,(function*(){try{return yield s.post({url:this.BASE_URL,data:e})}catch(t){throw new Error(t.message||"创建权限失败")}}))}static updatePermission(e,t){return r(this,null,(function*(){try{return yield s.put({url:`${this.BASE_URL}/${e}`,data:t})}catch(r){throw new Error(r.message||"更新权限失败")}}))}static deletePermission(e){return r(this,null,(function*(){try{yield s.delete({url:`${this.BASE_URL}/${e}`})}catch(t){throw new Error(t.message||"删除权限失败")}}))}static getPermissionsByRole(e){return r(this,null,(function*(){try{return(yield s.get({url:`/api/v1/admin/roles/${e}/permissions`}))||[]}catch(t){throw new Error(t.message||"获取角色权限失败")}}))}}t(n,"BASE_URL","/api/v1/admin/permissions");class o{static getUserRoles(e){return r(this,null,(function*(){try{return(yield s.get({url:`${this.BASE_URL}/${e}/roles`}))||[]}catch(t){throw new Error(t.message||"获取用户角色失败")}}))}static addRoleToUser(e,t){return r(this,null,(function*(){try{yield s.post({url:`${this.BASE_URL}/${e}/roles/${t}`})}catch(r){throw new Error(r.message||"添加角色失败")}}))}static removeRoleFromUser(e,t){return r(this,null,(function*(){try{yield s.delete({url:`${this.BASE_URL}/${e}/roles/${t}`})}catch(r){throw new Error(r.message||"移除角色失败")}}))}static setDefaultRoleForUser(e,t){return r(this,null,(function*(){try{yield s.put({url:`${this.BASE_URL}/${e}/default-role/${t}`})}catch(r){throw new Error(r.message||"设置默认角色失败")}}))}static getUsersByRole(e){return r(this,null,(function*(){try{return(yield s.get({url:`/api/v1/admin/roles-users/${e}`}))||[]}catch(t){throw new Error(t.message||"获取角色用户失败")}}))}}t(o,"BASE_URL","/api/v1/users");export{n as P,i as R,o as U};
