const t=new Set;function e(){const e=[];try{Array.from(document.styleSheets).forEach((n=>{try{Array.from(n.cssRules||n.rules).forEach((t=>{const n=function(t){if(!(t instanceof CSSStyleRule))return null;const{selectorText:e,style:n}=t;if(!(null==e?void 0:e.startsWith(".iconsys-"))||!e.includes("::before"))return null;const o=e.substring(1).replace("::before",""),s=n.getPropertyValue("content");if(!s)return null;const c=s.replace(/['"\\]/g,"");return{className:o,unicode:c?`&#x${r(c)};`:void 0}}(t);n&&e.push(n)}))}catch(o){const e={sheet:n,error:o};t.has(e)||t.add(e)}}))}catch(n){}return e}function r(t){return t?t.charCodeAt(0).toString(16).padStart(4,"0"):""}export{e};
