var e=(e,l,a)=>new Promise(((s,t)=>{var d=e=>{try{i(a.next(e))}catch(l){t(l)}},u=e=>{try{i(a.throw(e))}catch(l){t(l)}},i=e=>e.done?s(e.value):Promise.resolve(e.value).then(d,u);i((a=a.apply(e,l)).next())}));import{_ as l}from"./index-rNRt1EuS.js";/* empty css               *//* empty css                             *//* empty css                */import{R as a,U as s}from"./rolePermissionApi-CKaU4rQM.js";import{U as t}from"./userManagementApi-BPI8-eRA.js";import{d,r as u,X as i,j as r,f as n,c as o,o as c,e as v,b as m,N as p,a as f,w as y,Z as _,_ as h,ar as g,F as b,A as w,as as U,H as k,K as I,aV as x,aN as R,b1 as C,b2 as V,x as z,aX as j,a2 as $,aI as P,aJ as T,aG as B,aT as A,Y as F,M as L,ai as N}from"./vendor-CAPBtMef.js";const D={class:"user-role-management"},M={key:0,class:"user-info"},X={class:"card-header"},G={class:"user-roles"},H={class:"role-users"},J={key:0,class:"assign-role"},K={style:{float:"right",color:"#8492a6","font-size":"13px"}},S=l(d({__name:"index",setup(l){const d=u(!1),S=u(!1),Y=u(!1),Z=u([]),q=u([]),E=u(null),O=u(null),Q=u([]),W=u([]),ee=i({selectedUserId:"",selectedRoleId:""}),le=u(!1),ae=u([]),se=r((()=>{const e=Q.value.map((e=>e.id));return q.value.filter((l=>!e.includes(l.id)))})),te=e=>e?new Date(e).toLocaleString("zh-CN"):"-",de=()=>e(this,null,(function*(){try{const e=yield t.getUserList({page:1,page_size:1e3});Z.value=e.items}catch(e){L.error(e.message||"加载用户列表失败")}})),ue=()=>e(this,null,(function*(){try{const e=yield a.getRoleList({page:1,page_size:1e3});q.value=e.items}catch(e){L.error(e.message||"加载角色列表失败")}})),ie=l=>e(this,null,(function*(){try{d.value=!0,Q.value=yield s.getUserRoles(l)}catch(e){L.error(e.message||"加载用户角色失败")}finally{d.value=!1}})),re=l=>e(this,null,(function*(){try{S.value=!0,W.value=yield s.getUsersByRole(l)}catch(e){L.error(e.message||"加载角色用户失败")}finally{S.value=!1}})),ne=l=>e(this,null,(function*(){if(!l)return E.value=null,void(Q.value=[]);E.value=Z.value.find((e=>e.id===l))||null,E.value&&(yield ie(l)),ee.selectedRoleId="",W.value=[]})),oe=l=>e(this,null,(function*(){if(!l)return O.value=null,void(W.value=[]);O.value=q.value.find((e=>e.id===l))||null,O.value&&(yield re(l)),ee.selectedUserId="",E.value=null,Q.value=[]})),ce=()=>e(this,null,(function*(){yield Promise.all([de(),ue()]),ee.selectedUserId&&(yield ne(ee.selectedUserId)),ee.selectedRoleId&&(yield oe(ee.selectedRoleId))})),ve=()=>{E.value&&(ae.value=[],le.value=!0)},me=()=>e(this,null,(function*(){if(E.value&&0!==ae.value.length)try{Y.value=!0;const e=ae.value.map((e=>s.addRoleToUser(E.value.id,e)));yield Promise.all(e),L.success(`成功分配 ${ae.value.length} 个角色`),yield ie(E.value.id),le.value=!1}catch(e){L.error(e.message||"分配角色失败")}finally{Y.value=!1}else L.warning("请选择要分配的角色")})),pe=l=>e(this,null,(function*(){if(E.value)try{yield N.confirm(`确定要从用户 "${E.value.username}" 中移除角色 "${l.name}" 吗？`,"确认移除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),yield s.removeRoleFromUser(E.value.id,l.id),L.success("角色移除成功"),yield ie(E.value.id)}catch(e){"cancel"!==e&&L.error(e.message||"移除角色失败")}})),fe=l=>e(this,null,(function*(){if(E.value)try{yield N.confirm(`确定要将角色 "${l.name}" 设置为用户 "${E.value.username}" 的默认角色吗？`,"确认设置",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"}),yield s.setDefaultRoleForUser(E.value.id,l.id),L.success("默认角色设置成功"),yield ie(E.value.id)}catch(e){"cancel"!==e&&L.error(e.message||"设置默认角色失败")}})),ye=l=>e(this,null,(function*(){ee.selectedUserId=l.id,ee.selectedRoleId="",yield ne(l.id)}));return n((()=>e(this,null,(function*(){yield Promise.all([de(),ue()])})))),(e,l)=>{const a=U,s=g,t=h,u=k,i=_,r=x,n=V,L=j,N=C,O=R,de=T,ue=P,ie=A,re=F,_e=B;return c(),o("div",D,[l[17]||(l[17]=v("div",{class:"page-header"},[v("div",{class:"header-left"},[v("h1",{class:"page-title"},"用户角色管理"),v("p",{class:"page-description"},"管理用户的角色分配和权限设置")])],-1)),m(r,{class:"search-card"},{default:y((()=>[m(i,{model:ee,inline:""},{default:y((()=>[m(t,{label:"选择用户"},{default:y((()=>[m(s,{modelValue:ee.selectedUserId,"onUpdate:modelValue":l[0]||(l[0]=e=>ee.selectedUserId=e),placeholder:"请选择用户",clearable:"",filterable:"",style:{width:"200px"},onChange:ne},{default:y((()=>[(c(!0),o(b,null,w(Z.value,(e=>(c(),p(a,{key:e.id,label:`${e.username} (${e.email})`,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),m(t,{label:"选择角色"},{default:y((()=>[m(s,{modelValue:ee.selectedRoleId,"onUpdate:modelValue":l[1]||(l[1]=e=>ee.selectedRoleId=e),placeholder:"请选择角色",clearable:"",style:{width:"200px"},onChange:oe},{default:y((()=>[(c(!0),o(b,null,w(q.value,(e=>(c(),p(a,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),m(t,null,{default:y((()=>[m(u,{type:"primary",onClick:ce},{default:y((()=>l[5]||(l[5]=[I(" 刷新数据 ")]))),_:1})])),_:1})])),_:1},8,["model"])])),_:1}),ee.selectedUserId?(c(),p(ie,{key:0,gutter:20},{default:y((()=>[m(O,{span:12},{default:y((()=>[m(r,{class:"user-info-card"},{header:y((()=>l[6]||(l[6]=[v("div",{class:"card-header"},[v("span",null,"用户信息")],-1)]))),default:y((()=>[E.value?(c(),o("div",M,[m(N,{column:1,border:""},{default:y((()=>[m(n,{label:"用户名"},{default:y((()=>[I(z(E.value.username),1)])),_:1}),m(n,{label:"邮箱"},{default:y((()=>[I(z(E.value.email),1)])),_:1}),m(n,{label:"状态"},{default:y((()=>[m(L,{type:E.value.is_active?"success":"danger",size:"small"},{default:y((()=>[I(z(E.value.is_active?"活跃":"禁用"),1)])),_:1},8,["type"])])),_:1}),m(n,{label:"创建时间"},{default:y((()=>[I(z(te(E.value.created_at)),1)])),_:1})])),_:1})])):f("",!0)])),_:1})])),_:1}),m(O,{span:12},{default:y((()=>[m(r,{class:"user-roles-card"},{header:y((()=>[v("div",X,[l[8]||(l[8]=v("span",null,"用户角色",-1)),m(u,{type:"primary",size:"small",onClick:ve},{default:y((()=>l[7]||(l[7]=[I(" 分配角色 ")]))),_:1})])])),default:y((()=>[v("div",G,[$((c(),p(ue,{data:Q.value,stripe:"",border:"","max-height":"300"},{default:y((()=>[m(de,{prop:"name",label:"角色名称","min-width":"120"}),m(de,{prop:"description",label:"角色描述","min-width":"150","show-overflow-tooltip":""}),m(de,{prop:"is_system",label:"类型",width:"80"},{default:y((({row:e})=>[m(L,{type:e.is_system?"warning":"success",size:"small"},{default:y((()=>[I(z(e.is_system?"系统":"自定义"),1)])),_:2},1032,["type"])])),_:1}),m(de,{label:"操作",width:"120"},{default:y((({row:e})=>[m(u,{type:"danger",size:"small",onClick:l=>pe(e)},{default:y((()=>l[9]||(l[9]=[I(" 移除 ")]))),_:2},1032,["onClick"]),m(u,{type:"warning",size:"small",onClick:l=>fe(e)},{default:y((()=>l[10]||(l[10]=[I(" 设为默认 ")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])),[[_e,d.value]])])])),_:1})])),_:1})])),_:1})):f("",!0),ee.selectedRoleId&&!ee.selectedUserId?(c(),p(ie,{key:1,gutter:20},{default:y((()=>[m(O,{span:24},{default:y((()=>[m(r,{class:"role-users-card"},{header:y((()=>l[11]||(l[11]=[v("div",{class:"card-header"},[v("span",null,"角色用户列表")],-1)]))),default:y((()=>[v("div",H,[$((c(),p(ue,{data:W.value,stripe:"",border:""},{default:y((()=>[m(de,{prop:"username",label:"用户名","min-width":"120"}),m(de,{prop:"email",label:"邮箱","min-width":"180"}),m(de,{prop:"is_active",label:"状态",width:"100"},{default:y((({row:e})=>[m(L,{type:e.is_active?"success":"danger",size:"small"},{default:y((()=>[I(z(e.is_active?"活跃":"禁用"),1)])),_:2},1032,["type"])])),_:1}),m(de,{prop:"created_at",label:"创建时间",width:"180"},{default:y((({row:e})=>[I(z(te(e.created_at)),1)])),_:1}),m(de,{label:"操作",width:"120"},{default:y((({row:e})=>[m(u,{type:"primary",size:"small",onClick:l=>ye(e)},{default:y((()=>l[12]||(l[12]=[I(" 查看角色 ")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])),[[_e,S.value]])])])),_:1})])),_:1})])),_:1})):f("",!0),m(re,{modelValue:le.value,"onUpdate:modelValue":l[4]||(l[4]=e=>le.value=e),title:"分配角色",width:"600px","close-on-click-modal":!1},{footer:y((()=>[m(u,{onClick:l[3]||(l[3]=e=>le.value=!1)},{default:y((()=>l[15]||(l[15]=[I("取消")]))),_:1}),m(u,{type:"primary",loading:Y.value,onClick:me},{default:y((()=>l[16]||(l[16]=[I(" 确认分配 ")]))),_:1},8,["loading"])])),default:y((()=>[E.value?(c(),o("div",J,[v("p",null,[l[13]||(l[13]=I("为用户 ")),v("strong",null,z(E.value.username),1),l[14]||(l[14]=I(" 分配角色："))]),m(s,{modelValue:ae.value,"onUpdate:modelValue":l[2]||(l[2]=e=>ae.value=e),placeholder:"请选择要分配的角色",style:{width:"100%"},multiple:""},{default:y((()=>[(c(!0),o(b,null,w(se.value,(e=>(c(),p(a,{key:e.id,label:e.name,value:e.id},{default:y((()=>[v("span",null,z(e.name),1),v("span",K,z(e.description),1)])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue"])])):f("",!0)])),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-01bdfe14"]]);export{S as default};
