var e,t=Object.defineProperty,r=(e,t,r)=>new Promise(((s,a)=>{var c=e=>{try{n(r.next(e))}catch(t){a(t)}},o=e=>{try{n(r.throw(e))}catch(t){a(t)}},n=e=>e.done?s(e.value):Promise.resolve(e.value).then(c,o);n((r=r.apply(e,t)).next())}));import{h as s}from"./index-rNRt1EuS.js";class a{static getUserList(){return r(this,arguments,(function*(e={}){try{const t=yield s.get({url:this.BASE_URL,params:{page:e.page||1,page_size:e.page_size||20,keyword:e.keyword||"",status:e.status||"",role_id:e.role_id||"",order_by:e.order_by||"created_at",order:e.order||"desc",include_deleted:e.include_deleted||!1,_t:Date.now()},headers:{"Cache-Control":"no-cache, no-store, must-revalidate",Pragma:"no-cache",Expires:"0"}});if(t.success&&t.data)return t.data;throw new Error(t.message||"获取用户列表失败")}catch(t){throw new Error(t.message||"获取用户列表失败")}}))}static getUserStatistics(){return r(this,null,(function*(){try{const e=yield s.get({url:`${this.BASE_URL}/statistics`,params:{_t:Date.now()},headers:{"Cache-Control":"no-cache, no-store, must-revalidate",Pragma:"no-cache",Expires:"0"}});if(e.success&&e.data)return e.data;throw new Error(e.message||"获取用户统计失败")}catch(e){throw new Error(e.message||"获取用户统计失败")}}))}static getUserDetail(e){return r(this,null,(function*(){try{const t=yield s.get({url:`${this.BASE_URL}/${e}`});if(t.success&&t.data)return t.data;throw new Error(t.message||"获取用户详情失败")}catch(t){throw new Error(t.message||"获取用户详情失败")}}))}static createUser(e){return r(this,null,(function*(){try{const t=yield s.post({url:this.BASE_URL,data:e});if(t.success&&t.data)return t.data;throw new Error(t.message||"创建用户失败")}catch(t){throw new Error(t.message||"创建用户失败")}}))}static updateUser(e,t){return r(this,null,(function*(){try{const r=yield s.put({url:`${this.BASE_URL}/${e}`,data:t});if(r.success&&r.data)return r.data;throw new Error(r.message||"更新用户失败")}catch(r){throw new Error(r.message||"更新用户失败")}}))}static updateUserStatus(e,t){return r(this,null,(function*(){try{const r=yield s.put({url:`${this.BASE_URL}/${e}/status`,data:t});if(!r.success)throw new Error(r.message||"更新用户状态失败")}catch(r){throw new Error(r.message||"更新用户状态失败")}}))}static resetUserPassword(e,t){return r(this,null,(function*(){try{const r=yield s.put({url:`${this.BASE_URL}/${e}/password`,data:t});if(!r.success)throw new Error(r.message||"重置密码失败")}catch(r){throw new Error(r.message||"重置密码失败")}}))}static deleteUser(e){return r(this,null,(function*(){try{const t=yield s.del({url:`${this.BASE_URL}/${e}`});if(!t.success)throw new Error(t.message||"删除用户失败")}catch(t){throw new Error(t.message||"删除用户失败")}}))}static forceDeleteUser(e){return r(this,null,(function*(){try{const t=yield s.del({url:`${this.BASE_URL}/${e}/force`});if(!t.success)throw new Error(t.message||"永久删除用户失败")}catch(t){throw new Error(t.message||"永久删除用户失败")}}))}static batchUpdateUserStatus(e,t){return r(this,null,(function*(){try{const r=yield s.patch({url:"/api/v1/system/admin/users/batch/status",data:{user_ids:e,is_active:t}});if(!r.success)throw new Error(r.message||"批量更新失败")}catch(r){throw new Error(r.message||"批量更新失败")}}))}}((e,r,s)=>{r in e?t(e,r,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[r]=s})(a,"symbol"!=typeof(e="BASE_URL")?e+"":e,"/api/v1/admin/users");export{a as U};
