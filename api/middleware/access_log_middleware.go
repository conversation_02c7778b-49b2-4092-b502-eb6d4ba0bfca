package middleware

import (
	"bytes"
	"io"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/your-org/go-kuaidi/internal/util"
	"go.uber.org/zap"
)

// AccessLogMiddleware 企业级访问日志中间件
func AccessLogMiddleware(accessLogger *zap.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 记录开始时间
		startTime := util.NowBeijing()

		// 获取请求信息
		method := c.Request.Method
		path := c.Request.URL.Path
		query := c.Request.URL.RawQuery
		userAgent := c.Request.UserAgent()
		clientIP := c.ClientIP()
		referer := c.Request.Referer()

		// 读取请求体（限制大小）
		var requestBody []byte
		if c.Request.Body != nil && (method == "POST" || method == "PUT" || method == "PATCH") {
			// 限制读取的请求体大小为1MB
			limitedReader := io.LimitReader(c.Request.Body, 1024*1024)
			requestBody, _ = io.ReadAll(limitedReader)
			c.Request.Body = io.NopCloser(bytes.NewBuffer(requestBody))
		}

		// 创建响应记录器
		responseRecorder := &ResponseRecorder{
			ResponseWriter: c.Writer,
			body:           &bytes.Buffer{},
			status:         0, // 初始状态码为0，等待实际设置
		}
		c.Writer = responseRecorder

		// 处理请求
		c.Next()

		// 计算处理时间
		duration := time.Since(startTime)

		// 获取响应信息
		statusCode := responseRecorder.status
		if statusCode == 0 {
			// 如果没有显式设置状态码，从底层ResponseWriter获取
			rw := responseRecorder.ResponseWriter
			if rw != nil {
				statusCode = rw.Status()
			}
			// 如果仍然是0，检查响应内容判断状态
			if statusCode == 0 {
				responseBody := responseRecorder.body.String()
				if strings.Contains(responseBody, "404 page not found") {
					statusCode = 404
				} else if responseRecorder.body.Len() > 0 {
					statusCode = 200
				} else {
					statusCode = 200 // 默认成功
				}
			}
		}
		responseSize := responseRecorder.body.Len()
		responseBody := responseRecorder.body.String()

		// 限制响应体记录大小
		if len(responseBody) > 1024 {
			responseBody = responseBody[:1024] + "... (truncated)"
		}

		// 记录访问日志
		fields := []zap.Field{
			zap.String("method", method),
			zap.String("path", path),
			zap.String("query", query),
			zap.Int("status", statusCode),
			zap.Duration("duration", duration),
			zap.String("client_ip", clientIP),
			zap.String("user_agent", userAgent),
			zap.String("referer", referer),
			zap.Int("request_size", len(requestBody)),
			zap.Int("response_size", responseSize),
		}

		// 根据状态码决定日志级别
		message := "HTTP Request"
		if statusCode >= 500 {
			// 服务器错误
			fields = append(fields,
				zap.String("request_body", string(requestBody)),
				zap.String("response_body", responseBody),
			)
			accessLogger.Error(message, fields...)
		} else if statusCode >= 400 {
			// 客户端错误
			fields = append(fields, zap.String("response_body", responseBody))
			accessLogger.Warn(message, fields...)
		} else {
			// 成功请求
			accessLogger.Info(message, fields...)
		}
	}
}

// ResponseRecorder 响应记录器
type ResponseRecorder struct {
	gin.ResponseWriter
	body   *bytes.Buffer
	status int
}

// Write 写入响应体
func (r *ResponseRecorder) Write(data []byte) (int, error) {
	r.body.Write(data)
	return r.ResponseWriter.Write(data)
}

// WriteHeader 写入状态码
func (r *ResponseRecorder) WriteHeader(statusCode int) {
	r.status = statusCode
	r.ResponseWriter.WriteHeader(statusCode)
}

// Status 获取状态码
func (r *ResponseRecorder) Status() int {
	return r.status
}

// Size 获取响应体大小
func (r *ResponseRecorder) Size() int {
	return r.body.Len()
}

// Body 获取响应体内容
func (r *ResponseRecorder) Body() string {
	return r.body.String()
}
