package middleware

import (
	"fmt"
	"net/http"
	"runtime/debug"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"github.com/your-org/go-kuaidi/internal/util"

)

// ErrorCaptureMiddleware 错误捕获中间件
func ErrorCaptureMiddleware(errorLogger *zap.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			if err := recover(); err != nil {
				// 获取堆栈信息
				stack := debug.Stack()

				// 获取请求信息
				method := c.Request.Method
				path := c.Request.URL.Path
				query := c.Request.URL.RawQuery
				userAgent := c.Request.UserAgent()
				clientIP := c.ClientIP()

				// 记录错误日志
				errorLogger.Error("Panic recovered",
					zap.String("method", method),
					zap.String("path", path),
					zap.String("query", query),
					zap.String("client_ip", clientIP),
					zap.String("user_agent", userAgent),
					zap.Any("error", err),
					zap.String("stack", string(stack)),
					zap.Time("timestamp", util.NowBeijing()),
				)

				// 返回500错误
				c.JSON(http.StatusInternalServerError, gin.H{
					"code":    500,
					"message": "Internal Server Error",
					"success": false,
				})
				c.Abort()
			}
		}()

		// 处理请求
		c.Next()

		// 检查是否有错误
		if len(c.Errors) > 0 {
			// 获取请求信息
			method := c.Request.Method
			path := c.Request.URL.Path
			query := c.Request.URL.RawQuery
			userAgent := c.Request.UserAgent()
			clientIP := c.ClientIP()
			statusCode := c.Writer.Status()

			// 记录所有错误
			for _, ginErr := range c.Errors {
				errorLogger.Error("Request error",
					zap.String("method", method),
					zap.String("path", path),
					zap.String("query", query),
					zap.String("client_ip", clientIP),
					zap.String("user_agent", userAgent),
					zap.Int("status_code", statusCode),
					zap.Uint("error_type", uint(ginErr.Type)),
					zap.Error(ginErr.Err),
					zap.String("error_meta", fmt.Sprintf("%v", ginErr.Meta)),
					zap.Time("timestamp", util.NowBeijing()),
				)
			}
		}
	}
}

// LogError 手动记录错误日志的辅助函数
func LogError(c *gin.Context, errorLogger *zap.Logger, err error, message string) {
	if errorLogger == nil {
		return
	}

	// 获取请求信息
	method := c.Request.Method
	path := c.Request.URL.Path
	query := c.Request.URL.RawQuery
	userAgent := c.Request.UserAgent()
	clientIP := c.ClientIP()

	// 记录错误日志
	errorLogger.Error(message,
		zap.String("method", method),
		zap.String("path", path),
		zap.String("query", query),
		zap.String("client_ip", clientIP),
		zap.String("user_agent", userAgent),
		zap.Error(err),
		zap.Time("timestamp", util.NowBeijing()),
	)
}

// LogErrorWithFields 带自定义字段的错误日志记录
func LogErrorWithFields(c *gin.Context, errorLogger *zap.Logger, err error, message string, fields ...zap.Field) {
	if errorLogger == nil {
		return
	}

	// 获取请求信息
	method := c.Request.Method
	path := c.Request.URL.Path
	query := c.Request.URL.RawQuery
	userAgent := c.Request.UserAgent()
	clientIP := c.ClientIP()

	// 基础字段
	baseFields := []zap.Field{
		zap.String("method", method),
		zap.String("path", path),
		zap.String("query", query),
		zap.String("client_ip", clientIP),
		zap.String("user_agent", userAgent),
		zap.Error(err),
		zap.Time("timestamp", util.NowBeijing()),
	}

	// 合并自定义字段
	allFields := append(baseFields, fields...)

	// 记录错误日志
	errorLogger.Error(message, allFields...)
}
