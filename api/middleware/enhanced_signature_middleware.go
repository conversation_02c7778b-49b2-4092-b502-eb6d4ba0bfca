package middleware

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"go.uber.org/zap"

	"github.com/your-org/go-kuaidi/internal/auth"
	"github.com/your-org/go-kuaidi/internal/security"
	"github.com/your-org/go-kuaidi/internal/util"
)

// EnhancedSignatureMiddleware 企业级签名验证中间件
// 使用新的nonce管理器，提供更强的安全性和更好的错误处理
func EnhancedSignatureMiddleware(
	signatureService security.SignatureService,
	clientService auth.ClientService,
	nonceManager security.NonceManager,
	redisClient interface{}, // Redis客户端接口，避免导入依赖
	config security.SignatureConfig,
	logger *zap.Logger,
) gin.HandlerFunc {
	return func(c *gin.Context) {
		startTime := util.NowBeijing()
		requestID := c.GetString("request_id")
		if requestID == "" {
			requestID = fmt.Sprintf("req_%d", util.NowBeijing().UnixNano())
			c.Set("request_id", requestID)
		}

		// 🚀 增强：记录所有请求的详细信息
		logger.Info("签名验证开始 - 请求详情",
			zap.String("request_id", requestID),
			zap.String("path", c.Request.URL.Path),
			zap.String("method", c.Request.Method),
			zap.String("client_ip", c.ClientIP()),
			zap.String("user_agent", c.GetHeader("User-Agent")),
			zap.String("content_type", c.GetHeader("Content-Type")),
			zap.Int64("content_length", c.Request.ContentLength),
			zap.String("referer", c.GetHeader("Referer")))

		// 检查是否应该跳过签名验证
		if signatureService.ShouldSkipSignature(c.Request.URL.Path) {
			logger.Debug("跳过签名验证",
				zap.String("request_id", requestID),
				zap.String("path", c.Request.URL.Path))
			c.Next()
			return
		}

		// 🚀 增强：记录原始请求参数用于调试
		logger.Debug("原始请求参数",
			zap.String("request_id", requestID),
			zap.Any("query_params", c.Request.URL.Query()),
			zap.Any("headers", map[string]string{
				"Content-Type":  c.GetHeader("Content-Type"),
				"Authorization": c.GetHeader("Authorization"),
				"X-Client-ID":   c.GetHeader("X-Client-ID"),
				"X-Timestamp":   c.GetHeader("X-Timestamp"),
				"X-Nonce":       c.GetHeader("X-Nonce"),
				"X-Signature":   c.GetHeader("X-Signature"),
			}))

		// 提取签名参数
		signatureParams, err := extractSignatureParameters(c)
		if err != nil {
			// 🚀 增强：记录详细的参数提取失败信息
			logger.Error("签名参数提取失败 - 详细信息",
				zap.String("request_id", requestID),
				zap.String("client_ip", c.ClientIP()),
				zap.String("user_agent", c.GetHeader("User-Agent")),
				zap.String("path", c.Request.URL.Path),
				zap.String("method", c.Request.Method),
				zap.Any("query_params", c.Request.URL.Query()),
				zap.Any("headers", c.Request.Header),
				zap.Error(err))

			respondWithError(c, http.StatusBadRequest, "INVALID_SIGNATURE_PARAMS",
				"签名参数格式错误", err.Error(), logger, requestID)
			return
		}

		// 🚀 增强：记录提取成功的签名参数
		logger.Info("签名参数提取成功",
			zap.String("request_id", requestID),
			zap.String("client_id", signatureParams.ClientID),
			zap.String("timestamp", signatureParams.Timestamp),
			zap.String("nonce", signatureParams.Nonce),
			zap.Bool("has_signature", signatureParams.Signature != ""),
			zap.Int("signature_length", len(signatureParams.Signature)))

		// 检查是否为Web客户端（所有签名参数都为空）
		if signatureParams.IsEmpty() {
			logger.Debug("Web客户端请求，跳过签名验证",
				zap.String("request_id", requestID))
			c.Next()
			return
		}

		// 验证必要参数
		if err := signatureParams.Validate(); err != nil {
			logger.Warn("签名参数验证失败",
				zap.String("request_id", requestID),
				zap.Error(err))

			respondWithError(c, http.StatusUnauthorized, "MISSING_SIGNATURE_PARAMS",
				"缺少必要的签名参数", err.Error(), logger, requestID)
			return
		}

		// 验证参数长度
		if err := signatureParams.ValidateLength(); err != nil {
			logger.Warn("签名参数长度验证失败",
				zap.String("request_id", requestID),
				zap.Error(err))

			respondWithError(c, http.StatusBadRequest, "INVALID_PARAM_LENGTH",
				"签名参数长度超出限制", err.Error(), logger, requestID)
			return
		}

		// 🚀 临时功能：检查是否禁用timestamp验证（通过环境变量）
		skipTimestampValidation := os.Getenv("SKIP_TIMESTAMP_VALIDATION") == "true"

		// 验证时间戳（可通过环境变量临时禁用）
		if !skipTimestampValidation && !signatureService.IsTimestampValid(signatureParams.Timestamp) {
			// 🚀 增强：记录详细的时间戳验证失败信息
			logger.Error("时间戳验证失败 - 详细信息",
				zap.String("request_id", requestID),
				zap.String("client_id", signatureParams.ClientID),
				zap.String("client_ip", c.ClientIP()),
				zap.String("user_agent", c.GetHeader("User-Agent")),
				zap.String("timestamp", signatureParams.Timestamp),
				zap.String("server_time", util.NowBeijing().Format(time.RFC3339)),
				zap.Int64("server_unix", util.NowBeijing().Unix()),
				zap.Int64("server_unix_milli", util.NowBeijing().UnixMilli()))

			respondWithError(c, http.StatusUnauthorized, "INVALID_TIMESTAMP",
				"时间戳无效或已过期", "请检查系统时间同步", logger, requestID)
			return
		}

		// 记录timestamp验证跳过状态
		if skipTimestampValidation {
			logger.Warn("⚠️ TIMESTAMP验证已被临时禁用",
				zap.String("request_id", requestID),
				zap.String("client_id", signatureParams.ClientID),
				zap.String("timestamp", signatureParams.Timestamp),
				zap.String("env_var", "SKIP_TIMESTAMP_VALIDATION=true"))
		}

		// 🚀 检查是否禁用nonce验证
		if config.DisableNonceValidation {
			logger.Info("⚠️ NONCE验证已被禁用",
				zap.String("request_id", requestID),
				zap.String("client_id", signatureParams.ClientID),
				zap.String("config_setting", "disable_nonce_validation=true"))
		} else {
			// 验证nonce（统一网关特殊处理）
			ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
			defer cancel()

			// 🚀 统一网关：使用优化的timestamp验证（智能处理重复请求）
			if c.Request.URL.Path == "/api/gateway/execute" {
				// 统一网关使用timestamp作为nonce，进行智能验证
				if err := validateGatewayTimestampSmart(ctx, signatureParams.ClientID, signatureParams.Nonce, redisClient, logger, requestID); err != nil {
					var errorCode, userMessage, suggestion string

					if strings.Contains(err.Error(), "已被使用") || strings.Contains(err.Error(), "重复") {
						errorCode = "TIMESTAMP_ALREADY_USED"
						userMessage = "请求重复，请使用新的timestamp"
						suggestion = "建议使用毫秒级时间戳：Date.now().toString()"
					} else if strings.Contains(err.Error(), "过期") {
						errorCode = "TIMESTAMP_EXPIRED"
						userMessage = "timestamp已过期，请重新生成"
						suggestion = "请确保使用当前时间生成timestamp，有效期为5分钟"
					} else if strings.Contains(err.Error(), "格式") {
						errorCode = "INVALID_TIMESTAMP_FORMAT"
						userMessage = "timestamp格式无效，请使用数字时间戳"
						suggestion = "正确格式：毫秒级(1704715845123)或秒级(1704715845)"
					} else if strings.Contains(err.Error(), "来自未来") {
						errorCode = "TIMESTAMP_FUTURE"
						userMessage = "timestamp来自未来，请检查系统时间"
						suggestion = "请确保客户端时间与服务器时间同步（北京时间）"
					} else {
						errorCode = "TIMESTAMP_VALIDATION_ERROR"
						userMessage = "timestamp验证失败"
						suggestion = "请检查timestamp格式和时间同步"
					}

					// 🚀 增强：记录详细的统一网关timestamp验证失败信息
					logger.Error("统一网关timestamp验证失败 - 详细信息",
						zap.String("request_id", requestID),
						zap.String("client_id", signatureParams.ClientID),
						zap.String("client_ip", c.ClientIP()),
						zap.String("user_agent", c.GetHeader("User-Agent")),
						zap.String("timestamp", signatureParams.Nonce),
						zap.String("error_code", errorCode),
						zap.String("error_message", err.Error()),
						zap.String("server_time", util.NowBeijing().Format(time.RFC3339)),
						zap.Int64("server_unix", util.NowBeijing().Unix()),
						zap.Int64("server_unix_milli", util.NowBeijing().UnixMilli()))

					// 🚀 增强错误响应，提供更详细的指导
					c.JSON(http.StatusBadRequest, gin.H{
						"success":     false,
						"code":        errorCode,
						"message":     userMessage,
						"suggestion":  suggestion,
						"server_time": util.NowBeijing().In(getBeijingLocation()).UnixMilli(),
						"request_id":  requestID,
						"details":     err.Error(),
					})
					c.Abort()
					return
				}
			} else {
				// 传统API：使用完整的nonce验证
				nonceResult, err := nonceManager.ValidateNonce(ctx, signatureParams.ClientID, signatureParams.Nonce)
				if err != nil {
					logger.Error("nonce验证失败",
						zap.String("request_id", requestID),
						zap.String("client_id", signatureParams.ClientID),
						zap.Error(err))

					respondWithError(c, http.StatusServiceUnavailable, "NONCE_VALIDATION_ERROR",
						"nonce验证服务暂时不可用", "请稍后重试", logger, requestID)
					return
				}

				if !nonceResult.Valid {
					logger.Warn("nonce验证失败",
						zap.String("request_id", requestID),
						zap.String("client_id", signatureParams.ClientID),
						zap.String("error_code", nonceResult.ErrorCode),
						zap.String("error_msg", nonceResult.ErrorMsg))

					var httpStatus int
					var userMessage string

					switch nonceResult.ErrorCode {
					case "NONCE_ALREADY_USED":
						httpStatus = http.StatusBadRequest
						userMessage = "请求重复，请使用新的nonce"
					case "NONCE_EXPIRED":
						httpStatus = http.StatusUnauthorized
						userMessage = "nonce已过期，请重新生成"
					case "INVALID_FORMAT", "INVALID_OWNERSHIP":
						httpStatus = http.StatusBadRequest
						userMessage = "nonce格式无效"
					default:
						httpStatus = http.StatusBadRequest
						userMessage = "nonce验证失败"
					}

					respondWithError(c, httpStatus, nonceResult.ErrorCode,
						userMessage, nonceResult.ErrorMsg, logger, requestID)
					return
				}
			}
		}

		// 验证客户端
		client, err := clientService.FindByID(signatureParams.ClientID)
		if err != nil {
			// 🚀 增强：记录详细的客户端验证失败信息
			logger.Error("客户端验证失败 - 详细信息",
				zap.String("request_id", requestID),
				zap.String("client_id", signatureParams.ClientID),
				zap.String("client_ip", c.ClientIP()),
				zap.String("user_agent", c.GetHeader("User-Agent")),
				zap.String("path", c.Request.URL.Path),
				zap.String("timestamp", signatureParams.Timestamp),
				zap.String("nonce", signatureParams.Nonce),
				zap.Error(err))

			respondWithError(c, http.StatusUnauthorized, "INVALID_CLIENT",
				"客户端不存在或已禁用", "请检查客户端ID", logger, requestID)
			return
		}

		if !client.IsActive {
			// 🚀 增强：记录详细的客户端禁用信息
			logger.Error("客户端已禁用 - 详细信息",
				zap.String("request_id", requestID),
				zap.String("client_id", signatureParams.ClientID),
				zap.String("client_name", client.Name),
				zap.String("client_ip", c.ClientIP()),
				zap.String("user_agent", c.GetHeader("User-Agent")),
				zap.String("path", c.Request.URL.Path),
				zap.Bool("is_active", client.IsActive))

			respondWithError(c, http.StatusUnauthorized, "CLIENT_DISABLED",
				"客户端已被禁用", "请联系管理员", logger, requestID)
			return
		}

		// 读取请求体
		requestBody, err := readRequestBody(c)
		if err != nil {
			logger.Error("读取请求体失败",
				zap.String("request_id", requestID),
				zap.Error(err))

			respondWithError(c, http.StatusBadRequest, "REQUEST_BODY_ERROR",
				"请求体读取失败", err.Error(), logger, requestID)
			return
		}

		// 获取客户端密钥
		clientSecret := client.Secret
		if clientSecret == "" {
			logger.Error("客户端密钥未配置",
				zap.String("request_id", requestID),
				zap.String("client_id", signatureParams.ClientID))

			respondWithError(c, http.StatusInternalServerError, "CLIENT_SECRET_ERROR",
				"客户端配置错误", "请联系管理员", logger, requestID)
			return
		}

		// 解密客户端密钥（如果需要）
		if decrypted, decErr := security.DecryptSecret(clientSecret); decErr == nil {
			clientSecret = decrypted
		}

		// 准备签名验证的请求体
		bodyForSignature := prepareBodyForSignature(c.Request.URL.Path, requestBody)

		// 构建签名参数
		params := map[string]string{
			"timestamp": signatureParams.Timestamp,
			"nonce":     signatureParams.Nonce,
			"client_id": signatureParams.ClientID,
		}

		// 验证签名
		err = signatureService.VerifySignature(params, bodyForSignature, signatureParams.Signature, clientSecret)
		if err != nil {
			// 🚀 增强：记录详细的签名验证失败信息
			logger.Error("签名验证失败 - 详细信息",
				zap.String("request_id", requestID),
				zap.String("client_id", signatureParams.ClientID),
				zap.String("client_name", client.Name),
				zap.String("client_ip", c.ClientIP()),
				zap.String("user_agent", c.GetHeader("User-Agent")),
				zap.String("path", c.Request.URL.Path),
				zap.String("method", c.Request.Method),
				zap.String("timestamp", signatureParams.Timestamp),
				zap.String("nonce", signatureParams.Nonce),
				zap.String("provided_signature", signatureParams.Signature),
				zap.Int("signature_length", len(signatureParams.Signature)),
				zap.Int("body_length", len(bodyForSignature)),
				zap.String("content_type", c.GetHeader("Content-Type")),
				zap.Error(err))

			respondWithError(c, http.StatusUnauthorized, "SIGNATURE_VERIFICATION_FAILED",
				"签名验证失败", "请检查签名算法和参数", logger, requestID)
			return
		}

		// 标记nonce已使用（仅在启用nonce验证时）
		if !config.DisableNonceValidation {
			ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
			defer cancel()

			if err := nonceManager.MarkNonceUsed(ctx, signatureParams.ClientID, signatureParams.Nonce); err != nil {
				logger.Error("标记nonce已使用失败",
					zap.String("request_id", requestID),
					zap.String("client_id", signatureParams.ClientID),
					zap.Error(err))

				// 这里不返回错误，因为签名已验证成功，只记录警告
				logger.Warn("nonce标记失败，但签名验证成功，继续处理请求")
			}
		} else {
			logger.Debug("跳过nonce标记（nonce验证已禁用）",
				zap.String("request_id", requestID),
				zap.String("client_id", signatureParams.ClientID))
		}

		// 记录成功信息
		duration := time.Since(startTime)
		logger.Info("签名验证成功",
			zap.String("request_id", requestID),
			zap.String("client_id", signatureParams.ClientID),
			zap.Duration("duration", duration))

		// 将客户端信息存储到上下文
		c.Set("client_id_from_signature", signatureParams.ClientID)
		c.Set("signature_verified", true)
		c.Set("signature_verification_duration", duration)

		c.Next()
	}
}

// SignatureParameters 签名参数结构
type SignatureParameters struct {
	Timestamp string `json:"timestamp"`
	Nonce     string `json:"nonce"`
	Signature string `json:"signature"`
	ClientID  string `json:"client_id"`
}

// IsEmpty 检查是否所有参数都为空
func (sp *SignatureParameters) IsEmpty() bool {
	return sp.Timestamp == "" && sp.Nonce == "" && sp.Signature == "" && sp.ClientID == ""
}

// Validate 验证必要参数
func (sp *SignatureParameters) Validate() error {
	if sp.Timestamp == "" {
		return fmt.Errorf("缺少timestamp参数")
	}
	if sp.Signature == "" {
		return fmt.Errorf("缺少signature参数")
	}
	if sp.ClientID == "" {
		return fmt.Errorf("缺少client_id参数")
	}
	// nonce可以为空，会使用timestamp作为nonce
	return nil
}

// ValidateLength 验证参数长度
func (sp *SignatureParameters) ValidateLength() error {
	if len(sp.Timestamp) > 20 {
		return fmt.Errorf("timestamp长度超出限制")
	}
	if len(sp.Nonce) > 64 {
		return fmt.Errorf("nonce长度超出限制")
	}
	if len(sp.Signature) > 512 {
		return fmt.Errorf("signature长度超出限制")
	}
	if len(sp.ClientID) > 64 {
		return fmt.Errorf("client_id长度超出限制")
	}
	return nil
}

// 辅助函数

// extractSignatureParameters 提取签名参数
func extractSignatureParameters(c *gin.Context) (*SignatureParameters, error) {
	var params SignatureParameters

	if c.Request.URL.Path == "/api/gateway/execute" {
		// 统一网关：从JSON请求体中提取
		body, err := io.ReadAll(c.Request.Body)
		if err != nil {
			return nil, fmt.Errorf("读取请求体失败: %w", err)
		}

		// 重置请求体
		c.Request.Body = io.NopCloser(bytes.NewBuffer(body))

		// 🚀 支持多种字段名格式，提高兼容性
		var gatewayReq map[string]interface{}
		if err := json.Unmarshal(body, &gatewayReq); err != nil {
			return nil, fmt.Errorf("解析网关请求失败: %w", err)
		}

		// 提取clientId/client_id/username
		if clientId, ok := gatewayReq["clientId"].(string); ok && clientId != "" {
			params.ClientID = clientId
		} else if clientId, ok := gatewayReq["client_id"].(string); ok && clientId != "" {
			params.ClientID = clientId
		} else if clientId, ok := gatewayReq["clientID"].(string); ok && clientId != "" {
			params.ClientID = clientId
		} else if username, ok := gatewayReq["username"].(string); ok && username != "" {
			params.ClientID = username
		}

		// 提取timestamp
		if timestamp, ok := gatewayReq["timestamp"].(string); ok && timestamp != "" {
			params.Timestamp = timestamp
		} else if timestamp, ok := gatewayReq["timeStamp"].(string); ok && timestamp != "" {
			params.Timestamp = timestamp
		}

		// 提取sign/signature
		if sign, ok := gatewayReq["sign"].(string); ok && sign != "" {
			params.Signature = sign
		} else if signature, ok := gatewayReq["signature"].(string); ok && signature != "" {
			params.Signature = signature
		}

		// 使用timestamp作为nonce
		params.Nonce = params.Timestamp

		// 🔍 调试日志：记录提取的参数（生产环境可以移除）
		if gin.Mode() == gin.DebugMode {
			fmt.Printf("[DEBUG] 统一网关参数提取: clientID=%s, timestamp=%s, signature=%s\n",
				params.ClientID, params.Timestamp, params.Signature)
			fmt.Printf("[DEBUG] 原始请求体: %s\n", string(body))
		}
	} else {
		// 传统API：从HTTP头中提取
		params.Timestamp = c.GetHeader("X-Timestamp")
		params.Nonce = c.GetHeader("X-Nonce")
		params.Signature = c.GetHeader("X-Signature")
		params.ClientID = c.GetHeader("X-Client-ID")

		// 如果nonce为空，使用timestamp
		if params.Nonce == "" {
			params.Nonce = params.Timestamp
		}
	}

	return &params, nil
}

// readRequestBody 读取请求体
func readRequestBody(c *gin.Context) ([]byte, error) {
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		return nil, fmt.Errorf("读取请求体失败: %w", err)
	}

	// 重置请求体供后续处理使用
	c.Request.Body = io.NopCloser(bytes.NewBuffer(body))

	return body, nil
}

// prepareBodyForSignature 准备用于签名验证的请求体
func prepareBodyForSignature(path string, body []byte) []byte {
	if path == "/api/gateway/execute" {
		// 统一网关：移除sign字段
		return removeSignFieldFromJSONEnhanced(body)
	}
	// 传统API：直接使用原始请求体
	return body
}

// removeSignFieldFromJSONEnhanced 从JSON中移除sign字段（增强版）
// 使用字符串操作而不是JSON解析，以保持字段顺序
func removeSignFieldFromJSONEnhanced(jsonData []byte) []byte {
	jsonStr := string(jsonData)

	// 使用正则表达式移除sign字段
	// 匹配 "sign":"value", 或 ,"sign":"value" 或 "sign":"value"}
	signPattern := `(?:,\s*"sign"\s*:\s*"[^"]*")|(?:"sign"\s*:\s*"[^"]*"\s*,?)`

	// 使用正则表达式替换
	re := regexp.MustCompile(signPattern)
	result := re.ReplaceAllString(jsonStr, "")

	// 清理可能的多余逗号
	result = regexp.MustCompile(`,,`).ReplaceAllString(result, ",")
	result = regexp.MustCompile(`{\s*,`).ReplaceAllString(result, "{")
	result = regexp.MustCompile(`,\s*}`).ReplaceAllString(result, "}")

	return []byte(result)
}

// respondWithError 统一错误响应
func respondWithError(c *gin.Context, httpStatus int, errorCode, userMessage, details string, logger *zap.Logger, requestID string) {
	response := gin.H{
		"success":    false,
		"code":       httpStatus,
		"message":    userMessage,
		"error":      errorCode,
		"request_id": requestID,
		"timestamp":  util.NowBeijing().Unix(),
	}

	// 在开发环境中包含详细错误信息
	if gin.Mode() == gin.DebugMode {
		response["details"] = details
	}

	logger.Warn("签名验证失败响应",
		zap.String("request_id", requestID),
		zap.Int("status", httpStatus),
		zap.String("error_code", errorCode),
		zap.String("user_message", userMessage),
		zap.String("details", details))

	c.JSON(httpStatus, response)
	c.Abort()
}

// getBeijingLocation 获取北京时区
func getBeijingLocation() *time.Location {
	location, _ := time.LoadLocation("Asia/Shanghai")
	return location
}

// validateGatewayTimestampSmart 智能验证统一网关的timestamp（优化用户体验）
func validateGatewayTimestampSmart(ctx context.Context, clientID, timestamp string, redisClient interface{}, logger *zap.Logger, requestID string) error {
	// 基础格式验证
	if timestamp == "" {
		return fmt.Errorf("timestamp不能为空")
	}

	// 检查是否为数字
	timestampInt, err := strconv.ParseInt(timestamp, 10, 64)
	if err != nil {
		return fmt.Errorf("timestamp必须是数字格式")
	}

	// 解析timestamp并检查是否过期
	var timestampTime time.Time
	var isMillisec bool

	// 判断是秒级还是毫秒级时间戳
	if timestampInt > 1e12 {
		// 毫秒级时间戳
		timestampTime = time.UnixMilli(timestampInt)
		isMillisec = true
	} else {
		// 秒级时间戳
		timestampTime = time.Unix(timestampInt, 0)
		isMillisec = false
	}

	// 使用北京时间进行比较
	location := getBeijingLocation()
	now := util.NowBeijing().In(location)
	timestampTime = timestampTime.In(location)

	// 🚀 优化：大幅扩展有效期到30分钟，适应高并发和网络延迟
	validityDuration := 30 * time.Minute
	if now.Sub(timestampTime) > validityDuration {
		return fmt.Errorf("timestamp已过期，超过%v", validityDuration)
	}

	// 🚀 优化：允许更大的时钟偏差（2分钟），适应分布式环境和网络延迟
	if timestampTime.Sub(now) > 2*time.Minute {
		return fmt.Errorf("timestamp来自未来，时钟可能不同步")
	}

	// 🚀 智能防重放检查：对于秒级时间戳，添加客户端标识避免误判
	if err := checkTimestampDuplicateSmart(ctx, clientID, timestamp, isMillisec, redisClient, logger); err != nil {
		return err
	}

	logger.Debug("统一网关timestamp验证通过",
		zap.String("request_id", requestID),
		zap.String("client_id", clientID),
		zap.String("timestamp", timestamp),
		zap.Bool("is_millisec", isMillisec),
		zap.Duration("validity", validityDuration))

	return nil
}

// checkTimestampDuplicateSmart 智能检查timestamp重复（优化用户体验）
func checkTimestampDuplicateSmart(ctx context.Context, clientID, timestamp string, isMillisec bool, redisClient interface{}, logger *zap.Logger) error {
	// 🚀 修复：直接使用go-redis客户端类型断言
	client, ok := redisClient.(*redis.Client)
	if !ok {
		// 记录详细的类型信息用于调试
		logger.Warn("Redis客户端类型断言失败，跳过重复检查",
			zap.String("client_id", clientID),
			zap.String("timestamp", timestamp),
			zap.Bool("is_millisec", isMillisec),
			zap.String("expected_type", "*redis.Client"),
			zap.String("actual_type", fmt.Sprintf("%T", redisClient)))
		return nil
	}

	var timestampKey string
	var expiration time.Duration

	if isMillisec {
		// 🚀 毫秒级时间戳：直接使用timestamp作为键，30分钟过期
		timestampKey = fmt.Sprintf("gateway_timestamp_ms:%s:%s", clientID, timestamp)
		expiration = 30 * time.Minute
	} else {
		// 🚀 秒级时间戳：使用计数器机制，大幅放宽限制适应高并发（最多50次）
		timestampKey = fmt.Sprintf("gateway_timestamp_sec:%s:%s", clientID, timestamp)
		expiration = 30 * time.Minute // 与有效期保持一致

		// 检查同一秒内的请求次数
		countCmd := client.Incr(ctx, timestampKey)
		count, err := countCmd.Result()
		if err != nil {
			logger.Warn("Redis计数操作失败，跳过重复检查", zap.Error(err))
			return nil
		}

		// 设置过期时间
		client.Expire(ctx, timestampKey, expiration)

		// 🚀 大幅放宽限制：允许同一秒内最多50次请求，适应高并发订单场景
		if count > 50 {
			logger.Warn("同一秒内请求次数过多",
				zap.String("client_id", clientID),
				zap.String("timestamp", timestamp),
				zap.Int64("count", count))
			return fmt.Errorf("同一秒内请求过于频繁（超过50次），建议使用毫秒级时间戳")
		}

		// 🚀 只在请求次数较多时记录警告日志
		if count > 20 {
			logger.Warn("同一秒内高频请求",
				zap.String("client_id", clientID),
				zap.String("timestamp", timestamp),
				zap.Int64("count", count))
		} else {
			logger.Debug("秒级时间戳请求计数",
				zap.String("client_id", clientID),
				zap.String("timestamp", timestamp),
				zap.Int64("count", count))
		}

		return nil
	}

	// 毫秒级时间戳使用SET NX确保唯一性
	cmd := client.SetNX(ctx, timestampKey, util.NowBeijing().Unix(), expiration)
	result, err := cmd.Result()
	if err != nil {
		logger.Warn("Redis操作失败，跳过重复检查", zap.Error(err))
		return nil // 不因为Redis错误阻止请求
	}

	if !result {
		// timestamp已存在，说明已被使用
		return fmt.Errorf("timestamp已被使用，请使用新的timestamp")
	}

	return nil
}
