package routes

import (
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"github.com/your-org/go-kuaidi/internal/auth"
	"github.com/your-org/go-kuaidi/internal/handler"
	"github.com/your-org/go-kuaidi/internal/middleware"
	"github.com/your-org/go-kuaidi/internal/repository"
	"github.com/your-org/go-kuaidi/internal/service/callback"
)

// SetupCallbackRetryRoutes 设置回调重试相关路由
// 🎯 核心功能：配置回调重试管理的API路由
func SetupCallbackRetryRoutes(
	router *gin.Engine,
	callbackRepository repository.CallbackRepository,
	retryServiceManager *callback.RetryServiceManager,
	tokenService auth.TokenService,
	logger *zap.Logger,
) {
	// 创建回调重试处理器
	retryHandler := handler.NewCallbackRetryHandler(
		callbackRepository,
		retryServiceManager.GetScheduler(),
		logger,
	)

	// 回调重试管理路由组（需要JWT认证）
	retryGroup := router.Group("/api/v1/callback/retry")
	retryGroup.Use(auth.JWTAuthMiddleware(tokenService)) // JWT认证中间件
	{
		// 重试记录管理
		retryGroup.GET("/records", retryHandler.GetRetryRecords)    // 获取重试记录列表
		retryGroup.GET("/records/:id", retryHandler.GetRetryRecord) // 获取重试记录详情

		// 手动重试操作
		retryGroup.POST("/manual/:id", retryHandler.ManualRetry) // 手动重试
		retryGroup.POST("/cancel/:id", retryHandler.CancelRetry) // 取消重试

		// 重试状态查询
		retryGroup.GET("/status", retryHandler.GetRetryStatus) // 获取重试状态
	}

	// 管理员专用路由组（需要管理员权限）
	adminRetryGroup := router.Group("/api/v1/admin/callback/retry")
	adminRetryGroup.Use(auth.JWTAuthMiddleware(tokenService))
	adminRetryGroup.Use(middleware.RequireAdminAuth()) // 管理员权限中间件
	{
		// 系统级重试管理
		adminRetryGroup.GET("/statistics", getRetryStatistics(callbackRepository, logger))
		adminRetryGroup.GET("/config", getRetryConfig(retryServiceManager, logger))
		adminRetryGroup.PUT("/config", updateRetryConfig(retryServiceManager, logger))

		// 批量操作
		adminRetryGroup.POST("/batch/cancel", batchCancelRetries(callbackRepository, logger))
		adminRetryGroup.POST("/batch/retry", batchRetryFailed(retryServiceManager, logger))

		// 服务控制
		adminRetryGroup.POST("/service/start", startRetryService(retryServiceManager, logger))
		adminRetryGroup.POST("/service/stop", stopRetryService(retryServiceManager, logger))
		adminRetryGroup.POST("/service/restart", restartRetryService(retryServiceManager, logger))
	}

	logger.Info("回调重试路由已配置",
		zap.String("user_routes", "/api/v1/callback/retry/*"),
		zap.String("admin_routes", "/api/v1/admin/callback/retry/*"))
}

// getRetryStatistics 获取重试统计信息
func getRetryStatistics(callbackRepository repository.CallbackRepository, logger *zap.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		// TODO: 实现重试统计查询
		c.JSON(200, gin.H{
			"success": true,
			"message": "重试统计功能开发中",
			"data":    nil,
		})
	}
}

// getRetryConfig 获取重试配置
func getRetryConfig(retryServiceManager *callback.RetryServiceManager, logger *zap.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		scheduler := retryServiceManager.GetScheduler()
		if scheduler == nil {
			c.JSON(500, gin.H{
				"success": false,
				"message": "重试调度器未初始化",
			})
			return
		}

		metrics := scheduler.GetMetrics()
		status := retryServiceManager.GetStatus()

		c.JSON(200, gin.H{
			"success": true,
			"message": "获取重试配置成功",
			"data": gin.H{
				"status":  status,
				"metrics": metrics,
			},
		})
	}
}

// updateRetryConfig 更新重试配置
func updateRetryConfig(retryServiceManager *callback.RetryServiceManager, logger *zap.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		// TODO: 实现配置更新
		c.JSON(200, gin.H{
			"success": true,
			"message": "配置更新功能开发中",
		})
	}
}

// batchCancelRetries 批量取消重试
func batchCancelRetries(callbackRepository repository.CallbackRepository, logger *zap.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		// TODO: 实现批量取消重试
		c.JSON(200, gin.H{
			"success": true,
			"message": "批量取消功能开发中",
		})
	}
}

// batchRetryFailed 批量重试失败的记录
func batchRetryFailed(retryServiceManager *callback.RetryServiceManager, logger *zap.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		// TODO: 实现批量重试
		c.JSON(200, gin.H{
			"success": true,
			"message": "批量重试功能开发中",
		})
	}
}

// startRetryService 启动重试服务
func startRetryService(retryServiceManager *callback.RetryServiceManager, logger *zap.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		if err := retryServiceManager.Start(); err != nil {
			logger.Error("启动重试服务失败", zap.Error(err))
			c.JSON(500, gin.H{
				"success": false,
				"message": "启动重试服务失败",
				"error":   err.Error(),
			})
			return
		}

		logger.Info("重试服务已通过API启动")
		c.JSON(200, gin.H{
			"success": true,
			"message": "重试服务已启动",
		})
	}
}

// stopRetryService 停止重试服务
func stopRetryService(retryServiceManager *callback.RetryServiceManager, logger *zap.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		if err := retryServiceManager.Stop(); err != nil {
			logger.Error("停止重试服务失败", zap.Error(err))
			c.JSON(500, gin.H{
				"success": false,
				"message": "停止重试服务失败",
				"error":   err.Error(),
			})
			return
		}

		logger.Info("重试服务已通过API停止")
		c.JSON(200, gin.H{
			"success": true,
			"message": "重试服务已停止",
		})
	}
}

// restartRetryService 重启重试服务
func restartRetryService(retryServiceManager *callback.RetryServiceManager, logger *zap.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 先停止
		if err := retryServiceManager.Stop(); err != nil {
			logger.Error("停止重试服务失败", zap.Error(err))
			c.JSON(500, gin.H{
				"success": false,
				"message": "重启失败：停止服务时出错",
				"error":   err.Error(),
			})
			return
		}

		// 再启动
		if err := retryServiceManager.Start(); err != nil {
			logger.Error("启动重试服务失败", zap.Error(err))
			c.JSON(500, gin.H{
				"success": false,
				"message": "重启失败：启动服务时出错",
				"error":   err.Error(),
			})
			return
		}

		logger.Info("重试服务已通过API重启")
		c.JSON(200, gin.H{
			"success": true,
			"message": "重试服务已重启",
		})
	}
}

// 🔥 健康检查路由（无需认证）
func SetupRetryHealthRoutes(router *gin.Engine, retryServiceManager *callback.RetryServiceManager) {
	router.GET("/health/retry", func(c *gin.Context) {
		status := retryServiceManager.GetStatus()

		httpStatus := 200
		if !status.Running {
			httpStatus = 503 // Service Unavailable
		}

		c.JSON(httpStatus, gin.H{
			"service":   "callback-retry",
			"status":    status,
			"timestamp": time.Now().Unix(),
			"healthy":   status.Running,
		})
	})
}

// 🔥 Prometheus指标路由（无需认证）
func SetupRetryMetricsRoutes(router *gin.Engine, retryServiceManager *callback.RetryServiceManager) {
	router.GET("/metrics/retry", func(c *gin.Context) {
		scheduler := retryServiceManager.GetScheduler()
		if scheduler == nil {
			c.JSON(503, gin.H{
				"error": "retry scheduler not available",
			})
			return
		}

		metrics := scheduler.GetMetrics()

		// 返回Prometheus格式的指标
		c.Header("Content-Type", "text/plain")
		c.String(200, `# HELP callback_retry_total_scheduled Total number of scheduled retries
# TYPE callback_retry_total_scheduled counter
callback_retry_total_scheduled %d

# HELP callback_retry_total_executed Total number of executed retries
# TYPE callback_retry_total_executed counter
callback_retry_total_executed %d

# HELP callback_retry_total_succeeded Total number of successful retries
# TYPE callback_retry_total_succeeded counter
callback_retry_total_succeeded %d

# HELP callback_retry_total_failed Total number of failed retries
# TYPE callback_retry_total_failed counter
callback_retry_total_failed %d

# HELP callback_retry_average_latency_ms Average execution latency in milliseconds
# TYPE callback_retry_average_latency_ms gauge
callback_retry_average_latency_ms %d

# HELP callback_retry_active_workers Number of active worker goroutines
# TYPE callback_retry_active_workers gauge
callback_retry_active_workers %d
`,
			metrics.TotalScheduled,
			metrics.TotalExecuted,
			metrics.TotalSucceeded,
			metrics.TotalFailed,
			metrics.AverageLatencyMs,
			metrics.ActiveWorkers,
		)
	})
}
