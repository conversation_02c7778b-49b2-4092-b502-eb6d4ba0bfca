package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/your-org/go-kuaidi/api/handler"
)

// SetupAdminRawCallbackRoutes 设置管理员原始回调路由
func SetupAdminRawCallbackRoutes(router *gin.Engine, handler *handler.AdminRawCallbackHandler) {
	// 管理员原始回调管理路由组
	adminRawCallbackGroup := router.Group("/api/v1/admin/raw-callbacks")

	// 应用管理员认证中间件
	// adminRawCallbackGroup.Use(middleware.AdminAuthMiddleware()) // TODO: 需要实现AdminAuthMiddleware

	{
		// 获取原始回调列表
		adminRawCallbackGroup.GET("/records", handler.GetRawCallbackList)

		// 获取原始回调详情
		adminRawCallbackGroup.GET("/records/:id", handler.GetRawCallbackDetail)

		// 重推单个原始回调
		adminRawCallbackGroup.POST("/retry/:id", handler.RetryRawCallback)

		// 批量重推原始回调
		adminRawCallbackGroup.POST("/batch-retry", handler.BatchRetryRawCallbacks)

		// 按条件批量重推
		adminRawCallbackGroup.POST("/batch-retry-by-condition", handler.BatchRetryByCondition)

		// 获取原始回调统计
		adminRawCallbackGroup.GET("/statistics", handler.GetRawCallbackStatistics)

		// 导出原始回调数据
		adminRawCallbackGroup.GET("/export", handler.ExportRawCallbacks)
	}
}
