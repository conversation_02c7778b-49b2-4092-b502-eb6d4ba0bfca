package handler

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/repository"
	"github.com/your-org/go-kuaidi/internal/util"

)

// Response 通用响应结构
type Response struct {
	Success bool        `json:"success"`
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// OrderAttemptHandler 下单尝试记录处理器
type OrderAttemptHandler struct {
	orderAttemptRepo repository.OrderAttemptRepository
	logger           *zap.Logger
}

// NewOrderAttemptHandler 创建下单尝试记录处理器
func NewOrderAttemptHandler(orderAttemptRepo repository.OrderAttemptRepository, logger *zap.Logger) *OrderAttemptHandler {
	return &OrderAttemptHandler{
		orderAttemptRepo: orderAttemptRepo,
		logger:           logger,
	}
}

// GetOrderAttempts 获取订单尝试记录
// @Summary 获取订单尝试记录
// @Description 根据客户订单号获取详细的下单尝试记录
// @Tags 订单尝试记录
// @Accept json
// @Produce json
// @Param customer_order_no path string true "客户订单号"
// @Success 200 {object} Response{data=[]model.OrderAttempt}
// @Failure 400 {object} Response
// @Failure 500 {object} Response
// @Router /api/v1/order-attempts/{customer_order_no} [get]
func (h *OrderAttemptHandler) GetOrderAttempts(c *gin.Context) {
	customerOrderNo := c.Param("customer_order_no")
	if customerOrderNo == "" {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Code:    http.StatusBadRequest,
			Message: "客户订单号不能为空",
		})
		return
	}

	attempts, err := h.orderAttemptRepo.GetByCustomerOrderNo(c.Request.Context(), customerOrderNo)
	if err != nil {
		h.logger.Error("获取订单尝试记录失败",
			zap.String("customer_order_no", customerOrderNo),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Code:    http.StatusInternalServerError,
			Message: "获取订单尝试记录失败",
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Code:    http.StatusOK,
		Message: "获取成功",
		Data:    attempts,
	})
}

// GetOrderAttemptSummary 获取订单尝试汇总
// @Summary 获取订单尝试汇总
// @Description 获取订单的尝试汇总信息
// @Tags 订单尝试记录
// @Accept json
// @Produce json
// @Param customer_order_no path string true "客户订单号"
// @Success 200 {object} Response{data=model.OrderAttemptSummary}
// @Failure 400 {object} Response
// @Failure 500 {object} Response
// @Router /api/v1/order-attempts/{customer_order_no}/summary [get]
func (h *OrderAttemptHandler) GetOrderAttemptSummary(c *gin.Context) {
	customerOrderNo := c.Param("customer_order_no")
	if customerOrderNo == "" {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Code:    http.StatusBadRequest,
			Message: "客户订单号不能为空",
		})
		return
	}

	summary, err := h.orderAttemptRepo.GetSummary(c.Request.Context(), customerOrderNo)
	if err != nil {
		h.logger.Error("获取订单尝试汇总失败",
			zap.String("customer_order_no", customerOrderNo),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Code:    http.StatusInternalServerError,
			Message: "获取订单尝试汇总失败",
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Code:    http.StatusOK,
		Message: "获取成功",
		Data:    summary,
	})
}

// GetFailedAttempts 获取失败的尝试记录
// @Summary 获取失败的尝试记录
// @Description 获取失败的下单尝试记录，支持多种过滤条件
// @Tags 订单尝试记录
// @Accept json
// @Produce json
// @Param customer_order_no query string false "客户订单号"
// @Param user_id query string false "用户ID"
// @Param provider query string false "供应商"
// @Param express_type query string false "快递公司类型"
// @Param attempt_stage query string false "尝试阶段"
// @Param start_time query string false "开始时间 (RFC3339格式)"
// @Param end_time query string false "结束时间 (RFC3339格式)"
// @Param limit query int false "限制数量" default(20)
// @Param offset query int false "偏移量" default(0)
// @Success 200 {object} Response{data=[]model.OrderAttempt}
// @Failure 400 {object} Response
// @Failure 500 {object} Response
// @Router /api/v1/order-attempts/failed [get]
func (h *OrderAttemptHandler) GetFailedAttempts(c *gin.Context) {
	filter := &model.OrderAttemptFilter{
		CustomerOrderNo: c.Query("customer_order_no"),
		UserID:          c.Query("user_id"),
		Provider:        c.Query("provider"),
		ExpressType:     c.Query("express_type"),
		AttemptStage:    c.Query("attempt_stage"),
		Limit:           20, // 默认限制
		Offset:          0,  // 默认偏移
	}

	// 解析时间参数
	if startTimeStr := c.Query("start_time"); startTimeStr != "" {
		if startTime, err := time.Parse(time.RFC3339, startTimeStr); err == nil {
			filter.StartTime = startTime
		}
	}
	if endTimeStr := c.Query("end_time"); endTimeStr != "" {
		if endTime, err := time.Parse(time.RFC3339, endTimeStr); err == nil {
			filter.EndTime = endTime
		}
	}

	// 解析分页参数
	if limitStr := c.Query("limit"); limitStr != "" {
		if limit, err := strconv.Atoi(limitStr); err == nil && limit > 0 {
			filter.Limit = limit
		}
	}
	if offsetStr := c.Query("offset"); offsetStr != "" {
		if offset, err := strconv.Atoi(offsetStr); err == nil && offset >= 0 {
			filter.Offset = offset
		}
	}

	attempts, err := h.orderAttemptRepo.GetFailedAttempts(c.Request.Context(), filter)
	if err != nil {
		h.logger.Error("获取失败的订单尝试记录失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Code:    http.StatusInternalServerError,
			Message: "获取失败的订单尝试记录失败",
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Code:    http.StatusOK,
		Message: "获取成功",
		Data:    attempts,
	})
}

// GetUserAttempts 获取用户的尝试记录
// @Summary 获取用户的尝试记录
// @Description 根据用户ID获取该用户的下单尝试记录
// @Tags 订单尝试记录
// @Accept json
// @Produce json
// @Param user_id path string true "用户ID"
// @Param limit query int false "限制数量" default(20)
// @Param offset query int false "偏移量" default(0)
// @Success 200 {object} Response{data=[]model.OrderAttempt}
// @Failure 400 {object} Response
// @Failure 500 {object} Response
// @Router /api/v1/order-attempts/user/{user_id} [get]
func (h *OrderAttemptHandler) GetUserAttempts(c *gin.Context) {
	userID := c.Param("user_id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Code:    http.StatusBadRequest,
			Message: "用户ID不能为空",
		})
		return
	}

	limit := 20
	offset := 0

	if limitStr := c.Query("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 {
			limit = l
		}
	}
	if offsetStr := c.Query("offset"); offsetStr != "" {
		if o, err := strconv.Atoi(offsetStr); err == nil && o >= 0 {
			offset = o
		}
	}

	attempts, err := h.orderAttemptRepo.GetByUserID(c.Request.Context(), userID, limit, offset)
	if err != nil {
		h.logger.Error("获取用户订单尝试记录失败",
			zap.String("user_id", userID),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Code:    http.StatusInternalServerError,
			Message: "获取用户订单尝试记录失败",
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Code:    http.StatusOK,
		Message: "获取成功",
		Data:    attempts,
	})
}

// DeleteOldAttempts 删除旧的尝试记录
// @Summary 删除旧的尝试记录
// @Description 删除指定时间之前的尝试记录（管理员功能）
// @Tags 订单尝试记录
// @Accept json
// @Produce json
// @Param days query int true "保留天数"
// @Success 200 {object} Response
// @Failure 400 {object} Response
// @Failure 500 {object} Response
// @Router /api/v1/order-attempts/cleanup [delete]
func (h *OrderAttemptHandler) DeleteOldAttempts(c *gin.Context) {
	daysStr := c.Query("days")
	if daysStr == "" {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Code:    http.StatusBadRequest,
			Message: "保留天数不能为空",
		})
		return
	}

	days, err := strconv.Atoi(daysStr)
	if err != nil || days <= 0 {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Code:    http.StatusBadRequest,
			Message: "保留天数必须是正整数",
		})
		return
	}

	beforeTime := util.NowBeijing().AddDate(0, 0, -days)
	err = h.orderAttemptRepo.DeleteOldRecords(c.Request.Context(), beforeTime)
	if err != nil {
		h.logger.Error("删除旧的订单尝试记录失败",
			zap.Int("days", days),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Code:    http.StatusInternalServerError,
			Message: "删除旧的订单尝试记录失败",
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Code:    http.StatusOK,
		Message: "删除成功",
	})
}
