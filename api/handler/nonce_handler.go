package handler

import (
	"context"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"github.com/your-org/go-kuaidi/internal/security"
	"github.com/your-org/go-kuaidi/internal/util"

)

// NonceHandler nonce管理处理器
type NonceHandler struct {
	nonceManager security.NonceManager
	logger       *zap.Logger
}

// NewNonceHandler 创建nonce处理器
func NewNonceHandler(nonceManager security.NonceManager, logger *zap.Logger) *NonceHandler {
	return &NonceHandler{
		nonceManager: nonceManager,
		logger:       logger,
	}
}

// GenerateNonce 生成nonce
// @Summary 生成nonce
// @Description 为指定客户端生成新的nonce
// @Tags nonce
// @Accept json
// @Produce json
// @Param request body GenerateNonceRequest true "生成nonce请求"
// @Success 200 {object} GenerateNonceResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/admin/nonce/generate [post]
func (h *NonceHandler) GenerateNonce(c *gin.Context) {
	var req GenerateNonceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("生成nonce请求参数错误",
			zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"code":    http.StatusBadRequest,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 生成nonce
	nonce, err := h.nonceManager.GenerateNonce(req.ClientID)
	if err != nil {
		h.logger.Error("生成nonce失败",
			zap.String("client_id", req.ClientID),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"code":    http.StatusInternalServerError,
			"message": "生成nonce失败",
			"error":   err.Error(),
		})
		return
	}

	h.logger.Info("生成nonce成功",
		zap.String("client_id", req.ClientID),
		zap.String("nonce", nonce[:16]+"..."))

	c.JSON(http.StatusOK, GenerateNonceResponse{
		Success: true,
		Code:    http.StatusOK,
		Message: "生成nonce成功",
		Data: NonceData{
			Nonce:     nonce,
			ClientID:  req.ClientID,
			ExpiresAt: util.NowBeijing().Add(5 * time.Minute),
		},
	})
}

// ValidateNonce 验证nonce
// @Summary 验证nonce
// @Description 验证指定nonce是否有效
// @Tags nonce
// @Accept json
// @Produce json
// @Param request body ValidateNonceRequest true "验证nonce请求"
// @Success 200 {object} ValidateNonceResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/admin/nonce/validate [post]
func (h *NonceHandler) ValidateNonce(c *gin.Context) {
	var req ValidateNonceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("验证nonce请求参数错误",
			zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"code":    http.StatusBadRequest,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证nonce
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	result, err := h.nonceManager.ValidateNonce(ctx, req.ClientID, req.Nonce)
	if err != nil {
		h.logger.Error("验证nonce失败",
			zap.String("client_id", req.ClientID),
			zap.String("nonce", req.Nonce[:16]+"..."),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"code":    http.StatusInternalServerError,
			"message": "验证nonce失败",
			"error":   err.Error(),
		})
		return
	}

	h.logger.Info("验证nonce完成",
		zap.String("client_id", req.ClientID),
		zap.String("nonce", req.Nonce[:16]+"..."),
		zap.Bool("valid", result.Valid))

	c.JSON(http.StatusOK, ValidateNonceResponse{
		Success: true,
		Code:    http.StatusOK,
		Message: "验证nonce完成",
		Data:    *result,
	})
}

// GetNonceStats 获取nonce统计信息
// @Summary 获取nonce统计
// @Description 获取nonce使用统计信息
// @Tags nonce
// @Produce json
// @Success 200 {object} NonceStatsResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/admin/nonce/stats [get]
func (h *NonceHandler) GetNonceStats(c *gin.Context) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	stats, err := h.nonceManager.GetNonceStats(ctx)
	if err != nil {
		h.logger.Error("获取nonce统计失败",
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"code":    http.StatusInternalServerError,
			"message": "获取nonce统计失败",
			"error":   err.Error(),
		})
		return
	}

	h.logger.Debug("获取nonce统计成功",
		zap.Int64("total_generated", stats.TotalGenerated),
		zap.Int64("total_used", stats.TotalUsed),
		zap.Int64("active_count", stats.ActiveNonceCount))

	c.JSON(http.StatusOK, NonceStatsResponse{
		Success: true,
		Code:    http.StatusOK,
		Message: "获取nonce统计成功",
		Data:    *stats,
	})
}

// CleanupExpiredNonces 清理过期nonce
// @Summary 清理过期nonce
// @Description 手动清理过期的nonce
// @Tags nonce
// @Produce json
// @Success 200 {object} CleanupResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/admin/nonce/cleanup [post]
func (h *NonceHandler) CleanupExpiredNonces(c *gin.Context) {
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	err := h.nonceManager.CleanupExpiredNonces(ctx)
	if err != nil {
		h.logger.Error("清理过期nonce失败",
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"code":    http.StatusInternalServerError,
			"message": "清理过期nonce失败",
			"error":   err.Error(),
		})
		return
	}

	h.logger.Info("清理过期nonce成功")

	c.JSON(http.StatusOK, CleanupResponse{
		Success: true,
		Code:    http.StatusOK,
		Message: "清理过期nonce成功",
		Data: CleanupData{
			CleanupTime: util.NowBeijing(),
			Status:      "completed",
		},
	})
}

// 请求和响应结构体

type GenerateNonceRequest struct {
	ClientID string `json:"client_id" binding:"required"`
}

type ValidateNonceRequest struct {
	ClientID string `json:"client_id" binding:"required"`
	Nonce    string `json:"nonce" binding:"required"`
}

type NonceData struct {
	Nonce     string    `json:"nonce"`
	ClientID  string    `json:"client_id"`
	ExpiresAt time.Time `json:"expires_at"`
}

type CleanupData struct {
	CleanupTime time.Time `json:"cleanup_time"`
	Status      string    `json:"status"`
}

type GenerateNonceResponse struct {
	Success bool      `json:"success"`
	Code    int       `json:"code"`
	Message string    `json:"message"`
	Data    NonceData `json:"data"`
}

type ValidateNonceResponse struct {
	Success bool                           `json:"success"`
	Code    int                            `json:"code"`
	Message string                         `json:"message"`
	Data    security.NonceValidationResult `json:"data"`
}

type NonceStatsResponse struct {
	Success bool                `json:"success"`
	Code    int                 `json:"code"`
	Message string              `json:"message"`
	Data    security.NonceStats `json:"data"`
}

type CleanupResponse struct {
	Success bool        `json:"success"`
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    CleanupData `json:"data"`
}

// ErrorResponse 已在其他文件中定义，这里不重复定义
