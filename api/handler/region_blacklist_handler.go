package handler

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/your-org/go-kuaidi/internal/service"
	"go.uber.org/zap"
)

// RegionBlacklistHandler 地区黑名单管理处理器
type RegionBlacklistHandler struct {
	blacklistService *service.RegionBlacklistService
	logger           *zap.Logger
}

// NewRegionBlacklistHandler 创建地区黑名单处理器
func NewRegionBlacklistHandler(blacklistService *service.RegionBlacklistService, logger *zap.Logger) *RegionBlacklistHandler {
	return &RegionBlacklistHandler{
		blacklistService: blacklistService,
		logger:           logger.Named("region_blacklist_handler"),
	}
}

// GetBlacklistStatistics 获取黑名单统计信息
// @Summary 获取地区黑名单统计信息
// @Description 获取云通供应商地区路线黑名单的统计信息
// @Tags 系统管理
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "统计信息"
// @Router /admin/region-blacklist/statistics [get]
func (h *RegionBlacklistHandler) GetBlacklistStatistics(c *gin.Context) {
	stats := h.blacklistService.GetStatistics()

	h.logger.Info("获取黑名单统计信息",
		zap.Any("statistics", stats))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取统计信息成功",
		"data":    stats,
	})
}

// GetAllBlacklistEntries 获取所有黑名单条目
// @Summary 获取所有黑名单条目
// @Description 获取所有在黑名单中的地区路线条目
// @Tags 系统管理
// @Accept json
// @Produce json
// @Success 200 {array} service.BlacklistEntry "黑名单条目列表"
// @Router /admin/region-blacklist/entries [get]
func (h *RegionBlacklistHandler) GetAllBlacklistEntries(c *gin.Context) {
	entries := h.blacklistService.GetAllBlacklistEntries()

	h.logger.Info("获取所有黑名单条目",
		zap.Int("total_entries", len(entries)))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取黑名单条目成功",
		"data":    entries,
		"total":   len(entries),
	})
}

// GetBlacklistEntry 获取特定黑名单条目
// @Summary 获取特定黑名单条目
// @Description 根据供应商、路线和快递公司获取特定黑名单条目
// @Tags 系统管理
// @Accept json
// @Produce json
// @Param provider query string true "供应商代码"
// @Param route query string true "路线，如'上海市->安徽省'"
// @Param express_code query string true "快递公司代码"
// @Success 200 {object} service.BlacklistEntry "黑名单条目详情"
// @Router /admin/region-blacklist/entry [get]
func (h *RegionBlacklistHandler) GetBlacklistEntry(c *gin.Context) {
	provider := c.Query("provider")
	route := c.Query("route")
	expressCode := c.Query("express_code")

	if provider == "" || route == "" || expressCode == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "缺少必要参数：provider, route, express_code",
		})
		return
	}

	entry := h.blacklistService.GetBlacklistEntry(provider, route, expressCode)
	if entry == nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "未找到对应的黑名单条目",
		})
		return
	}

	h.logger.Info("获取特定黑名单条目",
		zap.String("provider", provider),
		zap.String("route", route),
		zap.String("express_code", expressCode),
		zap.Bool("is_blacklisted", entry.IsBlacklisted))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取黑名单条目成功",
		"data":    entry,
	})
}

// RemoveFromBlacklist 从黑名单中移除条目
// @Summary 从黑名单中移除条目
// @Description 将指定的地区路线从黑名单中移除
// @Tags 系统管理
// @Accept json
// @Produce json
// @Param provider formData string true "供应商代码"
// @Param route formData string true "路线，如'上海市->安徽省'"
// @Param express_code formData string true "快递公司代码"
// @Success 200 {object} map[string]interface{} "操作结果"
// @Router /admin/region-blacklist/remove [post]
func (h *RegionBlacklistHandler) RemoveFromBlacklist(c *gin.Context) {
	provider := c.PostForm("provider")
	route := c.PostForm("route")
	expressCode := c.PostForm("express_code")

	if provider == "" || route == "" || expressCode == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "缺少必要参数：provider, route, express_code",
		})
		return
	}

	// 检查条目是否存在
	entry := h.blacklistService.GetBlacklistEntry(provider, route, expressCode)
	if entry == nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "未找到对应的黑名单条目",
		})
		return
	}

	// 从黑名单中移除
	h.blacklistService.RemoveFromBlacklist(provider, route, expressCode)

	h.logger.Info("从黑名单中移除条目",
		zap.String("provider", provider),
		zap.String("route", route),
		zap.String("express_code", expressCode))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "已从黑名单中移除",
		"data": map[string]string{
			"provider":     provider,
			"route":        route,
			"express_code": expressCode,
		},
	})
}

// ClearExpiredEntries 清理过期的黑名单条目
// @Summary 清理过期的黑名单条目
// @Description 清理30天未失败的黑名单条目
// @Tags 系统管理
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "清理结果"
// @Router /admin/region-blacklist/clear-expired [post]
func (h *RegionBlacklistHandler) ClearExpiredEntries(c *gin.Context) {
	// 获取清理前的统计信息
	beforeStats := h.blacklistService.GetStatistics()

	// 执行清理
	h.blacklistService.ClearExpiredEntries()

	// 获取清理后的统计信息
	afterStats := h.blacklistService.GetStatistics()

	h.logger.Info("清理过期黑名单条目",
		zap.Int("before_total", beforeStats["total_entries"].(int)),
		zap.Int("after_total", afterStats["total_entries"].(int)))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "清理过期条目完成",
		"data": map[string]interface{}{
			"before_total": beforeStats["total_entries"],
			"after_total":  afterStats["total_entries"],
			"cleared":      beforeStats["total_entries"].(int) - afterStats["total_entries"].(int),
		},
	})
}

// TestRoute 测试路线是否在黑名单中
// @Summary 测试路线是否在黑名单中
// @Description 检查指定路线是否在黑名单中，用于测试
// @Tags 系统管理
// @Accept json
// @Produce json
// @Param provider query string true "供应商代码"
// @Param route query string true "路线，如'上海市->安徽省'"
// @Param express_code query string true "快递公司代码"
// @Success 200 {object} map[string]interface{} "测试结果"
// @Router /admin/region-blacklist/test [get]
func (h *RegionBlacklistHandler) TestRoute(c *gin.Context) {
	provider := c.Query("provider")
	route := c.Query("route")
	expressCode := c.Query("express_code")

	if provider == "" || route == "" || expressCode == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "缺少必要参数：provider, route, express_code",
		})
		return
	}

	isBlacklisted := h.blacklistService.IsBlacklisted(provider, route, expressCode)
	shouldSkip := h.blacklistService.ShouldSkipQuery(provider, route, expressCode)
	entry := h.blacklistService.GetBlacklistEntry(provider, route, expressCode)

	result := map[string]interface{}{
		"provider":       provider,
		"route":          route,
		"express_code":   expressCode,
		"is_blacklisted": isBlacklisted,
		"should_skip":    shouldSkip,
		"entry":          entry,
	}

	h.logger.Info("测试路线黑名单状态",
		zap.String("provider", provider),
		zap.String("route", route),
		zap.String("express_code", expressCode),
		zap.Bool("is_blacklisted", isBlacklisted),
		zap.Bool("should_skip", shouldSkip))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "测试完成",
		"data":    result,
	})
}
