package handler

import (
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"github.com/your-org/go-kuaidi/internal/service"
)

// MonitoringHandler 监控处理器
// 🔥 企业级监控：提供平台订单号相关的监控指标和健康检查接口
type MonitoringHandler struct {
	monitoringService *service.PlatformOrderMonitoringService
	logger            *zap.Logger
}

// NewMonitoringHandler 创建监控处理器
func NewMonitoringHandler(
	monitoringService *service.PlatformOrderMonitoringService,
	logger *zap.Logger,
) *MonitoringHandler {
	return &MonitoringHandler{
		monitoringService: monitoringService,
		logger:            logger,
	}
}

// GetMetrics 获取监控指标
// @Summary 获取监控指标
// @Description 获取平台订单号相关的监控指标
// @Tags 监控
// @Accept json
// @Produce json
// @Success 200 {object} service.MonitoringMetrics "监控指标"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/monitoring/metrics [get]
func (h *MonitoringHandler) GetMetrics(c *gin.Context) {
	h.logger.Info("接收监控指标查询请求")

	// 获取监控指标
	metrics := h.monitoringService.GetMetrics()

	// 返回响应
	h.respondSuccess(c, metrics)
}

// GetHealthCheck 健康检查
// @Summary 健康检查
// @Description 获取系统健康状态
// @Tags 监控
// @Accept json
// @Produce json
// @Success 200 {object} HealthCheckResponse "健康状态"
// @Failure 503 {object} ErrorResponse "服务不可用"
// @Router /api/monitoring/health [get]
func (h *MonitoringHandler) GetHealthCheck(c *gin.Context) {
	h.logger.Info("接收健康检查请求")

	// 获取监控指标
	metrics := h.monitoringService.GetMetrics()

	// 构建健康检查响应
	healthResponse := &MonitoringHealthCheckResponse{
		Status:           h.getHealthStatus(metrics.HealthScore),
		HealthScore:      metrics.HealthScore,
		ErrorRate:        metrics.ErrorRate,
		AvailabilityRate: metrics.AvailabilityRate,
		Timestamp:        metrics.LastUpdated.Format("2006-01-02 15:04:05"),
		Components: map[string]ComponentHealth{
			"platform_order_generator": {
				Status:  h.getComponentStatus(metrics.GenerationSuccess, metrics.GenerationFailure),
				Message: h.getGeneratorMessage(metrics),
			},
			"order_query": {
				Status:  h.getComponentStatus(metrics.QuerySuccess, metrics.QueryFailure),
				Message: h.getQueryMessage(metrics),
			},
			"data_migration": {
				Status:  h.getMigrationStatus(metrics.MigrationProgress),
				Message: h.getMigrationMessage(metrics),
			},
		},
	}

	// 根据健康分数返回相应的HTTP状态码
	if metrics.HealthScore < 50 {
		c.JSON(http.StatusServiceUnavailable, healthResponse)
	} else {
		c.JSON(http.StatusOK, healthResponse)
	}
}

// RecordMetric 记录指标
// @Summary 记录指标
// @Description 手动记录监控指标
// @Tags 监控
// @Accept json
// @Produce json
// @Param request body MetricRecordRequest true "指标记录请求"
// @Success 200 {object} SuccessResponse "记录成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/monitoring/metrics/record [post]
func (h *MonitoringHandler) RecordMetric(c *gin.Context) {
	h.logger.Info("接收指标记录请求")

	// 解析请求参数
	var req MetricRecordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("解析指标记录请求失败", zap.Error(err))
		h.respondError(c, http.StatusBadRequest, "请求参数格式错误: "+err.Error())
		return
	}

	// 验证请求参数
	if err := h.validateMetricRecordRequest(&req); err != nil {
		h.logger.Error("指标记录请求参数验证失败", zap.Error(err))
		h.respondError(c, http.StatusBadRequest, err.Error())
		return
	}

	// 根据指标类型记录
	switch req.MetricType {
	case "order_generation":
		h.monitoringService.RecordOrderGeneration(req.Success, req.Duration)
	case "order_query":
		h.monitoringService.RecordOrderQuery(req.QueryType, req.Success, req.Duration)
	case "migration_progress":
		h.monitoringService.RecordMigrationProgress(req.Total, req.SuccessCount, req.Failure)
	default:
		h.respondError(c, http.StatusBadRequest, "不支持的指标类型: "+req.MetricType)
		return
	}

	// 返回成功响应
	h.respondSuccess(c, map[string]string{
		"message": "指标记录成功",
		"type":    req.MetricType,
	})
}

// GetDashboard 获取监控仪表板
// @Summary 获取监控仪表板
// @Description 获取监控仪表板数据
// @Tags 监控
// @Accept json
// @Produce json
// @Success 200 {object} DashboardResponse "仪表板数据"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/monitoring/dashboard [get]
func (h *MonitoringHandler) GetDashboard(c *gin.Context) {
	h.logger.Info("接收监控仪表板请求")

	// 获取监控指标
	metrics := h.monitoringService.GetMetrics()

	// 构建仪表板响应
	dashboard := &DashboardResponse{
		Overview: &MonitoringDashboardOverview{
			HealthScore:      metrics.HealthScore,
			ErrorRate:        metrics.ErrorRate,
			AvailabilityRate: metrics.AvailabilityRate,
			TotalOrders:      metrics.GenerationTotal,
			TotalQueries:     metrics.QueryTotal,
		},
		OrderGeneration: &OrderGenerationMetrics{
			Total:       metrics.GenerationTotal,
			Success:     metrics.GenerationSuccess,
			Failure:     metrics.GenerationFailure,
			SuccessRate: h.calculateSuccessRate(metrics.GenerationSuccess, metrics.GenerationTotal),
			AvgDuration: metrics.GenerationDuration,
		},
		OrderQuery: &OrderQueryMetrics{
			Total:        metrics.QueryTotal,
			Success:      metrics.QuerySuccess,
			Failure:      metrics.QueryFailure,
			SuccessRate:  h.calculateSuccessRate(metrics.QuerySuccess, metrics.QueryTotal),
			AvgDuration:  metrics.QueryDuration,
			ByPlatformNo: metrics.QueryByPlatformNo,
			ByCustomerNo: metrics.QueryByCustomerNo,
			ByProviderNo: metrics.QueryByProviderNo,
			ByTrackingNo: metrics.QueryByTrackingNo,
		},
		DataMigration: &DataMigrationMetrics{
			Total:    metrics.MigrationTotal,
			Success:  metrics.MigrationSuccess,
			Failure:  metrics.MigrationFailure,
			Progress: metrics.MigrationProgress,
		},
		LastUpdated: metrics.LastUpdated.Format("2006-01-02 15:04:05"),
	}

	// 返回响应
	h.respondSuccess(c, dashboard)
}

// 辅助方法

// getHealthStatus 获取健康状态
func (h *MonitoringHandler) getHealthStatus(healthScore float64) string {
	if healthScore >= 90 {
		return "healthy"
	} else if healthScore >= 70 {
		return "warning"
	} else {
		return "critical"
	}
}

// getComponentStatus 获取组件状态
func (h *MonitoringHandler) getComponentStatus(success, failure int64) string {
	if failure == 0 {
		return "healthy"
	}

	total := success + failure
	if total == 0 {
		return "unknown"
	}

	errorRate := float64(failure) / float64(total) * 100
	if errorRate < 5 {
		return "healthy"
	} else if errorRate < 20 {
		return "warning"
	} else {
		return "critical"
	}
}

// getMigrationStatus 获取迁移状态
func (h *MonitoringHandler) getMigrationStatus(progress float64) string {
	if progress >= 100 {
		return "completed"
	} else if progress >= 50 {
		return "in_progress"
	} else if progress > 0 {
		return "started"
	} else {
		return "not_started"
	}
}

// getGeneratorMessage 获取生成器消息
func (h *MonitoringHandler) getGeneratorMessage(metrics *service.MonitoringMetrics) string {
	if metrics.GenerationTotal == 0 {
		return "暂无生成记录"
	}

	successRate := h.calculateSuccessRate(metrics.GenerationSuccess, metrics.GenerationTotal)
	return fmt.Sprintf("成功率: %.2f%%, 平均耗时: %dms", successRate, metrics.GenerationDuration)
}

// getQueryMessage 获取查询消息
func (h *MonitoringHandler) getQueryMessage(metrics *service.MonitoringMetrics) string {
	if metrics.QueryTotal == 0 {
		return "暂无查询记录"
	}

	successRate := h.calculateSuccessRate(metrics.QuerySuccess, metrics.QueryTotal)
	return fmt.Sprintf("成功率: %.2f%%, 平均耗时: %dms", successRate, metrics.QueryDuration)
}

// getMigrationMessage 获取迁移消息
func (h *MonitoringHandler) getMigrationMessage(metrics *service.MonitoringMetrics) string {
	if metrics.MigrationTotal == 0 {
		return "暂无迁移任务"
	}

	return fmt.Sprintf("进度: %.2f%%, 成功: %d, 失败: %d",
		metrics.MigrationProgress, metrics.MigrationSuccess, metrics.MigrationFailure)
}

// calculateSuccessRate 计算成功率
func (h *MonitoringHandler) calculateSuccessRate(success, total int64) float64 {
	if total == 0 {
		return 0
	}
	return float64(success) / float64(total) * 100
}

// validateMetricRecordRequest 验证指标记录请求
func (h *MonitoringHandler) validateMetricRecordRequest(req *MetricRecordRequest) error {
	if req.MetricType == "" {
		return fmt.Errorf("指标类型不能为空")
	}

	validTypes := map[string]bool{
		"order_generation":   true,
		"order_query":        true,
		"migration_progress": true,
	}

	if !validTypes[req.MetricType] {
		return fmt.Errorf("无效的指标类型: %s", req.MetricType)
	}

	if req.MetricType == "order_query" && req.QueryType == "" {
		return fmt.Errorf("查询类型不能为空")
	}

	if req.Duration < 0 {
		return fmt.Errorf("持续时间不能为负数")
	}

	return nil
}

// respondSuccess 返回成功响应
func (h *MonitoringHandler) respondSuccess(c *gin.Context, data interface{}) {
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "成功",
		"data":    data,
	})
}

// respondError 返回错误响应
func (h *MonitoringHandler) respondError(c *gin.Context, code int, message string) {
	c.JSON(code, gin.H{
		"success": false,
		"code":    code,
		"message": message,
	})
}

// 响应类型定义

// MonitoringHealthCheckResponse 监控健康检查响应
type MonitoringHealthCheckResponse struct {
	Status           string                     `json:"status"`            // healthy, warning, critical
	HealthScore      float64                    `json:"health_score"`      // 健康分数
	ErrorRate        float64                    `json:"error_rate"`        // 错误率
	AvailabilityRate float64                    `json:"availability_rate"` // 可用性
	Timestamp        string                     `json:"timestamp"`         // 时间戳
	Components       map[string]ComponentHealth `json:"components"`        // 组件健康状态
}

// ComponentHealth 组件健康状态
type ComponentHealth struct {
	Status  string `json:"status"`  // healthy, warning, critical, unknown
	Message string `json:"message"` // 状态描述
}

// MetricRecordRequest 指标记录请求
type MetricRecordRequest struct {
	MetricType   string        `json:"metric_type"`   // order_generation, order_query, migration_progress
	Success      bool          `json:"success"`       // 是否成功（仅order_generation, order_query需要）
	Duration     time.Duration `json:"duration"`      // 持续时间
	QueryType    string        `json:"query_type"`    // 查询类型（仅order_query需要）
	Total        int64         `json:"total"`         // 总数（仅migration_progress需要）
	SuccessCount int64         `json:"success_count"` // 成功数（仅migration_progress需要）
	Failure      int64         `json:"failure"`       // 失败数（仅migration_progress需要）
}

// DashboardResponse 仪表板响应
type DashboardResponse struct {
	Overview        *MonitoringDashboardOverview `json:"overview"`
	OrderGeneration *OrderGenerationMetrics      `json:"order_generation"`
	OrderQuery      *OrderQueryMetrics           `json:"order_query"`
	DataMigration   *DataMigrationMetrics        `json:"data_migration"`
	LastUpdated     string                       `json:"last_updated"`
}

// MonitoringDashboardOverview 监控仪表板概览
type MonitoringDashboardOverview struct {
	HealthScore      float64 `json:"health_score"`
	ErrorRate        float64 `json:"error_rate"`
	AvailabilityRate float64 `json:"availability_rate"`
	TotalOrders      int64   `json:"total_orders"`
	TotalQueries     int64   `json:"total_queries"`
}

// OrderGenerationMetrics 订单生成指标
type OrderGenerationMetrics struct {
	Total       int64   `json:"total"`
	Success     int64   `json:"success"`
	Failure     int64   `json:"failure"`
	SuccessRate float64 `json:"success_rate"`
	AvgDuration int64   `json:"avg_duration"`
}

// OrderQueryMetrics 订单查询指标
type OrderQueryMetrics struct {
	Total        int64   `json:"total"`
	Success      int64   `json:"success"`
	Failure      int64   `json:"failure"`
	SuccessRate  float64 `json:"success_rate"`
	AvgDuration  int64   `json:"avg_duration"`
	ByPlatformNo int64   `json:"by_platform_no"`
	ByCustomerNo int64   `json:"by_customer_no"`
	ByProviderNo int64   `json:"by_provider_no"`
	ByTrackingNo int64   `json:"by_tracking_no"`
}

// DataMigrationMetrics 数据迁移指标
type DataMigrationMetrics struct {
	Total    int64   `json:"total"`
	Success  int64   `json:"success"`
	Failure  int64   `json:"failure"`
	Progress float64 `json:"progress"`
}
