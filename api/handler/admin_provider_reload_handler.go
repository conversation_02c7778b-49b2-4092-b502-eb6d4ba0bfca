package handler

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"github.com/your-org/go-kuaidi/internal/adapter"
	"github.com/your-org/go-kuaidi/internal/model"
)

// AdminProviderReloadHandler 管理员供应商重载处理器
type AdminProviderReloadHandler struct {
	dynamicProviderManager *adapter.DynamicProviderManager
	logger                 *zap.Logger
}

// NewAdminProviderReloadHandler 创建管理员供应商重载处理器
func NewAdminProviderReloadHandler(
	dynamicProviderManager *adapter.DynamicProviderManager,
	logger *zap.Logger,
) *AdminProviderReloadHandler {
	return &AdminProviderReloadHandler{
		dynamicProviderManager: dynamicProviderManager,
		logger:                 logger,
	}
}

// ReloadProvider 重新加载指定供应商
// @Summary 重新加载指定供应商适配器
// @Description 根据数据库最新配置重新加载指定供应商适配器，实现配置热更新
// @Tags 管理员-供应商管理
// @Accept json
// @Produce json
// @Param provider_code path string true "供应商代码" Enums(kuaidi100,yida,yuntong,cainiao)
// @Success 200 {object} model.Response{data=ProviderReloadResponse} "重载成功"
// @Failure 400 {object} model.Response "请求参数错误"
// @Failure 500 {object} model.Response "服务器内部错误"
// @Router /admin/providers/{provider_code}/reload [post]
func (h *AdminProviderReloadHandler) ReloadProvider(c *gin.Context) {
	providerCode := c.Param("provider_code")
	if providerCode == "" {
		c.JSON(http.StatusBadRequest, model.APIResponse{
			Success: false,
			Code:    http.StatusBadRequest,
			Message: "供应商代码不能为空",
		})
		return
	}

	// 验证供应商代码
	validProviders := map[string]bool{
		"kuaidi100": true,
		"yida":      true,
		"yuntong":   true,
		"cainiao":   true,
	}
	if !validProviders[providerCode] {
		c.JSON(http.StatusBadRequest, model.APIResponse{
			Success: false,
			Code:    http.StatusBadRequest,
			Message: "不支持的供应商代码",
		})
		return
	}

	h.logger.Info("管理员触发供应商重载",
		zap.String("provider_code", providerCode),
		zap.String("admin_user", c.GetString("user_id")))

	// 执行重载
	ctx := c.Request.Context()
	if err := h.dynamicProviderManager.ReloadProvider(ctx, providerCode); err != nil {
		h.logger.Error("供应商重载失败",
			zap.String("provider_code", providerCode),
			zap.Error(err))

		c.JSON(http.StatusInternalServerError, model.APIResponse{
			Success: false,
			Code:    http.StatusInternalServerError,
			Message: "供应商重载失败: " + err.Error(),
		})
		return
	}

	// 获取重载后的状态
	status := h.dynamicProviderManager.GetProviderStatus(ctx)
	providerStatus := status[providerCode]

	response := ProviderReloadResponse{
		ProviderCode: providerCode,
		Success:      true,
		Message:      "供应商重载成功",
		Status:       providerStatus,
	}

	c.JSON(http.StatusOK, model.APIResponse{
		Success: true,
		Code:    http.StatusOK,
		Message: "供应商重载成功",
		Data:    response,
	})
}

// ReloadAllProviders 重新加载所有供应商
// @Summary 重新加载所有供应商适配器
// @Description 根据数据库最新配置重新加载所有供应商适配器，实现全量配置热更新
// @Tags 管理员-供应商管理
// @Accept json
// @Produce json
// @Success 200 {object} model.Response{data=AllProvidersReloadResponse} "重载成功"
// @Failure 500 {object} model.Response "服务器内部错误"
// @Router /admin/providers/reload-all [post]
func (h *AdminProviderReloadHandler) ReloadAllProviders(c *gin.Context) {
	h.logger.Info("管理员触发所有供应商重载",
		zap.String("admin_user", c.GetString("user_id")))

	ctx := c.Request.Context()

	// 执行全量重载
	if err := h.dynamicProviderManager.ReloadAllProviders(ctx); err != nil {
		h.logger.Error("所有供应商重载失败", zap.Error(err))

		c.JSON(http.StatusInternalServerError, model.APIResponse{
			Success: false,
			Code:    http.StatusInternalServerError,
			Message: "所有供应商重载失败: " + err.Error(),
		})
		return
	}

	// 获取重载后的状态
	allStatus := h.dynamicProviderManager.GetProviderStatus(ctx)

	response := AllProvidersReloadResponse{
		Success:   true,
		Message:   "所有供应商重载成功",
		Providers: allStatus,
	}

	c.JSON(http.StatusOK, model.APIResponse{
		Success: true,
		Code:    http.StatusOK,
		Message: "所有供应商重载成功",
		Data:    response,
	})
}

// GetProviderStatus 获取供应商状态
// @Summary 获取供应商运行状态
// @Description 获取所有供应商的当前运行状态，包括启用状态、适配器状态等信息
// @Tags 管理员-供应商管理
// @Accept json
// @Produce json
// @Success 200 {object} model.Response{data=map[string]adapter.ProviderStatus} "获取成功"
// @Failure 500 {object} model.Response "服务器内部错误"
// @Router /admin/providers/status [get]
func (h *AdminProviderReloadHandler) GetProviderStatus(c *gin.Context) {
	ctx := c.Request.Context()
	status := h.dynamicProviderManager.GetProviderStatus(ctx)

	c.JSON(http.StatusOK, model.APIResponse{
		Success: true,
		Code:    http.StatusOK,
		Message: "获取供应商状态成功",
		Data:    status,
	})
}

// GetProviderMetrics 获取供应商指标
// @Summary 获取供应商重载指标
// @Description 获取供应商重载的详细指标信息，包括重载次数、成功率、平均时间等
// @Tags 管理员-供应商管理
// @Accept json
// @Produce json
// @Success 200 {object} model.APIResponse{data=adapter.ProviderMetrics} "获取成功"
// @Failure 500 {object} model.APIResponse "服务器内部错误"
// @Router /admin/providers/metrics [get]
func (h *AdminProviderReloadHandler) GetProviderMetrics(c *gin.Context) {
	metrics := h.dynamicProviderManager.GetMetrics()

	c.JSON(http.StatusOK, model.APIResponse{
		Success: true,
		Code:    http.StatusOK,
		Message: "获取供应商指标成功",
		Data:    metrics,
	})
}

// ProviderReloadResponse 供应商重载响应
type ProviderReloadResponse struct {
	ProviderCode string                 `json:"provider_code"`
	Success      bool                   `json:"success"`
	Message      string                 `json:"message"`
	Status       adapter.ProviderStatus `json:"status"`
}

// AllProvidersReloadResponse 所有供应商重载响应
type AllProvidersReloadResponse struct {
	Success   bool                              `json:"success"`
	Message   string                            `json:"message"`
	Providers map[string]adapter.ProviderStatus `json:"providers"`
}

// RegisterRoutes 注册路由
func (h *AdminProviderReloadHandler) RegisterRoutes(router *gin.RouterGroup) {
	providerGroup := router.Group("/providers")
	{
		providerGroup.POST("/:provider_code/reload", h.ReloadProvider)
		providerGroup.POST("/reload-all", h.ReloadAllProviders)
		providerGroup.GET("/status", h.GetProviderStatus)
	}
}
