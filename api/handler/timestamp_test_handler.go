package handler

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"github.com/your-org/go-kuaidi/internal/util"
)

// TimestampTestHandler timestamp测试处理器
type TimestampTestHandler struct {
	logger *zap.Logger
}

// NewTimestampTestHandler 创建timestamp测试处理器
func NewTimestampTestHandler(logger *zap.Logger) *TimestampTestHandler {
	return &TimestampTestHandler{
		logger: logger,
	}
}

// TimestampValidationRequest timestamp验证请求
type TimestampValidationRequest struct {
	Timestamp string `json:"timestamp" binding:"required"`
}

// TimestampValidationResponse timestamp验证响应
type TimestampValidationResponse struct {
	Success    bool              `json:"success"`
	Valid      bool              `json:"valid"`
	Type       string            `json:"type"`        // "second" 或 "millisecond"
	ServerTime int64             `json:"server_time"` // 服务器当前时间（毫秒）
	ClientTime int64             `json:"client_time"` // 客户端时间（毫秒）
	TimeDiff   int64             `json:"time_diff"`   // 时间差（毫秒）
	IsExpired  bool              `json:"is_expired"`
	IsFuture   bool              `json:"is_future"`
	Message    string            `json:"message"`
	Suggestion string            `json:"suggestion"`
	Examples   map[string]string `json:"examples"`
}

// ValidateTimestamp 验证timestamp格式和有效性
func (h *TimestampTestHandler) ValidateTimestamp(c *gin.Context) {
	var req TimestampValidationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 获取北京时间
	now := util.NowBeijing()
	serverTimeMs := now.UnixMilli()

	response := TimestampValidationResponse{
		Success:    true,
		ServerTime: serverTimeMs,
		Examples: map[string]string{
			"current_millisecond": strconv.FormatInt(serverTimeMs, 10),
			"current_second":      strconv.FormatInt(now.Unix(), 10),
			"javascript_code":     "Date.now().toString()",
			"go_code":             "util.NowBeijing().UnixMilli()",
		},
	}

	// 检查timestamp格式
	if req.Timestamp == "" {
		response.Valid = false
		response.Message = "timestamp不能为空"
		response.Suggestion = "请提供有效的数字时间戳"
		c.JSON(http.StatusOK, response)
		return
	}

	// 检查是否为数字
	timestampInt, err := strconv.ParseInt(req.Timestamp, 10, 64)
	if err != nil {
		response.Valid = false
		response.Message = "timestamp必须是数字格式"
		response.Suggestion = "请使用纯数字时间戳，如：" + response.Examples["current_millisecond"]
		c.JSON(http.StatusOK, response)
		return
	}

	// 判断时间戳类型和解析
	var isMillisec bool

	if timestampInt > 1e12 {
		// 毫秒级时间戳
		response.Type = "millisecond"
		response.ClientTime = timestampInt
		isMillisec = true
	} else {
		// 秒级时间戳
		response.Type = "second"
		response.ClientTime = timestampInt * 1000 // 转换为毫秒便于比较
		isMillisec = false
	}

	// 计算时间差
	response.TimeDiff = serverTimeMs - response.ClientTime

	// 检查有效性
	validityDuration := 10 * time.Minute
	timeDiffDuration := time.Duration(response.TimeDiff) * time.Millisecond

	// 检查是否过期
	if timeDiffDuration > validityDuration {
		response.IsExpired = true
		response.Valid = false
		response.Message = "timestamp已过期"
		response.Suggestion = "请使用当前时间生成timestamp，有效期为10分钟"
	} else if timeDiffDuration < -30*time.Second {
		// 检查是否来自未来（允许30秒偏差）
		response.IsFuture = true
		response.Valid = false
		response.Message = "timestamp来自未来"
		response.Suggestion = "请检查系统时间是否与服务器时间同步（北京时间）"
	} else {
		response.Valid = true
		if isMillisec {
			response.Message = "timestamp格式正确（毫秒级）"
			response.Suggestion = "推荐继续使用毫秒级时间戳"
		} else {
			response.Message = "timestamp格式正确（秒级）"
			response.Suggestion = "建议升级到毫秒级时间戳以避免重复问题"
		}
	}

	h.logger.Info("timestamp验证请求",
		zap.String("timestamp", req.Timestamp),
		zap.String("type", response.Type),
		zap.Bool("valid", response.Valid),
		zap.Int64("time_diff_ms", response.TimeDiff))

	c.JSON(http.StatusOK, response)
}

// GetServerTime 获取服务器当前时间
func (h *TimestampTestHandler) GetServerTime(c *gin.Context) {
	now := util.NowBeijing()

	response := gin.H{
		"success": true,
		"server_time": map[string]interface{}{
			"millisecond": now.UnixMilli(),
			"second":      now.Unix(),
			"formatted":   util.FormatBeijingTimeDefault(now),
			"timezone":    util.GetBeijingTimeZoneInfo(),
			"iso8601":     now.Format(time.RFC3339),
		},
		"examples": map[string]string{
			"javascript":     "Date.now().toString()",
			"go_millisecond": "util.NowBeijing().UnixMilli()",
			"go_second":      "util.NowBeijing().Unix()",
			"python":         "int(time.time() * 1000)",
		},
		"recommendations": []string{
			"使用毫秒级时间戳避免重复",
			"确保客户端时间与服务器时间同步",
			"timestamp有效期为10分钟",
			"允许30秒的时钟偏差",
		},
	}

	c.JSON(http.StatusOK, response)
}

// GenerateTimestamp 生成推荐的timestamp
func (h *TimestampTestHandler) GenerateTimestamp(c *gin.Context) {
	now := util.NowBeijing()

	response := gin.H{
		"success":               true,
		"recommended_timestamp": now.UnixMilli(),
		"alternative_timestamp": now.Unix(),
		"generation_time":       util.FormatBeijingTimeDefault(now),
		"code_examples": map[string]string{
			"javascript": `
// 推荐方式（毫秒级）
const timestamp = Date.now().toString();

// 高并发场景
let lastTimestamp = 0;
function generateUniqueTimestamp() {
    let timestamp = Date.now();
    if (timestamp <= lastTimestamp) {
        timestamp = lastTimestamp + 1;
    }
    lastTimestamp = timestamp;
    return timestamp.toString();
}`,
			"go": `
// 推荐方式（毫秒级）
timestamp := util.NowBeijing().UnixMilli()

// 字符串格式
timestampStr := strconv.FormatInt(util.NowBeijing().UnixMilli(), 10)`,
			"python": `
# 推荐方式（毫秒级）
import time
timestamp = str(int(time.time() * 1000))

# 或使用datetime
from datetime import datetime
timestamp = str(int(datetime.now().timestamp() * 1000))`,
		},
		"validation_url": "/api/test/timestamp/validate",
		"tips": []string{
			"每次API调用都应生成新的timestamp",
			"不要重复使用相同的timestamp",
			"确保timestamp在10分钟有效期内",
			"毫秒级时间戳可以避免大部分重复问题",
		},
	}

	c.JSON(http.StatusOK, response)
}
