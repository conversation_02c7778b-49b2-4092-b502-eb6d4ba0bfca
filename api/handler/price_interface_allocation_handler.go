package handler

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"github.com/your-org/go-kuaidi/internal/errors"
	"github.com/your-org/go-kuaidi/internal/express"
)

// PriceInterfaceAllocationHandler 查价接口分配管理处理器
type PriceInterfaceAllocationHandler struct {
	expressService express.ExpressCompanyService
	logger         *zap.Logger
}

// NewPriceInterfaceAllocationHandler 创建查价接口分配管理处理器
func NewPriceInterfaceAllocationHandler(expressService express.ExpressCompanyService, logger *zap.Logger) *PriceInterfaceAllocationHandler {
	return &PriceInterfaceAllocationHandler{
		expressService: expressService,
		logger:         logger,
	}
}

// SetPriceInterfaceRequest 设置查价接口请求
type SetPriceInterfaceRequest struct {
	ProviderCode  string `json:"provider_code" binding:"required" example:"yuntong"`
	CompanyCode   string `json:"company_code" binding:"required" example:"STO"`
	InterfaceType string `json:"interface_type" binding:"required" example:"QUERY_REALTIME_PRICE"`
}

// GetPriceInterfaceResponse 获取查价接口响应
type GetPriceInterfaceResponse struct {
	CompanyCode   string `json:"company_code" example:"JD"`
	InterfaceType string `json:"interface_type" example:"QUERY_REALTIME_PRICE"`
}

// GetAllAllocationsResponse 获取所有分配信息响应
type GetAllAllocationsResponse struct {
	Allocations []express.PriceInterfaceAllocation `json:"allocations"`
	Total       int                                `json:"total"`
}

// GetAllocationStatsResponse 获取分配统计响应
type GetAllocationStatsResponse struct {
	Stats *express.AllocationStats `json:"stats"`
}

// SetPriceInterface 设置快递公司的查价接口类型
// @Summary 设置快递公司查价接口类型
// @Description 管理员设置指定快递公司使用的查价接口类型
// @Tags 查价接口分配管理
// @Accept json
// @Produce json
// @Param request body SetPriceInterfaceRequest true "设置接口类型请求"
// @Success 200 {object} gin.H "设置成功"
// @Failure 400 {object} errors.ErrorResponse "请求参数错误"
// @Failure 401 {object} errors.ErrorResponse "未授权"
// @Failure 403 {object} errors.ErrorResponse "权限不足"
// @Failure 500 {object} errors.ErrorResponse "服务器内部错误"
// @Router /api/v1/admin/express/price-interface [post]
// @Security BearerAuth
func (h *PriceInterfaceAllocationHandler) SetPriceInterface(c *gin.Context) {
	requestID := getRequestID(c)

	// 获取操作者ID
	operatorID, exists := c.Get("admin_user_id")
	if !exists {
		h.logger.Warn("Admin user ID not found in context", zap.String("request_id", requestID))
		err := errors.NewBusinessError(errors.ErrCodeUnauthorized, "用户未认证")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	operatorIDStr, ok := operatorID.(string)
	if !ok {
		h.logger.Warn("Invalid admin user ID type", zap.String("request_id", requestID))
		err := errors.NewBusinessError(errors.ErrCodeUnauthorized, "用户ID格式错误")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	// 解析请求参数
	var req SetPriceInterfaceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("Invalid request parameters",
			zap.String("request_id", requestID),
			zap.Error(err))
		businessErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "请求参数格式错误")
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	// 验证接口类型
	interfaceType := express.PriceInterfaceType(req.InterfaceType)
	if !interfaceType.IsValid() {
		h.logger.Warn("Invalid interface type",
			zap.String("request_id", requestID),
			zap.String("interface_type", req.InterfaceType))
		businessErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest,
			"无效的接口类型，支持的类型：QUERY_PRICE, QUERY_REALTIME_PRICE")
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	// 调用服务层设置接口类型
	err := h.expressService.SetPriceInterface(c.Request.Context(), req.ProviderCode, req.CompanyCode, interfaceType, operatorIDStr)
	if err != nil {
		h.logger.Error("Failed to set price interface",
			zap.String("request_id", requestID),
			zap.String("provider_code", req.ProviderCode),
			zap.String("company_code", req.CompanyCode),
			zap.String("interface_type", req.InterfaceType),
			zap.String("operator_id", operatorIDStr),
			zap.Error(err))

		businessErr := errors.NewBusinessError(errors.ErrCodeInternal, err.Error())
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	h.logger.Info("Price interface set successfully",
		zap.String("request_id", requestID),
		zap.String("provider_code", req.ProviderCode),
		zap.String("company_code", req.CompanyCode),
		zap.String("interface_type", req.InterfaceType),
		zap.String("operator_id", operatorIDStr))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "查价接口类型设置成功",
		"data": gin.H{
			"provider_code":  req.ProviderCode,
			"company_code":   req.CompanyCode,
			"interface_type": req.InterfaceType,
		},
	})
}

// GetPriceInterface 获取供应商+快递公司的查价接口类型
// @Summary 获取供应商快递公司查价接口类型
// @Description 获取指定供应商+快递公司当前使用的查价接口类型
// @Tags 查价接口分配管理
// @Accept json
// @Produce json
// @Param provider_code path string true "供应商代码" example("yuntong")
// @Param company_code path string true "快递公司代码" example("STO")
// @Success 200 {object} GetPriceInterfaceResponse "获取成功"
// @Failure 400 {object} errors.ErrorResponse "请求参数错误"
// @Failure 401 {object} errors.ErrorResponse "未授权"
// @Failure 404 {object} errors.ErrorResponse "供应商或快递公司不存在"
// @Failure 500 {object} errors.ErrorResponse "服务器内部错误"
// @Router /api/v1/admin/express/price-interface/{provider_code}/{company_code} [get]
// @Security BearerAuth
func (h *PriceInterfaceAllocationHandler) GetPriceInterface(c *gin.Context) {
	requestID := getRequestID(c)
	providerCode := c.Param("provider_code")
	companyCode := c.Param("company_code")

	if providerCode == "" {
		h.logger.Warn("Provider code is required", zap.String("request_id", requestID))
		businessErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "供应商代码不能为空")
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	if companyCode == "" {
		h.logger.Warn("Company code is required", zap.String("request_id", requestID))
		businessErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "快递公司代码不能为空")
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	// 调用服务层获取接口类型
	interfaceType, err := h.expressService.GetPriceInterface(c.Request.Context(), providerCode, companyCode)
	if err != nil {
		h.logger.Error("Failed to get price interface",
			zap.String("request_id", requestID),
			zap.String("provider_code", providerCode),
			zap.String("company_code", companyCode),
			zap.Error(err))

		businessErr := errors.NewBusinessError(errors.ErrCodeInternal, err.Error())
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	h.logger.Info("Price interface retrieved successfully",
		zap.String("request_id", requestID),
		zap.String("provider_code", providerCode),
		zap.String("company_code", companyCode),
		zap.String("interface_type", string(interfaceType)))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "获取成功",
		"data": gin.H{
			"provider_code":  providerCode,
			"company_code":   companyCode,
			"interface_type": string(interfaceType),
		},
	})
}

// GetAllAllocations 获取所有快递公司的接口分配信息
// @Summary 获取所有接口分配信息
// @Description 获取所有快递公司的查价接口分配详情
// @Tags 查价接口分配管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Success 200 {object} GetAllAllocationsResponse "获取成功"
// @Failure 401 {object} errors.ErrorResponse "未授权"
// @Failure 500 {object} errors.ErrorResponse "服务器内部错误"
// @Router /api/v1/admin/express/price-interface/allocations [get]
// @Security BearerAuth
func (h *PriceInterfaceAllocationHandler) GetAllAllocations(c *gin.Context) {
	requestID := getRequestID(c)

	// 解析分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	// 调用服务层获取所有分配信息
	allocations, err := h.expressService.GetAllAllocations(c.Request.Context())
	if err != nil {
		h.logger.Error("Failed to get all allocations",
			zap.String("request_id", requestID),
			zap.Error(err))

		businessErr := errors.NewBusinessError(errors.ErrCodeInternal, err.Error())
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	// 简单分页处理
	total := len(allocations)
	start := (page - 1) * pageSize
	end := start + pageSize

	if start >= total {
		allocations = []express.PriceInterfaceAllocation{}
	} else {
		if end > total {
			end = total
		}
		allocations = allocations[start:end]
	}

	h.logger.Info("All allocations retrieved successfully",
		zap.String("request_id", requestID),
		zap.Int("total", total),
		zap.Int("page", page),
		zap.Int("page_size", pageSize))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "获取成功",
		"data": GetAllAllocationsResponse{
			Allocations: allocations,
			Total:       total,
		},
		"pagination": gin.H{
			"page":      page,
			"page_size": pageSize,
			"total":     total,
		},
	})
}

// GetAllocationStats 获取接口分配统计信息
// @Summary 获取接口分配统计
// @Description 获取查价接口分配的统计信息，包括各接口类型的分布情况
// @Tags 查价接口分配管理
// @Accept json
// @Produce json
// @Success 200 {object} GetAllocationStatsResponse "获取成功"
// @Failure 401 {object} errors.ErrorResponse "未授权"
// @Failure 500 {object} errors.ErrorResponse "服务器内部错误"
// @Router /api/v1/admin/express/price-interface/stats [get]
// @Security BearerAuth
func (h *PriceInterfaceAllocationHandler) GetAllocationStats(c *gin.Context) {
	requestID := getRequestID(c)

	// 调用服务层获取统计信息
	stats, err := h.expressService.GetAllocationStats(c.Request.Context())
	if err != nil {
		h.logger.Error("Failed to get allocation stats",
			zap.String("request_id", requestID),
			zap.Error(err))

		businessErr := errors.NewBusinessError(errors.ErrCodeInternal, err.Error())
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	h.logger.Info("Allocation stats retrieved successfully",
		zap.String("request_id", requestID),
		zap.Int("total_allocations", stats.TotalAllocations),
		zap.Int("standard_interface", stats.StandardInterface),
		zap.Int("realtime_interface", stats.RealtimeInterface))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "获取成功",
		"data": GetAllocationStatsResponse{
			Stats: stats,
		},
	})
}
