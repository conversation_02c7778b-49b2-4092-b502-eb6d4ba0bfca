package router

import (
	"github.com/gin-gonic/gin"
	"github.com/your-org/go-kuaidi/api/handler"
	"github.com/your-org/go-kuaidi/api/middleware"
)

// AdminRegionBlacklistRouterConfig 管理员地区黑名单路由配置
type AdminRegionBlacklistRouterConfig struct {
	RegionBlacklistHandler *handler.RegionBlacklistHandler
	AuthMiddleware         *middleware.AuthMiddleware
	AdminMiddleware        *middleware.AdminMiddleware
}

// SetupAdminRegionBlacklistRouter 设置管理员地区黑名单路由
func SetupAdminRegionBlacklistRouter(engine *gin.Engine, config AdminRegionBlacklistRouterConfig) {
	if config.RegionBlacklistHandler == nil || config.AuthMiddleware == nil || config.AdminMiddleware == nil {
		return
	}

	// 管理员API组
	adminGroup := engine.Group("/api/v1/admin")

	// 应用认证中间件
	adminGroup.Use(config.AuthMiddleware.RequireAuth())

	// 应用管理员权限中间件
	adminGroup.Use(config.AdminMiddleware.RequireAdmin())

	// 地区黑名单路由组
	blacklistGroup := adminGroup.Group("/region-blacklist")
	{
		// 获取黑名单统计信息
		blacklistGroup.GET("/statistics", config.RegionBlacklistHandler.GetBlacklistStatistics)

		// 获取所有黑名单条目
		blacklistGroup.GET("/entries", config.RegionBlacklistHandler.GetAllBlacklistEntries)

		// 获取特定黑名单条目
		blacklistGroup.GET("/entry", config.RegionBlacklistHandler.GetBlacklistEntry)

		// 从黑名单中移除条目
		blacklistGroup.POST("/remove", config.RegionBlacklistHandler.RemoveFromBlacklist)

		// 清理过期的黑名单条目
		blacklistGroup.POST("/clear-expired", config.RegionBlacklistHandler.ClearExpiredEntries)

		// 测试路线是否在黑名单中
		blacklistGroup.GET("/test", config.RegionBlacklistHandler.TestRoute)
	}
}
