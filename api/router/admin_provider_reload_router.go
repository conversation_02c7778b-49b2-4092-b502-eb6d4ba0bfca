package router

import (
	"github.com/gin-gonic/gin"
	"github.com/your-org/go-kuaidi/api/handler"
	"github.com/your-org/go-kuaidi/api/middleware"
)

// AdminProviderReloadRouterConfig 管理员供应商重载路由配置
type AdminProviderReloadRouterConfig struct {
	AdminProviderReloadHandler *handler.AdminProviderReloadHandler
	AuthMiddleware             *middleware.AuthMiddleware
	AdminMiddleware            *middleware.AdminMiddleware
}

// SetupAdminProviderReloadRouter 设置管理员供应商重载路由
func SetupAdminProviderReloadRouter(engine *gin.Engine, config AdminProviderReloadRouterConfig) {
	// 管理员供应商重载API组
	adminGroup := engine.Group("/api/v1/admin")

	// 应用认证中间件
	adminGroup.Use(config.AuthMiddleware.RequireAuth())

	// 应用管理员权限中间件
	adminGroup.Use(config.AdminMiddleware.RequireAdmin())

	// 供应商管理路由组 - 只需要管理员权限
	providerGroup := adminGroup.Group("/providers")
	{
		// 重新加载指定供应商
		providerGroup.POST("/:provider_code/reload", config.AdminProviderReloadHandler.ReloadProvider)

		// 重新加载所有供应商
		providerGroup.POST("/reload-all", config.AdminProviderReloadHandler.ReloadAllProviders)

		// 获取供应商状态
		providerGroup.GET("/status", config.AdminProviderReloadHandler.GetProviderStatus)

		// 获取供应商指标
		providerGroup.GET("/metrics", config.AdminProviderReloadHandler.GetProviderMetrics)
	}
}
