# 实时查价接口API对接文档

## 📋 **接口概述**

实时查价接口是高性能、实时价格查询接口，支持多供应商并行查询，包含预约时间信息，确保查价结果与下单时完全一致。

### 🎯 **核心特性**
- ✅ **实时查价**：直接调用供应商API，强制禁用缓存
- ✅ **多供应商支持**：菜鸟、快递鸟、云通、快递100、易达
- ✅ **预约时间信息**：支持菜鸟和快递鸟的预约时间查询
- ✅ **高性能**：内置熔断器、重试机制、超时控制
- ✅ **并发安全**：支持高并发访问，线程安全
- ✅ **生产级**：完整的错误处理和日志记录

## 🔗 **接口信息**

### 基本信息
- **接口路径**：`/api/gateway/execute`
- **请求方法**：`POST`
- **Content-Type**：`application/json`
- **认证方式**：JWT Token认证

### 请求格式
```json
{
  "apiMethod": "QUERY_REALTIME_PRICE",
  "clientType": "web",
  "accessToken": "your_jwt_token",
  "businessParams": {
    // 实时查价参数（见下方详细说明）
  }
}
```

## 📝 **请求参数**

### businessParams 参数详情

| 字段 | 类型 | 必填 | 说明 | 限制 |
|------|------|------|------|------|
| **sender** | object | ✅ | 寄件人信息 | - |
| sender.name | string | ✅ | 寄件人姓名 | 最多50个字符 |
| sender.mobile | string | ✅ | 寄件人手机号 | 11位数字 |
| sender.province | string | ✅ | 寄件人省份 | 最多20个字符 |
| sender.city | string | ✅ | 寄件人城市 | 最多20个字符 |
| sender.district | string | ✅ | 寄件人区县 | 最多20个字符 |
| sender.address | string | ✅ | 寄件人详细地址 | 最多200个字符 |
| **receiver** | object | ✅ | 收件人信息 | - |
| receiver.name | string | ✅ | 收件人姓名 | 最多50个字符 |
| receiver.mobile | string | ✅ | 收件人手机号 | 11位数字 |
| receiver.province | string | ✅ | 收件人省份 | 最多20个字符 |
| receiver.city | string | ✅ | 收件人城市 | 最多20个字符 |
| receiver.district | string | ✅ | 收件人区县 | 最多20个字符 |
| receiver.address | string | ✅ | 收件人详细地址 | 最多200个字符 |
| **weight** | number | ✅ | 包裹重量(kg) | 0.1-100 |
| length | number | ❌ | 包裹长度(cm) | 0-200 |
| width | number | ❌ | 包裹宽度(cm) | 0-200 |
| height | number | ❌ | 包裹高度(cm) | 0-200 |
| volume | number | ❌ | 包裹体积(m³) | 0-1.0 |
| quantity | number | ❌ | 包裹数量 | 默认1，0-100 |
| goods_name | string | ❌ | 物品名称 | 默认"物品"，最多100个字符 |
| pay_method | number | ❌ | 支付方式 | 0-寄付，1-到付，2-月结，默认0 |

### 请求示例
```json
{
  "apiMethod": "QUERY_REALTIME_PRICE",
  "clientType": "web",
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "businessParams": {
    "sender": {
      "name": "马娟",
      "mobile": "***********",
      "province": "广东省",
      "city": "广州市",
      "district": "荔湾区",
      "address": "建设路667号商务中心13单元103室"
    },
    "receiver": {
      "name": "郭娜",
      "mobile": "***********",
      "province": "广东省",
      "city": "深圳市",
      "district": "南山区",
      "address": "南京路261号中心4单元109室"
    },
    "weight": 2.1,
    "length": 50,
    "width": 40,
    "height": 30,
    "volume": 0.06,
    "quantity": 1,
    "goods_name": "文件",
    "pay_method": 0
  }
}
```

## 📊 **响应格式**

### 成功响应
```json
{
  "success": true,
  "code": 200,
  "msg": "操作成功",
  "data": {
    "success": true,
    "code": 200,
    "message": "查询成功",
    "data": [
      {
        "express_code": "STO",
        "express_name": "申通快递",
        "product_code": "KDN_STO_SAME_DAY",
        "product_name": "申通快递当日达快递",
        "price": 12.96,
        "continued_weight_per_kg": 1.25,
        "calc_weight": 8,
        "order_code": "ENHANCED_ORDER_CODE_...",
        "expires_at": "永不过期",
        "pickup_time_info": {
          "pickup_required": true,
          "supports_pickup_code": false,
          "min_advance_hours": 2,
          "time_format": "YYYY-MM-DD HH:mm:ss",
          "available_slots": [
            {
              "slot_id": "today_09_18",
              "slot_name": "今天 09:00-18:00",
              "start_time": "2025-07-25 09:00:00",
              "end_time": "2025-07-25 18:00:00"
            },
            {
              "slot_id": "tomorrow_09_18",
              "slot_name": "明天 09:00-18:00",
              "start_time": "2025-07-26 09:00:00",
              "end_time": "2025-07-26 18:00:00"
            }
          ]
        }
      }
    ]
  }
}
```

### 错误响应
```json
{
  "success": false,
  "code": 400,
  "msg": "参数验证失败: 寄件人手机号格式不正确",
  "data": null
}
```

## 📋 **响应字段说明**

### 外层响应字段
| 字段 | 类型 | 说明 |
|------|------|------|
| success | boolean | 请求是否成功 |
| code | number | 状态码 |
| msg | string | 响应消息 |
| data | object | 查价数据 |

### 查价数据字段 (data)
| 字段 | 类型 | 说明 |
|------|------|------|
| success | boolean | 查价是否成功 |
| code | number | 查价状态码 |
| message | string | 查价消息 |
| data | array | 价格列表 |

### 价格项目字段 (data.data[])
| 字段 | 类型 | 说明 |
|------|------|------|
| express_code | string | 快递公司代码 |
| express_name | string | 快递公司名称 |
| product_code | string | 产品代码 |
| product_name | string | 产品名称 |
| price | number | 总价格(元) |
| continued_weight_per_kg | number | 每公斤续重价格(元) |
| calc_weight | number | 计费重量(kg) |
| order_code | string | 下单代码（用于后续下单） |
| expires_at | string | 价格有效期 |
| pickup_time_info | object | 预约时间信息（可选） |

### 预约时间信息字段 (pickup_time_info)
| 字段 | 类型 | 说明 |
|------|------|------|
| pickup_required | boolean | 是否需要预约取件 |
| supports_pickup_code | boolean | 是否支持取件码 |
| min_advance_hours | number | 最少提前预约小时数 |
| time_format | string | 时间格式 |
| available_slots | array | 可用时间段列表 |

### 时间段字段 (available_slots[])
| 字段 | 类型 | 说明 |
|------|------|------|
| slot_id | string | 时间段ID |
| slot_name | string | 时间段名称 |
| start_time | string | 开始时间 |
| end_time | string | 结束时间 |
| available | boolean | 是否可用（可选） |
| description | string | 描述信息（可选） |

## 🔢 **状态码说明**

| 状态码 | 说明 | 处理建议 |
|--------|------|----------|
| 200 | 查询成功 | 正常处理价格数据 |
| 400 | 参数错误 | 检查请求参数格式和内容 |
| 401 | 认证失败 | 检查JWT Token是否有效 |
| 500 | 服务器内部错误 | 稍后重试或联系技术支持 |
| 503 | 服务暂时不可用 | 熔断器开启，稍后重试 |

## 🚀 **使用示例**

### cURL示例
```bash
curl -X POST "https://api.example.com/api/gateway/execute" \
  -H "Content-Type: application/json" \
  -d '{
    "apiMethod": "QUERY_REALTIME_PRICE",
    "clientType": "web",
    "accessToken": "your_jwt_token",
    "businessParams": {
      "sender": {
        "name": "张三",
        "mobile": "***********",
        "province": "北京市",
        "city": "北京市",
        "district": "朝阳区",
        "address": "三里屯街道1号"
      },
      "receiver": {
        "name": "李四",
        "mobile": "***********",
        "province": "上海市",
        "city": "上海市",
        "district": "浦东新区",
        "address": "陆家嘴街道2号"
      },
      "weight": 2.0,
      "length": 30.0,
      "width": 20.0,
      "height": 10.0,
      "goods_name": "测试物品",
      "quantity": 1,
      "pay_method": 0
    }
  }'
```

### JavaScript示例
```javascript
const response = await fetch('/api/gateway/execute', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    apiMethod: 'QUERY_REALTIME_PRICE',
    clientType: 'web',
    accessToken: 'your_jwt_token',
    businessParams: {
      sender: {
        name: '张三',
        mobile: '***********',
        province: '北京市',
        city: '北京市',
        district: '朝阳区',
        address: '三里屯街道1号'
      },
      receiver: {
        name: '李四',
        mobile: '***********',
        province: '上海市',
        city: '上海市',
        district: '浦东新区',
        address: '陆家嘴街道2号'
      },
      weight: 2.0,
      length: 30.0,
      width: 20.0,
      height: 10.0,
      goods_name: '测试物品',
      quantity: 1,
      pay_method: 0
    }
  })
});

const result = await response.json();
if (result.success && result.data.success) {
  console.log('查价成功，价格列表:', result.data.data);
  
  // 处理预约时间信息
  result.data.data.forEach(item => {
    if (item.pickup_time_info) {
      console.log(`${item.express_name} 支持预约时间:`, item.pickup_time_info);
    }
  });
} else {
  console.error('查价失败:', result.msg || result.data?.message);
}
```

## ⚡ **性能特性**

### 超时控制
- **查询超时**：5秒
- **重试延迟**：500毫秒
- **最大重试**：2次

### 熔断器机制
- **失败阈值**：5次连续失败
- **熔断时间**：30秒
- **半开状态**：允许一次尝试请求

### 并发安全
- 支持高并发访问
- 线程安全的熔断器状态管理
- 无状态设计，支持水平扩展

## 🎯 **支持的供应商**

### 当前支持的供应商
| 供应商 | 代码 | 支持快递公司 | 预约时间 | 备注 |
|--------|------|-------------|----------|------|
| **菜鸟裹裹** | cainiao | 菜鸟、韵达、申通 | ✅ | 支持取件码 |
| **快递鸟** | kuaidiniao | 申通、韵达、圆通、中通、极兔 | ✅ | 当日达快递 |
| **云通** | yuntong | 京东快递 | ❌ | 标准快递 |
| **快递100** | kuaidi100 | 德邦快递 | ❌ | 大件快递 |
| **易达** | yida | 多家快递公司 | ❌ | 标准快递 |

### 预约时间支持情况
- **菜鸟裹裹**：
  - 时间格式：`2006-01-02 15:04:05`
  - 支持取件码：是
  - 最少提前：1小时
  - 时间段：明天、后天

- **快递鸟**：
  - 时间格式：`YYYY-MM-DD HH:mm:ss`
  - 支持取件码：否
  - 最少提前：2小时
  - 时间段：今天、明天

## 🔍 **错误处理**

### 常见错误及解决方案

| 错误信息 | 原因 | 解决方案 |
|----------|------|----------|
| "寄件人手机号格式不正确" | 手机号格式不符合要求 | 检查手机号格式，支持11位手机号 |
| "详细地址过于简单" | 地址信息不够详细 | 提供至少2个字符的详细地址 |
| "包裹重量必须大于0" | 重量参数无效 | 设置有效的重量值(0.1-100kg) |
| "服务暂时不可用" | 熔断器开启 | 等待30秒后重试 |
| "所有供应商查询失败" | 供应商服务异常 | 稍后重试或联系技术支持 |
| "JWT Token无效" | 认证失败 | 重新获取有效的JWT Token |

### 错误响应示例
```json
{
  "success": false,
  "code": 400,
  "msg": "参数验证失败: 寄件人手机号格式不正确",
  "data": null
}
```

## 📊 **数据流程**

### 查价流程
```
客户端请求
    ↓
JWT Token验证
    ↓
参数验证
    ↓
熔断器检查
    ↓
并行查询多个供应商
    ├── 菜鸟裹裹
    ├── 快递鸟
    ├── 云通
    ├── 快递100
    └── 易达
    ↓
汇总价格结果
    ↓
生成增强版订单代码
    ↓
返回标准化响应
```

### 预约时间处理流程
```
价格查询成功
    ↓
检查供应商类型
    ↓
菜鸟 → 调用菜鸟预约时间API
快递鸟 → 调用快递鸟超区校验API
其他 → 无预约时间信息
    ↓
转换为标准格式
    ↓
包含在价格响应中
```

## 🛡️ **安全注意事项**

1. **JWT Token**：必须提供有效的JWT Token进行认证
2. **参数验证**：所有输入参数都会进行严格验证
3. **速率限制**：建议控制请求频率，避免触发熔断器
4. **敏感信息**：手机号等敏感信息会在日志中脱敏处理

## 📝 **最佳实践**

1. **地址准确性**：提供准确的详细地址，地址信息直接影响价格计算
2. **重量设置**：确保包裹重量准确，影响计费重量计算
3. **错误处理**：实现完整的错误处理逻辑，包括重试机制
4. **缓存策略**：实时查价接口不使用缓存，如需缓存请在客户端实现
5. **预约时间**：如果需要预约时间功能，优先选择菜鸟或快递鸟供应商

## 📈 **版本历史**

- **v1.0.0** (2025-07-25): 初始版本，支持多供应商实时查价
- **v1.1.0** (2025-07-25): 新增快递鸟预约时间支持
- **v1.2.0** (2025-07-25): 修复体积重量计算问题，统一抛比配置

## 📞 **技术支持**

如有问题，请联系技术支持团队或查看相关日志文件进行排查。
